﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using Cfg;
using Cfg.Cm;
using Common;
using cspb;
using Cysharp.Threading.Tasks;
using DeepUI;
using Game.Data;
using Logic;
using P2;
using Public;
using Render;
using TFW;
using TFW.Localization;
using TFW.Map;
using TFW.UI;
using UI;
using UnityEngine;
using UnityEngine.EventSystems;
using EventTriggerType = TFW.EventTriggerType;

namespace UI
{
    /// <summary>
    /// 活动模块
    /// </summary>
    public class UIActivityAlliancePower: UIActivityModule
    {

        #region UI
        //滚动列表
        private  TFWLoopListView loopListView;
        private TFWText title;
        private GameObject allianceSymbolObj;
        private TFWText powerText;
        private BaseBtnWidget ranBtnWidget;
        private BaseBtnWidget joinBtnWidget;
        private GameObject hasAllianceObj;
        private GameObject notHasAllianceObj;
        #endregion

        #region field
        private Dictionary<int, UIActivityAlliancePowerItemWidget> widgetDic = new Dictionary<int, UIActivityAlliancePowerItemWidget>();
        #endregion
        public UIActivityAlliancePower(int moduleId) : base(moduleId)
        {

        }

        /// <summary>
        /// 获取ui元素
        /// </summary>
        protected override void GetUI()
        {
            #region GetUI

            if (LinkModule)
            {
                loopListView = UIHelper.GetComponent<TFWLoopListView>(LinkModule,"ScrollView");
                title = UIHelper.GetComponent<TFWText>(LinkModule, "ItemTop/HasAlliance/Title");
                powerText = UIHelper.GetComponent<TFWText>(LinkModule, "ItemTop/HasAlliance/TextRoot/Text2");
                var rankBtnObj = UIHelper.GetChild(LinkModule, "ItemTop/RankBtn");
                hasAllianceObj = UIHelper.GetChild(LinkModule, "ItemTop/HasAlliance");
                notHasAllianceObj = UIHelper.GetChild(LinkModule, "ItemTop/NotHasAlliance");
                if(rankBtnObj)
                {
                    ranBtnWidget = new BaseBtnWidget(rankBtnObj);
                    ranBtnWidget.SetBtnClickCallBack(OnClickRank);
                }
                var joinBtnObj = UIHelper.GetChild(LinkModule, "ItemTop/NotHasAlliance/joinBtn");
                if (joinBtnObj)
                {
                    joinBtnWidget = new BaseBtnWidget(joinBtnObj);
                    joinBtnWidget.SetBtnClickCallBack(OnClickJoin);
                }
                allianceSymbolObj = UIHelper.GetChild(LinkModule, "ItemTop/HasAlliance/IconAllianceSymbol_k1");
            }
            #endregion
        }

        private void OnClickJoin(object[] obj)
        {
            PopupManager.I.ClosePopup<UIActivityMain>();
            //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.ALLIANCE,
            //         new UIChatData() { CurrentTabKey = GameData.I.MainData.CurrMenuType, CurrChatTab = ChatTabs.Alliance });
        }

        private void OnClickRank(object[] obj)
        {
            PopupManager.I.ShowLayer<UIActivityAlliancePowerRank>();
        }

        //更新静态UI
        private void UpdateStaticUI()
        {

        }

        private List<Cfg.G.CAlliancePower> AlliancePowerList;
        //更新跟随数据刷新的UI
        private async UniTask UpdateUIByData()
        {
            //AlliancePowerList = await Cfg.C.CAlliancePower.RawListAsync();
            AlliancePowerList = new List<Cfg.G.CAlliancePower>();
            var CAlliancePowerList = await Cfg.C.CAlliancePower.GetEnumerableAsync();
            foreach (var cfg in CAlliancePowerList)
            {
                if (cfg == null)
                    continue;
                AlliancePowerList.Add(cfg);
            }
                if (!loopListView.ListViewInited)
            {
                loopListView.InitListView(AlliancePowerList.Count, OnInitListView);
            }
            else
            {
                loopListView.SetListItemCount(AlliancePowerList.Count, false);
                loopListView.RefreshAllShownItem();
            }
            hasAllianceObj.SetActive(LPlayer.I.IsPlayerInUnion());
            notHasAllianceObj.SetActive(!LPlayer.I.IsPlayerInUnion());
            title.text = LAllianceMgr.I.GetUnionAllName();
            powerText.text =UIStringUtils.FormatIntegerByLanguage(LAllianceMain.I.GetUnionPower());
            UIHelper.SetAllianceFlag(allianceSymbolObj, LAllianceMain.I.GetUnionFlag());

        }

        private TFWLoopListViewItem OnInitListView(TFWLoopListView loopListView, int index)
        {
            if (index < 0)
                return null;

            if (AlliancePowerList == null
                || index >= AlliancePowerList.Count
                || AlliancePowerList[index] == null)
                return null;

            TFWLoopListViewItem temp = loopListView.NewListViewItem("Item");
            if (temp.gameObject == null)
                return null;

            UIActivityAlliancePowerItemWidget widget;
            if (!widgetDic.TryGetValue(index, out widget))
            {
                widget = new UIActivityAlliancePowerItemWidget(temp.gameObject);
                widgetDic[index] = widget;
            }
            else
                widget.OnChangeRoot(temp.gameObject);

            widget.SetData(AlliancePowerList[index]);
            return temp;
        }
        /// <summary>
        /// 加载
        /// </summary>
        /// <param name="go"></param>
        protected override void OnLoaded(GameObject go)
        {
            base.OnLoaded(go);
            Attach(this.m_UIActivity.ActivityRoot);

        }

        /// <summary>
        /// 显示
        /// </summary>
        /// <param name="curActivity"></param>
        /// <param name="moduleData"></param>
        public override void Show(Activity curActivity, ActivityModule moduleData)
        {
            base.Show(curActivity, moduleData);
            EventMgr.RegisterEvent(TEventType.AlliancePowerRefresh, OnRefresh, this);
            UpdateStaticUI();
            UpdateUIByData().Forget();
        }

        private async UniTaskVoid OnRefresh(object[] obj)
        {
           await UpdateUIByData();
        }

        public override void Hide()
        {
            base.Hide();
            EventMgr.UnregisterEvent(TEventType.AlliancePowerRefresh);
        }
        /// <summary>
        /// 数据刷新显示
        /// </summary>
        /// <param name="curActivity"></param>
        /// <param name="moduleData"></param>
        public override async UniTask Refresh(Activity curActivity, ActivityModule moduleData)
        {
            await base.Refresh(curActivity, moduleData);
            await UpdateUIByData();
        }
        protected override void OnDestroyed()
        {

        }
    }
}
class UIActivityAlliancePowerItemWidget : UIWidgetBase
{
    #region UI
    TFWText title;
    RewardListUI rewardListUI;
    GameObject checkObj;
    BtnDescWidget claimBtnWidget;
    #endregion
    Cfg.G.CAlliancePower cfg;
    /// <summary>
    /// 构造
    /// </summary>
    /// <param name="root"></param>
    public UIActivityAlliancePowerItemWidget(GameObject root) : base(root) { }

    /// <summary>
    /// 初始化
    /// </summary>
    public override void OnInit()
    {
        base.OnInit();
        title = UIHelper.GetComponent<TFWText>(rootObj, "Title");
        rewardListUI = UIHelper.GetComponent<RewardListUI>(rootObj, "RewardListUI");
        checkObj = UIHelper.GetChild(rootObj, "Check");
        var claimObj = UIHelper.GetChild(rootObj, "Button");
        if(claimObj)
        {
            claimBtnWidget = new BtnDescWidget(claimObj);
            claimBtnWidget.SetBtnClickCallBack(OnClickClaim);
        }
    }

    private void OnClickClaim(object[] obj)
    {
        if (LAllianceMain.I.GetUnionPower() < cfg.AlliancePower || LPlayer.I.GetMainCityLevel() < cfg.CastleLevel)
            return;
        GameData.I.ActivityData.ReqActvUnionPowerGetReward(cfg.Id);
    }

    internal void SetData(Cfg.G.CAlliancePower cfg)
    {
        this.cfg = cfg;
        rewardListUI.SetData(cfg.Reward);
        title.text = string.Format(LocalizationMgr.Get(LAllianceMain.I.GetUnionPower() < cfg.AlliancePower ? "ActivityAlliancePower_03" : "ActivityAlliancePower_01"), UIStringUtils.FormatIntegerByLanguage(cfg.AlliancePower)) + "  " + string.Format(LocalizationMgr.Get(LPlayer.I.GetMainCityLevel() < cfg.CastleLevel ?"ActivityAlliancePower_04": "ActivityAlliancePower_02"), cfg.CastleLevel);
        checkObj.SetActive(GameData.I.ActivityData.HasGotReward(cfg.Id));
        claimBtnWidget.SetRootVisible(!GameData.I.ActivityData.HasGotReward(cfg.Id));
        claimBtnWidget.SetBtnGrey(LAllianceMain.I.GetUnionPower() < cfg.AlliancePower || LPlayer.I.GetMainCityLevel() < cfg.CastleLevel);
    }
}