﻿using Common;
using DeepUI;
using DG.Tweening;
using TFW;
using Logic;
using System;
using System.Collections.Generic;
using System.Linq;
using UI;
using UnityEngine;
using UnityEngine.UI;
using Render;
using Game.Config;

namespace K3
{
    public class K3UnlockData : UIData
    {
        public UnlockType unlockType { get; set; }

        public int id;
        public int OriLv; // 升级前等级
        public int AddLv;

        /// <summary>
        /// 和服务器同步的 功能解锁类型 可新增不可改序！
        /// </summary>
        public enum UnlockType
        {
            None,

            Shop = 1,
            Hero,
            MainCity,
            Alliance,
            World,

            AutoMerge,
            SortMerge,
            CityBuilding = 1000,
            SpecialBox = 2000,
        }

        public Action closeUICallBack;
    }

    [Popup("Unlock", true, true)]
    public class K3Unlock : BasePopup
    {
        [PopupField("Root")]
        private GameObject _root;
        
        [PopupField("Root/bg")]
        private GameObject mask;
        
        [PopupField("Root/PS1")]
        private GameObject effect;

        [PopupField("Root/Common")]
        private GameObject commonObj;

        [PopupField("Root/Common/btn")]
        private GameObject btnClose;

        public static Queue<K3UnlockData> UnlockDatas = new Queue<K3UnlockData>();

        public static void ShowData(bool showUnlock = true)
        {
            if (UnlockDatas.Count > 0)
            {
                var unlockData = UnlockDatas.Dequeue();

                Debug.Log($"解锁功能:{unlockData.unlockType}");
                GameAudio.PlayAudio(AudioConst.taskComplete);

                if (unlockData.id == 0)
                {
                    if (!CSPlayer.I.HasUnlockMenus((int)unlockData.unlockType))
                    {
                        CSPlayer.I.AddUnlockMenus((int)unlockData.unlockType);

                        // MessageMgr.Send(new cspb.UnlockReq() { Id = (int)unlockData.unlockType });
                    }
                }
                else if (unlockData.unlockType == K3UnlockData.UnlockType.CityBuilding)
                {
                    if (!CSPlayer.I.HasUnlockMenus(unlockData.id))
                    {
                        CSPlayer.I.AddUnlockMenus(unlockData.id * 100);

                        // MessageMgr.Send(new cspb.UnlockReq() { Id = unlockData.id * 100 });
                    }
                }
                else if (unlockData.unlockType == K3UnlockData.UnlockType.SpecialBox)
                {
                    if (!CSPlayer.I.HasUnlockMenus(unlockData.id + 10000))
                    {
                        CSPlayer.I.AddUnlockMenus(unlockData.id + 10000);

                        // MessageMgr.Send(new cspb.UnlockReq() { Id = unlockData.id + 10000 });
                    }
                }

                if (showUnlock)
                {
                    PopupManager.I.ShowDialog<K3Unlock>(unlockData);
                    //PopupManager.I.SetPanelMaskDisableing(true);
                }
                else
                {
                    switch (unlockData.unlockType)
                    {
                        case K3UnlockData.UnlockType.CityBuilding:
                            GuidManage.TriggerGuid(GuidManage.GuidTriggerType.FirstUnlockMenu, unlockData.id * 100);
                            break;

                        case K3UnlockData.UnlockType.SpecialBox:
                            GuidManage.TriggerGuid(GuidManage.GuidTriggerType.FirstUnlockMenu, unlockData.id + 10000);
                            break;

                        default:
                            GuidManage.TriggerGuid(GuidManage.GuidTriggerType.FirstUnlockMenu, (int)unlockData.unlockType);
                            break;
                    }
                }
            }
        }

        // protected internal override PopupOverlayType OverlayType => PopupOverlayType.Blur;

        protected internal override bool PopBackEnabled => true;

        private K3UnlockData mData;

        private UnlockItem[] unlockItems;

        #region 初始化

        protected override void OnInit()
        {
            base.OnInit();
            
            BindClickListener(btnClose, (x, y) =>
            {
                ShowCloseAni();

                var time = 1;
                var endObj = GetAniEndObj(mData.unlockType);
                if (null == endObj)
                {
                    time = 0;
                }

                NTimer.CountDown(time, () =>
                {
                    OnWindowClose();
                });
            });

            unlockItems = commonObj.GetComponentsInChildren<UnlockItem>(true);
            commonObj.AddComponent<Canvas>().overrideSorting = true;
            commonObj.GetComponent<Canvas>().sortingLayerID = CustomSortingLayer.Dialog;
            commonObj.GetComponent<Canvas>().sortingOrder = 50;
            //unlcoasdcommonObj.AddComponent<GraphicRaycaster>();
            btnClose.AddComponent<Canvas>().overrideSorting = true;
            btnClose.GetComponent<Canvas>().sortingLayerID = CustomSortingLayer.Dialog;
            btnClose.GetComponent<Canvas>().sortingOrder = 51;
            btnClose.AddComponent<GraphicRaycaster>();
        }

        #endregion 初始化

        #region 数据刷新显示

        /// <summary>
        /// 当界面第一次打开时调用
        /// </summary>
        protected internal override void OnOpenStart()
        {
            base.OnOpenStart();

            OnShowStart();
        }

        private int timerCloseID;

        /// <summary>
        /// 界面每次显示时调用
        /// </summary>
        protected internal override void OnShowStart()
        {
            base.OnShowStart();
            
            mask.SetActive(true);
            effect.SetActive(true);
            commonObj.SetActive(true);

            //_root.gameObject.GetComponent<Animator>().Play("UI_unlock Animation");
            //Debug.LogError("播放动画：");
            mData = Data as K3UnlockData;

            foreach (var item in unlockItems)
            {
                item.gameObject.SetActive(false);
            }

            float closeTime = 4f;

            if (mData != null)
            {
                foreach (var item in unlockItems)
                {
                    if (item.UnlockType == mData.unlockType)
                    {
                        item.gameObject.SetActive(true);

                        item.InitData(mData.id);

                        break;
                    }
                }

                var gameEvent= new UnlockFunctionEvent() { EventKey = $"unlockfunction" };
                gameEvent.Properties.Add("unlockType", (int)mData.unlockType);
                gameEvent.Properties.Add("unlockid", mData.id);
                K3GameEvent.I.TaLog(gameEvent);

                switch (mData.unlockType)
                {
                    //case K3UnlockData.UnlockType.Hero:
                    //    //K3GameEvent.I.TimeStart("heroguide");
                    //    break;

                    case K3UnlockData.UnlockType.CityBuilding:
                        switch (mData.id)
                        {
                            case 4://酒馆
                                //K3GameEvent.I.TimeStart("tavernguide");
                                break;

                            case 5://占星台
                                //K3GameEvent.I.TimeStart("astralguide");
                                break;

                            default:
                                break;
                        }
                        break;
                }
            }

            timerCloseID = NTimer.CountDown(closeTime, OnWindowClose); ;
        }

        private void OnWindowClose()
        {
            ///建筑解锁触发剧情，放置在这个UI关闭后
            if (mData.unlockType == K3UnlockData.UnlockType.CityBuilding)
            {
                GuidManage.TriggerGuid(GuidManage.GuidTriggerType.UnlockTheBuilding, mData.id, 1);
            }
            
            Close();
        }


        #endregion 数据刷新显示

        private void ShowCloseAni()
        {
            NTimer.Destroy(timerCloseID);

            //var uIMerge = DeepUI.PopupManager.I.FindPopup<K3.UIMerge>();
            //if (uIMerge != null)
            //{
            //    uIMerge.RedSyn();
            //}

            EventMgr.FireEvent(TEventType.OpenWorldAndTech);
            
            

            if (mData != null)
            {
                foreach (var item in unlockItems)
                {
                    if (item.UnlockType == mData.unlockType)
                    {
                        var endObj = GetAniEndObj(item.UnlockType);
                        if (endObj != null)
                        {
                            PlayAni(endObj, item.atlasIcon, GetPlayAni(item.UnlockType));
                        }
                        else
                        {
                            mData.closeUICallBack?.Invoke();
                            mData.closeUICallBack = null;
                        }
                        break;
                    }
                }

                switch (mData.unlockType)
                {
                    //case K3UnlockData.UnlockType.Hero:
                    //    //K3GameEvent.I.TimeEnd("heroguide", "1");
                    //    break;

                    case K3UnlockData.UnlockType.CityBuilding:
                        switch (mData.id)
                        {
                            case 4://酒馆
                                //K3GameEvent.I.TimeEnd("tavernguide", "1");
                                break;

                            case 5://占星台
                                //K3GameEvent.I.TimeEnd("astralguide", "1");
                                break;

                            default:
                                break;
                        }
                        break;
                }
            }
        }

        protected internal override void OnCloseComplete()
        {
            base.OnCloseComplete();

            //PopupManager.I.SetPanelMaskDisableing(false);

            if (mData != null)
            {
                mData.closeUICallBack?.Invoke();
                mData.closeUICallBack=null;
            }

            if (UnlockDatas.Count > 0)
            {
                K3Unlock.ShowData(true);
            }
        }

        private bool GetPlayAni(K3UnlockData.UnlockType unlockType)
        {
            //switch (unlockType)
            //{
            //    case K3UnlockData.UnlockType.MainCity:
            //        EventMgr.FireEvent(TEventType.UIMainn_Update_UnLockMenu, MainMenuType.CITY);
            //        break;
            //    case K3UnlockData.UnlockType.Hero:
            //        EventMgr.FireEvent(TEventType.UIMainn_Update_UnLockMenu, MainMenuType.HERO);
            //        break;
            //    case K3UnlockData.UnlockType.World:
            //        EventMgr.FireEvent(TEventType.UIMainn_Update_UnLockMenu, MainMenuType.WORLD);
            //        break;
            //    case K3UnlockData.UnlockType.Alliance:
            //        EventMgr.FireEvent(TEventType.UIMainn_Update_UnLockMenu, MainMenuType.ALLIANCE);
            //        break;
            //    default:
            //        break;
            //}

            switch (unlockType)
            {
                //case K3UnlockData.UnlockType.MainCity:
                //case K3UnlockData.UnlockType.Hero:
                case K3UnlockData.UnlockType.World:
                //case K3UnlockData.UnlockType.Alliance:
                case K3UnlockData.UnlockType.AutoMerge:
                    return false;

                default:
                    break;
            }

            return true;
        }

        private GameObject GetAniEndObj(K3UnlockData.UnlockType unlockType)
        {
            UIGuidData guidData;
            var uIMerge = DeepUI.PopupManager.I.FindPopup<K3.UIMerge>();
            var uiMain2 = SceneManager.I.FindScene(typeof(UIMain2)) as UIMain2;

            switch (unlockType)
            {
                case K3UnlockData.UnlockType.CityBuilding:
                    GuidManage.TriggerGuid(GuidManage.GuidTriggerType.FirstUnlockMenu, mData.id * 100);
                    break;

                case K3UnlockData.UnlockType.SpecialBox:
                    GuidManage.TriggerGuid(GuidManage.GuidTriggerType.FirstUnlockMenu, mData.id + 10000);



                    break;

                default:
                    GuidManage.TriggerGuid(GuidManage.GuidTriggerType.FirstUnlockMenu, (int)unlockType);
                    break;
            }

            switch (unlockType)
            {
                case K3UnlockData.UnlockType.None:
                    break;
                //case K3UnlockData.UnlockType.Photo:
                //    return uIMerge?.photoTaskObj;
                case K3UnlockData.UnlockType.AutoMerge:
                    return uIMerge?.AutoButton;
                case K3UnlockData.UnlockType.SortMerge:
                    return uIMerge?.SortMergeBtn;
                //case K3UnlockData.UnlockType.Task:
                //    return uIMerge.lockTask;
                //case K3UnlockData.UnlockType.Atlas:
                //   return uIMerge?.lockAtlas;
                //case K3UnlockData.UnlockType.Shop:
                //    //K3GuidMgr.I.ShopGuid();
                //    return uIMerge?.lockShop;
                //case K3UnlockData.UnlockType.MainCity:

                //    uIMerge.btnBack.SetActive(true);

                //    //guidData = new UIGuidData();
                //    //guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMerge", UIItem = "Root/btnBack", slide = true, delayFinger = 1f, });
                //    //guidData.guidItems.Add(new UIGuidItem() { UIName = "UI.UIMain2", UIItem = "content/MenuRoot/Castle", slide = true, delayFinger = 1f, });
                //    //guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMainCity", UIItem = "Root/Building/Castle1", slide = true, });
                //    //guidData.guidItems.Add(new UIGuidItem() { UIName = "UI.UIMainCityLevelUp", UIItem = "Root/Button", slide = true, });

                //    //UIGuid.StartGuid(guidData);

                //    return uiMain2.mainMenuData.menuArr[(int)MainMenuType.PRISON - 1].obj;

                //case K3UnlockData.UnlockType.Hero:

                    //guidData = new UIGuidData();
                    //guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMerge", UIItem = "Root/btnBack", slide = true, delayFinger = 1f, });
                    //guidData.guidItems.Add(new UIGuidItem() { UIName = "UI.UIMain2", UIItem = "content/MenuRoot/Hero", slide = true, delayFinger = 1f, });
                    //guidData.guidItems.Add(new UIGuidItem() { UIName = "UIHeroUpgrade", UIItem = "Root/AddHero/Main", slide = true, });
                    //guidData.guidItems.Add(new UIGuidItem()
                    //{
                    //    UIName = "UIHeroList",
                    //    GetUIItemGa = () =>
                    //    {
                    //        var heroList = PopupManager.I.FindPopup<UIHeroList>();
                    //        if (heroList != null)
                    //        {
                    //            var heroItemList = heroList.loopListView.GetComponentsInChildren<UIHeroListItem>();
                    //            foreach (var item in heroItemList)
                    //            {
                    //                if (item.mHeroData.HeroCfg.Id == 435)
                    //                {
                    //                    return item.gameObject;
                    //                }
                    //            }
                    //        }
                    //        return null;
                    //    },
                    //    slide = true,
                    //});
                    //guidData.guidItems.Add(new UIGuidItem() { UIName = "UI.UIMain2", UIItem = "content/MenuRoot/Battle", slide = true, delayFinger = 1f });
                    //guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMergeMain", UIItem = "Root/bottom/btnPlay", slide = true, delayFinger = 1f });

                    //guidData.guidItems.Add(new UIGuidItem()
                    //{
                    //    UIName = "UIMerge",
                    //    GetUIItemGa = () =>
                    //{
                    //    foreach (var item in uIMerge.mGridDic)
                    //    {
                    //        if (item.Value.Good?.Info.Type == -1)
                    //        {
                    //            return item.Value.Good.gameObject;
                    //        }
                    //    }
                    //    return null;
                    //},
                    //    slide = true,
                    //    delayFinger = 1f,
                    //    d7Desc = TFW.Localization.LocalizationMgr.Get("Ui_Guide_MergeSkill_txt"),
                    //    d7ObjY = 150,
                    //});

                    //UIGuid.StartGuid(guidData);

                    //return uiMain2.mainMenuData.menuArr[(int)MainMenuType.HERO - 1].obj;

                //case K3UnlockData.UnlockType.World:

                //    //guidData = new UIGuidData();
                //    //guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMerge", UIItem = "Root/btnBack"});
                //    //guidData.guidItems.Add(new UIGuidItem() { UIName = "UI.UIMain2", UIItem = "content/MenuRoot/World", slide = true, delayFinger = 1f, });
                //    //guidData.guidItems.Add(new UIGuidItem() { UIName = "UI.UIMain2", UIItem = "content/MenuRoot/World", slide = true, delayFinger = 1f, });
                //    //guidData.guidItems.Add(new UIGuidItem() { UIName = "UI.UISearch", UIItem = "CentreOther/Animation/Popup1/Connet/Button/BtnSearch", slide = true, });

                //    //UIGuid.StartGuid(guidData);

                //    //EventMgr.FireEvent(TEventType.UIMain_Update_AllianceMoveCity_Active, false);

                //    return uiMain2.mainMenuData.menuArr[(int)MainMenuType.WORLD - 1].obj;

                //case K3UnlockData.UnlockType.Alliance:
                //    return uiMain2.mainMenuData.menuArr[(int)MainMenuType.ALLIANCE - 1].obj;

                case K3UnlockData.UnlockType.SpecialBox:
                    if (uIMerge != null)
                    {
                        switch (mData.id)
                        {
                            case 2:
                                return uIMerge.boxes.Count > 0 ? uIMerge.boxes[0].gameObject : null;

                            case 3:
                                return uIMerge.boxes.Count > 1 ? uIMerge.boxes[1].gameObject : null;

                            case 4:
                                return uIMerge.boxes.Count > 2 ? uIMerge.boxes[2].gameObject : null;

                            case 5:
                                return uIMerge.boxes.Count > 3 ? uIMerge.boxes[3].gameObject : null;

                            case 6:
                                return uIMerge.boxes.Count > 4 ? uIMerge.boxes[4].gameObject : null;

                            default:
                                break;
                        }
                    }
                    break;

                default:
                    break;
            }

            return null;
        }

        private void PlayAni(GameObject endGa, GameObject startGa, bool playAni = true)
        {
            if (endGa)
            {
                var flyObj = GameObject.Instantiate(startGa, this.Transform);
                flyObj.transform.localScale = Vector3.zero;
                flyObj.transform.position = startGa.transform.position;
                flyObj.transform.localPosition = new Vector3(flyObj.transform.localPosition.x, flyObj.transform.localPosition.y, 0);
                
                mask.SetActive(false);
                effect.SetActive(false);
                commonObj.SetActive(false);

                var seq = DOTween.Sequence();
                
                seq.Append(flyObj.transform.DOScale(1.2f, .3f));
                seq.Append(flyObj.transform.DOScale(0.36f, 0.6f));

                // seq.Insert(0.3f, flyObj.transform.DOMove(endGa.transform.position, 0.6f));
                seq.InsertCallback(0.3f, () =>
                {
                    var uimerge = DeepUI.PopupManager.I.FindPopup<UIMerge>();
                    var target = endGa;

                    if (null == target || null == uimerge)
                    {
                        return;
                    }

                    var targetPos1 = uimerge.GameObject.transform.TransformPoint(target.transform.position);
                    var boxPos = flyObj.gameObject.transform.parent.transform.InverseTransformPoint(targetPos1);

                    flyObj.transform.DOMoveX(boxPos.x, 0.3f);
                    flyObj.transform.DOMoveY(boxPos.y, 0.3f);
                    
                });

                seq.AppendCallback(() =>
                {
                    mData.closeUICallBack?.Invoke();
                    mData.closeUICallBack = null;

                    flyObj.SetActive(false);
                    //GameObject.DestroyImmediate(flyObj);

                    EventMgr.FireEvent(TEventType.K3LockIDRefresh);

                    var animation = endGa.GetComponent<Animation>();
                    if (animation && playAni)
                    {
                        animation.Play();
                    }
                });

                seq.Play();
            }
        }
    }
}
