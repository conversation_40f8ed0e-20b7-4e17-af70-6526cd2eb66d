﻿ 

using Cfg.C;
using Common;
using cspb;
using DeepUI;
using Game.Config;
using Game.Sprite.Fight;
using Logic;
using Public;
using Render;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using TFW;
using TFW.Localization;
using TFW.Map;
using TFW.UI;
using UnityEngine;
using UnityEngine.EventSystems;
using static MainCityItem;
using EventTriggerType = TFW.EventTriggerType;

namespace UI
{
    public class UISearchData : PopupData
    {
        /// <summary>
        /// 0默认打怪 1为采集 2集结
        /// </summary>
        public int SearchType;
        public new Action<UISearch> onShown;
    }

    [Popup("Map/ui4105Search")]
    public partial class UISearch : HistoricPopup
    {
        #region Property

        /// <summary> 
        /// 排序
        /// </summary>
        //public ESortingOrder SortingOrder => ESortingOrder.Auto;

        //public EPopPanelType PopPanelType => EPopPanelType.PopUp;

        public RectTransform SearchButtonOne { get; private set; }

        public RectTransform SearchButtonTwo { get; private set; }



        #endregion

        #region Field

        private List<float> m_BtnPositionOffsetList;

        private bool m_BlockSearch;
        private GameObject m_DetailPop;
        private TFWSlider m_LevelSlider;

        private GameObject m_LocationPop;
        private static int m_Choosed;
        private TFWinputField m_InputX;
        private TFWinputField m_InputY;

        private TFWImage mTitansLockImg;

        /// <summary>
        /// 0位默认打怪 1为采集 2为集结
        /// </summary>
        private int OnShowType;
        private Action<UISearch> m_OnShown;

        /// <summary>
        /// 搜索野怪
        /// </summary>
        private TFWTab _tabSearchEnmy;
        /// <summary>
        /// 搜索采集矿
        /// </summary>
        private TFWTab _tabSearchGather;
        /// <summary>
        /// 搜索集结怪
        /// </summary>
        private TFWTab _tabSearchEnmyRally;
        /// <summary>
        /// 搜索观星台情报
        /// </summary>
        private TFWTab _tabSearchStargazingPlatform;

        /// <summary>
        /// 战力信息配置
        /// </summary>
        private Cfg.G.CNpcTroopPower m_PowerConfig;
        /// <summary>
        /// 奖励道具的root
        /// </summary>
        private UIGrid m_ItemGridRoot;

        private Transform StargazingPlatform;
        private TFWText StargazingPlatformDes1;
        private TFWText StargazingPlatformDes2;

        private Transform Monster;
        private Transform GatherRally;
        // private Animator anim1;
        // private Animator anim2;
        // private Animator anim3;
        /// <summary>
        /// 当前搜索的采集的等级
        /// </summary>
        private int _currGatherLevel;
        // private Animator anim;
        /// <summary>
        /// 当前搜索野怪据点的等级
        /// </summary>
        private int _currEnmyRallyLevel
        {
            get
            {
                return currEnmyRallyLevel;
            }
            set
            {
                currEnmyRallyLevel = value;

            }
        }
        /// <summary>
        /// 当前搜索野怪据点的等级
        /// </summary>
        private int currEnmyRallyLevel = 1;

        #endregion

        #region Method

        protected string assetPath => "Map/ui4105Search";

        protected override void OnInit()
        {
            base.OnInit();
            m_BtnPositionOffsetList = new List<float>();
            m_BlockSearch = false;
        }


        protected override void OnLoad()
        {
            #region 注释
            //RegisterEvent(TEventType.WildMapSearchLock, (arg) => { m_BlockSearch = true; });
            //RegisterEvent(TEventType.WildMapSearchUnlock, (arg) => { m_BlockSearch = false; });
            //RegisterEvent(TEventType.WildMapSearchBeginJump, (arg) => { OnClose(null, null); });
            //RegisterEvent(TEventType.SearchNoResult, (arg) =>
            //{
            //    //搜索不到运输车有特殊表现
            //    if (LSearch.I.CurrItem?.SearchId == 131934)
            //    {
            //        Close();
            //        MapCameraMgr.MoveCameraToTarget(RMap.ViewCenter.x,RMap.ViewCenter.z,"lod_8",0.1f,0.5f,null);
            //    }
            //});

            //AddListener(EventTriggerType.Click, "CentreOther/Animation/Tab/View/Content/tab3", OnClickTab3);集结屏蔽去掉

            //GetComponent<TFWText>("CentreOther/Animation/Search/Connet/LVSlider/TxtLv").text =
            //    $"{LocalizationMgr.Get("LC_MENU_level")} {LSearch.I.SearchLevel}";
            //InitChoose();
            //CoroutineMgr.StartCoroutine(InitSearchItems());

            //OverridBtn();
            #endregion

            #region Popup2
            m_LocationPop = GetChild("CentreOther/Animation/Popup2");
            if (m_LocationPop != null)
                m_LocationPop.SetActive(false);
            AddListener(EventTriggerType.Click, "CentreOther/Animation/Popup2/BtnSearch", OnLocationClick, out var btnTwo);
            if (btnTwo != null)
                SearchButtonTwo = btnTwo.transform as RectTransform;
            m_InputX = UIHelper.GetComponent<TFWinputField>(m_LocationPop, "Input1/InputTxt");
            if (m_InputX != null)
            {
                m_InputX.text = RMap.ViewCenter.x.ToString();
                m_InputX.onValueChanged.AddListener((str) => { OnInputValueChanged(true, m_InputX, str); });
            }

            m_InputY = UIHelper.GetComponent<TFWinputField>(m_LocationPop, "Input2/InputTxt");
            if (m_InputY != null)
            {
                m_InputY.text = RMap.ViewCenter.z.ToString();
                m_InputY.onValueChanged.AddListener((str) => { OnInputValueChanged(false, m_InputY, str); });
            }
            #endregion

            // anim = GetComponent<Animator>("CentreOther/Animation");

            //标题
            var titleText = GetComponent<TFWText>("CentreOther/Animation/BG/Title/Title/TitleText");
            if (titleText != null)
                titleText.text = LocalizationMgr.Get("Searching_Title_Monster");
            AddListener(EventTriggerType.Click, "CentreOther/Animation/BG/Title/Title/CloseBtn", OnClose); //界面关闭

            #region 搜索滑动条&按钮
            m_DetailPop = GetChild("CentreOther/Animation/Content/Search");
            if (m_DetailPop != null)
                m_DetailPop.SetActive(true);

            m_LevelSlider = GetComponent<TFWSlider>("CentreOther/Animation/Content/Search/Connet/LVSlider/Slider");
            if (m_LevelSlider)
            {
                m_LevelSlider.wholeNumbers = true;
                m_LevelSlider.minValue = 1;
                m_LevelSlider.onValueChanged.AddListener(OnSliderChange);

                m_LevelSlider.onPointUp.AddListener(() =>
                {
                    //if (_tabSearchEnmy.isOn)
                    //{
                    //    LSearch.I.TmpSearchLv[1] = LSearch.I.SearchLevel;
                    //}
                    //else if (_tabSearchGather.isOn)
                    //{
                    //    LSearch.I.TmpSearchLv[3] = LSearch.I.SearchLevel;
                    //}
                    //else if (_tabSearchEnmyRally.isOn)
                    //{
                    //    LSearch.I.TmpSearchLv[2] = LSearch.I.SearchLevel;
                    //}

                    UpdateSearchLevel();
                });

                //m_LevelSlider.onSelect.AddListener(() =>
                //{
                //    ////BILog.UserClick($"{Name}.SliderChangeVal",$"{Name}",$"{Name}",new []{LSearch.I.CurrItem?.SearchId.ToString()});
                //});
            }

            AddListener(EventTriggerType.Click, "CentreOther/Animation/Content/Search/Connet/LVSlider/BtnReduce", OnReduceClick);
            AddListener(EventTriggerType.Click, "CentreOther/Animation/Content/Search/Connet/LVSlider/BtnAdd", OnAddClick);
            AddListener(EventTriggerType.Click, "CentreOther/Animation/Content/Search/Connet/Button/BtnSearch", OnSearchClick, out var btnOne);
            AddListener(EventTriggerType.Click, "CentreOther/Animation/Content/Search/Connet/Button/BtnPhysical", OnClickPhysicalBtn);
            if (btnOne != null)
                SearchButtonOne = btnOne.transform as RectTransform;

            #endregion

            #region 标签
            _tabSearchEnmy = GetComponent<TFWTab>("CentreOther/Animation/BG/Tab/View/Content/tab1");
            _tabSearchGather = GetComponent<TFWTab>("CentreOther/Animation/BG/Tab/View/Content/tab2");
            _tabSearchEnmyRally = GetComponent<TFWTab>("CentreOther/Animation/BG/Tab/View/Content/tab3");
            mTitansLockImg = GetComponent<TFWImage>("CentreOther/Animation/BG/Tab/View/Content/tab3/Off/Icon");
            _tabSearchStargazingPlatform = GetComponent<TFWTab>("CentreOther/Animation/BG/Tab/View/Content/tab4");

            if (_tabSearchEnmy != null)
                _tabSearchEnmy.AddTabClickEvent(SearchEnemy);

            if (_tabSearchGather != null)
                _tabSearchGather.AddTabClickEvent(SearchGather);

            if (_tabSearchEnmyRally != null)
                _tabSearchEnmyRally.AddTabClickEvent(SearchEnmyRally);

            if (_tabSearchStargazingPlatform != null)
                _tabSearchStargazingPlatform.AddTabClickEvent(SearchStargazingPlatform);
            #endregion

            #region 怪物&集结&采集&占星台
            //页签逻辑很麻烦，集结页签新增锁功能，只有在点击时才能反馈，导致动画重复播放问题
            // anim1 = GetComponent<Animator>("CentreOther/Animation/Content/Monster/MonsterScroll");
            // anim2 = GetComponent<Animator>("CentreOther/Animation/Content/Gather&Rally/Gold");
            // anim3 = GetComponent<Animator>("CentreOther/Animation/Content/Gather&Rally/Mass");
            // anim1.enabled = false;
            // anim2.enabled = false;
            // anim3.enabled = false;
            Monster = GetComponent<Transform>("CentreOther/Animation/Content/Monster");
            GatherRally = GetComponent<Transform>("CentreOther/Animation/Content/Gather&Rally");

            StargazingPlatform = GetComponent<Transform>("CentreOther/Animation/Content/StargazingPlatform");
            StargazingPlatformDes1 = GetComponent<TFWText>("CentreOther/Animation/Content/StargazingPlatform/Des1");
            StargazingPlatformDes2 = GetComponent<TFWText>("CentreOther/Animation/Content/StargazingPlatform/Des2");
            #endregion
        }

        private void OnClickTab3(GameObject arg0, PointerEventData arg1)
        {
            FloatTips.I.FloatMsg(LocalizationMgr.Get("DEMO_30"));
        }

        /// <summary>
        /// 搜索等级变化
        /// </summary>
        /// <param name="v"></param>
        private void OnSliderChange(float v)
        {
            if (!IsShow)
                return;

            //搜怪的逻辑
            //var npcId = LSearch.I.CurrItem.NpcId;
            //var npcConfig = CNpcTroop.I(int.Parse(npcId));
            if (_tabSearchEnmy.isOn)
            {
                var npcId = LSearch.I.CurrItem.SearchId;
                var npcConfig = CD2NpcTroopClass.I(npcId);
                if (npcConfig != null)
                {
                    //var preAttackId = LPlayer.I.GetKillOrderData(npcConfig.ClassId);
                    //var theMaxLvl = preAttackId != -1 ? CNpcTroop.I(preAttackId).Lvl + 1 : 1;
                    //var theMaxLvl = LPlayer.I.GetKillOrderData(npcConfig.ClassId);
                    var theMaxLvl = LPlayer.I.GetKillOrderData(npcConfig.Id) > 0 ? LPlayer.I.GetKillOrderData(npcConfig.Id) + 1 : 1;
                    if (v > theMaxLvl)
                    {
                        m_LevelSlider.SetValueWithoutNotify(theMaxLvl);
                        return;
                    }

                    if (v < 1)
                    {
                        m_LevelSlider.SetValueWithoutNotify(0);
                        return;
                    }
                }
                LSearch.I.SearchLevel = (int)v;
                LSearch.I.NeedResearch = true;
            }
            else if (_tabSearchGather.isOn)
            {
                var theMaxLevel = 5;
                if (v > theMaxLevel)
                {
                    m_LevelSlider.SetValueWithoutNotify(theMaxLevel);
                    return;
                }
                if (v < 1)
                {
                    m_LevelSlider.SetValueWithoutNotify(0);
                    return;
                }

                _currGatherLevel = (int)v;

                LSearch.I.SearchLevel = _currGatherLevel;
            }
            else if (_tabSearchEnmyRally.isOn)
            {
                int maxLevel = LSeason.I.MaxRallyMonsterLevel;// (int)MetaConfig.MaxRallyMonsterNum;
                int minLevel = (int)MetaConfig.MassMonsterMinLevel;
                if (chronicleType == ChroniclSwitchEnum.UnLock)
                {
                    //天下大势解锁泰坦等级
                    maxLevel = chronicleTitanLv;
                }

                if (v > maxLevel)
                {
                    m_LevelSlider.SetValueWithoutNotify(maxLevel);
                    return;
                }
                else if (v < minLevel)
                {

                    m_LevelSlider.SetValueWithoutNotify(0);
                    return;
                }

                _currEnmyRallyLevel = (int)v;

                LSearch.I.SearchLevel = _currEnmyRallyLevel;

            }
            GetComponent<TFWText>("CentreOther/Animation/Content/Search/Connet/LVSlider/TxtLv").text = v.ToString();// $"{LocalizationMgr.Get("LC_MENU_level")} {v}";

            MonsterNpcReward(LSearch.I.SearchItemList[m_Choosed].SearchId, LSearch.I.SearchLevel);
        }

        /// <summary>
        /// 搜索野怪据点（集结）
        /// </summary>
        /// <param name="target"></param>
        /// <param name="eventData"></param>
        private void SearchEnmyRally(TFWTab target, PointerEventData eventData)
        {
            StargazingPlatform.gameObject.SetActive(false);
            Monster.gameObject.SetActive(false);
            GatherRally.gameObject.SetActive(true);
            //Debug.LogErrorFormat("点击搜索集结");
            //if (chronicleType == ChroniclSwitchEnum.Lock)
            //{
            //    //_tabSearchEnmyRally.isOn = false;
            //    mTitansLockImg.enabled = true;
            //    _tabSearchEnmy.isOn = OnShowType == 0;
            //    _tabSearchGather.isOn = OnShowType == 1;
            //    anim1.enabled = false;
            //    anim2.enabled = false;

            //    var id = chronicleTitanLockEventId;
            //    if(id != 0)
            //    {
            //        if(GameData.I.ChronicleData.chronicleEventData.TryGetValue(id, out var dt))
            //        {
            //            UITools.PopTips(LocalizationMgr.Format("chronicle_txt_20", LocalizationMgr.Get(dt.cfg.Name)));
            //        }
            //    }

            //    return;
            //}

            //anim3.enabled = false;
            mTitansLockImg.enabled = false;
            // anim3.gameObject.SetActive(true);
            // anim3.enabled = true;
            OnShowType = 2;
            m_LevelSlider.onValueChanged.RemoveAllListeners();

            int maxLevel = LSeason.I.MaxRallyMonsterLevel;// (int)MetaConfig.MaxRallyMonsterNum;
            int minLevel = (int)MetaConfig.MassMonsterMinLevel;

            if (chronicleType == ChroniclSwitchEnum.UnLock)
            {
                //天下大势解锁泰坦等级
                maxLevel = chronicleTitanLv;
            }

            _currEnmyRallyLevel = _currEnmyRallyLevel > minLevel ? _currEnmyRallyLevel : minLevel;

            if (LSearch.I.TmpSearchLv.ContainsKey(2))
                _currEnmyRallyLevel = LSearch.I.TmpSearchLv[2];

            m_LevelSlider.maxValue = maxLevel;
            m_LevelSlider.minValue = minLevel;
            m_LevelSlider.value = _currEnmyRallyLevel;
            GetChild("CentreOther/Animation/Content/Search/Connet/LVSlider/AttackableTitle").SetActive(false);
            GetComponent<TFWText>("CentreOther/Animation/BG/Title/Title/TitleText").text = LocalizationMgr.Get("Searching_Title_Rally");
            GetComponent<TFWText>("CentreOther/Animation/Content/Search/Connet/LVSlider/TxtLv").text = _currEnmyRallyLevel.ToString();// $"{LocalizationMgr.Get("LC_MENU_level")} {_currEnmyRallyLevel}";
            LSearch.I.SearchLevel = _currEnmyRallyLevel;

            m_LevelSlider.onValueChanged.AddListener(OnSliderChange);
        }

        /// <summary>
        /// 搜索金矿
        /// </summary>
        /// <param name="target"></param>
        /// <param name="eventData"></param>
        private void SearchGather(TFWTab target, PointerEventData eventData)
        {
            StargazingPlatform.gameObject.SetActive(false);
            Monster.gameObject.SetActive(false);
            GatherRally.gameObject.SetActive(true);
            //Debug.LogErrorFormat("点击搜索金矿");
            // anim2.enabled = false;
            // anim2.enabled = true;
            OnShowType = 1;
            m_LevelSlider.onValueChanged.RemoveAllListeners();

            int maxLevel = 5;
            int minLevel = 1;

            if (LSearch.I.TmpSearchLv.ContainsKey(3))
                _currGatherLevel = LSearch.I.TmpSearchLv[3];

            _currGatherLevel = _currGatherLevel > minLevel ? _currGatherLevel : minLevel;
            m_LevelSlider.maxValue = maxLevel;
            m_LevelSlider.minValue = minLevel;
            m_LevelSlider.value = _currGatherLevel;

            //Debug.LogErrorFormat($"设置等级{_currGatherLevel}");

            GetChild("CentreOther/Animation/Content/Search/Connet/LVSlider/AttackableTitle").SetActive(false);
            GetComponent<TFWText>("CentreOther/Animation/BG/Title/Title/TitleText").text = LocalizationMgr.Get("Searching_Title_Gather");
            GetComponent<TFWText>("CentreOther/Animation/Content/Search/Connet/LVSlider/TxtLv").text = _currGatherLevel.ToString();// $"{LocalizationMgr.Get("LC_MENU_level")} {_currGatherLevel}";
            LSearch.I.SearchLevel = _currGatherLevel;

            m_LevelSlider.onValueChanged.AddListener(OnSliderChange);
        }

        /// <summary>
        /// 搜索怪
        /// </summary>
        /// <param name="target"></param>
        /// <param name="eventData"></param>
        private void SearchEnemy(TFWTab target, PointerEventData eventData)
        {
            StargazingPlatform.gameObject.SetActive(false);
            GatherRally.gameObject.SetActive(false);
            Monster.gameObject.SetActive(true);
            //Debug.LogErrorFormat("点击搜索怪物");
            // anim1.enabled = false;
            // anim1.enabled = true;
            OnShowType = 0;
            m_LevelSlider.onValueChanged.RemoveAllListeners();

            LSearch.I.Choose = m_Choosed;
            ResetPop(true);

            var obj = GetChild("CentreOther/Animation/Content/Search/Connet/LVSlider/AttackableTitle");
            if (obj != null)
                obj.SetActive(true);

            var text = GetComponent<TFWText>("CentreOther/Animation/BG/Title/Title/TitleText");
            if (text != null)
                text.text = LocalizationMgr.Get("Searching_Title_Monster");

            m_LevelSlider.onValueChanged.AddListener(OnSliderChange);
        }

        /// <summary>
        /// 搜索占星台
        /// </summary>
        /// <param name="target"></param>
        /// <param name="eventData"></param>
        private void SearchStargazingPlatform(TFWTab target, PointerEventData eventData)
        {
            GetComponent<TFWText>("CentreOther/Animation/BG/Title/Title/TitleText").text = LocalizationMgr.Get("ProphecyInterface_19");
            NowChooseMissionID = 0;
            StargazingPlatform.gameObject.SetActive(true);
            GatherRally.gameObject.SetActive(false);
            Monster.gameObject.SetActive(false);
            StargazingPlatformDes1.text = "";
            StargazingPlatformDes2.text = "";
            if (LIntelligence.I.Level == 0)
            {
                //Debug.LogError("===============UISearch LIntelligence.I.RequestAllIntelligence();==============");
                LIntelligence.I.RequestAllIntelligence();
            }
            else
            {
                ShowStargazingPlatformData();
            }
        }
        private void ShowStargazingPlatformData()
        {
            Cfg.G.CPlanetarium data_now = CPlanetarium.RawDict().Where(t => t.Value.PlanetariumLevel == LIntelligence.I.Level).FirstOrDefault().Value;

            if (data_now != null)
            {
                StargazingPlatformDes1.text = string.Format(LocalizationMgr.Get("ProphecyInterface_17"), LIntelligence.I.Level);
                if (CPlanetarium.RawDict().Where(t => t.Value.PlanetariumLevel == LIntelligence.I.Level + 1).FirstOrDefault().Value != null)
                {
                    StargazingPlatformDes2.text = string.Format(LocalizationMgr.Get("ProphecyInterface_18"), data_now.UpgradeRequirement, LIntelligence.I.CompletedNum, data_now.UpgradeRequirement);
                }
                else
                {
                    StargazingPlatformDes2.text = LocalizationMgr.Get("ProphecyInterface_28");
                }
            }
        }
        private void OnIntelligenceAck(object[] args)
        {
            ShowStargazingPlatformData();
        }

        void OnSearchIntelligenceTargetAck(object[] args)
        {
            var ack = args[0] as SearchIntelligenceTargetAck;
            //Debug.LogError("===========================ack.mission.CfgId:" + ack.mission.CfgId);
            //Debug.LogError("===========================ack.mission.UnitId:" + ack.mission.UnitId);
            Close();

            var pos = new Vector3(ack.mission.PosX / 1000, ack.mission.PosZ / 1000);

            var entityInfo = LMapEntityManager.I.GetEntityInfo(ack.mission.UnitId);
            if (entityInfo != null)
            {
                pos = new Vector3(entityInfo.position.position.X / 1000, entityInfo.position.position.Z / 1000);
            }
            else
            {
                //Debug.LogError("未找到目标实例:" + ack.mission.UnitId);
            }


            //Debug.LogError("跳转坐标系-------------pos:::" + pos);

            RMap.JumpIntoTargetPosition(pos.x, pos.y, () =>
            {

            }, true);

        }

        int NowChooseMissionID = 0;
        void SearchRandomStargazingPlatform()
        {
            //随机一个情报任务
            if (NowChooseMissionID == 0)
            {
                int random = UnityEngine.Random.Range(0, LIntelligence.I.DicMission.Count);
                Mission mission = LIntelligence.I.DicMission.ElementAt(random).Value;
                if (mission != null)
                {
                    SearchIntelligenceTargetReq req = new SearchIntelligenceTargetReq();
                    req.id = mission.Id;
                    NowChooseMissionID = mission.Id;
                    MessageMgr.Send(req);
                }
            }
        }

        private IEnumerator InitSearchItems()
        {
            yield return null;

            mTitansLockImg.enabled = false;
            if (chronicleType == ChroniclSwitchEnum.Lock)
            {
                _tabSearchEnmyRally.isOn = false;
                mTitansLockImg.enabled = true;
                _tabSearchEnmyRally.enabled = false;
                if (OnShowType == 2)
                    OnShowType = 0;
            }
            else
            {
                _tabSearchEnmyRally.enabled = true;
            }

            _tabSearchEnmy.isOn = OnShowType == 0;
            _tabSearchGather.isOn = OnShowType == 1;
            _tabSearchEnmyRally.isOn = OnShowType == 2;
            _tabSearchStargazingPlatform.isOn = OnShowType == 3;

            bool setSearchItemResult = SetSearchItemButtons();

            while (!setSearchItemResult)
            {
                setSearchItemResult = SetSearchItemButtons();
                yield return null;
            }

            ResetPop(true);

            Search(true);

            if (OnShowType == 2)
            {
                _currEnmyRallyLevel = 1;
                this.SearchEnmyRally(null, null);
            }
            else if (OnShowType == 0)
            {
                SearchEnemy(null, null);
            }
            else if (OnShowType == 1)
            {
                SearchGather(null, null);
            }
            else if (OnShowType == 3)
            {
                SearchStargazingPlatform(null, null);
            }
        }

        protected override void RegisterEvent()
        {
            base.RegisterEvent();

            RegisterEvent(TEventType.WildMapSearchLock, (arg) => { m_BlockSearch = true; });
            RegisterEvent(TEventType.WildMapSearchUnlock, (arg) => { m_BlockSearch = false; });
            RegisterEvent(TEventType.WildMapSearchBeginJump, (arg) => { OnClose(null, null); });
            RegisterEvent(TEventType.SearchNoResult, (arg) =>
            {
                Close();
                //搜索不到运输车有特殊表现
                if (LSearch.I.CurrItem?.SearchId == 131934)
                {
                    MapCameraMgr.MoveCameraToTarget(RMap.ViewCenter.x, RMap.ViewCenter.z, "lod_8", 0.1f, 0.5f, null);
                }
            });
            RegisterEvent(TEventType.IntelligenceAck, OnIntelligenceAck);
            RegisterEvent(TEventType.SearchIntelligenceTargetAck, OnSearchIntelligenceTargetAck);
        }

        protected override void SetStaticText()
        {
            base.SetStaticText();

            //GetComponent<TFWText>("CentreOther/Animation/Search/Connet/LVSlider/TxtLv").text =
            //    $"{LocalizationMgr.Get("LC_MENU_level")} {LSearch.I.SearchLevel}";
        }

        protected override void OnShown()
        {
            //DEL BY UIBASE2POPUP             OnInit();

            //Debug.LogErrorFormat("=======UISearch===OnShown=================");
            base.OnShown();

            if (InitData is UISearchData data)
            {
                m_OnShown = data.onShown;
                OnShowType = data.SearchType;
            }
            

            chronicleType = CheckChronicleEvent();

            D.Warning?.Log($"当前集结：{chronicleType}");

            InitChoose();
            CoroutineMgr.StartCoroutine(InitSearchItems());

            m_OnShown?.Invoke(this);

            // anim.enabled = true;
            // NTimer.CountDown(0.7f, () => { anim.enabled = false; });
            //OnSearchClick(null,null);


            //Debug.LogError("============搜索页面================");




            bool StargazingPlatformOpen = LPlayer.I.UnlockBuild_ReBuild((int)CityType.Star);


            Vector2 Size = new Vector2(305, 80);
            if (false)
            {
                //观星台已开放，单屏显示四个按钮
                Size = new Vector2(226, 100);
            }
            else
            {
                //观星台未开放，单屏显示三个按钮
                Size = new Vector2(305, 80);
            }

            _tabSearchEnmy.GetComponent<RectTransform>().sizeDelta = Size;
            _tabSearchGather.GetComponent<RectTransform>().sizeDelta = Size;
            _tabSearchEnmyRally.GetComponent<RectTransform>().sizeDelta = Size;
            _tabSearchStargazingPlatform.GetComponent<RectTransform>().sizeDelta = Size;

        }

        protected override void OnDestroyed()
        {
            m_Choosed = 0;
            LSearch.I.Choose = 0;
            LSearch.I.SearchLevel = 1;
        }

        private void InitChoose()
        {
            //if (m_Choosed != LSearch.I.Choose && m_Choosed != m_BtnPositionOffsetList.Count - 1)
            //         {
            //             m_Choosed = LSearch.I.Choose;
            //         }
            m_Choosed = LPlayer.I.SearchEnmyId;
            LSearch.I.Choose = LPlayer.I.SearchEnmyId;
        }

        private void OnInputValueChanged(bool isX, TFWinputField input, string str)
        {
            int num = int.Parse(str);
            float numLimit = isX ? MapMgr.GetMapSize().x : MapMgr.GetMapSize().y;
            if (num > numLimit)
            {
                num = (int)numLimit;
            }

            input.text = num.ToString();
        }

        private readonly float LOD2_Height = 25.6f;

        private void OnLocationClick(GameObject arg0, PointerEventData arg1)
        {
            ////BILog.UserClick($"{Name}.{arg0.name}",$"{Name}",$"{Name}");

            var inputX = UIHelper.GetComponent<TFWinputField>(m_LocationPop, "Input1/InputTxt");
            var inputY = UIHelper.GetComponent<TFWinputField>(m_LocationPop, "Input2/InputTxt");
            int x = int.Parse(inputX.text);
            int y = int.Parse(inputY.text);
            var pos = MapMgr.CalculateCameraPositionFromLookAtPosition(x, y, LOD2_Height);
            LViewportJump.JumpWithMeter(pos, null);
        }

        private void OnClose(GameObject arg0, PointerEventData arg1)
        {
            Close();
        }

        protected override void OnHidden()
        {
            base.OnHidden();
            //DEL BY UIBASE2POPUP             InitData = null;
            //_tabSearchEnmy.isOn = true;
            //this.SearchEnmyRally(null, null);
            LSearch.I.NeedResearch = false;

            OnHide();
            GameAudio.PlayAudio(AudioConst.SearchClose);
        }

        private void OnSearchClick(GameObject arg0, PointerEventData arg1)
        {
            ////BILog.UserClick($"{Name}.{arg0.name}",$"{Name}",$"{Name}",new []{LSearch.I.CurrItem?.SearchId.ToString()});
            //GameAudio.PlayAudio(AudioID.UIGeneric);
            if (m_BlockSearch)
            {
                return;
            }
            Search();
        }

        /// <summary>
        /// 搜索
        /// </summary>
        private void Search(bool setIndex = false)
        {
            if (_tabSearchEnmy.isOn)
            {
                if (!setIndex)
                {
                    //如果只是设置索引 并且普通npc不搜索
                    LSearch.I.TrySearch();
                }
            }
            else if (_tabSearchGather.isOn)
            {
                LSearch.I.SearchUranium(setIndex);
            }
            else if (_tabSearchEnmyRally.isOn)
            {
                LSearch.I.SearchRallyEnemy(setIndex);
            }
            else if (_tabSearchStargazingPlatform.isOn)
            {
                SearchRandomStargazingPlatform();
            }
        }

        /// <summary>
        /// 点击打开购买体力界面
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        private void OnClickPhysicalBtn(GameObject arg0, PointerEventData arg1)
        {
            // PopupManager.I.ShowPanel<UIPhysicalPower>("UIPhysicalPower");
            
            PhysicalPowerMgr.I.ShowAddPowerDialog();
        }

        private void OnAddClick(GameObject arg0, PointerEventData arg1)
        {
            int maxLevel = LSeason.I.MaxRallyMonsterLevel;// (int)MetaConfig.MaxRallyMonsterNum;
            if (OnShowType == 2 && chronicleType == ChroniclSwitchEnum.UnLock)
            {
                //天下大势解锁泰坦等级
                if (chronicleTitanLv < maxLevel && m_LevelSlider.value == chronicleTitanLv)
                {
                    UITools.PopTips(LocalizationMgr.Get("chronicle_txt_21"));
                    return;
                }
            }

            LSearch.I.NeedResearch = true;
            m_LevelSlider.value += 1;

            UpdateSearchLevel();
            ////BILog.UserClick($"{Name}.SliderChangeVal",$"{Name}",$"{Name}",new []{LSearch.I.CurrItem?.SearchId.ToString()});
        }

        private void OnReduceClick(GameObject arg0, PointerEventData arg1)
        {
            LSearch.I.NeedResearch = true;
            m_LevelSlider.value -= 1;

            UpdateSearchLevel();
            ////BILog.UserClick($"{Name}.SliderChangeVal",$"{Name}",$"{Name}",new []{LSearch.I.CurrItem?.SearchId.ToString()});
        }

        /// <summary>
        /// 刷新搜索等级显示
        /// </summary>
        private void UpdateSearchLevel()
        {
            if (_tabSearchEnmy.isOn)
            {
                LSearch.I.TmpSearchLv[1] = LSearch.I.SearchLevel;
            }
            else if (_tabSearchGather.isOn)
            {
                LSearch.I.TmpSearchLv[3] = LSearch.I.SearchLevel;
            }
            else if (/*_tabSearchEnmyRally.isOn*/false)
            {
                LSearch.I.TmpSearchLv[2] = LSearch.I.SearchLevel;
            }
        }

        /// <summary>
        /// 设置搜索项的按钮
        /// </summary>
        private bool SetSearchItemButtons()
        {
            if (LSearch.I.SearchItemList == null)
                return false;



            var parent = GetChild("CentreOther/Animation/Content/Monster/MonsterScroll/SearchBar");
            var grid = GetChild("CentreOther/Animation/Content/Monster/MonsterScroll/SearchBar/Grid");
            var showNum = LSearch.I.SearchItemList.Count;
            //Debug.LogErrorFormat(" 搜索野怪+++++++++++++++++++++++++++++++++++++++++++++ " + LSearch.I.SearchItemList.Count+"  "+ grid.transform.childCount);
            //for (int i = 0; i < LSearch.I.SearchItemList.Count; i++)
            //{
            //	//Debug.LogErrorFormat("搜索野怪的单位item+----------------------------  " + LSearch.I.SearchItemList[i].NpcId + "  " + LSearch.I.SearchItemList[i].SearchId + "  " + LSearch.I.SearchItemList[i].MaxLevel+ "  " + LSearch.I.SearchItemList[i].Requirement);
            //}
            for (int i = showNum + 1; i < grid.transform.childCount; i++)
            {
                var obj = GetChild($"CentreOther/Animation/Content/Monster/MonsterScroll/SearchBar/Grid/Btn{i + 1}");
                if (obj != null)
                    obj.SetActive(false);
            }

            //计算底板长度
            // y = x * 150 + (x - 1) * 100 + 100
            //            var gridComponent = grid.GetComponent<GridLayoutGroup>();
            //            var btnWidth = gridComponent.cellSize.x;
            //            var btnInterval = gridComponent.spacing.x;
            //            var size = parent.GetComponent<RectTransform>().sizeDelta;
            //            parent.GetComponent<RectTransform>().sizeDelta =
            //                new Vector2(((btnWidth + btnInterval) * (showNum + 1) - btnInterval + 150), size.y);

            //根据条件判定是否置灰

            int idx = 0;
            for (; idx < LSearch.I.SearchItemList.Count && idx < grid.transform.childCount; idx++)
            {
                //if (idx != 2)//暂时只显示此怪
                //{
                //    continue;
                //}
                var searchItem = LSearch.I.SearchItemList[idx];

                var btn = GetChild($"CentreOther/Animation/Content/Monster/MonsterScroll/SearchBar/Grid/Btn{idx + 1}");
                var attribute = UIHelper.GetComponent<TFWImage>(btn, $"Item/Attribute");
                var icon = UIHelper.GetComponent<TFWImage>(btn, $"Item/Icon");
                var name = UIHelper.GetComponent<TFWText>(btn, $"Item/Text");
                var quality = UIHelper.GetComponent<TFWImage>(btn, $"Item/BG");
                var npcInfo = Cfg.C.CD2NpcTroopClass.I(searchItem == null ? 0 : searchItem.SearchId);
                UITools.SetQualityIcon(quality, npcInfo.NPCRank);
                if (npcInfo != null)
                {
                    if (npcInfo.LeaderComposition.Count > 0)
                    {
                        var heroId = npcInfo.LeaderComposition[0].Id;
                        var heroCfg = Cfg.C.CD2Hero.I(heroId);

                        if (heroCfg != null)
                        {
                            //cfg.Icon1
                            //Debug.LogError("heroCfg.name:" + heroCfg.Name);
                            //Debug.LogError("heroCfg.SoldiersType:" + heroCfg.SoldiersType);
                            //UITools.SetImage(attribute, HeroUtils.GetHeroAttributeByAttribute(heroCfg.SoldiersType), "hero", false);
                            UITools.SetImageBySpriteName(attribute, UITools.GetAttributeDisplayKey(heroCfg.SoldiersType));
                            UITools.SetCommonItemIcon(icon, heroCfg.Icon1);
                        }
                    }
                }

                //Debug.LogErrorFormat(searchItem.DisplayKey + "  DisplayKeyDisplayKeyDisplayKeyDisplayKeyDisplayKeyDisplayKey88888888888888888888888");
                //ResourceMgr.LoadImage(img, ResourceDict.GetProxyAssetPath(ProxyAssetType.K1_Icon, searchItem.DisplayKey,"NewItem"));

                var selectedObj = UIHelper.GetChild(btn, $"Selected");
                if (selectedObj != null)
                    selectedObj.SetActive(false);

                if (searchItem != null)
                    name.text = LocalizationMgr.Get(searchItem.Name);

                int refIndex = idx;
                if (m_BtnPositionOffsetList != null)
                    m_BtnPositionOffsetList.Add(icon.GetComponent<RectTransform>().position.x);
                if (searchItem != null && searchItem.Requirement)
                {
                    UIHelper.AddListener(EventTriggerType.Click, btn,
                        (arg1, arg2) => { OnItemClick(refIndex); }, true);
                }
                else
                {
                    SetBtnGrey(btn, true); 
                }

            }


            // 追加一个定位的功能在后边
            if (idx < grid.transform.childCount)
            {
                int refIndex = idx;
                var img = GetComponent<TFWImage>($"CentreOther/Animation/Content/Monster/MonsterScroll/SearchBar/Grid/Btn{idx + 1}/Icon");
                var obj = GetChild($"CentreOther/Animation/Content/Monster/MonsterScroll/SearchBar/Grid/Btn{idx + 1}/Selected");
                if (obj != null)
                    obj.SetActive(false);
               
                UITools.SetImageBySpriteName(img, "SearchLocalisation");
                if (m_BtnPositionOffsetList != null)
                    m_BtnPositionOffsetList.Add(img.GetComponent<RectTransform>().position.x);
                UIHelper.AddListener(EventTriggerType.Click, GetChild($"CentreOther/Animation/Content/Monster/MonsterScroll/SearchBar/Grid/Btn{idx + 1}"),
                    (arg1, arg2) => { OnLocationItemClick(refIndex); }, true);
            }

            return true;
        }

        /// <summary>
        /// 搜索怪物等级应对奖励
        /// </summary>
        private void MonsterNpcReward(int NpcID, int level)
        {
            var parent = GetChild("CentreOther/Animation/Content/Monster/MonsterScroll/Reward");
            // var Arrow = UIHelper.GetComponent<RectTransform>(parent, $"Arrow");
            var obj = GetChild($"CentreOther/Animation/Content/Monster/MonsterScroll/SearchBar/Grid/Btn{m_Choosed + 1}");
            // Arrow.anchoredPosition = new Vector2(obj.transform.localPosition.x, -5);

            m_PowerConfig = CNpcTroopPowerExtension.GetNpcTroopPower(NpcID, level);
            m_ItemGridRoot = GetComponent<UIGrid>($"CentreOther/Animation/Content/Monster/MonsterScroll/Reward/ScrollView/RssDetail/Grid");
            if (m_ItemGridRoot)
            {
                m_ItemGridRoot.Clear();

                foreach (var item in m_PowerConfig.DropDisplay)
                {
                    if (item.Id == 21113001)//金币
                    {
                        CommonItem.CommonItemData itemData = new CommonItem.CommonItemData();
                        int realNum = MetaConfig.NpcTroopReward[0] + (level - 1) * MetaConfig.NpcTroopReward[1];
                        itemData.Val = realNum > MetaConfig.NpcTroopReward[2] ? MetaConfig.NpcTroopReward[2] : realNum;
                        itemData.Id = item.Id;
                        itemData.Typ = item.Typ;

                        m_ItemGridRoot.AddItem<CommonItem>().InitData(itemData);
                    }
                    else
                    {
                        if (50000001 == item.Id)//冷却加速道具，跳到当前面板会有小手引导，所以要有特殊处理
                        {
                            var commonItem = m_ItemGridRoot.AddItem<CommonItem>();
                            commonItem.gameObject.name = item.Id.ToString();
                            commonItem.InitData(new CommonItem.CommonItemData(item));
                        }
                        else
                        {
                            m_ItemGridRoot.AddItem<CommonItem>().InitData(new CommonItem.CommonItemData(item));
                        }

                    }
                }
            }
        }

        private void OnLocationItemClick(int refIndex)
        {
            if (refIndex == m_Choosed) return;
            ChangeChoose(refIndex);
            OffsetLocationPop();
            ////BILog.UserClick($"{Name}.ItemClick{refIndex}",$"{Name}",$"{Name}");
        }

        private void OffsetLocationPop()
        {
            if (m_DetailPop.activeSelf)
            {
                m_DetailPop.SetActive(false);
            }

            if (!m_LocationPop.activeSelf)
            {
                m_LocationPop.SetActive(true);
            }

            m_LocationPop.SetActive(true);
            var offsetPos = m_LocationPop.GetComponent<RectTransform>().position;
            offsetPos.x = m_BtnPositionOffsetList[m_BtnPositionOffsetList.Count - 1];
            m_LocationPop.GetComponent<RectTransform>().position = offsetPos;
        }

        protected override void OnUpdate(float deltaTime)
        {
            m_InputX.text = RMap.ViewCenter.x.ToString();
            m_InputY.text = RMap.ViewCenter.x.ToString();
        }

        private void ChangeChoose(int refIndex)
        {
            var chooseObj = GetChild($"CentreOther/Animation/Content/Monster/MonsterScroll/SearchBar/Grid/Btn{m_Choosed + 1}/Selected");
            if (chooseObj != null)
                chooseObj.SetActive(false);

            // var refIndexObj = GetChild($"CentreOther/Animation/Content/Monster/MonsterScroll/SearchBar/Grid/Btn{refIndex + 1}/Selected");
            // if (refIndexObj != null)
            //     refIndexObj.SetActive(true);

            m_Choosed = refIndex;

            LPlayer.I.SearchEnmyId = refIndex;
            MonsterNpcReward(LSearch.I.SearchItemList[m_Choosed].SearchId, LSearch.I.SearchLevel);
        }

        

        private void OnItemClick(int refIndex)
        {
            ////BILog.UserClick($"{Name}.ItemClick{refIndex}",$"{Name}",$"{Name}");
            //Debug.LogErrorFormat("搜索野怪？？？？？？？？？？？？？？？？？"+ refIndex);
            //GameAudio.PlayAudio(AudioID.UIGeneric);
            if (m_Choosed == refIndex) return;
            if (m_LocationPop.activeSelf)
            {
                m_LocationPop.SetActive(false);
            }

            if (!m_DetailPop.activeSelf)
            {
                m_DetailPop.SetActive(true);
            }

            LSearch.I.NeedResearch = true;
            ChangeChoose(refIndex);
            LSearch.I.Choose = refIndex;
            ResetPop();
        }

        private void ResetPop(bool isInit = false)
        {
            if (LSearch.I.CurrItem == null)
            {
                return;
            }

            //var npcId = LSearch.I.CurrItem.NpcId;
            //var npcConfig = CNpcTroop.I(int.Parse(npcId));
            var npcId = LSearch.I.CurrItem.SearchId;
            var npcConfig = CD2NpcTroopClass.I(npcId);
            m_LevelSlider.maxValue = LSearch.I.CurrItem.MaxLevel;
            m_LevelSlider.minValue = 1;

            if (npcConfig != null)
            {
                var preAttackId = LPlayer.I.GetKillOrderData(npcConfig.Id);

                //var theMaxLvl = preAttackId != -1 ? CNpcTroop.I(preAttackId).Lvl + 1 : 1;
                //m_LevelSlider.maxValue = theMaxLvl;
                //m_LevelSlider.value = preAttackId == -1 ? 1 : CNpcTroop.I(preAttackId).Lvl;

                var theMaxLvl = preAttackId != -1 ? preAttackId + 1 : 1;
                m_LevelSlider.maxValue = theMaxLvl;
                int preAttackIdTemp = preAttackId != -1 ? preAttackId + 1 : 1;
                int searchLevel = FightManager.I.TrainLevel - 5 > preAttackIdTemp ? theMaxLvl : preAttackIdTemp;
                searchLevel = Mathf.Max(1, searchLevel);
                if (_tabSearchEnmyRally.isOn)
                {
                    //集结最小10
                    searchLevel = Mathf.Min(LSeason.I.MaxRallyMonsterLevel, Mathf.Max((int)MetaConfig.MassMonsterMinLevel, searchLevel));
                }

                if (this._tabSearchEnmy.isOn)
                {
                    //if (LSearch.I.TmpSearchLv.ContainsKey(1))
                    //{
                    //    searchLevel = LSearch.I.TmpSearchLv[1];
                    //}

                    if (LSearch.I.TryGetTmpSearchLv(npcConfig.Id, out var level))
                    {
                        //限制计算超过上限
                        if (LPlayer.I.GetMainCityLevel() > MetaConfig.KillMonstersLevelLimit)
                        {
                            if (level > theMaxLvl)
                            {
                                level = theMaxLvl;
                            }
                        }

                        searchLevel = level;
                    }
                }

                if (/*_tabSearchEnmyRally.isOn*/false)
                {
                    if (LSearch.I.TmpSearchLv.ContainsKey(2))
                    {
                        searchLevel = LSearch.I.TmpSearchLv[2];
                    }
                    else
                    {
                        //集结怪如果不存在搜索记录,默认应该为1级
                        searchLevel = 1;
                    }
                }

                if (this._tabSearchGather.isOn)
                {
                    if (LSearch.I.TmpSearchLv.ContainsKey(3))
                    {
                        searchLevel = LSearch.I.TmpSearchLv[3];
                    }
                }


                //Debug.LogErrorFormat($"设置等级{searchLevel}");
                m_LevelSlider.value = searchLevel;
                if (theMaxLvl == 1)
                {
                    m_LevelSlider.minValue = 0;
                }
                GetComponent<TFWText>("CentreOther/Animation/Content/Search/Connet/LVSlider/TxtLv").text = searchLevel.ToString();// $"{LocalizationMgr.Get("LC_MENU_level")} {searchLevel}";
                LSearch.I.SearchLevel = searchLevel;
            }
            //m_LevelSlider.value = isInit ? LSearch.I.SearchLevel : 1;


            ChangeChoose(m_Choosed);
            //if (m_Choosed == m_BtnPositionOffsetList.Count - 1)
            //{
            //    OffsetLocationPop();
            //}
            //else
            //{
            OffsetDetailPop();
            SetCurDesc(isInit);
            //}
        }

        private void SetCurDesc(bool isInit)
        {
            var text = LocalizationMgr.Get(LSearch.I.CurrItem.Desc);
            //GetComponent<TFWText>("CentreOther/Animation/Search/Title/Title").text = text;
            //var npcId = LSearch.I.CurrItem.NpcId;
            var npcId = LSearch.I.CurrItem.SearchId;
            //GetChild( "CentreOther/Animation/Search/Connet/LVSlider/Slider").SetActive(LSearch.I.CurrItem.MaxLevel > 0);
            //if (LSearch.I.CurrItem.MaxLevel == 0) LSearch.I.SearchLevel = 0;
            //else
            //{
            //    if (!isInit) LSearch.I.SearchLevel = 1;
            //}

            //var npcConfig = CNpcTroop.I(int.Parse(npcId));
            var npcConfig = CD2NpcTroopClass.I(npcId);
            if (npcConfig != null)
            {

                var preAttackId = LPlayer.I.GetKillOrderData(npcConfig.Id);
                //text = preAttackId == -1
                //	? "1"
                //	: LocalizationMgr.Format("LC_NPC_npc_barbarian_locked", CNpcTroop.I(preAttackId).Lvl + 1);
                //text = preAttackId == -1 ? "1" : (CNpcTroop.I(preAttackId).Lvl + 1).ToString();
                text = preAttackId == -1 ? "1" : (preAttackId + 1).ToString();
                //Debug.LogErrorFormat(" preAttackIdpreAttackIdpreAttackIdpreAttackIdpreAttackIdpreAttackId************************ "+ preAttackId);
            }
            else
            {
                text = string.Empty;
            }
            //这个文本现在删掉了
            //GetComponent<TFWText>("CentreOther/Animation/Search/Title/Title").text = LocalizationMgr.Get(LSearch.I.CurrItem.Name);
            //GetComponent<TFWText>("CentreOther/Animation/Search/Connet/LVSlider/TxtDescribe").text = text;
            GetComponent<TFWText>("CentreOther/Animation/Content/Search/Connet/LVSlider/AttackableTitle").text = $"{LocalizationMgr.Get("LC_NPC_npc_barbarian_locked")} ";
            GetComponent<TFWText>("CentreOther/Animation/Content/Search/Connet/LVSlider/AttackableTitle/Attackable").text = text;
        }

        private void OffsetDetailPop()
        {
            var curItem = LSearch.I.CurrItem;
            if (curItem == null)
            {
                return;
            }
            //现在是让搜索面板显示固定在屏幕中心 位置不在做偏移
            //var offsetPos = m_DetailPop.GetComponent<RectTransform>().position;
            //offsetPos.x = m_BtnPositionOffsetList[LSearch.I.Choose];
            //m_DetailPop.GetComponent<RectTransform>().position = offsetPos;
        }

        private void SetBtnGrey(GameObject btn, bool bGrey)
        {
            UIHelper.GetComponent<TFWImage>(btn, "Icon").Grey = bGrey;
        }

        #endregion

        #region 天下大势集结事件

        private int chronicleTitanLv = 0;
        private int chronicleTitanLockEventId = 0;
        private ChroniclSwitchEnum chronicleType = ChroniclSwitchEnum.Lock;
        /// <summary>
        /// 获取集结天下大势类型
        /// </summary>
        /// <returns></returns>
        private ChroniclSwitchEnum CheckChronicleEvent()
        {
            if (MetaConfig.ChronicleTitanDefaultOpen.Length > LSeason.I.SeasonId)
            {
                chronicleTitanLv = MetaConfig.ChronicleTitanDefaultOpen[LSeason.I.SeasonId];
            }
            else
            {
                chronicleTitanLv = MetaConfig.ChronicleTitanDefaultOpen[MetaConfig.ChronicleTitanDefaultOpen.Length-1];
            }
            
            //chronicleTitanLockEventId = minLvEventID;
            return ChroniclSwitchEnum.UnLock; 
            //if (!ChronicleMgr.I.ChronicleEventSwitch)
            //    return ChroniclSwitchEnum.Close;

            ////限定某些服务器不走天下大势集结逻辑
            //var serverId = LoginMgr.I.loginData.serverid;
            //if (MetaConfig.ChronicleSpecialDeal != null)
            //{
            //    var count = MetaConfig.ChronicleSpecialDeal.Length;
            //    int min = 0;
            //    int max = 0;
            //    int index = 0;
            //    for (int i = 0; i < count; i++)
            //    {
            //        index++;
            //        if (index == 1)
            //        {
            //            min = MetaConfig.ChronicleSpecialDeal[i];
            //        }
            //        else if (index == 2)
            //        {
            //            max = MetaConfig.ChronicleSpecialDeal[i];
            //        }
            //        else
            //        {
            //            if (serverId >= min && serverId <= max)
            //                return ChroniclSwitchEnum.Close;

            //            index = 0;
            //        }
            //    }
            //}

            ////只有第一赛季存在泰坦解锁功能
            //if (LSeason.I.SeasonId != 1)
            //    return ChroniclSwitchEnum.Close;

            //var data = GameData.I.ChronicleData.eventTpeDic;
            //if (data == null)
            //    return ChroniclSwitchEnum.Close;

            //if (!data.TryGetValue((int)ChronicleEventTypeEnum.TitansLV, out var dt))
            //    return ChroniclSwitchEnum.Close;

            //if (dt == null)
            //    return ChroniclSwitchEnum.Close;

            //int maxLv = 0;
            //int minLv = 999;
            //int minLvEventID = 0;
            //bool pass = false; //记录是否有完成的事件
            //foreach (var cfg in dt)
            //{
            //    if (cfg.EventParam1 <= 0)
            //        continue;

            //    int lastMaxLv = maxLv;
            //    if (maxLv < cfg.EventParam1)
            //        maxLv = cfg.EventParam1;

            //    if (cfg.EventParam1 < minLv)
            //    {
            //        minLv = cfg.EventParam1;
            //        minLvEventID = cfg.Id;
            //    }

            //    var ok = ChronicleMgr.I.CheckChronicleEventVisible(ChronicleEventTypeEnum.TitansLV, maxLv);
            //    if (ok)
            //        pass = true;
            //    else
            //        maxLv = lastMaxLv;
            //}

            //chronicleTitanLv = maxLv;
            //chronicleTitanLockEventId = minLvEventID;

            //if (!pass)
            //    return ChroniclSwitchEnum.Lock;

            //return ChroniclSwitchEnum.UnLock;
        }
        #endregion
    }
}
//MODIFIED BY UIBASE2POPUP
//MODIFIED BY UIBASE2POPUP
//MODIFIED BY UIBASE2POPUP
//MODIFIED BY UIBASE2POPUP
