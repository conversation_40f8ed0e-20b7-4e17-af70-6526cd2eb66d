using System;
using System.Collections;
using System.Collections.Generic;
using Common;
using Config;
using cspb;
using Game.Data;
using Game.Utils;
using Logic;
using P2;
using Public;
using TFW;
using TFW.Localization;
using TFW.UI;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using EventTriggerType = TFW.EventTriggerType;
using DeepUI;
using Cysharp.Threading.Tasks;

namespace UI
{
    /// <summary>
    /// ui冲关活动排行奖励数据
    /// </summary>
    public class UIActivityCommanderRankRewardData : PopupData
    {
        /// <summary>
        /// 获得id
        /// </summary>
        public long activityId;

        /// <summary>
        /// 阶段
        /// </summary>
        public int stage;

        /// <summary>
        /// 活动类型
        /// </summary>
        public ActivityType activityType;
    }

    /// <summary>
    /// 最强指挥官排行奖励界面
    /// </summary>
    [Popup("Activity/UIActivityCommanderRankReward_New")]
    public class UIActivityCommanderRankReward : HistoricPopup
    {
        //public override ESortingOrder SortingOrder { get => ESortingOrder.AllianceRally + 1; }


        #region Field

        /// <summary>
        /// 标题
        /// </summary>
        private TFWText titleText;

        /// <summary>
        /// tab
        /// </summary>
        private TFWTabGroup tabGroup;


        protected string assetPath => "Activity/UIActivityCommanderRankReward_New";
        private bool hasReqData = false;

        #endregion

        #region Override

        /// <summary>
        /// OnInit
        /// </summary>
        protected override void OnInit()
        {
            base.OnInit();

        }

        /// <summary>
        /// 排行列表root
        /// </summary>
        private GameObject rankListRoot;

        /// <summary>
        /// 奖励列表根部
        /// </summary>
        private GameObject rewardListRoot;

        /// <summary>
        /// 排行列表root
        /// </summary>
        private GameObject rankListButtom;

        /// <summary>
        /// 奖励列表根部
        /// </summary>
        private GameObject rewardListButtom;

        /// <summary>
        /// 奖励列表根部
        /// </summary>
        private ScrollViewPro rewardListScrollView;

        /// <summary>
        /// 奖励列表根部
        /// </summary>
        private ScrollViewPro rankListScrollView;

        /// <summary>
        /// 排行奖励
        /// </summary>
        private RankOrReward rankOrReward;

        private RectTransform BG;
        private RectTransform BG1;
        private RectTransform RewardListButtom;
        private RectTransform RankListButtom;

        /// <summary>
        /// 排行奖励
        /// </summary>
        private RankOrReward RankOrRewardType
        {
            get
            {
                return rankOrReward;
            }

            set
            {
                if (rankOrReward != value)
                {
                    this.OnRankOrRewardChange(value);
                }
                rankOrReward = value;
                RefreshBgState();
            }
        }

        /// <summary>
        /// 奖励按钮
        /// </summary>
        private GameObject rewardBtn;

        /// <summary>
        /// 排行按钮
        /// </summary>
        private GameObject rankBtn;

        /// <summary>
        /// 总榜
        /// </summary>
        private GameObject rankListTitle;

        /// <summary>
        /// 自己的榜
        /// </summary>
        private GameObject selfItem;

        /// <summary>
        /// Defualt Tab
        /// </summary>
        private TFWTab defualtTab;

        /// <summary>
        /// 普通排行背景
        /// </summary>
        private GameObject rankBg1;

        /// <summary>
        /// 总排行背景
        /// </summary>
        private GameObject rankBg2;

        /// <summary>
        /// 奖励背景
        /// </summary>
        private GameObject rewardBg;


        /// <summary>
        /// OnLoad
        /// </summary>
        protected override void OnLoad()
        {
            var closeBtn = GetChild("Root/Title/CloseBtn");
            rankBg1 = GetChild("Root/RankBG1");
            rankBg2 = GetChild("Root/RankBG2");
            rewardBg = GetChild("Root/RewardBG");

            titleText = GetComponent<TFWText>("Root/Title/TitleText");

            rewardListRoot = GetChild("Root/BG/BG1/RewardListScrollView");
            rankListRoot = GetChild("Root/BG/BG1/RankListScrollView");

            rankListTitle = GetChild("Root/BG/BG1/RankListTitle");

            rewardListButtom = GetChild("Root/BG/BG1/RewardListButtom");
            rankListButtom = GetChild("Root/BG/BG1/RankListButtom");
            selfItem = GetChild("Root/BG/BG1/RankListButtom/Item");

            rewardListScrollView = rewardListRoot.GetComponent<ScrollViewPro>();
            rankListScrollView = rankListRoot.GetComponent<ScrollViewPro>();
            rewardListScrollView.onInitializeItem += InitRewardListItem;
            rankListScrollView.onInitializeItem += InitRankListItem;
            rankListScrollView.onOutEnd += OnOutEnd;

            tabGroup = GetComponent<TFWTabGroup>("Root/BG/BG1/Tab");
            for (int i = 1; i <= 2; ++i)
            {
                var tab = GetComponent<TFWTab>("Root/BG/BG1/Tab/tab" + i);
                if (i == 1)
                {
                    defualtTab = tab;
                }
                //var tagIndex = ConvertUtils.GetIntFromString(tab.TabTag);
                tabGroup.AddSingleTabClickEvent(tab, (target, args) => { OnTabClick(tab.name); });
            }
            //m_LeftTabRoot.TurnTabOn(((int)m_UIChatData.CurrChatTab)

            AddListener(TFW.EventTriggerType.Click, closeBtn, (a, n) =>
            {
                Close();
            });


            rewardBtn = GetChild("Root/BG/BG1/Tab/RewardBtn");
            rankBtn = GetChild("Root/BG/BG1/Tab/RankBtn");

            AddListener(TFW.EventTriggerType.Click, rewardBtn, OnShowRewardPanel);

            AddListener(TFW.EventTriggerType.Click, rankBtn, OnShowRankPanel);

            BG = GetComponent<RectTransform>("Root/BG");
            BG1 = GetComponent<RectTransform>("Root/BG/BG1");
            RewardListButtom = GetComponent<RectTransform>("Root/BG/BG1/RewardListButtom");
            RankListButtom = GetComponent<RectTransform>("Root/BG/BG1/RankListButtom");
        }

        /// <summary>
        /// 是否为总榜
        /// </summary>
        private bool IsTotalRank { get; set; }

        /// <summary>
        /// 切换奖励和排行
        /// </summary>
        private void SwitchRankReward(bool isReward)
        {
            rewardBtn.SetActive(!isReward);
            rewardListRoot.SetActive(isReward);
            rewardListButtom.SetActive(isReward);
            rankListRoot.SetActive(!isReward);
            rankListButtom.SetActive(!isReward);
            rankBtn.SetActive(isReward);

            if (this.data != null && (data.activityType == ActivityType.WorldBoss))
            {
                rankListTitle.SetActive(false);
            }
            else
            {
                //如果是排行帮且是总榜
                rankListTitle.SetActive(!isReward && IsTotalRank);
            }
        }

        /// <summary>
        /// 显示类型改变
        /// </summary>
        /// <param name="switchType"></param>
        private void OnRankOrRewardChange(RankOrReward switchType)
        {
            //类型改变需要重置Tab
            this.tabGroup.TurnTabOn(defualtTab);
            this.IsTotalRank = false;

            //ActivityLogUtils.Log("OnRankOrRewardChange={0}", switchType);
        }

        /// <summary>
        /// 背景显示状态
        /// </summary>
        private void RefreshBgState()
        {
            var isRank = this.RankOrRewardType == RankOrReward.Rank;
            rankBg1.SetActive(isRank && !IsTotalRank);
            rankBg2.SetActive(isRank && IsTotalRank);
            rewardBg.SetActive(!isRank);
        }

        /// <summary>
        /// 排行面板
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        private void OnShowRankPanel(GameObject arg0, PointerEventData arg1)
        {
            if (arg0)
            {//手动点击刷新tab
                ResetTabTag();
            }
            RankOrRewardType = RankOrReward.Rank;//默认显示排行
            SwitchRankReward(false);
            RefreshData();

            rankListScrollView.gameObject.SetActive(true);
            this.rankListScrollView.maxNum = this.actvRankItem.Count;
            this.rankListScrollView.refresh_list();
        }

        /// <summary>
        /// 奖励面板
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        private void OnShowRewardPanel(GameObject arg0, PointerEventData arg1)
        {
            if (arg0)
            {//手动点击刷新tab
                ResetTabTag();
            }
            RankOrRewardType = RankOrReward.Reward;//默认显示排行
            SwitchRankReward(true);
            RefreshData();
            rewardListScrollView.gameObject.SetActive(true);
            this.rewardListScrollView.maxNum = this.actvRankRewards.Count;
            this.rewardListScrollView.refresh_list();
        }

        #region 排行


        void OnOutEnd(bool state)
        {
            if (IsTotalRank)
            {
                ///总榜
                GameData.I.ActivityData.ReqActvRankList(data.activityId, 0, LPlayer.I.PlayerID, actvRankItem.Count);
            }
            else
            {
                GameData.I.ActivityData.ReqActvRankList(data.activityId, data.stage, LPlayer.I.PlayerID, actvRankItem.Count);
            }
        }

        /// <summary>
        /// 排行item
        /// </summary>
        /// <param name="go"></param>
        /// <param name="index"></param>
        private void InitRankListItem(GameObject go, int index)
        {
            for (int i = 1; i < 4; i++)
            {
                var icon = UIHelper.GetChild(go, string.Format("Item/Icon{0}", i));
                if (index < 4)
                {
                    icon.SetActive(index == i);
                }
                else
                {
                    icon.SetActive(false);
                }
            }

            index = index - 1; //index是从1开始的
                               //前3名显示icon
            if (this.actvRankItem != null)
            {
                if (index < actvRankItem.Count)
                {
                    var itemData = actvRankItem[index];
                    SetItem(UIHelper.GetChild(go, "Item"), itemData);
                }
            }
        }

        /// <summary>
        /// 设置单个item
        /// </summary>
        /// <param name="go"></param>
        /// <param name="itemData"></param>
        private void SetItem(GameObject go, RankItemData itemData, bool isSelf = false)
        {
            var nameTxt = UIHelper.GetComponent<TFWText>(go, "Name");//[xxx]
            var rankTxt = UIHelper.GetComponent<TFWText>(go, "NumText");//4.
            var powerTxt = UIHelper.GetComponent<TFWText>(go, "root/PowerText");//
            // change by yujiawei . 不知道原代码为啥加一个 root路径。但是有的item是不包含root路径的，所以这边要兼容一下
            if (powerTxt == null)
            {
                powerTxt = UIHelper.GetComponent<TFWText>(go, "PowerText");
            }
            var serverTxt = UIHelper.GetComponent<TFWText>(go, "root/Server/Text");//
            var serverBg = UIHelper.GetComponent<TFWImage>(go, "root/Server/BG");//
            var serverObj = UIHelper.GetChild(go, "root/Server");//
            var head = UIHelper.GetChild(go, "Head_k1");
            var headIcon = UIHelper.GetChild(head, "Mask/Icon");
            var self = UIHelper.GetChild(go, "Self");

            if (serverObj)
                serverObj.SetActive(this.data.activityType == ActivityType.ActivityCrossServerWar);

            if (itemData != null)
            {
                if (self != null)
                {
                    self.SetActive(itemData.rankItem.ID == LPlayer.I.PlayerID);
                }

                if (rankTxt)
                {
                    rankTxt.text = itemData.rankItem.place.ToString();
                }
                //powerTxt.text = itemData.rankItem.score.ToString();
                if (powerTxt)
                    powerTxt.text = UIStringUtils.FormatIntegerByLanguage(itemData.rankItem.score);
                if (itemData.meta != null)
                {
                    string myName;
                    if (!string.IsNullOrEmpty(itemData.meta.UnionNickName))
                    {
                        //unionName = LAllianceMgr.I.GetUnionName();
                        myName = string.Format("[{0}]{1}", itemData.meta.UnionNickName, itemData.meta.PlayerName);
                    }
                    else
                    {
                        myName = itemData.meta.PlayerName;
                    }
                    nameTxt.text = string.Format("{0}", myName);

                    if (serverBg)
                    {
                        GameData.I.ActivityData.GetCrollServerColor(itemData.meta.serverID).ContinueWith((color) =>
                        {
                            serverBg.color = ColorUtils.ColorFromRGBAString(color);
                        });
                    }
                    if (serverTxt)
                        serverTxt.text = itemData.meta.serverID.ToString();
                    PlayerIconWidget headWidget = new PlayerIconWidget(head);
                    headWidget.InitData(new Icon()
                    {
                        cfgID = itemData.meta.AvatarCfgID,
                        customHead = itemData.meta.customHead,
                        useCustomHead = itemData.meta.useCustomHead,
                        avatarFrrame = itemData.meta.avatarFrrame,
                    });
                }

                //head 头像需要通过玩家id进行拉去
                //LPlayer.I.CustomPlayerHead
                //UITools.SetHead



                //if (itemData.ID == LPlayer.I.PlayerID)
                //{
                //    PlayerIconWidget headWidget = new PlayerIconWidget(head);
                //    headWidget.InitData(new Icon()
                //    {
                //        cfgID = LPlayer.I.AvatarCfgID,
                //        customHead = LPlayer.I.CustomPlayerHead,
                //        useCustomHead = LPlayer.I.UseCustomHeadIcon
                //    });
                //}
                //else {
                //    PlayerIconWidget headWidget = new PlayerIconWidget(head);
                //    headWidget.InitData(new Icon()
                //    {
                //        cfgID = LPlayer.I.AvatarCfgID,
                //        customHead = LPlayer.I.CustomPlayerHead,
                //        useCustomHead = LPlayer.I.UseCustomHeadIcon
                //    });
                //}
            }
            else
            {
                if (isSelf)
                {
                    if (headIcon) headIcon.SetActive(true);
                    #region Name
                    string myName;
                    string unionNickName = "";
                    if (LPlayer.I.IsPlayerInUnion())
                    {
                        var unionInfo = LAllianceMgr.I.GetUnionInfo();
                        if (unionInfo != null)
                        {
                            unionNickName = unionInfo.NickName;
                        }
                    }
                    if (selfPos == 0 || selfPos == -1)
                    {
                        rankTxt.text = "-";
                    }
                    else
                    {
                        rankTxt.text = this.selfPos.ToString();
                    }

                    if (powerTxt)
                        powerTxt.text = UIStringUtils.FormatIntegerByLanguage(selfScore);


                    if (serverBg)
                    {
                        GameData.I.ActivityData.GetCrollServerColor(LPlayer.I.ServerId).ContinueWith((color) =>
                        {
                            serverBg.color = ColorUtils.ColorFromRGBAString(color);
                        });
                    }
                    if (serverTxt)
                        serverTxt.text = LPlayer.I.ServerId.ToString();
                    myName = LPlayer.I.PlayerName;
                    if (!string.IsNullOrEmpty(unionNickName))
                    {
                        //var unionName = LAllianceMgr.I.GetUnionName();
                        //D.Error?.Log("itemData.meta.customHead," + itemData.meta.customHead);
                        myName = string.Format("[{0}]{1}", unionNickName, myName);
                    }

                    #endregion

                    nameTxt.text = string.Format("{0}", myName);
                    PlayerIconWidget headWidget = new PlayerIconWidget(head);
                    headWidget.InitData(new Icon()
                    {
                        cfgID = LPlayer.I.AvatarCfgID,
                        customHead = LPlayer.I.CustomPlayerHead,
                        useCustomHead = LPlayer.I.UseCustomHeadIcon,
                        avatarFrrame = GameData.I.headFrameGameData.CurData.cfgId
                    });
                    headWidget = null;
                }
                else
                {
                    if (powerTxt)
                        powerTxt.text = "0";
                    if (rankTxt)
                    {
                        rankTxt.text = "999+";
                    }
                    if (headIcon) headIcon.SetActive(false);
                }
            }

        }
        #endregion

        #region 奖励

        /// <summary>
        /// 奖励item
        /// </summary>
        /// <param name="go"></param>
        /// <param name="index"></param>
        private void InitRewardListItem(GameObject go, int index)
        {
            var idx = index - 1;//从1开始
            var grid = UIHelper.GetChild(go, "Item/Grid");
            if (idx < this.actvRankRewards.Count)
            {
                var numTxt = UIHelper.GetComponent<TFWText>(go, "Item/NumText");

                var itemData = this.actvRankRewards[idx];
                if (itemData.fromRank != itemData.toRank)
                {
                    numTxt.text = string.Format("{0}-{1}", itemData.fromRank, itemData.toRank);
                }
                else
                {
                    numTxt.text = itemData.fromRank.ToString();
                }

                if (index < 4)
                {
                    //前3名显示icon
                    for (int i = 1; i < 4; i++)
                    {
                        var icon = UIHelper.GetChild(go, string.Format("Item/Icon{0}", i));
                        icon.SetActive(index == i);
                    }
                }
                else
                {
                    for (int i = 1; i < 4; i++)
                    {
                        var icon = UIHelper.GetChild(go, string.Format("Item/Icon{0}", i));
                        icon.SetActive(false);
                    }
                }


                for (int i = 0; i < 5; i++)
                {
                    RewardItemUI rewardItemGo = grid.transform.GetChild(i).gameObject.GetComponent<RewardItemUI>();
                    RemoveListener(EventTriggerType.Click, rewardItemGo.gameObject);
                    AddListener(EventTriggerType.Click, rewardItemGo.gameObject, OnRewardItemClick);

                    RemoveListener(EventTriggerType.BeginDrag, rewardItemGo.gameObject);
                    RemoveListener(EventTriggerType.Drag, rewardItemGo.gameObject);
                    RemoveListener(EventTriggerType.EndDrag, rewardItemGo.gameObject);

                    //注册滑动
                    AddListener(TFW.EventTriggerType.BeginDrag, rewardItemGo.gameObject, (x, y) =>
                    {
                        this.rewardListScrollView.onDragStart(x, y);
                    });
                    AddListener(TFW.EventTriggerType.Drag, rewardItemGo.gameObject, (x, y) =>
                    {
                        this.rewardListScrollView.onDrag(x, y);
                    });
                    AddListener(TFW.EventTriggerType.EndDrag, rewardItemGo.gameObject, (x, y) =>
                    {
                        this.rewardListScrollView.onDragEnd(x, y);
                    });

                    if (itemData.display != null && i < itemData.display.Count)
                    {
                        rewardItemGo.gameObject.SetActive(true);
                        rewardItemGo.SetData(itemData.display[i]);
                    }
                    else
                    {
                        rewardItemGo.gameObject.SetActive(false);
                    }
                }
            }
        }

        #endregion

        private string curTabTag = "tab1";

        /// <summary>
        /// 重置tab
        /// </summary>
        private void ResetTabTag()
        {
            curTabTag = "tab1";
        }

        /// <summary>
        /// 页签
        /// </summary>
        /// <param name="tagIndex"></param>
        private void OnTabClick(string tabTag)
        {
            if (curTabTag == tabTag)
            {
                //ActivityLogUtils.Error("OnTabClick return ={0} rankOrReward={1}", IsTotalRank, RankOrRewardType);
                return;
            }
            curTabTag = tabTag;
            IsTotalRank = tabTag == "tab2";
            //ActivityLogUtils.Error("OnTabClick IsTotalRank ={0} rankOrReward={1}", IsTotalRank, RankOrRewardType);
            if (IsTotalRank)
            {
                if (this.data != null)
                {
                    if (this.RankOrRewardType == RankOrReward.Rank)
                    {
                        rankListTitle.SetActive(true);
                    }
                    if (!hasReqData)
                    {
                        GameData.I.ActivityData.ReqActvRankList(data.activityId, 0, LPlayer.I.PlayerID);//请求总排行
                        hasReqData = true;
                    }
                    else
                    {
                        if (this.RankOrRewardType == RankOrReward.Rank)
                        {
                            if (rankListScrollView)
                                rankListScrollView.set_index(0);
                            this.OnShowRankPanel(null, null);
                        }
                        else
                        {
                            this.OnShowRewardPanel(null, null);
                        }
                    }
                }
            }
            else
            {
                if (this.RankOrRewardType == RankOrReward.Rank)
                {
                    if (rankListScrollView)
                        rankListScrollView.set_index(0);
                    this.OnShowRankPanel(null, null);
                }
                else
                {
                    this.OnShowRewardPanel(null, null);
                }
            }
            RefreshBgState();
        }


        /// <summary>
        /// 奖励
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        private void OnRewardItemClick(GameObject arg0, PointerEventData arg1)
        {

        }

        //[global::ProtoBuf.ProtoMember(5)]
        public long selfScore { get; set; }

        //[global::ProtoBuf.ProtoMember(6)]
        public int selfPos { get; set; }

        /// <summary>
        /// 注册事件
        /// </summary>
        /*protected override void RegisterEvent()
        {
            base.RegisterEvent();
            EventMgr.RegisterEvent(TEventType.ActvRankListAck, OnActvRankListAck, this);
        }*/

        /// <summary>
        /// 刷新数据-切换排行或奖励
        /// </summary>
        private void RefreshData()
        {
            var stage = data.stage;
            if (this.IsTotalRank)
            {
                stage = 0;//总排行
            }

            for (int i = 1; i < 7; i++)
            {
                var rankTxt = UIHelper.GetComponent<TFWText>(this.rankListTitle, string.Format("Rank/Rank{0}", i));
                if (rankTxt)
                {
                    var stageData = GameData.I.ActivityData.GetActvRankByStageId(data.activityId, i);
                    if (stageData != null)
                    {
                        if (stageData.selfPos == 0 || stageData.selfPos == -1)
                        {
                            rankTxt.text = "-";
                        }
                        else
                        {
                            rankTxt.text = stageData.selfPos.ToString();
                        }
                        if (stageData.selfPos >= 1000)
                        {
                            rankTxt.text = "999+";
                        }
                    }
                    else
                    {
                        rankTxt.text = "-";
                    }
                }
            }

            var rank = GameData.I.ActivityData.GetActvRankByStageId(data.activityId, stage);

            if (rank != null)
            {
                var items = rank.items;
                actvRankRewards = rank.rankRewards;
                actvRankItem.Clear();
                RankItemData rankItemData = null;
                //bool haveSelf = false;
                //ActivityLogUtils.Log("ActvTmplRankNtf actvRankRewards={0},rankitems={1}", actvRankRewards.Count, items.Count);

                for (int i = 0; i < items.Count; i++)
                {
                    var data = items[i];
                    var meta = Newtonsoft.Json.JsonConvert.DeserializeObject<RankMetaPlayer>(data.meta);
                    rankItemData = new RankItemData() { rankItem = data, meta = meta };
                    actvRankItem.Add(rankItemData);

                    //if (data.ID == LPlayer.I.PlayerID)
                    //{
                    //    this.selfItem.SetActive(true);
                    //    SetItem(selfItem, rankItemData, true);
                    //    haveSelf = true;
                    //}
                }
                //haveSelf=false;
                //if (!haveSelf)
                //{
                //    SetItem(selfItem, null, true);
                //}
                this.selfItem.SetActive(true);
                selfScore = rank.selfScore;
                selfPos = rank.selfPos;
                SetItem(selfItem, null, true);
            }
            //ActivityLogUtils.Log("是否总榜={0},stage={1},RankOrRewardType={2}", this.IsTotalRank, stage, RankOrRewardType);
        }

        /// <summary>
        /// 排行回调
        /// </summary>
        /// <param name="obj"></param>
        private void OnActvRankListAck(object[] obj)
        {

            if (this.RankOrRewardType == RankOrReward.Rank)
            {
                this.OnShowRankPanel(null, null);
            }
            else
            {
                this.OnShowRewardPanel(null, null);
            }
        }

        /// <summary>
        /// 关闭
        /// </summary>
        protected override void OnHidden()
        {
            if (rankListScrollView)
            {
                ///置顶
                rankListScrollView.set_index(0);
            }
            EventMgr.UnregisterEvent(TEventType.ActvRankListAck, this);
        }

        /// <summary>
        /// 销毁
        /// </summary>
        protected override void OnDestroyed()
        {

            //EventMgr.UnregisterEvent(this);
        }

        /// <summary>
        /// 奖励数据
        /// </summary>
        private UIActivityCommanderRankRewardData data;


        /// <summary>
        /// 奖励列表
        /// </summary>
        public List<ActvRankReward> actvRankRewards = new List<ActvRankReward>();

        /// <summary>
        /// 排行列表
        /// </summary>
        public List<RankItemData> actvRankItem = new List<RankItemData>();

        /// <summary>
        /// 显示
        /// </summary>
        protected override void OnShown()
        {
            base.OnShown();
            data = this.InitData as UIActivityCommanderRankRewardData;
            RankOrRewardType = RankOrReward.Rank;//默认显示排行
            EventMgr.RegisterEvent(TEventType.ActvRankListAck, OnActvRankListAck, this);

            GameData.I.ActivityData.ReqActvAllStageRankList(data.activityId, LPlayer.I.PlayerID);

            RefreshBgState();
            hasReqData = false;
            //刷新显示信息
            UpdateShowInfo();

            UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(BG);
            UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(BG1);
            UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(RewardListButtom);
            UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(RankListButtom);


        }

        #endregion


        #region 王座战数据显示刷新


        /// <summary>
        /// 刷新显示信息数据
        /// </summary>
        private void UpdateShowInfo()
        {
            if (data != null)
            {
                var isShowTab = data.activityType != ActivityType.ThroneWar;

                if (tabGroup != null)
                {
                    TFWTab tab = null;
                    GameObject obj = null;
                    for (int i = 0; i < tabGroup.TabList.Count; i++)
                    {
                        tab = tabGroup.TabList[i];
                        if (tab != null)
                        {
                            if (tab.Activate != null && tab.Activate.Count > 0)
                            {
                                for (int j = 0; j < tab.Activate.Count; j++)
                                {
                                    obj = tab.Activate[j];
                                    if (obj != null)
                                    {
                                        var comps = obj.GetComponentsInChildren<UIBehaviour>();
                                        for (int k = 0; k < comps.Length; k++)
                                        {
                                            if (comps[k] != null)
                                                comps[k].enabled = isShowTab;
                                        }
                                    }
                                }
                            }

                            if (tab.DeActivate != null && tab.DeActivate.Count > 0)
                            {
                                for (int j = 0; j < tab.DeActivate.Count; j++)
                                {
                                    obj = tab.DeActivate[j];
                                    if (obj != null)
                                    {
                                        var comps = obj.GetComponentsInChildren<UIBehaviour>();
                                        for (int k = 0; k < comps.Length; k++)
                                        {
                                            if (comps[k] != null)
                                                comps[k].enabled = isShowTab;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }


            }
        }


        #endregion



        /// <summary>
        /// 排行|奖励
        /// </summary>
        public enum RankOrReward
        {
            /// <summary>
            /// 排行
            /// </summary>
            Rank,
            /// <summary>
            /// 奖励
            /// </summary>
            Reward
        }
    }
}
//MODIFIED BY UIBASE2POPUP
//MODIFIED BY UIBASE2POPUP
//MODIFIED BY UIBASE2POPUP
//MODIFIED BY UIBASE2POPUP
