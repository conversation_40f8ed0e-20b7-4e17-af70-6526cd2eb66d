﻿using Cfg.C;
using Common;
using cspb;
using Cysharp.Threading.Tasks;
using DeepUI;
using Game.Config;
using Game.Data;
using Game.Data.Cache;
using Game.Utils;
using K1;
using Logic;
using Public;
using System;
using System.Collections.Generic;
using TFW;
using TFW.Localization;
using TFW.UI;
using UnityEngine;
using UnityEngine.UI;

namespace UI
{
    /// <summary>
    /// 城堡战斗相关
    /// 侦查，集结，攻击，援助等
    /// <AUTHOR>
    /// @date 2021/11/17 16:03:43
    /// @ver 1.0
    /// </summary>
    public partial class UIPlayerCityInfo
    {


        #region 侦查

        /// <summary>
        /// 是否可以集结
        /// </summary>
        private async void IsCanScout()
        {
            var b = GameData.I.ReconData.CheckCanRecon(playerCity.ID, out var scoutInfo);
            //如果不能侦查,显示上次的侦查界面
            if (!b)
            {
                if (scoutInfo != null)
                {
                    //显示侦查结果界面
                    var data = new UIReconData();
                    data._scoutInfo = scoutInfo;
                    data._entityID = playerCity.ID;
                    PopupManager.I.ShowPanel<UIInvestigationResults>(data);
                    Close();
                    return;
                }
            }

            var btn2data = new MsgBoxBtnParam()
            {
                str = LocalizationMgr.Get("LC_MENU_cancel_cap"),
            };
            var btn1data = new MsgBoxBtnParam()
            {
                str = LocalizationMgr.Get("LC_MENU_confirm_cap"),
                func = (o) => { GoScout(); },
            };

            if (playerCity != null && (this.playerCity.PlayerCity.GetShieldOpen() || this.playerCity.PlayerCity.GetGodShieldOpen() || this.playerCity.PlayerCity.GetServerShieldOpen()))
            {
                FloatTips.I.FloatMsg(LocalizationMgr.Get("LC_TIP_cant_scout_shield"));
            }
            else if (LPlayer.I.ShieldOpen || LPlayer.I.GodShieldOpen)
            {
                if (LPlayer.I.ShieldOpen && !LPlayer.I.GodShieldOpen)
                {
                    UIMsgBox.Push(EMsgBoxType.two, LocalizationMgr.Get("LC_MENU_warning_cap"),
                        LocalizationMgr.Format("LC_MAP_broken_shield_desc", SCommon.FormatTimeString(LEnergyGauge.I.ShieldRemainTime / 1000), SCommon.FormatTimeString((int)(await CConstConfig.GetConfigAsync(10134008)).Val)),
                        btn1data,
                        btn2data, ButtonColorGroup.RedBlue);
                }
                else if (LPlayer.I.ShieldOpen && LPlayer.I.GodShieldOpen)
                {
                    UIMsgBox.Push(EMsgBoxType.two, LocalizationMgr.Get("LC_MENU_warning_cap"),
                        LocalizationMgr.Format("LC_Castle_Protection_Attack2", SCommon.FormatTimeString(Mathf.Max((float)LEnergyGauge.I.ShieldRemainTime / 1000, MetaConfig.ShieldGodCD)), SCommon.FormatTimeString((int)(await CConstConfig.GetConfigAsync(10134008)).Val)),
                        btn1data,
                        btn2data, ButtonColorGroup.RedBlue);
                }
                else if (LPlayer.I.GodShieldOpen)
                {
                    UIMsgBox.Push(EMsgBoxType.two, LocalizationMgr.Get("LC_MENU_warning_cap"),
                        LocalizationMgr.Format("LC_Castle_Protection_Attack1", SCommon.FormatTimeString(MetaConfig.ShieldGodCD), SCommon.FormatTimeString((int)(await CConstConfig.GetConfigAsync(10134008)).Val)),
                        btn1data,
                        btn2data, ButtonColorGroup.RedBlue);
                }
            }
            else
            {
                //侦查
                GoScout();
            }
        }

        /// <summary>
        /// 侦查
        /// </summary>
        private async void GoScout()
        {
            if (playerCity != null && (this.playerCity.PlayerCity.GetShieldOpen() || this.playerCity.PlayerCity.GetGodShieldOpen() || this.playerCity.PlayerCity.GetServerShieldOpen()))
            {
                FloatTips.I.FloatMsg(LocalizationMgr.Get("LC_TIP_cant_scout_shield"));
                return;
            }

            var canScout = await LRecon.CanPorprotionScoutOrAttack(playerCity.PlayerCity.level, this.playerCity.GetCurrentPosition());
            if (!canScout)
            {
                var scoutMaxLV = CD2ConfigExtension.GetFloatVal(Config.ConfigID.ScoutMaxLV);//MetaConfig.ScoutMaxLV
                //您与的对方玩家差距过大，您无法侦查他的城堡
                FloatTips.I.FloatMsg(LocalizationMgr.Format("ERRCODE_cannotscout", scoutMaxLV));
                return;
            }

            LRecon.I.LaunchScout(playerCity.ID, false);
            //PopupManager.I.ShowDialog<UIReconEffects>();
        }


        #endregion


        #region 集结

        /// <summary>
        /// 集结时间对象
        /// </summary>
        [PopupField("centre_other/animation/ContentDetail/Time")]
        private GameObject rallyTimeObj;

        /// <summary>
        /// 集结时间选择
        /// </summary>
        private UITimeSelect rallyTimeSelect;

        /// <summary>
        /// 初始化集结时间控制
        /// </summary>
        private void InitRallyTime()
        {
            rallyTimeSelect = new UITimeSelect();
            rallyTimeSelect.Init(rallyTimeObj);

            UpdateRallyTimeVisible(false);
        }

        /// <summary>
        /// 重置集结时间控制
        /// </summary>
        private void ResetRallyTime()
        {
            if (rallyTimeSelect != null)
                rallyTimeSelect.Reset();
            //UpdateRallyTimeVisible(true);
            //if (GameData.I.DragonWarData.BattlefieldData.IsDragonWarState)
            //{
            //    rallyTimeObj.SetActive(false);
            //}
        }

        /// <summary>
        /// 刷新集结时间对象显隐
        /// </summary>
        /// <param name="visible"></param>
        private void UpdateRallyTimeVisible(bool visible)
        {
            rallyTimeSelect.SetVisable(visible);
        }

        /// <summary>
        /// 是否可以集结
        /// </summary>
        private async void IsCanRally()
        {
            if (playerCity.PlayerCity != null)
            {
                var canRally = await LRecon.CanPorprotionScoutOrAttack(playerCity.PlayerCity.level, this.playerCity.GetCurrentPosition());
                if (!canRally)
                {
                    var scoutMaxLV = CD2ConfigExtension.GetFloatVal(Config.ConfigID.ScoutMaxLV);//MetaConfig.ScoutMaxLV
                    //您与的对方玩家差距过大，您无法进攻他的城堡
                    FloatTips.I.FloatMsg(LocalizationMgr.Format("ERRCODE_cannotsgather", scoutMaxLV));
                    return;
                }
            }

            //二次确认提示
            var btn2data = new MsgBoxBtnParam()
            {
                str = LocalizationMgr.Get("LC_MENU_cancel_cap"),
            };
            var btn1data = new MsgBoxBtnParam()
            {
                str = LocalizationMgr.Get("LC_MENU_confirm_cap"),
                func = (o) => { GoRally(); },
            };

            if (playerCity != null && (this.playerCity.PlayerCity.GetShieldOpen() || this.playerCity.PlayerCity.GetGodShieldOpen() || this.playerCity.PlayerCity.GetServerShieldOpen()))
            {
                FloatTips.I.FloatMsg(LocalizationMgr.Get("LC_TIP_cant_rally_shield"));
            }
            else if (WarTip.I.IsOpen())
            {
                WarTip.I.PupopTip(GoRally);
            }
            else if (LPlayer.I.ShieldOpen || LPlayer.I.GodShieldOpen)
            {
                if (LPlayer.I.ShieldOpen && !LPlayer.I.GodShieldOpen)
                {
                    UIMsgBox.Push(EMsgBoxType.two, LocalizationMgr.Get("LC_MENU_warning_cap"),
                        LocalizationMgr.Format("LC_MAP_broken_shield_desc", SCommon.FormatTimeString(LEnergyGauge.I.ShieldRemainTime / 1000), SCommon.FormatTimeString((int)(await CConstConfig.GetConfigAsync(10134008)).Val)),
                        btn1data,
                        btn2data, ButtonColorGroup.RedBlue);
                }
                else if (LPlayer.I.ShieldOpen && LPlayer.I.GodShieldOpen)
                {
                    UIMsgBox.Push(EMsgBoxType.two, LocalizationMgr.Get("LC_MENU_warning_cap"),
                        LocalizationMgr.Format("LC_Castle_Protection_Attack2", SCommon.FormatTimeString(Mathf.Max((float)LEnergyGauge.I.ShieldRemainTime / 1000, MetaConfig.ShieldGodCD)), SCommon.FormatTimeString((int)(await CConstConfig.GetConfigAsync(10134008)).Val)),
                        btn1data,
                        btn2data, ButtonColorGroup.RedBlue);
                }
                else if (LPlayer.I.GodShieldOpen)
                {
                    UIMsgBox.Push(EMsgBoxType.two, LocalizationMgr.Get("LC_MENU_warning_cap"),
                        LocalizationMgr.Format("LC_Castle_Protection_Attack1", SCommon.FormatTimeString(MetaConfig.ShieldGodCD), SCommon.FormatTimeString((int)(await CConstConfig.GetConfigAsync(10134008)).Val)),
                        btn1data,
                        btn2data, ButtonColorGroup.RedBlue);
                }
            }
            else
            {
                GoRally();
            }
        }

        /// <summary>
        /// 前往集结
        /// </summary>
        private void GoRally()
        {
            var time = 0;
            if (rallyTimeSelect != null)
                time = rallyTimeSelect.CurTimeSeconds * 1000;
            //关闭当前界面
            Close();

            //开始去集结
            if (time > 0)
                Rally(data.entityId, time);
        }

        /// <summary>
        /// 发起集结
        /// </summary>
        /// <param name="entityId"></param>
        private void Rally(long entityId, int time)
        {
            var cityId = LPlayer.I.MainCityID;
            var cityEntity = LMapEntityManager.I.GetEntityInfo(cityId);
            var targetEntity = LMapEntityManager.I.GetEntityInfo(entityId);

            if (cityEntity == null || targetEntity == null)
            {
                return;
            }

            if (!LPlayer.I.IsPlayerInUnion())
            {
                //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.ALLIANCE);
            }
            else
            {

                PopupManager.I.ClosePopup<UIRallyBattleNpc>();
                if (WorldSwitchMgr.I.ShowWorldType == WorldTypeEnum.CITY)
                {
                    EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.WORLD);
                }

                EventMgr.FireEvent(TEventType.UIMain_Update_Start_DispatchTroop, new MarchArgs()
                {
                    targetId = entityId,
                    act = MarchAct.MarchActRally,
                    isCampAfterHunt = LMapEntityPlayerTroop.IsCampAfterHunt,
                    rallyTime = time
                });
            }
        }

        /// <summary>
        /// 清理集结相关信息
        /// </summary>
        private void ClearRallyInfo()
        {
            if (rallyTimeSelect != null)
                rallyTimeSelect.Dispose();
        }

        #endregion

        #region 攻击

        /// <summary>
        /// 检测是否可以攻击
        /// </summary>
        private async UniTask IsCanAttack()
        {
            //if (LCrossServer.I.CurrSceneType == SceneType.SceneTypeLB
            //    && await GameData.I.BrightBattleFieldGameData.HasIgnoreShieldItem())
            //{
            //    GoAttack();
            //    return;
            //}
            var btn2data = new MsgBoxBtnParam()
            {
                str = LocalizationMgr.Get("LC_MENU_cancel_cap"),
            };
            var btn1data = new MsgBoxBtnParam()
            {
                str = LocalizationMgr.Get("LC_MENU_confirm_cap"),
                func = (o) => { GoAttack(); },
            };
            
            if (playerCity != null && (this.playerCity.PlayerCity.GetShieldOpen() || this.playerCity.PlayerCity.GetGodShieldOpen() || this.playerCity.PlayerCity.GetServerShieldOpen()))
            {
                FloatTips.I.FloatMsg(LocalizationMgr.Get("LC_TIP_cant_attack_shield"));
            }
            else if(WarTip.I.IsOpen())
            {
                WarTip.I.PupopTip(GoAttack);
            }
            else if (LPlayer.I.ShieldOpen || LPlayer.I.GodShieldOpen)
            {
                if (LPlayer.I.ShieldOpen && !LPlayer.I.GodShieldOpen)
                {
                    UIMsgBox.Push(EMsgBoxType.two, LocalizationMgr.Get("LC_MENU_warning_cap"),
                        LocalizationMgr.Format("LC_MAP_broken_shield_desc", SCommon.FormatTimeString(LEnergyGauge.I.ShieldRemainTime / 1000), SCommon.FormatTimeString((int)(await CConstConfig.GetConfigAsync(10134008)).Val)),
                        btn1data,
                        btn2data, ButtonColorGroup.RedBlue);
                }
                else if (LPlayer.I.ShieldOpen && LPlayer.I.GodShieldOpen)
                {
                    UIMsgBox.Push(EMsgBoxType.two, LocalizationMgr.Get("LC_MENU_warning_cap"),
                        LocalizationMgr.Format("LC_Castle_Protection_Attack2", SCommon.FormatTimeString(Mathf.Max((float)LEnergyGauge.I.ShieldRemainTime / 1000, MetaConfig.ShieldGodCD)), SCommon.FormatTimeString((int)(await CConstConfig.GetConfigAsync(10134008)).Val)),
                        btn1data,
                        btn2data, ButtonColorGroup.RedBlue);
                }
                else if (LPlayer.I.GodShieldOpen)
                {
                    UIMsgBox.Push(EMsgBoxType.two, LocalizationMgr.Get("LC_MENU_warning_cap"),
                        LocalizationMgr.Format("LC_Castle_Protection_Attack1", SCommon.FormatTimeString(MetaConfig.ShieldGodCD), SCommon.FormatTimeString((int)(await CConstConfig.GetConfigAsync(10134008)).Val)),
                        btn1data,
                        btn2data, ButtonColorGroup.RedBlue);
                }
            }
            else
            {
                GoAttack();
            }
        }

        /// <summary>
        /// 进攻
        /// </summary>
        private async void GoAttack()
        {
            if (playerCity == null)
            {
                FloatTips.I.FloatMsg("Loading_Holdon_Tips".ToLocal());
                D.Warning?.Log("GoAttack.entity is null");
                return;
            }

            if (playerCity.PlayerCity is null)
            {
                FloatTips.I.FloatMsg("Loading_Holdon_Tips".ToLocal());
                D.Warning?.Log("GoAttack.entity.PlayerCity is null");
                return;
            }


            //对于非集结的攻击，这里要做额外检查
            //对方玩家的城堡正在受到保护，您无法进攻他的城堡
            var canAttack = await LRecon.CanPorprotionScoutOrAttack(playerCity.PlayerCity.level, this.playerCity.GetCurrentPosition());
            if (!canAttack)
            {
                var scoutMaxLV = CD2ConfigExtension.GetFloatVal(Config.ConfigID.ScoutMaxLV);//MetaConfig.ScoutMaxLV
                                                                                         //您与的对方玩家差距过大，您无法进攻他的城堡
                FloatTips.I.FloatMsg(LocalizationMgr.Format("ERRCODE_cannotatk", scoutMaxLV));
                return;
            }

            //关闭当前界面
            Close();

            EventMgr.FireEvent(TEventType.UIMain_Update_Start_DispatchTroop, new MarchArgs()
            {
                act = MarchAct.MarchActAttack,
                isCampAfterHunt = false,
                targetId = data.entityId
            });


            if (playerCity != null)
            {
                playerCity.IsLocked = true;

                if (playerInfo != null && playerInfo.head != null)
                {
                    //缓存被攻打的城市玩家头像id,需要在战斗时候的显示头像
                    OtherPlayerHeadCache.CachePlayerHead(playerCity.ID, playerInfo.head.avatarCfgID);
                }
            }
        }

        #endregion


        #region 援助

        /// <summary>
        /// 联盟援助root对象
        /// </summary>
        [PopupField("centre_other/animation/ContentDetail/Grid")]
        private GameObject troopItemRoot;

        [PopupField("centre_other/animation/ContentDetail/Grid/BG/FightNum")]
        private TFWText _fightNum;

        [PopupField("centre_other/animation/ContentDetail/Grid/BG/ScrollView")]
        private TFWScrollRect heroGrid;

        [PopupField("centre_other/animation/ContentDetail/Grid/BG/Item1")]
        private GameObject heroItemObj;


        /// <summary>
        /// 援助数据
        /// </summary>
        private OtherReinforceInfoAck otherReinforceInfoAck;

        /// <summary>
        /// 援助军队obj字典
        /// </summary>
        private Dictionary<long, FightHeroItem> supportTroopDic;

        /// <summary>
        /// 当前退出的集结部队id
        /// </summary>
        private long curSupportExitId;

        /// <summary>
        /// 初始化联盟援助信息
        /// </summary>
        private void InitAllianceSupport()
        {
            supportTroopDic = new Dictionary<long, FightHeroItem>();
        }


        private async UniTask SetReinforceTroopInfo(List<RallyTroopDetail> troops)
        {
            ClearReinforceDic();
            if (troopItemRoot != null)
            {
                if (troops.Count > 0)
                {
                    var isShow = false;
                    for (int i = 0; i < troops.Count; i++)
                    {
                        var data = troops[i];
                        if (cityType == PlayerCityTypeEnum.ALLY)
                        {
                            if (data.GetIsPlayerSelf())
                            {
                                //如果援助了盟友才能显示
                                isShow = true;
                                break;
                            }
                        }
                        else if(cityType == PlayerCityTypeEnum.SELF)
                        {
                            isShow = true;
                            break;
                        }
                    }

                    if (isShow)
                    {
                        if (_fightNum != null)
                            _fightNum.text = $"{troops.Count}/{MetaConfig.ReinforceNumLimit}";
                        troopItemRoot.SetActive(true);
                        int num = 0;
                        if(heroItemObj != null)
                        {
                            for (int i = 0; i < troops.Count; i++)
                            {
                                var obj = GameObject.Instantiate(heroItemObj);
                                obj.transform.parent = heroGrid.content;
                                obj.transform.localScale = Vector3.one;
                                obj.transform.localPosition = Vector3.zero;
                                obj.SetActive(true);
                                FightHeroItem item = new FightHeroItem(obj);
                                await item.SetHeroData(troops[i], cityType, OnClickAllianceSupportExit);
                                supportTroopDic.Add(troops[i].troopID, item);
                                num++;
                            }
                            if(troops.Count < 4)
                            {
                                for(int i=troops.Count;i<4;i++)
                                {
                                    var obj = GameObject.Instantiate(heroItemObj);
                                    obj.transform.parent = heroGrid.content;
                                    obj.transform.localScale = Vector3.one;
                                    obj.transform.localPosition = Vector3.zero;
                                    obj.SetActive(true);
                                    FightHeroItem item = new FightHeroItem(obj);
                                    await item.SetHeroData(null, cityType, OnClickAllianceSupportExit);
                                    num++;
                                    supportTroopDic.Add(num, item);
                                }
                            }
                            else if(troops.Count < MetaConfig.ReinforceNumLimit)
                            {
                                var obj = GameObject.Instantiate(heroItemObj);
                                obj.transform.parent = heroGrid.content;
                                obj.transform.localScale = Vector3.one;
                                obj.transform.localPosition = Vector3.zero;
                                obj.SetActive(true);
                                FightHeroItem item = new FightHeroItem(obj);
                                await item.SetHeroData(null, cityType, OnClickAllianceSupportExit);
                                num++;
                                supportTroopDic.Add(num, item);
                            }
                        }
                        LayoutRebuilder.ForceRebuildLayoutImmediate(heroGrid.content);
                    }
                    else
                    {
                        troopItemRoot.SetActive(false);
                    }
                }
                else
                {
                    troopItemRoot.SetActive(false);
                }
            }
        }

        /// <summary>
        /// 刷新援助信息数据
        /// </summary>
        private async UniTask UpdateReinforceInfo()
        {
            if (otherReinforceInfoAck != null || cityType == PlayerCityTypeEnum.SELF)
            {
                if (playerInfo != null && cityType == PlayerCityTypeEnum.ALLY)
                {
                    //如果是盟友 并且判断是否是援助的那个盟友
                    var datas = otherReinforceInfoAck.troop;
                    SrotReinforceInfos(ref datas);
                    await SetReinforceTroopInfo(datas);
                }
                else if(cityType == PlayerCityTypeEnum.SELF)
                {
                    var data = GameData.I.ReinforceData.GetReinforceList();
                    SrotReinforceInfos(ref data);
                    await SetReinforceTroopInfo(data);
                }
            }
        }

        private void SrotReinforceInfos(ref List<RallyTroopDetail> troops)
        {
            for (int i = 0; i < troops.Count; i++)
            {
                for (int j = i + 1; j < troops.Count; j++)
                {
                    if (troops[i].ownerID != troops[j].ownerID && troops[j].ownerID == LPlayer.I.PlayerID)
                    {
                        var temp = troops[j];
                        troops[j] = troops[i];
                        troops[i] = temp;
                    }
                }
            }
        }


        /// <summary>
        /// 点击联盟援助退出
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        private void OnClickAllianceSupportExit(RallyTroopDetail data)
        {
            if (playerCity != null)
            {
                var title = LocalizationMgr.Get("Rally_Assistance_Btn");
                var description = LocalizationMgr.Get("Rally_Assistance_Quit_Tips");//是否从援助中撤退？你的部队将返回城堡
                UITools.ShowMsgBox(EMsgBoxType.two_nc, title, description, new MsgBoxBtnParam()
                {
                    str = LocalizationMgr.Get("MENU_confirm_cap"),
                    func = (o) =>
                    {
                        if (data != null && playerCity != null)
                        {
                            curSupportExitId = data.troopID;
                            GameData.I.ReinforceData.DoReinforceQuit(playerCity.ID, data.troopID);
                        }
                    }
                }, new MsgBoxBtnParam() { str = LocalizationMgr.Get("MENU_cancel_cap"), func = null });
            }
        }

        /// <summary>
        /// 刷新联盟援助退出
        /// </summary>
        private void UpdateAllianceSupprotExit()
        {
            if (supportTroopDic.TryGetValue(curSupportExitId, out var item))
            {
                item.Destroy();
                supportTroopDic.Remove(curSupportExitId);
                foreach(var obj in supportTroopDic.Values)
                {
                    if(!obj.IsNone)
                    {
                        LayoutRebuilder.ForceRebuildLayoutImmediate(heroGrid.content);
                        return;
                    }
                }
                if (troopItemRoot)
                {
                    troopItemRoot.SetActive(false);
                    LayoutRebuilder.ForceRebuildLayoutImmediate(heroGrid.content);
                }
            }
        }

        private void ClearReinforceDic()
        {
            foreach (var item in supportTroopDic.Values)
            {
                item.Destroy();
            }
            supportTroopDic?.Clear();
        }

        /// <summary>
        /// 清理援助信息
        /// </summary>
        private void ClearReinforceInfo()
        {
            otherReinforceInfoAck = null;
            curSupportExitId = 0;
            ClearReinforceDic();
        }

        /// <summary>
        /// 刷新援助盟友的部队列表
        /// </summary>
        /// <param name="objs"></param>
        [PopupEvent(TEventType.SupportOtherInfo)]
        private async void OnUpdateReinforceInfo(object[] objs)
        {
            if (objs == null || objs.Length == 0)
                return;
            otherReinforceInfoAck = objs[0] as OtherReinforceInfoAck;
            //刷新援助数据
            await UpdateReinforceInfo();
        }

        /// <summary>
        /// 刷新联盟援助退出
        /// </summary>
        /// <param name="objs"></param>
        [PopupEvent(TEventType.AllianceSupportExit)]
        private void OnUpdateReinforceExit(object[] objs)
        {
            //刷新联盟援助退出
            UpdateAllianceSupprotExit();
        }


        #endregion

    }


    public class FightHeroItem : BaseBtnWidget
    {
        private GameObject none;
        private GameObject itemRoot;
        private GameObject closeObj;
        private BaseBtnWidget closeBtn;
        private GameObject heroObj;
        private TFWImage quatiyIcon;
        private TFWImage heroIcon;
        private TFWText lvTxt;
        private TFWText powerTxt;
        private TFWText name;
        public FightHeroItem(GameObject root) : base(root) { }


        public bool IsNone
        {
            get { return none.activeSelf; }
        }

        public override void OnInit()
        {
            base.OnInit();
            none = UIHelper.GetChild(rootObj, "None");
            itemRoot = UIHelper.GetChild(rootObj, "Item");
            heroIcon = UIHelper.GetComponent<TFWImage>(itemRoot, "Icon");
            quatiyIcon = UIHelper.GetComponent<TFWImage>(itemRoot, "Level/Icon");
            lvTxt = UIHelper.GetComponent<TFWText>(itemRoot, "Level/Text");
            closeObj = UIHelper.GetChild(itemRoot, "Close");
            closeBtn = new BaseBtnWidget(closeObj);
            heroObj = UIHelper.GetChild(itemRoot, "HeroItem");
            heroObj.GetComponent<EventTriggerListener>().enabled = false;
            powerTxt = UIHelper.GetComponent<TFWText>(heroObj, "Item/Attack/Text");
            name = UIHelper.GetComponent<TFWText>(itemRoot, "Name");

        }

        public async UniTask SetHeroData(RallyTroopDetail _data, PlayerCityTypeEnum cityType, Action<RallyTroopDetail> closeCallBack)
        {
           if(_data == null)
           {
                itemRoot.SetActive(false);
                none.SetActive(true);
           }
           else
           {
                name.text = _data.name;
                none.SetActive(false);
                itemRoot.SetActive(true);

                powerTxt.text = string.Format("{0:0,0}", _data.combat);

                if (_data.heros.Count > 0)
                {
                    var hero = _data.heros[0];
                    var heroCfg = await Cfg.C.CD2Hero.GetConfigAsync(hero.cfgID);

                    UIHeroNewUtils.SetData(heroObj, hero.cfgID, hero.level, 0, true);

                    UITools.SetImage(quatiyIcon, HeroUtils.GetHeroAttributeByAttribute(heroCfg.SoldiersType), "Hero");

                }
                if (cityType == PlayerCityTypeEnum.ALLY)
                {
                    closeObj.SetActive(_data.GetIsPlayerSelf());
                    if (_data.GetIsPlayerSelf())
                    {
                        closeBtn.SetBtnClickCallBack(obj =>
                        {
                            closeCallBack(_data);
                        });
                    }
                }
                else
                {
                    closeObj.SetActive(true);
                    closeBtn.SetBtnClickCallBack(obj =>
                    {
                        closeCallBack(_data);
                    });
                   
                }
            }
        }


        public override void Destroy()
        {
            base.Destroy();
            GameObject.Destroy(rootObj);
        }

    }
}
