﻿namespace Game.Config
{
    /// <summary>
    /// AudioList声音表的参照ID
    /// <AUTHOR>
    /// @date 2020/5/9
    /// @ver 1.0
    /// </summary>
    public class AudioConst
    {
        /// <summary>
        /// 普通关卡背景音乐
        /// </summary>
        public static int SIMPLE_LEVEL_AUDIO = 23;

        /// <summary>
        /// 世界背景音乐
        /// </summary>
        public static int WORLD_BGM = 27;

        public static int Merge_BGM = 1;
 

        /// <summary>
        /// boss关卡背景音乐
        /// </summary>
        public static int BOSS_LEVEL_AUDIO = 24;

        /// <summary>
        /// 内城城建环境背景音
        /// </summary>
        public static int CITY_BUILDING_AMBIENT_BGM = 56;

        /// <summary>
        /// 大世界环境背景音
        /// </summary>
        public static int WORLD_AMBIENT_BGM = 57;

        /// <summary>
        /// 大地图白天时背景音
        /// </summary>
        public static int WORLD_DAY_BGM = 15220072;
        /// <summary>
        /// 大地图夜晚时背景音
        /// </summary>
        public static int WORLD_NIGHT_BGM = 15220073;

        /// <summary>
        /// 胜利音效
        /// </summary>
        public static int SOUND_UI_WIN = 1;

        /// <summary>
        /// 失败音效
        /// </summary>
        public static int SOUND_UI_LOST = 2;

        /// <summary>
        /// 解锁音效
        /// </summary>
        public static int SOUND_UI_UNLOCK = 3;

        /// <summary>
        /// 士兵合成成功音效
        /// </summary>
        public static int SOUND_SCENE_SUCCESS = 4;

        /// <summary>
        /// 放置士兵的音效
        /// </summary>
        public static int SOUND_SCENE_SHELVE_1 = 5;

        /// <summary>
        /// 开门音效
        /// </summary>
        public static int SOUND_SCENE_OPEN = 7;

        /// <summary>
        /// 点击提起士兵的音效
        /// </summary>
        public static int SOUND_SCENE_MENTION = 8;

        /// <summary>
        /// 合成区满的音效
        /// </summary>
        public static int SOUND_SCENE_ERROR = 17;

        /// <summary>
        /// 销毁士兵
        /// </summary>
        public static int SOUND_SCENE_DESTORY = 18;

        /// <summary>
        /// 关门音效
        /// </summary>
        public static int SOUND_SCENE_CLOSE = 20;

        /// <summary>
        /// 金币不足购买
        /// </summary>
        public static int SOUND_SCENE_BUYERROR = 21;

        /// <summary>
        /// 点击造兵按钮
        /// </summary>
        public static int SOUND_SCENE_BUY = 22;


        public const int ChangeClothing = 2800; 
        public const int ChapterTaskPhoto = 2900;
        public const int LipsFly = 3000;

        public const int taskComplete=15220105;

        /// <summary>
        /// 技能升级
        /// </summary>
        public static int SOUND_UI_SKILL_UPGRADE = 29;

        /// <summary>
        /// 宝箱崩一下
        /// </summary>
        public static int SOUND_UI_CHEST_OPEN = 30;

        /// <summary>
        /// 宝箱原地崩
        /// </summary>
        public static int SOUND_UI_CHEST_OPEN1 = 31;

        /// <summary>
        /// 弹出卡牌
        /// </summary>
        public static int SOUND_UI_RECEIVE_CARD = 32;

        /// <summary>
        /// 卡牌展示
        /// </summary>
        public static int SOUND_UI_CARD_SHOW = 33;

        /// <summary>
        /// 进度条1
        /// </summary>
        public static int SOUND_UI_BAR_UPGRADE1 = 34;

        /// <summary>
        /// 进度条2
        /// </summary>
        public static int SOUND_UI_BAR_UPGRADE2 = 35;

        /// <summary>
        /// 金钱背景音效
        /// </summary>
        public static int BGSOUND_UI_COIN = 36;

        /// <summary>
        /// 物品背景音效
        /// </summary>
        public static int BGSOUND_UI_ITEM = 37;

        /// <summary>
        /// 英雄背景音效
        /// </summary>
        public static int BGSOUND_UI_HERO = 38;

        /// <summary>
        /// 主界面菜单开启音效
        /// </summary>
        public static int BGSOUND_UI_MENU_OPEN = 26;

        /// <summary>
        /// 主界面菜单关闭音效
        /// </summary>
        public static int BGSOUND_UI_MENU_CLOSE = 27;


        
        /// <summary>
        /// 城建的背景音乐
        /// </summary>
        public static int CITY_BUILDING_BG = 39;


        public static int ITEM_FLY = 11;
        public static int COIN_FLY = 10;
        public static int DIAMOND_FLY = 12;

        public static int RECRUIT_CARD_FLIP = 716;
        public static int RECRUIT_CARD_Get = 717;
        public static int POWER_UP = 718;
        public static int RECRUIT_CARD_FLIP10 = 720;



        public const int RequestAllianceHelp = 15220001;

        public const int CollectRss = 15220005;

        public const int LaunchMarch = 15220013;

        public const int FightOngoing = 15220014;

        public const int LaunchRally = 15220015;

        public const int RallyStart = 15220016;

        public const int CityShieldOn = 15220017;

        public const int CityTeleport = 15220074; // 15220021;

        public const int TroopsReturn = 15220022;

        public const int Claim = 15220028;

        public const int MapUnitClick = 15220030;

        public const int Stone = 15220114;

        /// <summary>
        /// 通用UI点击音效
        /// </summary>
        public const int UIGeneric = 15220034;

        public const int FightVictory = 15220052;

        public const int FightDefeat = 15220053;

        public const int RallyAttack = 15220047;

        public const int CityChange = 15220033;

        public const int SearchClose = 15220029;

        public const int LineUpClick = 15220027;

        public const int HeroLvUpAndStarUp = 15220075;

        public const int WoodBoxOpen = 15220077;

        public const int MetalBoxOpen = 15220076;

        public const int Water = 15220113;

        public const int Valhalla = 15220110;

        public const int DragonHome = 15220116;

        public const int ToolsHome = 15220111;

        public const int Mine = 15220115;

        public const int ThroneCity = 15220112;

        public const int PlayerCity = 15220117;

        public const int WorldLongClick = 15220108;

        public const int WorldSelectTroops = 15220109;

        public const int AreaShowAudio = 15220118;

        public const int TreasureShowAudio = 15220123;

        public const int KingHallShowAudio = 15220120;

        public const int WitchShowAudio = 15220121;

        public const int EquioShowAudio = 15220119;

        public const int SummonShowAudio = 15220122;

        /// <summary>
        /// 转盘旋转音效
        /// </summary>
        public const int UI_Spinwheel = 15220078;
        /// <summary>
        /// 转盘奖励弹窗音效 
        /// </summary>
        public const int UI_Congratulation = 15220079;

        /// <summary>
        /// 声音通道过渡时间
        /// </summary>
        public static float AUDIO_CHANNEL_LERP_TIME = 1.5f;

        public static int AUDIO_CHANNEL_STOP = -1;

        /// <summary>
        /// 声音通道缩放倍数
        /// </summary>
        public static float AUDIO_CHANNEL_VOLUME_TIME = 0.3f;

        /// <summary>
        /// 声音通道原始缩放倍数
        /// </summary>
        public static float AUDIO_CHANNEL_SRC_VOLUME_TIME = 1f;
        
      
    }
}


