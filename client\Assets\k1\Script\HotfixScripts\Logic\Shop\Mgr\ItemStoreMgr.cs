﻿using Cfg.G;
using Common;
using cspb;
using Game.Config;
using Game.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using TFW.Localization;
using THelper;
using UI;
using static Public.PublicFunc;
using DeepUI;
using Cfg;
using Cysharp.Threading.Tasks;
using GameState;
using Config;
using K3;
using Public;
using TFW;
using UnityEngine;
using UI.Alliance;
using UI.Utils;


namespace Logic
{
    public enum Enum_AccessType
    {
        // 1=广告获取
        Ad = 1,
        // 2=购买礼包
        Gift = 2,
        // 3=购买道具
        ItemStore = 3,
        // 4=参与集结
        Rally = 4,
        // 5=击杀野怪
        PVE = 5,
        // 6=道具合成
        Compose = 6,
        // 7=采集
        Collect = 7,
        // 8= 联盟商店
        AllianceShop = 8,
        // 9=联盟总动员
        AllianceMobilization = 9,
        // 10=赛季
        Season = 10,
        // 11=联盟求助
        AllianceHelp = 11,
        // 12=次元宝藏
        DimensionActivity = 12,
        // 14=跳转背包
        Bag = 14,
        // 20=日常任务
        DailyTask = 20,
        // 21=vip商店
        VIPShop = 21,
        // 22=金库
        BuildingGold = 22,
        // 23=兵营
        BuildingBarracks = 23,
        // 24=棋盘界面
        GoToMerge = 24,
        // 25=招募
        Recruit = 25,
        // 26=占星台
        StargazingPlatform = 26,
        // 27=金币/钻石购买
        CoinBuy = 27,
        CommonMonth = 28,
        ForverMonth = 29,
        Tech = 30,
        VIP = 31,
        WeekCard = 32, // 周卡
        ChatperFunc = 33, // 章节任务
        SuperWeaponss = 34, // 超级武器
        AllianceDonate = 35, // 联盟捐献
        /// <summary>
        /// 使用道具获取
        /// </summary>
        UseItem=36,
    }


    /// <summary>
    /// 物品商店
    /// </summary>
    public class ItemStoreMgr : Ins<ItemStoreMgr>, ILogoutCallback, ILoginCallback
    {
        public bool isValid { get; private set; }

        public event Action onItemStoreAck;

        /// <summary>
        /// Itemstore商店数据
        /// key：itemstore表id
        /// </summary>
        private Dictionary<int, ItemStoreItem> itemStoreDic;

        private Dictionary<int, ItemStoreData> itemStoreCacheMap;

        /// <summary>
        /// Itemstore商店数据
        /// key:道具item表id
        /// </summary>
        private Dictionary<int, ItemStoreItem> itemStoreCacheDic;

        public ItemStoreGiftAutoPopup AutoPopup = null;

        UniTask ILoginCallback.OnLogin()
        {
            this.itemStoreDic = new Dictionary<int, ItemStoreItem>();
            this.itemStoreCacheDic = new Dictionary<int, ItemStoreItem>();
            this.itemStoreCacheMap = new Dictionary<int, ItemStoreData>();

            AutoPopup = new ItemStoreGiftAutoPopup();

            MessageMgr.RegisterMsg<ItemStoreAck>(this, this.OnItemStoreAck);
            MessageMgr.RegisterMsg<ItemStoreBuyAck>(this, this.OnItemStoreBuyAck);

            VersionRewardMgr.I.onVersionChanged += this.RequestItemStoreData;
            if (VersionRewardMgr.I.isInited)
            {
                this.RequestItemStoreData();
            }

            return UniTask.CompletedTask;
        }



        /// <summary>
        /// 注销数据
        /// </summary>
        UniTask ILogoutCallback.OnLogout()
        {
            this.isValid = false;
            this.itemStoreDic = new Dictionary<int, ItemStoreItem>();
            this.itemStoreCacheDic = new Dictionary<int, ItemStoreItem>();
            this.itemStoreCacheMap = new Dictionary<int, ItemStoreData>();

            MessageMgr.UnregisterMsg<ItemStoreAck>(this);
            MessageMgr.UnregisterMsg<ItemStoreBuyAck>(this);

            VersionRewardMgr.I.onVersionChanged -= this.RequestItemStoreData;

            return UniTask.CompletedTask;
        }

        private void OnClickGotoLand()
        {

            PopupManager.I.ClearAllPopup();

            EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, MainMenuType.CITY);

            PopupManager.I.ShowLayer<UIMerge>();
        }

        /// <summary>
        /// 设置购买次数
        /// </summary>
        private bool SetItemStoreDataTimes(int cfgID, int times)
        {
            var data = this.GetItemStoreData(cfgID);
            if (data == null) return false;

            data.times = times;

            return true;
        }

        /// <summary>
        /// 根据itemID获取相应道具数据，没有返回null
        /// </summary>
        public ItemStoreItem GetItemStoreDataByItemID(int itemID)
        {
            if (this.itemStoreCacheDic == null)
                return null;

            if (!this.itemStoreCacheDic.TryGetValue(itemID, out var dt))
                return null;

            return dt;
        }

        /// <summary>
        /// 根据ID获取对应的itemData，拿不到返回null
        /// </summary>
        public ItemStoreItem GetItemStoreData(int cfgID)
        {
            if (this.itemStoreDic == null)
                return null;
            if (!this.itemStoreDic.TryGetValue(cfgID, out var it))
                return null;

            return it;
        }

        public ItemStoreData GetItemStoreDataById(int id)
        {
            if (this.itemStoreCacheMap == null)
                return null;

            if (!this.itemStoreCacheMap.TryGetValue(id, out var dt))
                return null;

            return dt;
        }

        /// <summary>
        /// 获取道具商城界面显示数据
        /// </summary>
        /// <returns></returns>
        public List<ItemStoreData> GetItemStoreShowData()
        {
            if (this.itemStoreCacheMap == null)
                return new List<ItemStoreData>();

            var dt = new Dictionary<int, ItemStoreData>();
            foreach (var cache in this.itemStoreCacheMap)
            {
                //主城等级足够才显示道具
                if (LSkill.I.GetMainCityLevel() >= cache.Value.cfg.Level)
                {
                    if (cache.Value.isShow)
                        dt[cache.Key] = cache.Value;
                }

            }

            var list = dt.Values.ToList();
            if (list == null)
                return new List<ItemStoreData>();


            list = list.OrderByDescending(s => s.itemData.Cfg.Order).ToList();

            return list;
        }

        #region ItemStore 协议

        private bool isOpenPanel;

        private void RequestItemStoreData()
        {
            MessageMgr.Send(new ItemStoreReq());
        }

        /// <summary>
        /// itemStore请求
        /// </summary>
        /// <param name="ack"></param>
        private async UniTaskVoid OnItemStoreAck(ItemStoreAck ack)
        {
            if (ack == null)
            {
                D.Debug?.Log("ack == null || ack.items == null || ack.items.Count == 0");
                return;
            }

            if (ack.items == null)
            {
                D.Debug?.Log("ack.items == null");
                return;
            }

            var count = ack.items.Count;
            if (count == 0)
            {
                D.Debug?.Log("ack.items.Count == 0");
                return;
            }

            for (int i = 0; i < count; i++)
            {
                this.itemStoreDic[ack.items[i].cfgID] = ack.items[i];

                var cfg = await Cfg.C.CItemStore.GetConfigAsync(ack.items[i].cfgID);
                if (cfg == null)
                    continue;

                var goodRewards = await VersionRewardMgr.I.GetDisplayRewards2(cfg.GoodVersionReward);
                var costRewards = await VersionRewardMgr.I.GetDisplayRewards2(cfg.PriceVersionReward);

                this.itemStoreCacheMap[ack.items[i].cfgID] = await ItemStoreData.CreateAsync(ack.items[i], cfg, costRewards, goodRewards);

                int good = await VersionRewardMgr.I.GetGoodValue(cfg.GoodVersionReward, goodRewards);

                if ((await VersionRewardMgr.I.GetGoodType(cfg.GoodVersionReward, goodRewards)).Equals("item")) this.itemStoreCacheDic[good] = ack.items[i];
            }

            //打开补足界面
            if (this.isOpenPanel)
            {
                this.isOpenPanel = false;
                this.OpenResNeedUI(this.resNeedType, this.resNeedNum).Forget();
            }

            this.isValid = true;
            this.onItemStoreAck?.Invoke();
        }

        private long clickTime;

        /// <summary>
        /// 正在购买的物品id
        /// </summary>
        private int buyingId;

        /// <summary>
        /// 正在购买的物品数量
        /// </summary>
        private int buyingCount;

        private Action<ItemData> callBack = null;

        /// <summary>
        /// 购买itemStore物品
        /// </summary>
        public void ReqItemStoreBuy(int id, int count, int itemId = 0, Action<ItemData> callBack = null)
        {
            if (this.clickTime > GameTime.Time)
            {
                //UITools.PopTips(LocalizationMgr.Get("Shop_item_shop_tips_03"));
                return;
            }

            this.clickTime = GameTime.Time + 1000;//1S CD

            //if (isBuying)
            //{
            //    UITools.PopTips(LocalizationMgr.Get("Shop_item_shop_tips_03"));
            //    return;
            //}
            this.buyingId = itemId;
            this.buyingCount = count;
            this.callBack = callBack;
            //isBuying = true;
            var req = new ItemStoreBuyReq()
            {
                cfgID = id,
                count = count,
            };

            MessageMgr.Send(req);


        }

        /// <summary>
        /// itemStore购买请求
        /// </summary>
        private async UniTaskVoid OnItemStoreBuyAck(ItemStoreBuyAck ack)
        {
            D.Debug?.Log("ItemStoreBuyAck");
            var ok = Common.CheckErrorCode(ack.errCode, true);
            if (!ok)
                return;

            if (this.buyingId > 0)
            {
                var itemData = new ItemData(this.buyingId);
                var name = itemData.GetShowName();
                //UITools.PopTips(LocalizationMgr.Format("Shop_item_shop_tips_01", name, this.buyingCount));
                //UITools.PopTips(LocalizationMgr.Format("Shop_item_shop_tips_01", name, buyingCount));
            }

            //道具商城新需求，刷新次数, 后续有空重构一块这块内容把，次数没必要每次都拉取道具商城数据
            this.RequestItemStoreData();

            //todo更新ItemStoreList的数据
            this.SetItemStoreDataTimes(ack.itemId, ack.count);

            var cfg = await Cfg.C.CItemStore.GetConfigAsync(ack.itemId);
            if (cfg != null)
            {
                var list = await VersionRewardMgr.I.GetDisplayRewards2(cfg.GoodVersionReward);
                if ((await VersionRewardMgr.I.GetGoodType(cfg.GoodVersionReward, list)).Equals("item"))
                {
                    int good = await VersionRewardMgr.I.GetGoodValue(cfg.GoodVersionReward, list);
                    var item = new ItemData(good, this.buyingCount);
                    this.callBack?.Invoke(item);
                }
            }

            this.callBack = null;
            this.buyingId = -1;
            this.buyingCount = -1;
        }

        #endregion

        #region 资源获取

        /// <summary>
        /// 如果资源不足就打开资源获取界面 
        /// </summary>
        /// <param name="type"></param>
        /// <param name="needCount"></param>
        /// <returns></returns>
        public bool TryOpenGetAssetPop(GameCurrencyEnum type, int needCount)
        {
            switch (type)
            {
                case GameCurrencyEnum.CURRENCY_ALLIANCE_SLIVER:
                    if (PlayerAssetsMgr.I.GetVMCount(CfgConst.AllianceCoin) < needCount)
                    {
                        this.OpenGetAssetPop(type, needCount);
                        return true;
                    }
                    break;
                case GameCurrencyEnum.CURRENCY_BRIGHTDIAMOND:
                    if (PlayerAssetsMgr.I.GetVMCount(CfgConst.BrightBattleId) < needCount)
                    {
                        OpenGetAssetPop(type, needCount);
                        return true;
                    }
                    break;
                default:
                    return false;
            }
            return false;
        }


        private GameCurrencyEnum resNeedType;
        private int resNeedNum;

        /// <summary>
        /// 打开资源不足弹窗 
        /// </summary>
        /// <param name="type"></param>
        public void OpenGetAssetPop(GameCurrencyEnum type, long needNum = 0)
        {
            switch (type)
            {
                case GameCurrencyEnum.CURRENCY_GOLD:
                    UITools.AddRes(K3ResText.ResEnum.Coin);
                    break;
                case GameCurrencyEnum.CURRENCY_DIAMOND:
                    UITools.AddRes(K3ResText.ResEnum.Diamond);
                    break;
                case GameCurrencyEnum.CURRENCY_ALLIANCE_SLIVER:
                    UITools.AddRes(K3ResText.ResEnum.Alliance_Sliver);
                    break;
                case GameCurrencyEnum.CURRENCY_JADE:
                    break;
                case GameCurrencyEnum.CURRENCY_BRIGHTDIAMOND:
                    break;
                case GameCurrencyEnum.BadgeHornor:
                    break;
                case GameCurrencyEnum.CURRENCY_PRISONER:
                    UITools.AddRes(K3ResText.ResEnum.Solider);
                    break;
                case GameCurrencyEnum.HERO_EXP:
                    UITools.AddRes(K3ResText.ResEnum.HeroExp);
                    break;
                default:
                    break;
            }
            //this.resNeedType = type;
            //this.resNeedNum = needNum;
            //D.Warning?.Log($"OpenGetAssetPop {type} {needNum}");
            ////保持和之前逻辑一样，每次打开界面获取数据，待重构
            //this.RequestItemStoreData();
            //this.isOpenPanel = true;
        }

        private GiftItemData GetDiamondGift()
        {
            var diamondGifts = GameData.I.GiftData.GetPeriodGiftDats(GiftTypeEnum.Diamond);
            if (diamondGifts != null && diamondGifts.Count > 0)
            {
                foreach (var gift in diamondGifts)
                {
                    if (gift.Value == null)
                        continue;

                    if (!GameData.I.GiftData.CheckGiftLimitBuy(gift.Value.CfgId))
                    {
                        return gift.Value;
                    }
                }
            }

            return null;
        }

        private GiftItemData GetCoinGift()
        {
            var coinGifts = GameData.I.GiftData.GetPeriodGiftDats(GiftTypeEnum.Coin);
            if (coinGifts != null && coinGifts.Count > 0)
            {
                foreach (var gift in coinGifts)
                {
                    if (gift.Value == null)
                        continue;

                    if (!GameData.I.GiftData.CheckGiftLimitBuy(gift.Value.CfgId))
                    {
                        return gift.Value;
                    }
                }
            }

            return null;
        }

        public async UniTask<GiftItemData> GetGiftDataByItemId(int id, string type)
        {
            var itemConfig = await Cfg.C.CK3Item.GetConfigAsync(id);
            if (itemConfig == null)
            {
                return null;
            }
            Dictionary<int, GiftItemData> giftDataDic = new Dictionary<int, GiftItemData>();
            var DailyDealDic = GameData.I.GiftData.GetPeriodGiftDats(GiftTypeEnum.DailyDeal);
            if (DailyDealDic != null)
            {
                foreach (var item in DailyDealDic)
                {
                    giftDataDic.Add(item.Key, item.Value);
                }
            }
            var valuePackageDic = GameData.I.GiftData.GetPeriodGiftDats(GiftTypeEnum.ValuePackage);
            if (valuePackageDic != null)
            {
                foreach (var item in valuePackageDic)
                {
                    giftDataDic.Add(item.Key, item.Value);
                }
            }
            foreach (var item in giftDataDic)
            {
                foreach (var source in itemConfig.ItemGiftSource)
                {
                    if (item.Value.CfgId == int.Parse(source))
                    {
                        if (!GameData.I.GiftData.CheckGiftLimitBuy(item.Value.CfgId))//  item.Value.GetBuyCount() < item.Value.giftInfo.BuyLimit
                        {
                            return item.Value;
                        }
                    }
                }
            }
            return null;
        }

        public async UniTask<List<ComplementPopData>> GetProShowDatas(UIComplementPop.UIComplementPopEnum popEnum, TypIDVal needValue, CItem itemCfg=null)
        {
            var showDataList = new List<ComplementPopData>();

            var count = PlayerAssetsMgr.I.GetAssetCountByTypID(needValue.typ, needValue.ID);// itemCfg == null ? 0 : PlayerAssetsMgr.I.GetItemCountByID(itemCfg.Id);
            var need = needValue.val;
            List<int> access = new List<int>();
          
            ItemStoreItem itemStore = null;
            
            switch (needValue.typ)
            {
                case AssetType.Item:
                    itemStore = ItemStoreMgr.I.GetItemStoreDataByItemID(needValue.ID);
                    itemCfg = await Cfg.C.CItem.GetConfigAsync(needValue.ID);
                    foreach (var item in itemCfg.Access)
                    {
                        if (int.TryParse(item, out var result))
                        {
                            access.Add(result);
                        }
                    }
                    break;
                case AssetType.Recover:
                    var RecoverCfg = await Cfg.C.CRecover.GetConfigAsync(needValue.ID);
                    foreach (var item in RecoverCfg.Access)
                    {
                        if (int.TryParse(item, out var result))
                        {
                            access.Add(result);
                        }
                    }
                    break;
                case AssetType.Vm:
                    var VmCfg = await Cfg.C.CVm.GetConfigAsync(needValue.ID);
                    foreach (var item in VmCfg.Access)
                    {
                        if (int.TryParse(item, out var result))
                        {
                            access.Add(result);
                        }
                    }
                    break;
                default:
                    break;
            }
             
            var allianceOpen = (int)MetaConfig.UnlockUnionBtn;
            var isAllianceUnlock = UnlockMgr.I.CheckUnlock(UnlockFuncEnum.Alliance);
            D.Warning?.Log($"补足列表 ID:{needValue.ID} Value:{needValue.val} Access:{GameUtils.SerializeToStr(access)}");

            foreach (var i in access)
            {
                var accessType = (Enum_AccessType)i;
                switch (accessType)
                {
                    // case Enum_AccessType.Ad:
                    // {
                    //     var adShopCfgs = Cfg.C.CADShop.RawList();
                    //     var cfg = adShopCfgs.Where(c => c.Param2 == itemCfg?.Id).FirstOrDefault();
                    //
                    //     if (cfg != null)
                    //     {
                    //         //免费获取
                    //         showDataList.Add(new ComplementPopADData(i, UIComplementPop.UIComplementPopItemEnum.WatchAD, ShopBtnTypeEnum.Free, GameData.I.AdShopData.GetAdGroupData(cfg.Group),
                    //             () => { OnClickAdsShop(itemCfg); }));
                    //     }
                    //     else if (itemData != null)
                    //     {
                    //         if (popEnum == UIComplementPop.UIComplementPopEnum.Recover)
                    //         {
                    //             var shopAdId = ShopLogicConfig.Physical_AdID;
                    //             var groupId = GameData.I.AdShopData.GetGroupId(shopAdId);
                    //             var adData = GameData.I.AdShopData.GetAdGroupData(groupId);
                    //
                    //             showDataList.Add(new ComplementPopADData(i, UIComplementPop.UIComplementPopItemEnum.WatchAD, ShopBtnTypeEnum.Free, adData, () =>
                    //             {
                    //                 ItemData item;
                    //
                    //                 if (adData != null && adData.IsCanGetAd() && AdShopMgr.I.CheckAdProp(out item))
                    //                 {
                    //                     AdShopMgr.I.UseAdProp(item, () => { RecoverAPByAd(new object[] {groupId, shopAdId}); });
                    //                 }
                    //                 else
                    //                 {
                    //                     AdTimingMgr.I.Play(AdTimingConst.PHYSICAL_POWER, RecoverAPByAd, null, groupId, shopAdId);
                    //                 }
                    //
                    //                 PopupManager.I.ClosePopup<UIComplementPop>();
                    //             }));
                    //         }
                    //     }
                    //
                    //     break;
                    // }
                    case Enum_AccessType.Gift:
                        {
                            GiftItemData gift = null;
                            var needItemId = -1;

                            if (popEnum == UIComplementPop.UIComplementPopEnum.Coin)
                            {
                                needItemId = CfgConst.GoldId;
                                gift = GetCoinGift();
                            }
                            else if (popEnum == UIComplementPop.UIComplementPopEnum.HeroExp)
                            {
                                needItemId = ConfigID.vm_heroExp;
                                gift = GetPropGift(needItemId);
                            }
                            else if (popEnum == UIComplementPop.UIComplementPopEnum.Energy)
                            {
                                needItemId = ConfigID.Recover_Energy;

                                if (!FirstChargeNewMgr.I.buyOk)
                                {
                                    gift = FirstChargeNewMgr.I.GetGiftData();
                                }
                                else
                                {
                                    var energyGiftData = GameData.I.GiftData.GetPeriodGiftDats(GiftTypeEnum.EnergyGift);

                                    if (energyGiftData != null)
                                    {
                                        foreach (var item in energyGiftData)
                                        {
                                            if (GameData.I.GiftData.CheckGiftLimitBuy(item.Value.CfgId))
                                            {
                                                continue;
                                            }

                                            gift = item.Value;
                                            break;
                                        }
                                    }
                                }
                            }
                            else
                            {
                                needItemId = needValue.ID;
                                gift = GetPropGift(needItemId);
                            }

                            if (gift != null)
                            {
                                D.Warning?.Log($"找到的礼包数据:{gift.CfgId}");

                                //礼包
                                showDataList.Add(new ComplementPopGiftData(i, UIComplementPop.UIComplementPopItemEnum.Gift, ShopBtnTypeEnum.RealMoney, () => { return gift; }, null,
                                    (giftValue) => { OnClickBuy(giftValue); }));

                                var autoPopupGift = gift;//GetAutoPopupGift(needItemId); //不要重复查找。

                                if (needItemId > 0)
                                {
                                    var curEnergy = K3PlayerMgr.I.PlayerData?.energy ?? 0;

                                    if (popEnum != UIComplementPop.UIComplementPopEnum.Energy)
                                    {
                                        AutoPopup.SetAutoPopupGift(autoPopupGift);
                                    }
                                    else if (curEnergy <= 0)
                                    {
                                        AutoPopup.SetAutoPopupGift(autoPopupGift);
                                    }
                                }
                            }
                            else
                            {
                                D.Debug?.Log($"找不到对应道具{needValue.ID}的礼包");
                            }

                            break;
                        }
                    case Enum_AccessType.ItemStore:
                        {
                            if (itemStore != null)
                            {
                                var itemStoreCfg = await Cfg.C.CItemStore.GetConfigAsync(itemStore.cfgID);
                                if (itemStore != null && ((itemStoreCfg?.Limit > itemStore.times) == true) && itemStore.costs.Count > 0)
                                {
                                    if (needValue.ID == 20010000 && LPlayer.I.GetMainCityLevel() <= MetaConfig.AddStoneOpen)
                                    {
                                        break; //石头购买 等级限制~
                                    }

                                    var buyNum = Mathf.CeilToInt((need - count) * 1f / itemStore.assets.val);
                                    //道具商店购买
                                    showDataList.Add(new ComplementPopItemStoreData(i, UIComplementPop.UIComplementPopItemEnum.Shop, ShopBtnTypeEnum.Money, itemStore,
                                        () => { DeepUI.PopupManager.I.ShowDialog<UIBuyItemPop>(new UIBuyItemPopData() { Item = itemCfg, NeedNum = (int)(need - count) }); },
                                        () =>
                                        {
                                            UIHelper.CostGoldToDo((success) =>
                                            {
                                                if (success)
                                                {
                                                    PopupManager.I.ClosePopup<UIComplementPop>();
                                                    StoreAssetsMgr.I.ItemStoreBuy(itemStore.cfgID, buyNum);
                                                }
                                                else
                                                {
                                                    PopupManager.I.ClosePopup<UIComplementPop>();
                                                    //PopupManager.I.ClosePopup<UIMainCityLevelUp>();
                                                }
                                            }, (int)(buyNum * itemStore.costs[0].val));
                                        }
                                    ));
                                }
                            }
                            break;
                        }
                    case Enum_AccessType.CoinBuy:
                        {
                            if (needValue.ID == (int)MetaConfig.EnergyIteam)
                            {
                                var priceType = AssetType.Vm;
                                var priceId = CfgConst.DiamondId;
                                var price = await PhysicalPowerMgr.I.GetPrice();
                                var buyCount = await PhysicalPowerMgr.I.GetBuyCountLeft();
                                buyCount = Math.Max(1, buyCount);

                                var canBuyPhysicalPower = await PhysicalPowerMgr.I.CanBuyPhysicalPower();

                                showDataList.Add(new ComplementPopItemCoinBuyData(i, UIComplementPop.UIComplementPopItemEnum.ItemStore, ShopBtnTypeEnum.Money, priceType, priceId, price, buyCount, () => { }, () =>
                                {
                                    if (needValue.ID == (int)MetaConfig.EnergyIteam)
                                    {
                                        if (!canBuyPhysicalPower)
                                        {
                                            UITools.PopTips(LocalizationMgr.Get("System_PowerBuy_Erro"));
                                            return;
                                        }

                                        UIHelper.CostGoldToDo((success) =>
                                        {
                                            PopupManager.I.ClosePopup<UIComplementPop>();

                                            if (success)
                                            {
                                                UITools.PopTips(LocalizationMgr.Get("Gift_monthcard_tips_02"));
                                                PhysicalPowerMgr.I.PowerBuy();
                                            }
                                        }, price);

                                        return;
                                    }
                                }));
                            }

                            break;
                        }
                    case Enum_AccessType.Rally:
                        {
                            //集结
                            showDataList.Add(new ComplementPopData(i, UIComplementPop.UIComplementPopItemEnum.Rally, ShopBtnTypeEnum.Desc, () => { SendSearchAssembled(itemCfg); }));
                            break;
                        }
                    case Enum_AccessType.PVE:
                        {
                             
                                //大地图打怪获取
                                showDataList.Add(new ComplementPopData(i, UIComplementPop.UIComplementPopItemEnum.Pve, ShopBtnTypeEnum.Desc, () => { SendSearchEnemy(itemCfg); }));
                             

                            break;
                        }
                    case Enum_AccessType.Compose:
                        {
                            //商城道具合成
                            showDataList.Add(new ComplementPopData(i, UIComplementPop.UIComplementPopItemEnum.ItemCompose, ShopBtnTypeEnum.Desc, () =>
                            {
                                PopupManager.I.ClosePopup<UIComplementPop>();
                                UIComposeItemPop.ComposeItem(itemCfg.Id, needValue.val);
                            }));
                            break;
                        }
                    case Enum_AccessType.Collect:
                        {
                            //采集
                            showDataList.Add(new ComplementPopData(i, UIComplementPop.UIComplementPopItemEnum.Collect, ShopBtnTypeEnum.Desc, () => { SendSearchCollect(itemCfg); }));
                            break;
                        }
                    case Enum_AccessType.AllianceShop:
                        {
                            //联盟商店
                            showDataList.Add(new ComplementPopData(i, UIComplementPop.UIComplementPopItemEnum.AllianceShop, ShopBtnTypeEnum.Desc, () =>
                            {
                                if (LPlayer.I.IsPlayerInUnion())
                                {
                                    CloseUI(() =>
                                    {
                                        //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, MainMenuType.ALLIANCE,
                                        //    new UIChatData()
                                        //    {
                                        //        CurrentTabKey = GameData.I.MainData.CurrMenuType,
                                        //        CurrChatTab = ChatTabs.Alliance
                                        //    });

                                        AllianceShopMgr.I.OpenAllianceShop();
                                    });
                                }
                                else
                                {
                                    if (!isAllianceUnlock)
                                    {
                                        // UITools.PopTips(LocalizationMgr.Format("Alliance_Challenge_Level_Tips", allianceOpen + 1));
                                        UITools.PopTips(UnlockMgr.I.GetUnlockDesc(UnlockFuncEnum.Alliance));
                                        return;
                                    }

                                    CloseUI(() =>
                                    {
                                        //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, MainMenuType.ALLIANCE,
                                        //    new UIChatData()
                                        //    {
                                        //        CurrentTabKey = GameData.I.MainData.CurrMenuType,
                                        //        CurrChatTab = ChatTabs.Alliance
                                        //    });
                                    });
                                }
                            }));

                            break;
                        }
                    case Enum_AccessType.AllianceMobilization:
                        {
                            //联盟总动员
                            showDataList.Add(new ComplementPopData(i, UIComplementPop.UIComplementPopItemEnum.AllianceMobilization, ShopBtnTypeEnum.Desc, () =>
                            {
                                if (LPlayer.I.IsPlayerInUnion())
                                {
                                    CloseUI(() => { AllianceMobilizeMgr.I.OpenAllianceAction(); });
                                }
                                else
                                {
                                    if (!isAllianceUnlock)
                                    {
                                        // UITools.PopTips(LocalizationMgr.Format("Alliance_Challenge_Level_Tips", allianceOpen + 1));
                                        UITools.PopTips(UnlockMgr.I.GetUnlockDesc(UnlockFuncEnum.Alliance));
                                        return;
                                    }

                                    // TODO不需要关闭所有panel，如果有特殊界面请单独关闭
                                    //PopupManager.I.ClearAllPanel(_ =>
                                    //{
                                    //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, MainMenuType.ALLIANCE,
                                    //    new UIChatData()
                                    //    {
                                    //        CurrentTabKey = GameData.I.MainData.CurrMenuType,
                                    //        CurrChatTab = ChatTabs.Alliance
                                    //    });

                                    //UIMain.I.TrySwitch(MainMenuConst.ALLIANCE, new UIChatData() { CurrentTabKey = GameData.I.MainData.CurrMenuType, CurrChatTab = ChatTabs.Alliance });
                                    //});
                                }
                            }));

                            break;
                        }
                    case Enum_AccessType.Season:
                        {
                            //赛季
                            showDataList.Add(new ComplementPopData(i, UIComplementPop.UIComplementPopItemEnum.SeasonAchRank, ShopBtnTypeEnum.Desc, () => { OnOpenSeasonRank(itemCfg); }));
                            break;
                        }
                    case Enum_AccessType.AllianceHelp:
                        {
                            //联盟帮助
                            if (isAllianceUnlock) //联盟解锁
                            {
                                UIComplementPop.UIComplementPopItemEnum _type = UIComplementPop.UIComplementPopItemEnum.AllianceHelp;
                                ShopBtnTypeEnum enumType = ShopBtnTypeEnum.AllianceHelp;
                                if (LPlayer.I.UnionID <= 0)
                                {
                                    _type = UIComplementPop.UIComplementPopItemEnum.JoinAlliance;
                                    enumType = ShopBtnTypeEnum.JoinAlliance;
                                }

                                //if (ChackItemCanHelp(itemCfg.Id))
                                //{
                                var itemId = 0;
                                if (popEnum == UIComplementPop.UIComplementPopEnum.Energy)
                                {
                                    itemId = 401001;
                                }
                                else
                                {
                                    itemId = null == itemCfg ? itemCfg.Id : 0;
                                }

                                var helpConfig = LAllianceHelp.I.GetHelpCofigByItemId(itemId);
                                if (helpConfig != null)
                                {
                                    LAllianceHelpData helpData = new LAllianceHelpData(helpConfig.Id);
                                    //添加帮助
                                    showDataList.Add(new ComplementPopAllianceHelpData(i, _type, enumType, helpData, () =>
                                    {
                                        //无联盟跳到申请加入联盟界面
                                        if (LPlayer.I.UnionID <= 0)
                                        {
                                            CloseUI(() =>
                                            {
                                                //PopupManager.I.ClosePopup<UIMainCityLevelUp>();
                                            });
                                            // if (!LSkill.I.CanToDoByCityLevel_ConfigID((int)MetaConfig.UnlockUnionBtn, true))
                                            if (!UnlockMgr.I.CheckUnlock(UnlockFuncEnum.Alliance))
                                            {
                                                UITools.PopTips(UnlockMgr.I.GetUnlockDesc(UnlockFuncEnum.Alliance));
                                                return;
                                            }

                                            //LAllianceMgr.I.ShowAllianceMain(null);
                                            //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.ALLIANCE);
                                            PopupManager.I.ShowDialog<UIAllianceWelcome>();
                                            //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.ALLIANCE);
                                        }
                                        else
                                        {
                                            if (ChackUnionHelpIsLimit(itemId, out int id))
                                            {
                                                if (AllianceGameData.I.AllianceHelp.ApplyHelping(id))
                                                {
                                                    FloatTips.I.FloatMsg(LocalizationMgr.Get("Alliance_Help_Text5"));
                                                }
                                                else
                                                {
                                                    //有联盟则发送请求
                                                    LAllianceHelp.I.ReqUnionHelpApply(id);
                                                    PopupManager.I.ClosePopup<UIComplementPop>();
                                                }
                                            }
                                            else
                                            {
                                                FloatTips.I.FloatMsg(LocalizationMgr.Get("Alliance_Help_Time_Out"));
                                            }
                                        }
                                    }));
                                    //}
                                }
                            }

                            break;
                        }
                    case Enum_AccessType.DimensionActivity:
                        {
                            //次元宝藏活动
                            showDataList.Add(new ComplementPopData(i, UIComplementPop.UIComplementPopItemEnum.DimensionActivity, ShopBtnTypeEnum.Desc, () =>
                            {
                                int actId = 10130001; //活动id写死了，策划说的
                                if (!UIActivityMain.ShowActivityByCfgId(actId))
                                {
                                    UITools.PopTips(LocalizationMgr.Get("Skin_manage_tips_01"));
                                    return;
                                }

                                CloseUI(null, false);
                            }));
                            break;
                        }
                    case Enum_AccessType.Bag:
                        {
                            if (UnlockMgr.I.CheckUnlock(UnlockFuncEnum.Bag))
                            {
                                //背包
                                showDataList.Add(new ComplementPopData(i, UIComplementPop.UIComplementPopItemEnum.ShopCoin, ShopBtnTypeEnum.Desc,
                                () => { CloseUI(() => { ShopMgr.I.OpenShopPanel(ShopJumpMark.bag); }); }));
                            }
                            break;
                        }
                    case Enum_AccessType.DailyTask:
                        {
                            if (LSwitchMgr.I.IsFunctionOpen(SwitchConfig.DailyTask) && UnlockMgr.I.CheckUnlock(UnlockFuncEnum.DailyTask))
                            {
                                showDataList.Add(new ComplementPopGoToData(i, UIComplementPop.UIComplementPopItemEnum.DailyTask, ShopBtnTypeEnum.Desc, () =>
                                {
                                    //if (!LSwitchMgr.I.IsFunctionOpen(SwitchConfig.DailyTask) || !UnlockMgr.I.CheckUnlock(UnlockFuncEnum.DailyTask))
                                    //{
                                    //    UITools.PopTips(UnlockMgr.I.GetUnlockDesc(UnlockFuncEnum.DailyTask));
                                    //    return;
                                    //}

                                    PopupManager.I.ClosePopup<UIComplementPop>();

                                    TaskMgr.I.OpenDailyTask(DailyTaskEnum.Alliance);
                                }));
                            }
                            break;
                        }
                    case Enum_AccessType.VIPShop:
                    case Enum_AccessType.VIP:
                        {
                            if (UnlockMgr.I.CheckUnlock(UnlockFuncEnum.VIP))
                            {
                                showDataList.Add(new ComplementPopData(i, UIComplementPop.UIComplementPopItemEnum.VIPShop, ShopBtnTypeEnum.Desc, () =>
                                { 
                                    //{
                                    PopupManager.I.ClosePopup<UIComplementPop>();
                                    VipManager.I.OpenVip();
                                    //}
                                    //else
                                    //{
                                    //    UITools.PopTips(UnlockMgr.I.GetUnlockDesc(UnlockFuncEnum.VIP));
                                    //}
                                }));
                            }
                        }
                        break;
                    case Enum_AccessType.BuildingGold:
                        {
                            if (UnlockMgr.I.CheckUnlock(UnlockFuncEnum.Building_2))
                            { 
                                showDataList.Add(new ComplementPopData(i, UIComplementPop.UIComplementPopItemEnum.ItemBuildingGold, ShopBtnTypeEnum.Desc, () =>
                                {
                                    //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.PRISON);
                                    var ui = PopupManager.I.FindPopup<UIMainCity>();
                                    if (ui != null)
                                    {
                                        PopupManager.I.ClearAllPopup();
                                         

                                        int cityCfgID = LPlayer.I.GetCityBuildingIDByType((int)MainCityItem.CityType.Offline);
                                        ui.dragImage.MoveTOCfg(cityCfgID, () =>
                                        {
                                            var guidData = new UIGuidData();

                                            guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMainCity", UIItem = $"FullScreenBgCanvas/Root/BG1/Content/Building/build_{(int)MainCityItem.CityType.Offline}" });

                                            UIGuid.StartGuid(guidData);
                                        });
                                    }
                                }));
                            }
                            break;
                        }
                    case Enum_AccessType.BuildingBarracks:
                        {
                            if (UnlockMgr.I.CheckUnlock(UnlockFuncEnum.Building_Un))
                            { 
                                showDataList.Add(new ComplementPopData(i, UIComplementPop.UIComplementPopItemEnum.ItemBuildingBarracks, ShopBtnTypeEnum.Desc, () =>
                                {
                                    //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.PRISON);
                                    var ui = PopupManager.I.FindPopup<UIMainCity>();
                                    if (ui != null)
                                    {
                                        PopupManager.I.ClearAllPopup();
                                         
                                        int cityCfgID = LPlayer.I.GetCityBuildingIDByType((int)MainCityItem.CityType.Barrack);
                                        ui.dragImage.MoveTOCfg(cityCfgID, () =>
                                        {
                                            var guidData = new UIGuidData();

                                            guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMainCity", UIItem = $"FullScreenBgCanvas/Root/BG1/Content/Building/build_{(int)MainCityItem.CityType.Barrack}" });

                                            UIGuid.StartGuid(guidData);
                                        });
                                    }
                                }));
                            }
                            break;
                        }
                    case Enum_AccessType.GoToMerge:
                        {
                            showDataList.Add(new ComplementPopGoToData(i, UIComplementPop.UIComplementPopItemEnum.GoToMerge, ShopBtnTypeEnum.GoToLand, () => { OnClickGotoLand(); }));
                            break;
                        }
                    case Enum_AccessType.Recruit:
                        {
                            if (UnlockMgr.I.CheckUnlock(UnlockFuncEnum.Building_4))
                            {
                                showDataList.Add(new ComplementPopData(i, UIComplementPop.UIComplementPopItemEnum.HeroRecruit, ShopBtnTypeEnum.Desc, () =>
                                { 
                                    PopupManager.I.ClosePopup<UIComplementPop>();
                                    PopupManager.I.ClosePopup<UIHeroUpStarDialog>();
                                    PopupManager.I.ClosePopup<UIHeroShowView>();
                                    HeroRecruitMgr.I.OpenHeroRecruit();
                                    K3PlayerMgr.I.SetOrderState(K3PlayerMgr.orderState.Normal);

                                }));
                            }
                            break;
                        }
                    case Enum_AccessType.StargazingPlatform:
                        { 
                                showDataList.Add(new ComplementPopData(i, UIComplementPop.UIComplementPopItemEnum.AstrologicalStage, ShopBtnTypeEnum.Desc, () =>
                                {
                                    if (UI.Utils.MainFunctionOpenUtils.WorldOpenState)
                                    {
                                        CloseUI(() => { PopupManager.I.ShowLayer<UIStargazingPlatform>(); });
                                    }
                                    else
                                    {
                                        FloatTips.I.FloatMsg(LocalizationMgr.Get("Tips_UnlockAstralSpire"));
                                    }
                                }));
                            
                            break;
                        }
                    case Enum_AccessType.CommonMonth:
                        {
                            if (UnlockMgr.I.CheckUnlock(UnlockFuncEnum.MonthCard))
                            {
                                showDataList.Add(new ComplementPopData(i, UIComplementPop.UIComplementPopItemEnum.MonthCard, ShopBtnTypeEnum.Desc, () =>
                                { 
                                        PopupManager.I.ClosePopup<UIComplementPop>();
                                        MonthCardMgr.I.OpenMonthCardPop(MonthCardEnum.Common); 
                                    //else
                                    //{
                                    //    UITools.PopTips(UnlockMgr.I.GetUnlockDesc(UnlockFuncEnum.MonthCard));
                                    //}
                                }));
                            }

                            break;
                        }
                    case Enum_AccessType.WeekCard:
                        {
                            var cfg = await Cfg.C.CD2Config.GetConfigAsync(10202);
                            var sevenDayLimitLevel = cfg.Val;
                            if (LPlayer.I.GetMainCityLevel() >= sevenDayLimitLevel)
                            {
                                showDataList.Add(new ComplementPopData(i, UIComplementPop.UIComplementPopItemEnum.MonthCard, ShopBtnTypeEnum.Desc, () =>
                                {
                                    PopupManager.I.ClosePopup<UIComplementPop>();
                                      
                                    GameData.I.ActivityData.ReqActvList(true, false);
                                    var tab = ShopTab.WeekCard;
                                    ShopMgr.I.OpenShopPanel(tab, ShopTabConfig.I.specialDealEntranceTabs);
                                    //PopupManager.I.ShowPanel<MonthCardPageDlg>();

                                }));
                            }

                            break;
                        }
                    case Enum_AccessType.ChatperFunc:
                        {
                            if (!ChapterTaskMgr.I.CheckChapterTaskIsFinish())
                            {
                                showDataList.Add(new ComplementPopData(i, UIComplementPop.UIComplementPopItemEnum.ChatperFunc, ShopBtnTypeEnum.Desc, () =>
                                {
                                    PopupManager.I.ClosePopup<UIComplementPop>();
                                    if (ChapterTaskMgr.I.CheckGotoHeroPreview())
                                    {
                                        PopupManager.I.ShowLayer<UIHeroSkinPreview>();
                                        return;
                                    }
                                    PopupManager.I.ShowPanel<UIChapterTask>();

                                }));
                            }
                            break;
                        }
                    case Enum_AccessType.SuperWeaponss:
                        { 
                                //跳转到超级武器
                                showDataList.Add(new ComplementPopData(i, UIComplementPop.UIComplementPopItemEnum.SuperWeaponss, ShopBtnTypeEnum.Desc, () =>
                                {
                                    if (MainFunctionOpenUtils.WorldOpenState)
                                    {
                                        PopupManager.I.ClearAllPopup();

                                        EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, MainMenuType.WORLD);
                                        //定位到龙巢
                                        LRandomMapBuilding.Instance.JumpToWarBuilding();
                                    }
                                    else
                                    {
                                        FloatTips.I.FloatMsg(UnlockMgr.I.GetUnlockDesc(UnlockFuncEnum.World));

                                    }
                                }));
                             
                            break;
                        }
                    case Enum_AccessType.AllianceDonate:
                        {
                            //跳转到联盟捐献
                            showDataList.Add(new ComplementPopData(i, UIComplementPop.UIComplementPopItemEnum.AllianceDonate, ShopBtnTypeEnum.Desc, () =>
                            {
                                if (LPlayer.I.IsPlayerInUnion())
                                {
                                    CloseUI(() =>
                                    {
                                        //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, MainMenuType.ALLIANCE,
                                        //    new UIChatData()
                                        //    {
                                        //        CurrentTabKey = GameData.I.MainData.CurrMenuType,
                                        //        CurrChatTab = ChatTabs.Alliance
                                        //    });
                                        PopupManager.I.ShowLayer<UIAllianceSkillNew>();
                                    });
                                }
                                else
                                {
                                    if (!isAllianceUnlock)
                                    {
                                        // UITools.PopTips(LocalizationMgr.Format("Alliance_Challenge_Level_Tips", allianceOpen + 1));
                                        UITools.PopTips(UnlockMgr.I.GetUnlockDesc(UnlockFuncEnum.Alliance));
                                        return;
                                    }

                                    CloseUI(() =>
                                    {
                                        //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, MainMenuType.ALLIANCE,
                                        //    new UIChatData()
                                        //    {
                                        //        CurrentTabKey = GameData.I.MainData.CurrMenuType,
                                        //        CurrChatTab = ChatTabs.Alliance
                                        //    });
                                    });
                                }
                            }));

                            break;
                        }

                    case Enum_AccessType.UseItem:
                        //使用道具获取
                        if (UnlockMgr.I.CheckUnlock(UnlockFuncEnum.Bag))
                        {
                            var useItems = PlayerAssetsMgr.I.GetItemListByNeed(needValue);

                            foreach (var useItem in useItems)
                            {
                                showDataList.Add(new ComplemenUseItemData(i, UIComplementPop.UIComplementPopItemEnum.USEITEM, ShopBtnTypeEnum.Desc, useItem,needValue.val, () =>
                                {
                                    //点击使用 
                                        var para = new UseItem() { CfgID = useItem.Cfg.Id, count = 1 };
                                        para.customCtx.Add("selected",needValue.ID.ToString());
                                        para.customCtx.Add("newVersion", "1");
                                        var req = new UseItemReq();
                                        req.items.Add(para);
                                        MessageMgr.Send(req); 

                                }, (num) =>
                                { 
                                        //快速使用
                                        D.Error?.Log($"快速使用");
                                        var para = new UseItem() { CfgID = useItem.Cfg.Id, count =num };
                                        para.customCtx.Add("selected", needValue.ID.ToString()); //服务器说后续这里改成传ID不传索引
                                        para.customCtx.Add("newVersion", "1");
                                        var req = new UseItemReq();
                                        req.items.Add(para);
                                        MessageMgr.Send(req);
                                     
                                }));
                            }


                        }
                        break;
                }
            }

            return showDataList;
        }

        private GiftItemData GetHeroGift(int heroModelId)
        {
            var giftData = GameData.I.GiftData.GetPeriodGiftDats(GiftTypeEnum.HeroGift);
            if (giftData == null)
            {
                D.Warning?.Log($"礼包数据找不到：{GiftTypeEnum.HeroGift}");
                return null;
            }

            foreach (var gift in giftData)
            {
                if (gift.Value == null)
                    continue;

                //if (gift.Value.GetGiftParam1() == heroModelId)
                {
                    if (!GameData.I.GiftData.CheckGiftLimitBuy(gift.Value.CfgId))
                    {
                        return gift.Value;
                    }
                    //return gift.Value;
                }
            }
            D.Warning?.Log($"礼包数据限购：{GiftTypeEnum.HeroGift}");

            return null;
        }

        private GiftItemData GetAutoPopupGift(int itemId)
        {
            var giftData = ShopTabConfig.I.triggerEntranceData?.gifts;
            if (giftData == null)
            {
                return null;
            }

            foreach (var giftList in giftData)
            {
                foreach (var gift in giftList)
                {
                    if (gift.GetGiftParam1() == itemId || gift.GetGiftParam3()?.Contains(itemId) == true)
                    {
                        if (!GameData.I.GiftData.CheckGiftLimitBuy(gift.CfgId))
                        {
                            return gift;
                        }
                    }
                }
            }

            return null;
        }

        /// <summary>
        /// 购买礼包
        /// </summary>
        /// <param name="param"></param>
        private void OnClickBuy(GiftItemData giftData)
        {
            if (giftData == null)
                return;

            var id = giftData.CfgId.ToString();
            var price = giftData.GetProductId().ToString();

            if (GameData.I.GiftData.CheckGiftLimitBuy(giftData.CfgId))
            {
                UITools.PopTips(LocalizationMgr.Get("Gift_dailygift_tips_01"));
                return;
            }

            LPay.I.BuyItem(id, price, ShopMgr.I.OnBuyItemAction);
        }


        public async UniTaskVoid OpenResNeedUI(GameCurrencyEnum type, int needNum)
        {
            int id = 0;

            var showDataList = new List<ComplementPopData>();

            if (type == GameCurrencyEnum.CURRENCY_DIAMOND)
            {

                id = (int)MetaConfig.ADShopGems;

                var diamondGift = this.GetDiamondGift();
                if (diamondGift != null)
                {
                    //礼包
                    showDataList.Add(new ComplementPopGiftData((int)Enum_AccessType.Gift, UIComplementPop.UIComplementPopItemEnum.Gift, ShopBtnTypeEnum.RealMoney, this.GetDiamondGift, null, (gi) =>
                    {
                        //跳到道具商城
                        ShopMgr.I.OpenShopPanel(ShopJumpMark.gem);
                    }));
                }
                //普通
                // AdShopData adShopData = null;
                // var cfg = await Cfg.C.CADShop.GetConfigAsync(id);
                // if (cfg != null)
                // {
                //     var groupId = AdShopMgr.I.GetGroupId(cfg.Id);
                //     adShopData = AdShopMgr.I.GetAdGroupData(groupId);
                // }
                //广告
                // showDataList.Add(new ComplementPopADData(UIComplementPop.UIComplementPopItemEnum.WatchAD, ShopBtnTypeEnum.Free, adShopData, () =>
                // {
                //     this.OnClickAdsShop(AdTimingConst.GetDiamond, id);
                // }));

                //背包
                if (this.CheckBagResEnough(type))
                    showDataList.Add(new ComplementPopData((int)Enum_AccessType.Compose, UIComplementPop.UIComplementPopItemEnum.ShopDiamond, ShopBtnTypeEnum.Desc, () => { ShopMgr.I.OpenShopPanel(ShopJumpMark.bag); }));

                PopupManager.I.ShowPanel<UIComplementPop>(new UIComplementPopData() { popShowEnum = UIComplementPop.UIComplementPopEnum.Diamond, datas = showDataList, needData = new CommonItem.CommonItemData() { Id = CfgConst.DiamondId, Typ = AssetType.Vm, Val = needNum } });
            }
            else if (type == GameCurrencyEnum.BadgeHornor)
            {
                showDataList.Add(new ComplementPopData(0, UIComplementPop.UIComplementPopItemEnum.ActivityGuangming, ShopBtnTypeEnum.GoToActivity, () =>
                {
                    UIActivityMain.Open(new UIActivityMainData() { ActvType = (ACTVTYPE)3 });
                    PopupManager.I.ClosePopup<UIComplementPop>();
                    //PopupManager.I.ClosePopup<KnightLevelPop>();
                }));
                PopupManager.I.ShowPanel<UIComplementPop>(new UIComplementPopData() { popShowEnum = UIComplementPop.UIComplementPopEnum.Knight, datas = showDataList });
            }
            else if (type == GameCurrencyEnum.CURRENCY_GOLD)
            {
                //var goldCfg = await Cfg.C.CVm.GetConfigAsync(ConfigID.VM_Gold);

                //var access = new List<int>();

                //foreach (var acc in goldCfg.Access)
                //{
                //    access.Add(int.Parse(acc));
                //}

                var showList = GetProShowDatas(UIComplementPop.UIComplementPopEnum.Coin, new TypIDVal()
                {
                     typ=AssetType.Vm,
                     ID= ConfigID.VM_Gold,
                     val =1
                });

                PopupManager.I.ShowPanel<UIComplementPop>(new UIComplementPopData()
                {
                    popShowEnum = UIComplementPop.UIComplementPopEnum.Coin,
                    datas = showDataList,
                    needData = new CommonItem.CommonItemData() { Id = CfgConst.GoldId, Typ = AssetType.Vm, Val = needNum }
                });

            }
            else if (type == GameCurrencyEnum.HERO_EXP)
            {
                //var goldCfg = await Cfg.C.CVm.GetConfigAsync(ConfigID.vm_heroExp);

                //var access = new List<int>();

                //foreach (var acc in goldCfg.Access)
                //{
                //    access.Add(int.Parse(acc));
                //}

                var showList = GetProShowDatas(UIComplementPop.UIComplementPopEnum.HeroExp, new TypIDVal()
                {
                    typ = AssetType.Vm,
                    ID = ConfigID.vm_heroExp,
                    val = 1
                });

                PopupManager.I.ShowPanel<UIComplementPop>(new UIComplementPopData()
                {
                    popShowEnum = UIComplementPop.UIComplementPopEnum.HeroExp,
                    datas = showDataList,
                    needData = new CommonItem.CommonItemData() { Id = Config.ConfigID.vm_heroExp, Typ = AssetType.Vm, Val = needNum }
                });
            }
            else if (type == GameCurrencyEnum.CURRENCY_JADE)
            {

                id = Config.ConfigID.VM_Jade;
                var itemStoreData = this.GetItemStoreData((int)MetaConfig.Auction_jade_exchange);
                int CostValue = 0;
                int buyValue = 1;
                if (itemStoreData != null)
                {
                    if (needNum > 0)
                    {
                        buyValue = Mathf.CeilToInt((needNum - PlayerAssetsMgr.I.GetVMCount(Config.ConfigID.VM_Jade)) * 1f / itemStoreData.assets.val);
                        CostValue = (int)(buyValue * itemStoreData.costs[0].val);
                    }
                    var itemCfg = await Cfg.C.CItemStore.GetConfigAsync(itemStoreData.cfgID);

                    if (itemStoreData.times >= itemCfg.Limit)
                    {
                        FloatTips.I.FloatMsg(LocalizationMgr.Get("ErrCodeShopBuyLimitErr"));
                        return;
                    }

                    if (PlayerAssetsMgr.I.GetVMCount(itemStoreData.costs[0].ID) < itemStoreData.costs[0].val)
                    {
                        PopupManager.I.ClosePopup<UIComplementPop>();
                        this.OpenGetAssetPop(GameCurrencyEnum.CURRENCY_DIAMOND);
                        return;
                    }

                    PopupManager.I.ClosePopup<UIComplementPop>();
                    PopupManager.I.ShowPanel<UIBuyGoldPop>(new UIBuyGoldPopData() { NeedNum = needNum, ItemstoreID = (int)MetaConfig.Auction_jade_exchange, vmId = id });
                }
            }
            else if (type == GameCurrencyEnum.CURRENCY_ALLIANCE_SLIVER)
            { 
                //var vmCfg = await Cfg.C.CVm.GetConfigAsync(ConfigID.VM_UnionStoreCoin);
                //var access = new List<int>();

                //foreach (var acc in vmCfg.Access)
                //{
                //    access.Add(int.Parse(acc));
                //}

                var showList = GetProShowDatas(UIComplementPop.UIComplementPopEnum.Silver, new TypIDVal() 
                { 
                    typ = AssetType.Vm, 
                    ID = ConfigID.VM_UnionStoreCoin,
                    val = 1 
                });

                PopupManager.I.ShowPanel<UIComplementPop>(new UIComplementPopData()
                {
                    popShowEnum = UIComplementPop.UIComplementPopEnum.Silver,
                    datas = showDataList,
                    needData = new CommonItem.CommonItemData() { Id = ConfigID.VM_UnionStoreCoin, Typ = AssetType.Vm, Val = 1 }
                });
            }
        }


        #endregion

        private bool CheckBagResEnough(GameCurrencyEnum type)
        {
            //由于金币钻石道具类型一直，策划细化详细区分，又不能改类型，于是写死id
            //策划：全艺  2021/9/3
            if (type == GameCurrencyEnum.CURRENCY_DIAMOND)
            {
                var ok1 = BagMgr.I.CheckBagItemEnoughById(3110001);
                var ok2 = BagMgr.I.CheckBagItemEnoughById(3110002);
                var ok3 = BagMgr.I.CheckBagItemEnoughById(3110003);
                var ok4 = BagMgr.I.CheckBagItemEnoughById(3110004);
                var ok5 = BagMgr.I.CheckBagItemEnoughById(3110005);
                var ok6 = BagMgr.I.CheckBagItemEnoughById(3110006);
                var ok7 = BagMgr.I.CheckBagItemEnoughById(3110007);
                var ok8 = BagMgr.I.CheckBagItemEnoughById(3110008);
                var ok9 = BagMgr.I.CheckBagItemEnoughById(3110009);
                return ok1 || ok2 || ok3 || ok4 || ok5 || ok6 || ok7 || ok8 || ok9;
            }
            else
            {
                var ok1 = BagMgr.I.CheckBagItemEnoughById(3100003);
                var ok2 = BagMgr.I.CheckBagItemEnoughById(3100004);
                var ok3 = BagMgr.I.CheckBagItemEnoughById(3100005);
                var ok4 = BagMgr.I.CheckBagItemEnoughById(3100006);
                var ok5 = BagMgr.I.CheckBagItemEnoughById(3100007);
                var ok6 = BagMgr.I.CheckBagItemEnoughById(3100008);
                var ok7 = BagMgr.I.CheckBagItemEnoughById(3100009);
                var ok8 = BagMgr.I.CheckBagItemEnoughById(3100010);
                var ok9 = BagMgr.I.CheckBagItemEnoughById(3100011);
                return ok1 || ok2 || ok3 || ok4 || ok5 || ok6 || ok7 || ok8 || ok9;
            }
        }

        #region 道具礼包

        public void OpenGetPropPop(int cfgID, int num, int searchEnemyIndex = 0, string bannerName = "")
        {
            this.OpenGetPropPop(ItemData.GetItemCfg(cfgID), num, searchEnemyIndex, bannerName);
        }

        public async UniTaskVoid OpenGetPropPop(CItem itemCfg, int num, int searchEnemyIndex = 0, string bannerName = "")
        {
            if (itemCfg == null)
            {
                return;
            }

            //num = num-(int) PlayerAssetsMgr.I.GetItemCountByID(itemCfg.Id);
            //if (num <= 0)
            //{
            //    num = 1;
            //}
            //var showList = new List<ComplementPopData>();

            //var need = num;

            //var access = new List<int>();

            //foreach (var acc in itemCfg.Access)
            //{
            //    access.Add(int.Parse(acc));
            //}

            var showList = await GetProShowDatas(UIComplementPop.UIComplementPopEnum.Item, new TypIDVal()
            {
                typ=AssetType.Item,
                ID=itemCfg.Id,
                val=num
            });

            PopupManager.I.ShowPanel<UIComplementPop>(new UIComplementPopData() { popShowEnum = UIComplementPop.UIComplementPopEnum.Item, datas = showList, needData = new CommonItem.CommonItemData() { Id = itemCfg.Id, Val = num, Typ = AssetType.Item } });
            // if (itemCfg == null)
            // {
            //     return;
            // }
            //
            // var showList = new List<ComplementPopData>();
            //
            // var count = PlayerAssetsMgr.I.GetItemCountByID(itemCfg.Id);
            // var need = num;
            //
            // var itemStore = I.GetItemStoreDataByItemID(itemCfg.Id);
            // var itemData = new ItemData(itemCfg.Id);
            // var ok = false;
            // long vmCount = 0;
            // if (itemStore != null)
            // {
            //     vmCount = (need - count) * itemStore.costs[0].val;
            //
            //     var rss = itemStore.costs[0];
            //     var ass = new RssConsume() { Id = rss.ID, Typ = rss.typ, Val = rss.val * (need - count) };
            //     ok = PlayerAssetsMgr.I.EasyCheckCostRess(ass);
            // }
            //
            // var allianceOpen = (int)MetaConfig.NewUnlockUnionBtn;
            // var isUnlock = GameData.I.LevelData.CanToDoByGameLevel(allianceOpen);
            // ///1：广告获取 2：礼包 3：道具商城购买 4：集结 5：打怪 6：合成 7:采集 8:联盟商店 9：联盟总动员 10:赛季成就排行列表
            // foreach (var ii in itemCfg.Access)
            // {
            //     switch (int.Parse(ii))
            //     {
            //         case 1:
            //             var dic = await Cfg.C.CADShop.RawDictAsync();
            //             var cfg = dic.Where(c => c.Value.Param2 == itemCfg.Id).FirstOrDefault().Value;
            //
            //             if (cfg != null)
            //             {
            //                 //免费获取
            //                 showList.Add(new ComplementPopADData(UIComplementPop.UIComplementPopItemEnum.WatchAD, ShopBtnTypeEnum.Free, AdShopMgr.I.GetAdGroupData(cfg.Group), () => { this.OnClickAdsShop(itemCfg); }));
            //             }
            //             break;
            //         case 2:
            //             var gift = this.GetPropGift(itemCfg);
            //             if (gift != null)
            //             {
            //                 //礼包
            //                 showList.Add(new ComplementPopGiftData(UIComplementPop.UIComplementPopItemEnum.Gift, ShopBtnTypeEnum.RealMoney, () =>
            //                 {
            //                     var itemGift = this.GetPropGift(itemCfg);
            //                     return itemGift;
            //                 }, null, (giftValue) => {
            //                     /*OnClickBuy(giftValue);*/
            //                     //跳转页签
            //                     var jumpMark = ShopJumpMark.GetByTabConfig(itemCfg.Cfgid);
            //                     if (jumpMark != null)
            //                     {
            //                         this.CloseUI(() =>
            //                         {
            //                             TaskUtils.JumpShop(jumpMark);
            //                         });
            //                     }
            //                     else
            //                     {
            //                         this.CloseUI(() =>
            //                         {
            //                             ShopMgr.I.OpenShopPanel();
            //                         });
            //                     }
            //                 }));
            //             }
            //             else
            //             {
            //                 D.Warning?.Log($"找不到对应道具{itemCfg.Id}的礼包");
            //             }
            //             break;
            //         case 3:
            //             var itemStoreCfg = await Cfg.C.CItemStore.GetConfigAsync(itemStore.cfgID);
            //             if (itemStore != null && ((itemStoreCfg?.Limit > itemStore.times) == true) && itemStore.costs.Count > 0)
            //             {
            //                 if (itemCfg.Id == 20010000 && LPlayer.I.GetMainCityLevel() <= MetaConfig.AddStoneOpen)
            //                 {
            //                     break; //石头购买 等级限制~
            //                 }
            //
            //                 var buyNum = Mathf.CeilToInt((need - count) * 1f / itemStore.assets.val);
            //                 //道具商店购买
            //                 showList.Add(new ComplementPopItemStoreData(UIComplementPop.UIComplementPopItemEnum.Shop, ShopBtnTypeEnum.Money, itemStore,
            //                     () =>
            //                    {
            //                        DeepUI.PopupManager.I.ShowDialog<UIBuyItemPop>(new UIBuyItemPopData() { Item = itemCfg, NeedNum = (int)(need - count) });
            //                    },
            //                    () =>
            //                    {
            //                        UIHelper.CostGoldToDo((success) =>
            //                        {
            //                            if (success)
            //                            {
            //                                PopupManager.I.ClosePopup<UIComplementPop>();
            //                                StoreAssetsMgr.I.ItemStoreBuy(itemStore.cfgID, buyNum);
            //                            }
            //                            else
            //                            {
            //                                PopupManager.I.ClosePopup<UIComplementPop>();
            //                            }
            //                        }, (int)(buyNum * itemStore.costs[0].val));
            //                    }
            //                     ));
            //             }
            //
            //             break;
            //         case 4:
            //             //集结
            //             showList.Add(new ComplementPopData(UIComplementPop.UIComplementPopItemEnum.Rally, ShopBtnTypeEnum.Desc, () => { this.SendSearchAssembled(itemCfg); }));
            //             break;
            //         case 5:
            //             //大地图打怪获取
            //             if (itemCfg != null)
            //             {
            //                 searchEnemyIndex = (itemCfg.Icon / 10) % 10 - 1;
            //                 searchEnemyIndex = Mathf.Clamp(searchEnemyIndex, 0, 3);
            //             }
            //             showList.Add(new ComplementPopData(UIComplementPop.UIComplementPopItemEnum.Pve, ShopBtnTypeEnum.Desc, () => { this.SendSearchEnemy(itemCfg, searchEnemyIndex); }));
            //             break;
            //         case 6:
            //             //商城道具合成
            //             showList.Add(new ComplementPopData(UIComplementPop.UIComplementPopItemEnum.ItemCompose, ShopBtnTypeEnum.Desc, () =>
            //             {
            //                 PopupManager.I.ClosePopup<UIComplementPop>();
            //                 UIComposeItemPop.ComposeItem(itemCfg.Id, num);
            //             }));
            //             break;
            //         case 7:
            //             //采集
            //             showList.Add(new ComplementPopData(UIComplementPop.UIComplementPopItemEnum.Collect, ShopBtnTypeEnum.Desc, () => { this.SendSearchCollect(itemCfg); }));
            //             break;
            //         case 8:
            //             //联盟商店
            //             showList.Add(new ComplementPopData(UIComplementPop.UIComplementPopItemEnum.AllianceShop, ShopBtnTypeEnum.Desc, () =>
            //            {
            //                if (LPlayer.I.IsPlayerInUnion())
            //                {
            //                    this.CloseUI(() =>
            //                    {
            //                        EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, MainMenuType.ALLIANCE,
            //                                  new UIChatData()
            //                                  {
            //                                      CurrentTabKey = GameData.I.MainData.CurrMenuType,
            //                                      CurrChatTab = ChatTabs.Alliance
            //                                  });
            //
            //                        AllianceShopMgr.I.OpenAllianceShop();
            //                    });
            //                }
            //                else
            //                {
            //                    if (!isUnlock)
            //                    {
            //                        UITools.PopTips(LocalizationMgr.Format("DEMO_37", allianceOpen + 1));
            //                        return;
            //                    }
            //
            //                    this.CloseUI(() =>
            //                          {
            //                              EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, MainMenuType.ALLIANCE,
            //                                      new UIChatData()
            //                                      {
            //                                          CurrentTabKey = GameData.I.MainData.CurrMenuType,
            //                                          CurrChatTab = ChatTabs.Alliance
            //                                      });
            //                          });
            //                }
            //            }));
            //
            //             break;
            //         case 9:
            //             //联盟总动员
            //             showList.Add(new ComplementPopData(UIComplementPop.UIComplementPopItemEnum.AllianceMobilization, ShopBtnTypeEnum.Desc, () =>
            //            {
            //                if (LPlayer.I.IsPlayerInUnion())
            //                {
            //                    this.CloseUI(() =>
            //                    {
            //                        AllianceMobilizeMgr.I.OpenAllianceAction();
            //                    });
            //                }
            //                else
            //                {
            //                    if (!isUnlock)
            //                    {
            //                        UITools.PopTips(LocalizationMgr.Format("DEMO_37", allianceOpen + 1));
            //                        return;
            //                    }
            //
            //                    // TODO不需要关闭所有panel，如果有特殊界面请单独关闭
            //                    //PopupManager.I.ClearAllPanel(_ =>
            //                    //{
            //                    EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, MainMenuType.ALLIANCE,
            //                        new UIChatData()
            //                        {
            //                            CurrentTabKey = GameData.I.MainData.CurrMenuType,
            //                            CurrChatTab = ChatTabs.Alliance
            //                        });
            //
            //                    //UIMain.I.TrySwitch(MainMenuConst.ALLIANCE, new UIChatData() { CurrentTabKey = GameData.I.MainData.CurrMenuType, CurrChatTab = ChatTabs.Alliance });
            //                    //});
            //                }
            //            }));
            //
            //             break;
            //         case 10:
            //             //采集
            //             showList.Add(new ComplementPopData(UIComplementPop.UIComplementPopItemEnum.SeasonAchRank, ShopBtnTypeEnum.Desc, () => { this.OnOpenSeasonRank(itemCfg); }));
            //             break;
            //         case 11:
            //             //联盟帮助
            //             if (isUnlock) //联盟解锁
            //             {
            //                 UIComplementPop.UIComplementPopItemEnum _type = UIComplementPop.UIComplementPopItemEnum.AllianceHelp;
            //                 ShopBtnTypeEnum enumType = ShopBtnTypeEnum.AllianceHelp;
            //                 if (LPlayer.I.UnionID <= 0)
            //                 {
            //                     _type = UIComplementPop.UIComplementPopItemEnum.JoinAlliance;
            //                     enumType = ShopBtnTypeEnum.JoinAlliance;
            //                 }
            //                 //if (ChackItemCanHelp(itemCfg.Id))
            //                 //{
            //                 int itemIndex = 2;
            //                 if (itemCfg.Id == 20010000)
            //                     itemIndex = 2;
            //                 else if (itemCfg.Id == 20010001)
            //                     itemIndex = 3;
            //                 else if (itemCfg.Id == 20010002)
            //                     itemIndex = 4;
            //                 LAllianceHelpData helpData = new LAllianceHelpData(itemIndex);
            //                 //添加帮助
            //                 showList.Add(new ComplementPopAllianceHelpData(_type, enumType, helpData, () =>
            //                 {
            //                     //无联盟跳到申请加入联盟界面
            //                     if (LPlayer.I.UnionID <= 0)
            //                     {
            //                         this.CloseUI(() =>
            //                         {
            //                             PopupManager.I.ClosePopup<UIMainCityLevelUp>();
            //                         });
            //                         LAllianceMgr.I.ShowAllianceMain(null);
            //                         //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.ALLIANCE);
            //                     }
            //                     else
            //                     {
            //                         if (this.ChackUnionHelpIsLimit((UnionHelpType)itemIndex))
            //                         {
            //                             //有联盟则发送请求
            //                             LAllianceHelp.I.ReqUnionHelpApply((UnionHelpType)itemIndex);
            //                             PopupManager.I.ClosePopup<UIComplementPop>();
            //                         }
            //                         else
            //                         {
            //                             FloatTips.I.FloatMsg(LocalizationMgr.Get("Alliance_Help_Time_Out"));
            //                         }
            //                     }
            //                 }));
            //                 //}
            //             }
            //             break;
            //         case 12:
            //             //次元宝藏活动
            //             showList.Add(new ComplementPopData()
            //             {
            //                 popItemEnum = UIComplementPop.UIComplementPopItemEnum.DimensionActivity,
            //                 btnTypeEnum = ShopBtnTypeEnum.Desc,
            //                 click = () =>
            //                 {
            //                     this.CloseUI(() =>
            //                     {
            //                         UI.Treasure.UIDimensionalTreasure.TryOpen(1);
            //                     });
            //                 },
            //                 name = LocalizationMgr.Get("item_acess_12")
            //             });
            //             break;
            //         case 15:
            //             //竞技场
            //             break;
            //         case 14:
            //             //背包
            //             showList.Add(new ComplementPopData(UIComplementPop.UIComplementPopItemEnum.ShopCoin, ShopBtnTypeEnum.Desc, () =>
            //             {
            //                 this.CloseUI(() =>
            //                 {
            //                     ShopMgr.I.OpenShopPanel(ShopJumpMark.bag);
            //                 });
            //             }));
            //             break;
            //         case 16:
            //             //珍妮弗的情报站
            //             showList.Add(new ComplementPopData(UIComplementPop.UIComplementPopItemEnum.Radar, ShopBtnTypeEnum.Desc, () =>
            //             {
            //                 this.CloseUI(() =>
            //                 {
            //                     //var topui=PopupManager.I.FindPopup<CollectAtlasUI.Layer>();
            //                     //var baseui=PopupManager.I.FindPopupFromPool<CollectAtlasTaskUI.Layer>();
            //                     //if(topui!=null&&baseui!=null)
            //                     //{
            //                     //    topui.Close();
            //                     //}
            //                     //if(baseui==null)
            //                     //{
            //                     //    PopupManager.I.ShowLayer<CollectAtlasTaskUI.Layer>();
            //                     //}
            //                     PopupManager.I.ClosePopup<CollectAtlasUI.Layer>();
            //                     PopupManager.I.ShowLayer<CollectAtlasTaskUI.Layer>();
            //                 });
            //             }));
            //             break;
            //         case 17:
            //             //特惠界面
            //             showList.Add(new ComplementPopData()
            //             {
            //                 popItemEnum = UIComplementPop.UIComplementPopItemEnum.DailyDealNew,
            //                 btnTypeEnum = ShopBtnTypeEnum.Desc,
            //                 click = () =>
            //                 {
            //                     this.CloseUI(() =>
            //                     {
            //                         TaskUtils.JumpShop(ShopJumpMark.dailyDeal);
            //                     });
            //                 },
            //                 name = LocalizationMgr.Get("HeroRecruitment_jump01")
            //             });
            //             break;
            //         case 18:
            //             //每日任务
            //             showList.Add(new ComplementPopData()
            //             {
            //                 popItemEnum = UIComplementPop.UIComplementPopItemEnum.DailyTask,
            //                 btnTypeEnum = ShopBtnTypeEnum.Desc,
            //                 click = () =>
            //                 {
            //                     this.CloseUI(() =>
            //                     {
            //                         TaskMgr.I.OpenDailyTask();
            //                     });
            //                 },
            //                 name = LocalizationMgr.Get("HeroRecruitment_jump02")
            //             });
            //             break;
            //         case 19:
            //             ///女神试炼商店
            //             showList.Add(new ComplementPopData()
            //             {
            //                 popItemEnum = UIComplementPop.UIComplementPopItemEnum.GoddessShop,
            //                 btnTypeEnum = ShopBtnTypeEnum.Desc,
            //                 click = () =>
            //                 {
            //                     this.CloseUI(() =>
            //                     {
            //                         LGoddessChallenge.I.TryOpenShop();
            //                     });
            //                 },
            //                 name = LocalizationMgr.Get("KeyGoddessTrial_Titel02")
            //             });
            //             break;
            //         case 20:
            //             ///关卡获取
            //             showList.Add(new ComplementPopData()
            //             {
            //                 popItemEnum = (UIComplementPop.UIComplementPopItemEnum)164019,
            //                 btnTypeEnum = ShopBtnTypeEnum.Desc,
            //                 click = () =>
            //                 {
            //                     this.CloseUI(TaskUtils.JumpToCity);
            //                 },
            //                 name = LocalizationMgr.Get("item_acess_20")
            //             });
            //             break;
            //         case 21:
            //             ///招募获取
            //             showList.Add(new ComplementPopData()
            //             {
            //                 popItemEnum = (UIComplementPop.UIComplementPopItemEnum)164024,
            //                 btnTypeEnum = ShopBtnTypeEnum.Desc,
            //                 click = () =>
            //                 {
            //                     this.CloseUI(() => { HeroRecruitMgr.I.OpenHeroRecruit(); });
            //                 },
            //                 name = LocalizationMgr.Get("item_acess_21")
            //             });
            //             break;
            //         case 22:
            //             // 超级矿的特殊逻辑放在循环外处理，有可能还要删除超值礼包
            //             break;
            //         case 23:
            //             showList.Add(new ActivityPopData()
            //             {
            //                 activityType = ActivityType.LightBattle,
            //                 popItemEnum = UIComplementPop.UIComplementPopItemEnum.ActivityGuangming,
            //                 btnTypeEnum = ShopBtnTypeEnum.GoToActivity,
            //                 click = () =>
            //                 {
            //                     // 跳到对应活动即可
            //                     this.CloseUI(() =>
            //                     {
            //                         UIActivityMain.Open(new UIActivityMainData() { ActvType = (ACTVTYPE)3 });
            //                         PopupManager.I.ClosePopup<UIComplementPop>();
            //                         PopupManager.I.ClosePopup<BadgePop>();
            //                     });
            //                 },
            //                 name = LocalizationMgr.Get("LuminaryBattlefield")
            //             });
            //             break;
            //         case 24: //kvk
            //             showList.Add(new ActivityPopData()
            //             {
            //                 activityType = ActivityType.FracturedLands3,
            //                 popItemEnum = UIComplementPop.UIComplementPopItemEnum.FracturedLands3,
            //                 btnTypeEnum = ShopBtnTypeEnum.GoToActivity,
            //                 click = () =>
            //                 {
            //                     // 跳到对应活动即可
            //                     CloseUI(() =>
            //                     {
            //                         UIActivityMain.Open(new UIActivityMainData() { });
            //                         PopupManager.I.ClosePopup<UIComplementPop>();
            //                         PopupManager.I.ClosePopup<BadgePop>();
            //                     });
            //                 },
            //                 name = LocalizationMgr.Get("Activity_Title_Stage3")
            //             });
            //             break;
            //     }
            // }
            //
            // // 超级矿的特殊逻辑
            // if (itemCfg.Access.Contains("22"))
            // {
            //     // 挂机礼包根据开关控制，关闭时，不显示超级资源矿，开启时，根据其他条件判断是否显示
            //     if (!LOfflineRevenueNew.I.HasFinishGoal() && LSwitchMgr.I.IsFunctionOpen(SwitchConfig.OfflineBenefitsGift))
            //     {
            //         showList.Insert(0, new ComplementPopData()
            //         {
            //             popItemEnum = UIComplementPop.UIComplementPopItemEnum.OffLineGift,
            //             btnTypeEnum = ShopBtnTypeEnum.RealMoney,
            //             click = () =>
            //             {
            //                 this.CloseUI(() => { TaskUtils.JumpShop(ShopJumpMark.offlineGift); });
            //             },
            //             name = LocalizationMgr.Get("Super_Offline")
            //         });
            //     }
            //     
            //     // 挂机礼包根据开关控制，关闭时，常驻显示超值礼包，开启时，根据其他条件判断是否显示
            //     if (LSwitchMgr.I.IsFunctionOpen(SwitchConfig.OfflineBenefitsGift))
            //     {
            //         // 超级矿有可能会把免费的移除
            //         if (!LOfflineRevenueNew.I.HasBoughtOne())
            //         {
            //             ComplementPopData data = null;
            //             foreach (var temp in showList)
            //             {
            //                 if (temp.popItemEnum == UIComplementPop.UIComplementPopItemEnum.Gift)
            //                 {
            //                     data = temp;
            //                     break;
            //                 }
            //             }
            //             if (data != null)
            //             {
            //                 showList.Remove(data);
            //             }
            //         }
            //     }
            //     
            //     
            // }
            //
            //
            //
            //
            // PopupManager.I.ShowPanel<UIComplementPop>(new UIComplementPopData() { popShowEnum = UIComplementPop.UIComplementPopEnum.Item, datas = showList, needData = new CommonItem.CommonItemData() { Id = itemCfg.Id, Val = need, Typ = AssetType.Item },bannerName = bannerName });
        }


        public void OpenGetPropPop(Cfg.G.CK3Item itemCfg, int num, int searchEnemyIndex = 0)
        {
            if (itemCfg == null)
            {
                return;
            }

            PopupManager.I.ShowDialog<K3.UIMergeGoodInfo>(new K3.UIMergeGoodInfoData(new ItemInfo(itemCfg)));
        }


        /// <summary>
        /// 检查联盟帮助上限
        /// </summary>
        /// <param name="_type"></param>
        /// <returns></returns>
        public bool ChackUnionHelpIsLimit(int itemId, out int id)
        {
            bool isLimit = false;
            id = 0;
            var config = LAllianceHelp.I.GetHelpCofigByItemId(itemId);
            if (config == null)
                return isLimit;
            var curCount = AllianceGameData.I.AllianceHelp.GetHelpCountById(config.Id);
            if (config.ApplicationLimit > curCount)
            {
                isLimit = true;
            }
            id = config.Id;
            return isLimit;
        }

        private void OnOpenSeasonRank(CItem itemCfg)
        {
            RankNewMgr.I.OpenSeasonRank();

            //164031
            PopupManager.I.ClosePopup<UIComplementPop>();
        }

        private GiftItemData GetPropGift(int id)
        {

            //IEnumerable<KeyValuePair<int, GiftItemData>> giftData = GameData.I.GiftData.GetPeriodGiftDats(GiftTypeEnum.Resource);
            //var giftData2 = GameData.I.GiftData.GetPeriodGiftDats(GiftTypeEnum.Supply);
            //if (giftData == null)
            //    giftData = Enumerable.Empty<KeyValuePair<int, GiftItemData>>();

            var temp = GameData.I.GiftData.GetGiftDats();


            //IEnumerable<KeyValuePair<int, GiftItemData>> temp = giftData;
            //if (giftData2 != null)
            //{
            //    temp = giftData.Concat(giftData2);
            //}

            foreach (var gift in temp)
            {
                if (gift == null)
                    continue;

                if (gift.GetGiftParam1() == id || (gift.GetGiftParam3()?.Contains(id) == true))
                {
                    if (!GameData.I.GiftData.CheckGiftLimitBuy(gift.CfgId))
                    {
                        return gift;
                    }
                    //else
                    //{
                    //    D.Info?.Log($"{gift.CfgId} 符合，但是不可购买！");
                    //}
                }
                //else
                //{
                //    D.Info?.Log($"{gift.CfgId} 不符合");
                //}
            }

            return null;
        }

        /// <summary>
        /// 点击广告获取ID
        /// </summary>
        // private async UniTaskVoid OnClickAdsShop(string adType, int id)
        // {

        //     var cfg = await Cfg.C.CADShop.GetConfigAsync(id);
        //     if (cfg == null)
        //         return;
        //     var groupId = AdShopMgr.I.GetGroupId(cfg.Id);

        //     var shopData = AdShopMgr.I.GetAdGroupData(groupId);
        //     ItemData item;
        //     if (shopData != null && shopData.IsCanGetAd() && AdShopMgr.I.CheckAdProp(out item))
        //         AdShopMgr.I.UseAdProp(item, () => { this.OnAdCallBack(new object[] { groupId, cfg.Id }); });
        //     else
        //         AdMgr.I.ShowAd(adType, this.OnAdCallBack, null, groupId, cfg.Id);
        //     //AdTimingMgr.I.Play(adType, OnAdCallBack, null, groupId, cfg.Id);
        // }

        // private void OnClickAdsShop(CItem itemCfg)
        // {
        //     var id = itemCfg.Id;
        //     Cfg.C.CADShop.RawDictAsync().ContinueWith(adShopCfgs =>
        //     {
        //         var cfg = adShopCfgs.Where(c => c.Value.Param2 == id).FirstOrDefault().Value;
        //         var groupId = AdShopMgr.I.GetGroupId(cfg.Id);
        //         var shopData = AdShopMgr.I.GetAdGroupData(groupId);
        //         if (shopData != null && shopData.IsCanGetAd() && AdShopMgr.I.CheckAdProp(out var item))
        //             AdShopMgr.I.UseAdProp(item, () => { this.OnAdCallBack(new object[] { groupId, cfg.Id }); });
        //         else
        //             AdMgr.I.ShowAd(AdTimingConst.SHOP, this.OnAdCallBack, null, groupId, cfg.Id);
        //         //AdTimingMgr.I.Play(AdTimingConst.SHOP, OnAdCallBack, null, groupId, cfg.Id);
        //     });
        // }

        /// <summary>
        /// 广告获得资源
        /// </summary>
        /// <param name="param"></param>
        // public void OnAdCallBack(object[] param)
        // {
        //     if (param == null || param.Length == 0)
        //     {
        //         FloatTips.I.FloatMsg(" ItemStoreMgr error param is null");
        //         AdMgr.I.AdBI("Ad CallBack", new string[] { "itemStoreMgr Param is null" });
        //         return;
        //     }

        //     //var groupId = (int)param[0];
        //     var adShopId = (int)param[1];
        //     AdShopMgr.I.BuyAdItem(adShopId);

        //     PopupManager.I.ClosePopup<UIComplementPop>();
        // }

        private void ShowToMainUIWorldGuid()
        {
            EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, MainMenuType.CITY);

            var UIMain2 = SceneManager.I.FindPopupFromPool(typeof(UIMain2));
            if (UIMain2 != null)
            {
                var guidData = new UIGuidData();
                guidData.guidItems.Add(new UIGuidItem() { UIName = "UI.UIMain2", UIItem = "content/MenuRoot/Menu/World", slide = true });
                UIGuid.StartGuid(guidData, true);
            }
        }

        /// <summary>
        /// 搜寻野怪
        /// </summary>
        public async UniTaskVoid SendSearchEnemy(CD2Skill skillCfg)
        {

            var worldOpen = (int)MetaConfig.NewUnlockWorldBtn;

            bool isUnlock = LPlayer.I.GetMainCityLevel() >= worldOpen;
            if (!isUnlock)
            {
                UITools.PopTips(LocalizationMgr.Format("Alliance_Create_lvneed", worldOpen));
                return;
            }

            var times = PlayerTriggerPrefMgr.I.Get(PlayerTriggerPrefMgr.ClickSearchTimesSaveKey);
            //var limit = PlayerTriggerPrefMgr.ClickSearchLimit;
            var limit = (int)MetaConfig.NewFunctionGuidelines;

            if (times < limit)
            {

                PlayerTriggerPrefMgr.I.Save(PlayerTriggerPrefMgr.ClickSearchTimesSaveKey, times + 1);
                //Guide.GuideInstance.IsTriggering = true;
                //Guide.GuideInstance.TriggeringRefIdx = GetSkillTypeRefIdx(skillCfg);
                //EventMgr.FireEvent(TEventType.AssetPopToMainUIWorld);
                this.CloseUI(() =>
                {
                    this.ShowToMainUIWorldGuid();
                });

            }
            else
            {
                if (LSearch.I.CurrItem == null)
                    return;
                Cfg.C.CD2NpcTroopClass.GetConfigAsync(LSearch.I.CurrItem.SearchId).ContinueWith(npcConfig =>
                {
                    if (npcConfig == null)
                        return;
                    var level = LPlayer.I.GetKillOrderData(npcConfig.Id);
                    var maxLv = level > 0 ? level + 1 : 1;

                    this.CloseUI(() =>
                    {
                        EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, MainMenuType.WORLD);
                        LSearch.I.NeedResearch = true;
                        var refIdx = GetSkillTypeRefIdx(skillCfg);
                        LSearch.I.Choose = refIdx;
                        LPlayer.I.SearchEnmyId = refIdx;
                        LSearch.I.SearchLevel = maxLv;
                        LSearch.I.TrySearch();
                    });
                });
            }
        }

        private void CloseUI(Action onCompleteCallback, bool includeActivity = true)
        {
            var uiList = new List<Type>()
            {
                //typeof(UIEvo),
                typeof(UIComplementPop),
                //typeof(UIEvoDetail),
                typeof(UICityLevelUp), 
                //typeof(UIHeroRecruit),
                typeof(UIHeroList),
                //typeof(UIHeroListOld),
                typeof(UITechDetail),
                typeof(UITech),
                typeof(UIDragon),
                typeof(UIAllianceMobilize),
                typeof(UIShop),
                typeof(UIAllianceTechDonate),
                typeof(UIAllianceSkillNew),
                typeof(UIAllianceShop),
                typeof(UIOtherPlayerData),
                typeof(UIAllianceOtherInfo),
                typeof(UIAllianceMessage),
                //typeof(UITalent),
                //typeof(UITalentDetail),
                typeof(UIVipNew),
                typeof(UIVipStore),
                typeof(KVKTech),
                typeof(UITechDetailKVK),
            };

            if (includeActivity)
                uiList.Add(typeof(UIActivityMain));

            PopupManager.I.ClosePopupList(uiList);

            //WndMgr.HideAll();
            //DeepUI.PopupManager.I.ClearAllLayer(_ => onCompleteCallback());
            // TODO不需要关闭所有panel，如果有特殊界面请单独关闭
            if (onCompleteCallback != null)
                onCompleteCallback();
        }

        public void SendSearchEnemy(CItem itemCfg, int searchEnemyIndex = 0)
        {

            var worldOpen = (int)MetaConfig.NewUnlockWorldBtn;

            bool isUnlock = LPlayer.I.GetMainCityLevel() >= worldOpen;
            if (!isUnlock)
            {
                UITools.PopTips(LocalizationMgr.Format("Alliance_Create_lvneed", worldOpen));
                return;
            }

            var times = PlayerTriggerPrefMgr.I.Get(PlayerTriggerPrefMgr.ClickSearchTimesSaveKey);
            //var limit = PlayerTriggerPrefMgr.ClickSearchLimit;
            var limit = (int)MetaConfig.NewFunctionGuidelines;

            this.CloseUI(() =>
            {
                if (times < limit)
                {

                    PlayerTriggerPrefMgr.I.Save(PlayerTriggerPrefMgr.ClickSearchTimesSaveKey, times + 1);
                    //Guide.GuideInstance.TriggeringRefIdx = 0;
                    //Guide.GuideInstance.IsTriggering = true;
                    //D.Debug?.Log("aaaaaaaaaaaaaaaaaa Guide.GuideInstance.TriggeringRefIdx" + Guide.GuideInstance.TriggeringRefIdx);
                    this.ShowToMainUIWorldGuid();
                }
                else
                {
                    LSearch.I.Choose = searchEnemyIndex;

                    if (LSearch.I.CurrItem == null)
                        return;

                    Cfg.C.CD2NpcTroopClass.GetConfigAsync(LSearch.I.CurrItem.SearchId).ContinueWith(npcConfig =>
                    {
                        if (npcConfig == null)
                            return;

                        //UIMain.I.TrySwitch(MainMenuConst.WORLD);
                        EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, MainMenuType.WORLD);

                        var level = LPlayer.I.GetKillOrderData(npcConfig.Id);
                        var maxLv = level > 0 ? level + 1 : 1;

                        LSearch.I.NeedResearch = true;
                        var refIdx = searchEnemyIndex;
                        LSearch.I.Choose = refIdx;
                        LPlayer.I.SearchEnmyId = refIdx;
                        LSearch.I.SearchLevel = maxLv;
                        LSearch.I.TrySearch();
                    });
                }
            });

        }

        public void SendSearchAssembled(CItem itemCfg)
        {
            var rallyOpen = (int)MetaConfig.RallyBtnUnlockStage;
            var isUnlock = true;// GameData.I.LevelData.CanToDoByGameLevel(rallyOpen);
            if (!isUnlock)
            {
                UITools.PopTips(LocalizationMgr.Format("DEMO_37", rallyOpen + 1));
                return;
            }

            this.CloseUI(() =>
            {
                if (LPlayer.I.IsPlayerInUnion())
                {
                    TaskUtils.JumpToWorld(2);
                }
                else
                {
                    //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.ALLIANCE);
                    //UIMain.I?.TrySwitch(MainMenuConst.ALLIANCE);
                }
            });
        }


        public void SendSearchCollect(CItem itemCfg)
        {
            var rallyOpen = (int)MetaConfig.RallyBtnUnlockStage;
            var isUnlock = true;// GameData.I.LevelData.CanToDoByGameLevel(rallyOpen);
            if (!isUnlock)
            {
                UITools.PopTips(LocalizationMgr.Format("DEMO_37", rallyOpen + 1));
                return;
            }

            this.CloseUI(() =>
            {
                TaskUtils.JumpToWorld(1);
            });
        }


        /// <summary>
        /// 获取技能对应的兵种搜索类型
        /// </summary>
        public static int GetSkillTypeRefIdx(CD2Skill skillCfg)
        {
            if (skillCfg != null)
            {
                var type = skillCfg.Category;
                switch ((SkillTypeEnum)type)
                {
                    case SkillTypeEnum.ARCHER:
                        return 0;
                    case SkillTypeEnum.FIRE_MAGE:
                        return 1;
                    case SkillTypeEnum.ICE_MAGE:
                        return 2;
                    case SkillTypeEnum.TOX:
                        return 3;
                }
            }

            D.Warning?.Log("skill cfg is null!!!!!");
            return -1;
        }

        #endregion

    }

    public class ItemStoreGiftAutoPopup
    {
        private Dictionary<int, long> mPopupHistory = new Dictionary<int, long>();
        private GiftItemData mPrepareGift = null;
        private string mLocalKey
        {
            get
            {
                return "ItemStoreGiftAutoPopup_" + Logic.LPlayer.I.PlayerID;
            }
        }

        private int mTimerId = -1;

        public ItemStoreGiftAutoPopup()
        {
            InitHistory();
        }

        public void SetAutoPopupGift(GiftItemData gift)
        {
            if (null == gift)
            {
                return;
            }

            // if (GameData.I.GiftData.CheckGiftLimitBuy(gift.CfgId))
            // {
            //     return;
            // }

            var now = GameTime.Time;
            mPopupHistory.TryGetValue(gift.CfgId, out var lastTime);

            if (now - lastTime < MetaConfig.Gift_Pack * 1000)
            {
                D.Warning?.Log("礼包cd中: " + gift.CfgId);
                return;
            }

            mPrepareGift = gift;

            StartTimer();
        }

        private void StartTimer()
        {
            if (mTimerId > 0)
            {
                NTimer.Destroy(mTimerId);
            }

            mTimerId = NTimer.Tick(NTimer.INF, 0.5f, () =>
            {
                if (PopupManager.I.AnyPopup)
                {
                    return;
                }

                if (WndMgr.HasUIShowed(true))
                {
                    return;
                }

                TryAutoPopup();
            });
        }

        public void TryAutoPopup()
        {
            if (UIGuid.HaveWakeGuid())
            {
                //D.Info?.Log($"In guid");
                return;//引导中 不进行弹窗礼包
            }

            if (null == mPrepareGift)
            {
                //D.Info?.Log($"check gift popup 2");
                return;
            }

            if (GameData.I.GiftData.CheckGiftLimitBuy(mPrepareGift.CfgId))
            {
                //D.Info?.Log($"check gift popup 3");
                return;
            }

            var anyCanPurchase = ShopTabConfig.I.triggerEntranceData?.anyCanPurchase;
            if (!anyCanPurchase.HasValue || !anyCanPurchase.Value)
            {
                //D.Info?.Log($"check gift popup 4");
                return;
            }

            // if (ShopTabConfig.I.triggerEntranceData == null || !ShopTabConfig.I.triggerEntranceData.anyCanPurchase)
            // {
            //     return;
            // }

            mPopupHistory[mPrepareGift.CfgId] = GameTime.Time;

            //PopupManager.I.ShowPanel<UIGiftAutoPop>(mPrepareGift);
            //弹出用最新的，不用老的
            TriggerGiftUI.Layer.OpenSafely();
            SaveHistory();
            mPrepareGift = null;

            if (mTimerId > 0)
            {
                NTimer.Destroy(mTimerId);
            }
        }

        private void InitHistory()
        {
            var history = PlayerPrefs.GetString(mLocalKey);

            if (string.IsNullOrEmpty(history))
            {
                return;
            }

            var historyList = history.Split("|");

            foreach (var info in historyList)
            {
                if (string.IsNullOrEmpty(info))
                {
                    continue;
                }

                var infoList = info.Split(";");

                if (infoList.Length != 2)
                {
                    continue;
                }

                mPopupHistory[int.Parse(infoList[0])] = long.Parse(infoList[1]);
            }
        }

        private void SaveHistory()
        {
            var history = "";

            foreach (var kv in mPopupHistory)
            {
                history += kv.Key + ";" + kv.Value + "|";
            }

            PlayerPrefs.SetString(mLocalKey, history);
            PlayerPrefs.Save();
        }

        public void DeleteHistory()
        {
            mPopupHistory.Clear();

            PlayerPrefs.SetString(mLocalKey, "");
            PlayerPrefs.Save();
        }
    }

    public class ItemStoreData
    {
        public int id;
        public CItemStore cfg;
        public ItemData itemData;
        public VMData cost;
        public ItemData costItem; //大概率不会使用，因为陈杰reward统一后没准会出现这种配置

        /// <summary>
        /// false理论上不在道具商城中出现, 后续需求TODO
        /// 该值目前的需求，一定是true
        /// </summary>
        public bool isShow;

        /// <summary>
        /// 一共购买数量
        /// </summary>
        public int times;

        public ItemStoreData(ItemStoreItem serverData, Cfg.G.CItemStore cfg, List<TypIDVal> costRewards, List<TypIDVal> goodRewards)
        {

        }

        public static async UniTask<ItemStoreData> CreateAsync(ItemStoreItem serverData, Cfg.G.CItemStore cfg, List<TypIDVal> costRewards, List<TypIDVal> goodRewards)
        {
            var instance = new ItemStoreData(serverData, cfg, costRewards, goodRewards);
            await instance.InitializeAsync(serverData, cfg, costRewards, goodRewards);
            return instance;
        }

        private async UniTask InitializeAsync(ItemStoreItem serverData, Cfg.G.CItemStore cfg, List<TypIDVal> costRewards, List<TypIDVal> goodRewards)
        {
            this.id = cfg.Id;
            this.cfg = cfg;

            if (this.cfg != null)
            {
                this.isShow = this.cfg.Show == 1;
                if (costRewards.Count > 0)
                {
                    var costReward = costRewards[0];
                    if (costReward.typ == "item")
                    {
                        this.costItem = new ItemData(costReward.ID, costReward.val);
                    }
                    else if (costReward.typ == "vm")
                    {
                        this.cost = await VMData.CreateAsync(costReward.ID, costReward.val);
                    }
                }

                if (goodRewards != null && goodRewards.Count > 0)
                {
                    var goodReward = goodRewards[0];
                    if (goodReward.typ.Equals("item"))
                    {
                        this.itemData = new ItemData(goodReward.ID, goodReward.val);
                    }
                }
            }

            this.times = serverData.times;
        }
    }
}
