﻿using System.Collections.Generic;
using System.Linq;
using Common;
using Cysharp.Threading.Tasks;
using Game.Config;
using Game.Data;
using K3;
using Logic;
using TFW;
using TFW.UI;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.Serialization;

namespace UI
{
    public class ActivityEntranceButton : MonoBehaviour
    {
        [SerializeField]
        private EventTriggerListener _button;

        [SerializeField]
        private SimpleCondition _simpleCondition;

        [FormerlySerializedAs("_activityEntranceButtonHandler")]
        [SerializeField]
        ActivityEntranceButtonHandlerBase _handler;

        private RedWidget activityRedWidget;
        
        private TFWImage giftIcon;
        private TFWText giftText;
        void RefreshOpenState()
        {
            if (!ActivityMgr.I.isValid)
            {
                this._simpleCondition.TrySetActive(false);
                return;
            }

            var isOpen = UnlockMgr.I.CheckUnlock(UnlockFuncEnum.ActivityFunc);
            var actvs = this._handler.GetActivities();
            this._simpleCondition.TrySetActive(isOpen && actvs.Any());

            UpdateActivityRedPoint(null);
            SetGiftIcon();
            SetGiftName();
        }

        private async UniTaskVoid UpdateActivityRedPoint(object[] objs)
        {
            if (this.activityRedWidget != null)
            {
                var redNum = await this._handler.GetRedNum();
                this.activityRedWidget.SetData(redNum);
            }
        }

        private void SetGiftIcon()
        {
            var iconIndex = this._handler.GetGiftIconName();
            if (iconIndex == -1) return;
            if (giftIcon == null) return;
             
            UITools.SetImage(giftIcon, iconIndex, "Activity", false); 
        }

        private void SetGiftName()
        {
            var giftName = this._handler.GetGiftName();
            if (giftText == null) return;
            giftText.text = giftName;
            
        }
        
        private void OnClick(GameObject arg0, PointerEventData arg1)
        {
            this._handler.Click();
        }

        private void Awake()
        {
            var red = this.transform.Find("Root/Img2/Red");
            if (red) this.activityRedWidget = new RedWidget(red.gameObject);
            this._button.onClick.AddListener(this.OnClick);
            giftIcon = this.transform.Find("Root/Img2/Card").GetComponent<TFWImage>();
            giftText = Public.UIHelper.GetComponent<TFWText>(gameObject, "Root/TextMask/Text");
            
            EventMgr.RegisterEvent(TEventType.OnUnlockConditionChanged, args => { this.RefreshOpenState(); }, this);
            EventMgr.RegisterEvent(TEventType.ActvListNtf, args => { this.RefreshOpenState(); }, this);
            EventMgr.RegisterEvent(TEventType.RefreshActvRedPoint, this.UpdateActivityRedPoint, this);
        }

        private void OnEnable()
        {
            this.RefreshOpenState();
        }

        private void OnDestroy()
        {
            this._button.onClick.RemoveListener(this.OnClick);

            EventMgr.UnregisterEvent(TEventType.GameLevelChange, this);
            EventMgr.UnregisterEvent(TEventType.MainCitySkillLevelUp, this);
            EventMgr.UnregisterEvent(TEventType.Update_GameLevel_Info, this);
            EventMgr.UnregisterEvent(TEventType.ActvListNtf, this);
            EventMgr.UnregisterEvent(TEventType.RefreshActvRedPoint, this);
        }
    }
}