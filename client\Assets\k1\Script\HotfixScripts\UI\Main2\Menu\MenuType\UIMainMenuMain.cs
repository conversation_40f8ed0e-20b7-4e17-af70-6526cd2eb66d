﻿

//using System.Collections.Generic;
//using Common;
//using Cysharp.Threading.Tasks;
//using DeepUI;
//using Game.Config;
//using Game.Sprite.Fight;
//using K1;
//using K3;
//using Logic;
//using Public;
//using Render;
//using TFW;
//using TFW.UI;
//using UnityEngine;
//using UnityEngine.UI;

//namespace UI
//{
//    public class UIMainMenuMain : MonoBehaviour
//    {

         

//        public GameObject levelRedWidget;


         
 

//        /// <summary>
//        /// 事件注册
//        /// </summary>
//        protected override void RegisterEvents()
//        {
//            base.RegisterEvents();

//            //EventMgr.RegisterEvent(TEventType.UIMain_Update_GameEnd_State, OnUpdateGameEndState, this);
//            //EventMgr.RegisterEvent(TEventType.GoldChange, OnGoldChange, this);

//            //EventMgr.RegisterEvent(TEventType.RefreshAssetAck, EnergyRefresh, this);
//            //EventMgr.RegisterEvent(TEventType.ChapterQuestInfo, RefreshMenuImg, this);
//            //EventMgr.RegisterEvent(TEventType.ChapterQuestReward, RefreshMenuImg, this);
//            //EventMgr.RegisterEvent(TEventType.ChapterReward, RefreshMenuImg, this);
//            //EventMgr.RegisterEvent(TEventType.ChapterQuestStateChange, RefreshMenuImg, this);
//            //EventMgr.RegisterEvent(TEventType.DragonLevelUp, RefreshMenuImg, this);
//            //EventMgr.RegisterEvent(TEventType.DragonDataRefresh, RefreshMenuImg, this);
//        }

//        protected override void UnRegisterEvents()
//        {
//            base.UnRegisterEvents();

//            EventMgr.UnregisterEvent(this);
//        }


//        #endregion


//        #region 数据刷新

//        /// <summary>
//        /// 刷新内城信息显示
//        /// </summary>
//        private bool UpdateCityInfo()
//        {
//            //if (!GuideMgr.I.IsGuideDone)
//            //    return false;

//            //在时间限制范围内，那么就不处理
//            //if (isCanClickChangeMap)
//            //    return false;

//            ////开始时间限制计时
//            //isCanClickChangeMap = true;
//            //NTimer.CountDown(CHANGE_MAP_TIME_LIMIT, () => { isCanClickChangeMap = false; });

//            //播放声音
//            //GameAudio.PlayAudio(AudioConst.CityChange);

//            WorldSwitchMgr.I.ShowWorldType = WorldTypeEnum.CITY;
//            //由于大地图切换缩放按钮 对其进行了隐藏处理，此时直接切换到内城 没有恢复显示 所以在此处显示下
//            if (Main != null)
//            {
//                //按钮显示
//                //(Main as UIMain1)?.UpdateMenuActive(true);
//                (Main as UIMain2)?.UpdateMenuActive(true);

//                //监测迁城提示
//                //(Main as UIMain1)?.CheckIsShowAllianceMoveCityTip();
//                (Main as UIMain2)?.CheckIsShowAllianceMoveCityTip();
//            }

//            //if (!isGameEnd)
//            //    return true;

//            ////为了避免玩家点击切换速度 > 协议接收速度
//            ////但如果出现断线，会出现不出兵的问题
//            //isGameEnd = false;

//            //开始关卡战斗
//            //FightManager.I.StartLevelFight();

//            //RefreshMenuImg();

//            return true;
//        }

//        //private void RefreshMenuImg(object[] _ = null)
//        //{
//        //    //var tfwImg = cityIcon.GetComponent<TFWImage>();

//        //    //if (null == tfwImg)
//        //    //{
//        //    //    return;
//        //    //}

//        //    //UITools.SetImageBySpriteName(tfwImg, "UI_Main_Buttom_Btn_Hero_440", true);

//        //    //var chapterReward = ChapterTaskMgr.I.GetCurrentChapterConfig();

//        //    //if (null == chapterReward || chapterReward.Hero == 0)
//        //    //{
//        //    //    return;
//        //    //}

//        //    //UITools.SetImageBySpriteName(tfwImg, "UI_Main_Buttom_Btn_Hero_" + chapterReward.Hero, true);
//        //}

//        /// <summary>
//        /// 退出菜单栏界面
//        /// </summary>
//        /// <param name="nextType"></param>
//        public override void ExitMenuPanel(MainMenuType nextType)
//        {
//            base.ExitMenuPanel(nextType);

//            //RefreshMenuImg();

//            //mergeIcon.SetActive(false);
//            //cityIcon.SetActive(true);

//            if (nextType != MainMenuType.WORLD)
//            {
//                WndMgr.Hide<UIUnionInviteTips>();
//            }




//            //if (nextType != MainMenuType.ALLIANCE)
//            //{

//            //    PopupManager.I.ClosePopup<UIPlayerReName>();
//            //}

//            // PopupManager.I.ClosePopup<UIMerage>();
//            PopupManager.I.ClosePopup<UIPlayerCityBuff>();
//        }


//        #region 训练金币数据刷新






//        #endregion

//        #endregion

//        #region 声音播放

//        /// <summary>
//        /// 刷新声音播放
//        /// </summary>
//        private void UpdateAuido()
//        {
//            AudioManager.Instance.StopOtherAudioChannel(AudioConst.AUDIO_CHANNEL_STOP,
//                            AudioChannelType.UI, AudioChannelType.CITY, AudioChannelType.BGM, AudioChannelType.GuidHeroAudio);

//            //BgmManager.PlayBgm(AudioConst.Merge_BGM);
//            GameAudio.PlayBGM(AudioConst.Merge_BGM);
//        }


//        #endregion


//        #region 事件监听

//        /// <summary>
//        /// 刷新关卡游戏结束状态
//        /// </summary>
//        /// <param name="objs"></param>
//        //private void OnUpdateGameEndState(object[] objs)
//        //{
//        //    if (objs == null || objs.Length == 0)
//        //        return;

//        //    isGameEnd = (bool)objs[0];
//        //}




//        //private void EnergyRefresh(object[] objs)
//        //{
//        //    if (energySlider)
//        //    {
//        //        energySlider.minValue = 0;
//        //        energySlider.maxValue = K3PlayerMgr.I.PlayerData?.maxEnergy ?? 1;

//        //        energySlider.value = K3PlayerMgr.I.PlayerData?.energy ?? 0;
//        //    }

//        //    if (energyNum)
//        //    {
//        //        energyNum.text = (K3PlayerMgr.I.PlayerData?.energy ?? 0).ToString();
//        //    }
//        //}

//        /// <summary>
//        /// 重复点击当前菜单按钮
//        /// </summary>
//        public override void ClickCurrMenuBtn(UIData data = null)
//        {
//            base.ClickCurrMenuBtn(data);


//        }



//        public override void EnterMenuPanel()
//        {
//            base.EnterMenuPanel();

//            //mergeIcon.SetActive(true);
//            //cityIcon.SetActive(false);

//            UpdateAuido();


//            //EnergyRefresh(null);
//            //WndMgr.Show<K3.UIMergePhoto>();
//        }

//        /// <summary>
//        /// 点击菜单按钮
//        /// </summary>
//        public override async UniTask<bool> ClickMenuBtn(UIData data = null)
//        {
//            await base.ClickMenuBtn(data);

//            //刷新声音播放
//            //UpdateAuido();

//            //刷新城市信息显示
//            var ok = UpdateCityInfo();

//            //播放Sprite特效
//            //SpriteDisplayManager.I.PlayEvoUpEffect();

//            //if (Main != null)
//            //{
//            //    //(Main as UIMain1)?.UpdateChatVisable(true);
//            //    (Main as UIMain2)?.UpdateChatVisable(true);
//            //}

//            //设置是否监听拖动事件
//            //MapManager.I.CanDragOnMap(true);


//            return ok;
//        }




//        #endregion


//        ///// <summary>
//        ///// 刷新关卡红点信息
//        ///// </summary>
//        //public void UpdateLevelRedInfo()
//        //{
//        //    var redNum = K3PlayerMgr.I.PlayerData?.energy ?? 0;
//        //    if (levelRedWidget != null)
//        //        levelRedWidget.SetActive(redNum > 0);
//        //}
//    }
//}
