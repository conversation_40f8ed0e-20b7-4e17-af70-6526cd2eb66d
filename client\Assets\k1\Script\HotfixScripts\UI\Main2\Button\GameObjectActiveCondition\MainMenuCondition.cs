﻿using Common;
using Game.Data;
using UnityEngine;

namespace UI
{
    [AddComponentMenu(MenuPath + nameof(MainMenuCondition))]
    public class MainMenuCondition : GameObjectActiveCondition
    {
        [Header("在主界面的那些场景中显示此按钮")]
        [SerializeField]
        MainMenuType[] _shouldShowMenus;

        void Refresh()
        {
            if (_shouldShowMenus != null && _shouldShowMenus.Length > 0)
            {
                var showType = GameData.I.MainData.CurrMenuType;
                //if (showType == MainMenuType.NONE)
                //    return;

                var shouldShow = false;
                for (int i = 0; i < _shouldShowMenus.Length; i++)
                {
                    if (showType == _shouldShowMenus[i])
                    {
                        shouldShow = true;
                        break;
                    }
                }
                this.TrySetActiveInternal(shouldShow);
            }
        }

        private void Start()
        {
            EventMgr.RegisterEvent(TEventType.OnSwitchMainUI, (args) => { this.Refresh(); }, this);
            this.Refresh();
        }

        private void OnDestroy()
        {
            EventMgr.UnregisterEvent(TEventType.OnSwitchMainUI, this);
        }
    }
}