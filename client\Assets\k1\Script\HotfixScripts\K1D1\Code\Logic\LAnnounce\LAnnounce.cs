﻿using Common;
using Config;
using cspb;
using Game.Config;
using Game.Data;
using Game.Map;
using Game.Utils;
using k1;
using TFW;
using TFW.Localization;
using THelper;
using UI;
using UnityEngine; using DeepUI;
using System;
using System.Collections.Generic;

namespace Logic
{
    /// <summary>
    /// 公告相关
    /// <AUTHOR>
    /// @date 2023/4/11 18:37:18
    /// @ver 1.0
    /// </summary>
    public class LAnnounce : Ins<LAnnounce>
    {

        public List<AnnounceInfo> AnnounceListData = new List<AnnounceInfo>();
        public Dictionary<long,bool> openStatus;
        public bool isInitReqData = false;
        /// <summary>
        /// 初始化信息数据
        /// </summary>
        public void Init()
        {
            isInitReqData = false;
            MessageMgr.RegisterMsg<AnnounceNtf>(this, OnAnnounceNtf);
            MessageMgr.RegisterMsg<AnnounceListAck>(this, OnAnnounceListAck);
            EventMgr.RegisterEvent(TEventType.OnSwitchMainUI, (a) =>
            {
                if (a?.Length > 0)
                {
                    //var type = (MainMenuType)a[0];
                    //if (type == MainMenuType.PRISON)
                    {
                        this.ReqAnnounceList(); 
                    }
                }

            }, this);
            openStatus = new Dictionary<long, bool>();
            //ReqAnnounceList();
        }

        public bool CanPop()
        {
            if(AnnounceListData!= null && LAnnounce.I.AnnounceListData.Count > 0)
            {
                var popCount = PlayerPrefs.GetInt("AnnounceMaxCount" + AnnounceListData[0].id.ToString(), 0);
                if (popCount >= AnnounceListData[0].maxPop)
                    return false;
                PlayerPrefs.SetInt("AnnounceMaxCount" + AnnounceListData[0].id.ToString(), popCount + 1);
                return true;
            }
            return false;
        }
        private void OnAnnounceListAck(AnnounceListAck obj)
        {
            AnnounceListData.Clear();
            AnnounceListData.AddRange(obj.infos);
            AnnounceListData.Sort(SortData);
            openStatus.Clear();
            for (int i = 0; i < AnnounceListData.Count; i++)
            {
                openStatus.Add(AnnounceListData[i].id, false);
                //DownLoadRemoteImgMgr.I.DownSimpleImg(AnnounceListData[i].bannerUrl);
            }

        }
       
        public bool HasRead(long id)
        {
            var status = PlayerPrefs.GetInt("Announce" + id.ToString(), 0);
            return status > 0 ;
        }
        public int RedPointCount
        {
            get
            {
                if (AnnounceListData == null)
                    return 0;
                int count = 0;
                for (int i = 0; i < AnnounceListData.Count; i++)
                {
                    if (HasRead(AnnounceListData[i].id))
                        continue;
                    else
                        count += 1;
                }
                return count;
            }
        }
        public bool HasAllRead()
        {
           
            if (AnnounceListData == null)
                return true;
            for(int i = 0;i< AnnounceListData.Count;i++)
            {
                if (HasRead(AnnounceListData[i].id))
                    continue;
                else
                    return false;
            }
            return true;
        }
        private int SortData(AnnounceInfo x, AnnounceInfo y)
        {
            if (x == null || y == null)
                return 0;
            if (x.priority > y.priority)
                return -1;
            else if(x.priority == y.priority)
            {
                if(x.startTime > y.startTime)
                {
                    return -1;
                }
                else if(x.startTime == y.startTime)
                {
                    return 0;
                }else
                {
                    return 1;
                }

            }
            else 
            {
                return 1;
            }
        }

        private void OnAnnounceNtf(AnnounceNtf obj)
        {
            if(obj.act == AnnounceAction.AnnounceActionAdd)
            {
                openStatus.Add(obj.info.id, false);
                AnnounceListData.Add(obj.info);
            }
            else if(obj.act == AnnounceAction.AnnounceActionDel)
            {
                for (int i = 0; i < AnnounceListData.Count; i++)
                {
                    if (obj.info.id == AnnounceListData[i].id)
                    {
                        AnnounceListData.Remove(AnnounceListData[i]);
                        openStatus.Remove(obj.info.id);
                    }
                        
                }
            }else if(obj.act == AnnounceAction.AnnounceActionMod)
            {
                for (int i = 0; i < AnnounceListData.Count; i++)
                {
                    if (obj.info.id == AnnounceListData[i].id)
                        AnnounceListData[i] = obj.info;
                }
            }
            AnnounceListData.Sort(SortData);
            EventMgr.FireEvent(TEventType.AnnounceRefresh);
        }
        public void ReqAnnounceList()
        {
            if (!isInitReqData)
            {
                isInitReqData = true;
                AnnounceListReq req = new AnnounceListReq();
                MessageMgr.Send(req);
            }
        }
        /// <summary>
        /// 反向初始化
        /// </summary>
        public void DeInit()
        {
            isInitReqData = false;
            AnnounceListData?.Clear();
            MessageMgr.UnregisterMsg(this);
            EventMgr.UnregisterEvent(this);
        }
    }
}
