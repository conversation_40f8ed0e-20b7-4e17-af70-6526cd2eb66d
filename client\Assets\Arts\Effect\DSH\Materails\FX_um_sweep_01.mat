%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: FX_um_sweep_01
  m_Shader: {fileID: 4800000, guid: 910d9de8e81ef5a4a80b23225396c272, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _MASKAISLE_R
  m_InvalidKeywords:
  - _MASKAISLE1_R
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DissloveTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex1:
        m_Texture: {fileID: 2800000, guid: 040a99cee618abb4197cf6abce997276, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MaskTex:
        m_Texture: {fileID: 2800000, guid: 894dcd86c08e7f847ac421606ff55b9b, type: 3}
        m_Scale: {x: 1, y: 8}
        m_Offset: {x: 0, y: 0}
    - _MaskTex1:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NoiseTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 0.1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _BlendMode: 1
    - _BumpScale: 1
    - _ColorMask: 15
    - _CullMode: 2
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _DissloveIntensity: 0
    - _DstBlend: 0
    - _GlossMapScale: 1
    - _Glossiness: 0.5
    - _GlossyReflections: 1
    - _MaintexDesaturate: 0
    - _MaintexDesaturate1: 0
    - _MaskAisle: 0
    - _MaskAisle1: 0
    - _Metallic: 0
    - _Mode: 0
    - _NoiseIntensity: 0
    - _OcclusionStrength: 1
    - _Parallax: 0.02
    - _SlofDisslove: 0
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _Stencil: 0
    - _StencilComp: 8
    - _StencilOp: 0
    - _StencilReadMask: 255
    - _StencilWriteMask: 255
    - _TexRotator: 0
    - _TexRotator1: 0
    - _UVSec: 0
    - _U_Speed_MainTex: 0
    - _U_Speed_MainTex1: -1
    - _U_Speed_NoiseTex: 0
    - _UseUIAlphaClip: 0
    - _V_Speed_MainTex: 0
    - _V_Speed_MainTex1: 0
    - _V_Speed_NoiseTex: 0
    - _ZWrite: 1
    m_Colors:
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _MainColor: {r: 1.4980392, g: 1.4980392, b: 1.4980392, a: 1}
    - _MainColor1: {r: 1, g: 1, b: 1, a: 1}
  m_BuildTextureStacks: []
