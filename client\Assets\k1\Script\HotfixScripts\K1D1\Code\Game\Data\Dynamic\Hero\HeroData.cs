﻿ 
using Cfg.G;
using cspb;
using Cysharp.Threading.Tasks;
using Game.Config;
using Game.Utils;
using K3;
using Logic;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Game.Data
{


    public enum HeroSortType
    {
        /// <summary>
        /// 品质
        /// </summary>
        QUALITY = 1,

        /// <summary>
        /// 等级
        /// </summary>
        LEVEL = 2,

        /// <summary>
        /// 战力
        /// </summary>
        COMBATFORCES = 3
    }

    /// <summary>
    /// 英雄数据结构
    /// </summary>
    public class HeroData : IComparer<HeroData>
    {
        /// <summary>
        /// 英雄唯一ID
        /// </summary>
        private long _heroId;

        /// <summary>
        /// 英雄模板ID
        /// </summary>
        private int _modelId;
        /// <summary>
        /// 英雄的星级
        /// </summary>
        private int _star;

        private int _starStep;
        /// <summary>
        /// 英雄好感度
        /// </summary>
        private int _goodWill;

        /// <summary>
        /// 英雄等级
        /// </summary>
        private int _level;

        /// <summary>
        /// 当前英雄经验
        /// </summary>
        private long _exp;

        public bool IsUseDefaultAsset = false;

        /// <summary>
        /// 英雄的数量
        /// </summary>
        private List<long> _heroIds = new List<long>();
        /// <summary>
        /// 这个英雄是否被锁住
        /// </summary>
        private bool _isLocked;

        /// <summary>
        /// 是否上阵
        /// </summary>
        private HeroStatus _isBattle;

        /// <summary>
        /// 英雄Config
        /// </summary>
        private CD2Hero _heroCfg;

        /// <summary>
        /// 英雄Config属性
        /// </summary>
        private CD2HeroAttribute _heroAttribute;

        /// <summary>
        /// 英雄Config经验
        /// </summary>
        private CD2HeroExp _heroExp;

        public long HeroId { get => _heroId; set => _heroId = value; }
        public int ModelId { get => _modelId; set => _modelId = value; }
        public int Star { get => _star; set => _star = value; }

        public int StarStep { get => _starStep; set => _starStep = value; }
        //public int GoodWill
        //{
        //    get
        //    {
        //        if (HeroServerData != null)
        //        {
        //            return HeroServerData.goodwill;
        //        }
        //        else
        //        {
        //            return 0;
        //        }
        //    }
        //    set
        //    {
        //        _goodWill = value;
        //    }
        //}
        public int Level { get => _level; set => _level = value; }

        public int MergeItemLevel 
        {
            get 
            {
                int intimacyCfgLv=(GetIntimacyCfg()?.MergeLevel ?? 0);
                int starLv = StarMergeItemLevel();

                var addLv = GameData.I.PlayerData.PropData.GetPropInfo(PropTypeEnum.MergeItemLvAdd, 0);

                return HeroAttribute.MergeItemLevel + starLv + intimacyCfgLv+ (int)addLv;
            }
        }

        public int StarMergeItemLevel()
        {
            if (StarStep > 0 || Star > 0)
            {
                if (StarStep == 0)
                {
                    if (HeroCfg.StarStageItemLv.Count > Star - 1)
                    {
                        var steps = HeroCfg.StarStageItemLv[Star - 1].Split(',');
                        return int.Parse(steps[steps.Length - 1]);
                    }
                }
                else
                {
                    if (HeroCfg.StarStageItemLv.Count > Star)
                    {
                        var steps = HeroCfg.StarStageItemLv[Star].Split(',');
                        return int.Parse(steps[StarStep - 1]);
                    }
                }
            }

            return 0;
        }

        public long Exp { get => _exp; set => _exp = value; }
        public List<long> HeroIDs { get => _heroIds; set => _heroIds = value; }
        public bool Locked { get => _isLocked; set => _isLocked = value; }
        public HeroStatus IsBattle
        {
            get
            {
                foreach (var lineUp in GameData.I.LineUpData.LineUpDataList)
                {
                    if (lineUp != null)
                    {
                        var heroes = GameData.I.LineUpData.GetLineUpHeros(lineUp);
                        for (int j = 0; j < heroes.Count; j++)
                        {
                            if (heroes[j]?.HeroId == this.HeroId)
                            {
                                return (cspb.HeroStatus)lineUp.state;
                            }
                        }
                    }
                }

                return HeroStatus.HeroStatusIdle;
            }

        }

        public CD2Hero HeroCfg { get => _heroCfg; set => _heroCfg = value; }
        public CD2HeroAttribute HeroAttribute
        {
            get
            {
                if (_heroAttribute == null)
                    GetHeroAttribute();
                return _heroAttribute;
            }
            set
            {
                _heroAttribute = value;
            }
        }
        public CD2HeroExp HeroExp { get => _heroExp; set => _heroExp = value; }

        public cspb.HeroData HeroServerData { private set; get; } = null;

        #region 技能相关
        public class HeroSkillData
        {

            public enum SkillType
            {
                /// <summary>
                /// 战斗被动
                /// </summary>
                HeroPassive,
                /// <summary>
                /// 棋盘被动
                /// </summary>
                MergePassive,
                /// <summary>
                /// 棋盘辅助
                /// </summary>
                MergeAssist
            } 


            /// <summary>
            /// 表ID
            /// </summary>
            public int id;
            /// <summary>
            /// 是否主动 、被动 走表的方式不同
            /// </summary>
            public bool Active;
            /// <summary>
            /// 等级  
            /// </summary>
            public int level;
            /// <summary>
            /// 释放完毕重置的时间戳
            /// </summary>
            public long ResetTime;

            /// <summary>
            /// 技能类型
            /// </summary>
            public HeroSkillData.SkillType mType;

            /// <summary>
            ///  解锁的星星  int index = mHero.HeroCfg.SkillUnlock.IndexOf(mHeroSKillData.id.ToString());
            /// </summary>
            public int unlockStar;

            public bool MaxLevel(out int maxLevel)
            {
                maxLevel = 0;
                var upgradeList = Cfg.C.CD2SkillUpgrade.Is();

                foreach (var item in upgradeList)
                {
                    if (item.SkillId == id)
                    {
                        maxLevel = maxLevel < item.Level ? item.Level : maxLevel;
                    }
                }

                return maxLevel <= level;
            }
        }

        private Dictionary<int, HeroSkillData> heroSkillDatas;
        /// <summary>
        /// 英雄技能数据
        /// </summary>
        public Dictionary<int, HeroSkillData> HeroSkillDatas
        {
            get
            {
                if (heroSkillDatas == null)
                {
                    heroSkillDatas = new Dictionary<int, HeroSkillData>();
                }

                if (heroSkillDatas.Count == 0 && HeroCfg != null)
                {
                    foreach (var item in HeroCfg.SkillUnlock)
                    {
                        if (int.TryParse(item, out var skillId))
                        {
                            if (skillId > 0)
                            {
                                heroSkillDatas[skillId] = new HeroSkillData()
                                {
                                    level = 0,
                                    ResetTime = 0,
                                    id = skillId,
                                    unlockStar = HeroCfg.SkillUnlock.IndexOf(skillId.ToString()),
                                    mType = HeroSkillData.SkillType.HeroPassive,
                                    Active = HeroCfg.ActiveSkill == skillId
                                };
                            }
                        }
                    }
                }

                return heroSkillDatas;
            }
        }


        public List<HeroSkillData> UnlockCurStar()
        {
           
            if (StarStep == 0)
            {
                List<HeroSkillData> skills = new List<HeroSkillData>();

                foreach (var item in HeroSkillDatas)
                {
                    if(item.Value.unlockStar==Star)
                        skills.Add(item.Value);
                }

                foreach (var item in HeroMergeSkillDatas)
                {
                    if (item.unlockStar == Star)
                        skills.Add(item);
                }

                return skills;
            }
            else
                return null;
        }


        public List<HeroSkillData> HeroMergeSkillDatas
        {
            get
            {
                List<HeroSkillData> mergeSkills = new List<HeroSkillData>();

                for (int i = 0; i < HeroCfg.SkillForPassive.Count; i++)
                {
                    string skillItem = HeroCfg.SkillForPassive[i];
                    if (!string.IsNullOrEmpty(skillItem))
                    {
                        if (skillItem.Contains(','))
                        {
                            var skills = skillItem.Split(',');
                            foreach (var ii in skills)
                            {
                                mergeSkills.Add(new HeroSkillData()
                                {
                                    level = 0,
                                    id = int.Parse(ii),
                                    mType = HeroSkillData.SkillType.MergePassive,
                                    unlockStar = i,
                                    Active = false
                                });
                            }
                        }
                        else if (int.TryParse(skillItem, out var skillId))
                        {
                            mergeSkills.Add(new HeroSkillData()
                            {
                                level = 0,
                                id = skillId,
                                mType = HeroSkillData.SkillType.MergePassive,
                                unlockStar = i,
                                Active = false
                            });
                        }
                    }

                }
                //if (HeroCfg.SkillForAssisted.Count>Star)
                //{
                //    string skillItem = HeroCfg.SkillForAssisted[Star];
                //    if (!string.IsNullOrEmpty(skillItem))
                //    { 
                //        if (skillItem.Contains(','))
                //        {
                //            var skills = skillItem.Split(',');
                //            foreach (var ii in skills)
                //            {
                //                mergeSkills.Add(new HeroSkillData()
                //                {
                //                    level = 0,
                //                    id = int.Parse(ii),
                //                    mType = UIHeroUpStarDialogSkillItem.SkillType.MergeAssist,
                //                    unlockStar = Star,
                //                    Active = false
                //                });
                //            }
                //        }
                //        else if (int.TryParse(skillItem, out var skillId))
                //        {
                //            mergeSkills.Add(new HeroSkillData()
                //            {
                //                level = 0,
                //                id = skillId,
                //                mType = UIHeroUpStarDialogSkillItem.SkillType.MergeAssist,
                //                unlockStar = Star,
                //                Active = false
                //            });
                //        }
                //    }
                //}


                return mergeSkills;
            }
        }


        /// <summary>
        /// 棋盘技能 只取当前星的
        /// </summary>
        //public List<HeroSkillData> HeroMergeStarSkillDatas
        //{
        //    get
        //    {
        //        List<HeroSkillData> mergeSkills=new List<HeroSkillData>();


        //        if (HeroCfg.SkillForPassive.Count>Star)
        //        {
        //            string skillItem= HeroCfg.SkillForPassive[Star];
        //            if (!string.IsNullOrEmpty(skillItem))
        //            {
        //                if (skillItem.Contains(','))
        //                {
        //                    var skills = skillItem.Split(',');
        //                    foreach (var ii in skills)
        //                    {
        //                        mergeSkills.Add(new HeroSkillData()
        //                        {
        //                            level = 0,
        //                            id = int.Parse(ii),
        //                            mType = UIHeroUpStarDialogSkillItem.SkillType.MergePassive,
        //                            unlockStar = Star,
        //                            Active = false
        //                        });
        //                    }
        //                }
        //                else if (int.TryParse(skillItem, out var skillId))
        //                {
        //                    mergeSkills.Add(new HeroSkillData()
        //                    {
        //                        level = 0,
        //                        id = skillId,
        //                        mType = UIHeroUpStarDialogSkillItem.SkillType.MergePassive,
        //                        unlockStar = Star,
        //                        Active = false
        //                    });
        //                }
        //            }
        //        }

        //        //if (HeroCfg.SkillForAssisted.Count>Star)
        //        //{
        //        //    string skillItem = HeroCfg.SkillForAssisted[Star];
        //        //    if (!string.IsNullOrEmpty(skillItem))
        //        //    { 
        //        //        if (skillItem.Contains(','))
        //        //        {
        //        //            var skills = skillItem.Split(',');
        //        //            foreach (var ii in skills)
        //        //            {
        //        //                mergeSkills.Add(new HeroSkillData()
        //        //                {
        //        //                    level = 0,
        //        //                    id = int.Parse(ii),
        //        //                    mType = UIHeroUpStarDialogSkillItem.SkillType.MergeAssist,
        //        //                    unlockStar = Star,
        //        //                    Active = false
        //        //                });
        //        //            }
        //        //        }
        //        //        else if (int.TryParse(skillItem, out var skillId))
        //        //        {
        //        //            mergeSkills.Add(new HeroSkillData()
        //        //            {
        //        //                level = 0,
        //        //                id = skillId,
        //        //                mType = UIHeroUpStarDialogSkillItem.SkillType.MergeAssist,
        //        //                unlockStar = Star,
        //        //                Active = false
        //        //            });
        //        //        }
        //        //    }
        //        //}


        //        return mergeSkills;
        //    }
        //}


        /// <summary>
        /// 主动技能id
        /// </summary>
        public int SkillId
        {
            get
            {
                if (this.HeroCfg != null)
                {
                    return this.HeroCfg.ActiveSkill;
                }
                return 0;
            }
        }

        /// <summary>
        /// 上次技能释放时间戳
        /// </summary>
        public long SkillTime
        {
            get
            {
                if (this.HeroSkillDatas.ContainsKey(this.HeroCfg.ActiveSkill))
                {
                    return this.HeroSkillDatas[this.HeroCfg.ActiveSkill].ResetTime;
                }
                return 0;
            }
        }

#if UNITY_EDITOR || INTERNAL_ENTRANCE || DEFAULT_ENV_BETA
        /// <summary>
        /// 设置技能cd-只用于方便测试
        /// </summary>
        /// <param name="time"></param>
        public void SetSkillTime(long time)
        {
            if (this.HeroSkillDatas.TryGetValue(this.HeroCfg.ActiveSkill, out var data))
            {
                data.ResetTime = time;
            }
        }
#endif


        public bool InSkillCD
        {
            get
            {
                var maxCdTime = (long)GetSkillCfg()?.Cd;
                if (maxCdTime > 0)
                {
                    var lastReleaseTime = SkillTime;
                    if (lastReleaseTime > 0)
                    {
                        var cdTime = GameTime.Time - lastReleaseTime;
                        cdTime = (long)(cdTime * GameConfig.GAME_TIME_MILLISECOND_TO_SECOND);
                        if (cdTime >= maxCdTime)
                        {
                            return false;
                        }
                        else
                        {
                            return true;
                        }
                    }
                    else
                    {
                        return false;
                    }
                }
                else
                {
                    return false;
                }
            }
        }

        /// <summary>
        /// 获取主动技能的英雄-已上阵
        /// </summary>
        public CD2Skill GetSkillCfg()
        {
            if (this.HeroCfg != null && this.HeroCfg.ActiveSkill > 0)
            {
                var skillCfg = Cfg.C.CD2Skill.I(this.HeroCfg.ActiveSkill);
                return skillCfg;
            }
            return null;
        }

        /// <summary>
        /// 设置英雄技能重置时间
        /// </summary>
        /// <param name="heroSkills"></param>
        public void SetHeroSkill(Dictionary<int, long> heroSkills)
        {
            foreach (var item in heroSkills)
            {
                if (this.HeroSkillDatas.TryGetValue(item.Key, out var skillData))
                {
                    skillData.ResetTime = item.Value;
                }
            }
        }

        public void SetHeroSkill(HeroData hero)
        {
            foreach (var item in hero.HeroSkillDatas)
            {
                if (this.HeroSkillDatas.TryGetValue(item.Key, out var skillData))
                {
                    skillData.ResetTime = item.Value.ResetTime;
                    skillData.level = item.Value.level;
                }
            }
        }


        public void SetHeroSkill(cspb.HeroData hero)
        {
            //foreach (var item in hero.heroSkills)
            //{
            //    if (this.HeroSkillDatas.TryGetValue(item.Key, out var skillData))
            //    {
            //        skillData.ResetTime = item.Value;
            //    }
            //}

            foreach (var item in hero.skills)
            {
                if (this.HeroSkillDatas.TryGetValue(item.Key, out var skillData))
                {
                    skillData.level = item.Value.level;
                }
            }
        }

        /// <summary>
        /// 更新技能时间
        /// </summary>
        /// <param name="skillId"></param>
        /// <param name="time"></param>
        public void UpdateSkillTime(int skillId, long time)
        {
            if (HeroSkillDatas.TryGetValue(skillId, out var skillData))
            {
                skillData.ResetTime = time;
            }
        }

        /// <summary>
        /// 棋盘主动技能
        /// </summary>
        public int MergeActiveSkill
        {
            get
            {
                int skillID = 0;
                if (HeroCfg.SkillForPro.Count > Star)
                {
                    int.TryParse(HeroCfg.SkillForPro[Star], out skillID);
                }

                return skillID;
            }
        }

        /// <summary>
        /// 棋盘被动技能 已判断星级
        /// </summary>
        //public List<int> MergeBeActiveSkill
        //{
        //    get
        //    {
        //        List<int> skillID = new List<int>();
        //        if (HeroCfg.SkillForPassive.Count > Star)
        //        {
        //            string skills = HeroCfg.SkillForPassive[Star];
        //            if (!string.IsNullOrEmpty(skills))
        //            {
        //                if (skills.Contains(','))
        //                {
        //                    var ids = skills.Split(',');
        //                    foreach (var item in ids)
        //                    {
        //                        skillID.Add(int.Parse(item));
        //                    }
        //                }
        //                else
        //                {
        //                    skillID.Add(int.Parse(skills));
        //                }
        //            }
        //        }

        //        return skillID;
        //    }
        //}

        ///// <summary>
        ///// 棋盘辅助技能 已判断星级
        ///// </summary>
        //public List<int> MergeAssistedSkill
        //{
        //    get
        //    {
        //        List<int> skillID = new List<int>();
        //        //if (HeroCfg.SkillForAssisted.Count > Star)
        //        //{
        //        //    string skills= HeroCfg.SkillForAssisted[Star];
        //        //    if (!string.IsNullOrEmpty(skills))
        //        //    {
        //        //        if (skills.Contains(','))
        //        //        {
        //        //            var ids = skills.Split(',');
        //        //            foreach (var item in ids)
        //        //            {
        //        //                skillID.Add(int.Parse(item));
        //        //            }
        //        //        }
        //        //        else
        //        //        {
        //        //            skillID.Add(int.Parse(skills));
        //        //        }
        //        //    } 
        //        //}

        //        return skillID;
        //    }
        //}

        ///亲密度
        public int Intimacy { private set; get; }


        public CHeroIntimacy GetIntimacyCfg()
        {
            var list = Cfg.C.CHeroIntimacy.RawList();
            int level = 0;
            list = list.Where(a => a.Quality == this.HeroCfg.HeroType).OrderBy(a => a.Intimacy).ToList();
            for (int i = 0; i < list.Count; i++)
            {
                var item = list[i];
                var preItem = i > 0 ? list[i - 1] : null;
                if (Intimacy < item.Intimacy)
                {
                    if (preItem != null)
                    {
                        float bilv = (Intimacy - preItem.Intimacy) * 1f / (item.Intimacy - preItem.Intimacy);
                        return preItem;
                    }
                    else
                    {
                        return null;
                    }
                }
                else if (i == list.Count - 1)
                {
                    return item;
                }
            }

            return null;
        }

        public async UniTask<bool> MaxIntimacyLevel()
        {

            var list = await Cfg.C.CHeroIntimacy.RawListAsync();
             
            list = list.Where(a => a.Quality == this.HeroCfg.HeroType).OrderBy(a => a.Intimacy).ToList();
            for (int i = 0; i < list.Count; i++)
            {
                var item = list[i];
                var preItem = i > 0 ? list[i - 1] : null;
                if (Intimacy < item.Intimacy)
                {
                    return false;
                }
                else if (i == list.Count - 1)
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 亲密度等级 需要判断品质和等级
        /// </summary>
        /// <returns></returns>
        public async UniTask<(int,float)> IntimacyLevel()
        {
            var list=await Cfg.C.CHeroIntimacy.RawListAsync();
            int level = 0;
            list = list.Where(a=>a.Quality==this.HeroCfg.HeroType).OrderBy(a => a.Intimacy).ToList();
            for (int i = 0; i < list.Count; i++)
            {
                var item = list[i];
                var preItem = i>0 ? list[i - 1] : null;
                if (Intimacy < item.Intimacy)
                {
                    if (preItem != null)
                    {
                        float bilv = (Intimacy - preItem.Intimacy) * 1f / (item.Intimacy - preItem.Intimacy);
                        return (preItem.Level, bilv);
                    }
                    else
                    {
                        float bilv = Intimacy * 1f / item.Intimacy;
                        return (0, bilv);
                    }
                }
                else if(i== list.Count-1)
                {
                    return (item.Level, 1);
                }
            }

            return (level,0);
        }

        /// <summary>
        /// 获取亲密度当前进度 当前值和区间值
        /// </summary>
        /// <returns></returns>
        public async UniTask<(int, int)> IntimacyProcess()
        {
            var list = await Cfg.C.CHeroIntimacy.RawListAsync();
            int level = 0;
            list = list.Where(a => a.Quality == this.HeroCfg.HeroType).OrderBy(a => a.Intimacy).ToList();
            for (int i = 0; i < list.Count; i++)
            {
                var item = list[i];
                var preItem = i > 0 ? list[i - 1] : null;
                if (Intimacy < item.Intimacy)
                {
                    if (preItem != null)
                    { 
                        return (Intimacy - preItem.Intimacy, item.Intimacy - preItem.Intimacy);
                    }
                    else
                    { 
                        return (Intimacy, item.Intimacy);
                    }
                }
                else if (i == list.Count - 1)
                {
                    return (item.Intimacy, item.Intimacy);
                }
            }

            return (1, 1);
        }

        public async UniTask<bool> CanIntimacyUp()
        {
            if (!UnlockMgr.I.CheckUnlock(UnlockFuncEnum.HeroIntimacy))
                return false;

            long needIntimacyValue = 0;
            var list = await Cfg.C.CHeroIntimacy.RawListAsync();
           
            list = list.Where(a => a.Quality == this.HeroCfg.HeroType).OrderBy(a => a.Intimacy).ToList();
            for (int i = 0; i < list.Count; i++)
            {
                var item = list[i];
                var preItem = i > 0 ? list[i - 1] : null;
                if (Intimacy < item.Intimacy)
                {
                    needIntimacyValue = item.Intimacy - Intimacy;
                    break;
                }
                else if (i == list.Count - 1)
                {
                    return false;
                }
            }

            var itemList = await Cfg.C.CIntimacyItem.GetEnumerableAsync();
            foreach (var item in itemList)
            {
                var itemCfg = await Cfg.C.CItem.GetConfigAsync(item.Id);
                var itemCount= PlayerAssetsMgr.I.GetItemCountByID(itemCfg.Id);
                if (itemCount > 0)
                {
                    needIntimacyValue = needIntimacyValue- (itemCount* itemCfg.CategoryParamEffect(0).Val);

                    if (needIntimacyValue <= 0)
                    {
                        return true;
                    }
                }
                 
            }
            return false;

        }

        #endregion

        public HeroData(long heroId, int modelId, int start, int level, long exp, int starStep, HeroStatus status = HeroStatus.HeroStatusIdle, cspb.HeroData heroDataProto = null)
        {
            _heroId = heroId;
            _modelId = modelId;
            _star = start;
            _level = level;
            _exp = exp;
            _isBattle = status;
            _starStep = starStep;
            Intimacy=heroDataProto?.intimacy??0;
            _heroCfg = Cfg.C.CD2Hero.I(_modelId);
            HeroServerData = heroDataProto;
            GetHeroAttribute();
            GetHeroExp();
        }

        public HeroData(cspb.HeroData heroDataProto)
        {
            _heroId = heroDataProto.heroId;
            _modelId = heroDataProto.cfgId;
            _star = heroDataProto.star;
            _level = heroDataProto.level;
            _exp = heroDataProto.exp;
            _isBattle = heroDataProto.status;
            _starStep = heroDataProto.steps;
            Intimacy=heroDataProto.intimacy;
            //_goodWill = heroDataProto.goodwill;
            _heroCfg = Cfg.C.CD2Hero.I(_modelId);
            HeroServerData = heroDataProto;
            GetHeroAttribute();
            GetHeroExp();

            SetHeroSkill(heroDataProto);
        }


        /// <summary>
        /// 获得英雄Config属性
        /// </summary>
        public Cfg.G.CD2HeroAttribute GetHeroAttribute()
        {
            //  int _attributeLevel = _level > 50 ? 50 : _level;
            if (_heroCfg != null)
            {
                _heroAttribute = Cfg.C.CD2HeroAttributeExtension.GetHeroAttributeList(_heroCfg.HeroAttributeId, _level, _heroCfg.HeroType);
            }

            return _heroAttribute;
        }

        /// <summary>
        /// 获得英雄Config经验
        /// </summary>
        public void GetHeroExp()
        {
            if (_heroCfg != null)
            {
                _heroExp = Cfg.C.CD2HeroExpExtension.GetHeroExp(_heroCfg.HeroExpId, _level); // Cfg.C.CD2HeroExp.GetHeroExp(_heroCfg.HeroExpId, _level);
            }
            else
            {
                D.Error?.Log($"英雄配置找不到:{this.ModelId}");
            }
        }

        /// <summary>
        /// 英雄数据排序的逻辑
        /// </summary>
        /// <param name="x"></param>
        /// <param name="y"></param>
        /// <returns></returns>
        int IComparer<HeroData>.Compare(HeroData x, HeroData y)
        {
            if ((x.IsBattle == 0 && y.IsBattle == 0) || (x.IsBattle != 0 && y.IsBattle != 0))
            {
                if (x.HeroCfg.HeroType > y.HeroCfg.HeroType)
                {
                    return 1;
                }
                else if (x.HeroCfg.HeroType == y.HeroCfg.HeroType)
                {
                    if (x.Level > y.Level)
                    {
                        return 1;
                    }
                    else if (x.Level == y.Level)
                    {
                        if (x.ModelId > y.ModelId)
                        {
                            return 1;
                        }
                        else
                        {
                            return -1;
                        }
                    }
                    else
                    {
                        return -1;
                    }
                }
                else
                {
                    return -1;
                }
            }
            else if (x.IsBattle != 0)
            {
                return 1;
            }
            else if (y.IsBattle != 0)
            {
                return -1;
            }
            else
            {
                return 1;
            }
        }


        HeroData singleData;

        /// <summary>
        /// 英雄的模型数据 （1级 0星）
        /// </summary>
        /// <returns></returns>
        public HeroData SingleData()
        {
            if (singleData == null)
                singleData = new HeroData(HeroId, ModelId, 0, 1, 0, 0);
            return singleData;
        }


        public bool MaxStar(out int maxStar)
        {
            maxStar = 5;
            if (HeroCfg == null
                || HeroCfg.StarStageCost == null)
                return false;

            maxStar = HeroCfg.StarStageCost.Count;
            if (maxStar <= Star)
            {
                return true;
            }

            return false;
        }

        public bool MaxSkillLevel()
        {
            foreach (var item in HeroSkillDatas)
            {
                if (!item.Value.MaxLevel(out var maxLevel))
                {
                    return false;
                }
            }
            return true;
        }

        public bool CanUpStarUp(out long curCard, out int needCard)
        {  
            HeroGameData.I.mCultivateMap.TryGetValue(ModelId, out curCard);
            needCard = 0;
           
            if (!UnlockMgr.I.CheckUnlock(UnlockFuncEnum.HeroStar))
                return false;
           
            curCard -= 1;

            if (HeroCfg != null
                && HeroCfg.StarStageCost.Count > Star)
            {
                var steps = HeroCfg.StarStageCost[Star].Split(',');
                if (steps.Length > StarStep)
                {
                    needCard = int.Parse(steps[StarStep]);
                    return needCard <= curCard;
                }
            }

            needCard = 0;
            return false;

        }
        //public bool CanUpLevelUp(HeroData mHeroData)
        //{
        //    int toLevel = Mathf.Min(mHeroData.Level + 1, GameData.I.UnlockData.MaxHeroLevel);
        //    var maxLvExp = HeroUtils.GetExpToLevel(mHeroData, toLevel);

        //    if (maxLvExp == 0)
        //    {
        //        return false;
        //    }

        //    //var costHeros = HeroGameData.I.GetExpHeros();

        //    long curExp = PlayerAssetsMgr.I.GetVMCount(Config.ConfigID.vm_heroExp);
        //    //for (int i = 0; i < costHeros.Count; i++)
        //    //{
        //    //    var heroData = costHeros[i];


        //    //    if (HeroGameData.I.mCultivateMap.TryGetValue(heroData.ModelId, out var CurHeroCount))
        //    //    {
        //    //        if (CurHeroCount <= 1)
        //    //        {
        //    //            continue;
        //    //        }

        //    //        var iiExp = HeroUtils.GetHeroExp(heroData);
        //    //        curExp = curExp + (iiExp * (CurHeroCount - 1));
        //    //    }
        //    //}

        //    return curExp >= maxLvExp;
        //}

        //public bool CanHeroEqui(HeroData mHeroData)
        //{

        //    HeroEquipItem _cHero = HeroEquipmentForgeMgr.I.HeroEquipState(mHeroData.HeroCfg.Id);
        //    if (_cHero != null)
        //    {
        //        return false;
        //    }

        //    List<HeroEquipItem> _equipItems = HeroEquipmentForgeMgr.I.GetHroEquipItems(mHeroData.HeroCfg.SoldiersType, mHeroData.HeroCfg.Id);
        //    if (_equipItems != null)
        //    {
        //        return _equipItems.Count > 0;
        //    }

        //    return false;

        //}



        public bool CanUpSkillUp(out long curCard, out int needCard)
        {
            HeroGameData.I.mCultivateMap.TryGetValue(ModelId, out curCard);
            curCard -= 1;

            if (HeroCfg.StarStageCost.Count <= Star)
            {
                needCard = HeroCfg.SkillNeeding;
                return needCard <= curCard;
            }

            needCard = 0;
            return false;

        }

        public bool CanUpStarOrSkill()
        {
            if (HeroCfg.StarStageCost.Count <= Star)
            {
                bool canUpSkill = false;

                if (LSwitchMgr.I.IsFunctionOpen(SwitchConfig.HeroSkill_levelup))
                {
                    foreach (var item in HeroSkillDatas)
                    {
                        if (!item.Value.Active && !item.Value.MaxLevel(out var maxLevel))
                        {
                            canUpSkill = true;
                            break;
                        }
                    }
                }

                return canUpSkill;
            }
            else
            {
                return true;
            }
        }
        public long GoodWillPower()
        {
            var interCfg= GetIntimacyCfg();
            if (interCfg != null)
                return interCfg.Power;
            return 0;

            //var goodwillCfg = Cfg.C.CHeroGoodwill.I(HeroCfg.Id);
            //long goodwillPower = 0;
            //int curProperty = 0;
            //if (goodwillCfg == null)
            //{
            //    return goodwillPower;
            //}
            //if (GoodWill >= int.Parse(goodwillCfg.GoodwillNode[2]))
            //{
            //    curProperty = int.Parse(goodwillCfg.Property[2]);
            //}
            //else if (GoodWill >= int.Parse(goodwillCfg.GoodwillNode[1]))
            //{
            //    curProperty = int.Parse(goodwillCfg.Property[1]);
            //}
            //else if (GoodWill >= int.Parse(goodwillCfg.GoodwillNode[0]))
            //{
            //    curProperty = int.Parse(goodwillCfg.Property[0]);
            //}
            //var goodwillPropertyCfg = Cfg.C.CHeroGoodwillProperty.I(curProperty);
            //if (goodwillPropertyCfg != null)
            //{
            //    long basePower = Level > 50 ? HeroAttribute.Power + (Level - 50) * MetaConfig.HeroPowerParm[HeroCfg.HeroType - 1] : HeroAttribute.Power;
            //    goodwillPower = (long)(basePower * goodwillPropertyCfg.Value);
            //}
            //return goodwillPower;
        }
        public long Power(bool isStarPower = false, bool isSkillPower = false, bool isbasePower = false)
        {
            long basePower = 0;
            basePower = Level > 50 ? HeroAttribute.Power + (Level - 50) * MetaConfig.HeroPowerParm[HeroCfg.HeroType - 1] : HeroAttribute.Power;


            double starPower = 0;
            long skillPower = 0;

            float starbilv = 0;

            //星级战力加成
            if (Star >= 1)
            {
                int powerCurTotal = int.Parse(HeroCfg.StarPower[Star - 1]);
                starbilv += (powerCurTotal * 0.01f);
            }

            //阶段战力加成 
            if (Star < HeroCfg.StarRating)
            {
                int next = int.Parse(HeroCfg.StarPower[Star]);
                if (Star > 0)
                {
                    next = next - int.Parse(HeroCfg.StarPower[Star - 1]);
                }

                starbilv += (next * StarStep * 0.01f / 6f);
            }
            starPower = (basePower * starbilv);


            //阶段战力 增加数值
            if (StarStep > 0 || Star > 0)
            {
                if (StarStep == 0)
                {
                    if (HeroCfg.StarStagePower.Count > Star - 1)
                    {
                        var steps = HeroCfg.StarStagePower[Star - 1].Split(',');
                        starPower += int.Parse(steps[steps.Length - 1]);
                    }
                }
                else
                {
                    if (HeroCfg.StarStagePower.Count > Star)
                    {
                        var steps = HeroCfg.StarStagePower[Star].Split(',');
                        starPower += int.Parse(steps[StarStep - 1]);
                    }
                }
            }

            float skillBilv = 0;
            var upgradeSkillList = Cfg.C.CD2SkillUpgrade.RawList();
            foreach (var item in HeroSkillDatas)
            {
                foreach (var ii in upgradeSkillList)
                {
                    if (item.Value.id == ii.SkillId && item.Value.level == ii.Level)
                    {
                        skillBilv += ii.Power;
                    }
                }
            }

            skillPower = (long)(basePower * skillBilv);


            //D.Info?.Log($"当前英雄{HeroCfg.Id} {Star}星{StarStep}阶 战力构成:基础{basePower} 星级{starPower} 技能{skillPower} 心愿战力:{GoodWillPower()}");

            // _power = (int)(_power * (1 + bilv / 100));
            if (isStarPower)
            {
                return (long)starPower;
            }
            if (isSkillPower)
            {
                return skillPower;
            }
            if (isbasePower)
            {
                return basePower;
            }
            return basePower + (long)starPower + skillPower + GoodWillPower();
        }
    }
}
