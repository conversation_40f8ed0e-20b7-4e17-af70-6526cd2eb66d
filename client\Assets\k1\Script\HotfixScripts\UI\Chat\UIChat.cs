﻿








using Common;
using cspb;
using Game.Config;
using Game.Utils;
using Logic;
using Public;
using System;
using System.Collections.Generic;
using DeepUI;
using TFW;
using TFW.Localization;
using TFW.UI;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Game.Data;
using UI.Utils;
using Logic.Alliance.Achievement;
using System.Linq;
using K3;

// using RedEnvelope;

namespace UI
{
    public class UIChatData : PopupData
    {
        public ChatTabs CurrChatTab = ChatTabs.Server;

        public UIChat.PrivateChatData privateChatTarget = null;

        /// <summary>
        /// 返回的时候看返回到哪里
        /// </summary>
        public MainMenuType CurrentTabKey;
    }
    /// <summary>
    /// 聊天页签枚举
    /// </summary>
    public enum ChatTabs
    {
        Invalid = 0,
        /// <summary>
        /// 世界-(王国相当于当前服)
        /// </summary>
        Server,
        /// <summary>
        /// 通知
        /// </summary>
        Notifications,
        /// <summary>
        /// 联盟
        /// </summary>
        Alliance = 4,
        /// <summary>
        /// 私聊
        /// </summary>
        PrivateChat = 7,
        /// <summary>
        /// 邮件
        /// </summary>
        Mail,
        /// <summary>
        /// 跨服聊天(世界,所有服)
        /// </summary>
        CrossServer,
        /// <summary>
        /// 军团频道wjk
        /// </summary>
        Legion = 10,

        /// <summary>
        /// 光明战场
        /// </summary>
        Luminary
    }

    [Popup("Chat/Ui2502Chat_New", true, true)]
    public partial class UIChat : HistoricPopup
    {
        /// <summary>
        /// Cpe小游戏成就任务指令，发送这个会打开成就界面
        /// </summary>
        const string CPETaskCommand = "task:/";

        public static event Action onClose;

        public override bool LFullScreen => false;

        public ChatContext CurrentChannel { get; private set; }

        public override bool IsDisableDrag { get; set; } = true;

        /// <summary>
        /// 当前的选中的tab
        /// </summary>
        public ChatTabs CurrentTab => m_FormerTab;

        /// <summary>
        /// 下个Tab。
        /// 如果当前没有正在打开任何Tab，则NextTab == CurrentTab。
        /// 反之NextTab = 要打开的Tab，CurrentTab = 当前Tab。
        /// </summary>
        public ChatTabs NextTab { get; private set; }

        // private PinnedRedEnvelopeUI _pinnedRedEnvelope;

        private MessageListUI messageChannel = null;
        private const int leftTabCount = 11;//添加了军团  从9变10  wjk //10👉11
        private const int ChatChannelInputLimit = 280;

        /// <summary>加入联盟buttons</summary>
        private GameObject _joinAlliancePage;

        /// <summary>
        /// 聊天chatCanvas
        /// </summary>
        private GameObject m_MainChatCanvas;

        /// <summary>
        /// 聊天chatCanvas滚动栏
        /// </summary>
        private TFWScrollRect _mainChannelScroll;

        /// <summary>
        /// 私聊列表面板
        /// </summary>
        private UIChatPrivateListCanvas m_PrivateChatsPanel;

        /// <summary>
        /// 网络状态提示框
        /// </summary>
        private GameObject m_NetworkStateBar;

        private GameObject m_InputFieldRoot;
        private Transform _inputRoot;
        private InputField m_InputField;
        private TFWTabGroup m_LeftTabRoot;
        /// <summary>
        /// 中间节点（消息列表）
        /// </summary>
        private GameObject m_MiddleRoot;
        private ChatTabs m_FormerTab;

        /// <summary>
        /// 不可输入的提示
        /// </summary>
        private TFWText _unInputeableTxt;

        private EventTriggerListener m_BtnSmileListener;
        //private EventTriggerListener m_BtnCaptureListener;
        private GameObject m_InputTextRootInCeilPhone;
        private TFWRichText m_InputTextInCeilPhone;

        ChatEmojiPanelUI _emojiPanelUI;

        /// <summary>
        /// 新消息提示
        /// </summary>
        private GameObject m_NewMsgTip;

        /// <summary>
        /// 加载提示（转圈特效）
        /// </summary>
        private GameObject m_LoadingEffect;

        private GameObject _sendBtnObj, _sendBtnObjWorld;

        #region  联盟帮助奖励

        /// <summary>
        /// 联盟帮助奖励领取
        /// </summary>
        GameObject _allianceHelpRewardObj;

        RedWidget _allianceHelpRewardRedWidget;

        TFWText _allianceHelpRewardText;

        #endregion

        public static UIChat I { get; private set; }

        /// <summary>
        /// 初始数据
        /// </summary>
        public UIChatData m_UIChatData;

        private float m_ChatOfflineTimeout = 10f;  //聊天系统判断离线的超时时间（单位：秒）
        private float m_ChatOfflineTimeoutTimer;

        /// <summary>
        /// 是否显示（这里原来是新P2 UIbase的逻辑）
        /// </summary>
        public bool IsLogicShown
        {
            get
            {
                return gameObject != null &&
                    gameObject.activeSelf &&
                    (!LFullScreen || (gameObject.GetComponent<CanvasGroup>() != null && gameObject.GetComponent<CanvasGroup>().alpha != 0));

            }
        }

        /// <summary>
        /// 聊天cd
        /// </summary>
        private Dictionary<int, float> lastSendTimeCDChannel = new Dictionary<int, float>() {
            { (int)ChatTabs.Server,MetaConfig.ChatCDTimesWorld},//世界频道聊天间隔时间（秒）  ChatCDTimesWorld  5
            { (int) ChatTabs.Alliance,MetaConfig.ChatCDTimesAlliance},// 联盟频道聊天间隔时间（秒）  ChatCDTimesAlliance  1
            { (int) ChatTabs.PrivateChat,MetaConfig.ChatCDTimesAlliance},
            { (int) ChatTabs.CrossServer,MetaConfig.CrossServeChatCD},
            { (int) ChatTabs.Legion,1},    // TODO: bishuai阵营聊天时间间隔
            { (int) ChatTabs.Luminary,1}
        };

        /// <summary>
        /// 初始化
        /// </summary>
        protected override void OnInit()
        {
            base.OnInit();

            m_FormerTab = ChatTabs.Invalid;
            NextTab = ChatTabs.Invalid;
            OnInitPrivateChat();
            I = this;
        }

        /// <summary>
        /// 加载
        /// </summary>
        protected override void OnLoad()
        {
            //D.Chat.Error?.Log("chat->UIChat.OnLoad() start");
            ///初始化objects
            InitObjects();
            //事件监听
            InitListeners();
            //初始化tab
            InitTabs();

            //多语言
            SetLocalLanguage();
            //emoji相关
            //OnLoadEmoji();

            _emojiPanelUI = UIHelper.GetComponent<ChatEmojiPanelUI>(m_MiddleRoot, "ChatInputUI/emojiListPopup");

            _emojiPanelUI.onInputCustomEmoji += OnInputCustomizedEmoji;

            messageChannel = UIHelper.GetComponent<MessageListUI>(m_MiddleRoot, "ChatInputUI/chatCanvas/scroll_rect");

            // _pinnedRedEnvelope =
            //     UIHelper.GetComponent<PinnedRedEnvelopeUI>(m_MiddleRoot, "ChatInputUI/chatCanvas/PinnedRedEnvelopeUI");

            _allianceHelpRewardText = UIHelper.GetComponent<TFWText>(m_MiddleRoot, "ChatInputUI/chatCanvas/GiftBtn/Text");
            _allianceHelpRewardRedWidget = new RedWidget(UIHelper.GetChild(m_MiddleRoot, "ChatInputUI/chatCanvas/GiftBtn/RedDot"));
        }

        protected override void OnShown()
        {
            #region 缓存界面需要初始化的东西
            //首次（有联盟为联盟，无联盟世界）
            if (m_FormerTab == ChatTabs.Invalid)
            {
                m_FormerTab = LPlayer.I.UnionID != 0 ? ChatTabs.Alliance : ChatTabs.Server;
            }

            m_UIChatData = Data as UIChatData;
            if (m_UIChatData != null)
            {
                if (m_UIChatData.CurrChatTab != ChatTabs.Invalid)
                {
                    m_FormerTab = m_UIChatData.CurrChatTab;
                }
                m_PrivateChatTarget = m_UIChatData.privateChatTarget;
            }
            else
            {
                return;//预加载 不处理
            }

            OnLoadPrivateChat();

            #endregion

            //UpdateEnable = true;
            LAllianceAltar.I.ReqUnionAltar();

            base.OnShown();
            var act = ActivityMgr.I.GetActivityByActivityType(ActivityType.FracturedLands3);
            for (int i = 1; i <= leftTabCount; ++i)
            {
                var tab = GetComponent<TFWTab>("animation/left/tfw_tab_group/tabs/tab" + i);
                if (tab.TabTag == "9")
                {
                    tab.gameObject.SetActive(act != null);
                }
                if (tab.TabTag == "10")
                {
                    tab.gameObject.SetActive(LPlayer.I.UseNewKVK && GameData.I.ActivityData.MyUnionInMatchList() && (ActivityGameData.NewKvkInfoStage3 == KvkUnionActState.KvkUnionStateBtl || ActivityGameData.NewKvkInfoStage3 == KvkUnionActState.KvkUnionStatePre));
                }

                var chatTab = (ChatTabs)tab.TabTag.ToInt();
                if (chatTab == ChatTabs.Luminary)
                {
                    var isInLightBattle = false;// GameData.I.BrightBattleFieldGameData.IsInBrightBattleFieldWaring;
                    tab.gameObject.SetActive(isInLightBattle);
                }
                else if (chatTab == ChatTabs.CrossServer)
                {
                    var isInLightBattle = false;// GameData.I.BrightBattleFieldGameData.IsInBrightBattleFieldWaring;
                    tab.gameObject.SetActive(!isInLightBattle && LPlayer.I.UseNewKVK && GameData.I.ActivityData.MyUnionInMatchList() && (ActivityGameData.NewKvkInfoStage3 == KvkUnionActState.KvkUnionStateBtl || ActivityGameData.NewKvkInfoStage3 == KvkUnionActState.KvkUnionStatePre));
                }
            }

            SetUIAllianceInfoPopState(false);
            AllianceGameData.I.AlliancWarRecordData.ReqPvpInfo();
            GameData.I.AllianceGiftData.RefreshUnionGiftInfo();

            SetPrivateChatTopUIActive(false);

            if (LPlayer.I.FreeChangeNameTimes > 0)
            {
                PopupManager.I.ShowPanel<UIPlayerReName>(new UIPlayerReNameData()
                {
                    CancelCallBack = () =>
                    {
                        MainUIReturn();
                    }
                });
            }
            //LSocialReportMgr.I.ReqMyMuteState();
            FrameUpdateMgr.RegisterRenderingUpdate(Name, new Action<float>((dt) => { RenderingUpdate(dt); }));

            ShowJoinAllianceBtn(LPlayer.I.UnionID == 0 && NextTab == ChatTabs.Alliance);

            m_LeftTabRoot.TurnTabOn(((int)NextTab).ToString());
            OnTabClick(CurrentTab, true, true);

            this._emojiPanelUI.Hide();

            //提示已经被禁言了
            if (LSocialReportMgr.I.ImIReported)
            {
                var time = LSocialReportMgr.I.FormateMuteTime;
                //var time = LSocialReportMgr.I.FormateHoursTime;
                var key = string.Format(LocalizationMgr.GetUIString("Player_Message_Report_Notice"), "", time);
                FloatTips.I.FloatMsg(key);
                SetInputeable(false);
            }
            else
            {
                SetInputeable(true);
            }

            ShowOrHideNetworkStateNotice(false);
            ShowOrHideLoadingEffect(false);
            OnAllianceAchieve_HelpRedDotRefush(null);

            m_ChatOfflineTimeoutTimer = m_ChatOfflineTimeout;

            EventMgr.RegisterEvent(TEventType.AllianceAchieve_HelpRedDotRefush, OnRefreshChest, this);
            EventMgr.RegisterEvent(TEventType.AllianceJoining, OnAllianceJoining, this);
            OnRefreshChest();
        }


        private void OnRefreshChest(object[] obj = null)
        {
            bool isOpen = LAllianceNewAchievement.I.AllianceHelpRewardCount > 0;
            if (_allianceHelpRewardObj && _allianceHelpRewardObj.activeSelf != isOpen)
            {
                _allianceHelpRewardObj.SetActive(isOpen);
            }
        }

        /// <summary>
        /// 设置多语言
        /// </summary>
        private void SetLocalLanguage()
        {
            var txt = UIHelper.GetComponent<TFWText>(m_InputFieldRoot, "row/lab_describe/PlaceHolder");
            txt.text = LocalizationMgr.GetUIString("Chat_Input_Text");//请输入内容
            txt = UIHelper.GetComponent<TFWText>(m_LeftTabRoot.gameObject, "tabs/tab1/sp_icon2/Text");
            txt.text = LocalizationMgr.GetUIString("Chat_Channel_World");
            txt = UIHelper.GetComponent<TFWText>(m_LeftTabRoot.gameObject, "tabs/tab1/sp_icon1/Text");
            txt.text = LocalizationMgr.GetUIString("Chat_Channel_World");
            txt = UIHelper.GetComponent<TFWText>(m_LeftTabRoot.gameObject, "tabs/tab4/sp_icon1/Text");
            txt.text = LocalizationMgr.GetUIString("Chat_Channel_Alliance");
            txt = UIHelper.GetComponent<TFWText>(m_LeftTabRoot.gameObject, "tabs/tab4/sp_icon2/Text");
            txt.text = LocalizationMgr.GetUIString("Chat_Channel_Alliance");
            txt = UIHelper.GetComponent<TFWText>(m_LeftTabRoot.gameObject, "tabs/tab10/sp_icon1/Text");
            txt.text = LocalizationMgr.GetUIString("Crusade_Rank_Btn03");
            txt = UIHelper.GetComponent<TFWText>(m_LeftTabRoot.gameObject, "tabs/tab10/sp_icon2/Text");
            txt.text = LocalizationMgr.GetUIString("Crusade_Rank_Btn03");
            txt = UIHelper.GetComponent<TFWText>(m_LeftTabRoot.gameObject, "tabs/tab11/sp_icon1/Text");
            txt.text = LocalizationMgr.GetUIString("LB_chat");
            txt = UIHelper.GetComponent<TFWText>(m_LeftTabRoot.gameObject, "tabs/tab11/sp_icon2/Text");
            txt.text = LocalizationMgr.GetUIString("LB_chat");
        }

        /// <summary>
        /// 初始化各种button和objects
        /// </summary>
        private void InitObjects()
        {
            m_MiddleRoot = GetChild("animation/middle");
            m_PrivateChatsPanel = GetComponent<UIChatPrivateListCanvas>("animation/middle/PrivateChatListCanvas");

            m_MainChatCanvas = UIHelper.GetChild(m_MiddleRoot, "ChatInputUI/chatCanvas");
            _mainChannelScroll = UIHelper.GetComponent<TFWScrollRect>(m_MainChatCanvas, "scroll_rect");

            m_NetworkStateBar = GetChild("animation/middle/sp_network_di");
            _inputRoot = UIHelper.GetComponent<Transform>(GetChild("animation/middle/ChatInputUI/cont_input"));
            m_InputFieldRoot = UIHelper.GetChild(_inputRoot.gameObject, "inputRoot");
            m_InputField = GetComponent<TFWinputField>("animation/middle/ChatInputUI/cont_input/inputRoot/row/lab_describe");
            _unInputeableTxt = GetComponent<TFWText>("animation/middle/ChatInputUI/cont_input/inputRoot/row/UnInputeableObj");
            m_InputField.onValueChanged.AddListener(OnInputFieldValueChanged);

            var txt = GetComponent<TFWText>("animation/middle/ChatInputUI/cont_input/inputRoot/row/lab_describe");
            txt.dontConvertLineFeed = true;

            SetInputFieldText("");

            m_BtnSmileListener = GetComponent<EventTriggerListener>("animation/middle/ChatInputUI/cont_input/btn_smiley");
            _sendBtnObj = GetChild("animation/middle/ChatInputUI/cont_input/btn_send");

            _sendBtnObjWorld = GetChild("animation/middle/ChatInputUI/cont_input/btn_send_world");

            m_InputTextRootInCeilPhone = GetChild("animation/middle/ChatInputUI/cont_input/inputRoot/row/lab_describe/emojiText_Input");
            m_InputTextInCeilPhone = GetComponent<TFWRichText>("animation/middle/ChatInputUI/cont_input/inputRoot/row/lab_describe/emojiText_Input/cont_text/lab");
            //加入联盟页面
            _joinAlliancePage = GetChild("animation/JoinAlliancePage");
            //set chat menu
            var anim = GetChild("animation");
            m_LoadingEffect = UIHelper.GetChild(m_MiddleRoot, "loading_network");

            m_NewMsgTip = UIHelper.GetChild(m_MiddleRoot, "newChatMsgTip");
            //if (messageChannel != null)
            //{
            //    messageChannel.SetMsgMenuGameObject(anim);
            //    messageChannel.SetNewMsgTipGameObject(m_NewMsgTip);
            //}
            m_NewMsgTip.SetActive(false);
            InitInputFieldByDeviceType();
        }

        /// <summary>
        /// 显示或隐藏网络状态提示
        /// </summary>
        public void ShowOrHideNetworkStateNotice(bool active)
        {
            if (m_NetworkStateBar != null && m_NetworkStateBar.activeSelf != active)
            {
                m_NetworkStateBar.SetActive(active);
            }
        }

        /// <summary>
        /// 显示或隐藏Loading提示特效
        /// </summary>
        /// <param name="active"></param>
        public void ShowOrHideLoadingEffect(bool active)
        {
            if (m_LoadingEffect != null && m_LoadingEffect.activeSelf != active)
            {
                m_LoadingEffect.SetActive(active);
            }
        }

        /// <summary>
        /// 初始化各种事件监听
        /// </summary>
        private void InitListeners()
        {
            //关闭
            AddListener(TFW.EventTriggerType.Click, "animation/btn_close", OnBtnCloseClick);
            //关闭
            AddListener(TFW.EventTriggerType.Click, "animation/SetBtn", OnSetBtnClick);

            //发送消息
            AddListener(TFW.EventTriggerType.Click, "animation/middle/ChatInputUI/cont_input/btn_send", OnBtnSendClick);

            AddListener(TFW.EventTriggerType.Click, "animation/middle/ChatInputUI/cont_input/btn_send_world", OnBtnWorldSendClick);

            //Emoji
            m_BtnSmileListener.AddListener(TFW.EventTriggerType.Click, OnBtnEmojiClick);

            AddListener(TFW.EventTriggerType.Down, _mainChannelScroll.gameObject, (x, y) =>
            {
                messageChannel?.CloseMsgMenu();
            });
            _allianceHelpRewardObj = UIHelper.GetChild(m_MiddleRoot, "ChatInputUI/chatCanvas/GiftBtn");
            AddListener(TFW.EventTriggerType.Click, _allianceHelpRewardObj, OnAllianceHelpRewardClick);
        }

        #region

        private void OnAllianceHelpRewardClick(GameObject arg0, PointerEventData arg1)
        {
            LAllianceNewAchievement.I.OpenRewardList();
        }

        #endregion


        /// <summary>
        /// 被禁言了，不能输入 
        /// </summary>
        private void SetInputeable(bool active)
        {
            m_InputField.gameObject.SetActive(active);
            _unInputeableTxt.gameObject.SetActive(!active);

            if (!active)
            {
                var time = LSocialReportMgr.I.FormateMuteTime;
                var key = string.Format(LocalizationMgr.GetUIString("Player_Message_Report_Notice"), "", time);
                _unInputeableTxt.text = key;
            }

        }

        /// <summary>
        /// 初始化频道tabs
        /// </summary>
        private void InitTabs()
        {
            var inst = this;
            m_LeftTabRoot = GetComponent<TFWTabGroup>("animation/left/tfw_tab_group");
            for (int i = 1; i <= leftTabCount; ++i)
            {
                var tab = GetComponent<TFWTab>("animation/left/tfw_tab_group/tabs/tab" + i);
                var tagIndex = ConvertUtils.GetIntFromString(tab.TabTag);
                m_LeftTabRoot.AddSingleTabClickEvent(tab, (target, args) => { inst.OnTabClick((ChatTabs)tagIndex, true); });
            }
        }

        /// <summary>
        /// Emoji按钮点击
        /// </summary>
        private void OnBtnEmojiClick(GameObject arg0, PointerEventData arg1)
        {
            if (LSocialReportMgr.I.ImIReported)
            {
                var time = LSocialReportMgr.I.FormateMuteTime;
                //var time = LSocialReportMgr.I.FormateHoursTime;
                var key = string.Format(LocalizationMgr.GetUIString("Player_Message_Report_Notice"), "", time);
                FloatTips.I.FloatMsg(key);
                return;
            }

            if (this._emojiPanelUI.isShown)
                this._emojiPanelUI.Hide();
            else
                this._emojiPanelUI.Show();
        }

        long sendTime = 0;

        private void OnBtnWorldSendClick(GameObject arg0, PointerEventData arg1)
        {
            var str = m_InputField.text;
            if (str == CPETaskCommand)
            {
                //CpeTaskUI.Layer.OpenSafely();
                return;
            }

            if (!string.IsNullOrEmpty(str))
            {

                if (LSocialReportMgr.I.ImIReported)
                {
                    var time = LSocialReportMgr.I.FormateHoursTime;
                    FloatTips.I.FloatMsg(string.Format(LocalizationMgr.GetUIString("Player_Message_Report_Text"), time));
                    return;
                }


                if (!UIChatHelper.I.ChatMessageCheck(ChatPayloadType.ChatTextType, new object[] { str }))
                {
                    D.Chat.Error?.Log("发送消息 失败！");
                    return;
                }

                if (sendTime < GameTime.Time)
                {

                    if (PlayerAssetsMgr.I.GetItemCountByID(4000000) > 0)
                    {
                        sendTime = GameTime.Time + 1000 * 5;
                        OnBtnSendClick(null, null);
                    }
                    else
                    {
                        ItemStoreMgr.I.OpenGetPropPop(Logic.ItemData.GetItemCfg(4000000), 1);
                        //ShopMgr.I.OpenShopPanel(ShopTabEnum.ItemStore);
                    }
                }
                else
                {
                    var msg = string.Format(LocalizationMgr.GetUIString("Chat_Input_CD_Time"), Mathf.CeilToInt((sendTime - GameTime.Time) / 1000f));
                    FloatTips.I.FloatMsg(msg);
                }


            }
            else
            {
                FloatTips.I.FloatMsg(LocalizationMgr.GetUIString("MAIL_neet_content"));
            }
        }

        /// <summary>
        /// 发送消息
        /// </summary>
        private void OnBtnSendClick(GameObject arg0, PointerEventData arg1)
        {
            ////BILog.UserClick($"{Name}.{arg0.name}", $"{Name}", $"Name");
            //Send();
            if (this._emojiPanelUI.isShown)
            {
                this._emojiPanelUI.Hide();
            }

            OnSendClickChat();
        }

        protected override void OnHidden()
        {
            messageChannel?.CloseMsgMenu();

            if (_emojiPanelUI && this._emojiPanelUI.isShown)
                this._emojiPanelUI.Hide();

            base.OnHidden();

            m_UIChatData = null;

            FrameUpdateMgr.UnregisterRenderingUpdate(Name);

        }

        protected internal override void OnShowComplete()
        {
            base.OnShowComplete();
            MainUIReturn();

        }

        private void MainUIReturn()
        {
            //CurrentChannel = null;

            if (m_UIChatData != null 
              ) // && m_UIChatData.CurrentTabKey != MainMenuType.ALLIANCE && m_UIChatData.CurrentTabKey != MainMenuType.NONE
            {
                EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)m_UIChatData.CurrentTabKey);

                //TODO 补丁处理，在内城界面跳转聊天时，如果关闭聊天界面，会花屏
                //if (GameData.I.MainData.CurrMenuType == MainMenuType.PRISON)
                //    SceneManager.I.UpdateCityCameraEnable();

                //if (m_UIChatData.CurrentTabKey == MainMenuType.PRISON
                //     && GameData.I.MainData.CurrMenuType == MainMenuType.PRISON)
                //{
                //    //zjw 20210507 fix bug
                //    //44关解锁聊天按钮时，从城堡界面打开聊天界面，聊天界面返回无法返回城市界面
                //    //特殊处理
                //    //SceneManager1.I.ShowScene<UIMainCity>();
                //}
            }
            else
            {
                EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.CITY);
            }
        }

        protected override void OnDestroyed()
        {
            //if (messageChannel != null)
            //    messageChannel.StopControl();
            UnRegisterFunctions();
            I = null;
            if (_emojiPanelUI)
                _emojiPanelUI.onInputCustomEmoji -= OnInputCustomizedEmoji;
        }

        protected override void RegisterEvent()
        {

            base.RegisterEvent();
            EventMgr.RegisterEvent(TEventType.AllianceInfoDetail, OnAllianceInfoDetail, this);
            EventMgr.RegisterEvent(TEventType.RedPointNumChange, OnRedPointNumChange, this);

            //EventMgr.RegisterEvent(TEventType.NumMailUnreadChanged, OnNumMailRedPointRefresh, this);
            EventMgr.RegisterEvent(TEventType.ChatAddMention, OnAddMention, "UIChat");

            EventMgr.RegisterEvent(TEventType.OnCloseAllianceMainUI, OnCloseAllianceMainUI, "UIChat");
            EventMgr.RegisterEvent(TEventType.OnImMuted, OnImMuted, "UIChat");
            EventMgr.RegisterEvent(TEventType.CancelMyMute, OnCancelMyMute, "UIChat");

            RegisterFunctionsPrivateChat();
            EventMgr.RegisterEvent(TEventType.ChangeChatTypeByMainMenu, OnChangeChatTypeByMainMenu, this);
            //MessageMgr.RegisterMsg<UnionRandomCoordAck>(this, OnUnionRandomCoordAck);
        }

        private void UnRegisterFunctions()
        {
            FrameUpdateMgr.UnregisterRenderingUpdate(Name);
            EventMgr.UnregisterEvent(TEventType.AllianceRallyDataUpdate, this);
            EventMgr.UnregisterEvent(TEventType.AllianceInfoDetail, this);
            //EventMgr.UnregisterEvent(TEventType.NumMailUnreadChanged, this);
            EventMgr.UnregisterEvent(TEventType.OnCloseAllianceMainUI, "UIChat");
            EventMgr.UnregisterEvent(TEventType.ChangeChatTypeByMainMenu, this);
            EventMgr.UnregisterEvent(TEventType.OnImMuted, "UIChat");
            EventMgr.UnregisterEvent(TEventType.CancelMyMute, "UIChat");

            UnRegisterFunctionsPrivateChat();


            //MessageMgr.UnregisterMsg<UnionRandomCoordAck>(this);

            EventMgr.UnregisterEvent(this);
        }
        private void OnChangeChatTypeByMainMenu(object[] obj)
        {
            ChatTabs _chatTab = (ChatTabs)obj[0];

            SwitchTab(_chatTab);
            if (_chatTab == ChatTabs.Alliance)
            {
                if (LPlayer.I.UnionID == 0)
                {
                    FloatTips.I.FloatMsg(LocalizationMgr.GetUIString("UNION_not_joined_union"));//你还未加入联盟
                }

            }

        }

        /// <summary>
        /// 当输入Emoji
        /// </summary>
        private void OnInputCustomizedEmoji((int, Sprite) obj)
        {
            var emojiPackID = obj.Item1;
            var emojiSprite = obj.Item2;

            if (emojiSprite == null)
            {
                // D.Warning?.Assert(false);
                return;
            }

            //富文本显示格式为[图集ID#表情名称]
            var inputStr = string.Format("[{0}#{1}]", emojiPackID, emojiSprite.name);

            m_InputField.text += inputStr;
        }

        /// <summary>
        /// 当增加@其他玩家
        /// </summary>
        private void OnAddMention(object[] obj)
        {
            // D.Warning?.Assert(obj.Length == 1);

            if (obj == null || obj.Length == 0)
                return;
            string inputStr;

            if (obj[0] is UnifyPlayerInfo playerInfo)
            {
                inputStr = LChatHelper.CreateMentionTextString(playerInfo);
                TrySetAllianceHelpPlayerID(playerInfo.ID);
            }
            else if (obj[0] is PlayerData playerData)
            {
                inputStr = LChatHelper.CreateMentionTextString(playerData);
                TrySetAllianceHelpPlayerID(playerData.playerID);
            }
            else
            {
                return;
            }

            if (obj.Length == 2)
            {
                var uid = (long)obj[1];
                LAllianceNewAchievement.I.HelpDataUid = uid;
            }

            m_InputField.text += inputStr + " "; // @消息后面自动补一个空格用作分隔

            OnInputFieldValueChanged(null);
        }

        void TrySetAllianceHelpPlayerID(long playerID)
        {
            if (m_FormerTab != ChatTabs.Alliance)
                return;
            LAllianceNewAchievement.I.PlayerID = playerID;
        }

        /// <summary>
        /// UIChat我的禁言被解除了
        /// </summary>
        private void OnCancelMyMute(object[] args)
        {
            SetInputeable(true);
        }

        /// <summary>
        /// UIChat我被禁言了
        /// </summary>
        private void OnImMuted(object[] args)
        {
            var time = LSocialReportMgr.I.FormateMuteTime;
            //var time = LSocialReportMgr.I.FormateHoursTime;
            FloatTips.I.FloatMsg(string.Format(LocalizationMgr.GetUIString("Player_Message_Report_Notice"), "", time));
            SetInputeable(false);
        }

        /// <summary>
        /// 关闭联盟主UI
        /// </summary>
        private void OnCloseAllianceMainUI(object[] args)
        {
            //OnTabClick(CurrentTab);
            if (NextTab == ChatTabs.Alliance)
            {
                OnClickAllianceTab();
            }
        }

        private void OnRedPointNumChange(object[] args)
        {
            var moduleID = (int)args[0];
            var nodeName = (string)args[1];

            D.Chat.Error?.Log($"聊天红点 {moduleID} : {nodeName}");
        }

        private void RenderingUpdate(float dt)
        {
            //NewMenuUpdate();
            UpdateLoadingTip(dt);
        }

        private void UpdateLoadingTip(float dt)
        {
            //如果断线10秒内又连接上了，就把计时器重新赋值
            if (m_ChatOfflineTimeoutTimer < m_ChatOfflineTimeout)
                m_ChatOfflineTimeoutTimer = m_ChatOfflineTimeout;

            var shouldShowNetworkStateNotice = false;
            var shouldShowLoadingEffect = false;

            if (!shouldShowNetworkStateNotice)
            {
                // CurrentChannel为空时视为已连接
                if (CurrentChannel != null)
                {
                    // CurrentChannel不为空但查不到，这有可能是私聊UI在实际创建私聊频道前设置的"占位符"，这时默认不显示LoadingEffect
                    if ((CurrentChannel.channelType == ChatChannelType.ChatPrivateChannel && LChatRoomMgr.I.HasPrivateChatRoom(CurrentChannel.playerId))
                        || LChatRoomMgr.I.HasServerChatRoom(CurrentChannel))
                    {
                        // 聊天消息列表为空，且没有拉取到所有历史记录时，视为拉取中，
                        // 这是始终显示LoadingEffect
                        if (messageChannel.GetChatShowDataCount() == 0
                         && !LChatMessageMgr.I.HasPulledAllHistoryForChannel(CurrentChannel))
                        {
                            shouldShowLoadingEffect = true;
                        }
                    }
                }
            }

            ShowOrHideNetworkStateNotice(shouldShowNetworkStateNotice);
            ShowOrHideLoadingEffect(shouldShowLoadingEffect);
        }

        /// <summary>
        /// 显示加入联盟按钮
        /// </summary>
        private void ShowJoinAllianceBtn(bool isShow)
        {
            //if (isShow)
            //{
            //    NTimer.CountDown(0.1f, () =>
            //    {
            //        SwitchTab(ChatTabs.Server);
            //        //m_FormerTab = ChatTabs.World;

            //        var ui = PopupManager.I.FindPopup<UIAllianceWel_k1>();
            //        if (ui == null)
            //            PopupManager.I.ShowLayer<UIAllianceWel_k1>();
            //    });
            //}
            _joinAlliancePage.SetActive(isShow);
        }

        private void UpdateCurrentPrivateChannel(ChatContext chatroom)
        {
            this.UpdateCurrentChannel(chatroom);
        }

        /// <summary>
        /// 更新当前的频道
        /// </summary>
        private void UpdateCurrentChannel(ChatContext channelId)
        {
            //点击联盟页签就重置联盟红点
            if (channelId == ChatContext.Union)
            {
                LSocialRedPoint.I.SocialRedPointModule.SetRedPointNumOfNode(channelId.roomName, 0);
            }

            CurrentChannel = channelId;

            D.Chat.Debug?.Log($"UpdateCurrentChannel()调用栈 {CurrentChannel} : {m_MainChatCanvas.name}");
            if (CurrentChannel != null)
            {
                if (m_MainChatCanvas != null && !m_MainChatCanvas.activeSelf)
                    m_MainChatCanvas.SetActive(true);

                switch (NextTab)
                {
                    case ChatTabs.Alliance:
                        SetPrivateChatTopUIActive(false);
                        break;

                    case ChatTabs.PrivateChat:
                        SetPrivateChatTopUIActive(true);
                        break;

                    default:
                        SetPrivateChatTopUIActive(false);
                        break;
                }

                //todo
                messageChannel.SetData(null, CurrentChannel);

                // this._pinnedRedEnvelope.SetData(CurrentChannel.channelType);
            }
            else
            {
                SetPrivateChatTopUIActive(false);
            }

            SetInputUIActive(CurrentChannel != null);
        }

        private void OnInputFieldValueChanged(string _)
        {
            CheckAndLimitInputField();

            if (Application.isMobilePlatform)
                SetInputTextInCeilPhone(m_InputField.text);

            if (LAllianceNewAchievement.I.PlayerID != 0)
            {
                if (m_InputField.text.Count() == 0)
                    LAllianceNewAchievement.I.PlayerID = -1;
            }
        }
        private void CheckAndLimitInputField()
        {
            var limit = ChatChannelInputLimit;

            string result;
            if (UIHelper.LimitStringWithDisplayLength(m_InputField.text, limit, out result, true))
                m_InputField.SetTextWithoutNotify(result);
        }

        /// <summary>
        /// 设置手机的文本输入
        /// </summary>
        private void SetInputTextInCeilPhone(string text)
        {
            if (m_InputTextInCeilPhone == null)
                return;

            m_InputTextInCeilPhone.text = UIChatHelper.I.ConvertToDisplayText(
                string.IsNullOrEmpty(text) ? "" : text);
        }

        /// <summary>
        /// 开关输入区域
        /// </summary>
        private void SetInputUIActive(bool active)
        {
            if (_sendBtnObj != null)
                _sendBtnObj.SetActive(active && NextTab != ChatTabs.CrossServer);

            if (_sendBtnObjWorld != null)
                _sendBtnObjWorld.SetActive(active && NextTab == ChatTabs.CrossServer);

            if (m_InputFieldRoot != null)
                if (m_InputFieldRoot.gameObject.activeSelf != active) m_InputFieldRoot.gameObject.SetActive(active);

            // if (m_BtnSmileListener != null)
            //     if (m_BtnSmileListener.gameObject.activeSelf != active) m_BtnSmileListener.gameObject.SetActive(active);
            m_BtnSmileListener?.gameObject.SetActive(false);


            //if (m_BtnCaptureListener.gameObject.activeSelf != active) m_BtnCaptureListener.gameObject.SetActive(active);
        }

        /// <summary>
        /// 切换页签
        /// </summary>
        public void SwitchTab(ChatTabs tab)
        {
            //m_LeftTabRoot.SetDefaultTabOn(((int)tab).ToString());
            if (this._emojiPanelUI.isShown)
            {
                this._emojiPanelUI.Hide();
            }
            m_LeftTabRoot.TurnTabOn(((int)tab).ToString());
            OnTabClick(tab);
        }

        /// <summary>
        /// tab点击处理,
        /// </summary>
        public void OnTabClick(ChatTabs tab, bool mainMenuChange = false, bool force = false)
        {
            if (tab == ChatTabs.Alliance)
            {
                //聊天开放时联盟如果没开放，不允许切换

                var isOpen = true;//  MainFunctionOpenUtils.IsMenuFunctionOpen(MainMenuType.ALLIANCE, true);

                if (!isOpen)
                {

                    //UITools.PopTips(LocalizationMgr.Format("Actv_KingRoad_Task_CastleLevelReaches", MetaConfig.NewUnlockUnionBtn));
                    m_LeftTabRoot.TurnTabOn(((int)CurrentTab).ToString());
                    return;
                }
            }
            if (tab == ChatTabs.Legion)
            {
                // 判断是否开启了kvk，开启了才能切换到阵营聊天
                bool isOpen = LPlayer.I.FactionId > 0;
                if (!isOpen)
                {
                    FloatTips.I.FloatMsg(LocalizationMgr.Format("Kvk_not_Match_Chat"));
                    m_LeftTabRoot.TurnTabOn(((int)CurrentTab).ToString());
                    return;
                }
            }
            if (tab == ChatTabs.Luminary)
            {
                // 判断是否开启了光明战场，开启了才能切换到阵营聊天
                bool isLuminaryOpen = LCrossServer.I.CurrSceneType == SceneType.SceneTypeLB;
                if (!isLuminaryOpen)
                {
                    FloatTips.I.FloatMsg(LocalizationMgr.Format("Kvk_not_Match_Chat"));
                    m_LeftTabRoot.TurnTabOn(((int)CurrentTab).ToString());
                    return;
                }
            }

            NextTab = tab;

            if (tab == ChatTabs.CrossServer)
            {
                //if (!GameData.I.LevelData.CanToDoByGameLevel(MetaConfig.CrossServeOpen))
                //{
                //    FloatTips.I.FloatMsg(LocalizationMgr.Format("DEMO_37", MetaConfig.CrossServeOpen));
                //    m_LeftTabRoot.TurnTabOn(((int)CurrentTab).ToString());
                //    return;
                //}

                if (string.IsNullOrEmpty(ChatMigrationHelper.WorldChatRoomID))
                {
                    m_LeftTabRoot.TurnTabOn(((int)CurrentTab).ToString());
                    return;
                }

            }

            //D.Chat.Error?.Log("chat->UIChat.OnTabClick(),tab:" + tab.ToString());
            if (tab != CurrentTab || force)
            {
                DeActiveAllPages(tab);
                switch (tab)
                {
                    case ChatTabs.Server://tab1
                        m_PrivateChatTarget = null;
                        OnClickWorldTab();
                        if (mainMenuChange) EventMgr.FireEvent(TEventType.ChangeChatTypeByChatMenu, 0);
                        break;
                    case ChatTabs.Alliance://tab4
                        OnClickAllianceTab();
                        m_PrivateChatTarget = null;
                        if (mainMenuChange) EventMgr.FireEvent(TEventType.ChangeChatTypeByChatMenu, 1);
                        break;
                    case ChatTabs.PrivateChat:
                        OnClickPrivateChatTab();
                        if (mainMenuChange) EventMgr.FireEvent(TEventType.ChangeChatTypeByChatMenu, 2);
                        break;
                    case ChatTabs.CrossServer:
                        m_PrivateChatTarget = null;
                        OnClickCrossServerTab();
                        if (mainMenuChange) EventMgr.FireEvent(TEventType.ChangeChatTypeByChatMenu, 3);
                        break;
                    case ChatTabs.Legion://军团wjk
                        m_PrivateChatTarget = null;
                        OnClickLegionTab();
                        if (mainMenuChange) EventMgr.FireEvent(TEventType.ChangeChatTypeByChatMenu, 5);
                        break;
                    case ChatTabs.Luminary://军团wjk
                        m_PrivateChatTarget = null;
                        OnClickLuminaryTab();
                        if (mainMenuChange) EventMgr.FireEvent(TEventType.ChangeChatTypeByChatMenu, 6);
                        break;
                    default:
                        break;
                }
            }
            ////BILog.UserClick($"{Name}.{tab}", $"{CurrentTab}", $"{tab}");

            m_FormerTab = tab;

            //刷新不同频道的cd时间
            if (lastSendTimeCDChannel.TryGetValue((int)CurrentTab, out var time))
            {
                LChatMessageMgr.I.UpdateCD(time);
            }
            else
            {
                LChatMessageMgr.I.UpdateCD(5f);
            }
            //AdjustNewsContentSize();
        }

        // 为了节约性能已经不是关闭所有界面了，如果后面一定会开启某个界面就先不关掉。
        private void DeActiveAllPages(ChatTabs tab)
        {
            if (this._emojiPanelUI.isShown)
            {
                m_InputField.text = "";
                SetInputTextInCeilPhone("");
                this._emojiPanelUI.Hide();
            }

            m_PrivateChatsPanel?.gameObject.SetUIGameObjectActive(false);
            if (tab != ChatTabs.Server)
            {
                m_MainChatCanvas?.SetActive(false);
            }
            D.Chat.Debug?.Log("DeActiveAllPages");
        }

        /// <summary>
        /// 打开世界频道
        /// </summary>
        private void OnClickWorldTab()
        {
            //Update UI layout
            if (m_MainChatCanvas != null && !m_MainChatCanvas.activeSelf)
                m_MainChatCanvas.SetActive(true);

            UpdateCurrentChannel(ChatContext.Server);

            ShowJoinAllianceBtn(false);
        }

        private void OnClickCrossServerTab()
        {
            //Update UI layout
            m_MainChatCanvas.SetActive(true);

            UpdateCurrentChannel(ChatContext.World);

            ShowJoinAllianceBtn(false);
        }

        /// <summary>
        /// 打开联盟频道
        /// </summary>
        private void OnClickAllianceTab()
        {
            UpdateCurrentChannel(null);

            var isNotJoinedAlliance = LPlayer.I.UnionID == 0;
            ShowJoinAllianceBtn(isNotJoinedAlliance);

            if (!isNotJoinedAlliance)
            {
                m_MainChatCanvas.SetActive(true);

                UpdateCurrentChannel(ChatContext.Union);
            }
        }

        /// <summary>
        /// 打开军团频道wjk
        /// </summary>
        private void OnClickLegionTab()
        {
            m_MainChatCanvas.SetActive(true);
            //军团概念去除，改为阵营聊天
            UpdateCurrentChannel(ChatContext.Faction);

            ShowJoinAllianceBtn(false);
        }

        private void OnClickLuminaryTab()
        {
            m_MainChatCanvas.SetActive(true);
            UpdateCurrentChannel(ChatContext.Luminary);

            ShowJoinAllianceBtn(false);
        }

        /// <summary>
        /// 发送一般聊天内容
        /// </summary>
        public void OnSendClickChat()
        {
            var msgStr = m_InputField.text;

            if (msgStr == CPETaskCommand)
            {
                //CpeTaskUI.Layer.OpenSafely();
                return;
            }

            //点击发送消息
            //如果我被禁言了，则不能发消息
            if (LSocialReportMgr.I.ImIReported)
            {
                var time = LSocialReportMgr.I.FormateHoursTime;
                FloatTips.I.FloatMsg(string.Format(LocalizationMgr.GetUIString("Player_Message_Report_Text"), time));
                return;
            }

            //if (CurrentChannel != null && CurrentChannel.channelType == ChatChannelType.ChatPrivateChannel && LSkill.I.GetMainCityLevel() < Game.Config.MetaConfig.ChatUnlockLevel)
            if (CurrentChannel != null && CurrentChannel.channelType == ChatChannelType.ChatPrivateChannel)
            {
                int vipLv = 0;
                var vip = VipManager.I.GetSelfVipInfo();
                if (vip != null)
                {
                    vipLv = vip.vipLv;
                }

                if (vipLv < Game.Config.MetaConfig.VipLevel_ChatNoLimit)
                {
                    if (VersionRewardMgr.I != null && VersionRewardMgr.I.currentVersion > 0)
                    {
                        if (LSkill.I.GetMainCityLevel() < Game.Config.MetaConfig.KVKChatUnlockLevel)
                        {
                            string popInfo = string.Format(LocalizationMgr.Get("ChatCityLvlLimit"), UIStringUtils.FormatIntegerByLanguage(int.Parse(Game.Config.MetaConfig.KVKChatUnlockLevel.ToString())), UIStringUtils.FormatIntegerByLanguage(int.Parse(Game.Config.MetaConfig.VipLevel_ChatNoLimit.ToString())));

                            UITools.PopTips(popInfo);
                            return;
                        }
                    }
                    else
                    {
                        if (LSkill.I.GetMainCityLevel() < Game.Config.MetaConfig.ChatUnlockLevel)
                        {
                            string popInfo = string.Format(LocalizationMgr.Get("ChatCityLvlLimit"), UIStringUtils.FormatIntegerByLanguage(int.Parse(Game.Config.MetaConfig.ChatUnlockLevel.ToString())), UIStringUtils.FormatIntegerByLanguage(int.Parse(Game.Config.MetaConfig.VipLevel_ChatNoLimit.ToString())));

                            UITools.PopTips(popInfo);
                            return;
                        }
                    }

                }
                else//肯定满足要求
                {

                }
            }

            SendDefaultMessage();
        }

        /// <summary>
        /// 发送默认消息
        /// </summary>
        private void SendDefaultMessage()
        {
            //if (!UnlockMgr.I.CheckUnlock(UnlockFuncEnum.Chat))
            //{
            //    //聊天功能未解锁不可以发出
            //    SetInputFieldText("");
            //    return;
            //}
            
            var msgStr = m_InputField.text;

            msgStr = LChatHelper.ConvertTagToHref(msgStr);

            var msgType = (NextTab == ChatTabs.PrivateChat) ? MsgTypeMigration.PeerToPeer : MsgTypeMigration.PeerToGroup;

            if (SendTextChatMessage(msgType, new object[] { msgStr }))
            {
                var gameEvent = new K3.GameEvent();
                gameEvent.EventKey = $"send_message";
                K3.K3GameEvent.I.BiLog(gameEvent, "custom_social");

                SetInputFieldText("");
            }
        }

        /// <summary>
        /// 真正地发送文本聊天消息
        /// </summary>
        private bool SendTextChatMessage(MsgTypeMigration msgType, object[] contentData)
        {
            if (LChat.I.enableChatInternalLog)
            {
                D.Chat.Error?.Log("发送消息了~·!!");
            }

            if (!UIChatHelper.I.ChatMessageCheck(ChatPayloadType.ChatTextType, contentData))
            {
                D.Chat.Error?.Log("发送消息 失败！");
                return false;
            }

            bool sendSuccess = LChatMessageMgr.I.SendTextMessage(CurrentChannel, msgType, (string)contentData[0]);

            if (CurrentChannel.channelType == ChatChannelType.ChatPrivateChannel)
            {
                var playerId = CurrentChannel.playerId;
                if (playerId > 0)
                {
                    if (LFriend.I.IsFriend(playerId) != null)
                    {
                        LFriend.I.FriendUpdateContactReq(playerId);//更新好友聊天节点时间
                    }
                }
            }

            return sendSuccess;
        }

        private void InitInputFieldByDeviceType()
        {
            if (Application.isMobilePlatform)
            {
                if (!m_InputTextRootInCeilPhone.activeSelf)
                    m_InputTextRootInCeilPhone.SetActive(true);
                SetInputTextInCeilPhone(m_InputField.text);
                var color = m_InputField.textComponent.color;
                m_InputField.textComponent.color = new Color(color.r, color.g, color.b, 0);
            }
            else
            {
                if (m_InputTextRootInCeilPhone.activeSelf)
                    m_InputTextRootInCeilPhone.SetActive(false);
            }
        }

        /// <summary>
        /// 设置输入文本框的内容
        /// </summary>
        private void SetInputFieldText(string txt)
        {
            m_InputField.text = txt;
        }
        /// <summary>
        /// 关闭聊天
        /// </summary>
        private void OnBtnCloseClick(GameObject gameObject, PointerEventData args)
        {
            // 清空私聊目标数据
            m_PrivateChatTarget = null;

            // 位于私聊Tab时：如果当前正处于多选状态，则退出多选状态，而不是关闭聊天UI
            if (NextTab == ChatTabs.PrivateChat)
            {
                if (m_PrivateChatsPanel != null && m_PrivateChatsPanel.currentState == UIChatPrivateListCanvas.State.MultiSelect)
                {
                    m_PrivateChatsPanel.ChangeState(UIChatPrivateListCanvas.State.Normal);
                    return;
                }
            }
            Close();

            //弹出其他界面
            //Pop();
            ////TODO:::::
            //var ui = WndMgr.Get<UIChat>();
            //if (ui != null && ui.IsShow)
            //{
            //    //界面隐藏
            //    WndMgr.Hide<UIChat>("UIChat");
            //}
        }


        /// <summary>
        /// 设置按钮
        /// </summary>
        private void OnSetBtnClick(GameObject gameObject, PointerEventData args)
        {
            PopupManager.I.ShowPanel<UIChatSet>();
        }

        /// <summary>
        /// 是否需要显示联盟加入信息弹窗
        /// </summary>
        private bool isShowUIAllianceInfoPopState;

        /// <summary>
        /// 显示联盟加入信息弹窗状态
        /// </summary>
        /// <param name="state"></param>
        /// <returns></returns>
        public void SetUIAllianceInfoPopState(bool state)
        {
            isShowUIAllianceInfoPopState = state;
        }

        /// <summary>
        /// 联盟信息改变消息
        /// 请求拉取其他联盟信息推送会进行 触发
        /// 在这做个联盟招募时从世界聊天打开联盟加入弹窗 动态请求联盟信息 然后显示弹窗 
        /// </summary>
        /// <param name="obj"></param>
        private void OnAllianceInfoDetail(object[] obj)
        {
            if (isShowUIAllianceInfoPopState)
            {
                isShowUIAllianceInfoPopState = false;
                var _unionInfo = LAllianceMgr.I.GetOtherUnionInfo();
                if (_unionInfo != null)
                {
                    PopupManager.I.ShowDialog<UI.Alliance.UIAllianceOtherInfo>(new UI.Alliance.UIAllianceOtherInfoData()
                    {
                        allianceInfo = _unionInfo
                    });
                }
            }
        }

        [PopupEvent(TEventType.AllianceAchieve_HelpRedDotRefush)]
        void OnAllianceAchieve_HelpRedDotRefush(object[] arr)
        {
            _allianceHelpRewardRedWidget?.SetData(LAllianceNewAchievement.I.AllianceHelpRewardCount);
            if (_allianceHelpRewardText)
                _allianceHelpRewardText.text = $"{LAllianceNewAchievement.I.AllianceHelpRewardCount}/{MetaConfig.AllianceGoalAllianceRewardShow}";
        }

        protected internal override void OnCloseComplete()
        {
            base.OnCloseComplete();
            EventMgr.UnregisterEvent(TEventType.AllianceAchieve_HelpRedDotRefush, this);
            EventMgr.UnregisterEvent(TEventType.AllianceJoining, this);
            // AdEventGameMgr.I.SpeedUp(true);
            UIChat.onClose?.Invoke();
        }

        /// <summary>
        /// 刷新显示联盟成员阶级Icon信息
        /// </summary>
        /// <param name="icon"></param>
        /// <param name="id"></param>
        public static void UpdateAllianceMemberClassIcon(TFWImage icon, long playerID)
        {
            if (icon == null)
                return;

            LAllianceMgr.I.UpdateMemberClassIcon(icon, playerID);
        }

        /// <summary>
        /// 刷新显示王座战Icon信息
        /// </summary>
        /// <param name="icon"></param>
        /// <param name="id"></param>
        public static void UpdateThroneWarPositionIcon(TFWImage icon, int id)
        {
            if (icon == null)
                return;

            if (id > 0)
            {
                icon.enabled = true;
                LThroneWar.I.UpdatePositionIcon(icon, id);
            }
            else
            {
                icon.enabled = false;
            }
        }

        /// <summary>
        /// 刷新显示联盟R4官职Icon信息
        /// </summary>
        /// <param name="icon"></param>
        /// <param name="id"></param>
        public static void UpdateOfficialPositionIcon(TFWImage icon, int id)
        {
            if (icon == null)
                return;

            if (id > 0)
            {
                icon.enabled = true;

                LAllianceMain.I.UpdatePositionIcon(icon, id,true);
            }
            else
            {
                icon.enabled = false;
            }
        }

        //加入联盟监听
        public void OnAllianceJoining(object[] obj)
        {
            OnClickAllianceTab();
        }

    }
}
