﻿

using Common;
using Cysharp.Threading.Tasks;
using DeepUI;
using DG.Tweening;
using Game.Data;
using K1;
using K3;
using Public;
using TFW.UI;
using UI.Utils;
using UnityEngine;
using UnityEngine.EventSystems;

namespace UI
{

    /// <summary>
    /// 菜单栏英雄按钮
    /// </summary>
    public class UIMainMenuHero : MonoBehaviour
    {

        #region 属性字段信息

        /// <summary>
        /// 可升级英雄数量对象root
        /// </summary>
        public GameObject heroNumObjRoot;
        public GameObject lockObj, unlockObj;
        /// <summary>
        ///  收集有奖励时候的tip节点
        /// </summary>
        public GameObject _collectAwardTipRoot;

        public TFWImage _collectBoxImg;
        #endregion


        #region 初始化

        private void Start()
        {
            UIBase.AddRemoveListener(TFW.EventTriggerType.Click, gameObject, ClickMenuBtn);
        }

        /// <summary>
        /// 数据初始化
        /// </summary>
        //protected override void Init()
        //{




        //    _collectAwardTipRoot = UIHelper.GetChild(Root, "CollectTipRoot");
        //    _collectBoxImg = UIHelper.GetComponent<TFWImage>(_collectAwardTipRoot, "BoxImg");
        //    _collectAwardTipRoot.SetActive(false);
        //}

        /// <summary>
        /// 事件注册
        /// </summary>
        protected   void OnEnable()
        { 
            //注册英雄升级完成事件
            EventMgr.RegisterEvent(TEventType.HeroLevelUpComplete, OnUpdateHeroInfo, this);
            //注册英雄数据刷新事件
            EventMgr.RegisterEvent(TEventType.RefreshHeroData, OnUpdateHeroInfo, this);
            //注册解锁最大英雄等级事件
            EventMgr.RegisterEvent(TEventType.PlayerUnlockMaxHeroLevelChange, OnUpdateHeroInfo, this);

            //EventMgr.RegisterEvent(TEventType.UIHeroStateChange, OnUIHeroStateChange, this);
            EventMgr.RegisterEvent(TEventType.OnSwitchMainUI, OnUpdateHeroInfo, this);

            EventMgr.RegisterEvent(TEventType.OnUnlockConditionChanged, UpdateMenuOpenState, this);

            OnUpdateHeroInfo(null);
            UpdateMenuOpenState(null);
        }

        private void OnDisable()
        {
            EventMgr.UnregisterEvent(this);
        }

        #endregion


        #region 数据刷新




        /// <summary>
        /// 退出菜单界面
        /// </summary>
        /// <param name="nextType"></param>
        //public override void ExitMenuPanel(MainMenuType nextType)
        //{
        //    base.ExitMenuPanel(nextType);

        //    //if (nextType != MainMenuType.ALLIANCE)
        //    //{
        //    //    PopupManager.I.ClosePopup<UIPlayerReName>();
        //    //}

        //    //WndMgr.Hide<UIHeroList>();
        //    PopupManager.I.ClosePopup<UIHeroList>();
        //    PopupManager.I.ClosePopup<UIHeroListWindow>();
        //    // PopupManager.I.ClosePopup<HeroShowView>();
        //    //PopupManager.I.ClosePopup<UIHero_GetHero>();
        //    //PopupManager.I.ClosePopup<UIDrawCard>();
        //    PopupManager.I.ClosePopup<UITreasureDetail>();
        //    //PopupManager.I.ClosePopup<UITreasurePop>();

        //    UpdateHeroRedInfo().Forget();
        //}

        public  void ClickMenuBtn(GameObject ga, PointerEventData po)
        {

            if (!MainFunctionOpenUtils.HeroOpenState)
            {
                //if (lockAnim != null)
                //    lockAnim.Play();
                FloatTips.I.FloatMsg(UnlockMgr.I.GetUnlockDesc(UnlockFuncEnum.Hero));

                return;
            }
             
            PopupManager.I.ShowLayer<UIHeroList>(new UIHeroListData()
            {
                isMain = true
            } );
             

            UpdateHeroRedInfo().Forget();
        }


        /// <summary>
        /// 刷新解锁状态
        /// </summary>
        public  void UpdateMenuOpenState(object[] objs)
        { 
            var isOpen = MainFunctionOpenUtils.HeroOpenState;
            lockObj.SetActive(!isOpen);
            unlockObj.SetActive(isOpen); 
        }

        /// <summary>
        /// 刷新英雄红点信息数据
        /// </summary>
        public async UniTaskVoid UpdateHeroRedInfo()
        {
            var heroList = HeroGameData.I.GetAllHeroData();

            var redPointNum = 0;

            if (!MainFunctionOpenUtils.HeroOpenState || heroList == null || heroList.Count <= 0 )
            {
                if (heroNumObjRoot != null)
                {
                    heroNumObjRoot.SetActive(false);
                }

                return;
            }

            foreach (var heroData in heroList)
            {
                if (heroData?.HeroCfg?.HeroType > 2)
                {
                    if (heroData.CanUpStarUp(out _, out _))
                    {
                        redPointNum++;
                    }
                    else if (await heroData.CanIntimacyUp())
                    {
                        redPointNum++;
                    }
                    else if (HeroGameData.I.UnlockSkins.TryGetValue(heroData.HeroCfg.Id, out var skins) && skins.Count > 0)
                    {
                        redPointNum++;
                    }
                }
            }

            /*var collectItem = await HeroGameData.I.GetCollectRedNum();
            _collectAwardTipRoot.SetActive(collectItem.Item1 > 0);
            redPointNum = redPointNum + collectItem.Item1;
            if (collectItem.Item2 != null)
            {
                UITools.SetImageBySpriteName(_collectBoxImg, collectItem.Item2.Icon);
            }*/

            if (redPointNum <= 0)
            {
                var collectItem = await HeroGameData.I.GetCollectRedNum();
                redPointNum = collectItem.Item1;
            }


            if (redPointNum > 0)
            {
                var isInHero = false;// GameData.I.MainData.CurrMenuType == MainMenuType.HERO;

                if (heroNumObjRoot != null)
                {
                    heroNumObjRoot.SetActive(!isInHero);
                }

                //if (heroNumText != null)
                //{
                //    heroNumText.text = redPointNum.ToString();
                //}
            }
            else
            {
                if (heroNumObjRoot != null)
                {
                    heroNumObjRoot.SetActive(false);
                }
            }


            return;

             
        }


        #endregion


        #region 事件监听

        /// <summary>
        /// 刷新英雄升级完成信息
        /// </summary>
        /// <param name="objs"></param>
        private void OnUpdateHeroInfo(object[] objs)
        {
            //刷新英雄红点信息
            UpdateHeroRedInfo().Forget();
        }
          

        #endregion



    }
}
