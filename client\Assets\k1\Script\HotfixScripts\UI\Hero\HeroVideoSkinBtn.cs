﻿using Common;
using Game.Data;
using K3;
using Public;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using TFW;
using TFW.Localization;
using TFW.UI;
using UI;
using UnityEngine;

public class HeroVideoSkinBtn  : UIGridItem
{
   
    public string videoName { get; private set; }

    private HeroData mHeroData;

    public GameObject selectObj;
    public GameObject unlockObj, lockObj;
      
    public GameObject downloading;
    public TFWText downLoadingText;

    public GameObject redPoint;

    private bool VideoUnlock;

    public GameObject btnPlay;
    private string selVideoName;
    public void SetData(HeroData heroData,string _videoName,int unlockLevel,int curLv,string _SelVideoName)
    { 
        mHeroData = heroData;
        videoName = _videoName;
        selVideoName = _SelVideoName;

        var unlock = unlockLevel <= curLv;

        VideoUnlock =unlock;

        lockObj.SetActive(!unlock);
        unlockObj.SetActive(unlock);

        //if (!string.IsNullOrEmpty(skinName))
        //{
        //    UIBase.AddRemoveListener(TFW.EventTriggerType.Click, btnPlay, (x, y) =>
        //    {
        //        if (unlock)
        //        { 
        //            ChapterTaskMgr.I.SetHeroSkin(heroData.HeroCfg.Id, skinName, true);
        //        }
        //        else
        //        {
                    
        //        }
        //    });

        //    CheckVideToDownLoad(videoName);
        //}

        if (!string.IsNullOrEmpty(videoName))
        {
            UIBase.AddRemoveListener(TFW.EventTriggerType.Click, gameObject, (x, y) =>
            {
                if (unlock)
                {
                    if (CheckVideToDownLoad(videoName))
                    {
                        if (HeroGameData.I.UnlockSkins.TryGetValue(mHeroData.HeroCfg.Id, out var videoSkins))
                        {
                            videoSkins.Remove(videoName);
                        }

                        EventMgr.FireEvent(TEventType.HeroVedioSkinSelect, videoName);
                        //ChapterTaskMgr.I.SetHeroVedioSkin(heroData.HeroCfg.Id, videoName);
                    }
                    else
                    {
                        //下载中
                        FloatTips.I.FloatMsg("Hero_intimacy_tips_01".ToLocal());
                    }
                }
                else
                { 
                    FloatTips.I.FloatMsg(LocalizationMgr.Format("Hero_intimacy_desc_06", FindStrFormat(unlockLevel)));
                }
            });


            UIBase.AddRemoveListener(TFW.EventTriggerType.Click, btnPlay, (x, y) =>
            {
                if (unlock)
                {
                    if (CheckVideToDownLoad(videoName))
                    {
                        if (HeroGameData.I.UnlockSkins.TryGetValue(mHeroData.HeroCfg.Id, out var videoSkins))
                        {
                            videoSkins.Remove(videoName);
                        }

                        ChapterTaskMgr.I.SetHeroVedioSkin(heroData.HeroCfg.Id, videoName);
                    }
                    else
                    {
                        //下载中
                        FloatTips.I.FloatMsg("Hero_intimacy_tips_01".ToLocal());
                    }
                }
                else
                {
                    FloatTips.I.FloatMsg(LocalizationMgr.Format("Hero_intimacy_desc_06", FindStrFormat(unlockLevel)));
                }
            });

            if (VideoUnlock)
            {
                CheckVideToDownLoad(videoName);
            }
            else
            {
                UIHelper.SafeSetActive(downloading, false);
            }
        }

        RefreshSkin();

        EventMgr.RegisterEvent(TEventType.HeroSkinSelect, (objs) => {
            RefreshSkin();
        }, this);

        EventMgr.RegisterEvent(TEventType.HeroVideoDownLoaded, (objs) => 
        {
            if (VideoUnlock)
            {
                CheckVideToDownLoad(videoName);
            }
        }, this);
    }


    private string FindStrFormat(int unlockLv)
    { 
        var format = string.Empty;
        var list = Cfg.C.CHeroIntimacy.RawList();
        var target = list.Find(a => a.Level == unlockLv);
        if (target != null)
        {
            format = target.Icon;
        }

        return format;
    }

    private bool CheckVideToDownLoad(string _videoName)
    {
        if (!string.IsNullOrEmpty(_videoName) && !PlayAddressableVideo.LocalVideo.Contains(_videoName))
        {
            var _localFilePath = Path.Combine(Application.persistentDataPath, _videoName);

            if (!File.Exists(_localFilePath))
            {
                HeroGameData.I.AddToDownloadQueue(_videoName); 
                UIHelper.SafeSetActive(downloading, true); 
                return false;
            }
            else
            {
                UIHelper.SafeSetActive(downloading, false);
                return true;
            }
        }
        else
        {
            UIHelper.SafeSetActive(downloading, false);
            return true;
        }
    }

    private void OnDisable()
    {
        EventMgr.UnregisterEvent(this);
    }

    private void LateUpdate()
    {
        if(string.IsNullOrEmpty(videoName)) { return; }

        if (downloading!=null  && downloading.activeSelf)
        {
            if (HeroGameData.I.DownLoadVieoProcess.TryGetValue(videoName, out var process))
            {
                downLoadingText.text = $"{Mathf.FloorToInt(process * 100)}%";
            }
            else
            {
                downLoadingText.text = $"0%";
            }
        }
    }



    private void RefreshSkin()
    {
        //var curHeroVideoSkin = ChapterTaskMgr.I.GetHeroVedioSkin(mHeroData.HeroCfg.Id);
        //var curHeroSkinSkin = ChapterTaskMgr.I.GetHeroSkin(mHeroData.HeroCfg.Id);

        if (HeroGameData.I.UnlockSkins.TryGetValue(mHeroData.HeroCfg.Id, out var skins) && !string.IsNullOrEmpty(videoName) && skins.Contains(videoName))
        {
            if(redPoint)
            redPoint.SetActive(true);
        }
        else
        {
            if (redPoint)
                redPoint.SetActive(false);
        }
        

        //if (!string.IsNullOrEmpty(skinName))
        //{
        //    //选中的video
        //   //UIHelper.SafeSetActive(mObj,true);
        //    UIHelper.SafeSetActive( selectObj,curHeroSkinSkin == skinName && string.IsNullOrEmpty(curHeroVideoSkin));
        //}
        //else
        if (!string.IsNullOrEmpty(videoName))
        {
            //UIHelper.SafeSetActive(mObj, true);
            UIHelper.SafeSetActive(selectObj, selVideoName == videoName);
        }
        else
        {
            //UIHelper.SafeSetActive(mObj, false);
        }
    }
}
