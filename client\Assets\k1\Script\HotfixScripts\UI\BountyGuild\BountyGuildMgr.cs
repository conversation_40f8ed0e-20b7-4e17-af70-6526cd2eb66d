﻿using Common;
using cspb;
using Game.Config;
using GameState;
using Logic;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Cysharp.Threading.Tasks;
using Game.Data;
using UnityEngine;
using UI;
using K1;

namespace BountyGuild
{
    public class BountyGuildMgr : THelper.Ins<BountyGuildMgr>, ILoginCallback, ILogoutCallback
    {
        public static bool debugShowTaskId = false;

        public event Action onDataChanged;

        public bool isUnlock { get; private set; }

        public IReadOnlyDictionary<int, BountyGuildTask> tasks => this._tasks;

        public int redDots
        {
            get
            {
                if (this.isUnlock)
                {
                    int count = 0;
                    foreach (var kv in tasks)
                    {
                        var value = kv.Value;
                        if (StartTaskFilter(value) || ClaimFilter(value))
                        {
                            count++;
                        }
                    }
                    return count;
                }
                return 0;
            }
        }

        readonly Dictionary<int, BountyGuildTask> _tasks = new Dictionary<int, BountyGuildTask>();

        readonly HashSet<int> _idHash = new HashSet<int>();

        #region 任务状态过滤
        public static bool ClaimFilter(BountyGuildTask x)
        {
            return x.taskState == 2;
        }

        public static bool AutoLineupFilter(BountyGuildTask x)
        {
            return x.taskState == 0 && BountyGuildMgr.IsTaskLineupEmpty(x);
        }

        public static bool StartTaskFilter(BountyGuildTask x)
        {
            return x.taskState == 0 && !BountyGuildMgr.IsTaskLineupEmpty(x);
        }

        public static bool CancelTaskFilter(BountyGuildTask x)
        {
            return x.taskState == 1;
        }
        #endregion

        /// <summary>
        /// 计算成功率暴击率
        /// </summary>
        public static (int successRate, int criticalRate) Calculate(IReadOnlyList<int> lineupHeros, Cfg.G.CBountyTaskList taskConfig)
        {
            if (lineupHeros != null)
            {
                var sum = 0;
                var sucRs = Game.Config.MetaConfig.BountyTaksHeroSuc;
                var index = 0;
                foreach (var heroId in lineupHeros)
                {
                    if (heroId > 0)
                    {
                        var heroData = Game.Data.HeroGameData.I.GetHeroByCfgId(heroId);
                        var quality = heroData.HeroCfg.HeroType; // 从 1 开始
                        var suc = sucRs[quality - 1];
                        sum += suc;

                        var slotAtt = taskConfig.GetSlotAttribute(index);
                        if (slotAtt == heroData.HeroCfg.SoldiersType)
                        {
                            sum += (int)Game.Config.MetaConfig.BountyTaksNeedMeet;
                        }
                    }

                    index++;
                }

                var cra = sum - 100;
                return (sum > 100 ? 100 : sum, cra < 0 ? 0 : cra);
            }
            else
            {
                return (0, 0);
            }
        }

        public static void Convert(IReadOnlyList<int> lineupHeros, Cfg.G.CBountyTaskList taskConfig, in Dictionary<int, cspb.BountyGuildPosInfo> target)
        {
            if (lineupHeros != null && taskConfig != null && target != null)
            {
                target.Clear();
                var index = 1;
                foreach (var heroId in lineupHeros)
                {
                    var targetAttr = taskConfig.GetSlotAttribute(index - 1);

                    var v = new cspb.BountyGuildPosInfo();
                    v.heroId = heroId;

                    var heroData = Game.Data.HeroGameData.I.GetHeroByCfgId(heroId);
                    v.artMatch = heroData != null ? targetAttr == heroData.HeroCfg.SoldiersType : false;

                    target[index] = v;
                    index++;
                }
            }
            else
            {
                D.Error?.Log($"lineupHeros {lineupHeros} && taskConfig {taskConfig} && target {target}");
            }
        }

        /// <summary>
        /// 上阵为空
        /// </summary>
        public static bool IsTaskLineupEmpty(BountyGuildTask task)
        {
            if (task != null)
            {
                foreach (var posInfo in task.posInfo.Values)
                {
                    if (posInfo.heroId > 0)
                    {
                        return false;
                    }
                }
            }
            return true;
        }

        /// <summary>
        /// 自动上阵
        /// </summary>
        public async UniTaskVoid DoAutoAllLineup(params BountyGuildTask[] tasks)
        {
            if (tasks != null)
            {
                var dic = new Dictionary<int, BountyGuildUpInfo>();
                this._idHash.Clear();
                foreach (var task in tasks)
                {
                    Cfg.G.CBountyTaskList config = await task.GetConfig();
                    BountyGuildUpInfo lineupInfo = new BountyGuildUpInfo();

                    foreach (var soldierType in config.slotAttributes)
                    {
                        if (soldierType >= 0)
                        {
                            var targetHeros = Game.Data.HeroGameData.I.GetHeroData(soldierType)
                                .Where(x => !this._idHash.Contains(x.HeroCfg.Id) && !this.IsHeroBusy(x.HeroCfg.Id));
                            var first = targetHeros.FirstOrDefault();
                            if (first != null)
                            {
                                this._idHash.Add(first.HeroCfg.Id);
                                lineupInfo.heroId.Add(first.HeroCfg.Id);
                            }
                            else
                            {
                                var allHeroFirst = HeroGameData.I
                                    .GetAllHeroDataBySort(HeroSortType.QUALITY)
                                    .FirstOrDefault(x => !this._idHash.Contains(x.HeroCfg.Id) && !this.IsHeroBusy(x.HeroCfg.Id));
                                if (allHeroFirst != null)
                                {
                                    this._idHash.Add(allHeroFirst.HeroCfg.Id);
                                    lineupInfo.heroId.Add(allHeroFirst.HeroCfg.Id);
                                }
                                else
                                {
                                    //有多少上阵多少
                                    break;
                                    //D.Error?.Log($"英雄数不足？");
                                    //return;
                                }
                            }
                        }
                        else
                        {
                            D.Error?.Log($"{nameof(Cfg.G.CBountyTaskList)} id：{config.Id} param 配错了");
                            return;
                        }
                    }

                    dic[task.uniqueId] = lineupInfo;
                }

                if (dic.Count > 0)
                {
                    BountyGuildMgr.I.RequestLineup(dic);
                }
            }
        }

        /// <summary>
        /// 英雄是否已经在某个任务中
        /// </summary>
        public bool IsHeroBusy(int heroId, int? excludeTask = null)
        {
            foreach (var pair in this._tasks)
            {
                if (!excludeTask.HasValue || pair.Key != excludeTask.Value)
                {
                    var taskInfo = pair.Value;
                    foreach (var posInfo in taskInfo.posInfo.Values)
                    {
                        if (posInfo.heroId == heroId)
                        {
                            return true;
                        }
                    }
                }
            }
            return false;
        }

        public void AddHero(BountyGuildTask task, int position)
        {

        }

        public void RemoveHero(BountyGuildTask task, int position)
        {

        }

        /// <summary>
        /// 查询任务id 0 表示查询全部 （当在任务界面倒计时为0时，可单独查询某个任务进行刷新）
        /// </summary>
        public void RequestTaskInfo(int taskId)
        {
            MessageMgr.Send(new BountyGuildReq() { pos = taskId });
        }

        /// <summary>
        /// 上阵
        /// </summary>
        public void RequestLineup(IEnumerable<KeyValuePair<int, BountyGuildUpInfo>> lineup)
        {
            var req = new BountyGuildUpSlotReq();

            foreach (var item in lineup)
            {
                req.upSlot.Add(item.Key, item.Value);
            }

            MessageMgr.Send(req);
        }

        /// <summary>
        /// 开始任务
        /// </summary>
        public void RequestStartTask(bool isAdvanced, params int[] taskId)
        {
            var req = new BountyGuildGetTaskReq()
            {
                isAdvanced = isAdvanced
            };
            req.posList.AddRange(taskId);

            MessageMgr.Send(req);
        }

        /// <summary>
        /// 赏金公会任务刷新
        /// </summary>
        public void RequestRefreshTask(params int[] taskId)
        {
            var req = new BountyGuildRefreshReq();
            req.posList.AddRange(taskId);

            MessageMgr.Send(req);
        }

        /// <summary>
        /// 赏金公会任务取消
        /// </summary>
        public void RequestCancelTask(params int[] taskId)
        {
            // 二次确认弹窗
            UI.UIMsgBox.Push(UI.EMsgBoxType.two_nc,
                TFW.Localization.LocalizationMgr.Get("Gender_Confirm_Tips_Title"),
                TFW.Localization.LocalizationMgr.Get("BountyTask_cancel_desc"),
                new UI.MsgBoxBtnParam
                {
                    str = TFW.Localization.LocalizationMgr.Get("Player_infos_change_name_btn_01"),
                    func = (o) => {
                        var req = new BountyGuildCancelReq();
                        req.posList.AddRange(taskId);

                        MessageMgr.Send(req);
                    }
                },
                new UI.MsgBoxBtnParam
                {
                    str = TFW.Localization.LocalizationMgr.Get("PLAYER_settings_btn_cancel")
                },
                UI.ButtonColorGroup.RedBlue);
        }

        /// <summary>
        /// 赏金公会任务领奖
        /// </summary>
        public void RequestClaimTaskReward(params int[] taskId)
        {
            var req = new BountyGuildRewardReq();
            req.posList.AddRange(taskId);

            MessageMgr.Send(req);
        }

        /// <summary>
        /// 赏金公会解锁
        /// </summary>
        public void RequestUnlock()
        {
            var req = new BountyGuildRewardReq();

            MessageMgr.Send(req);
        }

        void UpdateTaskDatas(Dictionary<int, BountyGuildTask> tasks, bool isFull)
        {
            if (isFull)
            {
                this._tasks.Clear();

                foreach (var item in tasks)
                {
                    item.Value.uniqueId = item.Key;
                    this._tasks.Add(item.Key, item.Value);
                }
            }
            else
            {
                foreach (var item in tasks)
                {
                    item.Value.uniqueId = item.Key;
                    this._tasks[item.Key] = item.Value;
                }
            }
        }

        #region Ack & Ntf
        /// <summary>
        /// 赏金公会查询
        /// </summary>
        void OnBountyGuildAck(BountyGuildAck obj)
        {
            this.isUnlock = obj.isUnLock;
            this.UpdateTaskDatas(obj.taskInfo, obj.isAll);

            this.onDataChanged?.Invoke();
        }

        /// <summary>
        /// 赏金公会英雄上阵
        /// </summary>
        void OnBountyGuildUpSlotAck(BountyGuildUpSlotAck obj)
        {
            if (Logic.Common.CheckErrorCode(obj.errCode, true))
            {
                this.UpdateTaskDatas(obj.taskInfo, false);

                this.onDataChanged?.Invoke();
            }
        }

        /// <summary>
        /// 赏金公会任务接取
        /// </summary>
        void OnBountyGuildGetTaskAck(BountyGuildGetTaskAck obj)
        {
            if (Logic.Common.CheckErrorCode(obj.errCode, true))
            {
                this.UpdateTaskDatas(obj.taskInfo, false);

                this.onDataChanged?.Invoke();
            }
        }

        /// <summary>
        /// 赏金公会任务刷新
        /// </summary>
        void OnBountyGuildRefreshAck(BountyGuildRefreshAck obj)
        {
            if (Logic.Common.CheckErrorCode(obj.errCode, true))
            {
                this.UpdateTaskDatas(obj.taskInfo, false);

                this.onDataChanged?.Invoke();
            }
        }

        /// <summary>
        /// 赏金公会任务取消
        /// </summary>
        void OnBountyGuildCancelAck(BountyGuildCancelAck obj)
        {
            if (Logic.Common.CheckErrorCode(obj.errCode, true))
            {
                this.UpdateTaskDatas(obj.taskInfo, false);

                this.onDataChanged?.Invoke();
            }
        }

        /// <summary>
        /// 赏金公会任务领奖
        /// </summary>
        async UniTaskVoid OnBountyGuildRewardAck(BountyGuildRewardAck obj)
        {
            if (Logic.Common.CheckErrorCode(obj.errCode, true))
            {
                var cfgDic = await Cfg.C.CBountyTaskList.RawDictAsync();
                var datas = obj.rdInfo.Values
                    .Where(x =>
                    {
                        if (x != null && cfgDic.ContainsKey(x.rewardId))
                        {
                            return true;
                        }
                        else
                        {
                            D.Warning?.Log($"任务配置 {x?.rewardId} 没找到");
                            return false;
                        }
                    })
                    .Select(x => (cfgDic[x.rewardId], x));

                BountyGuildTaskRewardUI.Show(datas);

                this.onDataChanged?.Invoke();

                //赏金任务，操作全部领取后，返回主城界面时
                FivePraiseMgr.I.UpdateCanOpenFivePraise();
            }
        }

        /// <summary>
        /// 赏金公会解锁
        /// </summary>
        void OnBountyGuildUnLockAck(BountyGuildUnLockAck obj)
        {
            if (Logic.Common.CheckErrorCode(obj.errCode, true))
            {
                this.isUnlock = obj.isUnLock;

                this.UpdateTaskDatas(obj.rdInfo, obj.isAll);

                this.onDataChanged?.Invoke();
            }
        }

        void OnBountyGuildTaskNtf(BountyGuildTaskNtf obj)
        {
            this.UpdateTaskDatas(obj.taskInfo, false);

            this.onDataChanged?.Invoke();
        }
        #endregion

        void OnGameLevelChange(object[] args)
        {
            CheckData();
        }

        private void CheckData()
        {
            //var shouldOpen = LPlayer.I.GetMainCityLevel() >= MetaConfig.BountyTaksOpenLevelNew;

            //if (shouldOpen && !this.isUnlock)
            //{
            //    //赏金公会系统解锁
            //    MessageMgr.Send(new BountyGuildUnLockReq());
            //}
        }

        Cysharp.Threading.Tasks.UniTask ILoginCallback.OnLogin()
        {
            //EventMgr.RegisterEvent(TEventType.GameLevelChange, OnGameLevelChange, this); //不需要关卡通关解锁 ，走切换到City后 判断是否解锁拉取数据
            EventMgr.RegisterEvent(TEventType.OnSwitchMainUI, (a) => {
                if (a?.Length > 0)
                {
                    //var type = (MainMenuType)a[0];
                    //if (type == MainMenuType.PRISON)
                    //{
                        this.CheckData();
                    //}
                }
            }, this);

            MessageMgr.RegisterMsg<BountyGuildAck>(this, this.OnBountyGuildAck);
            MessageMgr.RegisterMsg<BountyGuildUpSlotAck>(this, this.OnBountyGuildUpSlotAck);
            MessageMgr.RegisterMsg<BountyGuildGetTaskAck>(this, this.OnBountyGuildGetTaskAck);
            MessageMgr.RegisterMsg<BountyGuildRefreshAck>(this, this.OnBountyGuildRefreshAck);
            MessageMgr.RegisterMsg<BountyGuildCancelAck>(this, this.OnBountyGuildCancelAck);
            MessageMgr.RegisterMsg<BountyGuildRewardAck>(this, this.OnBountyGuildRewardAck);
            MessageMgr.RegisterMsg<BountyGuildUnLockAck>(this, this.OnBountyGuildUnLockAck);
            MessageMgr.RegisterMsg<BountyGuildTaskNtf>(this, this.OnBountyGuildTaskNtf );

            //RequestTaskInfo(0);
            
            return Cysharp.Threading.Tasks.UniTask.CompletedTask;
        }

        Cysharp.Threading.Tasks.UniTask ILogoutCallback.OnLogout()
        {
            EventMgr.UnregisterEvent(this);

            MessageMgr.UnregisterMsg<BountyGuildAck>(this);
            MessageMgr.UnregisterMsg<BountyGuildUpSlotAck>(this);
            MessageMgr.UnregisterMsg<BountyGuildGetTaskAck>(this);
            MessageMgr.UnregisterMsg<BountyGuildRefreshAck>(this);
            MessageMgr.UnregisterMsg<BountyGuildCancelAck>(this);
            MessageMgr.UnregisterMsg<BountyGuildRewardAck>(this);
            MessageMgr.UnregisterMsg<BountyGuildUnLockAck>(this);
            MessageMgr.UnregisterMsg<BountyGuildTaskNtf>(this);

            return Cysharp.Threading.Tasks.UniTask.CompletedTask;
        }
    }
}