﻿








using System.Collections.Generic;
using Common;
using Config;
using cspb;
using TFW.Localization;
using THelper;
using UI;
using TFW;
using System;
using Game.Data;
using Game.Config;
using K3;

namespace Logic
{
    /// <summary>
    /// 联盟成员帮助 Logic
    /// </summary>
    public class LAllianceHelp : Ins<LAllianceHelp>
    {
        #region Property
        /// <summary>
        /// 是否被初始化
        /// </summary>
        public bool HasInit { get; private set; } = false;

        /// <summary>
        /// 当前状态
        /// </summary>
        public UnionHelpState CurState { get; private set; }

        /// <summary>
        /// 能量
        /// </summary>
        private Asset EnergyAsset { get; set; }

        #endregion

        #region Interface

        public void Init()
        {
            if (HasInit) return;
            HasInit = true;
            InitData();
        }

        public void DeInit()
        {
            HasInit = false;
            CurState = UnionHelpState.None;
            DeInitNet();
            DeInitData();
        }

        /// <summary>
        /// 退出联盟
        /// </summary>
        public void ExitUnion()
        {
            AllianceGameData.I.AllianceHelp.Clear();
            CurState = UnionHelpState.None;
        }

        /// <summary>
        /// 加入联盟
        /// </summary>
        public void JoinUnion()
        {
            ReqUnionHelpList();
            ReqUnionHelpCount();
        }

        /// <summary>
        /// 扣除状态时 当前剩余能量
        /// </summary>
        public long RemianEnergy
        {
            get;
            private set;
        }

        /// <summary>
        /// 扣除状态时 当前剩余多少时间扣完
        /// </summary>
        public long RemianTime
        {
            get;

            private set;
        }

        /// <summary>
        /// 能量进度
        /// </summary>
        public float EnergyProgress
        {
            get;
            private set;
        }


        /// <summary>
        /// 已经申请帮助中
        /// </summary>
        public bool IsHelping
        {
            get
            {
                return AllianceGameData.I.AllianceHelp.IsHelping;
            }
        }

        /// <summary>
        /// 检查状态
        /// </summary>
        private void CheckState(float time)
        {
            EnergyAsset = PlayerAssetsMgr.I.GetHelpEnergyRestore();
            if (EnergyAsset != null)
            {
                //如果在扣除阶段,得到的是>0 当前剩余多少时间扣完
                RemianTime = EnergyAsset.grow.lastGrowTs - GameTime.Time;

                //D.Debug?.Log("remianTime={0},asset.grow.lastGrowTs={1},GameTime.Time={2}", RemianTime, EnergyAsset.grow.lastGrowTs, GameTime.Time);
                //var cur = TFW.Common.ConvertUnixTimeStampToDateTime(GameTime.Time);
                //var cur1 = TFW.Common.ConvertUnixTimeStampToDateTime(EnergyAsset.grow.lastGrowTs);
                //D.Error?.Log("lastGrowTs=" + cur1.ToString("yyyy-MM-dd hh:mm:ss") + " cur=" + cur.ToString("yyyy-MM-dd hh:mm:ss"));

                var per = (float)PlayerBuffMgr.I.Compute(PlayerBuffProperty.AllianceHelpCd, MetaConfig.AllianceHelpPointsPerSecond);
                if (RemianTime >= 1000 && LPlayer.I.IsPlayerInUnion())
                {
                    ChangeState(UnionHelpState.SpeedUping);

                    //在扣除阶段
                    //消耗 growMax 能量 需要花费的总时间
                    var costTime = (EnergyAsset.grow.growMax / per) * 1000;

                    //能量进度
                    EnergyProgress = RemianTime / costTime;
                    //var cur2 = TFW.Common.ConvertUnixTimeStampToDateTime((long)startTime);
                    RemianEnergy = (long)(RemianTime / 1000 * per);


                    //开始扣除的时间
                    //var startTime = EnergyAsset.grow.lastGrowTs - costTime;
                    //D.Error?.Log("startTime=" + cur2.ToString("yyyy-MM-dd hh:mm:ss") + ",Remian=" + RemianEnergy);
                    //D.Error?.Log("扣除阶段=EnergyProgress={0},RemianEnergy={1}", EnergyProgress, RemianEnergy);
                }
                else
                {
                    RemianTime = (long)(EnergyAsset.grow.growMax / per) * 1000;
                    //D.Error?.Log("能量未空数据" + RemianTime + "aaa" + EnergyAsset.grow.growMax + "ass" +   per);

                    //在恢复阶段
                    PlayerAssetsMgr.I.GetShowHelpEnergy(out var currentEnergy, out var maxEnergy);
                    if (currentEnergy >= maxEnergy)
                    {
                        ChangeState(UnionHelpState.CanUse);
                    }
                    else
                    {
                        ChangeState(UnionHelpState.Restoreing);
                    }
                }
            }
        }

        public long GetNormalSpeed()
        {
            var normalSpeed = 1;
            return (long)PlayerBuffMgr.I.Compute(PlayerBuffProperty.AllianceHelpSpeed, normalSpeed);
        }

        public bool CheckSelfAllianceHelp()
        {

            List<long> del = new List<long>();
            foreach (var item in AllianceGameData.I.AllianceHelp.SelfRequest.Values)
            {
                if (item.type != 0 || LAllianceHelp.I.CurState != UnionHelpState.CanUse)
                {
                    del.Add(item.ID);
                }
            }
            if (del.Count > 0)
                return true;
            else
                return false;
        }

        /// <summary>
        /// 状态
        /// </summary>
        /// <param name="newState"></param>
        public void ChangeState(UnionHelpState newState)
        {
            if (CurState != newState)
            {
                if (CurState == UnionHelpState.SpeedUping)
                {
                    //如果上个状态是加速中,那么代表加速结束了,直接重置加速
                    // AdEventGameMgr.I.UnionHelpSpeedUp(0, true);
                }
                CurState = newState;
                switch (newState)
                {
                    case UnionHelpState.None:
                        break;
                    case UnionHelpState.Restoreing:
                        break;
                    case UnionHelpState.SpeedUping:
                        if (EnergyAsset != null)
                        {
                            RemianEnergy = EnergyAsset.grow.growMax;
                        }

                        //false 代表 广告事件结束
                        // if (!GameData.I.AdEventData.CheckEventState(AdEventEnum.SpeedGame))
                        // {
                        //     //FloatTips.I.FloatMsg(LocalizationMgr.Get("Alliance_Help_Toast3"));
                        //     //开启加速
                        //     AdEventGameMgr.I.UnionHelpSpeedUp((int)GetNormalSpeed());
                        // }
                        // else
                        // {
                        //     D.Debug?.Log("CheckAdEventIsOver = false");
                        // }
                        break;
                    case UnionHelpState.CanUse:
                        break;
                    default:
                        break;
                }

                //D.Error?.Log("curState={0}", newState);
                EventMgr.FireEvent(TEventType.UnionHelpStateChange);
            }
        }

        /// <summary>
        /// 拉取玩家已经被帮助过的队列ID列表
        /// </summary>
        public void ReqHelpAll()
        {
            List<long> ids = new List<long>();
            foreach (var item in this.HelpList)
            {
                if (item.Helped)
                {
                    continue;
                }
                ids.Add(item.ID);
            }
            if (ids.Count > 0)
            {
                ReqUnionHelp(ids);
            }
        }

        /// <summary>
        /// 拉取玩家已经被帮助过的队列ID列表
        /// </summary>
        public void ReqUnionHelpList()
        {
            MessageMgr.Send(new UnionHelpListReq());
        }

        /// <summary>
        /// 请求联盟成员帮助
        /// </summary>
        public void ReqUnionHelpApply(int id)
        {
            if (!LSwitchMgr.I.IsFunctionOpen(SwitchConfig.Alliance_Help))
            {
                FloatTips.I.FloatMsg(LocalizationMgr.Get("DEMO_30"));
                return;
            }
            // if (!LSkill.I.CanToDoByCityLevel_ConfigID((int)MetaConfig.UnlockUnionBtn,true))
            if (!UnlockMgr.I.CheckUnlock(UnlockFuncEnum.Alliance))
            {
                return;
            }
            var isNotJoinedAlliance = LPlayer.I.UnionID == 0;
            if (isNotJoinedAlliance)
            {
                LAllianceMgr.I.ShowAllianceMain(null);
                //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.ALLIANCE);
            }
            var req = new UnionHelpApplyReq();
            req.type = id;
            MessageMgr.Send(req);
        }

        /// <summary>
        /// 帮助所有联盟成员
        /// </summary>
        public void ReqUnionHelp(List<long> ids)
        {
            var req = new UnionHelpReq();
            foreach (var item in ids)
            {
                req.IDs.Add(item);
            }
            MessageMgr.Send(req);
        }

        #endregion

        #region Method
        private void DeInitData()
        {
            EnergyAsset = null;
            AllianceGameData.I.AllianceHelp.Clear();
            FrameUpdateMgr.UnregisterPerSecondFunListUpdate(this);
        }

        /// <summary>
        /// Init Data
        /// </summary>
        void InitData()
        {
            //EnergyAsset = PlayerAssetsMgr.I.GetHelpEnergyRestore(); 次时还获取不到
           
            FrameUpdateMgr.RegisterPerSecondFunListUpdate(this, CheckState);
        }

        public void ReqData()
        {
            ReqUnionHelpList();
            ReqUnionHelpCount();
            CheckState(0);
        }

        private void DeInitNet()
        {
            MessageMgr.UnregisterMsg<UnionHelpApplyAck>(this);
            MessageMgr.UnregisterMsg<UnionHelpAck>(this);
            MessageMgr.UnregisterMsg<UnionHelpListAck>(this);
            MessageMgr.UnregisterMsg<CostSpeedUpPointAck>(this);
            MessageMgr.UnregisterMsg<UnionHelpCountAck>(this);
            MessageMgr.UnregisterMsg<UnionHelpNtf>(this);
            MessageMgr.UnregisterMsg<UnionHelpDelNtf>(this);
            MessageMgr.UnregisterMsg<UnionHelpNoticeNtf>(this);
            MessageMgr.UnregisterMsg<UnionHelpStatusNtf>(this);
        }

        /// <summary>
        /// Init Net
        /// </summary>
        public void InitNet()
        {
            MessageMgr.RegisterMsg<UnionHelpNoticeNtf>(this, OnUnionHelpNoticeNtf);
            MessageMgr.RegisterMsg<UnionHelpApplyAck>(this, OnUnionHelpApplyAck);
            MessageMgr.RegisterMsg<UnionHelpAck>(this, OnUnionHelpAck);
            MessageMgr.RegisterMsg<UnionHelpListAck>(this, OnUnionHelpListAck);
            MessageMgr.RegisterMsg<CostSpeedUpPointAck>(this, OnCostSpeedUpPointAck);
            MessageMgr.RegisterMsg<UnionHelpCountAck>(this, OnUnionHelpCountAck);

            MessageMgr.RegisterMsg<UnionHelpNtf>(this, OnUnionHelpNtf);
            MessageMgr.RegisterMsg<UnionHelpDelNtf>(this, OnUnionHelpDelNtf);
            MessageMgr.RegisterMsg<UnionHelpStatusNtf>(this, OnUnionHelpStatusNtf);
        }

        /// <summary>
        /// 帮助状态
        /// </summary>
        /// <param name="unionHelpStatusNtf"></param>
        public void OnUnionHelpStatusNtf(UnionHelpStatusNtf unionHelpStatusNtf)
        {
            AllianceGameData.I.AllianceHelp.UpdateHelpeing(unionHelpStatusNtf.helping);
            D.Debug?.Log("OnUnionHelpStatusNtf={0}", unionHelpStatusNtf.helping);
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="obj"></param>
        private void OnUnionHelpNoticeNtf(UnionHelpNoticeNtf obj)
        {
            //Alliance_Help_Toast5  [{0}]帮助你增加了{1}点能量  [{0}] helped you to gain {1} energy
            FloatTips.I.FloatMsg(LocalizationMgr.Format("Alliance_Help_Toast7", obj.name));
        }

        /// <summary>
        /// 已帮助次数
        /// </summary>
        /// <param name="obj"></param>
        public void ReqUnionHelpCount()
        {
            UnionHelpCountReq req = new UnionHelpCountReq();
            MessageMgr.Send(req);
        }

        public Cfg.G.CUnionHelp GetHelpCofigByItemId(int itemId)
        {
            foreach (var item in Cfg.C.CUnionHelp.RawDict())
            {
                if (item.Value.ItemId == itemId)
                {
                    return item.Value;
                }
            }
            return null;
        }

        /// <summary>
        /// 使用能量加速
        /// </summary>
        /// <param name="obj"></param>
        public void ReqCostSpeedUpPoint()
        {
            CostSpeedUpPointReq req = new CostSpeedUpPointReq();
            MessageMgr.Send(req);
        }

        /// <summary>
        /// 已帮助的删除时间 10分钟
        /// </summary>
        public const int DelTime = 10 * 60 * 1000;

        /// <summary>
        /// 已帮助的删除时间 10秒
        /// </summary>
        //public const int DelTime = 10000;

        /// <summary>
        /// [加速]已帮助的次数
        /// </summary>
        public int SpeedHelpedCount
        {
            get
            {
                return AllianceGameData.I.AllianceHelp.GetHelpCountById((int)UnionHelpType.AddSpeed);
            }
        }
        /// <summary>
        /// [金币]已帮助的次数
        /// </summary>
        public int CoinHelpedCount
        {
            get
            {
                return AllianceGameData.I.AllianceHelp.GetHelpCountById((int)UnionHelpType.Coin);
            }
        }
        /// <summary>
        /// [石头]已帮助的次数
        /// </summary>
        public int StoneHelpedCount
        {
            get
            {
                return AllianceGameData.I.AllianceHelp.GetHelpCountById((int)UnionHelpType.Stone);
            }
        }
        /// <summary>
        /// [木头]已帮助的次数
        /// </summary>
        public int WoodHelpedCount
        {
            get
            {
                return AllianceGameData.I.AllianceHelp.GetHelpCountById((int)UnionHelpType.Wood);
            }
        }
        /// <summary>
        /// [铁矿]已帮助的次数
        /// </summary>
        public int IronHelpedCount
        {
            get
            {
                return AllianceGameData.I.AllianceHelp.GetHelpCountById((int)UnionHelpType.Iron);
            }
        }
        /// <summary>
        /// 依靠帮助获得的联盟贡献点
        /// </summary>
        public int HelpedUnionPoint
        {
            get
            {
                return AllianceGameData.I.AllianceHelp.unionPoint;
            }
        }

        /// <summary>
        /// 帮助list
        /// </summary>
        public int HelpListCount
        {
            get
            {
                int count = 0;
                var dic = AllianceGameData.I.AllianceHelp.HelpDic;
                if (dic != null)
                {
                    foreach (var item in AllianceGameData.I.AllianceHelp.HelpDic)
                    {
                        //自己的不显示
                        var isSelf = item.Value.playerID == LPlayer.I.PlayerID;
                        if (isSelf)
                        {
                            continue;
                        }
                        if (item.Value.Helped || item.Value.IsNotHelp)
                        {
                            continue;
                        }
                        count++;
                    }
                }
                return count;
            }
        }

        /// <summary>
        /// 帮助list
        /// </summary>
        public List<UnionHelpInfo> HelpList
        {
            get
            {
                List<UnionHelpInfo> list = new List<UnionHelpInfo>();
                var dic = AllianceGameData.I.AllianceHelp.HelpDic;
                if (dic != null)
                {
                    foreach (var item in AllianceGameData.I.AllianceHelp.HelpDic)
                    {
                        if (item.Value.Helped)
                        {
                            //已帮助的过不再显示
                            continue;
                        }
                        if (item.Value.HelpedLastTime > 0)
                        {
                            //var time = GameTime.Time - item.Value.timestamp;
                            ////已帮助的过期时间不做处理
                            //if (time >= DelTime)
                            //{
                            //    continue;
                            //}
                            if (item.Value.IsNotHelp || item.Value.Helped)
                            {
                                continue;
                            }
                        }

                        list.Add(item.Value);
                    }
                    list.Sort(Sort);
                }
                return list;
            }
        }

        public List<UnionHelpInfo> SelfHelpList
        {
            get
            {
                List<UnionHelpInfo> list = new List<UnionHelpInfo>();
                var dic = AllianceGameData.I.AllianceHelp.SelfRequest;
                List<long> del = new List<long>();
                if (dic != null)
                {
                    foreach (var item in AllianceGameData.I.AllianceHelp.SelfRequest.Values)
                    {
                        if (item.type == 0 && CurState == UnionHelpState.CanUse)
                        {
                            del.Add(item.ID);
                        }
                        else
                        {
                            UnionHelpInfo info = new UnionHelpInfo();
                            info.ID = item.ID;
                            info.playerID = LPlayer.I.PlayerID;
                            info.progress = item.progress;
                            info.total = item.total;
                            info.type = item.type;
                            info.extra = item.extra;
                            list.Add(info);
                        }
                    }
                    list.Sort(Sort);
                }
                if (del.Count > 0)
                    AllianceGameData.I.AllianceHelp.Del(del);
                return list;
            }
        }

        /// <summary>
        /// 未帮助
        /// 已帮助
        /// </summary>
        /// <param name="a"></param>
        /// <param name="b"></param>
        /// <returns></returns>
        private int Sort(UnionHelpInfo a, UnionHelpInfo b)
        {
            var helpDic = AllianceGameData.I.AllianceHelp.HelpDic;
            if (!helpDic.ContainsKey(a.ID) && helpDic.ContainsKey(b.ID))
            {
                return -1;
            }
            else if (helpDic.ContainsKey(a.ID) && !helpDic.ContainsKey(b.ID))
            {
                return 1;
            }
            return 0;
        }
        #region Ack&Ntf

        /// <summary>
        /// 执行联盟帮助
        /// </summary>
        /// <param name="obj"></param>
        private void OnUnionHelpAck(UnionHelpAck obj)
        {
            D.Debug?.Log("OnUnionHelpAck obj.count={0}", obj.IDs.Count);
            var list = AllianceGameData.I.AllianceHelp.Help(obj.IDs);
            EventMgr.FireEvent(TEventType.UnionHelpAck, list);
            ReqUnionHelpCount();

            FloatTips.I.FloatMsg(LocalizationMgr.Get("Alliance_Help_Ally_All"));
            // if (list.Count > 0)
            // {
            //     //if (list.Count == 1)
            //     //{
            //     //    //Alliance_Help_Toast1  你帮助了{0}  You helped {0}
            //     //    FloatTips.I.FloatMsg(LocalizationMgr.Format("Alliance_Help_Toast1", list[0].head.name));
            //     //}
            //     //else
            //     //{
            //     //    for (int i = 0; i < list.Count; i++)
            //     //    {
            //     //        var tempItem = list[i];
            //     //        if (i > 0)
            //     //        {
            //     //            FloatTips.I.FloatMsg(LocalizationMgr.Format("Alliance_Help_Toast1", tempItem.head.name));
            //     //        }
            //     //        else
            //     //        {
            //     //            NTimer.CountDown(i, () =>
            //     //            {
            //     //                if (tempItem != null)
            //     //                {
            //     //                    FloatTips.I.FloatMsg(LocalizationMgr.Format("Alliance_Help_Toast1", tempItem.head.name));
            //     //                }
            //     //            });
            //     //        }
            //     //    }
            //
            //         //Alliance_Help_Toast2  你帮助了{0}和其他{1}名盟友  You helped {0} and other {1} allys
            //         //FloatTips.I.FloatMsg(LocalizationMgr.Format("Alliance_Help_Toast2", list[0].head.name, list.Count - 1));
            //     //}
            // }
            EventMgr.FireEvent(TEventType.RefreshAllianceHelpRed);
        }

        public int GetUnionHelpCountByType(int type)
        {
            int Count = 0;
            switch (type)
            {
                case (int)UnionHelpType.AddSpeed:
                    Count = SpeedHelpedCount;
                    break;
                case (int)UnionHelpType.Coin:
                    Count = CoinHelpedCount;
                    break;
                case (int)UnionHelpType.Stone:
                    Count = StoneHelpedCount;
                    break;
                case (int)UnionHelpType.Wood:
                    Count = WoodHelpedCount;
                    break;
                case (int)UnionHelpType.Iron:
                    Count = IronHelpedCount;
                    break;
                default:
                    D.Debug?.Log("UpdateHelpedCount UnionHelpType={0}", type);
                    break;
            }
            return Count;
        }

        /// <summary>
        /// 已帮助次数
        /// </summary>
        /// <param name="obj"></param>
        private void OnUnionHelpCountAck(UnionHelpCountAck obj)
        {
            // D.Debug?.Log("OnUnionHelpCountAck obj.count={0}", obj.count, obj.helping);
            // AllianceGameData.I.AllianceHelp.UpdateHelpeing(obj.helping);
            AllianceGameData.I.AllianceHelp.UpdateHelpedCount(obj);
            // D.Debug?.Log("OnUnionHelpCountAck obj.count={0}", obj.unionPoint, obj.helping);
            EventMgr.FireEvent(TEventType.UnionHelpCountAck, obj);
        }

        /// <summary>
        /// 请求联盟帮助
        /// </summary>
        /// <param name="obj"></param>
        private void OnUnionHelpApplyAck(UnionHelpApplyAck obj)
        {
            D.Debug?.Log("OnUnionHelpApplyAck obj.count={0}", obj.count, obj.errCode);
            if (obj.errCode == ErrCode.ErrCodeSuccess)
            {
                AllianceGameData.I.AllianceHelp.UpdateHelpeing(true);
                AllianceGameData.I.AllianceHelp.UpdateHelpedCount(obj.type, obj.count);
                FloatTips.I.FloatMsg(LocalizationMgr.Get("Alliance_Help_Toast4"));

                EventMgr.FireEvent(TEventType.UnionHelpApplyAck, obj);
            }
            else
            {
                //FloatTips.I.FloatErrcodeMsg(obj.errCode);
            }
            EventMgr.FireEvent(TEventType.UnionHelpApplyAck, obj);
        }


        /// <summary>
        /// 消耗加速值
        /// </summary>
        /// <param name="obj"></param>
        private void OnCostSpeedUpPointAck(CostSpeedUpPointAck obj)
        {
            D.Debug?.Log("OnCostSpeedUpPointAck obj.errCode={0}", obj.errCode);
            if (obj.errCode == ErrCode.ErrCodeSuccess)
            {
                CheckState(0);
                EventMgr.FireEvent(TEventType.CostSpeedUpPointAck);
            }
        }

        /// <summary>
        /// 联盟帮助信息删除
        /// </summary>
        /// <param name="obj"></param>
        private void OnUnionHelpDelNtf(UnionHelpDelNtf obj)
        {
            D.Debug?.Log("UnionHelpDelNtf obj.ids.Count={0}", obj.ids.Count);
            AllianceGameData.I.AllianceHelp.Del(obj.ids);
            EventMgr.FireEvent(TEventType.UnionHelpDelNtf, obj.ids);
            EventMgr.FireEvent(TEventType.RefreshAllianceHelpRed);
        }

        /// <summary>
        /// 联盟帮助信息推送
        /// </summary>
        /// <param name="obj"></param>
        private void OnUnionHelpNtf(UnionHelpNtf obj)
        {
            D.Debug?.Log("OnUnionHelpNtf obj.info.ID={0}", obj.info.ID);
            if (obj.info.playerID == LPlayer.I.PlayerID)
            {
                UnionHelpSelfInfo info = new UnionHelpSelfInfo();
                info.extra = obj.info.extra;
                info.ID = obj.info.ID;
                info.progress = obj.info.progress;
                info.total = obj.info.total;
                info.type = obj.info.type;
                AllianceGameData.I.AllianceHelp.Update(info);
            }
            else
            {
                AllianceGameData.I.AllianceHelp.Update(obj.info);
            }
            EventMgr.FireEvent(TEventType.UnionHelpNtf, obj.info.ID);
            EventMgr.FireEvent(TEventType.RefreshAllianceHelpRed);
        }

        /// <summary>
        /// 联盟帮助列表
        /// </summary>
        /// <param name="obj"></param>
        private void OnUnionHelpListAck(UnionHelpListAck obj)
        {
            D.Debug?.Log("UnionHelpListAck obj.info.Count={0}", obj.infos.Count);
            AllianceGameData.I.AllianceHelp.Update(obj.infos);
            AllianceGameData.I.AllianceHelp.Update(obj.selfs);
            EventMgr.FireEvent(TEventType.UnionHelpListAck);
            EventMgr.FireEvent(TEventType.RefreshAllianceHelpRed);
        }

        #endregion

        #endregion

        #region Field


        #endregion
    }

    /// <summary>
    /// 联盟帮助加速状态
    /// </summary>
    public enum UnionHelpState
    {
        /// <summary>
        /// 无
        /// </summary>
        None,
        /// <summary>
        /// 恢复中
        /// </summary>
        Restoreing,
        /// <summary>
        /// 加速中-扣除中
        /// </summary>
        SpeedUping,
        /// <summary>
        /// 可使用
        /// </summary>
        CanUse,
    }
    /// <summary>
    /// 联盟帮助类型
    /// </summary>
    public enum UnionHelpType
    {
        AddSpeed = 0,
        Coin = 1,
        Stone = 2,
        Wood = 3,
        Iron = 4,
    }
}