﻿
using Game.Data;
using Logic;
using Public;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TFW;
using TFW.Localization;
using TFW.UI;
using UI;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using static TFW.NTimer;
using Game.Config;
using Render;
using Common;
using DeepUI;
using Game.Utils;
using UnityEngine.UI.Extensions;
using Spine.Unity;
using Spine;
using Game.Effect;
using K3;

namespace UI
{

    public class UINewHeroShowParams : UIData
    {
        public List<Game.Data.HeroData> ShowHeroDatas;
        public bool first = false;
        public Action CloseCallBack;
    }

    [Popup("Hero/NewHeroShow", true)]
    public class NewHeroShow : BasePopup
    {
        [PopupField("Root")]
        private GameObject root;
        [PopupField("Root/BtnGet")]
        private GameObject click;
        [PopupField("Root/BtnGet")]
        private Animator m_ClickAnim;
        [PopupField("Root/BtnGet")]
        private AnimatorEvents m_AnimatorEvents;

        [PopupField("Root/BG/BG1")]
        private GameObject bg1;

        [PopupField("Root/BG/BG2")]
        private GameObject bg2;

        [PopupField("Root/BtnGet/TextBg")]
        private GameObject m_TextGroup;


        [PopupField("Root/Name/Image/HeroSpine/Main")]
        private Transform mainHeroSpine;
        //[PopupField("Root/Skill/Skill/Grid")]
        //private UIGrid skillGrid;
        //[PopupField("Root/Skill/MergeSkill/SkillIcon")]
        //private TFWImage mergeSkillIcon;
        //[PopupField("Root/Skill/MergeSkill/SkillIcon1")]
        //private TFWImage mergeSkillIcon1;
        //[PopupField("Root/Skill/MergeSkill/SkillIcon2")]
        //private TFWImage mergeSkillIcon2;
        [PopupField("Root/Name/Image/name")]
        private TFWText nameText;

        [PopupField("Root/Name/Image/attr/Icon")]
        private TFWImage heroIcon;

        [PopupField("Root/Name/Image/attr/Text")]
        private TFWText info0Text;

        //[PopupField("Root/Name/Image/number/Image/Text")]
        //private TFWText numberText;

        [PopupField("Root/Name/Image/ssr")]
        private TFWImage ssrImage;

        [PopupField("Root/Information/info1/Text")]
        private TFWText title1Text;

        [PopupField("Root/Information/info2/Text")]
        private TFWText title2Text;

        [PopupField("Root/Information/info3/Text")]
        private TFWText title3Text;

        [PopupField("Root/Information/info4/Text")]
        private TFWText title4Text;

        private Queue<Game.Data.HeroData> mShowHeroDatas = new Queue<Game.Data.HeroData>();
        private Game.Data.HeroData ToShowHero;
        private Action CloseCallBack;

        protected internal override void OnOpenStart()
        {
            var mData = Data as UINewHeroShowParams;
            if (mData != null && mData.CloseCallBack != null)
            {
                CloseCallBack = mData.CloseCallBack;
            }

            if (mData.ShowHeroDatas != null)
            {
                foreach (var item in mData.ShowHeroDatas)
                {
                    mShowHeroDatas.Enqueue(item);
                }
            }

            if (mShowHeroDatas.Count > 0)
            {
                ToShowHero = mShowHeroDatas.Dequeue();
            }
            else
            {
                NTimer.CountDown(0.2f, () => Close());
                return;
            }


            RefreshHeroUI();
        }

        private void RefreshHeroUI()
        {
            m_TextGroup.SetActive(true);
            m_AnimatorEvents.PlayAnim(m_ClickAnim, "UI_UIHeroSkinPreview_Reset", 0);

            //RefreshSkill();
            nameText.text = LocalizationMgr.Get(ToShowHero.HeroCfg.Name);

            var attr = UITools.GetAttributeDisplayKey(ToShowHero.HeroCfg.SoldiersType);
            UITools.SetImageBySpriteName(heroIcon, attr);

            SetHeroSpine(ToShowHero, mainHeroSpine);

            bg1.SetActive(ToShowHero.HeroCfg.HeroType == 3);
            bg2.SetActive(ToShowHero.HeroCfg.HeroType >= 4);


            UITools.SetImageBySpriteName(ssrImage, UITools.GetSSRQuality(ToShowHero.HeroCfg.HeroType));

            if (ToShowHero.HeroCfg.Info.Count > 0)
                info0Text.text = ToShowHero.HeroCfg.Info[0].ToLocal();

            if (ToShowHero.HeroCfg.Info.Count > 1)
                title1Text.text = $"{"unlockhero_des_title_2".ToLocal()}:{ToShowHero.HeroCfg.Info[1].ToLocal()}";

            if (ToShowHero.HeroCfg.Info.Count > 2)
                title2Text.text = $"{"unlockhero_des_title_3".ToLocal()}:{ToShowHero.HeroCfg.Info[2].ToLocal()}";

            if (ToShowHero.HeroCfg.Info.Count > 3)
                title3Text.text = $"{"unlockhero_des_title_4".ToLocal()}:{ToShowHero.HeroCfg.Info[3].ToLocal()}";

            if (ToShowHero.HeroCfg.Info.Count > 5)
                title4Text.text = $"{"unlockhero_des_title_6".ToLocal()}:{ToShowHero.HeroCfg.Info[5].ToLocal()}";
            // 暂时处理，时间是按照root上的状态机播放时间定义的
            NTimer.CountDown(1.1f, () =>
            {
                GameAudio.PlayAudio(718);
                CreateEffect();
            });
        }

        private void CreateEffect()
        {
            GameObject effect = ResourceMgr.LoadInstance("Assets/K1D7/Res/Effects/Prefabs/UIHeroShowView_01.prefab");
            effect.SetParent(root.transform);
            effect.transform.localPosition = new Vector3(0, -200, 0);
            effect.transform.localScale = Vector3.one * 1.0f;

            ParticleSystem[] ps = effect.gameObject.GetComponentsInChildren<ParticleSystem>();
            foreach (var item in ps)
            {
                ParticleSystemRenderer renderer = item.GetComponent<ParticleSystemRenderer>();
                renderer.sortingLayerID = CustomSortingLayer.Dialog;
            }

            GameObject.Destroy(effect, 2);
        }

        public void AddUnlockHeros(List<Game.Data.HeroData> ShowHeroDatas)
        {
            if (ShowHeroDatas?.Count > 0)
            {
                foreach (var item in ShowHeroDatas)
                {
                    mShowHeroDatas.Enqueue(item);
                }
            }
        }

        protected override void OnInit()
        {
            BindClickListener(click, OnClickBtn);
            //BindClickListener(jump, OnClickBtn);
        }

        private void OnClickBtn(GameObject arg0, PointerEventData arg1)
        {

            m_TextGroup.SetActive(false);

            GameAudio.PlayAudio(AudioConst.LipsFly);

            m_AnimatorEvents.PlayAnim(m_ClickAnim, "UI_UIHeroSkinPreview_Fly", 0, () =>
            {
                if (mShowHeroDatas?.Count > 0)
                {
                    ToShowHero = mShowHeroDatas.Dequeue();

                    RefreshHeroUI();

                }
                else
                {
                    if (ToShowHero.HeroCfg.Id == 447)
                    {
                        PopupManager.I.ClosePopup<FirstChargeNewUI.Layer>();
                        var uiMerge = PopupManager.I.FindPopup<UIMerge>();
                        if (uiMerge == null)
                        {
                            PopupManager.I.ShowLayer<UIMerge>();
                        }
                    }
                    Close();
                }
            });
        }
        public GameObject spineObj;
        private async void SetHeroSpine(HeroData heroData, Transform heroSpineCont)
        {
            if (heroData != null && heroData.HeroCfg != null)
            {
                UnityEngine.Object.Destroy(spineObj);
                var result = await HeroUtils.GetHeroSpinePrefab(heroData.HeroCfg);
                spineObj = result.obj;
                if (spineObj != null)
                {
                    spineObj.transform.SetParent(heroSpineCont, false);
                    spineObj.gameObject.name = heroData.HeroId.ToString();
                    var SpineLocalPostion = HeroUtils.Parse(heroData.HeroCfg.PlayerChar[0]);
                    spineObj.transform.localPosition = SpineLocalPostion;
                    var SpineLocalScale = HeroUtils.Parse(heroData.HeroCfg.PlayerChar[1]);
                    spineObj.transform.localScale = SpineLocalScale * heroSpineCont.localScale.x;
                }
            }
        }

        protected internal override void OnCloseStart()
        {
            base.OnCloseStart();
            EventMgr.FireEvent(TEventType.K3NewHeroShowClosed);
            EventMgr.FireEvent(TEventType.MainBuildBubbleRefresh);

            CloseCallBack?.Invoke();
        }
    }
}
