﻿using DeepUI;
using Game.Config;
using Game.Data;
using Game.Map;
using Logic;
using Public;
using System;
using System.Collections;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using TFW.Localization;
using TFW.UI;
using UnityEngine;
using UnityEngine.UI;
using Common;
using TFW;

namespace UI
{
    /// <summary>
    /// 皮肤详情Widget
    /// <AUTHOR>
    /// @date 2020/10/19 17:06:58
    /// @ver 1.0
    /// </summary>
    public class UISkinInfoWidget : UIWidgetBase
    {
        /// <summary>
        /// 名称
        /// </summary>
        private TFWText nameText;

        /// <summary>
        /// 属性标题1
        /// </summary>
        private TFWText attrText1;

        /// <summary>
        /// 属性1
        /// </summary>
        private List<TFWText> attrTestList1;

        /// <summary>
        /// 属性标题2
        /// </summary>
        private TFWText attrText2;

        /// <summary>
        /// 属性2
        /// </summary>
        private List<TFWText> attrTestList2;

        /// <summary>
        /// 无属性时显示的文本
        /// </summary>
        private TFWText descText;

        /// <summary>
        /// 内城大图
        /// </summary>
        private RawImage interBg;

        /// <summary>
        /// 外城大图
        /// </summary>
        private RawImage exterBg;
        
        /// <summary>
        /// 外城城堡图
        /// </summary>
        private RawImage exterCityBg;

        /// <summary>
        /// 外城横线Obj
        /// </summary>
        private GameObject exterLine;

        /// <summary>
        /// 购买选中按钮
        /// </summary>
        private BtnDescWidget btn;

        private TFWText btnShowText;

        /// <summary>
        /// 按钮红点
        /// </summary>
        private RedWidget redWidget;

        /// <summary>
        /// 界面数据
        /// </summary>
        private ShopSkinData panelData;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="root"></param>
        public UISkinInfoWidget(GameObject root) : base(root) { }

        /// <summary>
        /// 初始化
        /// </summary>
        public override void OnInit()
        {
            base.OnInit();
            nameText = UIHelper.GetComponent<TFWText>(rootObj, "Desc/Title/Text");
            attrText1 = UIHelper.GetComponent<TFWText>(rootObj, "Desc/Content/Title1/Text");
            attrText2 = UIHelper.GetComponent<TFWText>(rootObj, "Desc/Content/Title2/Text");
            descText = UIHelper.GetComponent<TFWText>(rootObj, "Desc/descText");
            attrTestList1 = new List<TFWText>();
            attrTestList2 = new List<TFWText>();
            for (int i = 1; i <= 3; i++)
            {
                var attrText = UIHelper.GetComponent<TFWText>(rootObj, $"Desc/Content/Text{i}/Text");
                if (!attrText)
                    continue;

                attrTestList1.Add(attrText);
            }

            for (int i = 4; i <= 6; i++)
            {
                var attrText = UIHelper.GetComponent<TFWText>(rootObj, $"Desc/Content/Text{i}/Text");
                if (!attrText)
                    continue;

                attrTestList2.Add(attrText);
            }

            interBg = UIHelper.GetComponent<RawImage>(rootObj, "SceneImg");
            exterBg = UIHelper.GetComponent<RawImage>(rootObj, "CastleImg");
            exterCityBg = UIHelper.GetComponent<RawImage>(rootObj, "CastleImg/Icon");
            exterLine = UIHelper.GetChild(rootObj, "ButtomLine");

            var btnObj = UIHelper.GetChild(rootObj, "Button");
            if(btnObj)
                btn = new BtnDescWidget(btnObj);

            btnShowText = UIHelper.GetComponent<TFWText>(rootObj, "BtnText"); 

            var redObj = UIHelper.GetChild(rootObj, "Button/Red");
            if(redObj)
                redWidget = new RedWidget(redObj);

            redWidget?.SetData(0);
        }

        /// <summary>
        /// 点击选中或购买
        /// </summary>
        /// <param name="obj"></param>
        private async void OnClickBtn(object[] obj)
        {
            if (panelData == null)
                return;

            if (obj == null || obj.Length == 0)
                return;

            //if(!MapSceneManager.I.IsCanChangeSceneSkin
            //    && panelData.GetSkinType() == 1)
            //{
            //    FloatTips.I.FloatMsg(LocalizationMgr.Get("skin_switch_off"));
            //    return;
            //}

            var data = obj[0] as ShopSkinData;
            if (data == null)
                return;

            if (data.isHas || await ShopSkinMgr.I.IsSpecialSkin())
            {
                
                //使用皮肤
                ShopSkinMgr.I.ChangeSkinReq(data);
                return;
            }

            if (data.isHasTimeLimit)
            {
                ////使用限时道具
                //data.UseLimitLogic();
                //跳转背包
                ShopMgr.I.OpenShopPanel(ShopJumpMark.bag);
                return;
            }

            var get = data.GetSkinGetType();
            if (get == 1)
            {
                if (panelData.GetSkinGift() == null)
                {
                    //未开放的礼包
                    UITools.PopTips(LocalizationMgr.Get("Skin_manage_tips_02"));
                    return;
                }

                //bool isScroll = false;
                //if (data.id == 401)
                //    isScroll = true;
                //跳转商城
                ShopMgr.I.OpenShopPanel();
            }
            else if (get == 3)
            {
                if (panelData.cfg == null)
                    return;

                //活动未开放
                if (!UIActivityMain.ShowActivityByCfgId(panelData.cfg.GetParam))
                    UITools.PopTips(LocalizationMgr.Get("Skin_manage_tips_01"));
            }
            //else if (get == 5)//冒险获取
            //{
            //    TaskUtils.GoTo((int)QuestTypeID.DefendMonsterTimes);
            //}
            else if (get == 5)
            {
                //if (!GameData.I.LevelData.CanToDoByGameLevel(MetaConfig.OpenFreeScene, false))
                //{
                //    FloatTips.I.FloatMsg(LocalizationMgr.Format("DEMO_37", MetaConfig.OpenFreeScene));
                //    return;
                //}

                DeepUI.PopupManager.I.ShowPanel<UIChangeSkin>(); //UnlockSkinByLevel
            }
            else if (get == 7)
            {
                RankNewMgr.I.OpenRankPanel();
            }
            else if (get == 9)
            {

                SummonAltarMgr.I.curTabIdx = data.id % 10;
                SummonAltarMgr.I.Jump = true;
                SummonAltarMgr.I.ReqPowerData();
            }
            else if (get == 8)
            {
                var vip = VipManager.I.GetSelfVipInfo();
                if (panelData.cfg == null || vip == null)
                    return;

                if (vip.vipLv < panelData.cfg.GetParam)
                {
                    FloatTips.I.FloatMsg(LocalizationMgr.Get("VIP17_InsufficientVipLevel"));
                    return;
                }
                DeepUI.PopupManager.I.ShowPanel<UIVipNew>(new UIVipNewData() { needOpenLv = panelData.cfg.GetParam });
            }
            else if (get == 10)
            {
                if (panelData.cfg == null)
                    return;

                //活动未开放
                if (!UIActivityMain.ShowActivityByContentId(panelData.cfg.GetParam))
                    UITools.PopTips(LocalizationMgr.Get("Skin_manage_tips_01"));
            }
            else if (get == 11)
            {
                PopupManager.I.ClearAllPopup();
                //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.HERO);
            }
            else
            {
                //尽情期待
                UITools.PopTips(LocalizationMgr.Get("Skin_manage_tips_03"));
            }
                

        }

        private string castleSpineKey;
        private GameObject castleSpineObj;


        /// <summary>
        /// 设置数据
        /// </summary>
        
        public async UniTaskVoid SetData(ShopSkinData data)
        {
            if (data == null || attrTestList1 == null)
            {
                SetBtnInfo();
                return;
            }
            panelData = data;

            if (nameText)
                nameText.text = data.GetName();

            var attrData = data.GetOwnAttrs();
            var count = attrTestList1.Count;
            bool isShowAttr = false;
            for (int i = 0; i < count; i++)
            {
                if (!attrTestList1[i])
                    continue;

                if(attrData == null || i >= attrData.Count || attrData[i] == null)
                {
                    attrTestList1[i].transform.parent.gameObject.SetActive(false);
                    continue;
                }
                attrTestList1[i].transform.parent.gameObject.SetActive(true);
                attrTestList1[i].text = attrData[i];
                isShowAttr = true;
            }

            if (attrText1 && descText)
            {
                attrText1.text = LocalizationMgr.Get("Skin_own_buff_desc");
                attrText1.enabled = isShowAttr;
                descText.enabled = !isShowAttr;
            }
               
            attrData = data.GetUseAttrs();
          
            isShowAttr = false;

            if (attrTestList2?.Count > 0)
            {
                count = attrTestList2.Count;
                for (int i = 0; i < count; i++)
                {
                    if (!attrTestList2[i])
                        continue;

                    if (attrData == null || i >= attrData.Count || attrData[i] == null)
                    {
                        attrTestList2[i].transform.parent.gameObject.SetActive(false);
                        continue;
                    }
                    attrTestList2[i].transform.parent.gameObject.SetActive(true);
                    attrTestList2[i].text = attrData[i];
                    isShowAttr = true;
                }
            }

            if (attrText2)
            {
                attrText2.text = LocalizationMgr.Get("Skin_use_buff_desc");
                attrText2.enabled = isShowAttr;
            }
                

            var isInInter = data.GetSkinType() == 1;
            if(descText)
            descText.text = LocalizationMgr.Get(data.GetSkinType() == 3 ? "Summon_skin_change_tips" : "Function_Adventure_Buff_None");
            if (interBg && exterBg && exterCityBg && exterLine)
            {
                interBg.enabled = isInInter;
                exterBg.enabled = !isInInter;
                exterCityBg.enabled = !isInInter;
                exterLine.SetActive(!isInInter);
            }
            
            if(castleSpineObj)
            castleSpineObj.SetActive(false);

            if (isInInter)
            {
                //内城
                if (interBg)
                    UITools.SetRawImage(interBg, data.GetMaxPreview(), "Skin");
            }
            else
            {
                //外城
                if (exterBg)
                {
                    UITools.SetRawImageByPrefab(exterBg, data.GetMaxPreview());

                    if (data.cfg?.PreType == (int)GiftAssetTypeEnum.Img)
                    {
                        exterCityBg.enabled = true;
                        UITools.SetRawImageByPrefab(exterCityBg, data.GetMaxCityPreview());
                        castleSpineObj?.SetActive(false);
                    }
                    else if (data.cfg?.PreType == (int)GiftAssetTypeEnum.Spine)
                    {
                        exterCityBg.enabled = false;

                        UIWidgetUtil.ReleaseEffect(castleSpineKey, castleSpineObj);
                        var assetPath = data.cfg.AssetClient;
                        var summonPetData = SummonAltarMgr.I.GetSummonPetDataByDefaultSceneId(data.cfg.Id);
                        if (summonPetData != null)
                        {
                            var sceneCfg = await Cfg.C.CD2Sence.GetConfigAsync(summonPetData.SkinColour);
                            if(sceneCfg!= null)
                                assetPath = sceneCfg.AssetClient;
                        }
                        castleSpineKey = assetPath;
                        castleSpineObj = await UIWidgetUtil.GetObjInstance(castleSpineKey);

                        if (castleSpineObj)
                        {
                            var ui = PopupManager.I.FindPopup<UIShopSkin>();
                            UIWidgetUtil.SetNormalEffect(castleSpineObj, exterCityBg.gameObject.transform, ui);

                            var cfg = data.cfg;
                            if (cfg != null && cfg.Location != null && cfg.Location.Count > 0 && cfg.Scale != null && cfg.Scale.Count > 0)
                            {
                                castleSpineObj.transform.localScale = new Vector3(
                                    float.Parse(cfg.Scale[0]),
                                    float.Parse(cfg.Scale[1]),
                                    float.Parse(cfg.Scale[2]));
                                castleSpineObj.transform.localPosition = new Vector3(
                                    int.Parse(cfg.Location[0]),
                                    int.Parse(cfg.Location[1]),
                                    int.Parse(cfg.Location[2]));
                            }
                            else
                            {
                                D.Error?.Log($"Sence 表中 id {cfg.Id} 的 size/scale 是空的");
                            }
                        }
                    }
                }
            }

            SetBtnInfo();
        }

        /// <summary>
        /// 设置按钮信息
        /// </summary>
        private async void SetBtnInfo()
        {
            if (btnShowText)
                btnShowText.enabled = false;

            var data = panelData;
            if (data == null)
            {
                btn?.SetData(LocalizationMgr.Get("Skill_btn_2"));
                return;
            }
           

            //穿戴状态不显示按钮
            if(data.isHas && data.IsServerSelectSkin() && data.GetSkinGetType() != 9)
            {
                btn?.SetRootVisible(false);
                return;
            }
                
            if(data.GetSkinGetType() == 5)
            { 
                //冒险皮肤特殊处理
                var isHasTimeLimit = await data.CheckUseLimit();
                if (isHasTimeLimit)
                {
                    btn?.SetRootVisible(true);
                }
                else if(data.isHas)
                {
                    btn?.SetRootVisible(true);
                }
                else
                {
                    btn?.SetRootVisible(false);
                    if (btnShowText)
                    {
                        var lv = 0;
                        var dic = await Cfg.C.CD2UnlockSkin.RawDictAsync();
                        if (dic.TryGetValue(data.id, out var dt))
                            lv = dt.UnlockIdLevle;
                        btnShowText.enabled = true;
                        btnShowText.text = LocalizationMgr.Format("System_Adshop_text_9", lv);
                    }
                }
            }
            else if(data.GetSkinGetType() == 9)
            {
                btnShowText.text = LocalizationMgr.Get("Quick_Access_Btn1");
                if (!data.isHas)
                    btn?.SetData(LocalizationMgr.Get("Quick_Access_Btn1"));
                else
                    btn?.SetData(data.isHas && data.IsServerSelectSkin() ? LocalizationMgr.Get("Dragon_Equip_unload_btn") : LocalizationMgr.Get("Skill_btn_2"));
                btn?.SetRootVisible(true);
            }
            else
            {
                btn?.SetRootVisible(true);
            }
            if(data.GetSkinGetType() != 9)
                btn?.SetData(await data.GetBtnDesc());
            btn?.SetBtnClickCallBack(OnClickBtn, true, data);

            //redWidget.SetData(data.isNew, false);
        }

        /// <summary>
        /// 清理
        /// </summary>
        public override void Clear()
        {
            base.Clear();
            btn?.Clear();

            UIWidgetUtil.ReleaseEffect(castleSpineKey, castleSpineObj);
            castleSpineObj = null;
        }
    }
}
