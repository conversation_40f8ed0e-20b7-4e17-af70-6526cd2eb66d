﻿using System.Collections.Generic;
using System.Linq;
using System.Text;
using Common;
using cspb;
using Logic;
using Public;
using THelper;
using UnityEngine.Serialization;

namespace K3
{
    /// <summary>
    /// 处理基础数据
    /// </summary>
    public class ChapterTaskMgr : Ins<ChapterTaskMgr>
    {

        public bool ZoomStatus = false;

        public bool GetChapterReward = false;

        public float WaitPhotoTime;

        /// <summary>
        /// 获取当前组
        /// </summary>
        /// <returns></returns>
        public int GetCurrentGroup()
        {
            var tempList = ChapterTaskGameData.I.mChapterQuestInfo.chapterQuests.OrderBy(x => x.ConfigData?.ActGroup).ToList();

            // 如果返回是0，相当于这个章节任务已经全部完成。需要进入领取章节奖励阶段
            int group = 0;
            foreach (var quest in tempList)
            {
                if (!CheckStepIsFinish($"{quest.ConfigData.ActGroup}") || quest.state == QuestState.QuestStateGoOn || quest.state == QuestState.QuestStateFinish)
                {
                    group = quest.ConfigData.ActGroup;
                    break;
                }
            }
            return group;
        }

        /// <summary>
        /// 获取当前章节任务的阶段
        /// </summary>
        /// <returns></returns>
        private List<int> GetCurrentTaskSteps()
        {
            List<Cfg.G.CChapterQuest> quests = Cfg.C.CChapterQuest.RawList().Where(x => x.ChapterID == ChapterTaskGameData.I.mChapterQuestInfo.curChapterID).ToList();
            List<int> tempList = new List<int>();
            foreach (var cfg in quests)
            {
                if (!tempList.Contains(cfg.ActGroup))
                {
                    tempList.Add(cfg.ActGroup);
                }
            }
            return tempList;
        }

        /// <summary>
        /// 获取当前章节任务列表
        /// </summary>
        /// <returns></returns>
        public List<D2Quest> GetCurrentChapterList()
        {
            if (ChapterTaskGameData.I.mChapterQuestInfo == null)
            {
                return new List<D2Quest>();
            }
            // 优先找进度
            int currentGroup = GetCurrentGroup();
            // 已经完成的任务
            List<D2Quest> finish = ChapterTaskGameData.I.mChapterQuestInfo.chapterQuests.Where(x => x.state == QuestState.QuestStateReward && x.ConfigData.ActGroup != currentGroup).ToList();
            /// 当前任务
            List<D2Quest> temp = ChapterTaskGameData.I.mChapterQuestInfo.chapterQuests.Where(x => x.ConfigData.ActGroup == currentGroup).ToList();

            if (finish.Count > 0)
            {
                temp.AddRange(finish);
            }

            return temp;
        }

        /// <summary>
        /// 获取当前英雄的基础信息
        /// </summary>
        /// <returns></returns>
        public (string, List<string>) GetCurrentChapterHeroInfo()
        {
            Cfg.G.CChapterReward chapterReward = Cfg.C.CChapterReward.I(ChapterTaskGameData.I.mChapterQuestInfo.curChapterID);
            Cfg.G.CD2Hero heroCfg = Cfg.C.CD2Hero.I(chapterReward.Hero);
            List<string> temp = new List<string>();

            // 年龄
            temp.Add($"{"unlockhero_des_title_2".ToLocal()}:{heroCfg.Info[1]}");
            // 生日
            temp.Add($"{"unlockhero_des_title_3".ToLocal()}:{heroCfg.Info[2]}");
            // 身高
            temp.Add($"{"unlockhero_des_title_4".ToLocal()}:{heroCfg.Info[3]}");
            // 胸围
            temp.Add($"{"unlockhero_des_title_6".ToLocal()}:{heroCfg.Info[5]}");

            return (heroCfg.Name.ToLocal(), temp);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public float GetSliderProgress()
        {
            // 优先找进度
            int currentProgress = GetCurrentGroup();
            if (currentProgress == 0)
            {
                return 1;
            }
            /// 当前任务
            List<D2Quest> temp = ChapterTaskGameData.I.mChapterQuestInfo.chapterQuests.Where(x => x.ConfigData.ActGroup == currentProgress).ToList();
            // 已完成的任务数量
            int finishCount = temp.Where(x => x.state == QuestState.QuestStateReward).ToList().Count;
            // 获取slider进度
            return finishCount > 0 ? (finishCount*1f / temp.Count) : 0;
        }

        /// <summary>
        /// 获取章节表格
        /// </summary>
        /// <returns>英雄的 Spine 路径</returns>
        public Cfg.G.CChapterReward GetCurrentChapterConfig()
        {
            return Cfg.C.CChapterReward.I(GetNewChapterId());
        }

        /// <summary>
        /// 新章节ID获取
        /// </summary>
        /// <returns></returns>
        public int GetNewChapterId()
        {
            int maxChapter = GetMaxChapterId();

            return ChapterTaskGameData.I.mChapterQuestInfo.curChapterID > maxChapter ? maxChapter : ChapterTaskGameData.I.mChapterQuestInfo.curChapterID;
        }

        /// <summary>
        ///  暂时默认只有10章
        /// </summary>
        /// <returns></returns>
        public bool CheckChapterTaskIsFinish()
        {
            if (ChapterTaskMgr.I.GetMaxChapterId() < ChapterTaskGameData.I.mChapterQuestInfo.curChapterID)
            {
                return true;
            }

            return ChapterTaskGameData.I.mChapterQuestInfo.CurState == QuestState.QuestStateReward;
        }


        public int GetMaxChapterId()
        {
            return Cfg.C.CChapterQuest.RawList().Max(x => x.ChapterID);
        }

        /// <summary>
        /// 获取当前步骤,
        /// </summary>
        /// <returns></returns>
        public int GetCurrentSkinStep()
        {
            if (CheckChapterTaskIsFinish())
            {
                return -1;
            }
            // 当前章节所处的进度位置。
            int progress = GetCurrentGroup();
            // 全部阶段
            List<int> taskSteps = GetCurrentTaskSteps();
            // 小于0 证明没有阶段可以执行
            // 等于0 证明执行的是章节任务的领奖
            // 大于0 证明执行的是皮肤的解锁
            int temp = -1;

            // 处于章节任务领奖阶段
            if (progress == 0 && taskSteps.Count>0)
            {
                taskSteps.Sort();
                int step = taskSteps[taskSteps.Count - 1];
                // 直接判断该章节最后一步是否已经完成
                if (CheckTaskIsReward(step) && !CheckStepIsFinish($"{step}"))
                {
                    temp = taskSteps.Count;
                }
                else
                {
                    temp = progress;
                }
            }
            else
            {
                // 
                foreach (var group in taskSteps)
                {
                    if (group == progress && CheckTaskIsReward(group) && !CheckStepIsFinish($"{group}"))
                    {
                        temp = group;
                        break;
                    }
                }
            }
            return temp;
        }

        private bool CheckTaskIsReward(int step)
        {
            foreach (D2Quest quest in ChapterTaskGameData.I.mChapterQuestInfo.chapterQuests)
            {
                if (quest.ConfigData.ActGroup == step && quest.state != QuestState.QuestStateReward)
                {
                    return false;
                }
            }
            return true;
        }


        /// <summary>
        /// 检查是否需要进入到英雄预览UI
        /// </summary>
        /// <returns></returns>
        public bool CheckGotoHeroPreview()
        {
            // 当前组
            int group = GetCurrentGroup();
            var currentList = ChapterTaskGameData.I.mChapterQuestInfo.chapterQuests.Where(x => x.ConfigData.ActGroup == group && x.state != QuestState.QuestStateReward).ToList();
            if (currentList.Count == 0)
            {
                return true;
            }

            return false;
        }

        #region 阶段数据处理

        /// <summary>
        /// 获取数据, 0未完成，1已完成
        /// </summary>
        /// <returns></returns>
        private string GetStatusData()
        {
            if (K3PlayerMgr.I.PlayerData == null)
                return string.Empty;

            if (K3PlayerMgr.I.PlayerData.chatperTaskData == null)
            {
                K3PlayerMgr.I.PlayerData.chatperTaskData = new UIChapterTaskData();
                K3PlayerMgr.I.PlayerData.chatperTaskData.TaskCache = new Dictionary<int, string>();
            }

            // 初始化当前章节的任务阶段
            if (K3PlayerMgr.I.PlayerData.chatperTaskData.TaskCache.TryGetValue(ChapterTaskGameData.I.mChapterQuestInfo.curChapterID,out var temp))
            {
                //数据兼容 没有进行添加
                bool changed = false;
                List<int> list = GetCurrentTaskSteps();
                foreach (var group in list)
                {
                    if (!temp.Contains($"{group}:"))
                    {
                        changed=true;
                        temp= $"{group}:0,"+temp;
                    }
                }

                if (changed)
                {
                    K3PlayerMgr.I.PlayerData.chatperTaskData.TaskCache[ChapterTaskGameData.I.mChapterQuestInfo.curChapterID] = temp;
                    K3PlayerMgr.I.SavePlayerDataToServer();
                }
            }
            else
            {
                List<int> list = GetCurrentTaskSteps();
                StringBuilder sb = new StringBuilder();
                foreach (var group in list)
                {
                    sb.Append($"{group}:0,");
                }
                // 开始以及结束的状态
                sb.Append("start:0");

                temp= sb.ToString();
                K3PlayerMgr.I.PlayerData.chatperTaskData.TaskCache.Add(ChapterTaskGameData.I.mChapterQuestInfo.curChapterID, temp);
                // 保存结构
                K3PlayerMgr.I.SavePlayerDataToServer();
            }
             
            // UnityEngine.Debug.Log($"LogDebug - ChapterTask 获取数据 = {ChapterTaskGameData.I.mChapterQuestInfo.curChapterID} = {temp}");
            return temp;
        }

        /// <summary>
        /// 更新完成状态
        /// </summary>
        /// <param name="tag">标签</param>
        public void UpdateStatusData(string tag)
        {
            string data = GetStatusData();
            data = data.Replace($"{tag}:0", $"{tag}:1");
            K3PlayerMgr.I.PlayerData.chatperTaskData.TaskCache[ChapterTaskGameData.I.mChapterQuestInfo.curChapterID] = data;
            // 保存结构
            K3PlayerMgr.I.SavePlayerDataToServer();
        }

        /// <summary>
        /// 判断是否已经完成了该步骤
        /// </summary>
        /// <param name="tag"></param>
        /// <returns></returns>
        public bool CheckStepIsFinish(string tag)
        {
            string data = GetStatusData();
            return data.Contains($"{tag}:1");
        }

        /// <summary>
        /// 设置英雄皮肤
        /// </summary>
        /// <param name="heroId"></param>
        /// <param name="skin"></param>
        public void SetHeroSkin(int heroId, string skin,bool overrideVideo=false)
        {
            if (K3PlayerMgr.I.PlayerData.chatperTaskData.SkinCache == null)
            {
                K3PlayerMgr.I.PlayerData.chatperTaskData.SkinCache = new Dictionary<int, string>();
            }
            K3PlayerMgr.I.PlayerData.chatperTaskData.SkinCache[heroId] = skin;

            if (overrideVideo)
            {
                K3PlayerMgr.I.PlayerData.chatperTaskData.HeroVideoCache?.Remove(heroId);
            }

            // 保存结构
            K3PlayerMgr.I.SavePlayerDataToServer();
            D.Warning?.Log($"设置英雄{heroId} Spine皮肤：{skin}");
            EventMgr.FireEvent(TEventType.HeroSkinSelect);
        }

        /// <summary>
        /// 获取英雄皮肤
        /// </summary>
        /// <param name="heroId"></param>
        /// <returns></returns>
        public string GetHeroSkin(int heroId)
        {
            if (K3PlayerMgr.I.PlayerData == null || K3PlayerMgr.I.PlayerData.chatperTaskData==null)
                return "01";

            if (K3PlayerMgr.I.PlayerData.chatperTaskData.SkinCache == null)
            {
                K3PlayerMgr.I.PlayerData.chatperTaskData.SkinCache = new Dictionary<int, string>();
            }
            if (!K3PlayerMgr.I.PlayerData.chatperTaskData.SkinCache.ContainsKey(heroId))
            {
                K3PlayerMgr.I.PlayerData.chatperTaskData.SkinCache[heroId] = "01";
                // 保存结构
                K3PlayerMgr.I.SavePlayerDataToServer();
                return K3PlayerMgr.I.PlayerData.chatperTaskData.SkinCache[heroId];
            }
            return K3PlayerMgr.I.PlayerData.chatperTaskData.SkinCache[heroId];
        }

         
        public void SetHeroVedioSkin(int heroId, string skin)
        {
            if (K3PlayerMgr.I.PlayerData.chatperTaskData.HeroVideoCache == null)
            {
                K3PlayerMgr.I.PlayerData.chatperTaskData.HeroVideoCache = new Dictionary<int, string>();
            }
            K3PlayerMgr.I.PlayerData.chatperTaskData.HeroVideoCache[heroId] = skin;
            // 保存结构
            K3PlayerMgr.I.SavePlayerDataToServer();

            D.Warning?.Log($"设置英雄{heroId} 视频皮肤：{skin}");

            //EventMgr.FireEvent(TEventType.HeroVedioSkin);
            EventMgr.FireEvent(TEventType.HeroSkinSelect);
        }

        public string GetHeroVedioSkin(int heroId)
        {
            if (K3PlayerMgr.I.PlayerData.chatperTaskData.HeroVideoCache == null)
            {
                K3PlayerMgr.I.PlayerData.chatperTaskData.HeroVideoCache = new Dictionary<int, string>();
            }
            if (!K3PlayerMgr.I.PlayerData.chatperTaskData.HeroVideoCache.ContainsKey(heroId))
            {
                return string.Empty;
            }
            return K3PlayerMgr.I.PlayerData.chatperTaskData.HeroVideoCache[heroId];
        }

        #endregion
    }
}
