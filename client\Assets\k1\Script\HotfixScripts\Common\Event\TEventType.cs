﻿








namespace Common
{
    /// <summary>
    /// 事件类型声明
    /// </summary>
    public enum TEventType
    {

        #region 通用

        ApplicationPause,
        ApplicationResume,
        ApplicationRestart,
        ApplicationQuit,

        #endregion

        #region 登陆相关
        GatewayLogin,
        AuthLogin,
        CharacterLogin,
        SDKLogin,
        SDKGameButtonBeans,
        SDKLoginBySDKManage,
        // 网关连接中
        //GatewayConnecting,

        // 网关已连接
        //GatewayConnected,

        // Auth登录中
        //AuthLogining,

        // Auth已登录
        //AuthLogined,

        // 网关返回
        //GatewayReturn,

        // Auth返回
        //AuthReturn,

        // 角色创建中
        //CharacterCreating,

        // 角色登录中
        //CharacterLogining,

        // 登录成功
        LoginSucceed,

        // 玩家信息已获取到
        PlayerInfoReceived,

        CSInfoReceived,

        /// AccountReq 请求后账号数据刷新~
        AccountInfoRefresh,

        //玩家城市所在服务器发生改变
        UpdateCityServer,

        #endregion

        #region 重连相关
        //尝试重连
        TryReconnect,
        //重连开始
        //ReconnectBegin,
        //重连成功
        //ReconnectSuccess,
        //重连失败
        ReconnectFail,
        //重连成功后的重置逻辑
        ReconnectResetLogic,

        #endregion

        #region 玩家相关
        PlayerProtect,
        PlayerAttackAlert,
        //UpdatePlayerInfo,
        PlayerPowerChange,
        PlayerPowerRefresh,
        PlayerLevelNtf,
        PlayerChangeName,
        UseItemAckChangeName,
        PlayerCheckName,
        PlayerChangeAvatar,
        PlayerChangeCustomHeadIcon,
        PlayerChangeFrame,
        PlayerChangeFashion,
        PlayerChangeHeroSpine,
        PlayerBattleDetail,
        PlayerTargetPlayerBase,
        PlayerUnlockAvatarList,
        //PlayerUnlockAvatarAck,
        //PlayerUnlockFrameAck,
        //PlayerCityDownScale,
        PlayerUnlockFrameList,
        //PlayerChangeBiography,
        PlayerChangeNationalFlag,
        PlayerAvatarFrameNtf,
        PlayerUnlockMaxHeroLevelChange,


        //PowerFlyStartPointSet,

        ChangevAvatarFrameAck,
        GetAllAvatarFrameAck,
        ChangeSexAck,
        #endregion

        #region 资产相关

        RefreshItemAck,
        RefreshVMAck,
        //RefreshXpAck,
        RefreshRecoverAck,
        RefreshSoldierAck,
        RefreshAssetAck,
        RefreshRssAck,
        UseItemAck,
        ItemStoreBuyUseAck,
        ItemStoreBuyAck,
        ItemStoreAck,
        DropAsset,
        UseAdItem,
        DragonEquipmentChanged,


        //HordeStoreItemListUpdate,
        //HordeStoreItemUpdate,
        //HordeStoreItemBuyAck,

        #endregion

        #region 邮件相关


        /// <summary>
        /// 有新邮件啦 
        /// </summary>
        MailNewMail,

        // 未读消息数量发生改变
        NumMailUnreadChanged,

        // 邮件列表有变化
        MailListChange,

        MailContnetRefresh,

        /// <summary>
        /// 删除邮件
        /// </summary>
        MailDelAck,

        // 邮件附件领取ok
        MailAttachmentChange,

        /// <summary>
        /// 获取邮件附件时
        /// </summary>
        MailOnGetAttachment,

        // 写邮件 玩家名查询
        MailQueryPlayerName,

        //一键已读加领取
        ReadAndCollectAllMail,

        // 战报 log ack
        MailNewBattleLog,

        // 最近发送邮件的玩家
        MailRecentSendPlayerInfo,

        #region  新邮件
        /// <summary>
        /// 邮件状态改变 单独类型
        /// </summary>
        MailStateChange,
        /// <summary>
        /// 本次申请所有邮件收包准备完成
        /// </summary>
        MailReqAllChangeDone,
        /// <summary>
        /// 收到了邮件红点信息
        /// </summary>
        MailRedPointGot,

        /// <summary>
        /// 邮件加载完成
        /// </summary>
        MailLoadDone,

        /// <summary>
        /// 邮件内容改变
        /// </summary>
        MailDetailChange,

        /// <summary>
        /// 联盟邮件发送次数更新
        /// </summary>
        MailUnionMailCount,

        /// <summary>
        /// 发送邮件cd
        /// </summary>
        MailSendCD,

        /// <summary>
        /// 最近发送邮件的玩家
        /// </summary>
        MailSendNearPlayerInfo,

        #endregion
        MailForceRefush,

        #endregion

        #region 联盟相关

        // 联盟信息变化
        AllianceInfoChange,

        // 联盟推荐列表 事件
        AllianceRecommendUnionList,
        AllianceKvkRecommendUnionList,
        AllianceLocalDragonRankUnionList,
        AllianceLocalDragonRankDataList,
        // 联盟申请列表 事件
        AllianceApplyRecordList,

        // 被哪些联盟联盟列表 事件
        AllianceBeInvitedRecordList,

        // 联盟查询列表 事件
        AllianceQueryList,
        //查询过快
        AllianceQueryError,
        // 联盟 简称|名字|公告 检查 事件
        AllianceCreateCheck,

        // 联盟 创建 事件
        AllianceCreate,

        // 联盟 解散 事件
        AllianceDisband,

        // 联盟 位阶名字更改
        AllianceRankNameChangeAck,

        // 联盟 位阶名字更改推送
        AllianceRankNameChangeNtf,

        // 申请加入联盟 事件
        AllianceJoin,

        // 撤销联盟申请
        AllianceCancelApply,

        // 同意玩家申请
        AllianceAgree,

        // 成员阶级变化 事件
        AllianceMemberClassChange,

        // 移除成员 事件
        AllianceMemberRemove,

        // 联盟名称改变
        AllianceNameChange,

        // 联盟昵称改变
        AllianceNicknameChange,

        // 联盟公告改变
        AllianceDescChange,

        // 联盟宣言改变
        AllianceManifestoChange,

        // 联盟旗帜改变
        AllianceFlagChange,

        // 联盟国家旗帜变化通知，已经不用了
        AllianceLangChange,

        // 联盟语言变化通知
        AllianceLanChange,

        // 入盟条件变化
        AllianceJoinConditionChange,

        // 联盟设置变化
        AllianceSettingChange,

        // 联盟信息
        AllianceInfoDetail,

        // 盟主转让
        AllianceLeaderTransfer,

        // 联盟申请列表变化 事件
        AllianceApplyRecordChange,

        // 联盟受邀操作 事件
        AllianceHandleInvite,

        // 联盟受邀列表 事件
        AllianceInvitedRecordChange,

        // 玩家查询 ack
        AllianceQueryPlayer,

        // 邀请玩家入盟查询 ack
        AllianceInviteQueryPlayer,

        // 推荐玩家入盟 ack
        AllianceInviteRecommendPlayer,

        // 邀请玩家入盟 ack
        AllianceInvitePlayer,

        // 取消 邀请玩家入盟 ack
        AllianceCancelInvitePlayer,

        // 加入联盟中
        AllianceJoining,

        // 加入联盟成功
        AllianceJoinSuccess,

        // 联盟退出中
        AllianceExiting,

        // 退出联盟成功
        AllianceExitSuccess,

        // 联盟集结信息更新
        AllianceRallyInfoUpdate,

        // 联盟集结信息列表返回
        AllianceRallyListAck,

        // 联盟集结创建
        AllianceCreateRallyAck,

        // 联盟集结解散
        AllianceDisbandRallyAck,

        // 联盟集结加入
        AllianceJoinRallyAck,

        // 联盟集结踢出
        AllianceRallyKickOutAck,

        // 集结通知
        AllianceRallyNtf,

        // 集结移除通知
        AllianceRallyDelNtf,

        // 联盟帮助信息
        //AllianceHelpInfoAck,

        // 请求联盟帮助
        AllianceSeekUnionHelpAck,

        /// <summary>
        /// 联盟日志更新
        /// </summary>
        AllianceLogsRefush,

        // 帮助所有联盟成员
        //AllianceHelpAllMemberAck,

        // 玩家已经被帮助过的队列ID列表
        //AllianceHaveSeekedUnionHelpNtf,

        // 联盟其他成员是否需要帮助
        //AllianceSeekUnionHelpNtf,

        // 玩家自己被其他成员帮助消息
        //AllianceBeUnionHelpedNtf,

        // 联盟礼物Ack
        //UnionGiftOverviewAck,

        //领取联盟礼物Ack
        //UnionGiftClaimAck,

        //领取大宝箱Ack
        //UnionGiftTreasureBoxAck,

        //获取联盟科技信息Ack
        //UnionResearchAck,

        //礼物数量ntf
        //UnionGiftCountNtf,

        //设置推荐科技Ack
        //UnionTechRecommendAck,

        //联盟科技捐献Ack
        UnionTechDonateAck,

        /// <summary>
        /// 联盟科技研究Ack
        /// </summary>
        ResearchUnionTechAck,

        /// <summary>
        /// 联盟科技推荐Ack
        /// </summary>
        RecommendUnionTechAck,

        //联盟科技开始研究Ack
        //UnionTechResearchAck,

        UnionTechNtf,

        /// <summary>
        /// 联盟科技捐献次数
        /// </summary>
        UnionTechDiamondTimesNtf,

        //联盟科技信息Ntf
        //UnionResearchNtf,

        //联盟成员没有开始联盟研究权限
        UnionCantStartResearch,

        //联盟申请通过
        UnionApplySuccess,

        //联盟申请失败
        UnionApplyFaild,

        /// <summary>
        /// 关闭联盟主UI
        /// </summary>
        OnCloseAllianceMainUI,

        /// <summary>
        /// 联盟招募
        /// </summary>
        UnionRecruitAck,

        GetRePowerDiamondCostAck,

        /// <summary>
        /// 联盟等级限制变化通知
        /// </summary>
        AllianceLimitLevelChange,
        /// <summary>
        /// 自动踢人开关
        /// </summary>
        UnionMemberKickOutSettingAck,
        /// <summary>
        /// 自动踢人开关
        /// </summary>
        UnionMemberKickOutSettingInfoAck,
        /// <summary>
        /// 晋升开关
        /// </summary>
        UnionPromotionOpenAck,
        /// <summary>
        /// 晋升战力条件
        /// </summary>
        UnionPromotionPowerAck,
        /// <summary>
        /// 盟主弹劾
        /// </summary>
        UnionLeaderDismissAck,
        /// <summary>
        /// 自动加入集结信息刷新
        /// </summary>
        AutoRallyInfoRefresh,

        /// <summary>
        /// 自动加入集结红点
        /// </summary>
        AutoRallyRedDotRefush,

        /// <summary>
        /// 联盟buff数据刷新
        /// </summary>
        AllianceBuffRefresh,

        /// <summary>
        /// 联盟邀请时间重置
        /// </summary>
        AllianceInvitedTimeReset,

        /// <summary>
        /// 战争信息
        /// </summary>
        InformationNtf,
        /// <summary>
        /// 战争信息删除
        /// </summary>
        InformationDelNtf,
        /// <summary>
        /// 实体驻守部队刷新
        /// </summary>
        EntityInfoTroopUpdate,

        #endregion

        #region 联盟战相关

        /// <summary>
        /// 联盟战区域图标显示状态标识刷新
        /// </summary>
        AllianceWarAreaIconActivity,
        /// <summary>
        /// 联盟战区域信息刷新
        /// </summary>
        AllianceWarAreaInfoUpdate,
        /// <summary>
        /// 联盟战领地信息放弃
        /// </summary>
        AllianceWarAreaGiveUp,
        /// <summary>
        /// 联盟战领地被抢
        /// </summary>
        AllianceWarAreaRobbed,
        /// <summary>
        /// 联盟战建筑伤害排行榜信息数据刷新
        /// </summary>
        AllianceWarDamageRankUpdate,

        #endregion

        #region 联盟领地-堡垒|旗帜
        /// <summary>
        /// 迁城
        /// </summary>
        UnionRandomCoordAck,
        /// <summary>
        /// 堡垒建造回调
        /// </summary>
        //UnionFortBuildAck,

        /// <summary>
        /// 堡垒数据变更
        /// </summary>
        UnionFortsInfo,

        /// <summary>
        /// 堡垒数删除
        /// </summary>
        UnionFortsInfoDel,

        /// <summary>
        /// 堡垒拆除
        /// </summary>
        UnionFortUnbuildAck,

        /// <summary>
        /// 所有领地数据推送-分帧
        /// </summary>
        //UnionLandsNtf,

        /// <summary>
        /// 领地删除
        /// </summary>
        //UnionLandDelNtf,

        /// <summary>
        /// 单个领地
        /// </summary>
        //UnionLandNtf,

        /// <summary>
        /// 旗帜数据变更
        /// </summary>
        UnionFlagsInfo,

        /// <summary>
        /// 旗帜数据删除
        /// </summary>
        UnionFlagsInfoDel,

        #endregion

        #region 圣坛
        /// <summary>
        /// 圣坛数据领取
        /// </summary>
        UnionAltarCollectAck,

        /// <summary>
        /// 圣坛数据变更
        /// </summary>
        UnionAltarAck,
        #endregion

        #region 实体相关

        EntityOutOfView,
        WildMapViewAck,
        EntityCreate,
        EntityUpdate,
        EntitiesUpdate,
        EntityDelete,
        EntityClear,
        CityTeleportOut,
        CityTeleportIn,
        CityTeleportAck,
        ActiveShieldSucceed,
        TroopReturned,
        TroopDelete,
        BattleFrameNtf,
        BattleResult,
        BattleResultAssets,
        SceneWorldLoaded,
        TroopCreated,
        RallyStart,
        RallyCreated,
        RallyBattleStart,
        RallyBattleEnd,
        //BattleFrameBoarder,
        EnterBattle,
        ExitBattle,
        EntityEnterBattle,
        EntityExitBattle,
        //TroopStationChange,
        EntityResourceUpdate,
        EntityResourceDelete,
        /// <summary>
        /// 城市实体刷新
        /// </summary>
        EntityCityUpdate,
        /// <summary>
        /// 城市实体 等级提升（需要同步当前视野内的所有实体对象，因为有蛋疼的显示保护罩需求）
        /// </summary>
        EntityCityLevelUpgrade,

        EntityResourceTroopChange,
        CameraStopFollow,
        //EntityEnterGather,
        EntityTroopExitGather,
        EntityTroopEnterGather,
        EntityHpMp,
        ScoutTroopCreated,
        ScoutTroopDelete,
        //ScoutTroopUpdate,
        //RuinsDetailAck,
        //RuinsStateChange,
        PlayerTroopRetreat,
        //EntityRewardBoxUpdate,
        EntityRallyUpdate,

        /// <summary>
        /// 新的侦察城市回包
        /// </summary>
        OnNewScoutAck,

        /// <summary>
        /// 收藏坐标地图实体数据刷新
        /// </summary>
        CoordsMapEntityUpdate,
        /// <summary>
        /// 删除单一实体，用于退出集结部队
        /// </summary>
        EntitySingleDelete,
        #endregion

        #region 联盟盟友实体相关

        WildMapUnionViewAck,
        UnionEntitiesNtf,
        UnionDelEntitiesNtf,

        #endregion

        #region UI相关

        //WndShowGlobalMask,
        //WndHideGlobalMask,
        //HideUIBluredBackground,

        ShowUI,//打开UI
        HideUI,//关闭UI

        /// <summary>
        /// 切换主UI
        /// </summary>
        OnSwitchMainUI,
        #endregion

        #region livefeed相关

        //----------------------------------------------------------------------
        //------------------------------新版举报系统------------------------------
        //----------------------------------------------------------------------

        /// <summary>
        /// 举报某位玩家返回
        /// </summary>
        OnReportPlayerAck,

        /// <summary>
        /// 取消我的禁言
        /// </summary>
        CancelMyMute,

        /// <summary>
        /// 当我被禁言了
        /// </summary>
        OnImMuted,

        //----------------------------------------------------------------------
        //------------------------------新版举报系统------------------------------
        //----------------------------------------------------------------------


        //拉取到帖子
        //EVENT_FEED_RECEIVE_POST,

        /// <summary>
        /// 拉取到通知
        /// </summary>
        //EventFeedReceiveNotice,

        //拉取到评论
        //EVENT_FEED_RECEIVE_COMMENT,

        //拉取到通知
        //EVENT_FEED_RECEIVE_NOTICE,

        //移除被屏蔽者的帖子记录
        //EVENT_FEED_REMOVE_POST_SEND_BY_MUTE_PLAYER,

        //移除被屏蔽者的评论记录
        //EVENT_FEED_REMOVE_COMMENT_SEND_BY_MUTE_PLAYER,

        //获取到个人资料
        //EVENT_FEED_GET_PROFILE_ACK,

        //屏蔽玩家ack
        //EVENT_FEED_MUTE_PLAYER_ACK,

        //取消屏蔽玩家ack
        //EVENT_FEED_UNMUTE_PLAYER_ACK,

        //创建评论ack
        //EVENT_FEED_CREATE_COMMENT_ACK,

        //点赞ack
        //EVENT_FEED_LIKE_ACK,

        //取消点赞ack
        //EVENT_FEED_LIKE_CANCEL_ACK,

        //发帖子ack
        //EVENT_FEED_CREATE_POST_ACK,

        //举报帖子ack
        //EVENT_FEED_REPORT_POST_ACK,

        //关注玩家ack
        //EVENT_FEED_FOLLOW_ACK,

        //取消关注玩家ack
        //EVENT_FEED_UNFOLLOW_ACK,

        //修改自我介绍ack
        //EVENT_FEED_CHANGE_INTRO_ACK,

        //feed模块上传文件成功
        //EVENT_FEED_UPLOAD_FILE_SUCCESS,

        //feed模块上传文件成功
        //EVENT_FEED_UPLOAD_FILE_FAILED,

        //创建预显示帖子
        //EVENT_FEED_CREATE_PREVIEW_POST,

        //收到LiveFeedNewPostNtf
        //EVENT_FEED_NEW_POST_NTF,

        #endregion

        #region 聊天相关

        /// <summary>
        /// 收到新消息
        /// </summary>
        ChatReceiveNewMsg,

        /// <summary>
        /// 收到历史消息
        /// </summary>
        ChatReceiveHistoryMsg,

        /// <summary>
        /// 增加@其他玩家。参数：（UnifyPlayerInfo playerInfo）或（PlayerData playerData）
        /// </summary>
        ChatAddMention,

        /// <summary>
        /// 创建预显示消息（转菊花的消息）
        /// </summary>
        ChatCreatePreviewMessage,

        /// <summary>
        /// 联系人列表更新(联系人列表是会话列表在UI层的表现)
        /// </summary>
        ContactListUpdate,

        /// <summary>
        /// 创建的私聊聊天室已经准备好（参数：ChatRoom room 聊天室, bool isPlayerCreate 是否玩家主动创建）
        /// </summary>
        ChatCreatePrivateChatRoomReady,

        /// <summary>
        /// 聊天本地缓存数据更新
        /// </summary>
        ChatLocalCacheDataUpdate,

        /// <summary>
        /// 创建预显示消息且消息对应的附加数据已经准备好
        /// </summary>
        ChatCreatePreviewMessageWithAdditionalDataReady,

        /// <summary>
        /// 收到历史消息并且消息对应的附加数据已经准备好
        /// </summary>
        ChatReceiveHistoryMsgWithAdditionalDataReady,

        /// <summary>
        /// 收到新消息且消息对应的附加数据已经准备好
        /// </summary>
        ChatReceiveNewMsgWithAdditionalDataReady,

        /// <summary>
        /// 未读消息有变化（清空了未读消息等）
        /// </summary>
        ChatMessageUnreadChanged,

        #endregion

        #region 联盟商店和VIP
        NewShopBuyAck,

        VipFreeRewardAck,
        #endregion

        #region 章节任务(主线)，联盟任务(日常), 总动员任务
        ChestChangeNtf,
        ChestRewardAck,
        QuestChangeNtf,
        QuestRandomSelect,
        QuestInfoAck,
        EnterTaskPanel,
        #endregion

        #region 总动员
        MilestoneRewardAck,
        #endregion

        #region 王国纪事
        GetServerHistoryRewardAck,
        GetServerHistoryAck,
        #endregion

        #region 破碎之地

        #region  记事录

        FracturedLandsChronicleRewardAck,
        FracturedLandsChronicleAck,

        #endregion

        #endregion

        #region 车站相关

        ////车站详情返回
        //GetRelayDetailAck,

        #endregion

        #region 运输车相关

        //运输车护卫部队更新
        DefendTroopUpdate,

        ////运输车详细信息返回
        //ConvoyDetailAck,

        ////运输车抵达目的地
        //ConvoyArrived,
        //ConvoyArrivedRewards,

        ////运输车加速
        //ConvoySpeedUp,

        #endregion

        #region 采集相关

        //SelfGatherInfo,
        GatherInfoDelete,

        #endregion

        #region 部落相关

        ////部落信息改变，收到HordeInfo ack
        //HordeInfoChange,
        //HordeAnnouncementAck,

        ////部落精英ack
        //AllHordeEliteAck,

        ////部落精英详情ack
        //HordeEliteDetailAck,

        ////挑战部落精英ack
        //ChallengeEliteAck,

        ////部落精英奖励领取ack
        //HordeEliteRewardAck,

        ////部落精英可领取奖励ntf
        //EliteRewardStatusNtf,

        ////部落精英挑战详情ack
        //ChallengeEliteInfoAck,

        ////部落精英状态变化ntf
        //HordeEliteStatusNtf,

        ////部落精英挑战CD变化ntf
        //EliteChallengeState,

        ////部落精英奖励CD变化ntf
        //EliteRewardStateNtf,

        #endregion

        #region 火箭相关

        //RocketLaunch,
        //LaunchPadLaunch,
        //LaunchPadShow,
        //RocketAnimationEnd,
        //RocketShowEnd,
        //FakeTrain,
        //TrainLaunch,
        //TrainArrivedRelay,
        //FakeTrainDelete,
        //EntityTrainUpdate,
        //SkipRocketLaunch,
        //StartCountdown,
        //RocketLaunchEnd,

        #endregion

        #region Buff相关

        UpdateBuffNtf,
        UpdateBuffDeleteNtf,
        /// <summary>
        /// 玩家新Buffs
        /// </summary>
        UpdateNewBuffs,
        AllianceHelpSpeed,

        #endregion

        #region Building相关
        PlayerCityAck,
        //BuildingCreateComplete, // 建筑升级成功
        //BuildingLevelUp, // 建筑升级成功
        UpdateAllBuildingInfo,
        //BuildingUpdate,
        //UpdateAllBuildingAreaInfo,
        //UpdateOneDomainInfo,
        //OnBuildingUnlockAck,
        //BuildingCreateAck,
        BuildingUpgradeAck,
        //BuildingDeleteAck,
        //OnBuildingRpAck,
        //OnBuildingAreaUnlockAck,
        //BuildingResGatherAck,
        //OnBuildingDeleteNtf,
        //GatherProduceAck,
        //EventCityDownScale,
        //BuildingMoveAck,
        //MoveCityCannotOverlap,

        //// LHospital
        //HospitalQueueUpdate,
        //HospitalWoundedSoldierInfoNtf,
        //HospitalHealingSoldierAck,
        //HospitalHealingSoldierGetAck,
        //HospitalHealingSoldierAccelAck,
        //HospitalDisbandWoundedSoldierAck,
        //HospitalCancelHealingAck,
        //HospitalHealingSoldierFinish,
        //HospitalReqAllianceHelpClick,

        //// LInformationCenter
        //InformationCenterOwnNotInformation,
        //InformationCenterOwnInformation,
        //InformationCenterWarning,
        //InformationCenterStopWarning,
        //InformationCenterUpdateTime,
        //InformationCenterUpdate,
        //InformationCenterUpdateScout,
        //InformationCenterTroopUpdate,

        //#region 研究

        //// 研究更新
        //ResearchUpdate,

        //// 研究取消
        //ResearchCanceled,

        //// 研究开始
        //ResearchBegin,

        //// 研究完成
        //ResearchFinished,

        //#endregion

        //CityQueueUpdate,
        //CameraPosChange,

        MaincityJumpTop,
        MaincityJumpBottom,

        #endregion

        #region 平台相关

        UpdateAccessToken,
        PlatformNtf,

        #endregion

        #region SDK相关

        SdkPushTokenRefresh,

        #endregion

        #region 省电相关
        /// <summary>
        /// 进入省电模式
        /// </summary>
        EnterPowerSaveMode,
        /// <summary>
        /// 退出省电模式
        /// </summary>
        ExitPowerSaveMode,
        #endregion

        #region 士兵训练相关

        ////TrainSoldierFruit, //训练完成
        //SoldierStatusNtf, //士兵状态通知
        //// 开始训练士兵
        //SoldierTrainingBegin,

        #endregion

        #region 城墙相关

        //WallCombustionStateNtf,
        WallCityDefenseNtf,
        WallCityDefenseAck,
        //WallExtinguishingAck,
        //WallCityRepairAck,
        //WallChangeGarrisonHeroAck,
        //WallGarrisonHeroNtf,

        #endregion


        #region 搜索

        WildMapSearchLock,
        WildMapSearchUnlock,
        WildMapSearchBeginJump,
        SearchNoResult,
        #endregion


        #region 排行榜相关

        //RankListAck,
        RankListAck,
        MoveToRank,
        RankListGet_Common,
        //RankListAll,
        Rank_AllianceArea,
        Rank_RankTypeUnionWarPoint,

        MobilizeRankRefresh,

        #endregion

        #region 联盟援助相关

        //TransportRssAck, //运输资源ack
        SupportOtherInfo, //支援盟友情况
        AllianceSupportExit, //联盟援助退出
        //AllianceSupportQueueUpdate, //增援队列变化
        //AllianceSupportRecordInfo, //增援记录ack
        //TargetPlayerCityAck, //玩家城池信息
        //UpdatePlayerSelfReinforceInfo,//刷新玩家自己城池的驻守信息

        #endregion

        #region 战斗

        // 战斗使用技能
        FightUseSkill,

        // 技能受击
        FightSkillHit,

        #endregion


        #region 玩家详情

        // LiveFeedProfile, //玩家live feed信息
        //OtherStatsInfo, //其他玩家stats

        #endregion

        #region 英雄装备
        HeroEquipMakeSuccess,
        HeroEquipUpLevelSuccess,
        HeroEquipUseSuccess,
        HeroEquipResolveSuccess,
        HeroEquipUpNtf,
        HeroEquipMgrDataNtf,
        HeroEquipBuildLvlSuccess,
        HeroEquipRefresh,
        HeroEquipOldPageRefresh,
        #endregion


        #region 设置

        SettingValueChange,

        #endregion

        #region 通用奖励弹窗
        BeginShowRewards,
        ShowNextReward,
        //ErrorAnimationEvent,
        #endregion

        #region Pay
        // 正在拉取商品列表
        FetchingProductList,
        //// 商品购买结果
        BuyItemResult,
        #endregion

        #region 实体位置
        EntityPos,
        #endregion


        #region 红点
        RedPointNumChange,
        #endregion


        #region K1D2

        /// <summary>
        /// 金币改变
        /// </summary>
        GoldChange,

        /// <summary>
        /// 游戏关卡改变
        /// </summary>
        GameLevelChange,

        /// <summary>
        /// 自动合成
        /// </summary>
        AutoMerge,

        /// <summary>
        /// 显示解锁
        /// </summary>
        ShowUnLockPanel,

        /// <summary>
        /// 关闭解锁冲突相关的界面
        /// </summary>
        ShowUnLockPanelClose,

        /// <summary>
        /// 士兵解锁
        /// </summary>
        SoldierUnLock,

        /// <summary>
        /// 飞塔
        /// </summary>
        FlyTower,
        /// <summary>
        /// 飞合成位置
        /// </summary>
        FlyCompose,

        ///// <summary>
        ///// 开启自动合成
        ///// </summary>
        //OpenAutoMerge,

        /// <summary>
        /// 开始世界和科技
        /// </summary>
        OpenWorldAndTech,

        /// <summary>
        /// 开启Boss红屏
        /// </summary>
        Open_Boss_Red,

        /// <summary>
        /// 关闭Boss红屏
        /// </summary>
        Close_Boss_Red,
        #endregion

        #region 技能

        /// <summary>
        /// 请求技能回包的消息广播
        /// </summary>
        OnReqSkillsAck,

        /// <summary>
        /// 升级技能回包的消息广播
        /// </summary>
        OnUpgradeSkillAck,

        /// <summary>
        /// 请求装备技能的回包
        /// </summary>
        OnEquipSkillAck,

        /// <summary>
        /// 获得新技能
        /// </summary>
        OnGetNewSkill,

        /// <summary>
        /// 解锁技能
        /// </summary>
        OnUnlockSkill,

        /// <summary>
        /// 处理科技主UI的阻断
        /// </summary>
        OnShowSkillUIBlock,
        /// <summary>
        /// 获取技能
        /// </summary>
        OnGetSkill,

        ChangeEnergyTimer,
        /// <summary>
        /// 主城技能升级完成
        /// </summary>
        MainCitySkillLevelUp,

        RefreshTech,

        /// <summary>
        /// 刷新技能
        /// </summary>
        RefreshSkill,
        ShowHeroSkill,
        /// <summary>
        /// 刷新科技总战力
        /// </summary>
        RefreshTotalSkillPower,
        #endregion

        #region 商城抽卡
        OffLineGift,


        ActivityGiftRefresh,

        //=============商城模块刷新
        MonthCardRefresh,

        //ShopLayout,
        SkinClick,
        RefreshSkin,
        RefreshSkinRed,
        //ShopTabIndex,
        //原逻辑不变，新增一个枚举用于处理皮肤红点。
        RefreshSkinRedNew,

        AllRightBtnsRed,

        GameBankRefresh,
        CloseGameBankPage,
        /// <summary>
        /// 商城页签点击
        /// </summary>
        //ShopTabClick,
        ShopTabClickNew, //上面哪个点击抽空给干掉
        //AdventureBoxNtf,


        /// <summary>
        /// 刷新Slider
        /// </summary>
        //SliderRefresh,
        //ItemStoreScroll,

        //=============商城模块刷新
        //ShopModuleRefresh,
        //OnRefreshBag,
        //CommonGiftRefresh,

        //=======================

        AdEventGame,

        ShopHudVisible,

        OptionalChestPopSelect,

        #endregion

        #region 天赋
        CommonTabClick,
        ShowTalentAt,

        SoldierTalentUnlockAck,
        TalentOperation,
        TalentPowerNtf,

        SoldierTalentOneClickAck,

        #endregion

        #region Guide

        /// <summary>
        /// 资源不足跳到大世界
        /// </summary>
        AssetPopToMainUIWorld,

        /// <summary>
        /// 关掉引导小手
        /// </summary>
        HideTriggerGuide,

        /// <summary>
        /// 引导显示黑色阻断背景
        /// </summary>
        GuideShowBlackMask,
        #endregion

        #region 英雄战斗手势相关

        OnClickDown,
        OnClickUp,
        OnDragStart,
        OnDrag,
        OnDragEnd,
        #endregion

        #region 断线相关

        /// <summary>
        /// 重新连接界面（转菊花）
        /// </summary>
        ReconnectInfoPanel,

        #endregion


        #region 情报系统
        IntelligenceAck,//下发情报数据
        SearchIntelligenceTargetAck,//前往
        IntelligenceChangeNtf,//任务广播
        ReceiveIntelligencePrizeAck,//领取奖励
        BRlogHelpCaravanEnd,//援助通知
        CompleteTheExplore,//遗迹探索成功
        ExchangeExplorePoint,//兑换探索点数
        ExploreMazeAgainAck,//再次挑战
        TaskToComplete,  //任务状态刷新T为有任务完成了
        #endregion

        /// <summary>
        /// 飞金币
        /// </summary>
        Gold_Fly,

        /// <summary>
        /// 显示关卡提示
        /// </summary>
        ShowLevelTips,

        /// <summary>
        /// 手动重新加载关卡
        /// </summary>
        ShowRestartLevel,


        #region 新的部队配置相关
        //刷新部队的数据
        RefreshLineData,
        RefreshHeroData,
        //部队回城才会刷新
        RefreshLineDef,
        //ClickLineUpItem,
        //RefreshLineUpItem,
        ClickHeroItem,
        //ClickSelectedHeroItem,
        //CancleSelectHeroItem,
        //CloseSelectHeroItem,
        //InitLineUpItem,
        ClickTab,
        //ClickSelectItem,
        //SendSelectHeroReq,
        //DragHeroItem,
        InitializeCity,
        //LevelUpOPeration,
        HeroOPerationUse,

        HeroGoBattle,
        //SelectedLevelUpItem,



        #endregion

        #region 内城关卡相关

        /// <summary>
        /// 刷新关卡信息数据
        /// </summary>
        Update_GameLevel_Info,

        Start_GameLevel_Event,
        #endregion

        #region 主界面
        CHANGE_SLG_OR_INNER,

        /// <summary>
        /// 主界面刷新游戏关卡
        /// </summary>
        UIMain_Update_Game_Level,
        /// <summary>
        /// 主界面关卡节点移动
        /// </summary>
        UIMain_Update_LevelNodeMoveTo,
        /// <summary>
        /// 主界面刷新挑战Boss活动状态
        /// </summary>
        UIMain_Update_FightBoss_Active,
        /// <summary>
        /// 主界面刷新破冰礼包活动状态
        /// </summary>
        UIMain_Update_IceBreak_Active,
        /// <summary>
        /// 主界面刷新触发礼包状态
        /// </summary>
        UIMain_Update_TriggerGift_Active,
        /// <summary>
        /// 主界面刷新限制礼包活动状态
        /// </summary>
        UIMain_Update_LimitGift_Active,
        /// <summary>
        /// 主界面刷新限制礼包数据信息
        /// </summary>
        UIMain_Update_LimitGift_Data,
        /// <summary>
        /// 主界面刷新广告事件数据信息
        /// </summary>
        UIMain_Update_AdEvent_Data,
        /// <summary>
        /// 主界面刷新跳关数据信息
        /// </summary>
        UIMain_Update_SkipLevel_Data,
        /// <summary>
        /// 主界面刷新联盟战对象活动状态
        /// </summary>
        UIMain_Update_AllianceWar_Active,
        /// <summary>
        /// 主界面刷新联盟迁城提示活动状态显示
        /// </summary>
        UIMain_Update_AllianceMoveCity_Active,
        /// <summary>
        /// 主界面刷新皮肤活动状态
        /// </summary>
        UIMain_Update_Skin_Active,
        /// <summary>
        /// 主界面任务活动状态
        /// </summary>
        UIMain_Update_Task_Active,
        /// <summary>
        /// 主界面聊天是否开启
        /// </summary>
        UIMain_Update_ChatMsg_Active,
        /// <summary>
        /// 主界面聊天接受消息状态
        /// </summary>
        UIMain_Update_ChatMsg_Mode,
        /// <summary>
        /// 主界面刷新关卡结束状态
        /// </summary>
        UIMain_Update_GameEnd_State,
        /// <summary>
        /// 初始化主界面map相关
        /// </summary>
        UIMain_Init_Map,
        /// <summary>
        /// 主界面刷新显示菜单
        /// </summary>
        UIMain_Update_ShowMenu,
        /// <summary>
        /// 主界面菜单栏解锁
        /// </summary>
        UIMainn_Update_UnLockMenu,
        /// <summary>
        /// 刷新主界面VIP信息
        /// </summary>
        UIMain_Update_VipInfo,
        UIMain_Update_Vip_Store,

        /// <summary>
        /// 刷新主界面VIP信息
        /// </summary>
        UIMain_Update_VipStoreInfo,
        /// <summary>
        /// 主界面刷新自动合成解锁
        /// </summary>
        UIMain_Update_OpenAutoMerge,
        /// <summary>
        /// 主界面刷新广告商城红点数据
        /// </summary>
        UIMain_Update_AdShopRedInfo,
        /// <summary>
        /// 主界面刷新月卡红点信息
        /// </summary>
        UIMain_Update_MonthCardRedInfo,

        /// <summary>
        /// 主界面刷新显示引导信息
        /// </summary>
        UIMain_Update_ShowGuid,
        /// <summary>
        /// 主界面刷新城市位置指示标活动状态显示
        /// </summary>
        UIMain_Update_CityLocatorAcitve,
        /// <summary>
        /// 主界面刷新城市位置指示标检测顶部碰撞盒信息
        /// </summary>
        UIMain_Update_CityLocatorTopBox,

        /// <summary>
        /// 主界面刷新大地图拉高之后的信息
        /// </summary>
        UIMain_Update_MapEnlargeInfo,
        /// <summary>
        /// 主界面刷新玩家城市窗口信息
        /// </summary>
        UIMain_Update_PlayerCityViewPort,

        /// <summary>
        /// 主界面刷新重置技能CD
        /// </summary>
        UIMain_Update_ResetHeroSkillCD,

        /// <summary>
        /// 主界面刷新左边面板按钮合集显示
        /// </summary>
        UIMain_Update_LeftBtns,

        /// <summary>
        /// 主界面播放获得金币效果
        /// </summary>
        UIMain_Play_DiamondEffect,

        /// <summary>
        /// 主界面飞item效果
        /// </summary>
        UIMain_FlyItemEffect,
        /// <summary>
        /// 主界面飞item效果，指定起始坐标
        /// </summary>
        UIMain_FlyItemEffect_S_Pos,
        /// <summary>
        /// 主界面飞item效果，指定起始和结束坐标
        /// </summary>
        UIMain_FlyItemEffect_SE_Pos,

        /// <summary>
        /// 主界面飞资产item效果
        /// </summary>
        UIMain_FlyVmItemEffect,

        /// <summary>
        /// 主界面飞资产item效果指定坐标位置
        /// </summary>
        UIMain_FlyVmItemEffect_Pos,

        ///// <summary>
        ///// 主界面移除无效军队信息
        ///// </summary>
        //UIMain_Update_RemoveInvalidTroop,
        /// <summary>
        /// 点中军队
        /// </summary>
        UIMain_Update_ClickTroop,
        /// <summary>
        /// 开始派遣军队
        /// </summary>
        UIMain_Update_Start_DispatchTroop,
        /// <summary>
        /// 结束派遣军队
        /// </summary>
        UIMain_Update_End_DispatchTroop,
        /// <summary>
        /// 主界面刷新军队列表active
        /// </summary>
        UIMain_Update_TroopMarList_Active,
        /// <summary>
        /// 主界面刷新操作界面信息
        /// </summary>
        UIMain_Update_OperationPanelInfo,
        /// <summary>
        /// 主界面刷新迁服按钮活动状态
        /// </summary>
        UIMain_Update_Migration_Active,

        /// <summary>
        /// 主界面刷新怪物图鉴信息
        /// </summary>
        UIMain_Update_Monster_IiiustratedBook,

        #endregion

        #region 日常任务

        /// <summary>
        /// 新增每日任务
        /// </summary>
        AddDailyQuest,

        /// <summary>
        /// 更新每日任务进度
        /// </summary>
        UpdateDailyQuestProgress,

        /// <summary>
        /// 更新每日任务宝箱的积分进度
        /// </summary>
        UpdateDailyQuestChestProgress,

        /// <summary>
        /// 领取每日任务奖励回调
        /// </summary>
        GetRewardDailyQuest,

        /// <summary>
        /// 领取每日任务宝箱回调
        /// </summary>
        GetRewardDailyQuestChest,

        #endregion

        #region 二次确认

        SecondConfirm,

        #endregion

        #region 战力相关
        /// <summary>
        /// 钻石恢复战力
        /// </summary>
        RePowerDiamondAck,

        /// <summary>
        /// 道具恢复战力
        /// </summary>
        RePowerItemAck,

        /// <summary>
        /// 广告恢复战力
        /// </summary>
        RePowerAdvAck,

        /// <summary>
        /// 使用回复战力道具
        /// </summary>
        UseRePowerItemAck,

        /// <summary>
        /// 当关闭战力恢复面板
        /// </summary>
        OnCloseCureUI,
        /// <summary>
        /// 即将行军信息
        /// </summary>
        OnMarchPreparedAck,

        ShowTargetPowerAck,

        #endregion

        /// <summary>
        /// 英雄主动技能
        /// </summary>
        RefreshHeroSkill,

        HeroSkinSelect,

        HeroSkillUp,
        
        HeroVideoDownLoaded,

        EndlessStartMove,

        ReconnectFailByLoginAck,

        /// <summary>
        /// 主城战斗胜利
        /// </summary>
        MainCityFightSuccess,

        /// <summary>
        /// 英雄试用-英雄上阵后刷新内城英雄替换之前
        /// </summary>
        RefreshCityHeroBefore,

        /// <summary>
        /// 显示主界面菜单
        /// </summary>
        ShowMainMenu,

        /// <summary>
        /// 关闭主界面菜单
        /// </summary>
        CloseMainMenu,
        /// <summary>
        /// 通过主UI切换聊天界面选项
        /// </summary>
        ChangeChatTypeByMainMenu,
        ShowMainCoinEffect,

        /// <summary>
        /// 通过Chat菜单切换聊天界面选项
        /// </summary>
        ChangeChatTypeByChatMenu,

        PhyicalPowerBuyNumRefresh,

        /// <summary>
        /// 关闭科技获取道具UI
        /// </summary>
        OnCloseSkillItemUI,
        /// <summary>
        /// 英雄升级完成
        /// </summary>
        HeroLevelUpComplete,
        /// <summary>
        /// 
        /// </summary>
        HeroLockStateRefresh,
        /// <summary>
        /// 英雄战力提升
        /// </summary>
        HeroPowerUp,
        /// <summary>
        /// 英雄显示战力更新
        /// </summary>
        HeroShowPowerRefresh,

        HeroListSelectRefresh,
        HeroListSelectRefreshLast,

        /// <summary>
        /// 主将下阵
        /// </summary>
        LineupBuildRemoveAck,

        //OnHideMainMenu,

        /// <summary>
        /// 场景切换延迟
        /// </summary>
        ScaneChangeDelay,

        ChangeTowerFightVolume,

        /// <summary>
        /// 跨天
        /// </summary>
        ChangeDay,

        #region 广告商城相关

        /// <summary>
        /// 购买广告商城物品成功
        /// </summary>
        OnBuyShopItemAck,

        /// <summary>
        /// 购买金币钻石礼包成功
        /// </summary>
        OnBuyGift,

        HeroRecruitRefresh,


        HeroRecruitNewRefresh,

        #endregion

        #region battlepass相关
        /// <summary>
        /// 激活或者过期通行证
        /// </summary>
        //ActiveBattlePass,
        /// <summary>
        /// 通行证数据变化
        /// </summary>
        BattlePassDataRefresh,
        BattlePassUnlock,

        BattlePassGift,
        #endregion


        /// <summary>
        /// 联盟集结信息更新
        /// </summary>
        AllianceRallyDataUpdate,

        /// <summary>
        /// 军情提醒红点
        /// </summary>
        AllianceMillitaryAlertRed,

        /// <summary>
        /// 活动提醒红点
        /// </summary>
        AllianceEventRed,

        /// <summary>
        /// 哨塔开炮
        /// </summary>
        ThroneTowerAttack,

        /// <summary>
        /// 双重惊喜
        /// </summary>
        ActvPayCfgDataNtf,
        #region 活动
        /// <summary>
        /// 活动列表
        /// </summary>
        ActvListNtf,
        /// <summary>
        /// 活动列表
        /// </summary>
        ActvUpdateDataSuccess,
        /// <summary>
        /// 数据层删除
        /// </summary>
        ActvClose,
        /// <summary>
        /// 活动状态刷新
        /// </summary>
        ActvStateUpdate,

        /// <summary>
        /// 最强指挥官排行模板
        /// </summary>
        ActvTmplRankNtf,
        /// <summary>
        /// 最强指挥官任务领取
        /// </summary>
        TakeQuestRewardAck,
        Gift_Refresh_ValuePackage,
        /// <summary>
        /// 排行列表
        /// </summary>
        ActvRankListAck,
        /// <summary>
        /// 新增
        /// </summary>
        ActvAdd,
        /// <summary>
        /// 所有红点刷新
        /// </summary>
        OnRefreshAllRedPoints,
        /// <summary>
        /// 逻辑层删除
        /// </summary>
        ActivityDel,
        /// <summary>
        /// 详情刷新
        /// </summary>
        ActvDetailUpdate,
        /// <summary>
        /// 最强指挥官
        /// </summary>
        ActvTmplCommanderNtf,
        /// <summary>
        /// 分享邀新活动
        /// </summary>
        ActvShareInviteNtf,
        /// <summary>
        /// 分享邀新活动匿名购买状态刷新
        /// </summary>
        AnonymousBuyUpdate,
        /// <summary>
        /// 累计签到,七日签到
        /// </summary>
        ActvLoginNtf,
        /// <summary>
        /// 七日签到(领取)
        /// </summary>
        ActvSignInAck,
        /// <summary>
        /// 刷新活动红点数
        /// </summary>
        RefreshActvRedPoint,
        /// <summary>
        /// 超值礼包ACk请求
        /// </summary>
        OnNewGreatValueGiftAck,
        /// <summary>
        /// 超值礼包ACk请求
        /// </summary>
        OnNewGreatValueGiftRewardAck,
        /// <summary>
        /// 超值礼包领取日包免费奖励
        /// </summary>
        OnNewGreatValueGiftFreeRewardAck,
        /// <summary>
        /// 超值礼包Ntf请求
        /// </summary>
        OnNewGreatValueGiftCntNtf,
        /// <summary>
        /// 超值礼包Ntf请求
        /// </summary>
        OnNewGreatValueGiftStaNtf,

        #region 节日活动相关
        /// <summary>
        /// 情人节活动 累计签到,七日签到
        /// </summary>
        ValentineLoginNtf,
        /// <summary>
        /// 情人节活动 转盘
        /// </summary>
        ActvTmplTurnTable214Ntf,
        TurnTable214SelectAwardAck,
        RefreshValentineRankUI,
        TurnTable214ScoreAwardAck,
        /// <summary>
        /// 送花活动数据刷新
        /// </summary>
        SentRoseRefresh,
        SentRoseRecordRefresh,
        /// <summary>
        /// 情人节活动 七日签到(领取)
        /// </summary>
        ValentineSignInAck,
        /// <summary>
        /// 情人节活动 转盘抽奖
        /// </summary>
        TurnTable214DrawAck,
        TurnTable214DrawAckError,
        /// <summary>
        /// 情人节活动 许愿池 下注回包
        /// </summary>
        ValentineWishPoolBetAck,
        /// <summary>
        /// 情人节活动 许愿池 界面回包
        /// </summary>
        ValentineWishPoolPanleAck,
        /// <summary>
        /// 情人节 掉落巧克力
        /// </summary>
        ValentineDropItemAck,
        ActivityCollectRewardGot,
        ActivityCollectDataRefush,
        /// <summary>
        /// 疯狂哈罗德
        /// </summary>
        ActivityHaroldCrazyAck,
        ActivityHaDeLuoInfoNtf,
        ActivityHaDeLuoInfoRefresh,

        //春日活动相关 
        ActivitySpringBuildingRefresh,
        ActivitySpringTaskInfoRefresh,
        ActivitySpringGalleryInfoRefresh,
        ActivitySpringShopInfoRefresh,

        ActivitySpringGalleryInfoChange,

        #endregion

        RankGotRankData,
        RankMaskClick,
        RankTypeRefush,

        /// <summary>
        /// 七日帝王路任务领取
        /// </summary>
        ActvCalQuestRewardAck,
        /// <summary>
        /// 七日帝王路宝箱领取
        /// </summary>
        ActvCalChestRewardAck,
        /// <summary>
        /// 七日帝王路模板
        /// </summary>
        ActvTmplCalendarNtf,

        /// <summary>
        /// 累计充值
        /// </summary>
        ActvTmplRechargeNtf,

        /// <summary>
        /// 累计充值领取
        /// </summary>
        ActvRechargeRewardAck,

        ActvRechargeHisAck,

        /// <summary>
        /// 小额累计充值
        /// </summary>
        ActvTmplSmallRechargeNtf,
        /// <summary>
        /// 小额累计充值后变化推送
        /// </summary>
        ActvTmplSmallRechargeChargeNtf,

        /// <summary>
        /// 团购ntf
        /// </summary>
        GroupBuyNtf,

        /// <summary>
        /// 战争通行证
        /// </summary>
        ActvTmplBatpassNtf,

        /// <summary>
        /// 战争通行证领取
        /// </summary>
        ActvBatpassRewardAck,

        /// <summary>
        /// 战争通行证解锁付费
        /// </summary>
        ActvUnlockPurchaseAck,

        /// <summary>
        /// 一键领取
        /// </summary>
        ActvBatpassRewardsAck,

        FiveDayReward,
        FiveDayBoxReward,

        /// <summary>
        /// 兑换
        /// </summary>
        ActvExchangeAck,

        /// <summary>
        /// 兑换列表
        /// </summary>
        ActvTmplExchangeNtf,

        /// <summary>
        /// 任务活动
        /// </summary>
        ActvTmplQuestNtf,

        /// <summary>
        /// 任务活动
        /// </summary>
        ActvQuestRewardAck,

        /// <summary>
        /// 王座战活动
        /// </summary>
        ActvTmplKingWarNtf,
        /// <summary>
        /// 哨塔争夺战
        /// </summary>
        ActvThroneTowerSoucerNtf,
        ActvThroneOccupyTimeUpdate,
        /// <summary>
        /// 转盘活动
        /// </summary>
        ActvTmplTurnTableNtf,
        ActvTurnTableAck,
        ActvTurnTableAckError,
        ActvTurnTableRewardAck,
        ActvTurnTableHeroSpine,
        ActvTurnTableClickSelectReward,
        ActvTurntableEnergyRewardAck,

        //Bingo活动
        ActvBingoGroupRewardAck,
        ActvBingoQuestRewardAck,
        ActvTmplBingoNtf,
        ActvTmplBingoClickSelect,

        ActvBindMailNtf,

        /// <summary>
        /// 活动日历
        /// </summary>
        ActvCalendarAck,

        /// <summary>
        /// 世界boss
        /// </summary>
        ActvTmplWorldBossNtf,
        /// <summary>
        /// 任务领取
        /// </summary>
        ActvWorldBossRewardAck,
        /// <summary>
        /// 排行列表
        /// </summary>
        ActvWorldBossRankListAck,
        /// <summary>
        /// 次元宝藏
        /// </summary>
        ActvTmplWarpMineNtf,

        /// <summary>
        /// 次元宝藏任务
        /// </summary>
        ActvWarpMineQuestReceiveAck,

        TreasureMineShopBuyRefush,

        TreasureMinePageData,
        TreasureMineRankData,
        TreasureMineTaskData,
        TreasureMineLineData,
        TreasureMineRedPoint,
        TreasureMineAttackCount,
        /// <summary>
        /// 次元宝藏 开始采集
        /// </summary>
        TreasureMineStart,

        /// <summary>
        /// 采集日活动
        /// </summary>
        ActivTmplCollectionDayNtf,

        ActvWarpMineRecallAck,
        ActvWarpMineAttackAck,
        ActvWarpMineSearchAck,

        #region  体力之王

        /// <summary>
        /// 开宝箱刷新
        /// </summary>
        StaminaKingBoxOpenRefush,

        /// <summary>
        /// 积分刷新
        /// </summary>
        StmainaKingScoreRefush,

        PowerKingRefushKey,

        #endregion

        //联盟宝藏
        ActvUnionMineAwardAck,
        ActvUnionMineBaseDataNtf,
        ActvTmplUnionMineChargeNtf,
        UIActvTmplUnionMineChargeNtf,
        UIActvTmplUnionMineChargePerson,
        //熔岩试炼
        ActvAllianceBossNtf,
        ActvUnionBossCallNtf,
        ActvUnionBossCallAck,
        ActvUnionBossRankAck,
        ActvUnionBossDamageRewardAck,
        ActvUnionBossUIRefresh,
        /// <summary>
        /// 活动特效提前隐藏或者激活
        /// </summary>
        ActvEffectVisible,
        /// <summary>
        /// 幸运大减价活动
        /// </summary>
        ActvLuckyDiscountAck,
        ActvQuestsLuckyNtf,
        /// <summary>
        /// 商店
        /// </summary>
        NewActvWarpMineShopNtf,
        /// <summary>
        /// 单笔充值活动领奖
        /// </summary>
        ActvSingleRechargeGetAwardAck,
        /// <summary>
        /// 自选礼包
        /// </summary>
        ActvOptionalGiftNtf,
        /// <summary>
        /// 邪灵入侵领取积分奖励
        /// </summary>
        ActvEvilInvasionScoreReward,
        /// <summary>
        /// 邪灵入侵开始
        /// </summary>
        ActvEvilInvasionBegin,
        ActvTmplEvilInvasionNtf,
        EvilInvasionMarchLineUpdate,
        EvilInvasionMarchLineRemove,
        EvilTpArrive,
        EvilBattleResultRefresh,
        //大富翁
        ActivityEasterNtf,
        ActivityEasterDrawSuccess,
        ActvUseEffectGoodsSuccess,
        ActvScoreAwardSuccess,
        GetActvRedisRankSuccess,
        ActvSelectAwardSuccess,

        ActvLittleHalleenRewardRefush,
        ActvLittleHalleenDetail,
        ActvLittleHalleenRecord,

        //团购
        GroupBuyGoodsRefresh,
        GroupBuyGoodsRefreshDiscount,

        //探宝
        ActivityMonopolyNtf,
        ActivityMonopolyDrawSuccess,
        /// <summary>
        /// 刷新自选礼包界面
        /// </summary>
        ActvOptionalGiftRefresh,

        /// 统一得活动事件
        /// </summary>
        ActvTmplNtf,

        // 周年庆
        ActvExploreFlopAck,
        ActvExploreFlopNtf,
        ExploreRefresh,
        ActvWarmUpInfoNtf,
        ActvWarmUpRewardAck,

        BlackJackNewGame,
        BlackJackHit,
        BlackJackStand,
        BlackJackRefreshRed,

        // 周年庆 气球打靶
        AnniversaryBallonShoot,
        AnniversaryBallonRefresh,
        AnniversaryBallonScoreAward,
        AnniversaryBallonGrandNtf,
        AnniversaryRefreshRed,

        /// <summary>
        /// 通用活动刷新
        /// </summary>
        UpdateActivityContent,

        // 三方支付
        OnThreePayNtf,

        #endregion

        #region 客诉红点
        AIHelpTip,

        #endregion

        /// <summary>
        /// 还款礼包购买
        /// </summary>
        DeadBeatBuy,

        /// <summary>
        /// 退款记录
        /// </summary>
        DeadBeatRecord,


        #region 章节任务相关
        /// <summary>
        /// 进度变化刷新单一任务
        /// </summary>
        ChapterQuestProcessChange,
        /// <summary>
        /// 状态变化，刷新列表，排序有更改
        /// </summary>
        ChapterQuestStateChange,
        /// <summary>
        /// 章节领奖
        /// </summary>
        ChapterReward,
        /// <summary>
        /// 任务领奖
        /// </summary>
        ChapterQuestReward,
        /// <summary>
        /// 章节任务信息相关
        /// </summary>
        ChapterQuestInfo,

        #endregion

        #region 联盟成就

        AllianceAchievementQuestUpdate,
        /// <summary>
        /// 联盟成就任务 奖励领取
        /// </summary>
        AllianceAchievementQuestReward,

        #endregion

        #region 龙相关
        DragonDataRefresh,
        DragonLevelUp,
        DragonTechUp,
        DragonOnlineRewardRefresh,
        #endregion



        #region 进化

        ///// <summary>
        ///// 提升士兵突破回包
        ///// </summary>
        //UpSoldierMergeLvlAck,

        /// <summary>
        /// 更新士兵进化信息
        /// </summary>
        UpdateEvoInfoNtf,

        ///// <summary>
        ///// 进化士兵
        ///// </summary>
        //EvolveSoldierAck,

        /// <summary>
        /// 刷新进化红点
        /// </summary>
        OnRefreshEvoRedPt,



        #endregion

        /// <summary>
        /// 离线金币奖励
        /// </summary>
        OfflineRevenueRefresh,
        /// <summary>
        /// 离线石头奖励
        /// </summary>
        OfflineStoneRefresh,
        /// <summary>
        /// 离线木材奖励
        /// </summary>
        OfflineWoodRefresh,
        /// <summary>
        /// 离线铁矿奖励
        /// </summary>
        OfflineIronRefresh,

        /// <summary>
        /// 切换离线采集物品
        /// </summary>
        ChangeOfflineItemIDRefresh,

        #region 联盟礼物
        /// <summary>
        /// 联盟礼物信息
        /// </summary>
        UnionGiftInfoAck,
        /// <summary>
        /// 领取礼物
        /// </summary>
        ReceiveGiftAck,
        /// <summary>
        /// 领取宝箱
        /// </summary>
        ReceiveBigChestsAck,

        UnionGiftAnonymityAck,
        #endregion

        #region TaskSolider
        /// <summary>
        /// 训练士兵前端 //pars 0为类型 
        /// </summary>
        TrainSolider_Client,
        /// <summary>
        /// 合成士兵前端 //pars 0为类型 
        /// </summary>
        ComposeSolider_Client,
        /// <summary>
        /// 士兵升级 （包含训练等级提升） //pars 0为类型 1为等级
        /// </summary>
        SoliderLevelUp_Client,
        ///
        /// 士兵战力详情刷新
        /// 
        SoliderPowerInfoRefresh,

        #endregion

        #region 战争记录

        /// <summary>
        /// pvp战争记录信息
        /// </summary>
        PvpInfoAck,

        /// <summary>
        /// 联盟留言板消息
        /// </summary>
        GetUnionMessageAck,

        #endregion

        #region 收藏夹
        /// <summary>
        /// 收藏删除
        /// </summary>
        CoordsFavoritesDelAck,
        /// <summary>
        /// 收藏夹刷新
        /// </summary>
        CoordsFavoritesRefresh,
        #endregion

        #region 好友相关

        FriendsSearchRefresh,
        FriendsListRefresh,
        FriendApplyListRefresh,
        FriendBlackListRefresh,
        FriendVisitNtf,

        #endregion

        #region 联盟帮助
        /// <summary>
        /// 消耗加速
        /// </summary>
        CostSpeedUpPointAck,
        /// <summary>
        /// 请求联盟帮助
        /// </summary>
        UnionHelpApplyAck,
        /// <summary>
        /// 联盟帮助别人
        /// </summary>
        UnionHelpAck,
        /// <summary>
        /// 联盟帮助列表
        /// </summary>
        UnionHelpListAck,
        /// <summary>
        /// 帮助推送
        /// </summary>
        UnionHelpNtf,
        /// <summary>
        /// 帮助删除
        /// </summary>
        UnionHelpDelNtf,
        /// <summary>
        /// 联盟帮助状态
        /// </summary>
        UnionHelpStateChange,
        /// <summary>
        /// 刷新申请次数
        /// </summary>
        UnionHelpCountAck,
        /// <summary>
        /// 刷新联盟红点
        /// </summary>
        RefreshAllianceHelpRed,
        #endregion

        #region 王座战

        /// <summary>
        /// 王座信息数据刷新
        /// </summary>
        ThroneWarUpdateInfo,
        /// <summary>
        /// 王座战刷新国王信息
        /// </summary>
        ThroneWarUpdateKingInfo,
        /// <summary>
        /// 王座战刷新国王记录信息数据
        /// </summary>
        ThroneWarKingHistoryInfo,
        /// <summary>
        /// 王座战刷新选中角色信息
        /// </summary>
        ThroneWarUpdateAllianceRoleRank,
        /// <summary>
        /// 王座战刷新选中角色信息搜索返回
        /// </summary>
        ThroneWarUpdateSelectRoleSearch,
        /// <summary>
        /// 王座战排行榜数据刷新
        /// </summary>
        ThroneWarDamageRankUpdate,
        /// <summary>
        /// 王座战剩余的礼包数量更新（所有的礼包数量）
        /// </summary>
        ThroneWarLeftGiftCountUpdate,
        /// <summary>
        /// 王座战分配礼包信息刷新
        /// </summary>
        ThroneWarDistributeGiftUpdate,

        /// <summary>
        /// 王座战礼包数据刷新
        /// </summary>
        ThroneWarDistributeGiftNtf,

        /// <summary>
        /// 王座战礼包列表数据刷新
        /// </summary>
        ThroneWarGiftListUpdate,

        /// <summary>
        /// 哨塔争夺战礼包列表刷新
        /// </summary>
        TowerWarGiftListUpdate,

        /// <summary>
        /// 王座战官职信息更新
        /// </summary>
        ThroneWarPositionUpdate,


        //王座战国王技能相关
        /// <summary>
        /// 王座战技能相关数据刷新
        /// </summary>
        ThroneWarKingSkillUpdate,
        /// <summary>
        /// 王座战国王技能日志信息刷新
        /// </summary>
        ThroneWarKingSkillLogUpdate,
        /// <summary>
        /// 王座战查询目标数据刷新
        /// </summary>
        ThroneWarKingSkillSearchTargetUpdate,
        /// <summary>
        /// 国王信息变更
        /// </summary>
        ThroneKingInfoUpdate,
        /// <summary>
        /// 国王公告变更
        /// </summary>
        ThroneKingNoticeInfoUpdate,
        /// <summary>
        /// 国王公告内容变更
        /// </summary>
        ThroneKingNoticeContentUpdate,
        /// <summary>
        /// 商店数据修改刷新
        /// </summary>
        ThroneKingStoreNumberUpdate,
        #endregion

        #region UI层级
        ResetLayer,
        #endregion

        #region 迁服

        /// <summary>
        /// 联盟迁服信息刷新
        /// </summary>
        AllianceMigrationServerUpdate,
        /// <summary>
        /// 服务器列表
        /// </summary>
        MigrationServersAck,

        /// <summary>
        /// 取消联盟迁服
        /// </summary>
        UnionMigrateCancelAck,
        #endregion

        #region 建筑记录
        /// <summary>
        /// 建筑记录
        /// </summary>
        UnionLandRecordAck,

        /// <summary>
        /// 建筑记录新增
        /// </summary>
        UnionLandRecordNtf,
        #endregion

        #region 图鉴

        /// <summary>
        /// 刷新图鉴信息显示
        /// </summary>
        UI_Update_IiiustratedBook,

        #endregion

        #region 赛季
        /// <summary>
        /// 赛季成就
        /// </summary>
        SeasonAchievementAck,
        /// <summary>
        /// 赛季商店
        /// </summary>
        NewSeasonShopNtf,
        /// <summary>
        /// 总查询
        /// </summary>
        SeasonChallengeInfoQueryAck,
        /// <summary>
        /// 成就自选奖励
        /// </summary>
        SeasonChallengeOptionalGiftChooseAck,
        /// <summary>
        /// battlepass刷新
        /// </summary>
        SeasonChallengeBattlePassRefresh,
        /// <summary>
        /// 赛季好礼刷新
        /// </summary>
        SeasonChallengeGiftRefresh,
        #endregion

        #region 英雄相关事件整理

        UIHeroStateChange,
        DeputyHeroChange,
        UIHeroDeputyShowPos,
        UIHeroShowViewDeputyShowPos,

        /// <summary>
        /// 点击选中、取消英雄升级操作
        /// </summary>
        ClickHeroItemToLevelUp,

        UIHeroUpgradeEffectPlay,

        UIHeroUpgradePowerUp,

        FetterPropRefresh,

        UIHeroRetainCardRefresh,



        UIHeroMaxPopAddItem,
        UIHeroMaxPopRemoveItem,

        UIHeroMaxPopRefresh,

        #endregion

        #region 神圣领地活动
        UIGetShieldInfoAck,
        #endregion

        #region 切换服务器准备完成
        SwitchServerPrepareComplete,
        ChangeIsNeedDownCityState,
        CrossServerChange,
        #endregion

        #region 昼夜系统

        /// <summary>
        /// 白天黑夜状态刷新
        /// </summary>
        UpdateDayNight,

        /// <summary>
        /// 刷新夜晚光照效果
        /// </summary>
        UpdateNightLight,

        /// <summary>
        /// 刷新白天光照效果
        /// </summary>
        UpdateDayLight,

        /// <summary>
        /// 刷新夜晚军队光圈Renderer
        /// </summary>
        UpdateNightTroopLightRingRenderer,

        /// <summary>
        /// 隐藏军队光照光圈信息
        /// </summary>
        HideTroopLightRing,

        #endregion

        #region 地图分块事件

        UpdateRegionStatus,

        #endregion

        #region 推送权限

        /// <summary>
        /// 刷新推送的权限信息
        /// </summary>
        UpdatePushPermission,

        #endregion

        #region 巨龙之战

        #region 巨龙报名
        /// <summary>
        /// 活动数据刷新
        /// </summary>
        LocalDragonActvDataUpdateAck,
        /// <summary>
        /// 选择参战时间
        /// </summary>
        LocalDragonArenaSelectTimeAck,
        /// <summary>
        /// 报名
        /// </summary>
        DragonArenaSignUpAck,

        /// <summary>
        /// 巨龙选择参赛时间
        /// </summary>
        UISelectSessionConfirm,

        /// <summary>
        /// 巨龙刷新报名时区信息
        /// </summary>
        DragonWar_UpdateSignTimeZone,

        #endregion

        #region 巨龙人员管理

        /// <summary>
        /// 巨龙战场刷新战斗人员信息
        /// </summary>
        DragonWar_UpdateBattleMemberManageInfo,

        #endregion

        /// <summary>
        /// 巨龙活动报名开始倒计时
        /// </summary>
        DgWarApplyTime,

        /// <summary>
        /// 巨龙hud双方信息刷新
        /// </summary>
        DragonWar_UpdateHudInfo,

        /// <summary>
        /// 巨龙活动状态变更
        /// </summary>
        DgWarActvStateChange,

        #region 巨龙之战 战斗记录

        /// <summary>
        /// 巨龙之战刷新战斗记录
        /// </summary>
        DragonWar_UpdateFightRecord,

        #endregion


        #region 巨龙之战 战场数据

        /// <summary>
        /// 巨龙之战 刷新战况信息
        /// </summary>
        DragonWar_UpdateBattlefieldInfo,

        /// <summary>
        /// 巨龙积分数据刷新
        /// </summary>
        DragonWar_UpdateScoreInfo,

        /// <summary>
        /// 巨龙刷新列表
        /// </summary>
        DragonWar_UpdateEntityList,
        #endregion

        #region 巨龙援助

        /// <summary>
        /// 巨龙援助返回自己的部队
        /// </summary>
        DgWarWarBackSelfTroop,
        #endregion


        #region 巨龙之战商店

        /// <summary>
        /// 巨龙之战刷新商店所有信息数据
        /// </summary>
        DragonWar_UpdateShopAllItemInfo,
        /// <summary>
        /// 巨龙之战刷新商店某一商品信息数据
        /// </summary>
        DragonWar_UpdateShopItemInfo,

        #endregion

        #region 竞技场

        /// <summary>
        /// 竞技场积分数据刷新
        /// </summary>
        Arena_UpdateSoreInfo,
        Arena_UpdateMatchInfo,
        Arena_DiscardMatchInfo,
        Arena_UpdateRewardListInfo,
        Arena_RankUpdate,

        #endregion


        #endregion
        #region 拍卖行
        ActvTemlAuctionNtfSuccess,
        AuctionGetSelfRecordSuccess,
        AuctionGetAllSoldRecordSuccess,
        AuctionBidCancelSuccess,
        AuctionBidSuccess,
        ActvTempAuctionSellerUpdateSuccess,
        #endregion
        #region 拍卖行
        ActvTmplCrystalBallNtf,
        ReqCrystalBallOpenUISuccess,
        CrystalBallSelectHeroSuccess,
        CrystalBallClick,
        ActvTemlCrystalBallUpdateNtf,
        ShowGreenStone,
        #endregion
        #region 召唤兽
        SummonPetActiveSuccess,
        SummonPetUpLvlSuccess,
        SummonPetUseSuccess,
        SummonPetDecomposeSuccess,
        SummonPetSkinChangeSuccess,
        #endregion
        #region 首充3日购
        FirstChargeGetRewardSuccess,
        FirstChargeNtfDataSuccess,
        FirstChargeNewNtfDataSuccess,
        RefreshFirstChargeRedPoint,
        RefreshNewAccumulateUI,
        #endregion
        #region 跨服最强领主
        GetCrossCommanderQuestInfo,
        GetCrossCommanderQuestRewardSuccess,
        GetCrossCommanderStageRewardSuccess,
        #endregion
        #region 调查问卷
        AnswerDisplayState,
        #endregion
        KvKBuildingEntityUpdate,
        OnKvkRankListNtf,
        UnionTaskCreateComplete,
        ServerTaskRecordComplete,
        ServerShopDataRefush,
        FractureTaskUpdate,
        BuyFractureTaskItemComplete,
        PhyPowerScoreCfgNtf,
        KvKBuildingInfomationUpdate,
        KvKThroneOccInfoUpdate,
        /// <summary>
        /// kvk战场军情红点刷新
        /// </summary>
        KvKBuildingInformationNumberUpdate,
        /// <summary>
        /// 内城怪物死亡事件
        /// </summary>
        CityMonsterDieEvent,

        KvkInfoRefresh,
        /// <summary>

        /// 怪物发生传送
        /// </summary>
        MonsterTransmission,

        /// 破碎之地占领信息更新
        /// </summary>
        FracturedLandsMiniMapUpdate,
        FracturedLandsBuildStateUpdate,

        FracturedLandsSupplyDataUpdate,
        FracturedLandsUpdateSupplyReturnVal,
        FracturedLandsRefresSupplyReturnRedPoint,
        UpdateGarrisonTroopEvent,

        /// <summary>
        /// 新的玩家信息请求头像协议
        /// </summary>
        UpdateMapPlayerInfoPanel,
        FracturedLandsAchievementUpdate,
        FracturedLandsTrialsRankUpdate,
        FracturedLandsTrialsUpdate,
        FracturedLandsTrialsCallUpdate,

        FracturedLands2StageUpdate,
        GetFracturedLands2QuestRewardSuccess,

        FracturedLands3StageUpdate,
        GetFracturedLands3QuestRewardSuccess,
        IceBossCallUpdate,
        #region 资源包下载

        /// <summary>
        /// 刷新资源包下载状态
        /// </summary>
        UpdateResPackDownloadStatus,

        /// <summary>
        /// 刷新资源包下载进度
        /// </summary>
        UpdateResPackDownloadProgress,

        /// <summary>
        /// 资源包下载完成
        /// </summary>
        ResPackDownloadDone,

        #endregion
        UpdateHalloweenVisible,
        /// <summary>
        /// 刷新新版战力之王活动
        /// </summary>
        UpdateRefreshDailyPowerKing,
        UpdateRefreshDailyPowerKingScore,
        RankPowerKingRankData,

        //摄像机视野刷新
        UpdateCameraViewPortInfo,

        //世界杯活动
        WorldCupShopBuySuccess,
        WorldCupPuizSuccess,
        //圣诞节活动
        UpdateChristmasAnswerUI,
        None,

        #region 计时赛
        UpdateTTRound,
        UpdateTTGold,
        OpenTimeTrial,
        #endregion
        RefreshLavaCaveLayer,
        RefreshLavaCaveRewardUI,

        LavaCaveFlyItemIcon,


        ScrollManekoCat,
        RefreshManekoCatUI,
        RefreshManekoCatUI1,

        /// <summary>
        /// 战力限制
        /// </summary>
        RefushPowerLimit,
        //复活节2023
        RefreshArchaeologyUI,
        RefreshArchaeologyIntegralUI,
        //联盟战力刷新
        AlliancePowerRefresh,
        //冰霜试炼召唤
        ActvIceBossCallAck,


        #region  女神的试炼

        //女神试炼结果
        TrialsBattleResult,

        GoddessChallengeDataRefush,
        GoddessChallengeLayerReward,

        GoddessChallengeQuestReward,
        GoddessChallengeQuestRefush,

        GoddessChallengeShopRefush,
        GoddessChallengeShopBuy,
        TrialChallengeLineUpSuccess,
        TrialChallengeResult,

        GoddessChallengeUnlock,
        //个人争霸赛
        GoddessTournamentRefresh,
        #endregion


        // 个人排行活动
        GoddessPowerRankRefresh,



        GetActvRedisRankAck,

        GetAllSkinAck,

        #region  鲜花榜

        FlowerRankQuestUpdate,
        FlowerRankSendFlower,
        FlowerRankRecordList,
        FlowerRankChangeSkin,

        #endregion


        #region 军团
        //军团信息更改
        LegionMessageChange,

        //军团创建事件
        LegionCreate,
        //军团信息
        LegionInfoDetail,
        //刷新列表
        LegionListUpdate,
        //退出军团
        QuitLegion,
        //刷新军团审核列表
        UpdateLegionProcessList,
        //请求或取消请求加入军团
        ApplyKvkLegion,
        //转让军团
        TransferenceLegion,
        #endregion

        // 远征科技更新
        RefreshKVKTech,
        // kvk card刷新
        KVKCardRefresh,

        /// <summary>
        /// 公告相关
        /// </summary>
        AnnounceRefresh,
        AnnounceReadStatusRefresh,

        #region  联盟成就

        AllianceAchieve_SeekHelpReward,
        AllianceAchieve_LotteryInfo,
        AllianceAchieve_HelpRedDotRefush,
        AllianceAchieve_QuestReward,
        AllianceAchieve_QuestUpdate,
        AllianceAchieve_HelpReq,
        AllianceAchieve_StageChange,
        #endregion


        FractureLand3SignSuccess,

        #region 守卫币

        UpdateGuardCoin,

        #endregion


        HeroLvlUpAck,
        HeroStepsUpgradeAck,
        #region 联盟转服

        OnGetServerCrossListAck,
        OnGetUnionCrossListAck,
        OnGetUnionCrossMemeberListAck,
        OnUnionCrossPublishAck,
        OnUnionCrossModifyAck,
        OnPlayerCrossApplyAck,
        OnUnionCrossCancelApplyAck,
        OnGetUnionCrossApplyListAck,
        OnUnionCrossCheckApplyAck,
        OnGetUnionCrossMessageListAck,
        OnUnionCrossMessageAck,

        #endregion

        #region 支付订单
        CreateOrderAck,
        #endregion



        #region 赛季挑战

        SeasonChallengeTaskGetRewardAck,

        #endregion

        OnEasterModuleDataUpdate,


        #region 新巨龙

        OnRefreshCrossDg,
        OnRefreshCrossDgShop,
        OnRefreshCrossDgRank,
        OnCrossDgPlayerRankRefresh,
        #endregion


        //新巨龙
        NewDragonWarBuildingOccupyed,
        NewDragonWarBossKilled,
        NewDragonWarResult,
        NewDragonWarBossBorn,
        NewDragonWarPentaKill,
        NewDragonWarPlayerPointChange,
        NewDragonWarBuffChange,
        NewDragonWarRankDataChange,
        NewDragonWarBossDamageDetailUpdate,
        NewDragonWarBossShowBuff,
        NewDragonWarBossSelfDataChange,

        /// <summary>
        /// 转盘抽奖记录
        /// </summary>
        ReufshTurntableRecord,
        SimpleModeChange,
        //光明战场
        LBPlayerBattleUIDataRefresh,
        LBBattleRankDataRefresh,
        SupplyRefreshIdx,
        LBUseSupplySuccess,
        LBUseItemSuccess,
        ToolsRefreshIdx,
        BrightBattleItemSelectSuccess,
        BrightBattleShop,
        CastleEffectSkillSelectUpdate,
        BrightBattleSearchMonster,
        BrightBattleSearchMonsterSelectUpdate,
        UseCastleEffectSkillSuccess,
        ShowLightDiamond,
        #region 功勋相关
        RefreshBadge,
        #endregion

        #region 神器升级相关
        ArtifactLevelupSelectItemChange,
        ArtifactLevelupRefreshStatus,
        ArtifactLevelupCallback,
        ArtifactPromotionCallback,
        #endregion

        #region 联盟活动礼物
        /// <summary>
        /// 打开联盟活动礼物主面板
        /// </summary>
        UnionActvGiftPanelNtf,

        /// <summary>
        /// 刷新联盟活动礼物分配
        /// </summary>
        UnionActvGiftAllocateAck,

        #endregion
        ///联盟任职选中刷新
        UnionOfficialSelectRefresh,
        AllianceMemberOfficialChange,

        #region kvk4.0相关
        KvkDonateReqCallback,
        #endregion

        #region 国服
        SDK37LoginEnd,
        #endregion
        #region CP弹窗
        CpWindowDisplayState,
        CpWindowClose,
        #endregion
        UIArtifactRetainCardRefresh,




        /// <summary>
        /// 头像红点
        /// </summary>
        AvatarFrameRedCount,
        

        /// <summary>
        /// 上阵英雄后通知
        /// </summary>
        DeployableHeroes,


        ExChangeHeroEnd,
        TestUseHero,
        TestUseHeroEnd,

        /// <summary>
        /// 解锁条件变化
        /// </summary>
        OnUnlockConditionChanged,

        /// <summary>
        /// 建筑解锁
        /// </summary>
        OnBuildUnlock,


        #region 建筑重建
        /// <summary>
        /// 英雄装备成功构建等级
        /// </summary>
        CityBuildingRefresh,
        /// <summary>
        /// 显示对应建筑的镜头和缩放
        /// </summary>
        ClickToShowCityBuilding,
        
        OnShowBuildingPanel,

        CityBuildingComplete,
        /// <summary>
        /// 主建筑气泡刷新
        /// </summary>
        MainBuildBubbleRefresh,
        #endregion

        FlyFirstChargeBtn,
        HeroTabClick,
        ClickTroopTab,
        UIMain_FlyItemEffect_Task_Pos,
        AllianceHelpClick,
        
        HeroVedioSkinSelect,
        
        HeroCollectGetAward,
        //刷新日历
        RefreshCalendar,
        
        #region K3

        K3LockIDRefresh,
        K3AreaingItemRefresh,
        K3TaskRefresh,
        K3TaskComplete,
        K3TaskComplete_UIMerge,
        K3TaskComplete_CatchSettlement_2,
        K3TaskComplete_CatchSettlement_3,
        K3TaskAutoToDO,
        K3TaskCompleteAnimation,
        K3ShopDataRefresh,
        K3GridNtf,
        K3AreaNtf,
        K3BoxNtf,
        K3ClickBox,
        K3MergeItemLevelUp,//合成道具
        K3MergeUnlockIdRefresh,
        K3MergeBoxHeroRefresh,
        K3MergeStoreHouseRefresh,
        K3MergeStoreHouseAdd, // 合成仓库增加item

        #region 主城能量计

        EnergyGaugeUpdate,

        #endregion

        /// <summary>
        /// 英雄一键上阵成功回调
        /// </summary>
        OnMergeHeroBatchSetCallback,

        /// <summary>
        /// 棋盘更新
        /// </summary>
        K3GridDataRefresh,
        K3MainUI_PhotoOrMain,
        K3MergeSoldierStoreRefesh,
        K3SoldierPanelRefresh,
        K3OfflineGoldPanelRefresh,
        K3HeroDataToDoSelHero,

        K3NewHeroShowClosed,

        K3NewTechSelected,

        K3PhotoWallSelect,

        K3TaskCompletePhotoWall,

        K3BuildingLvUp,

        K3UIMerge_ToHero,
        K3UIMerge_ToMerge,
        K3UIMerge_SelectHeroType,

        K3UIHero_ExChangeHeroSelectHeroType,
        K3UIHero_ExChangeHeroSelectHero,

        K3UIHero_Skill_Select,
        K3UIHero_GoodItem_Select,

        GoToWorldEnd,
        RefreshCaravanLineUpData,
        PowerFlyStartPointSet,
        
        // 任务相关
        OnChapterJumpOut,
        PlayChapterItemFly,
        OnHeroSpineUpdate,
        OnHeroItemClick,
        OnChapterRewardReceive,
        OnMainTaskUpdate,
        OnMergeTaskChanged
        #endregion
    }
}



