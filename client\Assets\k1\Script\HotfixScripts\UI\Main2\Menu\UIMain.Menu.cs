﻿using Common;
using cspb;
using Cysharp.Threading.Tasks;
using DeepUI;
using Game.Config;
using Game.Data;
using Game.Sprite.Fight;
using K1;
using Logic;
using TFW;
using TFW.Localization;
using UI.Utils;
using UnityEngine;
using UnityEngine.EventSystems;

namespace UI
{

    /// <summary>
    /// 主界面菜单栏
    /// </summary>
    public partial class UIMain2
    {


        #region 属性字段信息

        ///// <summary>
        ///// 主界面菜单栏Data数据
        ///// </summary>
        //[PopupField("content/MenuRoot")]
        //public UIMainMenuData mainMenuData;
         
        /// <summary>
        /// 限制点击其他分页时间
        /// </summary>
        private const float LIMIT_CLICK_OTHER_TIME = 0.5f;

        /// <summary>
        /// 限制点击相同分页时间
        /// </summary>
        private const float LIMIT_CLICK_SAME_TIME = 0.1f;

        /// <summary>
        /// 上次点击的时间
        /// </summary>
        private float lastClickTime;

        /// <summary>
        /// 进入游戏后 默认显示训练
        /// </summary>
        MainMenuType lastChooseType = MainMenuType.CITY;

        #endregion


        #region 初始化

        /// <summary>
        /// 初始化菜单栏信息数据
        /// </summary>
        public void InitMenu()
        {

            //添加菜单栏事件监听
            //AddMenuListener();
            //初始化菜单栏功能
            //InitMenuFunction();

             
        }

        /// <summary>
        /// 反向初始化菜单栏
        /// </summary>
        public void UnInitMenu()
        {

            //移除菜单栏事件监听
            //RemoveMenuListener();
            //初始化菜单栏功能
            //UnInitMenuFunction();

          
        }


        /// <summary>
        /// 添加菜单栏事件监听
        /// </summary>
        //private void AddMenuListener()
        //{
        //    //添加菜单按钮事件监听
        //    if (mainMenuData != null && mainMenuData.menuArr != null)
        //    {
        //        for (int i = 0; i < mainMenuData.menuArr.Length; i++)
        //        {
        //            BindClickListener(mainMenuData.menuArr[i].obj, OnClickMenuBtn);
        //        }
        //    }
        //}

        /// <summary>
        /// 移除菜单栏事件监听
        /// </summary>
        //private void RemoveMenuListener()
        //{

        //    //移除菜单按钮事件监听
        //    if (mainMenuData != null && mainMenuData.menuArr != null)
        //    {
        //        for (int i = 0; i < mainMenuData.menuArr.Length; i++)
        //        {
        //            RemoveClickListener(mainMenuData.menuArr[i].obj);
        //        }
        //    }
        //}




        #endregion



        #region 数据刷新显示


        /// <summary>
        /// 刷新主界面菜单信息展示
        /// </summary>
        /// <param name="type"></param>
        /// <param name="isCheckClickTime"></param>
        /// <param name="data"></param>
        /// <param name="isForceUpdate">true 同一类型也会走刷新的逻辑处理</param>
        private async UniTask UpdateMainMenuInfo(MainMenuType type)
        {
            // AdEventGameMgr.I.SpeedUp(type == MainMenuType.CITY,false);
            SceneManager.I.IsChangeDonw = false;
            //没有此种数据，那么就不处理
            //if (type == MainMenuType.NONE)
            //{
            //    SceneManager.I.IsChangeDonw = true;
            //    //D.Error?.Log($"type is None");
            //    return;
            //}

            //检测功能开启状态
            if (!MainFunctionOpenUtils.IsMenuFunctionOpen(type, true))
            {
                SceneManager.I.IsChangeDonw = true;
                //GetMenuFunctionType(type).PlayLockAni();
                //D.Error?.Log($"Function not Open");
                return;
            }

            //在点击限制时间范围内，那么就不进行处理

            //根据菜单类型进行显示切换
            if (GameData.I.MainData.CurrMenuType == type)
            {
               
                //处理引导相关
                //if (GameData.I.MainData.CurrMenuType == MainMenuType.PRISON)
                //    EventMgr.FireEvent(TEventType.HideTriggerGuide, GameData.I.MainData.CurrMenuType);
                //else
                    EventMgr.FireEvent(TEventType.HideTriggerGuide);

                //已经选中状态的逻辑处理
                //var menuFunction = GetMenuFunctionType(GameData.I.MainData.CurrMenuType);
                //if (menuFunction != null)
                //{
                //    //播放动画和声音
                //    PlayClickAnim(GameData.I.MainData.CurrMenuType);
                //    PlayClickSound();
                //    menuFunction.ClickCurrMenuBtn(data);
                //}
                SceneManager.I.IsChangeDonw = true;
            }
            else
            {
                EventMgr.FireEvent(TEventType.DragonDataRefresh);
                
                //播放声音
                PlayClickSound();
                //刷新声音通道信息
                UpdateAudioChannelVolume(type);

               
                ///执行点击逻辑 (打开界面)
                //var menuFunction = GetMenuFunctionType(type);
                //if (menuFunction != null)
                //{
                //    clickOk = await menuFunction.ClickMenuBtn(data);

                //}

               
                //var currMenuFunction = GetMenuFunctionType(GameData.I.MainData.CurrMenuType);
                //if (currMenuFunction != null)
                //    currMenuFunction.ExitMenuPanel(type);

                //if (type == MainMenuType.ALLIANCE)
                //{
                //    if (LPlayer.I.UnionID != 0)
                //    {
                //        //场景信息切换
                //        SceneManager.I.SwitchScene((int)GameData.I.MainData.CurrMenuType, (int)type);
                //    }
                //}
                //else
                {
                    //场景信息切换
                    SceneManager.I.SwitchScene((int)GameData.I.MainData.CurrMenuType, (int)type);
                }


                //if (menuFunction != null)
                //    menuFunction.EnterMenuPanel();

                //同步当前显示菜单栏
                GameData.I.MainData.UpdateCurrMenuType(type);

                //if (GameData.I.MainData.CurrMenuType != MainMenuType.PRISON && type != MainMenuType.PRISON)
                //{
                //    //保险起见关闭主城界面
                //    //SceneManager1.I.ClearScene<UIMainCity>();
                //}

                if (type == MainMenuType.WORLD)
                {
                    if (LCrossServer.I.CurrSceneType == SceneType.SceneTypeKvk)
                        GameData.I.ActivityData.KvkSuppressReq();
                    //if (GameData.I.BrightBattleFieldGameData.needPopSettlementUI)
                    //{
                    //    GameData.I.BrightBattleFieldGameData.needPopSettlementUI = false;
                    //    if (GameData.I.BrightBattleFieldGameData.LBPlayerBattleUINtfData != null &&
                    //        !GameData.I.BrightBattleFieldGameData.LBPlayerBattleUINtfData.isViewer
                    //        && GameData.I.BrightBattleFieldGameData.NeedDisplayGuide())
                    //    {
                    //        PopupManager.I.ShowPanel<UIActivityBrightBattleGuide>();
                    //    }

                    //    PopupManager.I.ShowPanel<UIActivityBrightBattleField_Settlement>();
                    //}
                }

                //刷新内城红点
                UpdateCityRedInfo();
                //刷新英雄红点
                if (LGameRedPoint.I.OpenClientRed)
                    UpdateHeroRedInfo();
                //刷新联盟红点
                UpdateAlliacneRedInfo();
                //刷新三日购红点
                //await UpdateFirstChargeRedPoint();
                //通知外部刷新显示
                EventMgr.FireEvent(TEventType.OnSwitchMainUI, type);
            }
            if (type == MainMenuType.CITY)
            {
                //if(GameData.I.LevelData.CurrGameLevel >= MetaConfig.Hero_System_OpenLevel)
                GameData.I.LineUpData.TryShowNewHero();
                //开始关卡战斗
                //if (FightManager.I.IsFightEnd || UITools.GetActiveChildren(GameInstance.EnemySpriteRootTran) == 0) //防止卡死判断，无敌人的情况下 进行开启战斗
                //    await FightManager.I.StartLevelFight();

                //检测五星好评是否自动打开
                FivePraiseMgr.I.AutoOpenFiveParise();
            }
            //if (type == MainMenuType.HERO)
            //    GameData.I.LineUpData.needOpenUIHeroList = true;
            //判断玩家是否跨服，是否需要显示回家按钮
            if (type == MainMenuType.WORLD)
            {
                UpdateCrossSeverUI();
            }
        }

        private void UpdateCrossSeverUI()
        {
            if (LPlayer.I.IsCrossServer)
            {
                if (_crossServerDesc)
                    _crossServerDesc?.SetActive(LCrossServer.I.IsNeedDownCity);

                if (_crossServerText)
                {
                    if (GameData.I.ServerWarData.IsInServerWar)
                    {
                        _crossServerText.text = LocalizationMgr.Get("Kvk_Teleport_Desc");
                    }
                    else if (LCrossServer.I.CurrSceneType == cspb.SceneType.SceneTypeKingWar)
                    {
                        _crossServerText.text = LocalizationMgr.Get("King_War_teleport_tips");
                    }
                    else
                    {
                        _crossServerText.text = LocalizationMgr.Get("Kvk_Teleport_Desc");
                    }
                }

                if (crossServerGoHomeObj)
                {
                    RemoveClickListener(crossServerGoHomeObj);
                    crossServerGoHomeObj.SetActive(true);

                    BindClickListener(crossServerGoHomeObj, OnClickSendGoHomeServer);
                }


                if (favoriteObj)
                {
                    favoriteObj.SetActive(LCrossServer.I.CurrSceneType == SceneType.SceneTypeKvk);
                }


                //if (GameData.I.DragonWarData.SignData.IsDragonWaring)
                //{
                //    //刷新收藏按钮显示ctivity

                //    EventMgr.FireEvent(TEventType.SwitchServerPrepareComplete);
                //    if (crossServerGoHomeObj) crossServerGoHomeObj.SetActive(false);
                //}
                //else if (GameData.I.DragonWarNewData.IsInNewDragonWaring)
                //{
                //    //刷新收藏按钮显示
                //    EventMgr.FireEvent(TEventType.SwitchServerPrepareComplete);
                //    if (crossServerGoHomeObj) crossServerGoHomeObj.SetActive(false);
                //}
                //else if (GameData.I.DragonWarNewData.IsInNewDragonWaring)
                //{
                //    //刷新收藏按钮显示
                //    EventMgr.FireEvent(TEventType.SwitchServerPrepareComplete);
                //    if (crossServerGoHomeObj) crossServerGoHomeObj.SetActive(false);
                //}
                // if (GameData.I.BrightBattleFieldGameData.IsInBrightBattleFieldWaring)
                //{
                //    //刷新收藏按钮显示
                //    EventMgr.FireEvent(TEventType.SwitchServerPrepareComplete);
                //    if (crossServerGoHomeObj) crossServerGoHomeObj.SetActive(true);
                //}

            }
            else
            {
                _crossServerDesc?.SetActive(LCrossServer.I.IsNeedDownCity);
                if (GameData.I.ServerWarData.IsInServerWar)
                {
                    if (_crossServerText)
                        _crossServerText.text = LocalizationMgr.Get("Kvk_Teleport_Desc");
                }
                else if (LCrossServer.I.CurrSceneType == cspb.SceneType.SceneTypeKingWar)
                {
                    if (_crossServerText)
                        _crossServerText.text = LocalizationMgr.Get("King_War_teleport_tips");
                }
                else
                {
                    if (_crossServerText)
                        _crossServerText.text = LocalizationMgr.Get("Kvk_Teleport_Desc");
                }
                // if (favoriteObj)
                //     favoriteObj.SetActive(true);

                if (crossServerGoHomeObj)
                    crossServerGoHomeObj.SetActive(false);
            }
            if (LCrossServer.I.CurrSceneType == SceneType.SceneTypeGVGDragon)
            {
                DragonWarUtils.UpdateNeedRotationCameraSign();
            }
        }






        /// <summary>
        /// 获取点击对象的菜单类型
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        //private MainMenuType GetClickMenuType(GameObject obj)
        //{
        //    if (obj == null
        //        || mainMenuData == null
        //        || mainMenuData.menuArr == null)
        //        return MainMenuType.NONE;

        //    if (mainMenuData != null && mainMenuData.menuArr != null)
        //    {
        //        for (int i = 0; i < mainMenuData.menuArr.Length; i++)
        //        {
        //            if (mainMenuData.menuArr[i].obj == obj)
        //                return mainMenuData.menuArr[i].type;
        //        }
        //    }

        //    return MainMenuType.NONE;
        //}


   

        /// <summary>
        /// 刷新界面显隐状态
        /// </summary>
        /// <param name="active"></param>
        public void UpdateMenuActive(bool active)
        {

        }

        /// <summary>
        /// 刷新锁定菜单动画
        /// </summary>
        private void UpdateLockMenuAnim()
        {

        }



        #endregion


        #region 动画播放

        /// <summary>
        /// 播放点击动画
        /// </summary>
        /// <param name="type"></param>
        //private void PlayClickAnim(MainMenuType type)
        //{
        //    if (type == MainMenuType.NONE)
        //        return;

        //    var index = (int)type - 1;

        //    if (mainMenuData == null
        //        || mainMenuData.menuArr == null
        //        || mainMenuData.menuArr.Length == 0
        //        || mainMenuData.menuArr.Length < index)
        //        return;

        //    //var menu = mainMenuData.menuArr[index];
        //    //if (menu != null)
        //    //{
        //    //    var clip = menu.anim.GetClip("MainMenuBtnAnimClick");
        //    //    if (clip != null)
        //    //    {
        //    //        menu.anim.CrossFade("MainMenuBtnAnimClick");
        //    //    }
        //    //}
        //}

        #endregion


        #region 声音播放


        /// <summary>
        /// 点击音效 - 由于新增的菜单按钮 不能加ButtonScale组件 所以需要单独调用
        /// </summary>
        private void PlayClickSound()
        {
            //if (AudioManager.Instance != null)
            //{
            //    var cfg = Cfg.C.CAudioList.I(AudioConst.UIGeneric);
            //    if (cfg != null)
            //    {
            //        AudioManager.Instance.Play((AudioChannelType)cfg.Scene, $"{ResourceDict.Audio}/{cfg.Asset}",
            //            cfg.Time, 1, 1, cfg.Priority, (AudioPlayMode)cfg.Typ, cfg.Mode == 1, null);
            //    }
            //}
        }

        /// <summary>
        /// 刷新声音通道音量
        /// </summary>
        private void UpdateAudioChannelVolume(MainMenuType type)
        {
            //if (type == MainMenuType.HERO
            //    || type == MainMenuType.ALLIANCE)
            //{
            //    AudioManager.Instance.ModifyOtherAudioChannelVolume(AudioConst.AUDIO_CHANNEL_VOLUME_TIME,
            //                AudioChannelType.UI);
            //}
            //else
            //{
            //    AudioManager.Instance.ModifyOtherAudioChannelVolume(AudioConst.AUDIO_CHANNEL_SRC_VOLUME_TIME,
            //                            AudioChannelType.UI);
            //}
        }

        #endregion

         

        #region 事件处理

        /// <summary>
        /// 主界面刷新显示菜单栏信息
        /// </summary>
        /// <param name="objs"></param>
        [PopupEvent(TEventType.UIMain_Update_ShowMenu)]
        private async void OnUpdateShowMenu(object[] objs)
        {

            if (objs == null || objs.Length == 0)
                return;

            var type = (MainMenuType)objs[0];
            
            if (type == GameData.I.MainData.CurrMenuType)
                return;

           D.Warning?.Log("刷新世界界面++++"+type+"=========="+ LPlayer.I.IsCrossServer);

            if (type == MainMenuType.WORLD)
            {
                if (crossServerGoHomeObj)
                {
                    if (LPlayer.I.IsCrossServer)
                    {
                        RemoveClickListener(crossServerGoHomeObj);
                        crossServerGoHomeObj.SetActive(true);
                        BindClickListener(crossServerGoHomeObj, OnClickSendGoHomeServer);
                    }
                    else
                    {
                        crossServerGoHomeObj.SetActive(false);
                    }
                }
            }
            GameInstance.EnableGraphicRaycaster(type == MainMenuType.CITY);
            //bool _ignore = false;
            //if (objs.Length > 2)
            //    _ignore = System.Convert.ToBoolean(objs[2]);

            //刷新显示信息
            await UpdateMainMenuInfo(type);
            SceneManager.I.IsNeedUImainClickCheckTime = true;
            //UpdateWorldGuideObjActive(null);
        }

        /// <summary>
        /// 点击了菜单按钮监听
        /// </summary>
        /// <param name="go"></param>
        /// <param name="data"></param>
        private async void OnClickMenuBtn(GameObject go, PointerEventData data)
        {
            //判断当前点击是否为造兵操作
            GameData.I.MainData.UpdateIsClickBuildSoldiersBtn(false);

            //获取资产界面显示时，关闭此界面
            PopupManager.I.ClosePopup<UIComplementPop>();

            //获取点击的菜单类型
            //var clickMenuType = GetClickMenuType(go);
            //await UpdateMainMenuInfo(clickMenuType);

            //刷新左侧技能按钮
            //if (!GameData.I.MainData.IsClickBuildSoldierBtn)
            //{
            //    UpdateShowLeftBtnsInfo();
            //    UpdateIsOpenSkillBtns(false);
            //}
            //UpdateWorldGuideObjActive(null);

            //PlayClickAutio(clickMenuType);

            //lastChooseType = clickMenuType;
            //GameInstance.EnableGraphicRaycaster(lastChooseType == MainMenuType.CITY);

            ////刷新联盟帮助按钮状态
            //UpdateAllianceHelpSpeedupRootState(true);  //Logic.LPlayer.I.IsPlayerInUnion()

            //关闭右侧按钮列表(暂时不做收起操作)
            //PackUpRightBtns();

        }


        /// <summary>
        /// 主界面菜单栏解锁
        /// </summary>
        /// <param name="objs"></param>
        //[PopupEvent(TEventType.UIMainn_Update_UnLockMenu)]
        //private void OnUnLockMenu(object[] objs)
        //{
        //    if (objs == null || objs.Length == 0)
        //        return;

        //    var type = (MainMenuType)objs[0];
        //    if (type == MainMenuType.NONE)
        //        return;

        //    //菜单栏解锁
        //    UnLockMenu(type);
        //}

       
         

        #endregion


    }
}
