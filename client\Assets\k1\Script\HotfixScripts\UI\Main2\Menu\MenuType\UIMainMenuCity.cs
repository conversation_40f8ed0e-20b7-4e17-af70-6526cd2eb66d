﻿ 
using Common;
using Cysharp.Threading.Tasks;
using DeepUI;
using Game.Config;
using Game.Data;
using Game.Sprite.Fight;
using K1;
using K3;
using Logic;
using Public;
using Render;
using TFW;
using TFW.UI;
using UI.Utils;
using UnityEngine;
using UnityEngine.EventSystems;

namespace UI
{
    public class UIMainMenuCity : MonoBehaviour
    {

        #region 属性字段信息

        public GameObject redPoint;
        //public GameObject lockObj, unlockObj;
        #endregion

        
        #region 初始化

        private void Start()
        {
            UIBase.AddRemoveListener(TFW.EventTriggerType.Click, gameObject, ClickMenuBtn);
        }


        /// <summary>
        /// 事件注册
        /// </summary>
        public  void OnEnable()
        { 
            EventMgr.RegisterEvent(TEventType.RefreshTech, (a) => { RefreshRedPoint(); }, this);
            EventMgr.RegisterEvent(TEventType.RefreshAssetAck, (a) => { RefreshRedPoint(); }, this);
            EventMgr.RegisterEvent(TEventType.K3GridDataRefresh, (a) => { RefreshRedPoint(); }, this);
            EventMgr.RegisterEvent(TEventType.OnSwitchMainUI, (a) => { RefreshRedPoint(); }, this);
             
            RefreshRedPoint();
        }

        private void OnDisable()
        {
            EventMgr.UnregisterEvent(this);
        }


        #endregion


        #region 数据刷新
        private int[] checkTech =
       {
            (int)MainCityItem.CityType.Castle,
            (int)MainCityItem.CityType.Trial,
            (int)MainCityItem.CityType.Police,
            (int)MainCityItem.CityType.Meeting,
            (int)MainCityItem.CityType.Machine,
            (int)MainCityItem.CityType.Gene,
        };

        public void RefreshRedPoint()
        {
            var pair = GetRedActive();
            // 新增气泡红点验证
            bool bubble = MainCityMgr.I.GetMainCityStatus();
            //添加建筑解锁检查
            // bool isUnLock = UnlockMgr.I.CheckUnlock(UnlockFuncEnum.Building_6);

            redPoint.SetActive((pair.Item1 || bubble));
        }

        public (bool, int) GetRedActive()
        {
            int redPointNum = 0;
            int type = 0;
            foreach (int i in checkTech)
            {
                Cfg.G.CD2CityBuilding building = Cfg.C.CD2CityBuilding.I(i * 1000);
                if (GameData.I.SkillData.MTechs.TryGetValue(i, out var mTechData) && building.OpenState)
                {
                    var orderCondition = MergeTaskMgr.I.TotalCompleteTaskNum >= mTechData.MConfig?.WantedsLimit;
                    bool unlockBuild = i > 1 ? K3PlayerMgr.I.PlayerData.UnlockMenus.Contains(i * 100) : true;
                    if (mTechData.ReadyToLevelUp && orderCondition && unlockBuild)
                    {
                        redPointNum++;
                        type = mTechData.MConfig.Type;
                        break;
                    }
                }
            }
            return (redPointNum > 0, type);
        }

        
          
        #endregion

        #region 声音播放

        /// <summary>
        /// 刷新声音播放
        /// </summary>
        private void UpdateAuido()
        {
            AudioManager.Instance.StopOtherAudioChannel(AudioConst.AUDIO_CHANNEL_STOP,
                            AudioChannelType.UI, AudioChannelType.CITY, AudioChannelType.BGM, AudioChannelType.GuidHeroAudio);

            //BgmManager.PlayBgm(AudioConst.Merge_BGM); 
        }


        #endregion


        #region 事件监听

         
     
        public void ClickMenuBtn(GameObject ga, PointerEventData po)
        {
            if (!MainFunctionOpenUtils.CityOpenState)
            { 
                return;
            }

            PopupManager.I.ShowLayer<UIMainCity>();
        }

         

        //public override void EnterMenuPanel()
        //{
        //    base.EnterMenuPanel();

        //    //mergeIcon.SetActive(true);
        //    //cityIcon.SetActive(false);

        //    UpdateAuido();

        //    WndMgr.Show<UIMainCity>();

        //    EnergyRefresh(null);

        //    RefreshRedPoint();

        //    //WndMgr.Show<K3.UIMergePhoto>();
        //}

        ///// <summary>
        ///// 点击菜单按钮
        ///// </summary>
        //public override async UniTask<bool> ClickMenuBtn(UIData data = null)
        //{
        //    await base.ClickMenuBtn(data);

        //    //刷新声音播放
        //    //UpdateAuido();

        //    //刷新城市信息显示
        //    var ok = UpdateCityInfo();

        //    //播放Sprite特效
        //    //SpriteDisplayManager.I.PlayEvoUpEffect();

        //    //if (Main != null)
        //    //{
        //    //    //(Main as UIMain1)?.UpdateChatVisable(true);
        //    //    (Main as UIMain2)?.UpdateChatVisable(true);
        //    //}

        //    //设置是否监听拖动事件
        //    //MapManager.I.CanDragOnMap(true);


        //    return ok;
        //}

        ///// <summary>
        ///// 刷新解锁状态
        ///// </summary>
        //public override void UpdateMenuOpenState()
        //{
        //    base.UpdateMenuOpenState();

        //    //if (GameData.I.LevelData.CurrGameLevel != MetaConfig.OpenCity
        //    //    || !FightManager.I.IsFighted)
        //    //{
        //    var isOpen = MainFunctionOpenUtils.PrisonOpenState;
        //    lockAnim?.gameObject.SetActive(!isOpen);
        //    UnlockObj?.SetActive(isOpen);

        //    //if (UnlockObj2)
        //    //{
        //    //    UnlockObj2.SetActive(isOpen);
        //    //}
        //    //}
        //}

         
        #endregion



    }
}
