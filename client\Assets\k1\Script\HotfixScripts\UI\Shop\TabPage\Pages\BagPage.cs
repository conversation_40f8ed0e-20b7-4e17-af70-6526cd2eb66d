﻿using Common;
using cspb;
using DeepUI;
using Game.Config;
using Game.Data;
using Logic;
using Public;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Cysharp.Threading.Tasks;
using TFW;
using TFW.Localization;
using TFW.UI;
using UI.Utils;
using UnityEngine;
using Game.Utils;
using K3;
// using RedEnvelope;

namespace UI
{
    /// <summary>
    /// 背包
    /// </summary>
    [AddComponentMenu(MenuPath + nameof(BagPage))]
    public class BagPage : ShopPageBase, IShopModuleWidget
    {
        [SerializeField]
        NumberPickerSlider _numberPickerSlider;

        private TFWLoopGridView gridView;

        private List<ItemData> loopData;

        private List<bool> loopSelectData;

        private int lastClickIndex = -1;

        private BtnDescWidget useBtn;
        private BtnDescWidget mergeBtn;
        private BtnDescWidget mergeAllBtn;
        private TFWText name;
        private TFWText desc;
        private TFWText bgNone;

        /// <summary>
        /// 滚动列表缓存
        /// </summary>
        private Dictionary<int, SelectItemWidget> widgetDic;


        private void SetInitShow()
        {
            name.enabled = false;
            desc.enabled = false;
            _numberPickerSlider?.gameObject.SetActive(false);
            useBtn?.SetRootVisible(false);
            mergeBtn?.SetRootVisible(false);
            mergeAllBtn?.SetRootVisible(false);
            bgNone.enabled = true;

            var count = loopData.Count;
            if (count > 0)
                bgNone.text = LocalizationMgr.Get("Shop_item_shop_desc_01");
            else
                bgNone.text = LocalizationMgr.Get("Shop_item_shop_desc_02");
        }

        public void RefreshGridView(object[] args)
        {
            var ui = PopupManager.I.FindPopup<UIShop>();
            if (ui == null)
                return;

            if (loopData == null || !gridView || lastClickIndex == -1)
                return;

            loopData = BagMgr.I.GetBagShowData();
            var count = loopData.Count;
            loopSelectData = new List<bool>(count);
            for (int i = 0; i < count; i++)
            {
                loopSelectData.Add(false);
            }

            if (loopData.Count <= lastClickIndex)
            {
                lastClickIndex = loopData.Count - 1;
            }

            var data = loopData[lastClickIndex];
            if (data == null)
                return;

            loopSelectData[lastClickIndex] = true;
            SetBottomData(data).Forget();
            if (!gridView.GridViewInited)
            {
                gridView.InitGridView(count, OnGetItemByRowColumn);
            }
            else
            {
                gridView.SetListItemCount(count, false);
                gridView.RefreshAllShownItem();
            }
        }


        private TFWLoopGridViewItem OnGetItemByRowColumn(TFWLoopGridView gridView, int itemIndex, int row, int column)
        {
            if (itemIndex < 0)
                return null;

            if (loopData == null
                || itemIndex >= loopData.Count)
                return null;

            TFWLoopGridViewItem temp = gridView.NewListViewItem("Item");

            if (temp.gameObject == null)
                return null;

            SelectItemWidget widget;
            if (!widgetDic.TryGetValue(itemIndex, out widget))
            {
                widget = new SelectItemWidget(temp.gameObject);
                widgetDic[itemIndex] = widget;
            }
            else
                widget.OnChangeRoot(temp.gameObject);

            if (loopData != null
                && loopData.Count > itemIndex
                && loopSelectData != null
                && loopSelectData.Count > itemIndex
                && loopData[itemIndex] != null)
            {
                widget.SetData(loopData[itemIndex], loopSelectData[itemIndex]);
                int index = itemIndex;
                widget.SetBtnClickCallBack(OnClickItemWithAudio, true, loopData[itemIndex], index);

            }


            return temp;
        }

        private void OnClickItemWithAudio(params object[] obj)
        {
            OnClickItem(obj);
            Render.GameAudio.PlayAudio(Game.Config.AudioConst.UIGeneric);
        }

        private void OnClickItem(params object[] obj)
        {
            if (obj == null || obj.Length == 0 ||
                widgetDic == null || loopSelectData == null)
                return;

            var data = obj[0] as ItemData;
            var index = (int)obj[1];

            if (lastClickIndex != -1)
            {
                widgetDic[lastClickIndex]?.SetSelect(false);
                loopSelectData[lastClickIndex] = false;
            }

            widgetDic[index]?.SetSelect(true);
            loopSelectData[index] = true;
            lastClickIndex = index;

            SetBottomData(data).Forget();
        }

        private async UniTaskVoid SetBottomData(ItemData data)
        {
            name.text = data.GetShowName();
            var quality = data.GetQuality();
            name.color = UITools.GetTipColorByQuality(quality);
            desc.text = data.GetShowDesc();
            bgNone.enabled = false;
            name.enabled = true;
            desc.enabled = true;

            mergeBtn?.SetBtnClickCallBack(UseSkillItem, true, data.Cfg.Id);
            mergeAllBtn?.SetBtnClickCallBack(UseAllSkillItem, true, data.Cfg.Id, data.Count);
            useBtn.SetBtnClickCallBack(OnClickUse, true, data);
            _numberPickerSlider?.gameObject.SetActive(false);
            useBtn?.SetRootVisible(false);
            mergeBtn?.SetRootVisible(false);
            mergeAllBtn?.SetRootVisible(false);
            var maxCount = (int)data.MaxShow();

            if (this._numberPickerSlider != null)
            {
                this._numberPickerSlider.SetRange(1, maxCount);
                this._numberPickerSlider.value = 1;
            }

            if (data.Cfg.Class == "item_hero_normal_recruit" || data.Cfg.Class == "item_hero_premium_recruit")
            {
                //按钮特殊处理
                useBtn?.SetRootVisible(true);
                useBtn?.SetData(LocalizationMgr.Get(string.IsNullOrEmpty(data.Cfg.UseTxt) ? "Recruit_item_btn" : data.Cfg.UseTxt));
            }
            else if (data.Cfg.Class == "activity_lucky_discount")//乐透彩券单独处理
            {
                //if (GameData.I.ActivityData.GetLuckyDiscountData() != null)
                //{
                //    //可使用1个
                //    useBtn?.SetRootVisible(true);
                //    useBtn?.SetData(LocalizationMgr.Get(string.IsNullOrEmpty(data.Cfg.UseTxt) ? "Bag_use_btn_02" : data.Cfg.UseTxt));
                //}
                //else
                //{
                //活动未开启
                useBtn?.SetRootVisible(true);
                useBtn?.SetData(LocalizationMgr.Get("Lucky_Err"));
                //}

            }
            else
            {
                if (data.Cfg.Use == 2)
                {
                    //可使用1个
                    useBtn?.SetRootVisible(true);
                    useBtn?.SetData(LocalizationMgr.Get(string.IsNullOrEmpty(data.Cfg.UseTxt) ? "Bag_use_btn_02" : data.Cfg.UseTxt));

                }
                else if (data.Cfg.Use == 1)
                {
                    //可使用多个
                    this._numberPickerSlider?.gameObject.SetActive(true);
                    useBtn?.SetData(LocalizationMgr.Get(string.IsNullOrEmpty(data.Cfg.UseTxt) ? "Bag_use_btn_01" : data.Cfg.UseTxt));
                    useBtn?.SetRootVisible(true);
                }
                else if (data.Cfg.Use == 0)
                {
                    if (data.Cfg.IsMerge != null && data.Cfg.IsMerge.Count > 0)
                    {
                        int count = int.Parse(data.Cfg.IsMerge[0]);
                        if (count != 0 && (maxCount / count) > 0)
                        {
                            if (this._numberPickerSlider != null)
                            {
                                this._numberPickerSlider.gameObject.SetActive(true);
                                this._numberPickerSlider.SetRange(1, Mathf.Min(9999, (int)maxCount / count));
                            }

                            mergeBtn?.SetRootVisible(true);

                            var mergeAllCount = await CalCount(data.Cfg.Id, data.Count);
                            var lvOk = LSkill.I.GetMainCityLevel() >= MetaConfig.OneClickMaximum;
                            mergeAllBtn?.SetRootVisible(mergeAllCount > 0 && lvOk);
                        }
                    }
                }
                else if (data.Cfg.IsMerge.Count > 0)
                {
                    int count = int.Parse(data.Cfg.IsMerge[0]);
                    if (count != 0 && (maxCount / count) > 0)
                    {
                        if (this._numberPickerSlider != null)
                        {
                            this._numberPickerSlider.gameObject.SetActive(true);
                            this._numberPickerSlider.SetRange(1, Mathf.Min(9999, (int)maxCount / count));
                        }

                        mergeBtn?.SetRootVisible(true);

                        var mergeAllCount = await CalCount(data.Cfg.Id, data.Count);
                        var lvOk = LSkill.I.GetMainCityLevel() >= MetaConfig.OneClickMaximum;
                        mergeAllBtn?.SetRootVisible(mergeAllCount > 0 && lvOk);
                    }
                }
            }
        }

        private async void OnClickUse(System.Object[] param)
        {
            if (param == null || param.Length == 0 || this._numberPickerSlider == null)
                return;

            var data = param[0] as ItemData;
            if (data == null)
                return;

            var count = this._numberPickerSlider.value;
            if (data.Cfg.Class == "item_CommonTreasure")
            {
                //宝箱
                BagMgr.I.GetServerIdByItemId(data.Cfg.Id, out var serverId, out var cfgId);
                if (serverId == -1)
                    Debug.Log("Log : Treasure Id Or ServerId Is Error！！！");

                TreasureMgr.I.ShopUsed(cfgId, serverId, count, (UIDrawCardEnum)data.Cfg.ItemChest);
            }
            else if (data.Cfg.Class == "item_chest")
            {
                PlayerAssetsMgr.I.UseItem(new ItemData() { Cfg = data.Cfg, Count = count });
            }
            else if (data.Cfg.Class == "item_buff")
            {
                BagMgr.I.ProtectedItemUse(data.Cfg.Id);
            }
            else if (data.Cfg.Class == "item_skin")
            {
                if (data.Cfg.CategoryParam == null || data.Cfg.CategoryParam.Effect == null
                    || data.Cfg.CategoryParam.Effect.Count == 0 || data.Cfg.CategoryParam.Effect[0] == null)
                    return;

                var cfg = await Cfg.C.CD2Sence.GetConfigAsync(data.Cfg.CategoryParam.Effect[0].Id);
                if (cfg == null)
                    return;

                if (cfg.Type == 5 && LPlayer.I.GetMainCityID() == 0)
                {
                    UITools.PopTips(LocalizationMgr.Get("Item_skin_cant_use_tips"));
                    return;
                }

                PlayerAssetsMgr.I.UseItem(new ItemData() { Cfg = data.Cfg, Count = count });
                ShopSkinMgr.I.ChangeSkinReq(new ShopSkinData(data.Cfg.CategoryParam.Effect[0].Id));
            }
            else if (data.Cfg.Class == "item_hero_normal_recruit" || data.Cfg.Class == "item_hero_premium_recruit")
            {
                //if (!(await Cfg.C.CD2CityBuilding.GetConfigAsync((int)MainCityItem.CityType.Tavern)).GetIsOpen())
                //{
                //    return;
                //}

                HeroRecruitMgr.I.OpenHeroRecruit();
                //PopupManager.I.ShowLayer<UIHero_Upgrade_RecruitHero>(new UIHero_Upgrade_RecruitHeroData() { isGoToHero = false });
            }
            else if (data.Cfg.Class == "item_dragon")
            {
                if (!UnlockMgr.I.CheckUnlock(UnlockFuncEnum.Building_8))
                {
                    UITools.PopTips(UnlockMgr.I.GetUnlockDesc(UnlockFuncEnum.Building_8));
                    return;
                }
                PopupManager.I.ShowLayer<UIDragon>();
            }
            else if (data.Cfg.Class == "item_tech")
            {
                //if (!(await Cfg.C.CD2CityBuilding.GetConfigAsync((int)MainCityItem.CityType.Barracks)).GetIsOpen())
                //{
                //    return;
                //}
                //PopupManager.I.ShowLayer<UIEvo>();
                //if (LTowerDefenseType.I.isNTD)
                //    PopupManager.I.ShowLayer<UIEvoNew>();
                //else
                //    PopupManager.I.ShowLayer<UIEvo>();
            }
            else if (data.Cfg.Class == "item_change_name" || data.Cfg.Class == "item_change_sex")
            {
                PopupManager.I.ShowLayer<UIPlayerMain>();
            }
            else if (data.Cfg.Class == "item_fixed_point_teleport" || data.Cfg.Class == "item_alliance_teleport")
            {
                //高级迁城，联盟迁城
                LAllianceMgr.I.ReqUnionRandomCoord();
                PopupManager.I.ClosePopup<UIShop>();
            }
            else if (data.Cfg.Class == "item_talent")
            {
                //var soldierType = data.Cfg.CategoryParam.Effect[0].Id;
                //int lv = 1;
                //if (soldierType == 1)
                //    lv = LEvolution.I.ArcherData.EvoLevel;
                //else if (soldierType == 2)
                //    lv = LEvolution.I.IceData.EvoLevel;
                //else if (soldierType == 3)
                //    lv = LEvolution.I.FireData.EvoLevel;
                //else if (soldierType == 4)
                //    lv = LEvolution.I.ToxData.EvoLevel;

                //var evoData = TDUtils.NewEvolutionData(new EvoInfo() { evoLv = lv, exp = 0, soldierType = data.Cfg.CategoryParam.Effect[0].Id });
                //TalentMgr.I.OpenTalentPanel(evoData, true);
            }
            else if (data.Cfg.Class == "item_award_selectable")
            {
                BagMgr.I.OpenOptionalChestPop(new ItemData() { Cfg = data.Cfg, Count = count });
            }
            else if (data.Cfg.Class == "item_avatar_frame")
            {
                PlayerAssetsMgr.I.UseItem(new ItemData() { Cfg = data.Cfg, Count = count });
                NTimer.CountDown(1f, () =>
                {
                    //头像框跳转
                    HeadFrameMgr.I.OpenHeadInfo(HeadInfoEnum.Frame, data.Cfg.CategoryParam.Effect[0].Id);
                });
            }
            else if (data.Cfg.Class == "activity_lucky_discount")
            {
                //跳转幸运大减价活动
                //if (GameData.I.ActivityData.GetLuckyDiscountData() == null)
                UITools.PopTips(LocalizationMgr.Get("Lucky_Err"));
                //else
                //{
                //    UIActivityMain.ShowActivityByCfgId(GameData.I.ActivityData.LuckyDiscountActvCfgId);
                //}
            }
            else if (data.Cfg.Class == "item_dragonequip")
            {
                PlayerAssetsMgr.I.UseItem(new ItemData() { Cfg = data.Cfg, Count = count });
                if (count > 1)
                {
                    UITools.PopTips(LocalizationMgr.Get("Dragon_Equip_item_tips_01"));
                }
            }
            else if (data.Cfg.Class == "item_magic")
            {
                //WitchsBrewUI.Layer.OpenSafely();
            }
            else if (data.Cfg.Class == "item_summon")
            {
                ///哈罗德召唤道具
                if (LCrossServer.I.CurrSceneType == cspb.SceneType.SceneTypeCrossCommander)
                {
                    FloatTips.I.FloatMsg(LocalizationMgr.Get("MK_Forbidden_Action_Tips"));
                    return;
                }
                PlayerAssetsMgr.I.UseItem(new ItemData() { Cfg = data.Cfg, Count = count });
            }
            else if (data.Cfg.Class == "item_limit_buff")
            {
                if (LPlayerProp.I.PlayerNewBuffs != null)
                {
                    foreach (var item in LPlayerProp.I.PlayerNewBuffs)
                    {

                        var buffCfg1 = await Cfg.C.CNewBuff.GetConfigAsync(item.CfgId);
                        var buffCfg2 = await Cfg.C.CNewBuff.GetConfigAsync(data.GetEffectId());
                        if (buffCfg1.Group == buffCfg2.Group)
                        {
                            if (buffCfg2.Value < buffCfg1.Value)
                            {
                                var title = LocalizationMgr.Get("Buying_Confirm_Tips_Title");
                                var content = LocalizationMgr.Get("Buff_Tips4");
                                UITools.OpenConfirmPop(() =>
                                {
                                    PlayerAssetsMgr.I.UseItem(new ItemData() { Cfg = data.Cfg, Count = count });
                                }, null, title, content, false);
                                return;
                            }
                        }

                    }

                    foreach (var item in LPlayerProp.I.PlayerNewBuffs)
                    {
                        if (item.CfgId == data.GetEffectId())
                        {
                            var remainingTime = item.StartTs + item.Duration - GameTime.Time;//剩余时间

                            if (remainingTime > 0)
                            {
                                var title = LocalizationMgr.Get("Buying_Confirm_Tips_Title");

                                var timeDesc = UIHelper.GetFormatTime(remainingTime, true, true);
                                var content = LocalizationMgr.Format("Buff_Tips3", timeDesc);


                                UITools.OpenConfirmPop(() =>
                                {
                                    PlayerAssetsMgr.I.UseItem(new ItemData() { Cfg = data.Cfg, Count = count });
                                }, null, title, content, false);

                                return;
                            }
                        }
                    }
                }

                PlayerAssetsMgr.I.UseItem(new ItemData() { Cfg = data.Cfg, Count = count });
            }
            else if (data.Cfg.Class == "item_redpacket")
            {
                var redEnvelopeCfgId = data.Cfg.CategoryParam.Effect[0].Id;
                var redEnvelopeCfg = await Cfg.C.CRedEnvelope.GetConfigAsync(redEnvelopeCfgId);
                if (redEnvelopeCfg == null)
                    return;

                // GiveRedEnvelopeUI.ShowAsync(redEnvelopeCfg.GetAvailableChannels())
                //     .ContinueWith(
                //         (channel) =>
                //         {
                //             if (channel.HasValue)
                //             {
                //                 // RedEnvelopeMgr.I.RequestSendRed(channel.Value, data.Cfg);
                //             }
                //         });
            }
            else if (data.Cfg.Class == "item_badge")
            {
                //PopupManager.I.ShowPanel<BadgePop>(new BadgePopData()
                //{
                //    id = data.Cfg.Id
                //});
            }
            else if (data.Cfg.Class == "item_hero_intimacy")
            {
                //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.HERO);
                PopupManager.I.ClosePopup<UIShop>();
            }
            else
            {
                PlayerAssetsMgr.I.UseItem(new ItemData() { Cfg = data.Cfg, Count = count });
            }
        }

        private void UseSkillItem(System.Object[] param)
        {
            if (this._numberPickerSlider == null || param == null || param.Length == 0)
                return;

            int id = (int)param[0];
            var count = this._numberPickerSlider.value;
            ItemMergeReq req = new ItemMergeReq();
            req.itemID = id;
            req.times = count;
            MessageMgr.Send(req);
        }

        private async UniTask<int> CalCount(int itemId, long firstCount)
        {
            int id = itemId;
            var cfg = await Cfg.C.CItem.GetConfigAsync(id);
            if (cfg == null)
                return 0;

            int count = (int)firstCount;
            while (true)
            {
                if (cfg.IsMerge == null || cfg.IsMerge.Count < 2)
                    break;

                var need = int.Parse(cfg.IsMerge[0]);
                id = int.Parse(cfg.IsMerge[1]);

                cfg = await Cfg.C.CItem.GetConfigAsync(id);
                if (cfg == null)
                    break;

                count = count / need;
            }

            if (cfg.Quality != 4)
                return 0;

            return count;
        }

        private Dictionary<int, ItemData> mergeGetItem;

        private async void UseAllSkillItem(System.Object[] param)
        {
            if (this._numberPickerSlider == null || param == null || param.Length == 0)
                return;

            int id = (int)param[0];
            var all = (long)param[1];
            var cfg = await Cfg.C.CItem.GetConfigAsync(id);
            if (cfg == null)
                return;
            var count = (int)all / int.Parse(cfg.IsMerge[0]);  //下一级进化可获得的数量
            var leftCount = all - count * int.Parse(cfg.IsMerge[0]);
            var item = new ItemData(id, all - leftCount);
            var leftItem = new List<ItemData>();
            leftItem.Add(item);
            var rightItem = BagMgr.I.GetMergeResultItems(item);
            BagMgr.I.OpenMergePanel(BagMergeTypeEnum.OneToMany, leftItem, rightItem, () => { ClickCallBack(id, all).Forget(); });
        }

        private async UniTaskVoid ClickCallBack(int id, long all)
        {
            var cfg = await Cfg.C.CItem.GetConfigAsync(id);
            if (cfg == null)
                return;

            mergeGetItem = new Dictionary<int, ItemData>();
            var count = (int)all / int.Parse(cfg.IsMerge[0]);  //下一级进化可获得的数量

            BagMgr.I.ItemMergeReq(id, count);

            var leftCount = all - count * int.Parse(cfg.IsMerge[0]);
            //mergeGetItem.Add(id, new ItemData(id, leftCount));  屏蔽同种类型数量

            while (true)
            {
                var need = int.Parse(cfg.IsMerge[0]);
                id = int.Parse(cfg.IsMerge[1]);

                cfg = await Cfg.C.CItem.GetConfigAsync(id);
                if (cfg == null)
                    break;

                if (cfg.IsMerge == null || cfg.IsMerge.Count == 0)
                {
                    mergeGetItem.Add(id, new ItemData(id, count));
                    break;
                }


                leftCount = count % need;
                count = count / need;
                BagMgr.I.ItemMergeReq(id, count);

                mergeGetItem.Add(id, new ItemData(id, leftCount));
            }

            List<TypIDVal> reward = new List<TypIDVal>();
            foreach (var item in mergeGetItem)
            {
                if (item.Value.Count > 0)
                    reward.Add(new TypIDVal() { ID = item.Key, typ = "item", val = item.Value.Count });
            }

            UITools.ShowReward(reward);
        }

        IEnumerator SelectFirstItemNextFrame()
        {
            yield return null;
            //默认选中第一个item
            if (this.widgetDic != null)
            {
                var pair = this.widgetDic.FirstOrDefault();
                if (pair.Value != null)
                {
                    pair.Value.SetSelect(true);
                    OnClickItem(loopData[pair.Key], pair.Key);
                }

                if (this.gridView != null && this.gridView.GridViewInited)
                {
                    this.gridView.MovePanelToItemByIndex(0);
                }
            }
        }

        void Refresh()
        {
            if (loopData == null)
                return;

            if (this.isActiveAndEnabled && loopData != null)
            {
                var count = loopData.Count;
                loopSelectData = new List<bool>(count);
                for (int i = 0; i < count; i++)
                {
                    loopSelectData.Add(false);
                }

                SetInitShow();

                if (!gridView.GridViewInited)
                {
                    gridView.InitGridView(count, OnGetItemByRowColumn);
                }
                else
                {

                    gridView.SetListItemCount(count);
                    gridView.RefreshAllShownItem();
                }
            }
        }

        void IShopModuleWidget.SetData(PageData pageData = null)
        {
            lastClickIndex = -1;
            loopData = BagMgr.I.GetBagShowData();

            this.Refresh();
        }

        private void Awake()
        {
            var rootObj = this.gameObject;
            gridView = UIHelper.GetComponent<TFWLoopGridView>(rootObj, "ScrollView");
            name = UIHelper.GetComponent<TFWText>(rootObj, "Buttom/Title");
            desc = UIHelper.GetComponent<TFWText>(rootObj, "Buttom/Info");
            bgNone = UIHelper.GetComponent<TFWText>(rootObj, "Buttom/None");

            var useObj = UIHelper.GetChild(rootObj, "Buttom/UsedBtn");
            if (useObj)
                useBtn = new BtnDescWidget(useObj);

            var mergeObj = UIHelper.GetChild(rootObj, "Buttom/Merge/MergeBtn");
            if (mergeObj)
                mergeBtn = new BtnDescWidget(mergeObj);

            var mergeAllObj = UIHelper.GetChild(rootObj, "Buttom/Merge/MergeBtn1");
            if (mergeAllObj)
                mergeAllBtn = new BtnDescWidget(mergeAllObj);


            widgetDic = new Dictionary<int, SelectItemWidget>();
            lastClickIndex = -1;

            EventMgr.RegisterEvent(TEventType.RefreshItemAck, RefreshGridView, this);
            EventMgr.RegisterEvent(TEventType.UseItemAck, RefreshGridView, this);
            EventMgr.RegisterEvent(TEventType.ActiveShieldSucceed, RefreshGridView, this);
        }

        void OnEnable()
        {
            if (loopData != null)
            {
                if (!gridView.GridViewInited)
                {
                    gridView.InitGridView(loopData.Count, OnGetItemByRowColumn);
                }
                else
                {

                    gridView.SetListItemCount(loopData.Count);
                    gridView.RefreshAllShownItem();
                }
            }

            this.Refresh();

            StopAllCoroutines();
            StartCoroutine(this.SelectFirstItemNextFrame());
        }

        private void OnDestroy()
        {
            EventMgr.UnregisterEvent(TEventType.RefreshItemAck, this);
            EventMgr.UnregisterEvent(TEventType.UseItemAck, this);
            EventMgr.UnregisterEvent(TEventType.ActiveShieldSucceed, this);
        }
    }
}
