﻿ 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Common;
using DeepUI;
using K3;
using TFW.Localization;
using TFW.UI;
using Logic;
using Public;
using UI.Utils;
using UnityEngine;
using static Game.Config.PowerConst;

namespace UI
{
    public class UIPlayerPowerDetailItem : UIGridItem
    {

        public TFWText powerName;
        public TFWText powerValue;
        public TFWImage powerIcon;

        public GameObject btnGO;

        public void InitPowerData(PowerType powerType, long power)
        {
            //UITools.SetImage(powerIcon, (int)powerType, "GetItem", true);
            //图片设置
            switch (powerType)
            {
                case PowerType.soldier:
                    UITools.SetImageBySpriteName(powerIcon, "UI_Icon_Jail", true);
                    break;
                case PowerType.hero:
                    UITools.SetImageBySpriteName(powerIcon, "UI_Icon_Hero", true);
                    break;
                //case PowerType.skin:
                //    UITools.SetCommonItemIcon(powerIcon, "zlxq_img_4", true);
                //    break;
                //case PowerType.vip:
                //    UITools.SetCommonItemIcon(powerIcon, "zlxq_img_8", true);
                //    break;
                case PowerType.dragon:
                    UITools.SetImageBySpriteName(powerIcon, "UI_Icon_Chariot", true);
                    break;
                case PowerType.tech:
                    UITools.SetImageBySpriteName(powerIcon, "UI_Icon_Technology", true);
                    break;
                case PowerType.building:
                    UITools.SetImageBySpriteName(powerIcon, "UI_Icon_Building", true);
                    break;
                //case PowerType.alliance:
                //    UITools.SetCommonItemIcon(powerIcon, "zlxq_img_9");
                //    break;
                default:
                    break;
            }

            RefreshNameText(powerType);

            if(powerValue)
            powerValue.text = UIStringUtils.FormatIntegerByLanguage(power);

            UIBase.AddRemoveListener(TFW.EventTriggerType.Click, btnGO, (x, y) =>
            {
                switch (powerType)
                {
                    case PowerType.soldier:
                        if (UI.Utils.MainFunctionOpenUtils.WorldOpenState)
                        {
                            PopupManager.I.ClearAllPopup();
                            PopupManager.I.ShowLayer<UIStargazingPlatform>(); 
                        }
                        else
                        {
                            FloatTips.I.FloatMsg(LocalizationMgr.Get("Tips_UnlockAstralSpire"));
                        }

                        //if (UnlockMgr.I.CheckUnlock(UnlockFuncEnum.Building_Un))
                        //{
                        //    GoCityBuild(MainCityItem.CityType.Barracks);
                        //}
                        //else
                        //{
                        //    UnlockMgr.I.GetUnlockDesc(UnlockFuncEnum.Building_Un).ToShowFloatTips();
                        //}
                        break;
                    case PowerType.building:
                        GoCityBuild(MainCityItem.CityType.Castle);
                        break;
                    case PowerType.tech:
                        if (UnlockMgr.I.CheckUnlock(UnlockFuncEnum.Building_6))
                        {
                            GoCityBuild(MainCityItem.CityType.Academy);
                        }
                        else
                        {
                            UnlockMgr.I.GetUnlockDesc(UnlockFuncEnum.Building_6).ToShowFloatTips();
                        }
                        break;
                    case PowerType.hero:
                        if (!MainFunctionOpenUtils.CheckHeroOpenState(true))
                        {
                            return;
                        }
                        PopupManager.I.ClearAllPopup();
                        EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.CITY);
                        var guidData = new UIGuidData();
                        guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMain2", UIItem = "content/MenuRoot/Hero", slide = true, delayFinger = .5f });
                        UIGuid.StartGuid(guidData);
                        break;
                    case PowerType.dragon:
                        if (UnlockMgr.I.CheckUnlock(UnlockFuncEnum.Building_8))
                        {
                            GoCityBuild(MainCityItem.CityType.Wall);
                        }
                        else
                        {
                            UnlockMgr.I.GetUnlockDesc(UnlockFuncEnum.Building_8).ToShowFloatTips();
                        }
                        break;
                    default:
                        break;
                }
            });
        }

        private void GoCityBuild(MainCityItem.CityType cityType)
        { 
            //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.PRISON);
            var ui = PopupManager.I.FindPopup<UIMainCity>();  
            if (ui != null)
            {
                PopupManager.I.ClearAllPopup();

                int cityCfgID = LPlayer.I.GetCityBuildingIDByType((int)cityType);
                ui.dragImage.MoveTOCfg(cityCfgID, () =>
                {
                    var guidData = new UIGuidData();

                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMainCity", UIItem = $"FullScreenBgCanvas/Root/BG1/Content/Building/build_{(int)cityType}" });
                    UIGuid.StartGuid(guidData);
                });
            }
        }

        private void RefreshNameText(PowerType powerType)
        {
            switch (powerType)
            {
                case PowerType.hero:
                    powerName.text = LocalizationMgr.Get("Hero_Power");
                    break;
                case PowerType.soldier:
                    powerName.text = LocalizationMgr.Get("Soldier_Power");
                    break;
                case PowerType.dragon:
                    powerName.text = LocalizationMgr.Get("Dragon_Power");
                    break;
                case PowerType.building:
                    powerName.text = LocalizationMgr.Get("Power_Details_14");
                    break;
                //case PowerType.alliance:
                //    powerName.text = LocalizationMgr.Get("Alliance_Power");
                //    break;
                //case PowerType.skin:
                //    powerName.text = LocalizationMgr.Get("Skin_Power");
                //    break;
                //case PowerType.vip:
                //    powerName.text = LocalizationMgr.Get("Power_Details_10");
                //    break;
                //case PowerType.bond:
                //    powerName.text = LocalizationMgr.Get("Power_Details_11");
                //    break;
                case PowerType.tech:
                    powerName.text = LocalizationMgr.Get($"Power_Details_12");
                    break;
                default:
                    break;
            }

        }
    }
}
