﻿ 
using Public;
using System;
using Cfg;
using Render;
using UnityEngine;
using UnityEngine.EventSystems;
using EventTriggerType = TFW.EventTriggerType;
using TFW.UI;
using Common;
using Cysharp.Threading.Tasks;
using Game.Data;
using TFW;
using DeepUI;
using TFW.Localization;
using UnityEngine.UI;

namespace UI
{
    /// <summary>
    /// box界面类型，实际是按钮类型
    /// </summary>
    public enum EMsgBoxType
    {
        zero = 0,
        one = 1,
        two = 2,

        // 不带关闭按钮的
        zero_nc = 10,
        one_nc = 11,
        two_nc = 12,
    }

    /// <summary>
    /// 按钮颜色枚举
    /// </summary>
    public enum ButtonColorGroup
    {
        // For one button
        Red,
        Blue,
        Green,
        Golden,

        // For two buttons
        RedGreen,
        RedGold,
        RedBlue,
        GreenGold,
        GreenBlue,
        BlueGlod,
        BlueBlue,
        /// <summary>
        /// 灰色绿色
        /// </summary>
        GrayGreen,
        Default
    }

    /// <summary>
    /// box按钮数据参数
    /// </summary>
    public class MsgBoxBtnParam
    {
        /// <summary>
        /// 按钮上文字
        /// </summary>
        public string str;
        
        /// <summary>
        /// 按钮回调
        /// </summary>
        public Action<object> func; 

        /// <summary>
        /// 参数
        /// </summary>
        public object param;

        /// <summary>
        /// 确认点击是否自动关闭，默认为true
        /// </summary>
        public bool isClickAutoClose = true;
    }


    /// <summary>
    /// close按钮数据参数
    /// </summary>
    public class MsgCloseBtnParam
    {
        /// <summary>
        /// 按钮回调
        /// </summary>
        public Action<object> func;

        /// <summary>
        /// 参数
        /// </summary>
        public object param;
    }


    /// <summary>
    /// box参数
    /// </summary>
    public class UIMsgBoxInitData : PopupData
    {

        /// <summary>
        /// 是否处于界面最上层
        /// </summary>
        public bool IsPanelTop = false;

        /// <summary>
        /// box类型
        /// </summary>
        public EMsgBoxType Typ;

        /// <summary>
        /// 按钮颜色
        /// </summary>
        public ButtonColorGroup colorGroup = ButtonColorGroup.Default;

        /// <summary>
        /// 标题
        /// </summary>
        public string Title;

        /// <summary>
        /// 内容
        /// </summary>
        public string Content;

        /// <summary>
        /// 内容文本对齐方式
        /// </summary>
        public TextAnchor ContentTextAnchor = TextAnchor.MiddleCenter;

        /// <summary>
        /// 是否开启二次确认按钮
        /// </summary>
        public bool IsOpenSecondBtn;

        public OpenSecondEnum openSecondEnum;

        /// <summary>
        /// 是否使用警告版本弹窗背景
        /// </summary>
        public bool IsUseWarningBG;

        /// <summary>
        /// 是否显示左上角切换账号按钮
        /// </summary>
        public bool IsShowSwitchRoleBtn;

        /// <summary>
        /// 是否显示客户端日志按钮
        /// </summary>
        public bool ShowClientLogBtn;

        public enum OpenSecondEnum
        {
            Gold,
            PrivateChatDel,
            ActvExchange,
            /// <summary>
            /// 次元宝藏搜索
            /// </summary>
            WarpMainSearch,
            /// <summary>
            /// 次元宝藏攻击
            /// </summary>
            WarpMainAttack,
            /// <summary>
            /// 升级消耗卡片
            /// </summary>
            HeroRetainCard,
            ///许愿球
            Crystal,
            //新旧塔防切换
            NTD,
            //招财猫
            ManekoCat,
            /// <summary>
            /// 抽卡
            /// </summary>
            RecruitCard,
            /// <summary>
            /// 神器升级
            /// </summary>
            ArtifactLevelUp,

            /// <summary>
            /// 道具合成提示
            /// </summary>
            MergeGoodMergeHelpTip,

             
            None,
        }

        /// <summary>
        /// 按钮1参数
        /// </summary>
        public MsgBoxBtnParam Btn1data;

        /// <summary>
        /// 按钮2参数
        /// </summary>
        public MsgBoxBtnParam Btn2data;

        /// <summary>
        /// 关闭按钮参数
        /// </summary>
        public MsgCloseBtnParam Closedata;

        /// <summary>
        /// 左上角切换账号按钮参数
        /// </summary>
        public MsgBoxBtnParam BtnSwithcRoledata;
    }

    /// <summary>
    /// 通用消息弹窗
    /// </summary>
    [Popup("Other/Ui_MessageBox")]
    public partial class UIMsgBox : HistoricPopup
    {
        ///// <summary>
        ///// 资源路径
        ///// </summary>
        //protected override string assetPath => "Other/Ui_MessageBox";

        ///// <summary>
        ///// SortingOrder
        ///// </summary>
        //public override ESortingOrder SortingOrder => ESortingOrder.MsgBox;

        ///// <summary>
        ///// 遮罩类型
        ///// </summary>
        //public override EPopPanelType PopPanelType => EPopPanelType.PopUp;

        protected internal override bool DisposeOnClose => true;


        #region Field

        /// <summary>
        /// 按钮1
        /// </summary>
        private GameObject m_Btn1;

        /// <summary>
        /// 按钮2
        /// </summary>
        private GameObject m_Btn2;

        /// <summary>
        /// 关闭按钮
        /// </summary>
        private GameObject m_BtnClose;

        /// <summary>
        /// 左上角切换角色按钮
        /// </summary>
        private GameObject m_BtnSwitchRole;

        /// <summary>
        /// 按钮1数据
        /// </summary>
        private MsgBoxBtnParam m_Btn1data;

        /// <summary>
        /// 按钮2数据
        /// </summary>
        private MsgBoxBtnParam m_Btn2data;

        /// <summary>
        /// 关闭按钮数据
        /// </summary>
        private MsgCloseBtnParam m_Closedata;

        /// <summary>
        /// 二次确认按钮状态
        /// </summary>
        private bool _toggleState;

        /// <summary>
        /// 左上角切换账号按钮数据
        /// </summary>
        private MsgBoxBtnParam m_SwitchRoledata;
        #endregion

        /// <summary>
        /// 初始化
        /// </summary>
        protected override void OnInit()
        {

        }

        protected override void OnShown()
        {
            base.OnShown();
              
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;


            GameAudio.PlayAudio(AudioChannelType.UI, "Assets/k1/Res/Audio/UI/UI_prompt.ogg", 0.5f);

            var root = UIHelper.GetChild(GameObject, "CentreOther");
            RegisterEvent(TEventType.SecondConfirm, OnChooseToggle);


            SetToggle(root);
            SetTitleAndContent(root);
            SetButtons(root);
            SetIsUseWaringBG(root);
            SetIsShowSwitchRoleBtn(root);
            SetClientLogBtn(root);
        }

        protected override void OnHidden()
        {
            base.OnHidden();
            CallHideFunc();
            UnregisterEvent(TEventType.SecondConfirm);
        }

        /// <summary>
        /// 点击按钮1
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="args"></param>
        private void OnClickBtn1(GameObject obj, PointerEventData args)
        {
            if (m_Btn1data.isClickAutoClose)
                CallHideFunc();

            m_Btn1data.func?.Invoke(m_Btn1data.param);
        }

        /// <summary>
        /// 点击按钮2
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="args"></param>
        private void OnClickBtn2(GameObject obj, PointerEventData args)
        {
            //二次确认时的处理
            var data = InitData as UIMsgBoxInitData;
            if (data.IsOpenSecondBtn == true)
            {
                GameData.I.MaskData.SecondConfirm[(int)data.openSecondEnum] = _toggleState;
            }

            if (m_Btn2data.isClickAutoClose)
                CallHideFunc();

            m_Btn2data.func?.Invoke(m_Btn2data.param);
        }

        /// <summary>
        /// 点击关闭
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="args"></param>
        private void OnClickClose(GameObject obj, PointerEventData args)
        {
            CallHideFunc();

            Hide();
        }

        /// <summary>
        /// 点击按钮切换角色
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="args"></param>
        private void OnClickSwitchRoleBtn(GameObject obj, PointerEventData args)
        {
            if (m_SwitchRoledata.isClickAutoClose)
                CallHideFunc();

            m_SwitchRoledata.func?.Invoke(m_SwitchRoledata.param);
        }

        /// <summary>
        /// 点击客户端日志收集按钮
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="args"></param>
        private void OnClickClientLog(GameObject obj, PointerEventData args)
        {
            PopupManager.I.ShowDialog<UINetCheckPop>(false);
        }

        /// <summary>
        /// 关闭界面
        /// </summary>
        private void CallHideFunc()
        {
            if (m_Btn1 != null) RemoveListener(EventTriggerType.Click, m_Btn1);
            if (m_Btn2 != null) RemoveListener(EventTriggerType.Click, m_Btn2);
            if (m_Closedata != null)
                m_Closedata.func?.Invoke(m_Closedata.param);

            Close();
        }

        /// <summary>
        /// 设置标题和内容
        /// </summary>
        /// <param name="root"></param>
        public void SetTitleAndContent(GameObject root)
        {
            var data = InitData as UIMsgBoxInitData;
            if(data != null)
            {
                var txtTitle = UIHelper.GetComponent<TFWText>(root, "Board/Content/BG/TitleBar/Title/TitleText");
                if(data.IsUseWarningBG == true)
                    txtTitle = UIHelper.GetComponent<TFWText>(root, "Board/Content/WarningBG/TitleBar/Title/TitleText");
                if (txtTitle != null)
                    txtTitle.text = data.Title;

                var txtContent = UIHelper.GetComponent<TFWText>(root, "Board/Content/TxtInfo");
                if (txtContent != null)
                {
                    
                    txtContent.isFlipDelayText = true;
                    txtContent.alignment = data.ContentTextAnchor;
                    if (LocalizationMgr.IsRightToLeftLanguage)
                    {
                        Vector2 ver = new Vector2() {
                            x = UIHelper.GetComponent<LayoutElement>(root, "Board/Content/TxtInfo").preferredWidth,
                            y = UIHelper.GetComponent<LayoutElement>(root, "Board/Content/TxtInfo").preferredHeight,
                        };
                        txtContent.text = data.Content;
                    }
                    else
                    {
                        txtContent.text = data.Content;
                    }
                }
            }

            
        }
     
        public void SetIsUseWaringBG(GameObject root)
        {
            var data = InitData as UIMsgBoxInitData;
            if (data != null)
            {
                var waringBG = UIHelper.GetChild(root, "Board/Content/WarningBG");
                if (waringBG != null)
                    waringBG.SetActive(data.IsUseWarningBG);
            }
        }

        /// <summary>
        /// 设置左上角切换账号按钮
        /// </summary>
        /// <param name="root"></param>
        public void SetIsShowSwitchRoleBtn(GameObject root)
        {
            var data = InitData as UIMsgBoxInitData;
            if (data != null)
            {
                var switchRoleObj = UIHelper.GetChild(root, "Board/Content/SwitchRolesBtn");
                if (switchRoleObj != null)
                    switchRoleObj.SetActive(data.IsShowSwitchRoleBtn);
            }
        }

        /// <summary>
        /// 设置左上角收集日志按钮
        /// </summary>
        /// <param name="root"></param>
        public void SetClientLogBtn(GameObject root)
        {
            var data = InitData as UIMsgBoxInitData;
            if (data != null)
            {
                var switchRoleObj = UIHelper.GetChild(root, "Board/Content/ClientLogBtn");
                if (switchRoleObj != null)
                    switchRoleObj.SetActive(data.ShowClientLogBtn);
            }
        }
        

        /// <summary>
        /// 设置二次确认Toggle
        /// </summary>
        /// <param name="root"></param>
        public void SetToggle(GameObject root)
        {
            if (!root)
                return;

            var data = InitData as UIMsgBoxInitData;

            var toggleObj = UIHelper.GetChild(root, "Board/Content/Toggle");
            var toggle = UIHelper.GetComponent<TFWToggle>(root, "Board/Content/Toggle/CheckBox");
            if (toggle == null)
                return;

            toggle.isOn = false;

            if (data != null && data.IsOpenSecondBtn)
                toggleObj?.SetActive(data.IsOpenSecondBtn);
            else
                toggleObj?.SetActive(false);

            toggle.onValueChanged.RemoveAllListeners();
            toggle.onValueChanged.AddListener((bool value) => OnToggleClick(toggle, value));
        }

        /// <summary>
        /// 二次确认Toggle点击事件
        /// </summary>
        /// <param name="toggle"></param>
        /// <param name="value"></param>
        public void OnToggleClick(TFWToggle toggle, bool value)
        {
            EventMgr.FireEvent(TEventType.SecondConfirm, value);
        }

        /// <summary>
        /// 二次确认Toggle事件回调
        /// </summary>
        /// <param name="obj"></param>
        private void OnChooseToggle(object[] obj)
        {
            if (obj[0] == null)
            {
                Debug.Log("没有参数");
                return;
            }
            var para = obj[0];

            _toggleState = (bool)para;
        }

        /// <summary>
        /// 设置按钮
        /// </summary>
        /// <param name="root"></param>
        public void SetButtons(GameObject root)
        {
            var btnGroup = UIHelper.GetChild(root, "Board/Content/ButtonGroup");
            var btnBlueM1 = UIHelper.GetChild(btnGroup, "BtnBlueM1");
            var btnBlueM1b = UIHelper.GetChild(btnGroup, "BtnBlueM1b");
            var btnGreenM2 = UIHelper.GetChild(btnGroup, "BtnGreenM2");
            var btnRedM1 = UIHelper.GetChild(btnGroup, "BtnRedM1");
            var btnGreenM1 = UIHelper.GetChild(btnGroup, "BtnGreenM1");
            var btnGrayM1 = UIHelper.GetChild(btnGroup, "BtnGrayM1");
            var btnGoldM1 = UIHelper.GetChild(btnGroup, "BtnGoldM1");
            var btnGoldM2 = UIHelper.GetChild(btnGroup, "BtnGoldM2");
            var btnSwitchRole = UIHelper.GetChild(root, "Board/Content/SwitchRolesBtn");
            var btnClientLog = UIHelper.GetChild(root, "Board/Content/ClientLogBtn");

            btnBlueM1.SetActive(false);
            btnBlueM1b.SetActive(false);
            btnRedM1.SetActive(false);
            btnGreenM2.SetActive(false);
            btnGreenM1.SetActive(false);
            btnGoldM1.SetActive(false);
            btnGoldM2.SetActive(false);

            var data = InitData as UIMsgBoxInitData;

            var noCloseBtn = data.Typ > EMsgBoxType.zero_nc;
            var btnClose = UIHelper.GetChild(root, "Board/Content/BG/TitleBar/Title/CloseBtn");
            btnClose?.SetActive(!noCloseBtn);
            //if (btnClose.activeSelf)
            //    AddListener(EventTriggerType.Click, btnClose, OnClickClose);

            var msgBoxType = data.Typ;
            if (noCloseBtn)
                msgBoxType = (EMsgBoxType)(data.Typ - EMsgBoxType.zero_nc);

            var colorGroup = data.colorGroup;
            if (colorGroup == ButtonColorGroup.Default)
            {
                if (msgBoxType == EMsgBoxType.one)
                    colorGroup = ButtonColorGroup.Blue;
                else if (msgBoxType == EMsgBoxType.two)
                    colorGroup = ButtonColorGroup.BlueGlod;
            }

            // 清理数据
            m_Btn1 = null;
            m_Btn2 = null;
            m_Closedata = null;
            m_Btn1data = data.Btn1data;
            m_Btn2data = data.Btn2data;
            m_Closedata = data.Closedata;
            m_SwitchRoledata = data.BtnSwithcRoledata;

            if (msgBoxType == EMsgBoxType.one)
            {
                switch (colorGroup)
                {
                    case ButtonColorGroup.Blue:
                        m_Btn1 = btnBlueM1;
                        break;
                    case ButtonColorGroup.Green:
                        m_Btn1 = btnGreenM1;
                        break;
                    case ButtonColorGroup.Red:
                        m_Btn1 = btnRedM1;
                        break;
                    case ButtonColorGroup.Golden:
                        m_Btn1 = btnGoldM1;
                        break;
                    default:
                        m_Btn1 = btnBlueM1;
                        break;
                }
                m_Btn1.SetActive(true);
            }
            else if (msgBoxType == EMsgBoxType.two)
            {
                switch (colorGroup)
                {
                    case ButtonColorGroup.RedBlue:
                        m_Btn1 = btnRedM1;
                        m_Btn2 = btnBlueM1;
                        break;
                    case ButtonColorGroup.RedGold:
                        m_Btn1 = btnRedM1;
                        m_Btn2 = btnGoldM1;
                        break;
                    case ButtonColorGroup.RedGreen:
                        m_Btn1 = btnRedM1;
                        m_Btn2 = btnGreenM1;
                        break;
                    case ButtonColorGroup.GreenGold:
                        m_Btn1 = btnGreenM1;
                        m_Btn2 = btnGoldM1;
                        break;
                    case ButtonColorGroup.GreenBlue:
                        m_Btn1 = btnGreenM1;
                        m_Btn2 = btnBlueM1;
                        break;
                    case ButtonColorGroup.BlueGlod:
                        m_Btn1 = btnBlueM1;
                        m_Btn2 = btnGoldM1;
                        break;
                    case ButtonColorGroup.BlueBlue:
                        m_Btn1 = btnBlueM1;
                        m_Btn2 = btnBlueM1b;
                        break;
                    case ButtonColorGroup.GrayGreen:
                        m_Btn1 = btnGrayM1;
                        m_Btn2 = btnGreenM1;
                        break;
                    default:
                        m_Btn1 = btnBlueM1;
                        m_Btn2 = btnRedM1;
                        break;
                }
                m_Btn1.SetActive(true);
                m_Btn2.SetActive(true);
            }
            m_BtnSwitchRole = btnSwitchRole;

            if (m_Btn1data != null)
            {
                UIHelper.GetComponent<UniformText>(m_Btn1, "UniformText").Text = m_Btn1data.str;
                RemoveListener(EventTriggerType.Click, m_Btn1);
                AddListener(EventTriggerType.Click, m_Btn1, OnClickBtn1);
            }
            else
            {
                UIHelper.SafeSetActive(m_Btn1, false);
            }

            if(m_Btn2data != null)
            {
                UIHelper.GetComponent<UniformText>(m_Btn2, "UniformText").Text = m_Btn2data.str;
                RemoveListener(EventTriggerType.Click, m_Btn2);
                AddListener(EventTriggerType.Click, m_Btn2, OnClickBtn2);
            }
            else
            {
                UIHelper.SafeSetActive(m_Btn2, false);
            }

            //if(m_Closedata != null)
            //{

            if(btnClose != null)
            {
                RemoveListener(EventTriggerType.Click, btnClose);
                AddListener(EventTriggerType.Click, btnClose, OnClickClose);
            }
            //}

            if (m_SwitchRoledata != null)
            {
                RemoveListener(EventTriggerType.Click, m_BtnSwitchRole);
                AddListener(EventTriggerType.Click, m_BtnSwitchRole, OnClickSwitchRoleBtn);
            }

            if (data.ShowClientLogBtn && btnClientLog)
            {
                RemoveListener(EventTriggerType.Click, btnClientLog);
                AddListener(EventTriggerType.Click, btnClientLog, OnClickClientLog);
            }
        }

        /// <summary>
        /// 压入窗口栈的msgBox
        /// </summary>
        /// <param name="typ">类型</param>
        /// <param name="title">标题</param>
        /// <param name="content">内容</param>
        /// <param name="btn1data">按钮1数据</param>
        /// <param name="btn2data">按钮2数据</param>
        /// <param name="colorGroup">按钮颜色</param>
        public static void Push(EMsgBoxType typ, string title, string content,
            MsgBoxBtnParam btn1data = null,
            MsgBoxBtnParam btn2data = null,
            ButtonColorGroup colorGroup = ButtonColorGroup.Default, bool isUseWarningBG = false,
            bool isShowSwitchRoleBtn = false, MsgBoxBtnParam btnSwitchRoledata = null,
            bool isPanelTop = false, TextAnchor ContentTextAnchor = TextAnchor.MiddleCenter,
            bool showclientLog = false)
        {
            PopupManager.I.ShowDialog<UIMsgBox>(new UIMsgBoxInitData()
            {
                Typ = typ,
                Title = title,
                Content = content,
                ContentTextAnchor = ContentTextAnchor,
                Btn1data = btn1data,
                Btn2data = btn2data,
                colorGroup = colorGroup,
                openSecondEnum= UIMsgBoxInitData.OpenSecondEnum.Gold,
                IsUseWarningBG = isUseWarningBG,
                IsShowSwitchRoleBtn = isShowSwitchRoleBtn,
                BtnSwithcRoledata = btnSwitchRoledata,
                IsPanelTop = isPanelTop,
                ShowClientLogBtn = false,
            });
        }

        /// <summary>
        /// 二次消费确认压入窗口栈的msgBox
        /// </summary>
        /// <param name="typ">类型</param>
        /// <param name="title">标题</param>
        /// <param name="content">内容</param>
        /// <param name="btn1data">按钮1数据</param>
        /// <param name="btn2data">关闭按钮数据</param>
        /// <param name="colorGroup">按钮颜色</param>
        public static void Push(EMsgBoxType typ, string title, string content, bool isOpenSecondBtn,UIMsgBoxInitData.OpenSecondEnum openSecondEnum,
            MsgBoxBtnParam btn1data = null, MsgBoxBtnParam btn2data = null, MsgCloseBtnParam closedata = null,
            ButtonColorGroup colorGroup = ButtonColorGroup.Default, bool isUseWarningBG = false,
            bool isShowSwitchRoleBtn = false, MsgBoxBtnParam btnSwitchRoledata = null)
        {
            PopupManager.I.ShowDialog<UIMsgBox>(new UIMsgBoxInitData()
            {
                Typ = typ,
                Title = title,
                Content = content,
                IsOpenSecondBtn = isOpenSecondBtn,
                openSecondEnum= openSecondEnum,
                Btn1data = btn1data,
                Btn2data = btn2data,
                Closedata = closedata,
                colorGroup = colorGroup,
                IsUseWarningBG = isUseWarningBG,
                IsShowSwitchRoleBtn = isShowSwitchRoleBtn,
                BtnSwithcRoledata = btnSwitchRoledata,

            });
        }

        public static UniTask<bool> PushAsync(string title, string msg, string confirmButtonKey)
        {
            var tcs = new UniTaskCompletionSource<bool>();
            var btn1data = new MsgBoxBtnParam()
            {
                isClickAutoClose = false,
                str = LocalizationMgr.Get(confirmButtonKey),
                func = (o) =>
                {
                    tcs.TrySetResult(true);
                    PopupManager.I.ClosePopup<UIMsgBox>();
                }
            };
            var closeBtndata = new MsgCloseBtnParam()
            {
                func = (o) =>
                {
                    tcs.TrySetResult(false);
                }
            };

            PopupManager.I.ShowPanel<UIMsgBox>(new UIMsgBoxInitData()
            {
                Typ = EMsgBoxType.one,
                Title = title,
                Content = msg,
                Btn1data = btn1data,
                Closedata = closeBtndata,
                Btn2data = null,
                colorGroup = ButtonColorGroup.Default,
            });

            return tcs.Task;
        }
        
        //public static UniTask PushTipBoxAsync(string title, string msg)
        //{
        //    var ui = PopupManager.I.FindPopupFromPool<BoxNetDisconnect>();
        //    if (ui != null && ui.IsShow)
        //        PopupManager.I.ClosePopup<BoxNetDisconnect>();

        //    var tcs = new UniTaskCompletionSource();
        //    var btn1data = new MsgBoxBtnParam()
        //    {
        //        str = LocalizationMgr.Get("LC_MENU_confirm_cap"),
        //        func = (o) =>
        //        {
        //            tcs.TrySetResult();
        //        }
        //    };

        //    PopupManager.I.ShowPanel<BoxNetDisconnect>(new UIMsgBoxInitData()
        //    {
        //        Typ = EMsgBoxType.one_nc,
        //        Title = title,
        //        Content = msg,
        //        Btn1data = btn1data,
        //        Btn2data = null,
        //        colorGroup = ButtonColorGroup.Default,
        //    });

        //    return tcs.Task;
        //}
         

        public static bool CheckMsgBoxState()
        { 
            var ui1 = PopupManager.I.FindPopupFromPool<UILoginTipsDialog>();
            if (ui1 != null && ui1.IsShow)
                return true;

            return false;
        }

        //public static void PushNetDisconnectBox(Action onClick = null)
        //{
        //    var ui = PopupManager.I.FindPopupFromPool<BoxNetDisconnect>();
        //    if (ui != null && ui.IsShow)
        //        PopupManager.I.ClosePopup<BoxNetDisconnect>();

        //    var btn1data = new MsgBoxBtnParam()
        //    {
        //        str = LocalizationMgr.Get("LC_MENU_reconnect_cap"),
        //        func = (o) => 
        //            {
        //                //BILogInfo.UpdateFTEStageParameter("push net disconnect box");
        //                //LoadingLog.AddLoadingBILog("UIMsgBox: push net disconnect box");
        //                //ClientLogMgr.AddClientLogInfo(ClientLogType.ReconnectBoxRestart);
        //                Main.ReStart(); 
        //                onClick?.Invoke(); 
        //            }
        //    };

        //    PopupManager.I.ShowPanel<BoxNetDisconnect>(new UIMsgBoxInitData()
        //    {
        //        Typ = EMsgBoxType.one_nc,
        //        Title = LocalizationMgr.Get("LC_MENU_server_disconnected_title"),
        //        Content = LocalizationMgr.Get("LC_MENU_server_disconnected_desc"),
        //        Btn1data = btn1data,
        //        Btn2data = null,
        //        colorGroup = ButtonColorGroup.Default,
        //        //Closedata = new MsgCloseBtnParam() { func = (o) => { Main.ReStart(); } }
        //    });
        //}

        //public static void PushMigrationServerBox(Action onClick = null)
        //{
        //    var ui = PopupManager.I.FindPopupFromPool<BoxNetDisconnect>();
        //    if (ui != null && ui.IsShow)
        //        PopupManager.I.ClosePopup<BoxNetDisconnect>();

        //    var btn1data = new MsgBoxBtnParam()
        //    {
        //        str = LocalizationMgr.Get("LC_MENU_reconnect_cap"),
        //        func = (o) =>
        //        {
        //            //BILogInfo.UpdateFTEStageParameter("push migration server box");
        //            //LoadingLog.AddLoadingBILog("UIMsgBox: push migration server box");
        //            //ClientLogMgr.AddClientLogInfo(ClientLogType.ReconnectBoxRestart);
        //            Main.ReStart();
        //            onClick?.Invoke();
        //        }
        //    };

        //    PopupManager.I.ShowPanel<BoxNetDisconnect>(new UIMsgBoxInitData()
        //    {
        //        Typ = EMsgBoxType.one_nc,
        //        Title = LocalizationMgr.Get("LC_MENU_server_disconnected_title"),
        //        Content = LocalizationMgr.Get("Free_Transfer_08"),
        //        Btn1data = btn1data,
        //        Btn2data = null,
        //        colorGroup = ButtonColorGroup.Default,
        //        //Closedata = new MsgCloseBtnParam() { func = (o) => { Main.ReStart(); } }
        //    });
        //}
    }
}
