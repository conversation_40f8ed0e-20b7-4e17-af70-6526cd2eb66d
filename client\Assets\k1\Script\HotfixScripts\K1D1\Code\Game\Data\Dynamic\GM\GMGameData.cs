﻿#if UNITY_EDITOR || INTERNAL_ENTRANCE || DEFAULT_ENV_BETA

using Common;
using cspb;
using DeepUI;
using Game.Config;
using Game.Sprite;
#if UNITY_ANDROID
//using Google.Play.Review;
#endif
using Logic;
using Render;
using System;
using System.Collections;
using System.Collections.Generic;
using TFW;
using TFW.Localization;
using UI;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.EventSystems;
using Public;
using Game.Map.Cloud;
using Game.Sprite.Fight;
using System.Linq;
using System.Text.RegularExpressions;
using Config;
using Cysharp.Threading.Tasks;
using Game.Utils;
using K3;
using TFW.UI;
using ExLog;
using System.IO;

namespace Game.Data
{

    public partial class GameData
    {
        /// <summary>
        /// GM数据
        /// </summary>
        private GMGameData _gmGameData = new GMGameData();

        /// <summary>
        /// 初始化数据
        /// </summary>
        /// <returns></returns>
        public GMGameData GMData
        {
            get
            {
                return I._gmGameData;
            }
        }

    }

    /// <summary>
    /// GM相关数据
    /// <AUTHOR>
    /// @date 2020/2/20 12:35:34
    /// @ver 1.0
    /// </summary>
    public class GMGameData : IDisposable
    {
        /// <summary>
        /// 建造堡垒类型
        /// </summary>
        public enum GMBuildFortType
        {
            /// <summary>
            /// 建造堡垒(未建造)
            /// </summary>
            Type1,
            /// <summary>
            /// 建造堡垒(建造完)
            /// </summary>
            Type2,
            /// <summary>
            /// 建造旗帜(未建造)
            /// </summary>
            Type3,
            /// <summary>
            /// 建造旗帜(建造完)
            /// </summary>
            Type4,
            /// <summary>
            /// 随机联盟建造堡垒(未建造)
            /// </summary>
            Type5,
            /// <summary>
            /// 随机联盟建造堡垒(建造完)
            /// </summary>
            Type6,
            /// <summary>
            /// 随机联盟建造旗帜(未建造)
            /// </summary>
            Type7,
            /// <summary>
            /// 随机联盟建造旗帜(建造完)
            /// </summary>
            Type8,
            /// <summary>
            /// 检测领地是否有效
            /// </summary>
            Type9,
            /// <summary>
            /// 删除
            /// </summary>
            DelFort,
        }

        private static long fortEntityId;

        /// <summary>
        /// 记录堡垒或旗帜id
        /// </summary>
        /// <param name="entityId"></param>
        public static void RecordFort(long entityId)
        {
            fortEntityId = entityId;
        }

        /// <summary>
        /// 删除堡垒或旗帜
        /// </summary>
        public static void DelFort()
        {
            if (fortEntityId > 0)
            {
                SendPlayerGmCmdNoPlayerId("delfort", fortEntityId.ToString());
            }
        }

        /// <summary>
        /// 领地堡垒旗帜建造
        /// </summary>
        public static async UniTask BuildFort(GMBuildFortType type)
        {
            if (type == GMBuildFortType.DelFort)
            {
                DelFort();
                return;
            }
            string unionId = LPlayer.I.UnionID.ToString();
            string buildCfgId = (await Cfg.G.CUnionTerritory.RandomFortCfg()).Id.ToString();
            string isBuildOk = "0";
            var x = ((int)RMap.ViewCenter.x * 1000).ToString();
            var z = ((int)RMap.ViewCenter.z * 1000).ToString();

            if (type == GMBuildFortType.Type9)
            {
                var v3 = RMap.GetLastClickPosition();
                SendPlayerGmCmdNoPlayerId("isManorActive", ((int)v3.x * 1000).ToString(), ((int)v3.z * 1000).ToString(), unionId);
                return;
            }

            if (type == GMBuildFortType.Type1 || type == GMBuildFortType.Type2
                || type == GMBuildFortType.Type3 || type == GMBuildFortType.Type4
                )
            {
                if (LPlayer.I.UnionID == 0)
                {
                    FloatTips.I.FloatMsg("Client_tips_02".ToLocal());
                    return;
                }
            }
            else
            {
                var list = LAllianceMgr.I.GetRecommendUnionList();
                if (list.List.Count == 0)
                {
                    D.Warning?.Log("先打开联盟列表获取联盟数据!");
                    return;
                }
                var idx = UnityEngine.Random.Range(0, list.List.Count);
                unionId = list.List[idx].ID.ToString();
            }

            switch (type)
            {
                case GMBuildFortType.Type2:
                case GMBuildFortType.Type6:
                    isBuildOk = "1";
                    break;
                case GMBuildFortType.Type3:
                case GMBuildFortType.Type7:
                    buildCfgId = (await Cfg.G.CUnionTerritory.FlagCfg()).Id.ToString();
                    break;
                case GMBuildFortType.Type4:
                case GMBuildFortType.Type8:
                    buildCfgId = (await Cfg.G.CUnionTerritory.FlagCfg()).Id.ToString();
                    isBuildOk = "1";
                    break;
                default:
                    break;
            }

            D.Debug?.Log("buildfort unionId={0}, buildCfgId={1}, isBuildOk={2}, x={3}, z={4}", unionId, buildCfgId, isBuildOk, x, z);
            //buildfort 参数：联盟id 建筑id 是否建造完毕 1=建造完毕 x单位毫米z
            SendPlayerGmCmdNoPlayerId("buildfort", unionId, buildCfgId, isBuildOk, x, z);
        }

        /// <summary>
        /// 标志位，营造特殊测试环境
        /// if(!Game.Data.GMGameData.GMFlag)
        /// </summary>
        public static bool GMFlag = false;

        /// <summary>
        /// GM面板的数据，在此添加即可同步GM面板
        /// </summary>
        public Dictionary<string, List<GMItem>> gmInfo = new Dictionary<string, List<GMItem>>
        {
             {
                "置顶",
                new List<GMItem>
                {
                    new GMItem() {title = "跳过当前章节任务", callBack = (g, p) =>
                        {
                            SendPlayerGmCmd("finishall");
                            ChapterTaskGameData.I.GetChapterQuestInfo();
                        },
                    },
                    new GMItem{ title = "跳过当前订单", callBack = (g, p)=>
                        {
                            var uimerge=   DeepUI.PopupManager.I.FindPopup<K3.UIMerge>();
                            if(uimerge!=null)
                            {
                                 uimerge.CurTaskAutoTODO();

                                 PopupManager.I.ClosePopup<UIGmWindow>();
                            }
                        }},
                    new GMItem
                    {
                        title = "一键变强", callBack = async (g, p)=>
                        {
                            SendPlayerGmCmd("hyper");
                            UniTask.Delay(200);
                            SendPlayerGmCmd("kickme");
                        }
                    },

                    new GMItem
                    {   title = "发送指定道具", callBack = (g, p)=>
                        {
                            PopupManager.I.ShowPanel<UI.UIGMWithSelect>();
                        }
                    },

                    new GMItem
                    {
                        title = "通用GM", callBack = (g, p)=>
                        {
                            PopupManager.I.ShowPanel<UI.UIGM>();
                        }
                    },

                    new GMItem
                    { title =  "踢自己下线", callBack = (g, p)=>
                        {
                            SendPlayerGmCmd("kickme");
                        }
                    },
                    new GMItem
                    { title =  "日志级别切换", callBack = (g, p)=>
                        {
                            if(ExLogMgr.logLevel==0)
                            {
                                ExLogMgr.SetLogLevel((int)ExLogLevelEnum.Error);

                                var logLevelPath= $"{Application.persistentDataPath}/LogLevel";
                                File.WriteAllText(logLevelPath,"4");
                            }
                            else
                            {
                                ExLogMgr.SetLogLevel((int)ExLogLevelEnum.Debug);

                                  var logLevelPath= $"{Application.persistentDataPath}/LogLevel";
                                File.WriteAllText(logLevelPath,"0");
                            }
                        }
                    },
                     new GMItem{ title = "删除自己账号(慎用)", callBack = (g, p)=>
                            {
                                SendPlayerGmCmd("delme");


                                    PlayerPrefs.DeleteAll();
                                    PlatformInfo.I.RandomUDID();
                                   
#if UNITY_EDITOR
                                         UnityEditor.EditorApplication.isPlaying = false;
#else
                                          Application.Quit(); 
#endif
                            }
                        },

                        new GMItem{
                            title = $"自动合成:强制开关[{UIMerge.ForceAutoMergeingGM}]",
                            callBack = (g, p)=>
                            {
                                 UIMerge.ForceAutoMergeingGM=!  UIMerge.ForceAutoMergeingGM;

                             g.GetComponentInChildren<TFWText>().text=$"自动合成:强制开关[{UIMerge.ForceAutoMergeingGM}]";


                            }},


                    new GMItem()
                    {
                        title = "城堡等级xx级", callBack = async (g, p)=>
                        {
                            UITools.PopTips($"输入城堡等级");
                            var result = await UIGM.ShowAsync();
                            if (!string.IsNullOrEmpty(result))
                            {
                                SendPlayerGmCmd("newtechLvl", "1", result.ToString());
                            }
                        }
                    }
                }
            },
               {
                    "棋盘玩法",
                    new List<GMItem>
                    {
                        new GMItem{ title = "章节任务（新）", callBack = (g, p)=>
                        {
                            PopupManager.I.ShowPanel<UIChapterTask>(new UIChapterTaskPopupData()
                            {
                                IsTest = true
                            });
                            // GuidManage.TriggerStation();
                            // PopupManager.I.ClosePopup<UIGmWindow>();
                        }},
                        new  GMItem{ title = "坦克工坊", callBack = (g, p)=>
                        {
                            PopupManager.I.ShowLayer<UITankFactory>();
                        }},
                        new GMItem{ title = "哨塔", callBack = (g, p)=>
                        {
                            PopupManager.I.ShowLayer<UIStargazingPlatform>();
                        }},
                        new GMItem{ title = "一键解锁（Unlock）", callBack = (g, p)=>
                        {
                            UnlockMgr.I.GMUnlock = true;
                            EventMgr.FireEvent(TEventType.OnUnlockConditionChanged);
                        }},

                        new GMItem{ title = "合成之王Tips", callBack = (g, p)=>
                        {
                            FloatTips.I.FloatMsg("99,999,999", FloatTips.TipsType.KingOfMerge);
                        }},
                        new GMItem{ title = "最强指挥官Tips", callBack = (g, p)=>
                        {
                            FloatTips.I.FloatMsg("99,999,999:999,999", FloatTips.TipsType.StrongestLoad1);
                        }},
                        new GMItem()
                        {
                            title = "首冲试用结束动画",
                            callBack = (_, _) =>
                            {
                                EventMgr.FireEvent(TEventType.TestUseHeroEnd);

                                PopupManager.I.ClosePopup<UIGmWindow>();
                            }
                        },
                        new GMItem{ title = "解锁迷雾", callBack = (g, p)=>
                        {
                            var uimerge=   DeepUI.PopupManager.I.FindPopup<K3.UIMerge>();
                            uimerge?.ClearMist();
                        }},
                        new GMItem{ title = "清空棋盘", callBack = (g, p)=>
                        {
                             var uimerge_maze=   DeepUI.PopupManager.I.FindPopup<K3.UIMerge_Maze>();
                            if(uimerge_maze!=null)
                            {
                                   uimerge_maze?.ClearGrids();
                            }
                            else
                            {
                           var uimerge=   DeepUI.PopupManager.I.FindPopup<K3.UIMerge>();
                            uimerge?.ClearGrids();
                           }
                        } },

                         new GMItem{ title = "道具双击Copy", callBack = (g, p)=>
                        {
                            K3.UIMerge.douclickCopyGood=!K3.UIMerge.douclickCopyGood;
                        }},



                           new GMItem{ title = "解锁新英雄", callBack = (g, p)=>
                        {  List<HeroData> unlockHeros = new List<HeroData>();

                            unlockHeros.Add(new HeroData(0,435,1,1,0,0));

                               DeepUI.PopupManager.I.ShowDialog<NewHeroShow>(new UINewHeroShowParams()
                    {
                        ShowHeroDatas = unlockHeros,
                        first = true
                    });
                        }},


                    }
                },

            {
                "命令行GM",
                new List<GMItem>
                { 
                    new GMItem
                    {
                        title = "龙皮肤入口", callBack = (g, p)=>
                        {
                            DragonSkinSelectUI.Show();
                        }
                    },
                    
 
                    new GMItem
                    {
                        title = "清除缓存", callBack = (g, p)=>
                        {
                            PlayerPrefs.DeleteAll();
                        }
                    },
                    new GMItem
                    {
                        title = "当前配表分支是？", callBack = (g, p)=>
                        {
                            UITools.PopTips($"当前配表分支是 {MetaConfig.TABLE_CONFIG}");
                        }
                    },

                    new GMItem
                    {
                        title = "消除小游戏调试", callBack = (g, p)=>
                        {
                            TileMatching.SlotManager.enableDebug = !TileMatching.SlotManager.enableDebug;
                            UITools.PopTips($"消除小游戏调试开关已切换为 {TileMatching.SlotManager.enableDebug}");
                        }
                    },
                    new GMItem
                    {
                        title = "切换邮件配置ID显隐", callBack = (g, p)=>
                        {
                            UI.Mail.UImail.debugShowConfigId = !UI.Mail.UImail.debugShowConfigId;
                            UITools.PopTips($"邮件配置ID显隐已切换为 {UI.Mail.UImail.debugShowConfigId}");
                        }
                    },

                    new GMItem
                    {
                        title="视野刷新",callBack = (g,p)=>
                        {
                              //视野数据刷新
                                RMap.UpdateMapViewInfo();
                        }
                    },


                    new GMItem
                    {
                        title = "冰霜巨兽Test", callBack = (g, p)=>
                        {
                            IceBossManager.I.TryStartDropBoss();
                            PopupManager.I.ClosePopup<UI.UIGmWindow>();
                        }
                    },
                     new GMItem
                    {
                        title = "模拟快速点击活动&商城", callBack = (g, p)=>
                        {
                            UIActivityMain.Open();

                            ShopMgr.I.OpenShopPanel();
                            EventMgr.FireEvent(TEventType.HideTriggerGuide);
                            EventMgr.FireEvent(TEventType.ChangeTowerFightVolume);

                            UIActivityMain.Open();

                            ShopMgr.I.OpenShopPanel();
                            EventMgr.FireEvent(TEventType.HideTriggerGuide);
                            EventMgr.FireEvent(TEventType.ChangeTowerFightVolume);

                            UIActivityMain.Open();


                            ShopMgr.I.OpenShopPanel();
                            EventMgr.FireEvent(TEventType.HideTriggerGuide);
                            EventMgr.FireEvent(TEventType.ChangeTowerFightVolume);

                            UIActivityMain.Open();
                        }
                    },
                     new GMItem
                     {
                          title = "打开龙装备预览界面", callBack = (g, p)=>
                        {

                            PopupManager.I.ShowPanel<DragonEquipmentPreviewUI.Layer>();
                        }
                     },
                    // new GMItem
                    //{
                    //    title = "测试强制更新", callBack = (g, p)=>
                    //    {
                    //        VerForceUpdateUtils.ShowVerUpdatePanel();
                    //    }
                    //},

                     new GMItem
                    {
                        title = "客诉测试", callBack = (g, p)=>
                        {
                            FiveStarPraiseUI.isTest = true;
                            FiveStarPraiseUI.Open();
                        }
                    },
                     new GMItem
                    {
                        title = "客诉正式", callBack = (g, p)=>
                        {
                            FiveStarPraiseUI.isTest = false;
                            FiveStarPraiseUI.Open();
                        }
                    },
                    new GMItem
                    {
                        title = "跨服跳阶段", callBack = (g, p)=>
                        {
                            UIGM.SendGMReq("actv",false,new string[]{
                                "cdg_next_stage",
                                "9001"
                            });
                        }
                    },
                    new GMItem
                    {
                        title = "添加30个假联盟", callBack = (g, p)=>
                        {
                            UIGM.SendGMReq("cdg_match_test",true,new string[]{
                            "30"});
                        }
                    },

                    new GMItem{ title = "充值金币1000000000", callBack = (g, p)=> { AddVMCount("vm", 11151001, 1000000000); } },
                    new GMItem{ title = "充值钻石1000000000", callBack = (g, p)=> { AddVMCount("vm", 11151002, 1000000000); } },
                    new GMItem{ title = "清空钻石和金币", callBack = (g, p)=> {
                        AddVMCount("vm", 11151001, -(int)PlayerAssetsMgr.I.GetVMCount(11151001));
                        AddVMCount("vm", 11151002, -(int)PlayerAssetsMgr.I.GetVMCount(11151002));
                    } },
                    new GMItem
                    {
                        title = "地图画线测试", callBack = (g,p)=>
                        {
                            LElementalContinentMgr.I.Init();
                        }
                    },
                    new GMItem
                    {
                        title = "测试嵌入Web网页", callBack = (g,p)=>
                        {
                            DeepUI.PopupManager.I.ShowPanel<OpenWebView>(new OpenWebUrlData()
                            {
                                url = "https://www.baidu.com/"
                            });
                        }
                    },
                    new GMItem
                    {
                        title = "显示 OS Version", callBack = (g,p)=>
                        {
                            FloatTips.I.FloatMsg(SystemInfo.operatingSystem);
                        }
                    },
                    new GMItem
                    { title = "重启游戏", callBack = (g, p)=>
                        {
                            PlatformUtils.RestartApp(null);
                        }
                    },
                    new GMItem
                    { title = "切换模拟器状态", callBack = (g, p)=>
                        {
                            PlatformUtils.IsSimulator = !PlatformUtils.IsSimulator;
                            FloatTips.I.FloatMsg("当前模拟器状态为 " + PlatformUtils.IsSimulator);
                        }
                    },
                    new GMItem
                    {
                        title = "世界树", callBack = (g, p)=>
                        {
                            PopupManager.I.ShowLayer<UIFracturedLandsWorldTreeDonation>();
                        }
                    },
                    //new GMItem
                    //{ title = "切换WebPay状态", callBack = (g, p)=>
                    //    {
                    //        K1D2Config.I.isUseWebPay = !K1D2Config.I.isUseWebPay;
                    //    }
                    //},
                    new GMItem
                    { title = "测试弹出界面", callBack = (g, p)=>
                        {
                            AutoOpenMgr.I.AutoOpenLogic();
                        }
                    },

                     new GMItem
                    { title = "测试阿拉伯文字", callBack = (g, p)=>
                        {
                            //UI.UIData uiData = new UI.UIData();
                            PopupManager.I.ShowPanel<UI.ArabicSupportTest>();
                        }
                    },
                    //new GMItem() { title = "增加100关", callBack = HundredStage,},
                     new GMItem
                    { title = "发送链接跨服战场准备", callBack = (g, p)=>
                        {
                            SendPlayerGmCmd("kvk_c");
                        }
                    },

                    new GMItem
                    { title = "打开国王技能界面", callBack = (g, p)=>
                        {
                            PopupManager.I.ShowPanel<UIThroneWarKingSkill>();
                        }
                    },

                    new GMItem
                    { title = "成为国王，可以使用国王技能", callBack = (g, p)=>
                        {
                            SendPlayerGmCmd("kingskill");
                        }
                    },

                    new GMItem
                    { title = "国王技能恢复次数", callBack = (g, p)=>
                        {
                            SendPlayerGmCmd("kingskill", "1");
                        }
                    },

// #if USE_TGSBASIC && !UNITY_WEBGL
//                      new GMItem
//                     { title = "打开Log们", callBack = (g, p)=>
//                         {
//                               ExLog.ExLogMgr.SetAllLogEnable(true);
//                             TGS.Utils.LogUtil.SetInternalLogEnable(true);
//                               LChat.I.enableChatInternalLog = true;
//                               //TGS.Chat.Logger.SetInternalLogEnable(true);
//                         }
//                      },
//                      new GMItem
//                     { title = "关上Log们", callBack = (g, p)=>
//                         {
//                               ExLog.ExLogMgr.SetAllLogEnable(false);
//                               LChat.I.enableChatInternalLog = false;
//                             TGS.Utils.LogUtil.SetInternalLogEnable(false);
//                               //TGS.Chat.Logger.SetInternalLogEnable(false);
//                         }
//                      },
// #endif
                     new GMItem()
                     { title = "显示多语言内容", callBack = (g, p) =>
                         {
                             LocalizationMgr.ShowLCKey = false;
                             FloatTips.I.FloatMsg("显示多语言内容");
                         }
                     },
                     new GMItem()
                     { title = "显示多语言key", callBack = (g, p) =>
                         {
                             LocalizationMgr.ShowLCKey = true;
                             FloatTips.I.FloatMsg("显示多语言key");
                         }
                     },

                     //new GMItem() { title = "增加10000关", callBack = HundredTenThousandStage,},
                    new GMItem() { title = "GM标志位", callBack = (g, p) => { GMFlag = !GMFlag; },},
                    new GMItem() { title = "获取随机位置", callBack = TestRandom,},
                    new GMItem
                    {
                        title = "填写ItemID测试飞", callBack = (g, p)=>
                        {
                            UI.UIData uiData = new UI.UIData();
                            UI.WndMgr.Show<UI.UIGMItemFly>("UIGMItemFly", uiData);
                        }
                    },


                     new GMItem(){ title = "重置加入联盟的时间到8天前", callBack = (g, p)=>
                     {
                         TaskMgr.I.QuestInfoReq();
                         SendPlayerGmCmd("resetUnionJoinTime");
                     } },

                     //new GMItem(){ title = "设置画质（High）", callBack = (g, p)=>
                     //{
                     //    GameQualityMgr.I.SetDebugProfileID(4);
                     //    GameQualityMgr.I.DetectQuality(true);
                     //} },

                     //new GMItem(){ title = "设置画质（Medium）", callBack = (g, p)=>
                     //{
                     //    GameQualityMgr.I.SetDebugProfileID(3);
                     //    GameQualityMgr.I.DetectQuality(true);
                     //} },

                     //new GMItem(){ title = "设置画质（Low）", callBack = (g, p)=>
                     //{
                     //    GameQualityMgr.I.SetDebugProfileID(2);
                     //    GameQualityMgr.I.DetectQuality(true);
                     //} },

                     //new GMItem(){ title = "设置画质（VeryLow）", callBack = (g, p)=>
                     //{
                     //    GameQualityMgr.I.SetDebugProfileID(1);
                     //    GameQualityMgr.I.DetectQuality(true);
                     //} },

                     new GMItem
                    {
                        title = "模拟断线重连（心跳超时）", callBack = (g, p)=>
                        {
                             GameServerConnection.GameServerConnection.Disconnect();
                             Close();
                        }
                    },

                    //new GMItem
                    //{
                    //    title = "模拟断线重连（连接断开）", callBack = (g, p)=>
                    //    {
                    //        GameServerConnection.GameServerConnection.Disconnect();
                    //         EventMgr.FireEvent(TEventType.TryReconnect, ReconnectEnum.SeverBreakReally);
                    //         Close();
                    //    }
                    //},

                    new GMItem
                    {
                        title = "模拟切换网络。TCP - WebSocket", callBack = (g, p)=>
                        {
                            LoginMgr.I.CurGateAddressIdx++;
                            GameServerConnection.GameServerConnection.Disconnect();
                            Close();
                        }
                    },

                    new GMItem
                    {
                        title = "触发CRASH", callBack = (g, p)=>
                        {
                            D.Warning?.Log("触发CRASH");
                            UnityEngine.Diagnostics.Utils.ForceCrash(UnityEngine.Diagnostics.ForcedCrashCategory.AccessViolation);
                        }
                    },
                    new GMItem
                    {
                        title = "模拟跨天", callBack = (g, p)=>
                        {
                            D.Warning?.Log("模拟跨天");
                            EventMgr.FireEvent(TEventType.ChangeDay);
                        }
                    },


                    new GMItem
                    {
                        title = "跳转评价", callBack = (g, p)=>
                        {
#if UNITY_IOS
                            Game.Utils.AppleStoreUtils.ShowStoreReview();
#else
                            Utils.GoogleStoreUtils.OpenAPPinMarket(false);
#endif
                        }
                    },



        }
            },
            {
                "错误码多语言拼接测试",
                new List<GMItem>
                {
                    new GMItem
                    {
                        title = "错误码多语言拼接测试", callBack = async (g, p)=>
                        {
                            UITools.PopTips($"输入错误码CfgID");
                            var result = await UIGM.ShowAsync();
                            UIGM.Close();
                            if (!string.IsNullOrEmpty(result) && int.TryParse(result, out var errCodeCfgId))
                            {
                                Logic.Common.CheckErrorCode((ErrCode)errCodeCfgId, true);
                            }
                        }
                    },
                }
            },
            {
                "存钱罐",
                 new List<GMItem>
                {
                    new GMItem
                    {
                        title = "存钱罐进度加满", callBack = async (g, p)=>
                        {
                            SendPlayerGmCmd("piggybank_finish");
                        }
                    },
                }
            },

            {
                "跨服巨龙",
                new List<GMItem>
                {
                    new GMItem
                    {
                        title = "活动重置900109", callBack = async (g, p)=>
                        {
                            SendPlayerGmCmd("timereset");
                            await UniTask.Delay(200);
                            SendPlayerGmCmdNoPlayerId("actv del 900109");
                            await UniTask.Delay(200);
                            SendPlayerGmCmdNoPlayerId("actv newActv 900109");
                            await UniTask.Delay(500);
                            SendPlayerGmCmd("kickme");
                        }
                    },
                    new GMItem
                    {
                        title = "切换到下一个阶段", callBack = async (g, p)=>
                        {
                            //如果切换到战斗阶段 会马上创建战斗场景
                            SendPlayerGmCmd("actv", "cdg_next_stage", "900109");
                        }
                    },
                    new GMItem
                    {
                        title = "进入到巨龙战斗中", callBack = async (g, p)=>
                        {
                            SendPlayerGmCmd("enterdgl");
                        }
                    }
                }
            },
            {
                "本服巨龙",
                new List<GMItem>
                {
                    new GMItem
                    {
                        title = "活动重置100004", callBack = async (g, p)=>
                        {
                            SendPlayerGmCmd("timereset");
                            await UniTask.Delay(200);
                            SendPlayerGmCmdNoPlayerId("actv del 100004");
                            await UniTask.Delay(200);
                            SendPlayerGmCmdNoPlayerId("actv newActv 100004");
                            await UniTask.Delay(500);
                            SendPlayerGmCmd("kickme");
                        }
                    },
                    new GMItem
                    {
                        title = "切换到下一个阶段", callBack = async (g, p)=>
                        {
                            //如果切换到战斗阶段 会马上创建战斗场景
                            SendPlayerGmCmd("dglnext");
                        }
                    },
                    new GMItem
                    {
                        title = "进入到巨龙战斗中", callBack = async (g, p)=>
                        {
                            SendPlayerGmCmd("enterdgl");
                        }
                    }
                }
            },
            {
                "光明战场",
                new List<GMItem>
                {
                    new GMItem
                    {
                        title = "光明战场规则", callBack = async (g, p)=>
                        {
                            ActivityLightBattleRuleUI.Show();
                        }
                    },
                    new GMItem
                    {
                        title = "活动重置6000000", callBack = async (g, p)=>
                        {
                            SendPlayerGmCmd("timereset");
                            await UniTask.Delay(200);
                            SendPlayerGmCmdNoPlayerId("actv del 6000000");
                            await UniTask.Delay(200);
                            SendPlayerGmCmdNoPlayerId("actv newActv 6000000");
                            await UniTask.Delay(500);
                            SendPlayerGmCmd("kickme");
                        }
                    },
                    new GMItem
                    {
                        title = "进入战场", callBack = async (g, p)=>
                        {
                            SendPlayerGmCmd("lb_create", "10060", "10", "600000");
                            await UniTask.Delay(200);
                            SendPlayerGmCmd("lb_enter", "10060", "10");
                        }
                    },
                    new GMItem
                    {
                        title = "离开战场", callBack = async (g, p)=>
                        {
                            SendPlayerGmCmd("leave_cross_scene", "true");
                        }
                    }
                }
            },
            {
                "活动工具",
                new List<GMItem>
                {
                    new GMItem
                    {
                        title = "活动数据查找", callBack = async (g, p)=>
                        {
                            UITools.PopTips($"输入活动CfgID");
                            var result = await UIGM.ShowAsync();
                            if (!string.IsNullOrEmpty(result) && int.TryParse(result, out var actvCfgId))
                            {
                                var ok = ActivityMgr.I.Activities.Any(x => x.Brief.ConfigId == actvCfgId);
                                UITools.PopTips($"活动CfgID {actvCfgId}数据 {(ok ? "存在" : "不存在")}");
                            }
                        }
                    }

                }
            },
            {
                "循环礼包",
                new List<GMItem>
                {
                    new GMItem
                    {
                        title = "增加守卫币（支付代币）1千", callBack = (g, p)=>
                        {
                            AddVMCount("vm", 11151005, 1000);
                        }
                    },
                    new GMItem
                    {
                        title = "循环礼包配置 ID 显隐", callBack = (g, p)=>
                        {
                            GiftLinkItemUI.debugShowLinkId = !GiftLinkItemUI.debugShowLinkId;
                            UITools.PopTips($"循环礼包配置ID显隐已切换为 {GiftLinkItemUI.debugShowLinkId}");
                        }
                    },
                    new GMItem
                    {
                        title = "循环礼包循环取值测试", callBack = (g, p)=>
                        {
                            UnityEngine.Debug.Log($"<color=yellow>循环礼包循环取值算法测试: group=1, index=0, count=20</color>");
                            var str = string.Empty;
                            foreach (var cfg in GiftLinkMgr.I.GetConfigs(1, 0, 20))
                            {
                                str += $"{cfg.Id},";
                            }
                            UnityEngine.Debug.Log($"<color=yellow>{str}</color>");

                            UnityEngine.Debug.Log($"<color=yellow>循环礼包循环取值算法测试: group=1, index=2, count=5</color>");
                            str = string.Empty;
                            foreach (var cfg in GiftLinkMgr.I.GetConfigs(1, 2, 5))
                            {
                                str += $"{cfg.Id},";
                            }
                            UnityEngine.Debug.Log($"<color=yellow>{str}</color>");
                        }
                    },
                }
            },
            {
                "赏金公会",
                new List<GMItem>
                {
                    new GMItem
                    {
                        title = "直接完成赏金公会任务", callBack = async (g, p)=>
                        {
                            UITools.PopTips($"输入任务ID");
                            var result = await UIGM.ShowAsync();
                            if (!string.IsNullOrEmpty(result))
                            {
                                //bounty_guild_rd_over 勾选playerid
                                UIGM.SendGMReq("bounty_guild_rd_over", true, result);
                            }
                        }
                    },
                    new GMItem
                    {
                        title = "切换赏金公会任务ID显隐", callBack = (g, p)=>
                        {
                            BountyGuild.BountyGuildMgr.debugShowTaskId = !BountyGuild.BountyGuildMgr.debugShowTaskId;
                            UITools.PopTips($"赏金公会任务ID显隐已切换为 {BountyGuild.BountyGuildMgr.debugShowTaskId}");
                        }
                    },
                }
            },
            {

                "跨服战场",
                new List<GMItem>
                {
                    new GMItem
                    { title = "创建跨服战场", callBack = (g, p)=>
                        {
                            SendPlayerGmCmd("create_server_battle");
                        }
                    },
                    new GMItem
                    { title = "进入跨服战场", callBack = (g, p)=>
                        {
                            //SendPlayerGmCmd("enter_server_battle");
                            //LCrossServer.I.SendServerSwitchReq(SceneType.SceneTypeServersWar, isForce:true);
                            //请求进入巨龙战场
                            LCrossServer.I.SendServerSwitchReq(SceneType.SceneTypeKvk);
                        }
                    },
                    new GMItem
                    { title = "退出跨服战场", callBack = (g, p)=>
                        {
                            //SendPlayerGmCmd("enter_server_battle");
                            LCrossServer.I.OnChangeGoHomeServer();
                        }
                    },

                    new GMItem
                    { title = "进入战场地图", callBack = (g, p)=>
                        {
                            LElementalContinentMgr.I.EnterElementalWarMap(false);
                        }
                    },
                    new GMItem
                    { title = "退出战场地图", callBack = (g, p)=>
                        {
                            LElementalContinentMgr.I.ExitElementalWarMap(false);
                        }
                    },

                }
            },

            {

                "巨龙之战",
                new List<GMItem>
                {
                    //new GMItem
                    //{ title = "刷新地图", callBack = (g, p)=>
                    //    {
                    //        DragonWarMgr.I.UpdateMapInfo();
                    //    }
                    //},
                    new GMItem
                    { title = "进入世界地图", callBack = (g, p)=>
                        {
                            //K1D2Config.I.IsDragonWar = true;
                            //MapCloudManager.instance.UpdateDragonWarSign(false);
                            //GameData.I.DragonWarData.SignData.UpdateDragonWaringSign(false);
                            //Main.ReStart();

                            //退出巨龙之战地图
                            //DragonWarMgr.I.ExitDragonWarMap();
                            LCrossServer.I.OnChangeGoHomeServer();
                            //LCrossServer.I.SendExitCross();
                        }
                    },
                    new GMItem
                    { title = "巨龙 未开放", callBack = (g, p)=>
                        {
                            SendPlayerGmCmd("dgchangephase", "0");
                        }
                    },
                    new GMItem
                    { title = "巨龙 准备(报名开启倒计时)", callBack = (g, p)=>
                        {
                            SendPlayerGmCmd("dgchangephase", "1");
                        }
                    },
                    new GMItem
                    { title = "巨龙 预备(报名阶段)", callBack = (g, p)=>
                        {
                            SendPlayerGmCmd("dgchangephase", "2");
                        }
                    },
                    new GMItem
                    { title = "巨龙 战前(战斗开始倒计时)", callBack = (g, p)=>
                        {
                            SendPlayerGmCmd("dgchangephase", "3");
                        }
                    },
                    new GMItem
                    { title = "巨龙 战前(创建巨龙地图)", callBack = (g, p)=>
                        {
                            SendPlayerGmCmd("ctdgscene");
                        }
                    },
                    new GMItem
                    { title = "巨龙 战斗", callBack = (g, p)=>
                        {
                            SendPlayerGmCmd("dgchangephase", "4");
                        }
                    },
                    new GMItem
                    { title = "巨龙 战后(活动结束)", callBack = (g, p)=>
                        {
                            SendPlayerGmCmd("dgchangephase", "5");
                        }
                    },
                    new GMItem
                    { title = "巨龙 退出战场", callBack = (g, p)=>
                        {
                            SendPlayerGmCmd("closedgwar");
                        }
                    },
                    new GMItem
                    { title = "巨龙 清理报名数据", callBack = (g, p)=>
                        {
                            SendPlayerGmCmd("deldgdata");
                        }
                    },


                    new GMItem
                    {
                        title = "跨服巨龙跳阶段", callBack = (g, p)=>
                        {
                            UIGM.SendGMReq("actv",false,new string[]{
                                "cdg_next_stage",
                                "9001"
                            });
                        }
                    },
                    new GMItem
                    {
                        title = "跨服巨龙添加30个假联盟", callBack = (g, p)=>
                        {
                            UIGM.SendGMReq("cdg_match_test",true,new string[]{
                            "30"});
                        }
                    },


                }
            },

            {
                "商城",
                new List<GMItem>
                {
                    new GMItem
                    { title = "当前奖励版本是？", callBack = (g, p)=>
                        {
                             UITools.PopTips($"当前奖励版本是 {VersionRewardMgr.I.currentVersion}，配表分支 {MetaConfig.TABLE_CONFIG}");
                        }
                    },
                    new GMItem
                    { title = "切换礼包标题ID显隐", callBack = (g, p)=>
                        {
                            GiftItemData.debugShowId = !GiftItemData.debugShowId;
                            UITools.PopTips($"礼包标题ID显隐已切换为 {GiftItemData.debugShowId}");
                        }
                    },
                    new GMItem
                    { title = "切换道具标题ID显隐", callBack = (g, p)=>
                        {
                            Cfg.G.CItem.showIdInTitle = !Cfg.G.CItem.showIdInTitle;
                            UITools.PopTips($"道具标题ID显隐已切换为 {Cfg.G.CItem.showIdInTitle}");
                        }
                    },
                    new GMItem
                    { title = "活动日历切换商店活动显隐", callBack = (g, p)=>
                        {
                            UIActivityCalendar.showShopActivity = !UIActivityCalendar.showShopActivity;
                            UITools.PopTips($"活动日历商店活动显隐已切换为 {UIActivityCalendar.showShopActivity}");
                        }
                    },
                    new GMItem
                    {
                        title = "道具补足弹窗", callBack = async (g, p)=>
                        {
                            UITools.PopTips($"输入道具ID");
                            var result = await UIGM.ShowAsync();
                            UIGM.Close();
                            if (!string.IsNullOrEmpty(result) && int.TryParse(result, out var itemId))
                            {
                                var itemConfig = await Cfg.C.CItem.GetConfigAsync(itemId);
                                ItemStoreMgr.I.OpenGetPropPop(itemConfig, 50, -1);
                            }
                        }
                    },

                    new GMItem{ title = "增加石头木材铁矿各100", callBack = (g, p)=> {
                        AddVMCount("item", 20010000, 100);
                        AddVMCount("item", 20010001, 100);
                        AddVMCount("item", 20010002, 100);
                    } },
                    new GMItem{ title = "增加石头木材铁矿各10000", callBack = (g, p)=> {
                        AddVMCount("item", 20010000, 10000);
                        AddVMCount("item", 20010001, 10000);
                        AddVMCount("item", 20010002, 10000);
                    } },
                    new GMItem{ title = "充值金币100", callBack = (g, p)=> { AddVMCount("vm", 11151001, 100); } },
                    new GMItem{ title = "充值金币1000", callBack = (g, p)=> { AddVMCount("vm", 11151001, 1000); } },
                    new GMItem{ title = "充值金币1000000000", callBack = (g, p)=> { AddVMCount("vm", 11151001, 1000000000); } },
                    new GMItem{ title = "充值钻石100", callBack = (g, p)=> {AddVMCount("vm", 11151002, 100); } },
                    new GMItem{ title = "充值钻石1000", callBack = (g, p)=> { AddVMCount("vm", 11151002, 1000); } },
                    new GMItem{ title = "充值钻石1000000000", callBack = (g, p)=> { AddVMCount("vm", 11151002, 1000000000); } },
                    new GMItem{ title = "联盟银币10000000", callBack = (g, p)=> { AddVMCount("vm", 11151003, 10000000); } },
                    new GMItem{ title = "清空钻石和金币", callBack = (g, p)=> {
                        AddVMCount("vm", 11151001, -(int)GameData.I.CurrencyData.TotalGold);
                        AddVMCount("vm", 11151002, -(int)PlayerAssetsMgr.I.GetVMCount(11151002));
                    } },
                    new GMItem{ title = "增加普通宝箱10个", callBack = (g, p)=> { AddVMCount("item", 21110017 , 10); } },


                    new GMItem{ title = "pc不用激活，激活本地购买环境", callBack = (g, p)=>
                    {
                      K1D2Config.I.LocalBuyMonthCard=!K1D2Config.I.LocalBuyMonthCard;
                    }
                    },

                     }
            },
            {
                "=====领地=====",
                new List<GMItem>
                {
                     new GMItem{ title = "删除堡垒或旗帜", callBack = (g, p)=> { BuildFort(GMBuildFortType.DelFort); } },
                    new GMItem{ title = "建造堡垒(未建造)", callBack = (g, p)=> { BuildFort(GMBuildFortType.Type1); } },
                    new GMItem{ title = "建造堡垒(建造完)", callBack = (g, p)=> { BuildFort(GMBuildFortType.Type2); } },
                    new GMItem{ title = "建造旗帜(未建造", callBack = (g, p)=> { BuildFort(GMBuildFortType.Type3); } },
                    new GMItem{ title = "建造旗帜(建造完)", callBack = (g, p)=> {BuildFort(GMBuildFortType.Type4); } },
                    new GMItem{ title = "随机联盟建造堡垒(未建造)", callBack = (g, p)=> {BuildFort(GMBuildFortType.Type5);} },
                    new GMItem{ title = "随机联盟建造堡垒(建造完)", callBack = (g, p)=> {BuildFort(GMBuildFortType.Type6); } },
                    new GMItem{ title = "随机联盟建造旗帜(未建造)", callBack = (g, p)=> {BuildFort(GMBuildFortType.Type7);} },
                    new GMItem{ title = "随机联盟建造旗帜(建造完)", callBack = (g, p)=> {BuildFort(GMBuildFortType.Type8); } },
                      new GMItem{ title = "检测领地是否有效", callBack = (g, p)=> {BuildFort(GMBuildFortType.Type9); } },
                }
            },
            //{
            //    "战斗",
            //    new List<GMItem>
            //    {
            //        new GMItem() { title = "下一关", callBack = (g, p) =>
            //            {
            //                SendPlayerGmCmd("stage", (GameData.I.LevelData.CurrGameLevel + 1).ToString());
            //            } },
            //         new GMItem() { title = "上一关", callBack = (g, p) =>
            //            {
            //                SendPlayerGmCmd("stage", (GameData.I.LevelData.CurrGameLevel - 1).ToString());
            //            } },
            //        new GMItem() { title = "关卡重置", callBack = LevelReset,},

            //           new GMItem() { title = "PVP安慰奖", callBack = (x,y)=>{
            //            DeepUI.PopupManager.I.ShowDialog<UIMsgRewardBox>(new UIMsgRewardBoxData()
            //                {
            //                    content= LocalizationMgr.Get("MAIL_PVP_Fstfail_award_content"),
            //                    tittle =LocalizationMgr.Get("MAIL_PVP_Fstfail_award_title"),
            //                    rewards= new List<TypIDVal>(){
            //                        new TypIDVal(){ ID= 11151001, typ=Cfg.AssetType.Vm,val=200000},
            //                        new TypIDVal(){ ID=11151002, val=1000000, typ=Cfg.AssetType.Vm}
            //                    }
            //                });
            //           },},
            //    }
            //},

            //{
            //    "广告玩法",
            //    new List<GMItem>
            //    {
            //        new GMItem(){ title = "更换场景", callBack = (g, p)=> { SendPlayerGmCmd("adevent", "107001");  } },
            //        new GMItem(){ title = "立即跳关", callBack = (g, p)=> { SendPlayerGmCmd("adevent", "104001");} },
            //        new GMItem(){ title = "获得道具", callBack = (g, p)=> { SendPlayerGmCmd("adevent", "108001");  } },
            //        //new GMItem(){ title = "升级塔上士兵", callBack = (g, p)=> { AdEventGameMgr.I.LevelUpSolider(); } },
            //        new GMItem(){ title = "召唤金币怪", callBack = (g, p)=> {  SendPlayerGmCmd("adevent", "102001"); } },
            //        new GMItem(){ title = "召唤英雄", callBack = (g, p)=> { SendPlayerGmCmd("adevent", "103001");  } },
            //        new GMItem(){ title = "关卡加速2倍", callBack = (g, p)=> { SendPlayerGmCmd("adevent", "106001"); } },
            //        new GMItem(){ title = "关卡加速3倍", callBack = (g, p)=> {
            //            SendPlayerGmCmd("adevent", "106002");
            //        } },
            //         new GMItem(){ title = "单机加速3倍", callBack = (g, p)=> {
            //            AdEventGameMgr.I.AdEventSpeedUp(2);
            //        } },
            //         new GMItem(){ title = "单机恢复普通速度", callBack = (g, p)=> {
            //            AdEventGameMgr.I.AdEventSpeedUp(2, true);
            //        } },
            //    }
            //},

            {
                "时间",
                new List<GMItem>
                {
                    new GMItem(){ title = "增加6小时离线收益", callBack = (g, p)=> { AddLeftMoney(900 * 24); } }, //15分钟一个单位
                    new GMItem(){ title = "打印当前服务器时间", callBack = DebugServerTime},
                    new GMItem(){ title = "重置当前服务器时间", callBack = ResetServerTime},
                }
            },

            {
                "外城战斗",
                new List<GMItem>
                {
                    //new GMItem() { title = "体力恢复", callBack = RecoverPower,},
                    new GMItem() { title = "行动力恢复", callBack = RecoverAction,},
                    new GMItem() {title = "清空体力", callBack = (g, p) =>
                    {
                        SendPlayerGmCmd("clearap");
                    },},
                }
            },

            {
                "章节任务",
                new List<GMItem>
                {
                    new GMItem
                    {
                        title = "切换任务ID显隐", callBack = (g, p)=>
                        {
                            cspb.D2Quest.debugShowTaskCfgId = !cspb.D2Quest.debugShowTaskCfgId;
                            UITools.PopTips($"任务ID显隐已切换为 {cspb.D2Quest.debugShowTaskCfgId}");
                        }
                    },

                     new GMItem() {title = "直接第一章", callBack = (g, p) =>
                    {
                        SendPlayerGmCmd("firstchapter");
                        ChapterTaskGameData.I.GetChapterQuestInfo();
                    },},

                     new GMItem
                    { title = "填写任务类型ID测试前往", callBack = (g, p)=>
                        {
                            UI.UIData uiData = new UI.UIData();
                            UI.WndMgr.Show<UI.UIGMTask>("UIGMTask", uiData);
                        }
                    },
                }
            },

            {
                "昼夜效果",
                new List<GMItem>
                {
                    new GMItem() {title = "开启昼夜系统", callBack = (g, p) =>
                    {
                        //DayNightSystemLut.I.OpenDayNight();
                    },},
                    new GMItem() {title = "关闭昼夜系统", callBack = (g, p) =>
                    {
                        //DayNightSystemLut.I.CloseDayNight();
                    },}
                }
            },

            //{
            //    "科技进化",
            //    new List<GMItem>
            //    {
            //        new GMItem() {title = "播放动画", callBack = (g, p) =>
            //        {
            //            SpriteDisplayManager.I.ShowDisplayType.Add(1);
            //             SpriteDisplayManager.I.ShowDisplayType.Add(2);
            //             SpriteDisplayManager.I.ShowDisplayType.Add(3);
            //             SpriteDisplayManager.I.ShowDisplayType.Add(4);
            //            SpriteDisplayManager.I.PlayEvoUpEffect();
            //        },},

            //    }
            //},

            {
                "辅助",
                new List<GMItem>
                {
                    new GMItem
                    { title = "清除缓存", callBack = (g, p)=>
                        {
                            PlayerPrefs.DeleteAll();
                        }
                    },




                    //new GMItem
                    //{ title = "打开模糊界面", callBack = (g, p)=>
                    //    {
                    //         PopupManager.I.ShowPanel<UIGetRewardList>(new UIGetRewardListData() {});
                    //    }
                    //},


                }
            },

            {
                "科技",
                new List<GMItem>
                {

                    new GMItem
                    { title = "科技全满", callBack = (g, p)=>
                        {
                            SendPlayerGmCmd("getTech");
                        }
                    },

                    new GMItem
                    { title = "全科技升1级", callBack = (g, p)=>
                        {
                            SendPlayerGmCmd("techLvlAll", "1");
                        }
                    },

                    new GMItem
                    { title = "全科技升10级", callBack = (g, p)=>
                        {
                            SendPlayerGmCmd("techLvlAll", "10");
                        }
                    },


                }
            },
            {
                "日常任务",
                new List<GMItem>
                {
                    new GMItem{ title = "完成所有任务", callBack = (g, p)=> { FinishTask(); } },
                    new GMItem{ title = "刷新一个任务", callBack = (g, p)=> { RefreshTask(); } },
                    new GMItem{ title = "任务跨天", callBack = (g, p)=> { TaskDay(); } },
                }
            },
            {
                "联盟总动员",
                new List<GMItem>
                {
                    new GMItem{ title = "完成所有任务", callBack = (g, p)=> { SendPlayerGmCmd("finishallmobilizequest");} },
                    new GMItem{ title = "刷新所有任务", callBack = (g, p)=>
                    {
                        SendPlayerGmCmd("resetmobilizequest");
                        TaskMgr.I.QuestInfoReq();
                    } },
                    new GMItem{ title = "总动员一键结算", callBack = (g, p)=> { SendPlayerGmCmd("mobilizesettlement");} },
                    new GMItem{ title = "强改自己积分1000", callBack = (g, p)=> { SendPlayerGmCmd("updateplayerscore", "1000");} },
                    new GMItem{ title = "新加入联盟刷新", callBack = (g, p)=> {
                        TaskMgr.I.QuestInfoReq();
                        SendPlayerGmCmd("resetUnionJoinTime");}
                    },

                    new GMItem{ title = "排行榜手动请求刷新", callBack = (g, p)=> { RankNewMgr.I.ReqRankData(); } },

                     new GMItem
                     {
                        title = "打开新联盟总动员！", callBack = (g, p)=>
                        {
                            AllianceMobilizeMgr.I.OpenAllianceAction();
                            Close();
                        }
                    },
                }
            },
            {
                "测试",
                new List<GMItem>
                {
                    new GMItem
                    { title = "Android 重启 App", callBack = (g, p)=>
                        {
                            Main.RestartAndroid();
                        }
                    },
                    //new GMItem
                    //{ title = "Android打印包SHA1签名", callBack = (g, p)=>
                    //    {
                    //        var allKeyHash = T4fBaseClass.AppUtils.GetAllAppSignSha1();
                    //        D.Debug?.Log($"Android打印包SHA1签名'{allKeyHash}'");
                    //        FloatTips.I.FloatMsg(LocalizationMgr.Get(allKeyHash.Replace(';', '\n')));
                    //    }
                    //},
                    new GMItem
                    { title = "测试引导相关信息", callBack = (g, p)=>
                        {
                            var list = Cfg.C.CD2Guide.Is();
                            for (int i = 0; i < list.Count; i++)
                            {
                                D.Debug?.Log(" i " + i + "  id " + list[i].Id);
                            }
                        }
                    },
                      new GMItem
                    { title = "重置技能CD", callBack = (g, p)=>
                        {
                            //UIMain uIMain = UIMain.I;// WndMgr.Get<UIMain>("UIMain");
                            //if (uIMain !=null && uIMain.IsShow)
                            //    {
                            //    uIMain.ResetSkillCD();
                            //    }

                            //主界面重置技能CD
                            EventMgr.FireEvent(TEventType.UIMain_Update_ResetHeroSkillCD);
                        }
                    },

                    new GMItem
                    { title = "打开邮件", callBack = (g, p)=>
                        {
                            PopupManager.I.ShowLayer<UI.Mail.UImail>("UIMail");
                        }
                    },

                    new GMItem
                    { title = "打开迁城通用面板", callBack = async (g, p)=>
                        {
                            var data = new UIMsgPopData()
                            {
                                Title = "111111111",
                                 Content = "22222222",
                                 BtnData = await VMData.CreateAsync(11151001, 100),
                                 BtnClickCallBack = () => {Debug.Log("迁城确认，GOGOGO！"); },
                            };
                            PopupManager.I.ShowPanel<UIMsgPop>("UIMsgPop", data);
                        }
                    },
                    new GMItem
                    { title = "测试打开跑马灯", callBack = ShowMarquee},
                    new GMItem
                    { title = "测试全服广播类型8", callBack = (g, p) =>
                    {
                        BroadcastGameNtf data = new BroadcastGameNtf()
                        {
                             cgfId = 108001,
                        };
                        data.contexts.Add("玩家AAA");
                        MarqueeMgr.I.OnShowBroadCast(data);
                     }
                    },

                     new GMItem
                    { title = "打开另一个dialog", callBack = (g, p) =>
                    {
                       var title = LocalizationMgr.Get("Rally_Function_Title2");
                        var content = LocalizationMgr.Get("Rally_Function_Kick_Tips");// "确认踢出队伍吗?";// LocalizationMgr.Format("Buying_Confirm_Tips_Content", costNum);
                        UITools.OpenConfirmPop(() =>
                        {

                            }, null, title, content);
                     }
                    },
                    new GMItem
                    {
                        title = "打开city保护罩buff界面",callBack = (g, p)=>{ PopupManager.I.ShowPanel<UIPlayerCityBuff>("UIProtectMain"); }
                    },
                    new GMItem
                    {
                        title = "打开被攻击提醒界面",callBack = (g, p)=>{ PopupManager.I.ShowPanel<UIAttackAlert>("UIAttackAlert"); }
                    },
                    new GMItem
                    {
                        title = "测试宝箱界面", callBack = ShowReward
                    },
                    new GMItem
                    {
                        title = "测试飞道具", callBack = FlyPanelCoin
                    },


                    new GMItem
                    {
                        title = "谷歌商店账号绑定测试", callBack = GooglePlayLogin
                    },

                    new GMItem
                    {
                        title = "Google Plus V3 解除绑定", callBack = UnlinkGooglePlusV3
                    },

                    new GMItem
                    {
                        title = "Facebook 解除绑定", callBack = UnlinkFacebook
                    },
                    new GMItem
                    {
                        title = "Email 解除绑定", callBack = UnlinkEmail
                    },
                    new GMItem
                    {
                        title = "VK 解除绑定", callBack = UnlinkVK
                    },
                    new GMItem
                    {
                        title = "跳转商城背包", callBack = (g, p) => { ShopMgr.I.OpenShopPanel(ShopJumpMark.bag); }
                    },

                    //new GMItem
                    //{
                    //    title = "打开商城滚动到广告商城", callBack = (g, p) => { ShopMgr.I.OpenShopPanel(ShopTagEnum.AdShop, true); }
                    //},

                    //new GMItem
                    //{
                    //    title = "获取当前未读客诉信息", callBack = (g, p) => { AIhelpService.Instance.SetUnreadMessageFetchUid(); }
                    //},

                    new GMItem
                    {
                        title = "打开联盟战区域显示界面", callBack = (g, p) => { WndMgr.Show<UIAllianceWarArea>(); }
                    },

                    new GMItem
                    {
                        title = "打开联盟建筑显示界面", callBack = (g, p) => { PopupManager.I.ShowPanel<UIAllianceWarBuilding>(); }
                    },

                    //  new GMItem
                    //{
                    //    title = "打开登陆失败弹出枚举界面", callBack = (g, p) => {

                    //        var loginError= (LoginFailEnum)UnityEngine.Random.Range(1,9);
                    //        D.Warning?.Log(loginError.ToString());
                    //        LoginMgr.ShowLogfailWindow((_) =>
                    //    {

                    //    },
                    //    null,
                    //   loginError
                    //    );
                    //    }
                    //},
                    

    }
            },
        

            //{
            //    "付费",
            //    new List<GMItem>
            //    {
            //        new GMItem(){ title = "增加50付费金额", callBack = (g, p)=> { AddPayTotal(50); } },
            //        new GMItem(){ title = "增加100付费金额", callBack = (g, p)=> { AddPayTotal(100); } },
            //        new GMItem(){ title = "增加1000付费金额", callBack = (g, p)=> { AddPayTotal(1000); } },
            //        new GMItem(){ title = "增加10000付费金额", callBack = (g, p)=> { AddPayTotal(10000); } },
            //        new GMItem(){ title = "增加100000付费金额", callBack = (g, p)=> { AddPayTotal(100000); } },
            //    }
            //},

            {
                "消息推送",
                new List<GMItem>
                {
                    new GMItem(){ title = "1分钟之后显示推送", callBack = (g, p)=> { TestLocalMessagePushFromTime(1); } },
                }
            },
            //{
            //    "客服消息测试",
            //    new List<GMItem>
            //    {
            //        new GMItem
            //        { title = "加消息", callBack = (g, p)=>
            //            {
            //               AIhelpService.Instance._unReadMessageCount += 1;
            //            }
            //        },
            //        new GMItem
            //        { title = "减消息", callBack = (g, p)=>
            //            {
            //               AIhelpService.Instance._unReadMessageCount -= 1;
            //            }
            //        },
            //    }
            //},

            {
                "商店测试",
                new List<GMItem>
                {
#if UNITY_ANDROID
                    new GMItem
                    { title = "应用内评价", callBack = (g, p)=>
                        {
                           TestInappReview();
                        }
                    },

                    new GMItem
                    { title = "游戏内评价", callBack = (g, p)=>
                        {
                           GradeMgr.I.ShowAppStoreJump();
                        }
                    },

#endif

                    new GMItem
                    { title = "客户端本地替换皮肤(万圣节)", callBack = (g, p)=>
                        {
                            ShopSkinMgr.I.SetInternerSkin(1051);
                        }
                    },
                    new GMItem
                    { title = "客户端本地替换皮肤(圣诞节)", callBack = (g, p)=>
                        {
                            ShopSkinMgr.I.SetInternerSkin(1071);
                        }
                    },
                    new GMItem
                    { title = "客户端本地替换皮肤(沙漠)", callBack = (g, p)=>
                        {
                            ShopSkinMgr.I.SetInternerSkin(1061);
                        }
                    },
                }
            },

            {
                "开始弹窗",new List<GMItem>
                {
                    new GMItem
                    { title = "删除开始弹窗所有缓存", callBack = (g, p)=>
                        {
                            //清理所有缓存数据信息
                            int today = new DateTime(GameTime.Tricks).DayOfYear;
                            GamePlayerPrefs.Set(AutoOpenMgr.I.loginDay, today - 1);

                            GamePlayerPrefs.Set(AutoOpenMgr.I.popTimes, 0);

                            GamePlayerPrefs.Set(AutoOpenMgr.I.hasClickFirstCharge, false);
                        }
                    },
                }
            },

#if UNITY_EDITOR

            {
                "性能分析",
                new List<GMItem>
                {
                    new GMItem
                    { title = "恢复默认相机", callBack = (g, p)=>
                        {
                            var cameras = Camera.allCameras;
                            for (int i = 0; i < cameras.Length; i++)
                            {
                                if(!cameras[i].CompareTag("UICamera"))
                                    cameras[i].SetReplacementShader(null, "");
                            }

                        }
                    },
                    new GMItem
                    { title = "查看 Overdraw", callBack = (g, p)=>
                        {
                            var overdrawShader = (UnityEditor.EditorGUIUtility.LoadRequired("SceneView/SceneViewShowOverdraw.shader") as Shader);
                            //Camera.main.SetReplacementShader(s_ShowOverdrawShader, "");
                            var cameras = Camera.allCameras;
                            for (int i = 0; i < cameras.Length; i++)
                            {
                                if(!cameras[i].CompareTag("UICamera"))
                                    cameras[i].SetReplacementShader(overdrawShader, "");
                            }
                        }
                    },
                    new GMItem
                    { title = "查看 Mipmap", callBack = (g, p)=>
                        {
                            var mipShader = (UnityEditor.EditorGUIUtility.LoadRequired("SceneView/SceneViewShowMips.shader") as Shader);
                            //Camera.main.SetReplacementShader(mipShader, "");

                            var cameras = Camera.allCameras;
                            for (int i = 0; i < cameras.Length; i++)
                            {
                                if(!cameras[i].CompareTag("UICamera"))
                                    cameras[i].SetReplacementShader(mipShader, "");
                            }
                        }
                    },
                }
            },
#endif


            //！！！！注意！！！别写在最后包括在Editor里，会导致包里没有哦

        };

        private static void AuthorizationTrackingReceived(int status)
        {
            UITools.PopTips($"Tracking status received: {status}");
            Debug.LogFormat("Tracking status received: {0}", status);
        }

        public GMGameData()
        {
            MessageMgr.RegisterMsg<GMAck>("GMEditor_GMAck", OnGM);
        }

        /// <summary>
        /// 回调
        /// </summary>
        /// <param name="gMAck"></param>
        private void OnGM(GMAck gMAck)
        {
            D.Debug?.Log("GMAck strRet={0},error={1}", gMAck.strRet, gMAck.errMsg);
            if (string.IsNullOrEmpty(gMAck.errMsg))
            {
                var str = gMAck.strRet;
                // if (str.Length > 40)
                // {
                //     str = str.Substring(0, 10);
                // }
                UITools.EnqueueTips(string.Format("发送成功" + str));
                //FloatTips.I.FloatMsg(string.Format("发送成功" + str));
            }
            else
            {
                if (K1D2Config.I.ShowErrorCode)
                    FloatTips.I.FloatMsg(gMAck.errMsg);
            }

            if (!string.IsNullOrEmpty(gMAck.strRet) && gMAck.strRet.Length > 0 && gMAck.strRet.Contains("SelfIndex"))
            {
            }

            if (isTestRandom)
            {
                isTestRandom = false;
                var strArr = gMAck.strRet.Split(',');
                if (strArr != null && strArr.Length == 2)
                {
                    //GMAck strRet = 3654661,5890435,error =
                    var x = Game.Utils.ConvertUtils.GetIntFromString(strArr[0]) / 1000;
                    var z = Game.Utils.ConvertUtils.GetIntFromString(strArr[1]) / 1000;
                    var pos = new Vector3(x, 0, z);
                    if (LViewportJump.IsPosVaild(pos))
                    {
                        RMap.JumpIntoTargetPosition(pos.x, pos.z, null);
                    }
                    //Game.Utils.ConvertUtils.
                }
            }

            //if (isHundredStage)
            //{
            //    isHundredStage = false;
            //    if (LSwitchMgr.I.IsFunctionOpen(SwitchConfig.Tower_New_Switch))
            //    {
            //        AdventureInfoV2Req newMessage = new AdventureInfoV2Req();
            //        MessageMgr.Send(newMessage);
            //    }
            //    else
            //    {
            //        //进入游戏后向服务器索要当前的存储信息
            //        AdventureInfoReq message = new AdventureInfoReq();
            //        MessageMgr.Send(message);
            //    }
            //}
        }

        private static bool isTestRandom;

        private static void TestRandom(GameObject arg0, PointerEventData arg1)
        {
            isTestRandom = true;
            SendPlayerGmCmd("testrandom");
        }

        //private static bool isTestPos;
        //public static void TestPos(Vector2Int pos)
        //{
        //    isTestPos = true;
        //    SendPlayerGmCmdNoPlayerId("centerManorRangeInfo", (pos.x * 1000).ToString(), (pos.y * 1000).ToString());
        //}

        /// <summary>
        /// 恢复行动力
        /// </summary>
        /// <param name="go"></param>
        /// <param name="data"></param>
        private static void RecoverAction(GameObject go, PointerEventData data)
        {
            //RecoverCfg 
            //11171001    15110051    { "typ":"lc","txt":"LC_PLAYER_stamina_name"}
            //11171002    15112383    { "typ":"lc","txt":"LC_PLAYER_action_point"}
            //11171003    15112261    { "typ":"lc","txt":"LC_PLAYER_energy_point"}

            SendPlayerGmCmd("addasset", "recover", "11171002", "200");
        }

        /// <summary>
        /// 恢复体力
        /// </summary>
        /// <param name="go"></param>
        /// <param name="data"></param>
        private static void RecoverPower(GameObject go, PointerEventData data)
        {
            SendPlayerGmCmd("addasset", "recover", "11171001", "1000");
        }

        /// <summary>
        /// 发送Gm消息的通用函数，包含玩家id
        /// </summary>
        /// <param name="args"></param>
        public static void SendPlayerGmCmd(string cmd, params string[] param)
        {
            var req = new GMReq();
            req.args.Add(cmd);
            req.args.Add(LPlayer.I.PlayerID.ToString());
            foreach (string item in param)
            {
                req.args.Add(item);
            }
            MessageMgr.Send(req);
            //UITools.PopTips("发送成功！！！");
        }

        /// <summary>
        /// 发送Gm消息的通用函数，包含玩家id
        /// </summary>
        /// <param name="args"></param>
        public static void SendPlayerGmCmdNoPlayerId(string cmd, params string[] param)
        {
            var req = new GMReq();
            req.args.Add(cmd);
            foreach (string item in param)
            {
                req.args.Add(item);
            }
            MessageMgr.Send(req);
            //UITools.PopTips("发送成功！！！");
        }

        public static void SendPlayerGmCmdNoPlayerId(string input)
        {
            var temp1 = Regex.Split(input, " ", RegexOptions.IgnoreCase).ToList();
            var cmd = temp1[0];
            temp1.RemoveAt(0);
            var parmas = temp1.ToArray();
            SendPlayerGmCmdNoPlayerId(cmd, parmas);
        }

        private static bool isHundredStage;

        /// <summary>
        /// 增加百关
        /// </summary>
        /// <param name="go"></param>
        /// <param name="data"></param>
        private static void HundredStage(GameObject go, PointerEventData data)
        {
            //SendPlayerGmCmd("stage", (GameData.I.LevelData.CurrGameLevel + 100).ToString());
            //isHundredStage = true;
            //MessageMgr.RegisterMsg<GMAck>("GMEditor_GMAck", ack =>
            //{
            //    MessageMgr.UnregisterMsg<GMAck>("GMEditor_GMAck");
            //    MessageMgr.Send(new AdventureInfoReq());
            //});
        }
        /// <summary>
        /// 增加10000关
        /// </summary>
        /// <param name="go"></param>
        /// <param name="data"></param>
        private static void HundredTenThousandStage(GameObject go, PointerEventData data)
        {
            //SendPlayerGmCmd("stage", (GameData.I.LevelData.CurrGameLevel + 10000).ToString());
            //isHundredStage = true;
            //MessageMgr.RegisterMsg<GMAck>("GMEditor_GMAck", ack =>
            //{
            //    MessageMgr.UnregisterMsg<GMAck>("GMEditor_GMAck");
            //    MessageMgr.Send(new AdventureInfoReq());
            //});
        }

        /// <summary>
        /// 充值
        /// </summary>
        public static void AddVMCount(string type, int id, int count)
        {
            SendPlayerGmCmd("addasset", type, id.ToString(), count.ToString());
        }

        /// <summary>
        /// 增加离线收益
        /// </summary>
        /// <param name="count"></param>
        private static void AddLeftMoney(int count)
        {
            SendPlayerGmCmd("offoutput", count.ToString());
        }


        #region 日常任务

        /// <summary>
        /// 任务跨天
        /// </summary>
        private static void TaskDay()
        {

            SendPlayerGmCmd("movenextday");
            TaskMgr.I.QuestInfoReq();
            //SendPlayerGmCmd("nextDayQuest");
        }

        /// <summary>
        /// 刷新任务
        /// </summary>
        /// <param name="count"></param>
        private static void RefreshTask()
        {
            SendPlayerGmCmd("createQuest");
        }


        /// <summary>
        /// 完成任务
        /// </summary>
        /// <param name="count"></param>
        private static void FinishTask()
        {
            SendPlayerGmCmd("finishalldaily");
            TaskMgr.I.QuestInfoReq();
            //SendPlayerGmCmd("finishQuest");
        }

        #endregion

        /// <summary>
        /// 关卡重置
        /// </summary>
        private static void LevelReset(GameObject go, PointerEventData data)
        {
            //if (LSwitchMgr.I.IsFunctionOpen(SwitchConfig.Tower_New_Switch))
            //{
            //    AdventureInfoV2Req newMessage = new AdventureInfoV2Req();
            //    MessageMgr.Send(newMessage);
            //}
            //else
            //{
            //进入游戏后向服务器索要当前的存储信息
            AdventureInfoReq message = new AdventureInfoReq();
            MessageMgr.Send(message);
            //}

        }

        private static bool testFly = true;

        /// <summary>
        /// 测试离线奖励飞金币
        /// </summary>
        private static void OfflineFlyCoin(int coinCount, int diamondCount)
        {
            if (testFly)
                EventMgr.FireEvent(TEventType.UIMain_FlyVmItemEffect, GameCurrencyEnum.CURRENCY_GOLD, coinCount);
            //UIMain.I?.FlyVmEffect(GameCurrencyEnum.CURRENCY_GOLD, coinCount);
            else
                EventMgr.FireEvent(TEventType.UIMain_FlyVmItemEffect, GameCurrencyEnum.CURRENCY_DIAMOND, coinCount);
            //UIMain.I?.FlyVmEffect(GameCurrencyEnum.CURRENCY_DIAMOND, diamondCount);
            testFly = !testFly;
            PopupManager.I.ClosePopup<UIGmWindow>();
            //var data1 = new UIOfflineRevenueData()
            //{
            //    title = LocalizationMgr.Get("Idle_Income_Title"),
            //    desc = LocalizationMgr.Get("Idle_Income_Desc"),
            //    btnDesc = LocalizationMgr.Get("DEMO_38"),
            //    clickCallBack = () => { UIMain.I.FlyCoin(); PopupManager.I.ClosePopup<UIGmWindow>(); },
            //    count = "111111111111",
            //};
            ////显示界面
            //PopupManager.I.ShowPanel<UIOfflineRevenue>("UIOfflineRevenue", data1);
        }

        /// <summary>
        /// 测试跑马灯
        /// </summary>
        private static void ShowMarquee(GameObject go, PointerEventData data)
        {
            MarqueeMgr.I.ShowMarquee(true);
        }

        /// <summary>
        /// 测试抽卡界面
        /// </summary>
        private static void ShowReward(GameObject go, PointerEventData data)
        {
            var anchor = GameData.I.MainData.MapFlyStartAnchor;
            EventMgr.FireEvent(TEventType.UIMain_FlyVmItemEffect_Pos,
                GameCurrencyEnum.CURRENCY_DIAMOND, 100, anchor.transform.position);
            PopupManager.I.ClosePopup<UIGmWindow>();
        }

        /// <summary>
        /// 测试飞资源条
        /// </summary>
        private static void FlyPanelCoin(GameObject go, PointerEventData data)
        {
            var anchor = GameData.I.MainData.MapFlyStartAnchor;
            if (anchor != null)
            {
                var rewards = new List<TypIDVal>()
            {
                new TypIDVal(){ ID = 20010001, typ = "item", val = 216},
                new TypIDVal(){ ID = 20010002, typ = "item", val = 216},
                new TypIDVal(){ ID = 20110013, typ = "item", val = 2},
            };
                UIHelper.FlyItems(rewards, anchor.transform.position, true);
            }
            PopupManager.I.ClosePopup<UIGmWindow>();
        }

        #region 服务器时间
        /// <summary>
        /// 获取当前服务器时间
        /// </summary>
        private static void DebugServerTime(GameObject go, PointerEventData data)
        {
            SendPlayerGmCmd("timeget");
        }

        /// <summary>
        /// 重置当前服务器时间
        /// </summary>
        private static void ResetServerTime(GameObject go, PointerEventData data)
        {
            SendPlayerGmCmd("timereset");
        }
        #endregion



        #region 账号绑定，解除绑定设置

        /// <summary>
        /// 谷歌商店账号登陆测试
        /// </summary>
        private static void GooglePlayLogin(GameObject go, PointerEventData data)
        {
            //调用谷歌账号进行登陆
            //GoogleSignInSdkMgr.Instance.Login();
        }

        /// <summary>
        /// 解除GooglePlusV3 账号绑定测试
        /// </summary>
        private static void UnlinkGooglePlusV3(GameObject go, PointerEventData data)
        {
            //调用谷歌账号进行登陆
            //GoogleSignInSdkMgr.Instance.Login();

            LAccount.I.AccountUnlinkBind(SDKLoginType.GooglePlusV3);
        }


        /// <summary>
        /// 解除Facebook 账号绑定测试
        /// </summary>
        private static void UnlinkFacebook(GameObject go, PointerEventData data)
        {
            //调用谷歌账号进行登陆
            //GoogleSignInSdkMgr.Instance.Login();

            LAccount.I.AccountUnlinkBind(SDKLoginType.Facebook);
        }

        /// <summary>
        /// 解除邮箱绑定
        /// </summary>
        /// <param name="go"></param>
        /// <param name="data"></param>
        private static void UnlinkEmail(GameObject go, PointerEventData data)
        {
            LAccount.I.AccountUnlinkBind(SDKLoginType.Email);
        }
        /// <summary>
        /// 解除邮箱绑定
        /// </summary>
        /// <param name="go"></param>
        /// <param name="data"></param>
        private static void UnlinkVK(GameObject go, PointerEventData data)
        {

            LAccount.I.AccountUnlinkBind(SDKLoginType.VK);

        }
        #endregion


        #region 付费金额设置

        /// <summary>
        /// 添加付费金额
        /// </summary>
        /// <param name="count"></param>
        private static void AddPayTotal(int count)
        {
            SendPlayerGmCmd("cmdPayTotal", count.ToString());
        }

        #endregion


        #region 本地消息推送

        /// <summary>
        /// 1分钟之后显示推送信息
        /// </summary>
        /// <param name="time">延后多长时间显示（分钟）</param>
        private static void TestLocalMessagePushFromTime(float minutes)
        {
            var time = DateTime.Now.AddMinutes(minutes);
            D.Debug?.Log(minutes + " 分钟之后显示推送信息 时间" + time.ToString("yy-MM-dd HH:mm:ss"));
            GameNotifyMgr.I.ScheduleGameNotify(1001, "Test Push", "One Monute Later Show", time);
        }


        #endregion

#if UNITY_ANDROID

        #region 应用内评价

        /// <summary>
        /// 测试应用内评价
        /// </summary>
        private static void TestInappReview()
        {
            //var reviewManager = new ReviewManager();
            //CoroutineMgr.StartCoroutine(TestReview(reviewManager));
        }

        //private static IEnumerator TestReview(ReviewManager review)
        //{
        //    var requestFlowOperation = review.RequestReviewFlow();
        //    yield return requestFlowOperation;
        //    if (requestFlowOperation.Error != ReviewErrorCode.NoError)
        //    {
        //        D.Warning?.Log(" Review Request Error " + requestFlowOperation.Error);
        //        yield break;
        //    }

        //    var playReviewInfo = requestFlowOperation.GetResult();
        //    var launchFlowOperation = review.LaunchReviewFlow(playReviewInfo);
        //    yield return launchFlowOperation;
        //    playReviewInfo = null;
        //    if (launchFlowOperation.Error != ReviewErrorCode.NoError)
        //    {
        //        D.Warning?.Log(" Review Launch Error " + launchFlowOperation.Error);
        //        yield break;
        //    }

        //    D.Warning?.Log(" Review Done ");
        //}

        #endregion

#endif

        #region 数据清理

        /// <summary>
        /// 数据清理
        /// </summary>
        public void Dispose()
        {
            isHundredStage = false;
        }

        #endregion

        private static void Close()
        {
            PopupManager.I.ClosePopup<UIGmWindow>();
        }

        public class GMItem
        {
            public string title;
            public UnityAction<GameObject, PointerEventData> callBack;
        }

    }

}

#endif
