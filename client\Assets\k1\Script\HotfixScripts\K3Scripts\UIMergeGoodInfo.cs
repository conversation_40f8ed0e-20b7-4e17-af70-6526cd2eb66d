﻿using System.Collections;
using System.Collections.Generic;
using Cfg;
using Common;
using DeepUI;
using Game.Data;
using TFW;
using TFW.Localization;
using TFW.UI;
using Logic;
using UI;
using UnityEngine;

namespace K3
{
    public class UIMergeGoodInfoData : UIData
    {
        public ItemInfo Good;
        public bool OpenSourceBtn;
        public UIMergeGoodInfoData(ItemInfo good, bool _OpenSourceBtn = true)
        {
            Good = good;
            OpenSourceBtn = _OpenSourceBtn;
        }


    }

    [Popup("UIMergeGoodInfo", true, true)]
    public class UIMergeGoodInfo : BasePopup
    {
        [PopupField("Root/CloseButton")] private GameObject btnClose;

        [PopupField("Root/WhereBtn")] private GameObject btnWhere;

        [PopupField("Root/boxIcon")] private TFWImage boxImage;

        [PopupField("Root/PageBG/PageDes")] private TFWText PageDes;

        [<PERSON>upField("Root/BtnLeft")] private GameObject BtnLeft;

        [PopupField("Root/BtnRight")] private GameObject BtnRight;

        [PopupField("Root/ScrollView/ViewPort/Content/line")]
        private GameObject linePre;

        [PopupField("Root/title/BG/Text")]
        private TFWText nameText;

        [PopupField("Root/cur")] private Animator curGoodAni;

        private UIMergeGoodInfoData mData;

        private int curCode = -1;
        private int curType = -1;
        private List<GameObject> lineList = new List<GameObject>();
        private List<UIMergeGoodInfo_infopanel> goodinfoList;
        private ItemInfo _curInfo;
        private ItemInfo curInfo
        {

            get
            {
                return _curInfo;
            }
            set
            {
                _curInfo = value;
            }
        }
        protected override void OnInit()
        {
           

            BindClickListener(btnClose, (x, y) =>
            {
                Close();
                curGoodAni.gameObject.SetActive(false);
            });
            BindClickListener(btnWhere, (x, y) =>
            {
                ShowFromPanel();
            });

            BindClickListener(BtnLeft, (x, y) =>
            {
                ShowLeft();
            });

            BindClickListener(BtnRight, (x, y) =>
            {
                ShowRight();
            });
        }

        #region 数据刷新显示

        int OnePageItemCount;

        int PageNow;
        int PageMax;

        /// <summary>
        /// 当界面第一次打开时调用
        /// </summary>
        protected internal override void OnOpenStart()
        {
            base.OnOpenStart();
            OnShowStart();
        }

        /// <summary>
        /// 界面每次显示时调用
        /// </summary>
        protected internal override void OnShowStart()
        {
            OnePageItemCount = 16;
            base.OnShowStart();
            mData = Data as UIMergeGoodInfoData;
            int code = mData.Good.Code;
            int type = mData.Good.Type;

            //var uimerge = DeepUI.PopupManager.I.FindPopup<UIMerge_Maze>();
            //if (uimerge != null)
            //{
            //    curInfo = CSVItem.GetItemInfoById_Maze(mData.Good.id);
            //}
            //else
            {
                curInfo = CSVItem.GetItemInfoById(mData.Good.id);
            }

            btnWhere.SetActive(mData.OpenSourceBtn && curInfo.ItemSource.Count > 0);
            nameText.text = mData.Good.Name;

            var csBoxs = Cfg.C.CCsBox.RawList();
            foreach (var item in csBoxs)
            {
                if (item.CreateType.Contains(curInfo.Code.ToString()))
                {
                    boxID = item.Id; break;
                }
            }

            var boxCfg = Cfg.C.CCsBox.I(boxID);


            UITools.SetImageBySpriteName(boxImage, UITools.GetAttributeDisplayKey(boxCfg.Id - 1), true);

            if (curCode != code || curType != type)
            {
                ShowItemIist(code, type);
            }
            else
            {
                for (int i = 0; i < goodinfoList.Count; i++)
                {
                    if (goodinfoList[i].UpdateIcon(mData.Good.id))
                    {
                        CoroutineMgr.StartCoroutine(SetCur(goodinfoList[i].transform));
                    }
                }
            }
        }

        void ShowLeft()
        {
            curGoodAni.gameObject.SetActive(false);
            PageNow = Mathf.Max(1, PageNow - 1);
            ShowItemIist(curCode, curType, PageNow);
        }

        void ShowRight()
        {
            curGoodAni.gameObject.SetActive(false);
            PageNow = Mathf.Min(PageMax, PageNow + 1);
            ShowItemIist(curCode, curType, PageNow);
        }


        int boxID;

        void ShowItemIist(int code, int type, int page = 0)
        {


            //var uimerge = DeepUI.PopupManager.I.FindPopup<UIMerge_Maze>();

            goodinfoList = new List<UIMergeGoodInfo_infopanel>();
            for (int i = 0; i < lineList.Count; i++)
            {
                lineList[i].SetActive(false);
            }

            curCode = code;
            curType = type;
            linePre.SetActive(false);
            List<ItemInfo> allInfo = null;
            //if (uimerge != null)
            //{
            //    allInfo = CSVItem.GetItemInfosByCodeAndType_Maze(curCode, curType);
            //}
            //else
            {
                allInfo = CSVItem.GetItemInfosByCodeAndType(curCode, curType);
            }



            int index = 0;
            GameObject line = null;
            PageMax = allInfo.Count / OnePageItemCount;
            if (allInfo.Count % OnePageItemCount != 0)
            {
                PageMax++;
            }
            if (page == 0)
            {
                int x = 0;
                foreach (var item in allInfo)
                {
                    x++;
                    if (item.id == curInfo.id)
                    {

                        break;
                    }
                }


                PageNow = x / OnePageItemCount;
                if (x % OnePageItemCount > 0)
                {
                    PageNow++;
                }
            }
            else
            {
                PageNow = page;
            }
            BtnLeft.gameObject.SetActive(true);
            BtnRight.gameObject.SetActive(true);
            if (PageNow == 1)
            {
                BtnLeft.gameObject.SetActive(false);
            }
            if (PageNow == PageMax)
            {
                BtnRight.gameObject.SetActive(false);
            }

            PageDes.text = string.Format("{0}/{1}", PageNow, PageMax);

            List<ItemInfo> new_list = new List<ItemInfo>();

            for (int i = 0; i < OnePageItemCount; i++)
            {
                int _index = OnePageItemCount * (PageNow - 1) + i;
                if (allInfo.Count >= _index + 1)
                {
                    new_list.Add(allInfo[_index]);
                }

            }

            for (int i = 0; i < new_list.Count; i++)
            {

                if (index == 0)
                {
                    line = GetLine();
                    line.SetActive(true);
                    for (int j = 1; j < line.transform.childCount; j++)
                    {
                        line.transform.GetChild(j).gameObject.SetActive(false);
                    }

                    line.transform.SetAsLastSibling();
                    Transform tr = line.transform.GetChild(index * 2);
                    UIMergeGoodInfo_infopanel goodInfo = tr.gameObject.GetComponent<UIMergeGoodInfo_infopanel>();
                    if (goodInfo == null)
                    {
                        goodInfo = tr.gameObject.AddComponent<UIMergeGoodInfo_infopanel>();
                        goodInfo.Init(this);
                    }

                    goodInfo.InitData(new_list[i]);
                    goodInfo.gameObject.SetActive(true);
                    goodinfoList.Add(goodInfo);
                    if (new_list[i].id == mData.Good.id)
                    {
                        CoroutineMgr.StartCoroutine(SetCur(tr));
                    }
                }
                else if (index <= 3)
                {
                    line.transform.GetChild(index * 2).gameObject.SetActive(true);
                    line.transform.GetChild(index * 2 - 1).gameObject.SetActive(true);
                    Transform tr = line.transform.GetChild(index * 2);
                    UIMergeGoodInfo_infopanel goodInfo = tr.gameObject.GetComponent<UIMergeGoodInfo_infopanel>();
                    if (goodInfo == null)
                    {
                        goodInfo = tr.gameObject.AddComponent<UIMergeGoodInfo_infopanel>();
                        goodInfo.Init(this);
                    }

                    goodInfo.InitData(new_list[i]);
                    goodInfo.gameObject.SetActive(true);
                    goodinfoList.Add(goodInfo);
                    if (new_list[i].id == mData.Good.id)
                    {
                        CoroutineMgr.StartCoroutine(SetCur(tr));
                    }
                }

                index++;
                if (index == 4)
                {
                    index = 0;
                }
            }
        }

        IEnumerator SetCur(Transform target)
        {

            yield return new WaitForSeconds(0.1f);
            curGoodAni.gameObject.SetActive(true);
            curGoodAni.SetTrigger("show");
            if (target != null)
            {
                curGoodAni.transform.position = target.position;
                curGoodAni.transform.SetParent(target);
            }
        }

        private GameObject GetLine()
        {
            GameObject lineObj = null;
            for (int i = 0; i < lineList.Count; i++)
            {
                if (!lineList[i].activeSelf)
                {
                    lineObj = lineList[i];
                    break;
                }
            }

            if (lineObj == null)
            {
                lineObj = GameObject.Instantiate(linePre, linePre.transform.parent);
                lineList.Add(lineObj);
            }

            return lineObj;
        }

        #endregion

        #region 数据刷帧处理

        /// <summary>
        /// 刷帧处理
        /// </summary>
        /// <param name="deltaTime"></param>
        protected internal override void OnUpdate(float deltaTime)
        {
            base.OnUpdate(deltaTime);
        }

        #endregion

        void ShowFromPanel()
        {
            //跳转指引箱子

            Close();

            PopupManager.I.ClosePopup<UIMergeStorehouse>();

            var guidData = new UIGuidData();

            var uimerge = PopupManager.I.FindPopup<UIMerge>();
            if (uimerge != null)
            {
                guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMerge", UIItem = $"Root/bottomBtns/boxes/SpecialBox{boxID + 9}", slide = true });
                UIGuid.StartGuid(guidData);
                return;//已经打开
            }

            PopupManager.I.ClearAllPopup();

            //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.PRISON);


            guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMain2", UIItem = "content/MenuRoot/Battle", slide = true });
            guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMerge", UIItem = $"Root/bottomBtns/boxes/SpecialBox{boxID + 9}", slide = true });
            UIGuid.StartGuid(guidData);


            //var showDataList = new List<ComplementPopData>();
            //ItemInfo info = CSVItem.GetItemInfoById(curInfo.id);
            //var giftData = ItemStoreMgr.I.GetGiftDataByItemId(curInfo.id, "");
            //if (giftData != null)
            //{
            //    showDataList.Add(new ComplementPopData(UIComplementPop.UIComplementPopItemEnum.Gift, ShopBtnTypeEnum.GoToLand, () =>
            //    {
            //        ShopMgr.I.OpenShopPanel(ShopTab.ValuePackage, ShopTabConfig.I.specialDealEntranceTabs);
            //    }));
            //}
            //foreach (var item in info.ItemSource)
            //{

            //    switch (item)
            //    {
            //        case 1:
            //            showDataList.Add(new ComplementPopGoToData(UIComplementPop.UIComplementPopItemEnum.ItemOpenBox, ShopBtnTypeEnum.GoToLand, () =>
            //            {
            //                CSVItem.ToGetItemBytype(info, item);
            //            }));
            //            break;
            //        case 2:
            //            showDataList.Add(new ComplementPopGoToData(UIComplementPop.UIComplementPopItemEnum.ItemTask, ShopBtnTypeEnum.GoToLand, () =>
            //            {
            //                CSVItem.ToGetItemBytype(info, item);
            //            }));
            //            break;
            //        case 3:
            //            showDataList.Add(new ComplementPopGoToData(UIComplementPop.UIComplementPopItemEnum.ItemStore, ShopBtnTypeEnum.GoToLand, () =>
            //            {
            //                CSVItem.ToGetItemBytype(info, item);
            //            }));
            //            break;
            //        case 4:
            //            showDataList.Add(new ComplementPopGoToData(UIComplementPop.UIComplementPopItemEnum.ItemOpenSpecialBox, ShopBtnTypeEnum.GoToLand, () =>
            //            {
            //                CSVItem.ToGetItemBytype(info, item);
            //            }));
            //            break;
            //        case 5:
            //            showDataList.Add(new ComplementPopGoToData(UIComplementPop.UIComplementPopItemEnum.ItemHeroProduct, ShopBtnTypeEnum.GoToLand, () =>
            //            {
            //                CSVItem.ToGetItemBytype(info, item);
            //            }));
            //            break;
            //        case 6:
            //            showDataList.Add(new ComplementPopGoToData(UIComplementPop.UIComplementPopItemEnum.ItemAllianceHelp, ShopBtnTypeEnum.AllianceHelp, () =>
            //            {
            //                if (ItemStoreMgr.I.ChackUnionHelpIsLimit(info.id, out int id))
            //                {
            //                    //有联盟则发送请求
            //                    LAllianceHelp.I.ReqUnionHelpApply(id);
            //                    LAllianceHelp.I.ReqUnionHelpCount();
            //                    PopupManager.I.ClosePopup<UIMergeGoodFrom>();
            //                    PopupManager.I.ClosePopup<UIMergeGoodInfo>();
            //                }
            //                else
            //                {
            //                    FloatTips.I.FloatMsg(LocalizationMgr.Get("Alliance_Help_Time_Out"));
            //                }
            //            }));
            //            break;
            //        case 12:
            //            if (Logic.CSPlayer.I.SpecialBoxs[0].Unlock)
            //            {
            //                showDataList.Add(new ComplementPopGoToData(UIComplementPop.UIComplementPopItemEnum.Itemquarry, ShopBtnTypeEnum.GoToLand, () =>
            //                {    //采石
            //                    CSVItem.ToGetItemBytype(info, item);
            //                }));
            //            }

            //            break;
            //        case 13:
            //            if (Logic.CSPlayer.I.SpecialBoxs[1].Unlock)
            //            {
            //                showDataList.Add(new ComplementPopGoToData(UIComplementPop.UIComplementPopItemEnum.ItemBuildTheFactory, ShopBtnTypeEnum.GoToLand, () =>
            //                {    //建造
            //                    CSVItem.ToGetItemBytype(info, item);
            //                }));
            //            }
            //            break;
            //        case 15:
            //            if (Logic.CSPlayer.I.SpecialBoxs[2].Unlock)
            //            {
            //                showDataList.Add(new ComplementPopGoToData(UIComplementPop.UIComplementPopItemEnum.ItemForgingFactory, ShopBtnTypeEnum.GoToLand, () =>
            //                {    //锻造
            //                    CSVItem.ToGetItemBytype(info, item);
            //                }));
            //            }
            //            break;
            //        case 14:
            //            if (Logic.CSPlayer.I.SpecialBoxs[3].Unlock)
            //            {
            //                showDataList.Add(new ComplementPopGoToData(UIComplementPop.UIComplementPopItemEnum.Itemport, ShopBtnTypeEnum.GoToLand, () =>
            //                {   //港口
            //                    CSVItem.ToGetItemBytype(info, item);
            //                }));
            //            }
            //            break;

            //        default:
            //            break;
            //    }
            //}
            //PopupManager.I.ShowDialog<UIComplementPop>(new UIComplementPopData() { popShowEnum = UIComplementPop.UIComplementPopEnum.Item, datas = showDataList, needData = new CommonItem.ItemData() { Id = info.id, Typ = AssetType.Merge } });
            ////PopupManager.I.ShowWidget<UIMergeGoodFrom>(new UIMergeGoodFormData(curInfo.id));
        }
        private void OnClickBuy(GiftItemData giftData)
        {
            if (giftData == null)
                return;

            var id = giftData.CfgId.ToString();
            var price = giftData.GetProductId().ToString();

            if (GameData.I.GiftData.CheckGiftLimitBuy(giftData.CfgId))
            {
                UITools.PopTips(LocalizationMgr.Get("Gift_dailygift_tips_01"));
                return;
            }

            LPay.I.BuyItem(id, price, ShopMgr.I.OnBuyItemAction);
        }

        public void ShowCur(UIMergeGoodInfo_infopanel infopanel, ItemInfo info)
        {

            curInfo = info;
            curGoodAni.transform.position = infopanel.transform.position;
            curGoodAni.transform.SetParent(infopanel.transform);
            btnWhere.gameObject.SetActive(mData.OpenSourceBtn && info.ItemSource.Count > 0);
            //btnWhere.SetActive(info.ItemSource.Count > 0);
            CoroutineMgr.StartCoroutine(SetCur(infopanel.transform));
        }
    }
}