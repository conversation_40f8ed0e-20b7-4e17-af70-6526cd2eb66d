﻿using Common;
using cspb;
using Cysharp.Threading.Tasks;
using DeepUI;
using DG.Tweening;
using Game.Config;
using Game.Data;
using K1;
using Logic;
using System;
using System.Collections.Generic;
using K3;
using TaskType;
using TFW;
using TFW.Localization;
using UnityEngine;
using UI.Utils;
using System.Linq;

namespace UI
{
    /// <summary>
    /// 任务工具类
    /// by zjw at 20200709 16.25
    /// </summary>
    public static class TaskUtils
    {
        /// <summary>
        /// 任务跳转到克劳蒂亚购买界面（因无配表所以不占用QuestTypeID）
        /// </summary>
        public static void GoToClaudia()
        {
            var ui = PopupManager.I.FindPopup<UIActivityMain>();
            if (ui != null)
                PopupManager.I.ClosePopup<UIActivityMain>();
            //新首充过滤礼包机制会导致触发包数据为空的情况，策划说改成弹新首充
            if (LFirstCharge.I.FirstRechargeVersionInfoNtfData != null && !LFirstCharge.I.FirstRechargeVersionInfoNtfData.finish
                && LFirstCharge.I.FirstRechargeVersionInfoNtfData.version == NewFirstRechargeVer.NewFirstRechargeVer1)
            {
                PopupManager.I.ShowPanel<UIFirstChargeNew>();
            }
            else if (LFirstCharge.I.FirstRechargeVersionInfoNtfData != null && LFirstCharge.I.FirstRechargeVersionInfoNtfData.version == NewFirstRechargeVer.NewFirstRechargeVerOld)
            {
                TriggerGiftUI.Layer.OpenSafely();
            }
        }
        public static void GotoFunc(int type)
        {

            switch (type)
            {
                case 1: ///内城界面
                    JumpToCity();
                    break;
                case 3: ///英雄升级界面
                    JumpToHero(HeroToDO.LevelUp);
                    break;
                case 4: ///英雄升星界面
                    JumpToHero(HeroToDO.StarUp);
                    break;
                case 5: ///招募界面
                    JumapToHeroRecruit();
                    break;
                case 6: ///龙界面
                    JumpToDragonSkill();
                    break;
                case 7: ///士兵界面
                    JumpToEvo();
                    break;
                case 8: ///小怪界面
                    JumpToWorld();
                    break;
                case 9: ///泰坦界面
                    JumpToWorld(2);
                    break;
                case 10: ///金币采集界面
                    JumpToWorld(1);
                    break;
                case 11: ///熔岩试炼活动界面
                    OpenActvPage();
                    break;
                case 12: ///大地图界面
                    JumpToWorld(4);
                    break;
                case 13: ///背包界面
                    JumpToWorld(2);
                    break;
                case 14: ///道具商店界面
                    JumpShop(ShopJumpMark.itemStore);
                    break;
                case 15: ///联盟界面
                    JumpToAlliance();
                    break;
                case 16: ///角色信息界面
                    OpenChangeName();
                    break;
                case 17: ///城堡升级界面
                    OpenCity();
                    break;
                case 18: ///钻石购买界面
                    JumpShop(ShopJumpMark.gem);
                    break;
                case 19: ///平价包界面
                         ///暂时废弃
                    break;
                case 20: ///王座活动界面
                    JumpToKingThrone();
                    break;
                case 21: ///竞技场界面
                    break;
                case 22: ///次元宝藏界面
                    OpenDimensionalTreasure();
                    break;
                case 23: ///联盟帮助界面
                    OpenAllianceHelp();
                    break;
                case 24: ///联盟科技界面
                    OpenAllianceTech();
                    break;
                case 25: ///铁匠铺界面
                    // OpenHeroEquipment();
                    break;
                case 26: ///召唤阵界面
                    OpenSummonAltar();
                    break;
                case 27: ///女巫锅界面
                    OpenWitchsBrew();
                    break;
                case 28: ///学院科技界面
                    JumpToDragonSkill();
                    break;
                case 29: ///联盟挑战界面
                    OpenAllianceChallenge();
                    break;
                case 30: ///英雄治疗界面
                    JumpToHero();
                    break;
                case 31:///联盟领地堡垒
                    OpenUIAllianceWarTerritoryList(1);
                    break;
                case 32:///联盟领地方尖碑
                    OpenUIAllianceWarTerritoryList(2);
                    break;

            }
        }

        /// <summary>
        /// 任务跳转
        /// </summary>
        /// <param name="taskTypeId">任务类型</param>
        /// <param name="parms">参数列表</param>
        [Obsolete("任务跳转使用新版本！[部分需要使用后续整理！]")]
        public static void GoTo(int taskTypeId, params string[] parms)
        {
            //修改活动跳转前关闭所有界面，主城界面动画容易穿透大地图背景，此处只需要关闭活动界面即可
            var ui = PopupManager.I.FindPopup<UIActivityMain>();
            if (ui != null)
                PopupManager.I.ClosePopup<UIActivityMain>();

            //var UIDailyTask = PopupManager.I.FindPopup<UIDailyTask>();
            //if (UIDailyTask != null)
            //    PopupManager.I.ClosePopup<UIDailyTask>();

            //var UIMerge = PopupManager.I.FindPopup<UIMerge>();
            //if (UIMerge != null)
            //    PopupManager.I.ClosePopup<UIMerge>();

            //DeepUI.PopupManager.I.ClearAllLayer();
            //WndMgr.HideAll(false, excludeHideType);
            //WndMgr.HideAll();
            //跳转功能：
            //a) 造兵次数——内城
            //b) 攻击野怪次数——前往大地图并打开搜索
            //c) 通过关卡数——前往内城
            //d) 开启宝箱数——前往商城
            //e) 杀死野怪数（塔防）——前往内城
            //f) 消耗英雄卡次数——前往英雄
            //g) 合成士兵次数——前往内城
            var isWorld = WorldSwitchMgr.I.ShowWorldType == WorldTypeEnum.WORLD;

            UIGuidData guidData;

            QuestTypeID type = (QuestTypeID)taskTypeId;
            Debug.LogWarningFormat($"任务跳转：{taskTypeId}==>>{type.ToString()}！");
            switch (type)
            {
                #region 城堡
                case QuestTypeID.TotalPower:
                case QuestTypeID.SoliderPorwer:
                case QuestTypeID.JumpMainCastle:
                    //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.PRISON);

                    PopupManager.I.ClosePopup<UIDailyTask>();

                    PopupManager.I.ShowPanel<UICityLevelUp>(new UICityBuildLevelUpData() { cityType = (int)MainCityItem.CityType.Castle });
                    break;
                #endregion

                #region 英雄

                case QuestTypeID.HeroUpTimes:
                case QuestTypeID.PowerUpByHero:
                case QuestTypeID.PowerUp:
                    JumpToHero(HeroToDO.LevelUp);
                    break;
                case QuestTypeID.HeroUpToLv:
                    JumpToHero(HeroToDO.LevelUp_MinTypeLevel);
                    break;
                case QuestTypeID.HeroNumToStar:
                case QuestTypeID.HeroUpStarTimes:
                case QuestTypeID.HeroUpStarTimes_1:
                case QuestTypeID.HeroLevelUp:
                case QuestTypeID.HeroLevelUpOn:
                    JumpToHero(HeroToDO.StarUp);
                    break;
                case QuestTypeID.HeroRecruitHighTimes:
                    var _advanceFreeCDRemindTime = HeroRecruitMgr.I.GetAdvanceEndingTime() - GameTime.Time / 1000;
                    //PopupManager.I.ShowLayer<UIHero_Upgrade_RecruitHero>(new UIHero_Upgrade_RecruitHeroData() { isGoToHero = false });
                    HeroRecruitMgr.I.OpenHeroRecruit(recruitType: 2);
                    guidData = new UIGuidData();
                    if (_advanceFreeCDRemindTime > 0)
                    {
                        guidData.guidItems.Add(new UIGuidItem() { UIName = "UIHeroRecruitNew", UIItem = "Root/GroupBtns/BtnLeft", slide = true, closeDelay = 5 });
                    }
                    else
                    {
                        guidData.guidItems.Add(new UIGuidItem() { UIName = "UIHero_Upgrade_RecruitHero", UIItem = "Root/Buttom/Button/Free", slide = true, closeDelay = 5 });
                    }
                    UIGuid.StartGuid(guidData, true);
                    break;
                case QuestTypeID.HeroRecruitCommonTimes:
                case QuestTypeID.HeroRecruitTimes:
                    int _normalFreeTimes = 0;
                    long _normalFreeCDRemindTime = 0;
                    // var adData = GameData.I.AdShopData.GetAdGroupData(7);
                    // if (adData != null)
                    // {
                    //     ////剩余的次数
                    //     var remianLimit = adData.GetAdLeftTime();
                    //     long remainTime = adData.GetLastTimeStamp() + adData.GetInterValTime() * 1000 - GameTime.Time;
                    //     _normalFreeTimes = remianLimit;
                    //     _normalFreeCDRemindTime = remainTime / 1000;
                    // }
                    HeroRecruitMgr.I.OpenHeroRecruit(recruitType: 2);
                    //PopupManager.I.ShowLayer<UIHero_Upgrade_RecruitHero>(new UIHero_Upgrade_RecruitHeroData() { isGoToHero = false });

                    guidData = new UIGuidData();
                    if (_normalFreeCDRemindTime > 0 || _normalFreeTimes == 0)
                    {
                        guidData.guidItems.Add(new UIGuidItem() { UIName = "UIHero_Upgrade_RecruitHero", UIItem = "Root/Top/Button/OneTimes", slide = true, closeDelay = 5 });
                    }
                    else
                    {
                        guidData.guidItems.Add(new UIGuidItem() { UIName = "UIHero_Upgrade_RecruitHero", UIItem = "Root/Top/Button/Free", slide = true, closeDelay = 5 });
                    }

                    UIGuid.StartGuid(guidData, true);
                    break;
                #endregion

                #region 科技
                case QuestTypeID.ArmyEvoUp:
                case QuestTypeID.CostEvoUp:
                    JumpToEvo();
                    break;
                case QuestTypeID.SkillScene:
                    JumpToDragonSkill(false);
                    break;
                case QuestTypeID.SkillNumToLv:
                case QuestTypeID.SkillUpTimes:
                case QuestTypeID.SkillUpTimesById:
                case QuestTypeID.PowerUpBySkill:
                    JumpToDragonSkill();
                    break;
                #endregion

                #region 内城
                case QuestTypeID.ArmyTrainTimes:
                    JumpToCityToMerge();

                    //guidData = new UIGuidData();
                    //guidData.guidItems.Add(new UIGuidItem() { UIName = "UI.UIMain2", UIItem = "content/MenuRoot/Menu/Train", slide = true, closeDelay = 5 });
                    //UIGuid.StartGuid(guidData, true);
                    break;
                case QuestTypeID.ArmyComposeTime:
                case QuestTypeID.ArmyComposeTimeByType:
                case QuestTypeID.ArmyNumToLv:
                    JumpToCityToMerge();

                    //if (GameData.I.MergeGameData.CanOpenAutoMerge)
                    //{
                    //    int parm = parms.Length > 0 ? int.Parse(parms[0]) : 0;
                    //    if (MapManager.I.MapOperation.GetCanCompose(parm, out var soldier1, out var soldier2))
                    //    {
                    //        guidData = new UIGuidData();
                    //        guidData.guidItems.Add(new UIGuidItem()
                    //        {
                    //            swip = new UIGuidSwip()
                    //            {
                    //                swip = true,
                    //                startPos = GameInstance.MainCamera.WorldToScreenPoint(soldier1.landTran.position),
                    //                endPos = GameInstance.MainCamera.WorldToScreenPoint(soldier2.landTran.position),
                    //            }
                    //            ,
                    //            closeDelay = 2
                    //        });
                    //        UIGuid.StartGuid(guidData, true);
                    //    }
                    //    else
                    //    {
                    //        guidData = new UIGuidData();
                    //        guidData.guidItems.Add(new UIGuidItem() { UIName = "UI.UIMain2", UIItem = "content/MenuRoot/Menu/Train", slide = true, closeDelay = 5 });
                    //        UIGuid.StartGuid(guidData, true);
                    //    }
                    //}
                    //else
                    //{
                    //    guidData = new UIGuidData();
                    //    guidData.guidItems.Add(new UIGuidItem() { UIName = "UI.UIMain2", UIItem = "content/MenuRoot/Menu/Train", slide = true, closeDelay = 5 });
                    //    UIGuid.StartGuid(guidData);
                    //}

                    break;

                case QuestTypeID.CostNumGold:
                case QuestTypeID.MergeOpenBigBoxTimes:
                case QuestTypeID.MergeItemTimes:
                case QuestTypeID.MergeTargetTimes:
                case QuestTypeID.MergeUnlockTimes:
                case QuestTypeID.MergeUnlockArea:
                case QuestTypeID.MergeUseGoldItem:
                case QuestTypeID.MergeUseDiamondItem:
                case QuestTypeID.MergeUseEnergyItem:
                case QuestTypeID.MergeResetBox:
                case QuestTypeID.MergeOpenBoxTimes:
                case QuestTypeID.MergeOrder:
                case QuestTypeID.MergeOrder_1:
                    // case QuestTypeID.MergeOrder_2:
                    JumpToCityToMerge();
                    break;

                case QuestTypeID.MergeUnlockPhotoObj:
                case QuestTypeID.MergeUnlockPhotoObj2:
                    JumpToPhoto();
                    break;
                case QuestTypeID.MergeRebuild:
                    JumpToReBuild(int.Parse(parms[0]));
                    break;
                #endregion

                #region PVE
                case QuestTypeID.PVENumLvTimes:
                case QuestTypeID.PVEAllLvTimes:
                    JumpToWorld();
                    break;
                case QuestTypeID.PVECollectResNum:
                case QuestTypeID.CollectRes:
                    JumpToWorld(1);
                    break;
                case QuestTypeID.PVERallyNumLvTimes:
                case QuestTypeID.PVERallyAllLvTimes:
                    JumpToWorld(2);
                    break;
                #endregion

                #region PVP
                case QuestTypeID.PVPTimes:
                    JumpToWorld(4);
                    break;
                #endregion

                #region 其他
                case QuestTypeID.CostNumEngery:
                    JumpToWorld();
                    break;
                case QuestTypeID.CostNumCommonBox:
                case QuestTypeID.CostNumSilverBox:
                case QuestTypeID.CostNumGoldBox:
                    JumpShop(ShopTab.Bag, new ShopTab[] { ShopTab.Bag });
                    // PopupManager.I.ShowPanel<UIMonthCardPop>(new UIMonthCardPopData() { cardEnum = MonthCardEnum.Ad });
                    break;
                case QuestTypeID.JumpItemStore:
                    JumpShop(ShopTab.ItemStore);
                    break;
                case QuestTypeID.JumpCommonGift:
                    JumpShop(ShopTab.GemRecharge);
                    break;
                case QuestTypeID.LoginDayNum:
                    if (!UIActivityMain.ShowActivityByCfgId(1000002))
                        UITools.PopTips(LocalizationMgr.Get("Skin_manage_tips_01"));
                    break;
                case QuestTypeID.InAlliance:
                    JumpToAlliance();
                    break;
                case QuestTypeID.InWorld:
                    JumpToWorld(-1);
                    break;
                case QuestTypeID.ChangName:
                case QuestTypeID.ChangName_1:
                    PopupManager.I.ClearAllPopup();

                    guidData = new UIGuidData();
                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMain2", UIItem = $"content/Left_Top/Head_k1" });
                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIPlayerMain", UIItem = $"Content/Content/Info/RenameBtn" });
                    UIGuid.StartGuid(guidData);
                    break;


                #endregion


                #region 旧类型（联盟任务
                case QuestTypeID.TrainSoldierTimes:
                case QuestTypeID.PassLevelCount:
                case QuestTypeID.MergeSoldierTimes:
                case QuestTypeID.DefendMonsterTimes:
                    if (isWorld)
                    {
                        //跳转到内城
                        JumpToCityToMerge();
                    }
                    else
                    {
                        //UIMain.I.TrySwitch(MainMenuConst.TRAIN);
                        //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.PRISON);
                        if (LSwitchMgr.I.IsFunctionOpen(SwitchConfig.new_hud))
                        {
                            EventMgr.FireEvent(TEventType.RefreshSkill);
                        }
                        //WndMgr.HideAll();
                        //DeepUI.PopupManager.I.ClearAllLayer();
                    }

                    break;
                case QuestTypeID.AttackMonsterTimes:
                    //if (!isWorld)
                    {
                        JumpToWorld();
                    }
                    break;
                case QuestTypeID.OpenChestTimes:
                    JumpShop();
                    break;
                case QuestTypeID.MergeHeroTimes:
                    JumpToHero();
                    break;
                case QuestTypeID.ActivityAcquisition:
                    JumpToWorld(1);
                    break;
                #endregion

                default:

                    Type t = Type.GetType(string.Format("TaskType.TaskTypeGoTo_{0}", taskTypeId));

                    if (t != null)
                    {
                        D.Info?.Log($"使用新方法进行任务的跳转实现扩展 {taskTypeId}");

                        var taskGoToInfo = System.Activator.CreateInstance(t) as TaskGoTo;
                        taskGoToInfo?.GoTo(parms);
                    }
                    else
                    {
                        Debug.LogWarningFormat($"任务跳转尚未实现：{taskTypeId} ，请找对应策划及程序！");
                    }

                    break;
            }
        }


        //public static void GoTo(Cfg.G.CChapterQuest taskData)
        //{
        //    if (taskData != null)
        //    {
        //        GoTo(taskData.QuestType, taskData.Params.ToArray());
        //    }
        //}

        public enum HeroToDO
        {
            None,
            LevelUp,
            StarUp,
            Fellter,
            LevelUp_MinTypeLevel,
            Intimacy
        }

        /// <summary>
        /// 跳转到英雄
        /// </summary>
        public static void JumpToHero(HeroToDO heroToDO = HeroToDO.None, Game.Data.HeroData mHeroData = null)
        {

            if (!MainFunctionOpenUtils.CheckHeroOpenState(true))
            {
                return;
            }


            PopupManager.I.ClearAllPopup();

            EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.CITY);

            var heroList = HeroGameData.I.GetAllHeroData().Where(x => x.HeroCfg.HeroType >= 3).ToList();

            var guidData = new UIGuidData();
            guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMain2", UIItem = "content/MenuRoot/Hero", slide = true, delayFinger = .5f });

            switch (heroToDO)
            {
                case HeroToDO.None:
                    //todo 此处要不要把这个方法放在Switch后面？不知道为啥每一个case都执行一遍。
                    UIGuid.StartGuid(guidData, true);
                    break;
                case HeroToDO.LevelUp:

                    if (heroList.Count > 0)
                    {

                        var heroList1 = heroList.OrderByDescending(a => a.Level).ToList();

                        if (heroList1.Count > 0)
                        {
                            heroList = heroList1;
                        }

                        guidData.guidItems.Add(new UIGuidItem()
                        {
                            UIName = "UIHeroList",
                            GetUIItemGa = () =>
                            {
                                var heroListUI = PopupManager.I.FindPopup<UIHeroList>();
                                return heroListUI?.GetHeroItem(heroList[0].ModelId) ?? null;
                            },
                            slide = true,
                            delayFinger = .5f
                        });
                        guidData.guidItems.Add(new UIGuidItem() { UIName = "MyHeroShowView", UIItem = "Root/HeroInfo/LevelUpBtn", slide = true, delayFinger = .5f });

                    }

                    UIGuid.StartGuid(guidData, true);
                    break;
                case HeroToDO.StarUp:
                    if (heroList.Count > 0)
                    {
                        guidData.guidItems.Add(new UIGuidItem()
                        {
                            UIName = "UIHeroList",
                            GetUIItemGa = () =>
                            {
                                var heroListUI = PopupManager.I.FindPopup<UIHeroList>();
                                return heroListUI?.GetHeroItem(heroList[0].ModelId) ?? null;
                            },
                            slide = true,
                            delayFinger = .5f
                        });
                        guidData.guidItems.Add(new UIGuidItem() { UIName = "MyHeroShowView", UIItem = "Root/Tab/tab2", slide = true, delayFinger = .5f });
                        guidData.guidItems.Add(new UIGuidItem() { UIName = "MyHeroShowView", UIItem = "Root/HeroStar/StarUpBtn", slide = true, delayFinger = .5f });
                    }

                    UIGuid.StartGuid(guidData, true);
                    break;
                case HeroToDO.Intimacy:
                    if (heroList.Count > 0)
                    {

                        var heroList1 = heroList.OrderByDescending(a => a.Level).ToList();

                        if (heroList1.Count > 0)
                        {
                            heroList = heroList1;
                        }

                        guidData.guidItems.Add(new UIGuidItem()
                        {
                            UIName = "UIHeroList",
                            GetUIItemGa = () =>
                            {
                                var heroListUI = PopupManager.I.FindPopup<UIHeroList>();
                                return heroListUI?.GetHeroItem(heroList[0].ModelId) ?? null;
                            },
                            slide = true,
                            delayFinger = .5f
                        });
                        guidData.guidItems.Add(new UIGuidItem() { UIName = "MyHeroShowView", UIItem = "Root/Tab/tab3", slide = true, delayFinger = .5f });
                        //guidData.guidItems.Add(new UIGuidItem() { UIName = "MyHeroShowView", UIItem = "Root/HeroStar/StarUpBtn", slide = true, delayFinger = .5f });

                    }

                    UIGuid.StartGuid(guidData, true);
                    break;
                case HeroToDO.Fellter:
                    if (heroList.Count > 0)
                    {
                        guidData.guidItems.Add(new UIGuidItem()
                        {
                            UIName = "UIHeroList",
                            GetUIItemGa = () =>
                            {
                                var heroListUI = PopupManager.I.FindPopup<UIHeroList>();
                                return heroListUI?.GetHeroItem(heroList[0].ModelId) ?? null;
                            },
                            slide = true,
                            delayFinger = .5f
                        });
                        guidData.guidItems.Add(new UIGuidItem() { UIName = "MyHeroShowView", UIItem = "Root/Tab/tab2", slide = true, delayFinger = .5f });
                        guidData.guidItems.Add(new UIGuidItem() { UIName = "MyHeroShowView", UIItem = "Root/HeroStar/StarUpBtn", slide = true, delayFinger = .5f });
                    }
                    UIGuid.StartGuid(guidData, true);
                    break;
                case HeroToDO.LevelUp_MinTypeLevel:
                    if (heroList.Count > 0)
                    {

                        var heroList1 = heroList.GroupBy(a => a.HeroCfg.HeroMergeType).ToList();

                        heroList.Clear();

                        foreach (var item in heroList1)
                        {
                            var item1 = item.OrderByDescending(a => a.Level).FirstOrDefault();
                            if (item1 != null)
                            {
                                heroList.Add(item1);
                            }
                        }

                        if (heroList.Count > 0)
                        {
                            heroList = heroList.OrderBy(a => a.Level).ToList();

                            guidData.guidItems.Add(new UIGuidItem()
                            {
                                UIName = "UIHeroList",
                                GetUIItemGa = () =>
                                {
                                    var heroListUI = PopupManager.I.FindPopup<UIHeroList>();
                                    return heroListUI?.GetHeroItem(heroList[0].ModelId) ?? null;
                                },
                                slide = true,
                                delayFinger = .5f
                            });
                            guidData.guidItems.Add(new UIGuidItem() { UIName = "MyHeroShowView", UIItem = "Root/HeroInfo/LevelUpBtn", slide = true, delayFinger = .5f });
                        }
                    }

                    UIGuid.StartGuid(guidData, true);
                    break;
                default:
                    break;
            }

            //MapManager.I.CanDragOnMap(false);
        }


        public static void JumpToAlliance()
        {
            if (!MainFunctionOpenUtils.ALllianceOpenState)
            {
                FloatTips.I.FloatMsg(UnlockMgr.I.GetUnlockDesc(UnlockFuncEnum.Alliance));
            }

            //if (UIMain.I != null)
            //{
            //    UIMain.I.TrySwitch(MainMenuConst.ALLIANCE);
            //}
            //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.ALLIANCE);

            //MapManager.I.CanDragOnMap(false);
        }

        public static void JumpCityTechLevelUp()
        {
            //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.PRISON);
            var ui = PopupManager.I.FindPopup<UIMainCity>();
            if (ui != null & ui.dragImage!=null)
            {
                PopupManager.I.ClearAllPopup();

                int cityCfgID = LPlayer.I.GetCityBuildingIDByType(1);
                ui.dragImage.MoveTOCfg(cityCfgID, () =>
                {
                    var guidData = new UIGuidData();
                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMainCity", UIItem = $"FullScreenBgCanvas/Root/BG1/Content/Building/build_1" });
                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UICityLevelUp", UIItem = $"Root/mBtnRoot/Button" });
                    UIGuid.StartGuid(guidData);
                });
            }
        }

        public static void JumpToStar()
        {
            PopupManager.I.ClearAllPopup();

            if (GameData.I.MainData.CurrMenuType == MainMenuType.CITY)//PRISON
            {
                var ui = PopupManager.I.FindPopup<UIMainCity>();
                int cityType = (int)MainCityItem.CityType.Star;
                int cityCfgID = LPlayer.I.GetCityBuildingIDByType(cityType);
                ui.dragImage.MoveTOCfg(cityCfgID, () =>
                {
                    var guidData = new UIGuidData();
                    var offset = Vector3.zero;
                    if (cityType == (int)MainCityItem.CityType.Star)
                    {
                        offset = new Vector3(50, 250, 0);
                    }

                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMainCity", UIItem = $"FullScreenBgCanvas/Root/BG1/Content/Building/build_{cityType}" ,figherOffset = offset});
                    UIGuid.StartGuid(guidData);
                });

            }
            else
            {

                NTimer.CountDown(0.5f, () =>
                {
                    var guidData = new UIGuidData();
                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMain2", UIItem = $"content/RightBtns/StargazingPlatform/Button", slide = true });
                    UIGuid.StartGuid(guidData);
                });
            }
        }

        public static void JumpToBuildLevelUp()
        {
            //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.PRISON);
            PopupManager.I.ClosePopup<UIDailyTask>();
            PopupManager.I.ShowPanel<UICityLevelUp>(new UICityBuildLevelUpData() { cityType = (int)MainCityItem.CityType.Castle });
        }

        public static void JumpToMainCity(bool isJumpTop = true)
        {
            ShowMainCity();

            //if (SceneManager.I.IsUIMainShow())
            //{
            //    if (GameData.I.MainData.CurrMenuType != MainMenuType.PRISON)
            //        EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.PRISON);
            //}

            if (isJumpTop)
            {

                EventMgr.FireEvent(TEventType.MaincityJumpTop);
            }
            else
            {
                EventMgr.FireEvent(TEventType.MaincityJumpBottom);
            }
        }

        public static void JumpToAllianceScience()
        {
            PopupManager.I.ClearAllPopup();

            NTimer.CountDown(0.5f, () =>
            {
                var guidData = new UIGuidData();

                guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMain2", UIItem = $"content/MenuRoot/Alliance", slide = true });
                if (LPlayer.I.IsPlayerInUnion())
                {
                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIAllianceMain", UIItem = $"Root/AlliancePage/Btns/Tech" });
                }
                UIGuid.StartGuid(guidData);
            });
        }

        /// <summary>
        /// 带引导的
        /// </summary>
        /// <param name="cityType"></param>
        public static void JumpToMainCity(MainCityItem.CityType cityType)
        {
            ShowMainCity();

            //if (SceneManager.I.IsUIMainShow())
            //{
            //    if (GameData.I.MainData.CurrMenuType != MainMenuType.PRISON)
            //        EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.PRISON);
            //}
            var guidData = new UIGuidData();
            switch (cityType)
            {
                case MainCityItem.CityType.BlacksmithShop:
                    EventMgr.FireEvent(TEventType.MaincityJumpBottom);
                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMainCity", UIItem = "FullScreenBgCanvas/Root/CanDragRoot/Canvas/BlacksmithShop", slide = true, delayFinger = .5f });
                    // guidData.guidItems.Add(new UIGuidItem() { UIType = typeof(UIMainCity), UIItem = "Root/CanDragRoot/Canvas/BlacksmithShop", slide = true, delayFinger = .5f });
                    break;
                //case MainCityItem.CityType.College:
                //    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMainCity", UIItem = "Root/CanDragRoot/Canvas/College", slide = true, delayFinger = .5f });
                //    guidData.guidItems.Add(new UIGuidItem() { UIType = typeof(UIMainCity), UIItem = "Root/CanDragRoot/Canvas/College", slide = true, delayFinger = .5f });
                //    break;
                //case MainCityItem.CityType.Barracks:
                //    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMainCity", UIItem = "Root/CanDragRoot/Canvas/Barracks", slide = true, delayFinger = .5f });
                //    guidData.guidItems.Add(new UIGuidItem() { UIType = typeof(UIMainCity), UIItem = "Root/CanDragRoot/Canvas/Barracks", slide = true, delayFinger = .5f });
                //    break;
                //case MainCityItem.CityType.DragonPoint:
                //    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMainCity", UIItem = "Root/CanDragRoot/Canvas/DragonPoint", slide = true, delayFinger = .5f });
                //    guidData.guidItems.Add(new UIGuidItem() { UIType = typeof(UIMainCity), UIItem = "Root/CanDragRoot/Canvas/DragonPoint", slide = true, delayFinger = .5f });
                //    break;
                //case MainCityItem.CityType.Main:
                //    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMainCity", UIItem = "Root/CanDragRoot/Canvas/Main", slide = true, delayFinger = .5f });
                //    guidData.guidItems.Add(new UIGuidItem() { UIType = typeof(UIMainCity), UIItem = "Root/CanDragRoot/Canvas/Main", slide = true, delayFinger = .5f });
                //    break;
                case MainCityItem.CityType.Tavern:
                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMainCity", UIItem = "FullScreenBgCanvas/Root/CanDragRoot/Canvas/Tavern", slide = true, delayFinger = .5f });
                    // guidData.guidItems.Add(new UIGuidItem() { UIType = typeof(UIMainCity), UIItem = "Root/CanDragRoot/Canvas/Tavern", slide = true, delayFinger = .5f });
                    break;
                case MainCityItem.CityType.SublimeTreasure:
                    // guidData.guidItems.Add(new UIGuidItem() { UIType = typeof(UIMainCity), UIItem = "Root/CanDragRoot/Canvas/SublimeTreasure", slide = true, delayFinger = .5f });
                    break;
                case MainCityItem.CityType.SummonAltar:
                    // guidData.guidItems.Add(new UIGuidItem() { UIType = typeof(UIMainCity), UIItem = "Root/CanDragRoot/Canvas/SummonAltar", slide = true, delayFinger = .5f });
                    break;
                case MainCityItem.CityType.Godness:
                    // guidData.guidItems.Add(new UIGuidItem() { UIType = typeof(UIMainCity), UIItem = "Root/CanDragRoot/Canvas/Godness", slide = true, delayFinger = .5f });
                    break;
            }
            UIGuid.StartGuid(guidData, true);

            //NTimer.CountDown(0.1f, () => { EventMgr.FireEvent(TEventType.MaincityJumpTop); });
        }

        public static void JumpToMainAllianceSetting(UI.Alliance.AllianceTabType type = Alliance.AllianceTabType.Info)
        {
            if (LPlayer.I.UnionID > 0)
            {
                var ui = PopupManager.I.FindPopup<Alliance.UIAllianceMain>();
                if (ui == null)
                {
                   PopupManager.I.ShowLayer<UI.Alliance.UIAllianceMain>(new UI.Alliance.UIAllianceMainData
                    {
                        openAlliancePage = type
                    });
                }
            }

            //if (SceneManager.I.IsUIMainShow())
            //{
            //    if (GameData.I.MainData.CurrMenuType != MainMenuType.ALLIANCE)
            //        EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.ALLIANCE);
            //}
        }

        public static void JumpToDragonSkill(bool isOpenSkillUI = true)
        {
            ShowMainCity();

            if (SceneManager.I.IsUIMainShow())
            {
                //if (GameData.I.MainData.CurrMenuType != MainMenuType.PRISON)
                //    EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.PRISON);
                //UIMain.I.TrySwitch(MainMenuConst.CITY);

                if (isOpenSkillUI)
                {
                    if (!UnlockMgr.I.CheckUnlock(UnlockFuncEnum.Building_8))
                    {
                        UITools.PopTips(UnlockMgr.I.GetUnlockDesc(UnlockFuncEnum.Building_8));
                        return;
                    }

                    PopupManager.I.ShowLayer<UIDragon>();

                    var guidData2 = new UIGuidData();
                    guidData2.guidItems.Add(new UIGuidItem() { UIName = "UI.UIDragon", UIItem = "Root/btn_upgrade", slide = true, delayFinger = .5f });
                    UIGuid.StartGuid(guidData2, true);
                }
            }
        }

        /// <summary>
        /// 跳到学院
        /// </summary>
        public static async void JumpToTech()
        {
            //if (!(await Cfg.C.CD2CityBuilding.GetConfigAsync((int)MainCityItem.CityType.College)).GetIsOpen())
            //{
            //    return;
            //}
            //PopupManager.I.ShowLayer<UITech>();
        }

        public static async void JumpToEvo()
        {
            ShowMainCity();

            if (SceneManager.I.IsUIMainShow())
            {
                //if (GameData.I.MainData.CurrMenuType != MainMenuType.PRISON)
                //    EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.PRISON);
                //UIMain.I.TrySwitch(MainMenuConst.CITY);

                //if (!(await Cfg.C.CD2CityBuilding.GetConfigAsync(1002)).GetIsOpen())
                //{
                //    return;
                //}
                //PopupManager.I.ShowLayer<UIEvo>();
                //if (LTowerDefenseType.I.isNTD)
                //    PopupManager.I.ShowLayer<UIEvoNew>();
                //else
                //    PopupManager.I.ShowLayer<UIEvo>();
                //var guidData2 = new UIGuidData();
                //if (LEvolution.I.ArcherData.CanToUp && string.IsNullOrEmpty(LEvolution.I.ArcherData.CostEnoughToUpgrade))
                //{
                //    guidData2.guidItems.Add(new UIGuidItem() { UIName = "UI.UIEvo", UIItem = "Root/SoldierGrid/1/UpgradeBtn", slide = true, delayFinger = .5f });
                //}
                //else if (LEvolution.I.IceData.CanToUp && string.IsNullOrEmpty(LEvolution.I.IceData.CostEnoughToUpgrade))
                //{
                //    guidData2.guidItems.Add(new UIGuidItem() { UIName = "UI.UIEvo", UIItem = "Root/SoldierGrid/3/UpgradeBtn", slide = true, delayFinger = .5f });
                //}
                //else if (LEvolution.I.FireData.CanToUp && string.IsNullOrEmpty(LEvolution.I.FireData.CostEnoughToUpgrade))
                //{
                //    guidData2.guidItems.Add(new UIGuidItem() { UIName = "UI.UIEvo", UIItem = "Root/SoldierGrid/2/UpgradeBtn", slide = true, delayFinger = .5f });
                //}
                //else if (LEvolution.I.ToxData.CanToUp && string.IsNullOrEmpty(LEvolution.I.ToxData.CostEnoughToUpgrade))
                //{
                //    guidData2.guidItems.Add(new UIGuidItem() { UIName = "UI.UIEvo", UIItem = "Root/SoldierGrid/4/UpgradeBtn", slide = true, delayFinger = .5f });
                //}
                //else
                //{
                //    guidData2.guidItems.Add(new UIGuidItem() { UIName = "UI.UIEvo", UIItem = "Root/SoldierGrid/1/UpgradeBtn", slide = true, delayFinger = .5f });
                //}

                //UIGuid.StartGuid(guidData2, true);
            }
        }

        /// <summary>
        /// 跳转到商城
        /// </summary>
        public static void JumpShop(ShopJumpMark jump)
        {
            //if (!GuideMgr.I.IsGuideDone)
            //    return;
            //var shopLockLevel = Cfg.C.CD2Config.I(MetaConst.SHOP_BTN_UNLOCK);
            //var unlockLevel = shopLockLevel.Val;
            //if (!GameData.I.LevelData.CanToDoByGameLevel(MetaConfig.UnlockShopBtn))
            //{
            //    UITools.PopTips(LocalizationMgr.Format("DEMO_37", MetaConfig.UnlockShopBtn));
            //    return;
            //}

            //if (UIMain.I != null)
            //{
            //    UIMain.I.TrySwitch(MainMenuConst.TRAIN);
            //}

            EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.CITY);

            ShopMgr.I.OpenShopPanel(jump);
        }

        /// <summary>
        /// 跳转到商城
        /// </summary>
        public static void JumpShop(ShopTab type, ShopTab[] shopTabs)
        {
            //if (!GuideMgr.I.IsGuideDone)
            //    return;
            //var shopLockLevel = Cfg.C.CD2Config.I(MetaConst.SHOP_BTN_UNLOCK);
            //var unlockLevel = shopLockLevel.Val;
            //if (!GameData.I.LevelData.CanToDoByGameLevel(MetaConfig.UnlockShopBtn))
            //{
            //    UITools.PopTips(LocalizationMgr.Format("DEMO_37", MetaConfig.UnlockShopBtn));
            //    return;
            //}

            EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.CITY);

            ShopMgr.I.OpenShopPanel(type, shopTabs);
        }

        /// <summary>
        /// 跳转到商城
        /// </summary>
        public static void JumpShop(ShopTab type = null, bool isScrollEnd = true)
        {
            //if (!GuideMgr.I.IsGuideDone)
            //    return;
            //var shopLockLevel = Cfg.C.CD2Config.I(MetaConst.SHOP_BTN_UNLOCK);
            //var unlockLevel = shopLockLevel.Val;
            //if (!GameData.I.LevelData.CanToDoByGameLevel(MetaConfig.UnlockShopBtn))
            //{
            //    UITools.PopTips(LocalizationMgr.Format("DEMO_37", MetaConfig.UnlockShopBtn));
            //    return;
            //}

            //if (UIMain.I != null)
            //{
            //    UIMain.I.TrySwitch(MainMenuConst.TRAIN);
            //}

            EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.CITY);

            ShopMgr.I.OpenShopPanel(type);
        }


        /// <summary>
        ///  跳转到世界
        /// </summary>
        /// <param name="worldShowType">-1不显示搜怪 0未默认打怪 1为采集 2为集结</param>
        public static async void JumpToWorld(int worldShowType = 0)
        {
            // if(worldShowType == 2)
            // {
            //     //集结需要判断是否天下大事满足条件
            //     if (! await ChronicleMgr.I.GetChronicleMassState())
            //         worldShowType = 0;
            // }

            if (!MainFunctionOpenUtils.WorldOpenState)
            {
                FloatTips.I.FloatMsg(UnlockMgr.I.GetUnlockDesc(UnlockFuncEnum.World));
                return;
            }

            if (SceneManager.I.IsUIMainShow())
            {
                //UIMain.I.TrySwitch(MainMenuConst.WORLD);
                EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.WORLD);

                switch (worldShowType)
                {
                    case 0:
                        PopupManager.I.ShowPanel<UISearch>(new UISearchData() { SearchType = 0 });
                        break;
                    case 1:
                        PopupManager.I.ShowPanel<UISearch>(new UISearchData() { SearchType = 1 });
                        break;
                    case 2:
                        PopupManager.I.ShowPanel<UISearch>(new UISearchData() { SearchType = 2 });
                        break;
                    case 4:
                        //直接跳世界 啥也不显示
                        break;
                    default:
                        break;
                }

                //switch (worldShowType)
                //{
                //    case 0:
                //    case 1:
                //    case 2:
                //        var guidData = new UIGuidData();
                //        guidData.guidItems.Add(new UIGuidItem() { UIName = "UI.UISearch", UIItem = "CentreOther/Animation/Popup1/Connet/Button/BtnSearch", slide = true, delayFinger = .5f });
                //        UIGuid.StartGuid(guidData, true);
                //        break;
                //}
            }
            //if (!GuideMgr.I.IsGuideDone) return;
            //var val = Cfg.C.CD2Config.I(MetaConst.Map_Open).Val;
            //if (GameData.I.LevelData.CurrGameLevel < val && !K1D2Config.I.IsJumpLevel)
            //{
            //    UITools.PopTips(LocalizationMgr.Format("DEMO_37", val));
            //    return;
            //}
            //WorldSwitchMgr.I.ShowWorldType = WorldTypeEnum.WORLD;
            //EventMgr.FireEvent(TEventType.CHANGE_SLG_OR_INNER);

            //if (UIMain.I != null)
            //{
            //    UIMain.I.ShowSearch();
            //}
        }

        /// <summary>
        /// 跳转到内城
        /// </summary>
        public static void JumpToCity()
        {
            //UIMain.I.TrySwitch(MainMenuConst.TRAIN);
            EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.CITY);
            //if (LSwitchMgr.I.IsFunctionOpen(Config.SwitchConfig.MainUINew))
            //{
            //    EventMgr.FireEvent(TEventType.RefreshSkill);
            //}
            //if (UIMain.I != null)
            //{
            //    UIMain.I.OnChangeClick();
            //}
        }

        /// <summary>
        /// 显示宝箱获得的奖励
        /// </summary>
        /// <param name="rewards"></param>
        public static void ShowChestReward(List<TypIDVal> rewards, UIDrawCardEnum boxType, Action callBack)
        {
            UITools.ShowReward(rewards, boxType, callBack);
        }

        public static void JumpUtil(int questType)
        {
            D2Quest quest = new D2Quest();
            quest.QuestGoToType = questType;
            quest.GoTo();
        }

        public static void ShowMainCity()
        {
            //var ui = SceneManager1.I.FindScene(typeof(UIMainCity));
            //if (ui == null)
            //    SceneManager1.I.ShowScene<UIMainCity>();
        }

        #region 宝箱飞积分

        /// <summary>
        /// 飞积分对象池
        /// </summary>
        private static ObjectPool<GameObject> diamondPool = new ObjectPool<GameObject>();

        /// <summary>
        ///  飞积分
        /// </summary>
        public static void FlyObj(GameObject flyCoinObj, Transform flyRoot, Transform startPos, Transform endPos, Action callBack = null)
        {
            //var parent = flyCoinObj.transform.parent;
            Fly(flyCoinObj, flyRoot, startPos, endPos, callBack);
        }

        /// <summary>
        /// 清除缓存
        /// </summary>
        public static void ReleasePool()
        {
            diamondPool?.Clear();
        }

        /// <summary>
        /// 飞积分
        /// </summary>
        /// <param name="flyObj">需要飞的物体</param>
        /// <param name="begin">起点</param>
        public static void Fly(GameObject flyObj, Transform parentTrans, Transform begin = null, Transform end = null, Action callBack = null)
        {
            Vector3 beginPos = begin.position;//  parentTrans.position; //+ new Vector3(0, 0, -8);

            Transform endTrans;
            Vector3 endPos;
            if (end == null)
            {
                endTrans = null;// coinWidget.GetIcon().transform;
                endPos = Vector3.zero;// coinWidget.GetIcon().transform.position;
            }
            else
            {
                endTrans = end;
                endPos = end.position;//end.GetComponent<RectTransform>().anchoredPosition;//
            }

            for (int i = 0; i < 10; i++)
            {
                //播放音效TODO
                //GameAudio.PlayAudio(AudioConst.COIN_FLY);

                Vector3 pos = Vector3.zero;
                int disY;
                int disX;
                GameObject flyObject;

                if (diamondPool.Count > 0)
                {
                    flyObject = diamondPool.Get();
                }
                else
                {
                    flyObject = GameObject.Instantiate(flyObj, parentTrans);
                }
                if (flyObject == null)
                {
                    continue;
                }
                flyObject.SetActive(true);

                PlayParabolaAnimation anim = flyObject.GetComponent<PlayParabolaAnimation>();
                if (anim == null)
                {
                    anim = flyObject.AddComponent<PlayParabolaAnimation>();
                }
                flyObject.transform.position = beginPos;
                //anim.delayed = i * 0.3f;
                if (i <= 2)
                {
                    anim.SetDuration(i * 0.1f);
                }
                else if (i > 2 && i <= 4)
                {
                    anim.SetDuration(0.4f);
                }
                else if (i > 4 && i <= 7)
                {
                    anim.SetDuration(0.5f);
                }
                else if (i > 7 && i <= 9)
                {
                    anim.SetDuration(0.6f);
                }
                anim.IsXRandom = false;
                anim.IsYRandom = false;
                anim.MaxOffsetX = 10f;
                anim.MinOffsetX = -10f;
                anim.MaxOffsetY = 10f;
                anim.MinOffsetY = -10f;
                anim.IsDestory = true;
                disY = PseudoRandom.Range(0, 8);
                disX = PseudoRandom.Range(-8, 10);
                pos.x = beginPos.x + 0.2f * disX;
                pos.y = beginPos.y + 0.2f * disY;
                //D.Error?.Log(disY + "===============" + disX);
                //pos.x = beginPos.x + 1.5f * Mathf.Cos(50 * Mathf.PI / 180);
                //pos.y = beginPos.y + 1.5f * Mathf.Sin(disY * Mathf.PI / 180);
                pos.z = beginPos.z;
                //flyObject.transform.position = pos;
                var tempI = i;
                anim.BeginPlay(beginPos, pos, null, false,
                    () =>
                    {
                        if (flyObject != null)
                        {
                            //flyObject.GetComponent<RectTransform>().DOAnchorPos(endPos,0.6f).OnComplete;
                            //flyObject.transform.localScale = Vector3.one;
                            //flyObject.transform.DOScale(Vector3.zero, 1f);
                            flyObject.transform.DOMove(endPos, 0.5f).OnComplete(() =>
                            {
                                if (end)
                                {
                                    var endAnim = end.GetComponent<Animation>();
                                    if (endAnim)
                                    {
                                        endAnim.Play();
                                    }
                                }
                                if (flyObject != null)
                                {
                                    flyObject.SetActive(false);
                                    diamondPool.Release(flyObject);
                                }

                                if (tempI == 9)
                                {
                                    //D.Error?.Log("结束了");
                                    if (callBack != null)
                                    {
                                        callBack();
                                    }
                                }
                            });
                        }
                    });
            }

        }
        #endregion


        #region

        public static void JumpToReBuild(int cityType)
        {
            if (!UnlockMgr.I.BuildUnlock((MainCityItem.CityType)cityType))
            {
                return;
            }

            //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.PRISON);
            var ui = PopupManager.I.FindPopup<UIMainCity>();
            if (ui != null && ui.dragImage!=null)
            {
                PopupManager.I.ClearAllPopup();

                int cityCfgID = LPlayer.I.GetCityBuildingIDByType(cityType);
                ui.dragImage.MoveTOCfg(cityCfgID, () =>
                {

                    var guidData = new UIGuidData();

                    string path = $"FullScreenBgCanvas/Root/BG1/Content/Building/build_{cityType}";

                    // 如果当前建筑未解锁。优先触发解锁逻辑 
                    var rebuild = ui.GameObject.transform.Find($"FullScreenBgCanvas/Root/BG1/Content/Building/{cityType * 1000}");

                    if (rebuild != null && rebuild.gameObject.activeInHierarchy)
                    {
                        path = $"FullScreenBgCanvas/Root/BG1/Content/Building/{cityType * 1000}";
                    }


                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMainCity", UIItem = path });
                    UIGuid.StartGuid(guidData);


                });
            }
        }

        private static void JumpToPhoto()
        {
            EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.CITY);
            var guidData = new UIGuidData();
            guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMergePhoto", UIItem = "Root/btn_play/btnPhoto", slide = true, delayFinger = .5f });
            UIGuid.StartGuid(guidData);
        }

        /// <summary>
        /// 跳转到内城 点棋盘
        /// </summary>
        public static void JumpToCityToMerge()
        {
            var uimerge = PopupManager.I.FindPopup<UIMerge>();
            if (uimerge != null)
            {
                return; //已经打开
            }

            EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.CITY);

            var guidData = new UIGuidData();
            guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMain2", UIItem = "content/MenuRoot/Battle", slide = true });
            UIGuid.StartGuid(guidData, true);
        }


        public static void ShowCityGuid(int solderID = 0)
        {
            JumpToCity();
            //UIGuidData guidData;
            //if (GameData.I.MergeGameData.CanOpenAutoMerge)
            //{
            //    if (MapManager.I.MapOperation.GetCanCompose(solderID, out var soldier1, out var soldier2))
            //    {
            //        guidData = new UIGuidData();
            //        guidData.guidItems.Add(new UIGuidItem()
            //        {
            //            swip = new UIGuidSwip()
            //            {
            //                swip = true,
            //                startPos = GameInstance.MainCamera.WorldToScreenPoint(soldier1.landTran.position),
            //                endPos = GameInstance.MainCamera.WorldToScreenPoint(soldier2.landTran.position),
            //            }
            //            ,
            //            closeDelay = 2
            //        });
            //        UIGuid.StartGuid(guidData, true);
            //    }
            //    else
            //    {
            //        guidData = new UIGuidData();
            //        guidData.guidItems.Add(new UIGuidItem() { UIName = "UI.UIMain2", UIItem = "content/MenuRoot/Menu/Train", slide = true, closeDelay = 5 });
            //        UIGuid.StartGuid(guidData, true);
            //    }
            //}
            //else
            //{
            //    guidData = new UIGuidData();
            //    guidData.guidItems.Add(new UIGuidItem() { UIName = "UI.UIMain2", UIItem = "content/MenuRoot/Menu/Train", slide = true, closeDelay = 5 });
            //    UIGuid.StartGuid(guidData);
            //}
        }

        public static async UniTaskVoid JumapToHeroRecruit()
        {

            int _normalFreeTimes = 0;
            long _normalFreeCDRemindTime = 0;
            // var adData = AdShopMgr.I.GetAdGroupData(7);
            // if (adData != null)
            // {
            //     ////剩余的次数
            //     var remianLimit = adData.GetAdLeftTime();
            //     long remainTime = adData.GetLastTimeStamp() + adData.GetInterValTime() * 1000 - GameTime.Time;
            //     _normalFreeTimes = remianLimit;
            //     _normalFreeCDRemindTime = remainTime / 1000;
            // }
            await HeroRecruitMgr.I.OpenHeroRecruit(CallBack: () =>
            {
                Debug.LogWarning($"打开界面了");
                UIGuidData guidData;

                guidData = new UIGuidData();
                if (_normalFreeCDRemindTime > 0 || _normalFreeTimes == 0)
                {
                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIHeroRecruitNew", UIItem = "Root/GroupBtns/BtnLeft", slide = true, closeDelay = 5 });
                }
                else
                {
                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIHeroRecruitNew", UIItem = "Root/GroupBtns/BtnFree", slide = true, closeDelay = 5 });
                }

                UIGuid.StartGuid(guidData, true);
            });

        }

        public static void OpenActvPage()
        {
            if (!UIActivityMain.ShowActivityByCfgType(ActivityType.LavaTrials))
            {
                UITools.PopTips(LocalizationMgr.Get("Quest_EventNotOpen"));
            }
        }


        public static void OpenChangeName()
        {
            PopupManager.I.ShowLayer<UIPlayerMain>();
            PopupManager.I.ShowPanel<UIPlayerReName>();
        }

        public static void OpenCity()
        {
            //PopupManager.I.ShowLayer<UIMainCityLevelUp>();
        }

        public static void OpenDimensionalTreasure()
        {
            JumpToMainCity();
            UI.Treasure.UIDimensionalTreasure.TryOpen();
        }

        public static void OpenAllianceHelp()
        {
            if (LPlayer.I.UnionID == 0)
            {
                UI.TaskUtils.JumpToAlliance();
                return;
            }

            UIGuidData guidData;
            JumpToMainAllianceSetting();
            PopupManager.I.ShowLayer<UIAllianceHelpAndGift>(new UIAllianceGiftData()
            {
                showTab = UIAllianceGiftData.ShowTab.help
            }, onComplete: (sucess) =>
            {
                guidData = new UIGuidData();
                // guidData.guidItems.Add(new UIGuidItem() { UIName = "UIAllianceMain", UIItem = "Root/AlliancePage/Btns/Gift", slide = true, closeDelay = 5 });
                // guidData.guidItems.Add(new UIGuidItem() { UIName = "UIAllianceHelpAndGift", UIItem = "Root/Tab/tabs/tab2", slide = true, closeDelay = 5 });
                guidData.guidItems.Add(new UIGuidItem() { UIName = "UIAllianceHelpAndGift", UIItem = "Root/HelpRoot/Root/Bottom/Request/Btn", slide = true, closeDelay = 5 });
                UIGuid.StartGuid(guidData, true);
            });

        }

        public static void OpenAllianceTech()
        {
            if (LPlayer.I.UnionID == 0)
            {
                UI.TaskUtils.JumpToAlliance();
                return;
            }

            UIGuidData guidData;
            JumpToMainAllianceSetting();
            guidData = new UIGuidData();
            guidData.guidItems.Add(new UIGuidItem() { UIName = "UIAllianceMain", UIItem = "Root/AlliancePage/Btns/Tech", slide = true, closeDelay = 5 });
            UIGuid.StartGuid(guidData, true);
        }

        public static void OpenAllianceChallenge()
        {
            if (LPlayer.I.UnionID == 0)
            {
                UI.TaskUtils.JumpToAlliance();
                return;
            }

            UIGuidData guidData;
            JumpToMainAllianceSetting();
            guidData = new UIGuidData();
            guidData.guidItems.Add(new UIGuidItem() { UIName = "UIAllianceMain", UIItem = "Root/AlliancePage/Btns/Challenge", slide = true, closeDelay = 5 });
            UIGuid.StartGuid(guidData, true);
        }

        // public static void OpenHeroEquipment()
        // {
        //     JumpToMainCity();
        //     HeroEquipmentUI.Layer.OpenSafely();
        // }

        public static void OpenSummonAltar()
        {
            JumpToMainCity();
            SummonAltarMgr.I.OpenSummonAltarSafely();
        }

        public static void OpenWitchsBrew()
        {
            JumpToMainCity();
            //WitchsBrewUI.Layer.OpenSafely();
        }

        public static void OpenUIAllianceWarTerritoryList(int index)
        {
            if (LPlayer.I.UnionID == 0)
            {
                UI.TaskUtils.JumpToAlliance();
                return;
            }
            JumpToMainAllianceSetting();
            PopupManager.I.ShowPanel<UIAllianceWarTerritoryList>(new UIAllianceWarTerritoryListData()
            {
                openTabIndex = index
            });
        }
        /// <summary>
        /// 跳转王座
        /// </summary>
        public static void JumpToKingThrone()
        {
            if (GameData.I.ServerWarData.IsInServerWar)// || GameData.I.DragonWarData.SignData.ShieldDragonWaringAction()
            {
                FloatTips.I.FloatMsg(LocalizationMgr.Get("ErrCodeBackOrginalServer"));
                return;
            }
            if (DeadBeat.I.CheckIsDebt())
                return;
            EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.WORLD);
            TFW.Map.MapCameraMgr.MoveCameraToTarget(3510, 3510, "city_world", 0.5f, 0.8f, () => { }, true, true);
        }

        /// <summary>
        /// 普通招募
        /// </summary>
        public static void JumpToHeroRecruit()
        {
            int _normalFreeTimes = 0;
            long _normalFreeCDRemindTime = 0;
            // var adData = GameData.I.AdShopData.GetAdGroupData(7);
            // if (adData != null)
            // {
            //     ////剩余的次数
            //     var remianLimit = adData.GetAdLeftTime();
            //     long remainTime = adData.GetLastTimeStamp() + adData.GetInterValTime() * 1000 - GameTime.Time;
            //     _normalFreeTimes = remianLimit;
            //     _normalFreeCDRemindTime = remainTime / 1000;
            // }
            HeroRecruitMgr.I.OpenHeroRecruit(() =>
            {
                var guidData = new UIGuidData();
                if (_normalFreeCDRemindTime > 0 || _normalFreeTimes == 0)
                {
                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIHeroRecruitNew", UIItem = "Root/GroupBtns/BtnLeft", slide = true, closeDelay = 5 });
                }
                else
                {
                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIHeroRecruitNew", UIItem = "Root/GroupBtns/BtnFree", slide = true, closeDelay = 5 });
                }

                UIGuid.StartGuid(guidData, true);
            });
            //PopupManager.I.ShowLayer<UIHero_Upgrade_RecruitHero>(new UIHero_Upgrade_RecruitHeroData() { isGoToHero = false });
        }


        public static void JumpToAdvancedHeroRecruit()
        {
            HeroRecruitMgr.I.OpenHeroRecruit(() =>
            {
                var guidData = new UIGuidData();
                var endTime = HeroRecruitMgr.I.GetAdvanceEndingTime();
                bool show = (endTime - GameTime.Time / 1000) > 0;
                if (show)
                {
                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIHeroRecruitNew", UIItem = "Root/GroupBtns/BtnLeft", slide = true, closeDelay = 5 });
                }
                else
                {
                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIHeroRecruitNew", UIItem = "Root/GroupBtns/BtnFree", slide = true, closeDelay = 5 });
                }

                UIGuid.StartGuid(guidData, true);
            }, recruitType: 2);
        }

        public static void JumpToGoUIMain2()
        {
            PopupManager.I.ClearAllPopup();

            NTimer.CountDown(0.5f, () =>
            {
                var guidData = new UIGuidData();

                guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMain2", UIItem = $"content/MenuRoot/Alliance", slide = true });
                if (LPlayer.I.IsPlayerInUnion())
                {
                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIAlliance", UIItem = $"Root/Down/HelpItem" });
                }
                UIGuid.StartGuid(guidData);
            });
        }

        public static void JumpToCityToArmy()
        {
            PopupManager.I.ClearAllPopup();
            //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.PRISON);
            var ui = PopupManager.I.FindPopup<UIMainCity>();
            if (ui != null)
            {
                int cityCfgID = LPlayer.I.GetCityBuildingIDByType((int)MainCityItem.CityType.Barrack);
                ui.dragImage.MoveTOCfg(cityCfgID, () =>
                {

                    var guidData = new UIGuidData();

                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMainCity", UIItem = $"FullScreenBgCanvas/Root/BG1/Content/Building/build_{((int)MainCityItem.CityType.Barrack)}" });
                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UICreateSoldierInfoPanel", UIItem = $"Root/Content/btnAutoCreate", slide = true });
                    UIGuid.StartGuid(guidData);
                });
            }
        }

        public static void JumpToCityClickScience()
        {
            if (!UnlockMgr.I.CheckUnlock(UnlockFuncEnum.Building_6))
            {
                FloatTips.I.FloatMsg(UnlockMgr.I.GetUnlockDesc(UnlockFuncEnum.Building_6));
                return;
            }

            //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.PRISON);
            var ui = PopupManager.I.FindPopup<UIMainCity>();
            if (ui != null && ui.dragImage!=null)
            {
                PopupManager.I.ClearAllPopup();

                int cityCfgID = LPlayer.I.GetCityBuildingIDByType((int)MainCityItem.CityType.Academy);
                ui.dragImage.MoveTOCfg(cityCfgID, () =>
                {
                    var guidData = new UIGuidData();

                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMainCity", UIItem = $"FullScreenBgCanvas/Root/BG1/Content/Building/build_{(int)MainCityItem.CityType.Academy}" });

                    UIGuid.StartGuid(guidData);
                });
            }
        }

        
        public static void JumpToCityLevelUp(List<string> parms)
        { 
            if (parms?.Count > 0 && int.TryParse(parms[0], out int cityType))
            {
               
                if (!UnlockMgr.I.BuildUnlock((MainCityItem.CityType)cityType))
                {
                    return;
                }

                PopupManager.I.ClearAllPopup();

                //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.PRISON);
                var ui = PopupManager.I.FindPopup<UIMainCity>();
                int cityCfgID = LPlayer.I.GetCityBuildingIDByType(cityType);
                if (ui != null && ui.dragImage != null)
                {
                    ui.dragImage.MoveTOCfg(cityCfgID, () =>
                    {
                        var guidData = new UIGuidData();

                        var window = PopupManager.I.FindPopup<UIMainCity>();

                        string path = $"FullScreenBgCanvas/Root/BG1/Content/Building/build_{cityType}";
                        // 如果当前建筑未解锁。优先触发解锁逻辑
                        if (window != null)
                        { 
                            var rebuild = window.GameObject.transform.Find($"FullScreenBgCanvas/Root/BG1/Content/Building/{cityType * 1000}");

                            if (rebuild != null && rebuild.gameObject.activeInHierarchy)
                            {
                                path = $"FullScreenBgCanvas/Root/BG1/Content/Building/{cityType * 1000}";
                            }
                        }

                        guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMainCity", UIItem = path });

                        guidData.guidItems.Add(new UIGuidItem() { UIName = "UICityLevelUp", UIItem = $"Root/mBtnRoot/Button" });

                        UIGuid.StartGuid(guidData);
                    });
                }
            }
        }


        public static void JumpToChangeName()
        {
            PopupManager.I.ClearAllPopup();
            var guidData = new UIGuidData();
            guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMain2", UIItem = $"content/Left_Top/Head_k1" });
            guidData.guidItems.Add(new UIGuidItem() { UIName = "UIPlayerMain", UIItem = $"Content/Content/Info/RenameBtn" });
            UIGuid.StartGuid(guidData);
        }
    }
    #endregion
}

/// <summary>
/// 任务类型
/// </summary>
public enum QuestTypeID
{

    #region 新增任务类型
    /// <summary>
    /// 达到XXX关卡
    /// </summary>
    StageStatus = 101,
    /// <summary>
    /// 通过XXX关卡
    /// </summary>
    StageTimes = 102,

    /// <summary>
    /// 任意XX个英雄达到XX级
    /// </summary>
    HeroUpToLv = 201,
    /// <summary>
    /// 英雄升级XX次
    /// </summary>
    HeroUpTimes = 202,
    /// <summary>
    /// 任意XX个英雄达到XX星
    /// </summary>
    HeroNumToStar = 203,
    /// <summary>
    /// 英雄升星XX次
    /// </summary>
    HeroUpStarTimes = 204,
    /// <summary>
    /// 普通招募XX次
    /// </summary>
    HeroRecruitCommonTimes = 205,
    /// <summary>
    /// 高级招募XX次
    /// </summary>
    HeroRecruitHighTimes = 206,

    /// <summary>
    /// 普通或高级招募XX次
    /// </summary>
    HeroRecruitTimes = 207,

    /// <summary>
    /// XX英雄提升XX级
    /// </summary>
    HeroLevelUp = 208,

    /// <summary>
    /// XX英雄达到XX级
    /// </summary>
    HeroLevelUpOn = 209,

    /// <summary>
    /// 任意{0}个英雄达到{1}级
    /// </summary>
    Q215 = 215,
    Q211 = 211,

    /// <summary>
    /// 也是英雄跳转
    /// </summary>
    Q220 = 220,

    /// <summary>
    /// 配置部队
    /// </summary>
    Q216 = 216,

    /// <summary>
    /// 跳到科技场景
    /// </summary>
    SkillScene = 9999,

    /// <summary>
    /// 任意XX个科技达到XX级
    /// </summary>
    SkillNumToLv = 301,
    /// <summary>
    /// 科技升级XX次
    /// </summary>
    SkillUpTimes = 302,

    /// <summary>
    /// XX科技升级XX次
    /// </summary>
    SkillUpTimesById = 303,

    /// <summary>
    /// x色龙纹石达到{0}级
    /// </summary>
    Q304 = 304,

    /// <summary>
    /// x色龙纹石达到{0}级
    /// </summary>
    Q305 = 305,

    /// <summary>
    /// x色龙纹石达到{0}级
    /// </summary>
    Q306 = 306,

    /// <summary>
    /// x色龙纹石达到{0}级
    /// </summary>
    Q307 = 307,

    /// <summary>
    /// 训练士兵XX次
    /// </summary>
    ArmyTrainTimes = 401,
    /// <summary>
    /// 合成士兵XX次
    /// </summary>
    ArmyComposeTime = 402,
    /// <summary>
    /// XX士兵达到XX级
    /// </summary>
    ArmyNumToLv = 403,
    /// <summary>
    /// 合成XX士兵XX次
    /// </summary>
    ArmyComposeTimeByType = 404,
    /// <summary>
    /// XX士兵进化XX次
    /// </summary>
    ArmyEvoUp = 405,

    /// <summary>
    /// 弓箭手部队达到{0}阶
    /// </summary>
    Q406 = 406,
    /// <summary>
    /// 火法师部队达到{0}阶
    /// </summary>
    Q407 = 407,
    /// <summary>
    /// 冰法师部队达到{0}阶
    /// </summary>
    Q408 = 408,
    /// <summary>
    /// 哥布林部队达到{0}阶
    /// </summary>
    Q409 = 409,

    /// <summary>
    /// 挑战XX等级怪物XX次
    /// </summary>
    PVENumLvTimes = 501,
    /// <summary>
    /// 挑战任意等级怪物XX次
    /// </summary>
    PVEAllLvTimes = 502,
    /// <summary>
    /// 挑战XX等级野蛮人营地XX次
    /// </summary>
    PVERallyNumLvTimes = 503,
    /// <summary>
    /// 挑战任意等级野蛮人营地XX次
    /// </summary>
    PVERallyAllLvTimes = 504,
    /// <summary>
    /// 采集XXX金币
    /// </summary>
    PVECollectResNum = 505,

    /// <summary>
    /// 累计击杀{0}只巨兽哈卡
    /// </summary>
    PVEKillHaka = 509,

    /// <summary>
    /// 累计击杀{0}只熔岩哈卡
    /// </summary>
    PVEKillLavaHaka = 510,

    /// <summary>
    /// 参与PVPXX次
    /// </summary>
    PVPTimes = 601,

    /// <summary>
    /// 受到{0}伤害
    /// </summary>
    PVPHurt = 602,

    /// <summary>
    /// 消耗XX体力
    /// </summary>
    CostNumEngery = 701,
    /// <summary>
    /// 消耗XXX金币
    /// </summary>
    CostNumGold = 702,
    /// <summary>
    /// 使用XX个普通宝箱
    /// </summary>
    CostNumCommonBox = 703,
    /// <summary>
    /// 使用XX个白银宝箱
    /// </summary>
    CostNumSilverBox = 704,
    /// <summary>
    /// 使用XX个至尊宝箱
    /// </summary>
    CostNumGoldBox = 705,
    /// <summary>
    /// 登陆XX天
    /// </summary>
    LoginDayNum = 706,
    /// <summary>
    /// 加入联盟
    /// </summary>
    InAlliance = 707,
    /// <summary>
    /// 进入大世界
    /// </summary>
    InWorld = 708,
    /// <summary>
    /// 改名
    /// </summary>
    ChangName = 709,
    /// <summary>
    /// 城堡升级
    /// </summary>
    CityTechLevelUp = 710,
    /// <summary>
    /// 进化消耗
    /// </summary>
    CostEvoUp = 711,

    /// <summary>
    /// 跳转城堡升级
    /// </summary>
    JumpMainCastle = 712,

    /// <summary>
    /// 消耗XX个宝石
    /// </summary>
    JumpItemStore = 713,

    /// <summary>
    /// 充值XX次
    /// </summary>
    JumpCommonGift = 714,

    /// <summary>
    /// 累计充值跳转
    /// </summary>
    JumpGift = 1033,

    /// <summary>
    /// 竞技场战斗{0}次
    /// </summary>
    JumpArena = 724,

    /// <summary>
    /// 次元宝藏采集{0}秘银
    /// </summary>
    JumpDimensionalTreasure = 725,

    /// <summary>
    /// 累计治疗伤兵{0}分钟
    /// </summary>
    Q726 = 726,

    /// <summary>
    /// 累计迁城{0}次
    /// </summary>
    JumpMoveCity = 727,

    /// <summary>
    /// 累计联盟帮助{0}次
    /// </summary>
    Q731 = 731,

    /// <summary>
    /// 累计联盟捐献{0}次
    /// </summary>
    Q732 = 732,

    /// <summary>
    /// 铁匠铺达到{0}级
    /// </summary>
    Q733 = 733,

    /// <summary>
    /// 拥有{0}只召唤兽
    /// </summary>
    Q734 = 734,

    /// <summary>
    /// 光明等级达到{0}级
    /// </summary>
    Q735 = 735,

    /// <summary>
    /// 黑暗等级达到{0}级
    /// </summary>
    Q736 = 736,

    /// <summary>
    /// 完成X个情报站任务
    /// </summary>
    Q737 = 737,

    /// <summary>
    /// 总战力
    /// </summary>
    TotalPower = 801,
    /// <summary>
    /// 士兵战力达到
    /// </summary>
    SoliderPorwer = 802,

    /// <summary>
    /// 战力提升XX
    /// </summary>
    PowerUp = 803,

    /// <summary>
    /// 英雄战力提升XX
    /// </summary>
    PowerUpByHero = 804,

    /// <summary>
    /// 科技战力提升XX
    /// </summary>
    PowerUpBySkill = 805,

    /// <summary>
    /// 采集日任务跳转
    /// </summary>
    ActivityAcquisition = 806,
    /// <summary>
    /// 造兵次数
    /// </summary>
    TrainSoldierTimes = 2001,
    /// <summary>
    /// 消灭野怪个数
    /// </summary>
    AttackMonsterTimes = 2002,
    /// <summary>
    /// 内城消灭怪物个数
    /// </summary>
    DefendMonsterTimes = 2003,
    /// <summary>
    /// 通过关卡数
    /// </summary>
    PassLevelCount = 2004,
    /// <summary>
    /// 开启宝箱数
    /// </summary>
    OpenChestTimes = 2005,
    /// <summary>
    /// 消耗英雄卡个数
    /// </summary>
    MergeHeroTimes = 2006,
    /// <summary>
    /// 合成士兵次数
    /// </summary>
    MergeSoldierTimes = 2007,
    #endregion

    #region cs

    /// <summary>
    /// 开能量箱子XX次
    /// </summary>
    MergeOpenBigBoxTimes = 101,
    /// <summary>
    /// 合成棋盘道具XX次
    /// </summary>
    MergeItemTimes = 102,
    /// <summary>
    /// 合成XX个XX级道具
    /// </summary>
    MergeTargetTimes = 103,
    /// <summary>
    /// 解锁气泡XX次
    /// </summary>
    MergeUnlockTimes = 104,
    /// <summary>
    /// 解锁区域XX个
    /// </summary>
    MergeUnlockArea = 105,
    /// <summary>
    /// 解锁相册物件XX个
    /// </summary>
    MergeUnlockPhotoObj = 106,
    MergeUnlockPhotoObj2 = 107,
    /// <summary>
    /// 使用金币道具XX次
    /// </summary>
    MergeUseGoldItem = 108,
    /// <summary>
    /// 使用钻石道具XX次
    /// </summary>
    MergeUseDiamondItem = 109,
    /// <summary>
    /// 使用能量道具XX次
    /// </summary>
    MergeUseEnergyItem = 110,
    /// <summary>
    /// 重置专属箱子XX次
    /// </summary>
    MergeResetBox = 111,
    /// <summary>
    /// 开XX箱子XX次
    /// </summary>
    MergeOpenBoxTimes = 112,
    /// <summary>
    /// 重建XX建筑
    /// </summary>
    MergeRebuild = 116,
    /// <summary>
    /// 完成X个订单
    /// </summary>
    MergeOrder = 117,
    MergeOrder_1 = 118,
    // MergeOrder_2 = 119,

    /// <summary>
    /// 建筑A上阵1个主将英雄
    /// </summary>
    CityBuildToHero1 = 120,
    /// <summary>
    /// 建筑A上阵X个副将英雄
    /// </summary>
    CityBuildToHero2 = 121,
    /// <summary>
    /// 建筑A上阵X个辅助英雄
    /// </summary>
    CityBuildToHero3 = 122,
    /// <summary>
    /// 英雄升星XX次
    /// </summary>
    HeroUpStarTimes_1 = 213,
    /// <summary>
    /// 研究X次科技
    /// </summary>
    TechUpTimes = 311,
    TechUpTimes_1 = 312,
    TechUpTimes_2 = 313,
    /// <summary>
    /// 建筑等级达到XX
    /// </summary>
    CityBuildToLevel_2 = 314,
    CityBuildToLevel_1 = 315,
    CityBuildToLevel = 316,
    /// <summary>
    /// 获得X个士兵
    /// </summary>
    ArmyGeted = 410,
    /// <summary>
    /// 派遣X英雄抓囚犯次
    /// </summary>
    ArmyGetNum = 411,

    /// <summary>
    /// 进行X次采集（部队出发即完成任务）
    /// </summary>
    CollectRes = 507,

    ChangName_1 = 735,

    /// <summary>
    /// 完成X次雷达任务
    /// </summary>
    StargazingPlatformTask = 724,

    #endregion
}
