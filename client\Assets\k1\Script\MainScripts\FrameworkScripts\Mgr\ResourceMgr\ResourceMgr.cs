﻿using System;
using System.Diagnostics;
using System.IO;
using System.Threading;
using Cysharp.Threading.Tasks;
using Newtonsoft.Json;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UnityEngine.Serialization;
using UnityEngine.U2D;
using UnityEngine.UI;
using Object = UnityEngine.Object;

namespace TFW
{
    [DefaultExecutionOrder(-999)]
    public class ResourceMgr: MonoBehaviour
    {
        private static ResourceMgr _instance;

#if USE_ADDRESSABLE
        public static bool isUseAddressables => _instance._resourceMgrImplSO is AddressablesResourceMgrImpl;
#else
        public static bool isUseAddressables => false;
#endif

        [SerializeField]
        ScriptableObject _resourceMgrImplSO;

        [SerializeField]
        private bool _enableLog = true;

        [SerializeField]
        private string _debugLoadRecord = "Assets/k1/Script/MainScripts/FrameworkScripts/Mgr/ResourceMgr/Editor/AssetLoadRecord.asset";
        
        private IResourceMgrImpl _resourceMgrImpl => _resourceMgrImplSO as IResourceMgrImpl;
        
#region 包含在游戏内部的加载资源

        /// <summary>
        /// 加载内部资源数据
        /// </summary>
        /// <param name="tag"></param>
        /// <returns></returns>
        [Obsolete]
        public static SpriteAtlas LoadBuiltRes(string tag)
        {
            return _instance._resourceMgrImpl.LoadBuiltRes(tag);
        }

#endregion

        /// <summary>
        /// 检查Resource文件是否存在
        /// </summary>
        /// <param name="assetPath">资源路径</param>
        public static bool Exists(string assetPath)
        {
            return _instance._resourceMgrImpl.Exists(assetPath);
        }

        public static async UniTask PreloadAsync(string assetKey)
        {
             await ResourceMgr2.PreloadAsync(assetKey);
        }
        
        /// <summary>
        /// 加载资产，需配套 Release 释放资产
        /// </summary>
        public static T LoadAsset<T>(string assetKey) where T : Object
        {
            LoadDebugLog(assetKey);
            return _instance._resourceMgrImpl.LoadAsset<T>(assetKey);
        }

        public static void LateUpdate()
        {
             _instance._resourceMgrImpl.LateUpdate();
        }

        public static void ReleaseMemory()
        {
            _instance._resourceMgrImpl.ReleaseMemory();
        }

        /// <summary>
        /// 异步加载资产，需配套 Release 释放资产
        /// </summary>
        public static UniTask<T> LoadAssetAsync<T>(string assetKey)
            where T : Object
        {
            LoadDebugLog(assetKey);
            return _instance._resourceMgrImpl.LoadAssetAsync<T>(assetKey);
        }

        /// <summary>
        /// 异步加载 Stream，无需释放资产
        /// </summary>
        public static UniTask<Stream> LoadTextStreamAsync(string assetPath)
        {
            LoadDebugLog(assetPath);
            return _instance._resourceMgrImpl.LoadTextStreamAsync(assetPath);
        }

        /// <summary>
        /// 异步加载资产字byte[]，无需释放资产
        /// </summary>
        public static UniTask<byte[]> LoadTextBytesAsync(string assetPath)
        {
            LoadDebugLog(assetPath);
            return _instance._resourceMgrImpl.LoadTextBytesAsync(assetPath);
        }

        /// <summary>
        /// 加载资产实例，需配套 ReleaseInstance 释放资产
        /// </summary>
        public static GameObject LoadInstance(string assetKey, Transform parent = null)
        {
            LoadDebugLog(assetKey);
            return _instance._resourceMgrImpl.LoadInstance(assetKey, parent);
        }
        
        /// <summary>
        /// 异步加载资产实例，需配套 ReleaseInstance 释放资产
        /// </summary>
        public static UniTask<GameObject> LoadInstanceAsync(string assetKey, Transform parent = null)
        {
            LoadDebugLog(assetKey);
            return _instance._resourceMgrImpl.LoadInstanceAsync(assetKey, parent);
        }

         

        public static void Release(string assetKey)
        {
            _instance._resourceMgrImpl.Release(assetKey);
        }
        
        public static void ReleaseInstance(string assetKey)
        {
            _instance._resourceMgrImpl.ReleaseInstance(assetKey);
        }

        public static void ReleaseInstance(GameObject instance)
        {
            _instance._resourceMgrImpl.ReleaseInstance(instance);
        }
        
        [Obsolete]
        public static UniTask<Sprite> LoadSpriteInstanceAsync(string assetPath)
        {
            LoadDebugLog(assetPath);
            return _instance._resourceMgrImpl.LoadSpriteInstanceAsync(assetPath);
        }

        public static UniTask SetImageSpriteAsync(Image image, string spriteAssetPath, bool useNaticeSize = false)
        {
            LoadDebugLog(spriteAssetPath);
            return _instance._resourceMgrImpl.SetImageSpriteAsync(image, spriteAssetPath, useNaticeSize);
        }

        public static UniTaskVoid SetRawImageTextureAsync(RawImage rawImage, string textureAssetPath,
            bool useNaticeSize = false)
        {
            LoadDebugLog(textureAssetPath);
            return _instance._resourceMgrImpl.SetRawImageTextureAsync(rawImage, textureAssetPath, useNaticeSize);
        }

        /// <summary>
        /// 无需释放资产
        /// </summary>
        public static T DeserializeJSONFromAsset<T>(string path)
        {
            using var stream = ResourceMgr.LoadTextStream(path);
            using var sr = new StreamReader(stream);
            using var jsonTextReader = new JsonTextReader(sr);
            var serializer = new JsonSerializer();
            
            return serializer.Deserialize<T>(jsonTextReader);
        }
        
        /// <summary>
        /// 无需释放资产
        /// </summary>
        public static async UniTask<T> DeserializeJSONFromAssetAsync<T>(string path)
        {
            using var stream = await ResourceMgr.LoadTextStreamAsync(path);
            using var sr = new StreamReader(stream);
            using var jsonTextReader = new JsonTextReader(sr);
            var serializer = new JsonSerializer();
            
            return serializer.Deserialize<T>(jsonTextReader);
        }

        /// <summary>
        /// 以流形式加载文本资源
        /// 注：用后记得要配对调用UnloadText卸载资源。
        /// </summary>
        /// <param name="assetPath">资源数据体.</param>
        public static Stream LoadTextStream(string assetPath)
        {
            LoadDebugLog(assetPath);
            return _instance._resourceMgrImpl.LoadTextStream(assetPath);
        }

        /// <summary>
        /// 加载资产byte[]，并自动释放资产
        /// </summary>
        public static byte[] LoadTextBytes(string assetPath)
        {
#if UNITY_EDITOR
            if (!Application.isPlaying)
            {
                return File.ReadAllBytes(assetPath);
            }
#endif
            LoadDebugLog(assetPath);
            return _instance._resourceMgrImpl.LoadTextBytes(assetPath);
        }

        /// <summary>
        /// 销毁一个GameObject
        /// 资源不会放入通用缓存池中
        /// </summary>
        [Obsolete("使用 Release/ReleaseInstance 代替")]
        public static void Unload(GameObject obj)
        {
            _instance._resourceMgrImpl.Unload(obj);
        }

        public static void DebugLog(string msg)
        {
            D.Warning?.Log($"[ResourceMgr] {msg}");
        }
        
        static void LoadDebugLog(string assetKey)
        {
 
            if (_instance == null)
            {
                return;
            }

            AddLoadRecord(assetKey);
            if (_instance._enableLog)
            {
                var logStr = $"[ResourceMgr] Load: {assetKey}";
                UnityEngine.Debug.LogWarning(logStr);
            }
 
        }

        [Conditional("UNITY_EDITOR")]
        static void AddLoadRecord(string assetKey)
        {
#if UNITY_EDITOR
            if (_instance == null)
            {
                return;
            }
            var a = UnityEditor.AssetDatabase.LoadAssetAtPath<AssetLoadRecord>(_instance._debugLoadRecord);
            if (a != null)
            {
                a.AddRecord(assetKey);
            }
#endif
        }
        
        [Conditional("UNITY_EDITOR")]
        static void ClearLoadRecord()
        {
#if UNITY_EDITOR
            if (_instance == null)
            {
                return;
            }
            var a = UnityEditor.AssetDatabase.LoadAssetAtPath<AssetLoadRecord>(_instance._debugLoadRecord);
            if (a != null)
            {
                a.ClearRecord();
            }
#endif
        }

        private void Awake()
        {
            _instance = this;
            ClearLoadRecord();
        }
    }
}