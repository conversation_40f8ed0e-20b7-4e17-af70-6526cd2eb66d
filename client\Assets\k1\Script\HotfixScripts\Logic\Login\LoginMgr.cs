﻿







using Common;
using cspb;
using Game.Config;
using Game.Utils;
using GameState;
using Public;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Sockets;
using TFW;
using TFW.Localization;
using TFW.Map;
using THelper;
using UI;
using UnityEngine;
using UnityEngine.Serialization;
// #if USE_TGSBASIC && !UNITY_WEBGL
// using TGS;
// using TGS.Login;
// #endif
using MainScripts;
using Cysharp.Threading.Tasks;
using DeepUI;
using K3;
using KS;

namespace Logic
{
    /// <summary>
    /// 登录类型
    /// </summary>
    public class LoginAuthType
    {
        public static readonly IReadOnlyList<SDKLoginType> bingdingTypes = new List<SDKLoginType>()
        {
#if UNITY_IOS
                SDKLoginType.Apple,
#endif
            
                SDKLoginType.Line, //日本专用的
                SDKLoginType.Twitter,//日本专用的


            SDKLoginType.VK,

            SDKLoginType.GooglePlusV3,
                SDKLoginType.Facebook,
                SDKLoginType.Email,

        };

        public const string Anonymous = "anonymous";
        public const string Facebook = "facebook";
        public const string GooglePlusV3 = "googleplusv3";
        public const string Apple = "apple";
        public const string Email = "email";
        public const string Line = "line";
        public const string Twitter = "twitter";
        public const string VK = "vk";
        public const string SanQi = "sq_game";
        public const string ThreeSeven = "37";

        public static SDKLoginType GetSDKLoginType(string AuthType)
        {
            switch (AuthType)
            {
                case Facebook:
                    return SDKLoginType.Facebook;
                case GooglePlusV3:
                    return SDKLoginType.GooglePlusV3;
                case Apple:
                    return SDKLoginType.Apple;
                case Anonymous:
                    return SDKLoginType.Anonymous;
                case Email:
                    return SDKLoginType.Email;
                case Twitter:
                    return SDKLoginType.Twitter;
                case Line:
                    return SDKLoginType.Line;
                case ThreeSeven:
                    return SDKLoginType.ThreeSeven;
                case VK:
                    return SDKLoginType.VK;

            }

            return SDKLoginType.Unknown;
        }

        public static string GetAuthType(SDKLoginType type)
        {
            switch (type)
            {
                case SDKLoginType.Facebook:
                    return Facebook;
                case SDKLoginType.GooglePlusV3:
                    return GooglePlusV3;
                case SDKLoginType.Apple:
                    return Apple;
                case SDKLoginType.Email:
                    return Email;
                case SDKLoginType.Line:
                    return Line;
                case SDKLoginType.Twitter:
                    return Twitter;

                case SDKLoginType.VK:
                    return VK;

                case SDKLoginType.ThreeSeven:
                    return ThreeSeven;
                default:
                    return Anonymous;
            }
        }
        // #if USE_TGSBASIC

        //         public static TGS.Login.Platform GetAuthPlatform(SDKLoginType type)
        //         {
        //             switch (type)
        //             {
        //                 case SDKLoginType.Facebook:
        //                     return TGS.Login.Platform.Facebook;
        //                 case SDKLoginType.GooglePlusV3:
        //                     return TGS.Login.Platform.GooglePlusV3;
        //                 case SDKLoginType.Apple:
        //                     return TGS.Login.Platform.Apple;
        //                 case SDKLoginType.Email:
        //                     return TGS.Login.Platform.Email;
        //                 case SDKLoginType.Line:
        //                     return TGS.Login.Platform.Line;
        //                 case SDKLoginType.Twitter:
        //                     return TGS.Login.Platform.Twitter;

        //                 case SDKLoginType.VK:
        //                     return TGS.Login.Platform.VK;

        //                 default:
        //                     return TGS.Login.Platform.Anonymous;
        //             }
        //         }

        //         public static SDKLoginType GetSDKLoginType(TGS.Login.Platform type)
        //         {
        //             switch (type)
        //             {
        //                 case TGS.Login.Platform.Facebook:
        //                     return SDKLoginType.Facebook;
        //                 case TGS.Login.Platform.GooglePlusV3:
        //                     return SDKLoginType.GooglePlusV3;
        //                 case TGS.Login.Platform.Apple:
        //                     return SDKLoginType.Apple;
        //                 case TGS.Login.Platform.Email:
        //                     return SDKLoginType.Email;
        //                 case TGS.Login.Platform.Line:
        //                     return SDKLoginType.Line;
        //                 case TGS.Login.Platform.Twitter:
        //                     return SDKLoginType.Twitter;

        //                 case TGS.Login.Platform.VK:
        //                     return SDKLoginType.VK;

        //                 default:
        //                     return SDKLoginType.Anonymous;
        //             }
        //         }
        // #endif

    }

    /// <summary>
    /// 角色登录时间排序
    /// </summary>
    public class LoginTimeComparer : IComparer<CharacterData>
    {
        public int Compare(CharacterData x, CharacterData y)
        {
            if (x.lastlogintime == y.lastlogintime)
            {
                return 0;
            }

            return x.lastlogintime > y.lastlogintime ? -1 : 1;
        }
    }


    /// <summary>
    /// 本地储存账户信息
    /// </summary>
    [Serializable]
    public class AccountSaveData
    {
        public List<CharacterData> Account_List = new List<CharacterData>();
    }

    /// <summary>
    /// 缓存账号相关信息  当前SDK登录 使用UserName   logintype
    /// </summary>
    [Serializable]
    public class CharacterData
    {
        public string username;
        public int level;
        public int AvatarCfgID;
        public int headFrameID;
        public string headurl = "";
        public string logintype = LoginAuthType.Anonymous;
        public string password;
        public long power;
        public int serverid;
        public string servername;
        public long playerid;
        public long lastlogintime;
        public bool useCustomUrl;
        public CharacterAction action = CharacterAction.None;
    }

    /// <summary>
    /// 登录时的角色操作类型
    /// </summary>
    public enum CharacterAction
    {
        None,           //无操作
        LoginRole,      //登录指定角色
        CreatRole,      //创建角色
        SDKLogin,       //第三方登录
        LoginServer,    //登录制定服务
    }

    public enum SubmitAction
    {
        None = 0,       //无操作
        CreatRole,      //创建角色
        LoginRole,      //登录角色
        RoleLevelUp,    //角色升级
    }

    /// <summary>
    /// 当前登录重要信息
    /// </summary>
    public class CurrentLoginData
    {
        public string username;
        public string logintype = LoginAuthType.Anonymous;
        public string passport;
        public string password;
        public int serverid;
        public string servername;
        public long accountID;
        public long playerid;
        public string accessToken;

        public string sign;
        public string timestamp;
        /// <summary>
        /// 37用的
        /// </summary>
        public string uid;

        /// <summary>
        /// 平台Auth2认证授权用户名
        /// </summary>
        /// <see cref="https://wiki..com/pages/viewpage.action?pageId=********"/>
        //public string pfAuthUser => $"{logintype}:{passport}";

        public CurrentLoginData()
        {
            Reset();
        }

        public void Reset()
        {
            username = string.Empty;
            logintype = LoginAuthType.Anonymous;
            passport = string.Empty;
            sign = string.Empty;
            timestamp = string.Empty;
            password = SDKConfig.SDKPassword;
            servername = string.Empty;
            serverid = 0;
            accountID = 0;
            playerid = 0;
        }
    }


    public partial class LoginMgr : Ins<LoginMgr>, IBeforeLoginCallback
    {

        /// <summary>
        /// 是否为启动游戏登录
        /// </summary>
        public static bool IS_START_GAME_LOGIN = true;
        public static bool IS_CHANGESQ_ACCOUNT = false;
        public static bool IS_LOGINSQ_SUCCESS = false;
        private const string PPF_LastLoginServerID = "login_server_temp";
        public static string m_LoginUsernameTempPf = "monkey_";
        public static List<string> Account_BindingTypes = new List<string>(); //当前账户所有绑定类型
                                                                              // #if USE_TGSBASIC && !UNITY_WEBGL
                                                                              //         public static LoginUserData vkLoginUserData;
                                                                              // #endif
        public static SDKLoginUserInfo vkSDKLoginUserInfo;
        public static bool SwitchVK;
        public bool IsNewAccounts = true;
        /// <summary>
        /// 绑定账号中
        /// </summary>
        public bool BindAccounting;

        //public static readonly string[] NowOpenSDK =
        //{
        //    LoginAuthType.GooglePlusV3, LoginAuthType.Apple, LoginAuthType.Anonymous, LoginAuthType.Facebook
        //};

        /// <summary>
        /// 客户端标记(记录使用)
        /// 当两个相同请求不同位置发送的时候，对于返回的处理不一样的时候使用。
        /// </summary>
        public enum SendMessageState
        {
            Normal = 1,     //1号位置默认位置
            OtherPos = 2,   //其它位置标号
        }

        //public event Action onSDKCharacterInfoInitialized;

        /// <summary>
        /// 登录返回信息保存
        /// </summary>
        private LoginAck m_NowLoginAck;

        /// <summary>
        /// 平台反馈  SDKAuth.SetCharacterInfo错误导致的登录到新角色问题
        /// 平台给出方案：在每次 调用 SDK.I.Login 之前 保存一下登录类型，
        /// 在执行 SDKAuth.SetCharacterInfo 时再判断一下登录类型是已经变化了，
        /// 如果变化了就没必要执行 SDKAuth.SetCharacterInfo 了 
        /// </summary>
        public Queue<SDKLoginType> SDKLoginTypes;

        public long CreateTS
        {
            get;
            private set;
        }

        /// <summary>
        /// 是否已经登录
        /// </summary>
        public bool IsLogin => m_NowLoginAck != null && m_NowLoginAck.errCode == ErrCode.ErrCodeSuccess;

        /// <summary>
        /// 账户绑定信息保存
        /// </summary>
        private cspb.AccountInfo m_AccountInfo;

        /// <summary>
        /// 服务器列表
        /// </summary>
        public Dictionary<int, Server> m_Servers;
        //public Dictionary<long, Character> m_Characters;

        /// <summary>
        /// 当前登录的角色信息
        /// </summary>
        public CurrentLoginData loginData = new CurrentLoginData();

        /// <summary>
        /// 获取角色jwt
        /// </summary>
        public string CharacterJwt
        {
            get; set;
        }

        /// <summary>
        /// 服务器白名单列表
        /// </summary>
        public Dictionary<int, bool> m_ServersWhiteListDic;

        /// <summary>
        /// 平台验证信息保存
        /// </summary>
        private AuthAck m_NowAuthAck;
        /// <summary>
        /// 平台验证信息保存
        /// </summary>
        public AuthAck NowAuthAck => m_NowAuthAck;

        //骨骼框架是否可以使用
        public bool IsCanGooglePlay = true;

        //当前渠道字符串
        public string GameProvider = "";

        /// <summary>
        /// 所有可用的网关地址
        /// </summary>
        public string[] GateAddress;

        /// <summary>
        /// 当前指向的网关地址
        /// </summary>
        public int CurGateAddressIdx;

        /// <summary>
        /// 会话id,每次登录成功更新一次
        /// 用于数据打点
        /// </summary>
        public string SessionID
        {
            get;
            private set;
        }

        /// <summary>
        /// 服务器限制信息
        /// 老版本进新客户端
        /// </summary>
        Dictionary<int, string> serLimitInfo;

        /// <summary>
        /// 初始化
        /// </summary>
        public void Init()
        {
            IS_START_GAME_LOGIN = K1D2Config.I.IsShowLoginPanel;

            Account_BindingTypes.Clear();
            m_Servers = new Dictionary<int, Server>();
            //m_Characters = new Dictionary<long, Character>();
            m_ServersWhiteListDic = new Dictionary<int, bool>();
            serLimitInfo = new Dictionary<int, string>();
            SDKLoginTypes = new Queue<SDKLoginType>();
            if (Application.platform == RuntimePlatform.Android)
            {
                //检查下手机设备
                IsCanGooglePlay = GameUtils.IsPlayServicesAvailable();
#if PLATFORM_FLEXION
                //获取渠道字符串
                GameProvider = GameUtils.GetFlexionChannelName();
#endif
            }
            LoadCacheAccounts();
            MessageMgr.UnregisterMsg<VersionLimitAck>("OnVersionLimitAck");
            MessageMgr.RegisterMsg<VersionLimitAck>("OnVersionLimitAck", OnVersionLimitAck);

            MessageMgr.UnregisterMsg<ServersAck>(this);
            MessageMgr.RegisterMsg<ServersAck>(this, OnServersAck);

            //Test
            //serLimitInfo.Add(1, "1.0.186");
            //serLimitInfo.Add(612, "1.0.187");
        }

        public List<CharacterData> FindRoleByServerId(int serverId)
        {
            List<CharacterData> data = new List<CharacterData>();
            foreach (var item in CacheAccounts.Account_List)
            {
                if (item.serverid == serverId && item.action == CharacterAction.None)
                    data.Add(item);
            }
            return data;
        }

    
        /// <summary>
        /// 版本限制信息
        /// </summary>
        /// <param name="obj"></param>
        private void OnVersionLimitAck(VersionLimitAck obj)
        {
            serLimitInfo = obj.limitMap;
        }

        /// <summary>
        /// 检测该服务器id是否在限制列表中
        /// </summary>
        /// <param name="serId"></param>
        /// <returns></returns>
        public bool CheckIsLimit(int serId)
        {
            if (serLimitInfo != null
                && serLimitInfo.TryGetValue(serId, out var lowestVerStr))
            {
                var localVerStr = Application.version;
                D.Debug?.Log("CheckIsLimit localVer={0},limit lowestVerStr={1}", localVerStr, lowestVerStr);
                var localVer = new VersionCode(localVerStr);
                var serverVer = new VersionCode(lowestVerStr);
                var result = localVer.Compare(serverVer);
                var needUpdate = result == -1;
                if (needUpdate)
                {
                    FloatTips.I.FloatMsg(LocalizationMgr.Get("ErrCodeVersionLimit"));
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 标识已经登录
        /// </summary>
        /// <param name="loginAck"></param>
        public void Login(LoginAck loginAck)
        {
            m_NowLoginAck = loginAck;
            CreateTS = loginAck.createTs;
            LPlayer.I.ServerId = loginAck.serverId;
            loginData.playerid = loginAck.playerID; //aics在获取jwt前必须保证playerid是正确的
            //GetVersionLimitInfo();

            var lastCharacterInfo = GetLastCharacter();
            if (lastCharacterInfo != null)
            {
                lastCharacterInfo.playerid = loginAck.playerID;
                lastCharacterInfo.serverid = loginAck.serverId;
                var server = FindServerById(loginAck.serverId);
                if (server != null)
                    lastCharacterInfo.servername = server.name;
                lastCharacterInfo.action = CharacterAction.LoginRole;
                SetCharacterOrder(lastCharacterInfo, 0);
                SaveAccounts();
            }
        }

        public bool ContainServer(int serverID)
        {
            if (m_NowAuthAck?.servers.Count > 0)
            {
                foreach (var item in m_NowAuthAck.servers)
                {
                    if (item.serverID == serverID)
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        //private void GetVersionLimitInfo()
        //{
        //    VersionLimitReq req = new VersionLimitReq();
        //    MessageMgr.Send(req);
        //}


        public void SetCharacterInfo()
        {
            // #if USE_TGSBASIC && !UNITY_WEBGL
            //             if (K1D2Config.I.IsFastLoginGold
            //                 || K1D2Config.I.IsFastLoginDev
            //                 || K1D2Config.I.IsFastLoginBate)
            //             {
            //                 return;
            //             }

            //             if (V3SDKManager.Instance.AccountToken == null || V3SDKManager.Instance.AccountToken.Length == 0)
            //             {
            //                 //账号未设置成功
            //                 //BILog.UserFte("SetCharacterInfoError", "SetCharacterInfo", new JsonBIObj($"SetCharacterInfo  账号未登录，就去设置角色Token!", //BILogInfo.GetBIFTEInfo()));
            //                 return;
            //             }


            //             SDKCharacterInfo info = new SDKCharacterInfo();
            //             info.CharacterId = m_NowLoginAck.playerID.ToString(); // 必填参数,其余选填
            //             info.ServerId = m_NowLoginAck.serverId.ToString(); // 必填参数,其余选填       

            //             //执行 SDKAuth.SetCharacterInfo 时再判断一下登录类型是已经变化了，如果变化了就没必要执行 SDKAuth.SetCharacterInfo 了
            //             if (LoginMgr.I.SDKLoginTypes.Count > 1)
            //             {
            //                 LoginMgr.I.SDKLoginTypes.Dequeue();
            //                 //BILog.UserFte("SDKLoginTypes", "SDKLoginTypes", new JsonBIObj($"SDKLoginTypes Count > 1", //BILogInfo.GetBIFTEInfo()));
            //                 return;
            //             }

            //             // 平台部 SDK 配置角色信息
            //             SDKAuth.SetCharacterInfo(info, (code, message, data) =>
            //             {
            //                 if (LoginMgr.I.SDKLoginTypes.Count > 0)
            //                     LoginMgr.I.SDKLoginTypes.Dequeue();

            //                 D.Debug?.Log($"SetCharacterInfo code = {code} \n data = {data} \n message = {message}");
            //                 if (code == SDKAuth.AuthV3CallbackCode.AUTH_SET_CHARACTER_SUCCESS)
            //                 {
            //                     //BILog.UserFte("SetCharacterInfoSuccess", $"SetCharacterInfo{info.ToString()}", new JsonBIObj($"SetCharacterInfo  code={code} message={message} data={data} !", //BILogInfo.GetBIFTEInfo()));

            //                     var authorize = JsonUtility.FromJson<Auth2Authorize>(data.ToString());
            //                     LoginMgr.I.CharacterJwt = authorize.jwt;
            //                     D.Debug?.Log("获取JWTToken 成功!" + LoginMgr.I.CharacterJwt);
            //                     //LPay.I.DoInitSDK(null);
            //                     //SDK.I.InitPushSDK();

            //                     //BuglyAgent.SetUserId(LPlayer.I.PlayerID.ToString());


            //                     //callBack?.Invoke();
            //                     //this.onSDKCharacterInfoInitialized?.Invoke();
            //                 }
            //                 else
            //                 {
            // #if UNITY_EDITOR

            // #else
            //                     D.Error?.Log($"获取JWTTokenError:  {message} : {data}!");
            //                     //BILog.UserFte("SetCharacterInfoError", "SetCharacterInfo", new JsonBIObj($"SetCharacterInfo  code={code} message={message} data={data} !", //BILogInfo.GetBIFTEInfo()));

            //                     NTimer.CountDown(5f, () =>
            //                     {
            //                         SetCharacterInfo();
            //                     });
            // #endif
            //                 }
            //             });
            // #endif
        }

        /// <summary>
        /// 更新登录会话id-每次登录成功刷新
        /// 用于数据打点
        /// </summary>
        public void UpdateSessionID(long sessionId)
        {
            //var timestamp = DateTime.Now.Ticks;
            //var open_udid = PlatformInfo.I.GetUDID();
            //var user_id = LPlayer.I.PlayerID;
            //this.SessionID = NetUtils.GetMd5(open_udid + user_id + timestamp);

            this.SessionID = sessionId.ToString();
        }

        /// <summary>
        /// 标识已经登出
        /// </summary>
        public void Logout()
        {
            m_NowLoginAck = null;
            CreateTS = 0;
        }

        /// <summary>
        /// 获取上次登录的服务器ID
        /// </summary>
        public static int GetLastLoginServerId()
        {
            return PlayerPrefs.GetInt(PPF_LastLoginServerID);
        }

        /// <summary>
        /// 保存上次登录的服务器ID
        /// </summary>
        public static void SaveLastLoginServerId(int serverID)
        {
            PlayerPrefs.SetInt(PPF_LastLoginServerID, serverID);
        }

        /// <summary>
        /// 获取首次角色命名
        /// </summary>
        /// <returns></returns>
        public static string GetFirstName()
        {
            // 修正路径 加上路径 区别不同工程
#if UNITY_EDITOR
            return $"{Application.dataPath}_{m_LoginUsernameTempPf}";
#else
            string m_AccountName = m_LoginUsernameTempPf + (System.DateTime.Now.Ticks / 10000 / 1000 % *********);
            return m_AccountName.Replace("\r\n", string.Empty);
#endif
        }

        /// <summary>
        /// 获取当前服务器列表
        /// </summary>
        /// <param name="includeself">是否包含已有角色服务器</param>
        /// <returns></returns>
        public List<Server> GetSeverList(bool includeself = true)
        {
            List<Server> serverLists = m_NowAuthAck.servers;
            if (!includeself)
                serverLists = m_NowAuthAck.servers.FindAll(s => FindRoleBySeverId(s.serverID) == null);

            serverLists.Sort((x, y) =>
            {
                if (y == null)
                    return -1;
                else if (x == null)
                    return 1;
                else
                    return y.weight - x.weight;
            });

            return serverLists;
        }

        /// <summary>
        /// 获取已有角色的服务器列表
        /// </summary>
        /// <param name="includeself">是否包含已有角色服务器</param>
        /// <returns></returns>
        public List<Server> GetCharacterSeverList()
        {
            return m_NowAuthAck.servers.FindAll(s => FindRoleBySeverId(s.serverID) != null);
        }

        public Server GetServerDataByServerId(int serverId)
        {
            List<Server> servers = m_NowAuthAck.servers;
            for (int i = 0; i < servers.Count; i++)
            {
                if (servers[i].serverID == serverId)
                    return servers[i];
            }

            D.Error?.Log("无服务器可选");
            return new Server() { serverID = serverId };
        }

        #region 服务器列表相关

        private bool serverReq;

        public void ServersReq()
        {
            if (serverReq)
                return;

            MessageMgr.Send(new ServersReq());
        }

        public void OnServersAck(ServersAck ntf)
        {
            serverReq = true;

            if (m_NowAuthAck == null || ntf.servers.Count == 0)
                return;

            m_NowAuthAck.servers.Clear();
            var count = ntf.servers.Count;
            for (int i = 0; i < count; i++)
            {
                m_NowAuthAck.servers.Add(ntf.servers[i]);
            }
        }

        public List<Server> GetRecommendedServerList()
        {
            if (m_NowAuthAck == null)
                return new List<Server>();

            List<Server> servers = m_NowAuthAck.servers;
            return SortReCommandServer(servers);
        }

        /// <summary>
        /// 获取赛季列表
        /// </summary>
        /// <returns></returns>
        [System.Obsolete("没有赛季这一说了")]
        public List<Server> GetServerListBySeason(int Season)
        {
            if (m_NowAuthAck == null)
                return new List<Server>();

            List<Server> servers = m_NowAuthAck.servers.FindAll(s => GetSeasonByServerName(s) == Season);
            return SortReCommandServer(servers);
        }

        private int GetServerNameTryInt(Server _server)
        {
            int s = 0;
            if (int.TryParse(_server.name, out var ServerIndex))
            {
                s = ServerIndex;
            }
            else
            {
                s = _server.serverID;
            }
            return s;
        }

        private List<Server> SortReNewCommandServer(List<Server> list)
        {
            List<Server> sortList = new List<Server>();
            List<Server> TempList = new List<Server>();

            if (NowAuthAck != null)
            {
                foreach (var serverId in NowAuthAck.newRecommandServers)
                {
                    foreach (var s in list)
                    {
                        if (GetServerNameTryInt(s) == serverId)
                        {
                            TempList.Add(s);
                        }
                    }
                }
            }

            OrderByServerName(ref TempList);
            sortList.AddRange(TempList);
            return sortList;
        }


        private List<Server> SortReCommandServer(List<Server> list)
        {
            Server recommand = list.Find(s => GetServerNameTryInt(s) == NowAuthAck.recommandServer);
            List<Server> sortList = new List<Server>();
            List<Server> TempList = new List<Server>();
            if (recommand != null)
            {
                sortList.Add(recommand);
                //sortList.Add(recommand); //不知道为什么要添加两次推荐服
            }
            for (int i = 0; i < list.Count; i++)
            {
#if UNITY_EDITOR
                if (GetServerNameTryInt(list[i]) != NowAuthAck.recommandServer)
                {
                    TempList.Add(list[i]);
                }
#else
                if (GetServerNameTryInt(list[i]) != NowAuthAck.recommandServer && !ChackServerIsMerge(list[i].serverID))
                {
                    TempList.Add(list[i]);
                }
#endif
            }

            OrderByServerName(ref TempList);
            sortList.AddRange(TempList);
            return sortList;
        }

        //按数字从小到大排序
        private void OrderByServerName(ref List<Server> list)
        {
            for (int i = 0; i < list.Count; i++)
            {
                for (int j = 1; j < list.Count - i; j++)
                {
                    if (GetServerNameTryInt(list[j - 1]) < GetServerNameTryInt(list[j]))
                    {
                        var temp = list[j - 1];
                        list[j - 1] = list[j];
                        list[j] = temp;
                    }
                }
            }
        }
        //暂时不用了这是原设定合服服务器
        private bool ChackServerIsMerge(int ServerId)
        {
            return ServerId >= 9001 && ServerId <= 9999;
        }

        public bool ChackServerOpen(int serverId)
        {
            for (int i = 0; i < m_NowAuthAck.servers.Count; i++)
            {
                if (m_NowAuthAck.servers[i].serverID == serverId)
                    return true;
            }
            return false;
        }

        //得到服务器属于哪个赛季(应该按名字强转int进行判断，测试服无法完成加入无法强转改为ID判断)
        private int GetSeasonByServerName(Server _server)
        {
            int passServer = -1;
            if (null == _server)
                return passServer;
            //string serverName = _server.name;

            //if (_server.serverID == 2001 || _server.serverID == 2002 || _server.serverID >= 9000)
            //{
            //    if (Loading_Res.Ins.IsFastLoginGold || Loading_Res.Ins.IsFastLoginDev || Loading_Res.Ins.IsFastLoginBeta)
            //        passServer = 1;
            //    else
            //        passServer = 2;
            //}
            //else
            //{
                passServer = 1;
            //}
            return passServer;
        }

        #endregion

        /// <summary>
        /// 获取推荐服务器
        /// </summary>
        /// <returns></returns>
        public int GetRecommandSever()
        {
            return m_NowAuthAck.recommandServer;
        }

        /// <summary>
        /// 判断当前是否在白名单中
        /// </summary>
        /// <returns></returns>
        public bool IsInWhiteList(int serverId = -1)
        {
            if (m_ServersWhiteListDic == null || m_ServersWhiteListDic.Count == 0)
                return false;

            if (serverId == -1)
            {
                //当前获取不到服务器ID，那么就拿第一个服的id信息
                var ret = m_ServersWhiteListDic.Values.First();
                return ret;
            }
            else
            {
                m_ServersWhiteListDic.TryGetValue(serverId, out var isIn);
                return isIn;
            }


        }

        ///// <summary>
        ///// 存储绑定信息
        ///// </summary>
        ///// <param name="playerid"></param>
        ///// <param name="logintype"></param>
        //public void SaveLoginTypeRole(long playerid, string logintype, string userName, string password)
        //{
        //    foreach (var item in CacheAccounts.Account_List)
        //    {
        //        if (item.playerid == playerid)
        //        {
        //            item.logintype = logintype;
        //            item.username = userName;
        //            item.password = password;
        //            break;
        //        }
        //    }
        //    D.Debug?.Log(" save login tyoe " + playerid + " 1234 " + logintype);
        //    //D.Error?.Log($"保存本地账号数据{1}");
        //    SaveAccounts();
        //    AddBingInfo(logintype);
        //}

        /// <summary>
        /// 设置当前平台登录信息
        /// </summary>
        /// <param name="auth"></param>
        public AuthAck SetAuthData(AuthAck auth)
        {
            serverReq = false;
            m_NowAuthAck = auth;

            m_NowAuthAck.characters.Sort(new CharacterLoginTimeComparer());

            long need_first_playerid = 0;

            //运行时同步服务器账号删除本地缓存账号 ps:除开有行为账号
            CacheAccounts.Account_List = CacheAccounts.Account_List.Where(x =>
            {
                //判断是否为登录指定角色行为
                if (x.action == CharacterAction.LoginRole)
                {
                    need_first_playerid = x.playerid;
                }

                return x.action != CharacterAction.None
                    && x.action != CharacterAction.LoginRole;
                //&& m_NowAuthAck.characters.Find(data => data.playerID == x.playerid) != null;
            }).ToList();

            AuthDatasList.Clear();
            //比对本地缓存账号 服务器有则添加进缓存
            foreach (var character in m_NowAuthAck.characters)
            {
                if (character.meta == null)
                {
                    D.Warning?.Log($"character.meta is null, playerID {character.playerID}");

                    continue;
                }
                var serveritem = FindServerById(character.serverID);
                var server_name = serveritem == null ? "" : serveritem.name;

                var characterData = new CharacterData
                {
                    username = character.meta.name,
                    serverid = character.serverID,
                    playerid = character.playerID,
                    servername = server_name,
                    level = character.meta.cityLvl,
                    lastlogintime = character.lastLogin,
                    logintype = m_NowAuthAck.type,
                    action = CharacterAction.None,
                    power = character.meta.power,
                };

                if (character.meta?.head != null)
                {
                    characterData.headurl = character.meta.head.customHead;
                    characterData.AvatarCfgID = character.meta.head.avatarCfgID;
                    characterData.headFrameID = character.meta.head.avatarFrrame;
                    characterData.useCustomUrl = character.meta.head.useCustomHead;
                }

                var role_data = CacheAccounts.Account_List.Find(data => data.playerid == character.playerID);
                if (role_data == null)
                {
                    ///这个list合并了accountAck数据 不是很靠谱
                    CacheAccounts.Account_List.Add(characterData);
                }

                ///添加选择角色界面显示数据
                AuthDatasList.Add(characterData);
            }

            //把需要登录指定角色排序到最前
            if (need_first_playerid > 0)
            {
                var role_data = CacheAccounts.Account_List.Find(data => data.playerid == need_first_playerid);
                if (role_data != null) SetCharacterOrder(role_data, 0);
            }
            //LogoLoading.ins.UpdateLoadingStep(LogoLoadingStepEnum.UpdateLocalCacheCharacterInfo);
            return m_NowAuthAck;
        }

        /// <summary>
        /// 本次登录的时候失败次数
        /// </summary>
        public static int LoginFailCount = 0;

        /// <summary>
        /// 登录错误弹框
        /// </summary>
        /// <param name="action"></param>
        public static void ShowLogfailWindow(LoginFailEnum failEnum, string errCodeParams = "")
        {
            UITools.Loading(false);

            string msg = LocalizationMgr.Get("System_Maintain_Info_Content");
            /*
             * System_Maintain_Info_Content_timeOut        超时类（4，5,9） 大概率出现
System_Maintain_Info_Content_authRefuse   认证拒绝类(6、7、8)
System_Maintain_Info_Content_loginFail        游戏服务器登录失败（1、2、3）
             */

            //上传日志
            //ClientLogMgr.StartRequest();

            switch (failEnum)
            {
                case LoginFailEnum.HLAutoLoginFail:
                case LoginFailEnum.GSAutoLoginFail:
                case LoginFailEnum.LastServerIDFail:
                    msg = LocalizationMgr.Get("System_Banned_Info_Content");
                    break;
                case LoginFailEnum.GSGatewayFail:
                case LoginFailEnum.GSAuthFail:
                case LoginFailEnum.HLNetDisconnect:
                    msg = LocalizationMgr.Get("System_Maintain_Info_Content_timeOut");
                    break;
                case LoginFailEnum.SDKLoginFail:
                case LoginFailEnum.HLAuthFail:
                case LoginFailEnum.HLGatewayFail:
                    msg = $"{LocalizationMgr.Get("System_Maintain_Info_Content_authRefuse")}";
                    break;
                case LoginFailEnum.VersionLimit:
                    msg = LocalizationMgr.Get("ErrCodeVersionLimit");
                    break;
                default:
                    break;
            }

            if (!string.IsNullOrEmpty(errCodeParams))
            {
                msg = $"{msg} [{errCodeParams}]";
            }


            var dialog = PopupManager.I.FindPopup<UILoginTipsDialog>();
            if (dialog != null && dialog.IsShow)
                PopupManager.I.ClosePopup<UILoginTipsDialog>();


            K3.K3GameEvent.I.TaLog(new ReconnectFailEvent());

            PopupManager.I.ShowDialog<UILoginTipsDialog>(new UILoginTipsDialogData() { desc = msg });
        }

        /// <summary>
        /// 切换账号
        /// </summary>
        /// <param name="_"></param>
        public static void SwitchAccount(object _)
        {
            DeepUI.PopupManager.I.ShowPanel<UILogin>(new UIRoleSwitchData()
            {
                displayCloseBtn = false
            });
        }

        /// <summary>
        /// 跳转客服
        /// </summary>
        /// <param name="_"></param>
        public static void OpenAIHelp(object _)
        {
            D.Debug?.Log("点击跳转客服");
            //LogoLoadFail.isStopIEnum = true;
            // #if USE_TGSBASIC && !UNITY_WEBGL

            //             //不是停服维护的时候才发送打点信息
            //             if (!GameConfig.IsAppMaintain
            //                 && !GameConfig.IsColdServer)
            //             {

            //                 AIServiceMgr.I.ShowFAQs(isMaintain: true);

            //                 //当超时显示的时候，自动发送一次
            //                 //BILog.UserFte("connect_fail", "2", new JsonBIObj("loading_connect_fail", $"entrance:{//BILogInfo.FTEEntranceReq}_{//BILogInfo.FTEEntranceAck} fte:{//BILogInfo.GetBIFTEInfo()}"));
            //                 //LoadingLog.TraceBILog("connect_fail_open_aihelp", $"entrance:{//BILogInfo.FTEEntranceReq}_{//BILogInfo.FTEEntranceAck}");
            //             }
            //             else
            //             {
            //                 AIServiceMgr.I.ShowFAQs(isCanNotConnectServer: true);
            //             }
            // #endif

#if USE_SDK
            SDKManager.instance.SDKCustomerServiceView();
#endif
        }

        /// <summary>
        /// 登录错误弹框
        /// </summary>
        /// <param name="action"></param>
        /// <param name="title"></param>
        /// <param name="msg"></param>
        /// <param name="btn"></param>
        public static void ShowLoginFailWindow(Action<object> action, string title, string msg, string btn)
        {
            UIMsgBox.Push(EMsgBoxType.one_nc, title, msg, new MsgBoxBtnParam
            {
                str = btn,
                func = action,
            });
        }


        /// <summary>
        /// 查找服务器
        /// </summary>
        /// <param name="serverid"></param>
        /// <returns></returns>
        public Server FindServerById(int serverid)
        {
            return m_NowAuthAck.servers.Find(s => s.serverID == serverid);
        }

        /// <summary>
        /// 根据登录信息创建CharacterData
        /// </summary>
        /// <returns></returns>
        public CharacterData CreatCharacterByLoginData()
        {
            string name = FindServerById(loginData.serverid) == null ? "" : FindServerById(loginData.serverid).name;

            //D.Error?.Log("  playerid " + loginData.playerid + "  level " + m_NowLoginAck.playerInfo.unifyInfo.cityLevel);
            return new CharacterData
            {
                username = m_NowLoginAck.playerInfo.unifyInfo.head.name,
                headurl = m_NowLoginAck.playerInfo.unifyInfo.head.customHead,
                level = m_NowLoginAck.playerInfo.unifyInfo.cityLevel,
                AvatarCfgID = m_NowLoginAck.playerInfo.unifyInfo.head.avatarCfgID,
                headFrameID = m_NowLoginAck.playerInfo.unifyInfo.head.avatarFrrame,
                logintype = loginData.logintype,
                serverid = loginData.serverid,
                servername = name,
                playerid = loginData.playerid,
                lastlogintime = GameTime.Time,
                useCustomUrl = m_NowLoginAck.playerInfo.unifyInfo.head.useCustomHead,
                //action = CharacterAction.LoginRole,
            };


        }

        /// <summary>
        /// 完善角色信息
        /// </summary>
        public void PerfectCharacterInfo(CharacterData character)
        {
            if (m_NowLoginAck == null)
            {
                D.Warning?.Log("m_NowLoginAck is null!");
                return;
            }
            if (m_NowLoginAck.playerInfo == null)
            {
                D.Warning?.Log("m_NowLoginAck playerInfo is null!");
                return;
            }

            //D.Error?.Log(m_NowLoginAck.playerInfo.unifyInfo.cityLevel+"---------------");
            string name = FindServerById(loginData.serverid) == null ? "" : FindServerById(loginData.serverid).name;
            character.username = m_NowLoginAck.playerInfo.unifyInfo.head.name;
            character.headurl = m_NowLoginAck.playerInfo.unifyInfo.head.customHead;
            character.AvatarCfgID = m_NowLoginAck.playerInfo.unifyInfo.head.avatarCfgID;
            character.headFrameID = m_NowLoginAck.playerInfo.unifyInfo.head.avatarFrrame;
            //character.level = character.level;
            character.logintype = loginData.logintype;
            character.serverid = loginData.serverid;
            character.servername = name;
            character.playerid = loginData.playerid;
            character.lastlogintime = GameTime.Time;
            character.action = CharacterAction.None;
            character.useCustomUrl = m_NowLoginAck.playerInfo.unifyInfo.head.useCustomHead;
            //D.Error?.Log(m_NowLoginAck.playerInfo.unifyInfo.cityLevel+"************");
            //D.Error?.Log("  playerid " + character.playerid + "  level " + character.level);
        }

        /// <summary>
        /// 设置当前绑定账户信息
        /// </summary>
        /// <param name="accountInfo"></param>
        public void SetAccountInfo(cspb.AccountInfo accountInfo)
        {
            m_AccountInfo = accountInfo;
            Account_BindingTypes.Clear();
            if (m_AccountInfo != null && m_AccountInfo.accounts != null)
            {
                for (int i = 0; i < m_AccountInfo.accounts.Count; i++)
                {
                    Account_BindingTypes.Add(m_AccountInfo.accounts[i].userType);
                }
            }

        }

        /// <summary>
        /// 添加当前绑定类型
        /// </summary>
        /// <param name="type"></param>
        public void AddBingInfo(string type)
        {
            foreach (var item in Account_BindingTypes)
            {
                if (item == type)
                    return;
            }

            Account_BindingTypes.Add(type);
        }


        /// <summary>
        /// 移除内存中缓存账号绑定
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public void DeleteAccountBinding(string type)
        {
            D.Debug?.Log(" type ~~~~~ " + Account_BindingTypes.Count);
            if (Account_BindingTypes.Count == 0)
                return;

            for (int i = 0; i < Account_BindingTypes.Count; i++)
            {
                if (string.Equals(Account_BindingTypes[i], type))
                {
                    Account_BindingTypes.RemoveAt(i);
                    break;
                }
            }
        }

        /// <summary>
        /// 检测账号是否绑定了某一个平台
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public bool IsAccountBinding(string type)
        {

            D.Debug?.Log(" type " + type + "  1111 " + Account_BindingTypes.Count + "  cache " + CacheAccounts.Account_List.Count);

            if (Account_BindingTypes.Count == 0)
                return false;

            for (int i = 0; i < Account_BindingTypes.Count; i++)
            {
                if (string.Equals(Account_BindingTypes[i], type))
                {
                    return true;
                }
            }


            return false;
        }

        public bool GetAccountEq(string type, string accountName)
        {
            if (accountName.Contains("@"))
            {
                accountName = accountName.Split('@')[0];
            }

            if (m_AccountInfo != null && m_AccountInfo.accounts != null)
            {
                for (int i = 0; i < m_AccountInfo.accounts.Count; i++)
                {
                    if (string.Equals(m_AccountInfo.accounts[i].userType, type))
                    {
                        D.Debug?.Log($"切换同一类型的账户ID：{m_AccountInfo.accounts[i].userName} ？= {accountName}");
                        return m_AccountInfo.accounts[i].userName == accountName;
                    }
                }
            }

            D.Debug?.Log($"切换找不到{type}类型的账户ID");

            return false;
        }

         
        private const string platform =
#if UNITY_EDITOR
            "UNITY_EDITOR";
#elif UNITY_ANDROID
            "ANDROID";
#elif UNITY_IOS
            "IOS";
#else
            "WIN32";
#endif

        public ClientInfo GenerateClientInfo()
        {

            var clientInfo = new ClientInfo
            {
                payType = LPay.Provider,
                //当前使用的账号创建时的id，不一定是当期的设备id，如果要当前设备id，请使用PlatformInfo.I.GetUDID()
                openUDID = Public.PlatformInfo.I.Udid,
                platform = platform,
                bundleID = AppInfo.BundleID,
                version = AppInfo.Version,

                localCountry = SDKManager.instance.loginInfo?.area??"CN",

                localLanguage = LocalizationMgr.CurrentLanguage,
                deviceState = string.Empty,
                isSimulator = PlatformUtils.IsSimulator ? 1 : 0,

                IP = Entrance.ClientIP,

                idfa = PlatformUtility.GetIDFA(),
                idfv = PlatformUtility.GetIDFV(),


#if UNITY_IOS

#elif UNITY_ANDROID
                googleAid = PlatformInfo.I.GetGAID(),

#endif

#if UNITY_WEBGL
                macAddr = "A4BB6DCF2316",
#else
                macAddr = NetUtils.GetMacAddress(),
#endif
                latitude = 0,
                longitude = 0,
                altitude = 0,
                networkType = PlatformUtility.GetNetworkType(),
                // #if USE_TGSBASIC && !UNITY_WEBGL
                //                 thirdPartDeviceID = SDKAnalytics.GetADID(SDKAnalytics.Mode.Adjust),
                // #endif
                build = string.Empty,
                //当前设备id
                newOpenUDID = PlatformInfo.I.Udid,
                // 传入旧版设备ID
                v1OpenUDID = PlatformInfo.I.Udid,
                // #if USE_TGSBASIC && !UNITY_WEBGL
                //                 //传入推送相关信息
                //                 deviceType = "fcm",
                //                 deviceToken = SDK.I.DevicePushToken,
                // #endif

                //记录一下本地手机的UTC时间
                cDate = DateTime.UtcNow.ToString("yyyy/MM/dd HH:mm:ss"),
                deviceToken = SDKManager.instance.FcmPushToken,
                deviceType = "fcm",
                // #if USE_TGSBASIC && !UNITY_WEBGL
                //                 //传入FireBase InstallationID (旧称FireBase Instance ID)
                //                 firebaseInstanceID = K1D2Config.IsSq() ? "" : "",// FirebaseGlobal.GetFirebaseInstanceID() + EntranceUtils.GetEntranceInfo(EntranceKey.ad_permission),

                //                 BindEmail = SDK.I.EmailAddress,
                // #endif 
                os = ThinkingAnalytics.ThinkingAnalyticsAPI.GetPresetProperties(K3GameEvent.taAppId)?.OS ?? "",
                osVersion = ThinkingAnalytics.ThinkingAnalyticsAPI.GetPresetProperties(K3GameEvent.taAppId)?.OSVersion ?? "",
                manufacturer = ThinkingAnalytics.ThinkingAnalyticsAPI.GetPresetProperties(K3GameEvent.taAppId)?.Manufacturer ?? "",
                carrier = ThinkingAnalytics.ThinkingAnalyticsAPI.GetPresetProperties(K3GameEvent.taAppId)?.Carrier ?? "",
                simulator = ThinkingAnalytics.ThinkingAnalyticsAPI.GetPresetProperties(K3GameEvent.taAppId)?.Simulator ?? false,
                deviceID = ThinkingAnalytics.ThinkingAnalyticsAPI.GetPresetProperties(K3GameEvent.taAppId)?.DeviceId ?? "",
                deviceName = ThinkingAnalytics.ThinkingAnalyticsAPI.GetPresetProperties(K3GameEvent.taAppId)?.DeviceModel ?? "",
                androidID = ThinkingAnalytics.ThinkingAnalyticsAPI.GetDistinctId() ?? "",

                enableMsgCompress = true,
                limitServerList = false
            };

            //if(K1D2Config.IsSq())
            //{
            //    clientInfo.exts.Add("sq_pid", sqsdk.SanQiSDKManager.instance.pid);
            //    clientInfo.exts.Add("sq_gid", sqsdk.SanQiSDKManager.instance.gid);
            //    if(!string.IsNullOrEmpty(loginData.uid))
            //    {
            //        clientInfo.exts.Add("sq_uid", loginData.uid);
            //    }
            //}

            clientInfo.exts.Add("new_td", "1");
#if USE_CLOUDCFG
            clientInfo.exts.Add("res_ver_in", CloudCfgUtil.CurCfgInfo.CfgVersion.ToString());
            clientInfo.exts.Add("res_ver_remote", (CloudCfgUtil.RemoteCfgInfo == null) ? "N/A" : CloudCfgUtil.RemoteCfgInfo.CfgVersion.ToString());
            string fixCodeVer = EntranceUtils.GetEntranceInfo(EntranceKey.ConfigCodeDynamicVersion);
            clientInfo.exts.Add("code_ver", string.IsNullOrEmpty(fixCodeVer) ? "N/A" : fixCodeVer);
#endif
            return clientInfo;
        }

        /// <summary>
        /// 调用 SDK.I.Login 之前 保存一下登录类型
        /// </summary>
        public void AddSDKLoginTypes(SDKLoginType newType)
        {
            if (SDKLoginTypes == null)
            {
                SDKLoginTypes = new Queue<SDKLoginType>();
                return;
            }

            var count = SDKLoginTypes.Count;
            foreach (var type in SDKLoginTypes)
            {
                if (newType == type)
                    return;
            }

            SDKLoginTypes.Enqueue(newType);
        }

        public UniTask OnBeforeLogin()
        {
            Init();
             
            return UniTask.CompletedTask;
        }
    }

    /// <summary>
    /// Auth2中Authorize验证返回数据
    /// </summary>
    [System.Serializable]
    public class Auth2Authorize
    {
        /// <summary>
        /// 平台验证返回token
        /// </summary>
        public string access_token;
        /// <summary>
        /// 账号验证的jwt
        /// </summary>
        public string jwt;
        /// <summary>
        /// 账号id
        /// </summary>
        public string id_token;
        /// <summary>
        /// 账号id
        /// </summary>
        public string account_id;
        /// <summary>
        /// 失效期限
        /// </summary>
        public long expire_at;
        /// <summary>
        /// 账号创建时间
        /// </summary>
        public string data;

    }
}