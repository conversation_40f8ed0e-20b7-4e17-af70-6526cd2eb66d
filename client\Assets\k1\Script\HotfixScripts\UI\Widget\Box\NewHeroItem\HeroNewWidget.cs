﻿
using Cfg.G;
using Game.Utils;
using Public;
using System.Collections;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using Spine.Unity;
using TFW;
using TFW.Localization;
using TFW.UI;
using UI;
using UnityEngine;

/// <summary>
/// 统一Hero图标模型Widget
/// 拥有最基本的item元素，品质框，图标，等级，属性，星级
/// <AUTHOR>
/// @date 2020/12/15 16:06:58
/// @ver 1.0
/// </summary>
public class HeroNewWidget : BaseBtnWidget
{
    /// <summary>
    /// ItemObj
    /// </summary>
    protected GameObject itemObj;

    /// <summary>
    /// 品质
    /// </summary>
    protected TFWImage qualityIcon;

    /// <summary>
    /// 图标
    /// </summary>
    protected TFWImage icon;

    /// <summary>
    /// 无星级状态
    /// </summary>
    protected GameObject noStarObj;

    /// <summary>
    /// 有星级状态
    /// </summary>
    protected GameObject hasStarObj;

    protected UIGrid starGrid;

    /// <summary>
    /// 等级
    /// </summary>
    protected TFWText levelText;

    /// <summary>
    /// 等级1
    /// </summary>
    protected TFWText levelText1;

    /// <summary>
    /// 属性
    /// </summary>
    protected TFWImage attributeImg;

    protected TFWImage ssrImg;

    private UIGrid starGrid_new;

    protected GameObject SpineRoot;
    /// <summary>
    /// 品质文件夹
    /// </summary>
    protected string folder = "Quality";

    /// <summary>
    /// Item文件夹
    /// </summary>
    public string itemFolder = "NewItem";

    /// <summary>
    /// 属性文件夹
    /// </summary>
    public string AttributeFolder = "Attribute";



    /// <summary>
    /// 初始化
    /// </summary>
    /// <param name="root"></param>
    public HeroNewWidget(GameObject root) : base(root)
    {

    }

    /// <summary>
    /// 初始化
    /// </summary>
    public override void OnInit()
    {
        base.OnInit();
        itemObj = UIHelper.GetChild(rootObj, "Item");
        qualityIcon = UIHelper.GetComponent<TFWImage>(itemObj, "Quality");
        icon = UIHelper.GetComponent<TFWImage>(itemObj, "Icon");
        if (icon != null)
            icon.Grey = false;

        noStarObj = UIHelper.GetChild(itemObj, "Level/NoStar");
        hasStarObj = UIHelper.GetChild(itemObj, "Level/HaveStar");
        starGrid = UIHelper.GetComponent<UIGrid>(hasStarObj, "Star");

        if (noStarObj != null)
            levelText = UIHelper.GetComponent<TFWText>(noStarObj, "Text");
        if (hasStarObj != null)
            levelText1 = UIHelper.GetComponent<TFWText>(hasStarObj, "Text");

        attributeImg = UIHelper.GetComponent<TFWImage>(itemObj, "Attribute");
        ssrImg = UIHelper.GetComponent<TFWImage>(rootObj, "Rarity/SSR");
        starGrid_new = UIHelper.GetComponent<UIGrid>(itemObj, "Star/UpStar");

        SpineRoot = UIHelper.GetChild(itemObj, "Spine");
    }




    /// <summary>
    /// 设置品质框
    /// </summary>
    public virtual void SetQuality(int quality, bool isHalf = false)
    {
        if (qualityIcon)
            UITools.SetQualityIcon(qualityIcon, quality, isHalf);
    }

    /// <summary>
    /// 设置图片
    /// </summary>
    public virtual void SetIcon(string iconDiaplayKey, bool useNativeSize = true)
    {
        if (icon)
        {
            UITools.SetCommonItemIcon(icon, iconDiaplayKey, useNativeSize);
            //UITools.SetImage(icon, iconDiaplayKey, itemFolder, true);
            icon.Grey = false;
        }
    }

    /// <summary>
    /// 设置等级信息
    /// </summary>
    /// <param name="level">等级</param>
    public virtual void SetLevel(string level, int star)
    {
        if (levelText)
            levelText.text = level;

        if (levelText1)
            levelText1.text = LocalizationMgr.Format(LocalizationMgr.Get("Equipment_EquipmentLevel"), level);

        SetStar(star);
    }

    /// <summary>
    /// 设置属性
    /// 属性功能未开放，暂时先设置默认属性
    /// </summary>
    public virtual void SetAttribule(int attribute)
    {
        if (attributeImg)
        {
            var attr = UITools.GetAttributeDisplayKey(attribute);

            UITools.SetImageBySpriteName(attributeImg, attr);
        }
    }

    public void RefreshStar(Game.Data.HeroData heroData)
    {
        if (starGrid_new == null)
        {
            return;
        }
        starGrid_new.gameObject.SetActive(true);
        starGrid_new.Clear();
        if (heroData != null)
        {
            for (int i = 0; i < heroData.HeroCfg.StarRating; i++)
            {
                var star = starGrid_new.AddItem<Transform>();
                if (heroData.Star > i)
                {
                    UIHelper.SetStarActive(star, 10);
                }
                else if (heroData.Star == i)
                {
                    UIHelper.SetStarActive(star, heroData.StarStep - 1);
                }
                else
                {
                    UIHelper.SetStarActive(star, -1);
                }
            }
        }
    }


    /// <summary>
    /// 设置星级
    /// </summary>
    /// <param name="star"></param>
    public virtual void SetStar(int star)
    {
        // if (star == 0)
        // {
        //     noStarObj.SetActive(true);
        //     hasStarObj.SetActive(false);
        //     return;
        // }

        if (noStarObj)
            noStarObj.SetActive(false);
        if (hasStarObj)
            hasStarObj.SetActive(true);

        if (starGrid != null)
        {
            starGrid.Clear();
            for (int i = 0; i < star; i++)
            {
                starGrid.AddItem<Transform>();
            }
        }
    }

    /// <summary>
    /// 设置图片颜色
    /// </summary>
    public virtual void SetIconColor(Color color)
    {
        if (icon)
            icon.color = color;
    }

    public void SetSpine(Game.Data.HeroData heroData)
    {
        if (SpineRoot == null)
        {
            return;
        }

        var spineObjCount = SpineRoot.transform.childCount;
        if (spineObjCount > 0)
        {
            for (int i = spineObjCount - 1; i >= 0; i--)
            {
                var spineObj = SpineRoot.transform.GetChild(i).gameObject;
                ResourceMgr.ReleaseInstance(spineObj);
            }
        }

        if (heroData != null && heroData.HeroCfg != null)
        {
            int toModelID = heroData?.HeroCfg?.Id ?? 0;
            var spineObj = HeroUtils.GetHeroSpinePrefabSync(heroData.HeroCfg);
            if (spineObj != null)
            {
                spineObj.transform.SetParent(SpineRoot.transform, false);
                spineObj.gameObject.name = toModelID.ToString();
                SkeletonGraphic spineGraphic;
                spineGraphic = spineObj.GetComponent<SkeletonGraphic>();
                spineGraphic.material = new Material(spineGraphic.material);

                var SpineLocalPostion = HeroUtils.Parse(heroData.HeroCfg.TroopSpine[1]);
                spineObj.transform.localPosition = SpineLocalPostion;
                var SpineLocalScale = HeroUtils.Parse(heroData.HeroCfg.TroopSpine[2]);
                spineObj.transform.localScale = SpineLocalScale;
            }
        }
    }

    /// <summary>
    /// 设置数据
    /// </summary>
    /// <param name="cfgId"></param>
    /// <param name="level"></param>
    /// <param name="star"></param>
    public async UniTaskVoid SetData(int cfgId, int level, int star, bool forceShowLevel = false)
    {
        var heroCfg = await Cfg.C.CD2Hero.GetConfigAsync(cfgId);
        if (heroCfg != null)
        {
            itemObj.SetActive(true);
            icon.Grey = false;

            SetQuality(heroCfg.HeroType);
            SetIcon(heroCfg.Icon1);
            if ((level > 0 && star > 0) || forceShowLevel)
                SetLevel($"Lv.{level}", star);
            SetAttribule(heroCfg.SoldiersType);
        }

    }

    /// <summary>
    /// 设置数据
    /// </summary>
    /// <param name="quality">品质 1，2，3，4</param>
    /// <param name="iconDiaplayKey">图标Key，新版都用string类型的字符串</param>
    /// <param name="level">等级</param>
    /// <param name="attribute">属性</param>
    /// <param name="star">星级</param>
    public virtual void SetData(int quality, string iconDiaplayKey, string level, int attribute, int star)
    {
        itemObj.SetActive(true);
        icon.Grey = false;

        SetQuality(quality);
        SetIcon(iconDiaplayKey);
        SetLevel(level, star);
        SetAttribule(attribute);
    }

    /// <summary>
    /// 设置数据
    /// </summary>
    /// <param name="heroData">英雄数据结构</param>
    public virtual void SetData(Game.Data.HeroData heroData, bool useNativeSize = true)
    {
        if (heroData == null || heroData.HeroCfg == null)
        {
            return;
        }
        if (itemObj != null)
            itemObj.SetActive(true);

        SetIconGrey(false);
        if (ssrImg)
            UITools.SetImageBySpriteName(ssrImg, UITools.GetSSRQuality(heroData.HeroCfg.HeroType));

        SetQuality(heroData.HeroCfg.HeroType);
        SetIcon(heroData.HeroCfg.Icon1, useNativeSize);

        //UITools.SetCommonItemIcon(icon, iconDiaplayKey, nativeSize);

        //SetLevel(LocalizationMgr.Format("MAIL_radar_player_level", heroData.Level), heroData.Star);
        SetLevel(heroData.Level.ToString(), heroData.Star);
        SetAttribule(heroData.HeroCfg.SoldiersType);
    }


    public virtual void SetGreyData(Game.Data.HeroData heroData)
    {
        if (heroData == null || heroData.HeroCfg == null)
        {
            return;
        }

        SetIcon(heroData.HeroCfg.Icon1.ToString());
        SetQuality(heroData.HeroCfg.HeroType);


        SetLevel(heroData.Level.ToString(), heroData.Star);
        SetAttribule(heroData.HeroCfg.SoldiersType);

        noStarObj.SetActive(false);
        hasStarObj.SetActive(false);

        SetIconGrey(true);
    }

    public virtual void SetHalfGreyData(Game.Data.HeroData heroData)
    {
        if (heroData == null || heroData.HeroCfg == null)
        {
            return;
        }

        SetIcon(heroData.HeroCfg.BigIcon);
        SetQuality(heroData.HeroCfg.HeroType, true);
        SetLevel(heroData.Level.ToString(), heroData.Star);
        SetAttribule(heroData.HeroCfg.SoldiersType);
        RefreshStar(heroData);
        noStarObj.SetActive(false);
        hasStarObj.SetActive(false);
        starGrid_new.gameObject.SetActive(false);
        SetIconGrey(true);
    }

    public void SetIconGrey(bool grey)
    {
        var color = grey ? Color.gray : Color.white;

        if (icon)
        {
            icon.color = color;
        }

        if (attributeImg)
        {
            attributeImg.color = color;
        }

        if (qualityIcon)
        {
            qualityIcon.color = color;
        }

        // if (icon)
        //     icon.Grey = grey;
        // if (attributeImg)
        //     attributeImg.Grey = grey;
        // if (qualityIcon)
        //     qualityIcon.Grey = grey;
    }

    /// <summary>
    /// 设置数据
    /// </summary>
    /// <param name="heroData">服务器数据</param>
    public virtual void SetData(cspb.HeroData heroData)
    {
        if (heroData == null)
            return;

        var cfg = Cfg.C.CD2Hero.I(heroData.cfgId);
        if (cfg == null)
            return;

        itemObj.SetActive(true);
        icon.Grey = false;

        SetQuality(cfg.HeroType);
        SetIcon(cfg.Icon1);
        SetLevel(heroData.level.ToString(), heroData.star);
        SetAttribule(cfg.SoldiersType);
    }



}

