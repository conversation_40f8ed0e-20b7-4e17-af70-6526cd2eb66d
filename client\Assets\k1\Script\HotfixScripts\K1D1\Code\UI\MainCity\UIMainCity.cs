﻿using Common;
using DeepUI;
using K1;
using TFW;
using UnityEngine;

namespace UI
{

    /// <summary>
    /// 主界面信息数据
    /// </summary>
    [Popup("MainView/UIMain", true)]
    public partial class UIMainCity : BasePopup
    { 
        
        public UIDragImgNew dragImage;

        [PopupField("FullScreenBgCanvas/Root/btnClose")]
        private GameObject btnClose;

        #region 初始化
        protected override void OnInit()
        { 
            base.OnInit();

            dragImage = GetComponent<UIDragImgNew>("FullScreenBgCanvas/Root/BG1"); 

            BindClickListener(btnClose, (x, y) => {
                Close();
            });
        }



        #endregion


        #region 数据刷新显示

        protected internal override void OnOpenComplete()
        {
            base.OnOpenComplete(); OnShown();
        }

        protected internal override void OnShowComplete()
        {
            base.OnShowComplete(); OnShown();
        }


        protected   void OnShown()
        { 
            var ins = FloatTips.GetUIInstance();

            canvas.sortingLayerID = CustomSortingLayer.MainUI;
            canvas.sortingOrder = 0; //主城特殊处理
            SortEffectRenderer();

            EventMgr.FireEvent(TEventType.OnShowBuildingPanel);
        }

         

        #endregion

   

    }
}
