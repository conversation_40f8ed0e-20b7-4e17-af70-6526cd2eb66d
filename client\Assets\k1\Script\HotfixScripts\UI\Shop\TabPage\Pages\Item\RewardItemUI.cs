﻿using Cfg.Cm;
using cspb;
using Cysharp.Threading.Tasks;
using Game.Data;
using K3;
using Logic;
using Sirenix.OdinInspector;
using System.Collections.Generic;
using TFW.Localization;
using TFW.UI;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

namespace UI
{
    /// <summary>
    /// 奖励ItemUI
    /// </summary>
    public class RewardItemUI : MonoBehaviour
    {
        const string UIElementTitle = "UI Elements";
        /// <summary>
        /// 与策划约定的配置表中表示 StandardBack 为空的值，不知道为啥约定一个这个值，而不是直接为空
        /// </summary>
        const string StandardBackEmptyValue = "0";

        public readonly UnityEvent onClickHeroJump = new UnityEvent();

        [InfoBox("以下 UI 元素如不需要可置空")]
        [BoxGroup(UIElementTitle)]
        [Tooltip("品质")]
        [SerializeField]
        protected Image _quality;

        [BoxGroup(UIElementTitle)]
        [ShowIf(nameof(_quality))]
        [Indent]
        [Tooltip("品质框文件夹")]
        [SerializeField]
        protected string _qualityFolder = "Quality";

        [BoxGroup(UIElementTitle)]
        [Tooltip("图标")]
        [SerializeField]
        protected Image _icon;

        [BoxGroup(UIElementTitle)]
        [ShowIf(nameof(_icon))]
        [Indent]
        [Tooltip("Icon文件夹")]
        [SerializeField]
        protected string _iconFolder = "NewItem";

        [BoxGroup(UIElementTitle)]
        [Tooltip("数量")]
        [SerializeField]
        protected Text _count;

        [BoxGroup(UIElementTitle)]
        [ShowIf(nameof(_count))]
        [Indent]
        [Tooltip("数量前缀")]
        [SerializeField]
        protected string _countPrefix = "x";

        [BoxGroup(UIElementTitle)]
        [Tooltip("顶部标题")]
        [SerializeField]
        protected Text _title;

        [BoxGroup(UIElementTitle)]
        [ShowIf(nameof(_title))]
        [Indent]
        [Tooltip("顶部标题根节点")]
        [SerializeField]
        protected GameObject _titleRoot;

        [BoxGroup(UIElementTitle)]
        [Tooltip("英雄属性图标")]
        [SerializeField]
        protected Image _heroAttribute;

        [BoxGroup(UIElementTitle)]
        [ShowIf(nameof(_heroAttribute))]
        [Indent]
        [Tooltip("英雄属性图标文件夹")]
        [SerializeField]
        protected string _heroAttributeFolder = "Attribute";

        [BoxGroup(UIElementTitle)]
        [Tooltip("点击 Tip 按钮")]
        [SerializeField]
        protected Button _tipButton;

        [BoxGroup(UIElementTitle)]
        [Tooltip("价格Icon")]
        [SerializeField]
        protected TFWImage _priceIcon;

        [BoxGroup(UIElementTitle)]
        [Tooltip("价格")]
        [SerializeField]
        protected TFWText _price;

        [BoxGroup(UIElementTitle)]
        [Tooltip("另外一个价格")]
        [SerializeField]
        protected TFWText _priceBefore;

        [BoxGroup(UIElementTitle)]
        [Tooltip("剩余数量/总数量")]
        [SerializeField]
        protected TFWText _leftCount;

        [SerializeField]
        protected GameObject _LockObj;

        [SerializeField]
        protected GameObject _receiveObj;
        [BoxGroup(UIElementTitle)]
        [ShowIf(nameof(_tipButton))]
        [Indent]
        [SerializeField]
        protected bool _useNewTips = false;

        [BoxGroup(UIElementTitle)]
        [ShowIf(nameof(_tipButton))]
        [Indent]
        [Tooltip("Tip 基于的位置")]
        [SerializeField]
        protected GameObject _tipBasePosition;

        [BoxGroup("VM Icon")]
        [Tooltip("VM资产使用无框图标（FramelessIcon 字段）")]
        [SerializeField]
        protected bool _vmUseFramelessIcon = false;

        [BoxGroup("VM Icon")]
        [ShowIf(nameof(_vmUseFramelessIcon))]
        [Indent]
        [Tooltip("FramelessIcon文件夹")]
        [SerializeField]
        protected string _vmFramelessIconFolder = "Common";

        /// <summary>
        /// 一份道具 几个道具描述
        /// </summary>
        [SerializeField]
        protected TFWText itemNumText;

        [SerializeField]
        GameObject doubleDiscountObj;

        [HideInInspector]
        ///是否需要显示tips
        public bool IsNeedTips = true;

        /// <summary>
        /// 使用大额显示
        /// </summary>
        public bool UseCounter;

        /// <summary>
        /// 用于重载方法减少GC
        /// </summary>
        private readonly TypIDVal _cache = new TypIDVal();

        private ITypeIdValue _data;

        #region 
        public void SetData(ITypeIdValue data)
        {
            this._data = data;
            this.Refresh();
            SetShowDoubleDiscount(false);
        }

        public void SetData(TypIDVal data)
        {
            this.SetData(data as ITypeIdValue);
        }

        public void SetData(Cfg.ConstConfig.CValTypIdArg2Arg1_isiaii value)
        {
            SetData(new TypIDVal() { ID = value.Id, typ = value.Typ, val = value.Val });
        }

        public void SetData(CValTypId_lsi reward)
        {
            if (reward != null)
            {
                this.SetData(reward as ITypeIdValue);
            }
        }

        public void SetData(Logic.ItemData itemData)
        {

            if (itemData != null && itemData.Cfg != null)
            {
                this._cache.typ = Cfg.AssetType.Item;
                this._cache.ID = itemData.Cfg.Id;
                this._cache.val = itemData.Count;

                this.SetData(this._cache);
            }
        }

        public void SetData(Logic.VMData vmData)
        {
            if (vmData != null && vmData.Cfg != null)
            {
                this._cache.typ = Cfg.AssetType.Vm;
                this._cache.ID = vmData.Cfg.Id;
                this._cache.val = vmData.Count;

                this.SetData(this._cache);
            }
        }

        public void SetData(CValTypId_isi reward)
        {
            if (reward != null)
            {
                this.SetData(reward as ITypeIdValue);
            }
        }
        #endregion

        public void SetShowDoubleDiscount(bool flag)
        {
            if (doubleDiscountObj)
            {
                doubleDiscountObj.SetActive(flag);
            }
        }

        public void SetUnlockStatus(bool unlock)
        {
            if (_LockObj)
                _LockObj?.SetActive(!unlock);
        }
        public void SetReceiveStatus(bool receive)
        {
            if (_receiveObj)
                _receiveObj?.SetActive(receive);
        }
        /// <summary>
        /// 英雄的Tip跳转到英雄详情
        /// </summary>
        /// <param name="heroId"></param>
        private void ShowHeroTips(int heroId)
        {
            if (heroId == 0)
                return;

            var heroData = HeroGameData.I.GetHeroByCfgId(heroId);
            if (heroData != null)
            {
                var popData = new UIHeroShowViewData()
                {
                    mainHeroData = heroData,
                };

                DeepUI.PopupManager.I.ShowPanel<UIHeroShowView>(popData);

                this.onClickHeroJump.Invoke();
            }
        }

        /// <summary>
        /// 点击奖励的Tip显示
        /// </summary>
        private void ShowRewardTips(string name, string desc, int quality, bool isNewTip, string haveDesc = null, List<string> accessList = null)
        {
            var arr = ArrowDir.Down;
            if (isNewTip)
                arr = ArrowDir.None;

            var pos = this._tipBasePosition != null ? this._tipBasePosition.transform.position : this.transform.position;
            UIBubbleTipsMgr.I.ShowNewTip(pos, name, desc, arr, quality, haveDesc: haveDesc, accessList: accessList);
        }
        private void ShowTreasureTips(string name, string desc, int quality, bool isNewTip, List<string> attr, int power)
        {
            var arr = ArrowDir.Down;
            if (isNewTip)
                arr = ArrowDir.None;

            var pos = this._tipBasePosition != null ? this._tipBasePosition.transform.position : this.transform.position;
            UIBubbleTipsMgr.I.ShowTreasureTip(pos, name, desc, attr, arr, quality, power);
        }

        private async void Refresh()
        {
            if (this._tipButton != null)
            {
                this._tipButton.onClick.RemoveListener(this.OnClick);
                this._tipButton.onClick.AddListener(this.OnClick);
            }

            if (this._heroAttribute != null)
            {
                this._heroAttribute.enabled = false;
            }

            if (this.isActiveAndEnabled && this._data != null)
            {

                var reward = this._data;
                if (reward == null)
                    return;

                if (reward.type == Cfg.AssetType.Item)
                {
                    Logic.ItemData data = new Logic.ItemData(reward.id);
                    if (data.Cfg == null)
                        return;

                    var icon = data.Cfg.StandardIcon;
                    //if (data.Cfg.StandardBack != StandardBackEmptyValue)
                    //    icon = data.Cfg.StandardBack;
                    SetIcon(icon);
                    SetQuality(data.Cfg.Quality);

                    if (data.Cfg.shouldShowName)
                    {
                        SetTitle(data.Cfg.GetDisplayName());
                    }
                    else
                    {
                        SetTitle(string.Empty);
                    }

                    #region Hero 显示属性图标

                    // if (data.Cfg != null && data.Cfg.Class == "item_hero")
                    // {
                    //     var heroId = data.Cfg.CategoryParam.Effect[0].Id;
                    //     if (heroId == 0)
                    //         return;

                    //     var heroData = HeroGameData.I.GetHeroByCfgId(heroId);
                    //     if (heroData != null && this._heroAttribute != null && heroData.HeroCfg.HeroType > 2)
                    //     {
                    //         this._heroAttribute.enabled = true;
                    //         var attribute = heroData.HeroCfg.SoldiersType;
                    //         var attr = UITools.GetAttributeDisplayKey(attribute);
                    //         UITools.SetImageBySpriteName(this._heroAttribute, attr);
                    //     }
                    // }
                    #endregion
                }
                else if (reward.type == Cfg.AssetType.Vm)
                {
                    var cfg = await Cfg.C.CVm.GetConfigAsync(reward.id);
                    if (cfg == null)
                        return;

                    //var quality = (int)UITools.vmQuality;
                    //var icon = this._vmUseFramelessIcon ? cfg.FramelessIcon : cfg.StandardIcon;
                    //if (cfg.StandardBack != StandardBackEmptyValue)
                    //    icon = cfg.StandardBack;

                    //var iconFolder = this._vmUseFramelessIcon ? _vmFramelessIconFolder : _iconFolder;

                    if (this._icon != null)
                        UITools.SetImageBySpriteName(this._icon, cfg.StandardIcon);

                    SetQuality(cfg.Quality);

                    SetTitle(string.Empty);
                }
                else if (reward.type == Cfg.AssetType.Tech)
                {
                    var techId = reward.id;
                    var techCfg = Cfg.C.CD2Tech.I(techId);
                    if (techCfg != null)
                    {
                        SetIcon(techCfg.Icon);

                        var techType = techCfg.Type;
                        var needTechLv = reward.value;
                        GameData.I.SkillData.MTechs.TryGetValue(techType, out var techData);
                        var curLv = techData.Level;

                        SetTitle(string.Empty);
                        SetQuality((int)UITools.QualityEnum.Quality_Gold);

                        // if (techType == 1 || techType == 11 || techType == 12 || techType == 13 || techType == 14 || techType == 15)
                        // {
                        //     if (techLevelText)
                        //     {
                        //         if (curLv >= needTechLv)
                        //         {
                        //             techLevelText.text = $"<color=#00FF00>{"level_chest_name".ToLocal(needTechLv)}</color>";
                        //         }
                        //         else
                        //         {
                        //             techLevelText.text = $"<color=#DC143C>{"level_chest_name".ToLocal(needTechLv)}</color>";
                        //
                        //         }
                        //     }
                        // }
                        // else
                        // {
                        //     if (curLv >= needTechLv)
                        //     {
                        //         LeftNum.text = $"<color=#00FF00>{ResourceUtils.GetResourceShowStr(curLv)}</color>/";
                        //     }
                        //     else
                        //     {
                        //         LeftNum.text = $"<color=#DC143C>{ResourceUtils.GetResourceShowStr(curLv)}</color>/";
                        //
                        //     }
                        //     RightNum.text = $"{ResourceUtils.GetResourceShowStr(needTechLv)}";
                        // }
                    }
                }
                else if (reward.type == Cfg.AssetType.Recover)
                {
                    var cfg = Cfg.C.CRecover.I(reward.id);
                    if (cfg == null)
                        return;

                    var quality = (int)UITools.vmQuality;
                    var icon = cfg.Icon;

                    if (this._icon != null)
                        UITools.SetImageBySpriteName(this._icon, icon);

                    SetQuality(quality);

                    SetTitle(string.Empty);
                }

                if (this._count != null)
                {
                    if (reward.type == Cfg.AssetType.Vm && reward.id == Cfg.CfgConst.GoldId)
                        SetCount(UIStringUtils.ExchangeValue(reward.value));
                    else if (reward.type == Cfg.AssetType.Vm && reward.id == Cfg.CfgConst.DiamondId)
                        SetCount(UIStringUtils.FormatIntegerByLanguage(reward.value));
                    else
                    {
                        if (UseCounter)
                        {
                            SetCount(UIStringUtils.ExchangeValue(reward.value));
                        }
                        else
                            SetCount(UIStringUtils.FormatIntegerByLanguage(reward.value));
                    }
                }
            }
        }

        /// <summary>
        /// 设置品质框
        /// </summary>
        private void SetQuality(int quality)
        {
            if (this._quality != null)
                UITools.SetQualityIcon(this._quality, quality);
        }

        /// <summary>
        /// 设置数量
        /// </summary>
        public void SetCount(string count)
        {
            if (_count != null)
            {
                if (count == "0" || string.IsNullOrEmpty(count))
                {
                    _count.text = "";
                    return;
                }

                if (LocalizationMgr.IsRightToLeftLanguage) //阿拉伯语言镜像处理
                    _count.text = $"{count}{this._countPrefix}";
                else
                    _count.text = $"{this._countPrefix}{count}";
            }
        }
        public void SetLeftCount(string curCount, string maxCount)
        {
            if (_leftCount)
            {
                if (curCount == "0" && maxCount == "0")
                {
                    _leftCount.text = "";
                    _leftCount.transform.parent.gameObject.SetActive(false);
                }
                else
                {
                    _leftCount.transform.parent.gameObject.SetActive(true);
                    _leftCount.text = curCount + "/" + maxCount;
                }

            }
        }
        public async void SetPrice(CValTypId_isi priceCfg)
        {
            if (this._price)
                this._price.text = priceCfg.Val.ToString();
            if (this._priceIcon)
            {
                if (priceCfg.Typ == Cfg.AssetType.Item)
                {
                    Logic.ItemData data = new Logic.ItemData(priceCfg.Id);
                    if (data.Cfg == null)
                        return;

                    var icon = data.Cfg.StandardIcon;
                    if (this._icon != null)
                        UITools.SetCommonItemIcon(this._priceIcon, icon);
                }
                else if (priceCfg.Typ == Cfg.AssetType.Vm)
                {
                    var cfg = await Cfg.C.CVm.GetConfigAsync(priceCfg.Id);
                    if (cfg == null)
                        return;
                    //var icon = this._vmUseFramelessIcon ? cfg.FramelessIcon : cfg.StandardIcon;
                    //if (cfg.StandardBack != StandardBackEmptyValue)
                    //    icon = cfg.StandardBack;

                    //var iconFolder = this._vmUseFramelessIcon ? _vmFramelessIconFolder : _iconFolder;

                    if (this._icon != null)
                        UITools.SetCommonItemIcon(this._priceIcon, cfg.StandardIcon);

                }
            }

        }

        public void SetItemNum(int num)
        {
            if (itemNumText)
            {
                if (num > 1)
                {
                    itemNumText.text = $"x {num}";
                }
                else
                {
                    itemNumText.text = "";
                }
            }
        }

        public async void SetPrice(TypIDVal priceCfg, bool UseSingleDiamondIcon = false)
        {
            if (this._price)
                this._price.text = priceCfg.val.ToString();
            if (this._priceIcon)
            {
                if (priceCfg.typ == Cfg.AssetType.Item)
                {
                    Logic.ItemData data = new Logic.ItemData(priceCfg.ID);
                    if (data.Cfg == null)
                        return;

                    var icon = data.Cfg.StandardIcon;
                    if (this._icon != null)
                        UITools.SetCommonItemIcon(this._priceIcon, icon);
                }
                else if (priceCfg.typ == Cfg.AssetType.Vm)
                {
                    var cfg = await Cfg.C.CVm.GetConfigAsync(priceCfg.ID);
                    if (cfg == null)
                        return;
                    //var icon = this._vmUseFramelessIcon ? cfg.FramelessIcon : cfg.StandardIcon;
                    //if (cfg.StandardBack != StandardBackEmptyValue)
                    //    icon = cfg.StandardBack;

                    ////特殊处理一下钻石图标，3个钻石的很难看
                    //if (UseSingleDiamondIcon)
                    //    icon = $"{cfg.StandardIcon}2";

                    //var iconFolder = this._vmUseFramelessIcon ? _vmFramelessIconFolder : _iconFolder;

                    if (this._icon != null)
                        UITools.SetCommonItemIcon(this._priceIcon, cfg.StandardIcon);

                }
            }
        }

        public void SetBeforePrice(string before)
        {
            if (_priceBefore)
                _priceBefore.text = before;
        }

        /// <summary>
        /// 设置图片
        /// </summary>
        private void SetIcon(string iconDiaplayKey)
        {
            if (this._icon != null)
            {
                // UITools.SetImage(this._icon, iconDiaplayKey, this._iconFolder);
                UITools.SetImageBySpriteName(this._icon, iconDiaplayKey);
            }
        }

        /// <summary>
        /// 设置顶部描述，空字符串的情况会隐藏 _titleRoot
        /// </summary>
        /// <param name="title"></param>
        private void SetTitle(string title = null)
        {
            if (_title != null)
            {
                if (_titleRoot != null)
                {
                    _titleRoot.SetActive(!string.IsNullOrEmpty(title));
                }

                _title.text = title;
            }
        }

        /// <summary>
        /// 设置数据
        /// </summary>
        /// <param name="quality">品质 1，2，3，4</param>
        /// <param name="iconDiaplayKey">图标Key，新版都用string类型的字符串</param>
        /// <param name="count">数量，金币需要科学计数法</param>
        private void SetData(int quality, string iconDiaplayKey, string count)
        {
            SetQuality(quality);
            SetIcon(iconDiaplayKey);
            SetCount(count);
        }



        private void OnEnable()
        {
            this.Refresh();
            if (this._tipButton != null)
            {
                this._tipButton.onClick.AddListener(this.OnClick);
            }
        }
        private void OnDisable()
        {
            if (this._tipButton != null)
            {
                this._tipButton.onClick.RemoveListener(this.OnClick);
            }
        }

        //private async UniTask<object[]> GetTreasureAttr(int treasureId, int power)
        //{
        //    var treasureConfig = HeroTreasureData.I.GetConfig(treasureId);
        //    object[] param = new object[2]; 
        //    if (treasureConfig == null)
        //    {
        //        power = 0;
        //        param[0] = new List<string>();
        //        param[1] = power;
        //        return param;
        //    }

        //    var list = new List<string>();
        //    var firstFlag = true;
        //    foreach (var item in treasureConfig.ExAtt)
        //    {
        //        if (firstFlag)
        //        {
        //            power = int.Parse((treasureConfig.Power * 100).ToString());
        //            firstFlag = false;
        //            continue;
        //        }

        //        if (int.TryParse(item, out var attrCfgId))
        //        {
        //            var attrCfg = await Cfg.C.CAttribute.GetConfigAsync(attrCfgId);
        //            if (attrCfg != null)
        //            {
        //                var displayName = LocalizationMgr.Get(attrCfg.ShowDesc);
        //                list.Add(displayName);
        //            }
        //        }
        //        else
        //        {
        //            D.Warning?.Log($"错误 {nameof(treasureConfig.ExAtt)} 字段 {item} 无法解析id");
        //        }
        //    }

        //    //特技
        //    foreach (var item in treasureConfig.ExStunt)
        //    {
        //        if (int.TryParse(item, out var attrCfgId))
        //        {
        //            var attrCfg = await Cfg.C.CStunt.GetConfigAsync(attrCfgId);
        //            if (attrCfg != null)
        //            {
        //                var displayName = LocalizationMgr.Get(attrCfg.ShowDesc);
        //                list.Add(displayName);
        //            }
        //        }
        //        else
        //        {
        //            D.Warning?.Log($"错误 {nameof(treasureConfig.ExStunt)} 字段 {item} 无法解析id");
        //        }
        //    }

        //    param[0] = list;
        //    param[1] = power;
        //    return param;
        //}
        async void OnClick()
        {
            if (this._data != null && IsNeedTips)
            {
                var reward = this._data;

                #region Hero
                bool isHero = false;
                int heroId = 0;
                if (this._data.type == Cfg.AssetType.Item)
                {
                    Logic.ItemData data = new Logic.ItemData(this._data.id);
                    if (data.Cfg != null && data.Cfg.Class == "item_hero")
                    {

                        heroId = data.Cfg.CategoryParam.Effect[0].Id;

                        if (Cfg.C.CD2Hero.I(heroId)?.HeroType > 2)
                        {
                            isHero = true;
                        }
                    }
                }
                #endregion

                #region Quality
                int quality = 0;
                string itemClass = "item";
                int power = 0;
                List<string> list = new List<string>();
                if (reward.type == Cfg.AssetType.Item)
                {
                    Logic.ItemData data = new Logic.ItemData(reward.id);
                    if (data.Cfg == null)
                        return;
                    quality = data.Cfg.Quality;
                    if (data.Cfg.Class == "item_treasure")
                    {
                        //var param = await GetTreasureAttr(data.Cfg.CategoryParamEffect(0).Id, power);
                        //list = param[0] as List<string>;
                        //power = (int)param[1];
                        //itemClass = data.Cfg.Class;
                    }

                }
                else if (reward.type == Cfg.AssetType.Vm)
                {
                    var cfg = await Cfg.C.CVm.GetConfigAsync(reward.id);
                    if (cfg == null)
                        return;

                    quality = cfg.Quality;
                }
                else if (reward.type == Cfg.AssetType.Tech)
                {
                    quality = (int)UITools.vmQuality;
                }
                else if (reward.type == Cfg.AssetType.Recover)
                {
                    quality = (int)UITools.vmQuality;

                }

                #endregion

                if (isHero)
                {
                    ShowHeroTips(heroId);
                }
                else
                {
                    var (AccessStr, haveCount) = await GetAccess(reward.type, reward.id);

                    string haveStr = LocalizationMgr.Format("Item_tips_01", haveCount);
                    var displayName = await TypIDValHelper.GetDisplayName(reward.type, reward.id, reward.value);
                    var displayDescription = await TypIDValHelper.GetDisplayDescription(reward.type, reward.id, reward.value);
                    if (itemClass == "item_treasure")
                        ShowTreasureTips(displayName, displayDescription, quality, this._useNewTips, list, power);
                    else
                        ShowRewardTips(displayName, displayDescription, quality, this._useNewTips, haveDesc: haveStr, accessList: AccessStr);
                }
            }
        }


        private async UniTask<(List<string>, long)> GetAccess(string rType, int rewardId)
        {

            List<string> strList = new List<string>();
            long haveCount = 0;
            //git remote set-url origin git@***************:ks/cs.git

            if (rType == Cfg.AssetType.Item)
            {
                //VIP特殊处理，写死就好，不然还要去item表遍历。得不偿失
                if (rewardId == 30010001)
                {
                    var vip = VipManager.I.GetSelfVipInfo();
                    haveCount = vip.vipExp;
                }
                else
                {
                    haveCount = BagMgr.I.GetBagItem(rewardId);

                }
                Logic.ItemData data = new Logic.ItemData(this._data.id);
                var Access = ItemData.GetItemCfg(rewardId).Access;

                if (Access.Count > 0)
                {
                    for (int i = 0; i < Access.Count; i++)
                    {
                        var accessCfg = await Cfg.C.CReplenish.GetConfigAsync(int.Parse(Access[i]));
                        if (accessCfg == null)
                        {
                            continue;
                        }
                        strList.Add(accessCfg.Name);
                    }
                }
            }
            else if (rType == Cfg.AssetType.Vm)
            {
                haveCount = K3PlayerMgr.I.GetOwnCountsByMoneyType(rewardId);
                var vmCfg = await Cfg.C.CVm.GetConfigAsync(rewardId);
                var showDataList = vmCfg.Access;
                if (showDataList != null && showDataList.Count > 0)
                {
                    for (int i = 0; i < showDataList.Count; i++)
                    {
                        var accessCfg = await Cfg.C.CReplenish.GetConfigAsync(int.Parse(showDataList[i]));
                        if (accessCfg == null)
                        {
                            continue;
                        }
                        strList.Add(accessCfg.Name);
                    }
                    //
                }
            }
            else if (rType == Cfg.AssetType.Recover)
            {
                haveCount = K3PlayerMgr.I.GetOwnCountsByMoneyType(rewardId);
                var showDataList = await UIComplementPop.GetEnergyShowData();

                if (showDataList != null && showDataList.Count > 0)
                {
                    for (int i = 0; i < showDataList.Count; i++)
                    {
                        var AccessCfgID = showDataList[i].AccessCfgID;
                        var accessCfg = await Cfg.C.CReplenish.GetConfigAsync(AccessCfgID);
                        if (accessCfg == null)
                        {
                            continue;
                        }
                        strList.Add(accessCfg.Name);
                    }
                    //
                }
            }


            return (strList, haveCount);
        }

        //public async void SetDragonruleData()
        //{
        //    var rewardId = LocalDragonWarReceiveRewardUI.RewardId;
        //    var reward = await VersionRewardMgr.I.GetDisplayRewards(rewardId);

        //    SetData(reward.FirstOrDefault());
        //}
    }
}
