﻿






using Common;
using cspb;
using DeepUI;
using Game.Data;
using Logic;
using MainScripts;
using System;
using TFW.Localization;
using UI;

namespace GameState
{
    /// <summary>
    /// 平台阶段处理器
    /// </summary>
    public class HLAuth : GSHandler
    {
        public HLAuth() : base(TEventType.AuthLogin) { }

        public override void Handler(TEventType type, object[] args)
        {
            base.Handler(type, args);
            switch (type)
            {
                case TEventType.AuthLogin:
                    D.Warning?.Log("loading_4+++++++++++++");
                    //BILogInfo.UpdateFTEStageParameter("HLAuth: authLogin");
                    //LoadingLog.AddLoadingBILog("HLAuth: authLogin");
                    //BILog.UserFte(
                      //$"loading_4",
                      //$"2"
                      //);
                    //ClientLogMgr.AddClientLogInfo(ClientLogType.ReciveAuthAckSucceed);
                    //LoadingLog.TraceBILog("loading_6_auth_login_handle");
                    if (NoticeUtils.IsNeedShowNotice)
                    {
                        //当前是在维护状态
                        //检测 是否在白名单内部，是那么就正常进入游戏，不是那么就显示维护的状态

                        if (!LoginMgr.I.IsInWhiteList() && !K1D2Config.I.IsIgnoreMaintain)
                        {
                            //显示停服公告信息
                            // Logo.ShowServiceStopNotice();
                            //ClientLogMgr.AddClientLogInfo(ClientLogType.AuthAckServerMaintain);
                            PopupManager.I.ShowDialog<UIPauseServerUpdate>(new UI.UIPauseServerUpdateData() { endTime = NoticeUtils.NoticeEndTime });
                            return;
                        }
                    }


                    var isok = (bool)args[0];

                    var mgs = args[2] as AuthAck;

                    if (isok)
                    {
                        //LogoLoading.ins.UpdateLoadingStep(LogoLoadingStepEnum.HLAuthOk);
                        //平台验证成功
                        //BILog.UserFteSwitch($"loading_4.1");
                        mgs = LoginMgr.I.SetAuthData(mgs);
                        var last_role = LoginMgr.I.GetLastCharacter();
                        LoginMgr.I.IsNewAccounts = true;
                        //判断是否用本地最近号登陆还是服务器最近账户登录
                        if (last_role == null)
                        {
                            //LogoLoading.ins.UpdateLoadingStep(LogoLoadingStepEnum.HLAuthLastCharacterIsNull);
                            if (mgs.characters.Count == 0)
                            {
                                //ClientLogMgr.AddClientLogInfo(ClientLogType.AuthAckSucceedCreatRole);
                                D.Warning?.Log("切换角色登录阶段 ps：创建角色");
                                //切换角色登录阶段 ps：创建角色
                                GSSwitchMgr.AddStage(new GSAutoLogin(new AutoLoginData
                                {
                                    characterData = null,
                                    authAck = mgs
                                }));
                                //逐记录当前账户信息
                                LoginMgr.I.loginData.accountID = mgs.accountID;
                            }
                            else if (mgs.characters.Count > 0)
                            {
                                //LogoLoading.ins.UpdateLoadingStep(LogoLoadingStepEnum.HLAuthCharactersIsNotNull);
                                D.Warning?.Log("找出最近服务器角色");
                                try
                                {
                                    //BILog.UserFteSwitch($"loading_4.3");
                                    //找出最近服务器角色
                                    var role = LoginMgr.I.NowAuthAck.characters[0];
                                    var server = LoginMgr.I.FindServerById(role.serverID);
                                    var sever_name = server == null ? "" : server.name;
                                    //ClientLogMgr.AddClientLogInfo(ClientLogType.AuthAckSucceedLoginRole);
                                    //切换角色登录阶段
                                    GSSwitchMgr.AddStage(new GSAutoLogin(new AutoLoginData
                                    {
                                        characterData = new CharacterData
                                        {
                                            username = role.meta.name,
                                            serverid = role.serverID,
                                            servername = sever_name,
                                            playerid = role.playerID,
                                        },
                                        authAck = mgs
                                    }));

                                    //逐记录当前账户信息
                                    LoginMgr.I.loginData.accountID = mgs.accountID;
                                    LoginMgr.I.loginData.serverid = role.serverID;
                                    LoginMgr.I.loginData.playerid = role.playerID;
                                    LoginMgr.I.loginData.username = role.meta.name;
                                    LoginMgr.I.IsNewAccounts = false;
                                }
                                catch(Exception e)
                                {
                                    //ClientLogMgr.AddClientLogInfo(ClientLogType.AuthAckSucceedBug);
                                    //BILog.UserFte(
                                  //$"loading_bug4",
                                  //$"2", new JsonBIObj("args", e.Message)
                                  //);
                                }
                                
                            }
                        }
                        else
                        {
                            LoginMgr.I.IsNewAccounts = false;
                            if ((LoginMgr.IS_START_GAME_LOGIN || LoginMgr.I.IsInWhiteList() )&& last_role.action!= CharacterAction.CreatRole)
                            {
                                //ClientLogMgr.AddClientLogInfo(ClientLogType.AuthAckSucceedSelectRole);
                                //LogoLoading.ins.UpdateLoadingStep(LogoLoadingStepEnum.AuthAckSucceedSelectRole);
                                D.Warning?.Log("切换角色登录阶段 ps:使用缓存最近账户登录 111");
                                //切换角色登录阶段 ps:使用缓存最近账户登录
                                GSSwitchMgr.AddStage(new GSSelectRole());
                                
                                //测试 aics 网络
                                // DiagnoseConfig config = new DiagnoseConfig();
                                // config.SetTgsServerUrls(true);//开启平台服务检测
                                //
                                // config.AddInnerUrlDiagnose(DiagnoseConfig.InnerDiagnoseTypeMaintenance,null);//添加网络公网ip和DNS检测 任务
                                // config.AddInnerUrlDiagnose(DiagnoseConfig.InnerDiagnoseTypeNetSpeed,null);//添加网速检测 任务
                                //
                                // List<DiagnoseKeyValue> keyValues = new List<DiagnoseKeyValue>();
                                // keyValues.Add(new DiagnoseKeyValue().SetValue("www..com").SetTitle("公司域名"));
                                // keyValues.Add(new DiagnoseKeyValue().SetValue("5c7021242c10k1d2.tap4hub.com").SetTitle("Name Server"));
                                // keyValues.Add(new DiagnoseKeyValue().SetValue("dfcb72721047gate.tap4api.net").SetTitle("网关域名.net"));
                                // keyValues.Add(new DiagnoseKeyValue().SetValue("dfcb72721047gate.tap4api.com").SetTitle("网关域名.com"));
                                // keyValues.Add(new DiagnoseKeyValue().SetValue("update..com").SetTitle("CDN 1"));
                                // keyValues.Add(new DiagnoseKeyValue().SetValue("d1mruz2hui3li3.cloudfront.net").SetTitle("CDN 2"));
                                // keyValues.Add(new DiagnoseKeyValue().SetValue("kdg-k1d2.akamai..com").SetTitle("CDN 3"));
                                // keyValues.Add(new DiagnoseKeyValue().SetValue("70e1616a9efb4ek1.cdn..com").SetTitle("CDN 4"));
                                // config.AddInnerUrlDiagnose(DiagnoseConfig.InnerDiagnoseTypeUrlsDomainParse,"域名解析", keyValues);//添加网络公网ip和DNS检测 任务
                                // SDKDiagnose.Instance.SetConfig(config);//设置配置
                                //
                                // SDKAicsManager.Instance.ShowAllFaqSections("true");
                            }
                            else
                            {
                                //ClientLogMgr.AddClientLogInfo(ClientLogType.AuthAckSucceedCacheLoginRole);
                                //LogoLoading.ins.UpdateLoadingStep(LogoLoadingStepEnum.AuthAckSucceedSelectRole);
                                D.Warning?.Log($"切换角色登录阶段 ps:使用缓存最近账户登录 222 {GameUtils.SerializeToStr( last_role)}");

                                //切换角色登录阶段 ps:使用缓存最近账户登录
                                GSSwitchMgr.AddStage(new GSAutoLogin(new AutoLoginData
                                {
                                    characterData = last_role,
                                    authAck = mgs
                                }));

                            }

                            //逐记录当前账户信息
                            LoginMgr.I.loginData.accountID = mgs.accountID;
                            LoginMgr.I.loginData.serverid = last_role.serverid;
                            LoginMgr.I.loginData.playerid = last_role.playerid;
                            LoginMgr.I.loginData.username = last_role.username;
                        }

                    }
                    else
                    { 
                        var _sdkIsUnBindAccount = (bool)args[3];
                        //BILogInfo.UpdateFTEStageParameter($"HLAuth: Bind Thrid SDK:{_sdkIsUnBindAccount}");
                        //LoadingLog.AddLoadingBILog($"HLAuth: Bind Thrid SDK:{_sdkIsUnBindAccount}");
                        if (_sdkIsUnBindAccount)
                        {
                            ////ClientLogMgr.AddClientLogInfo(ClientLogType.AuthAckRoleBug1);
                            ////BILog.UserFteSwitch($"loading_4.6");
                            D.Warning?.Log("当前三方账号未绑定平台账号");
                            //ClientLogMgr.AddClientLogInfo(ClientLogType.AuthAckBug1Restart);
                            //LogoLoading.ins.UpdateLoadingStep(LogoLoadingStepEnum.AuthAckBug1Restart);
                            // 提示当前三方账号未绑定平台账号，
                            LoginMgr.ShowLoginFailWindow(
                                (_) =>
                                {
                                    var accountType = LoginAuthType.GetAuthType(GameData.I.BindData.sdkLoginType);
                                    //移除三方账号信息数据
                                    LoginMgr.I.DeleteAccountBinding(accountType);
                                    LoginMgr.I.DeleteCharacterInfo(accountType);


                                    Loading_Res.ShowLoading();
                                    Main.ReStart();
                                },
                                LocalizationMgr.Get("MENU_acc_switch_fail"),
                                LocalizationMgr.Get("MENU_acc_fail_desc"),
                                LocalizationMgr.Get("MENU_ok_cap")
                            );
                        }
                        else
                        {
                            ////ClientLogMgr.AddClientLogInfo(ClientLogType.AuthAckRoleBug2);
                            D.Warning?.Log(" 平台认证失败，点击重新认证");
                            //ClientLogMgr.AddClientLogInfo(ClientLogType.AuthAckBug2Restart);
                            //LogoLoading.ins.UpdateLoadingStep(LogoLoadingStepEnum.AuthAckBug2Restart);
                            // 平台认证失败，点击重新认证
                            LoginMgr.ShowLogfailWindow( 
                                LoginFailEnum.HLAuthFail,mgs.errCode.ToString()
                            );
                        }
                    }
                    break;
            }
        }
    }
}
