﻿using System;
using System.Collections.Generic;
using Common;
using cspb;
using Cysharp.Threading.Tasks;
using DeepUI;
using Game.Config;
using Game.Data;
using Game.Utils;
using K3;
using Logic;
using SharedDataStructure;
using Sirenix.OdinInspector;
using TFW.Localization;
using TFW.UI;
using UnityEngine;
using UnityEngine.UI;

namespace UI
{
    /// <summary>
    /// 基础战力
    /// </summary>
    public class NewPowerBaseItemUI : MonoBehaviour
    {
        [SerializeField]
        TFWText _powerName;

        [SerializeField]
        TFWText _powerValue;

        [SerializeField]
        TFWImage _powerIcon;

        [ShowIf(nameof(_powerIcon))]
        [SerializeField]
        string _iconFolder = "GetItem";

        [SerializeField]
        Button _improveButton;

        private PowerType _powerType;
        private ShowPower _showPower;

        public async void SetData(PowerType powerType, ShowPower showPower, long power, int iconKey, string lang)
        {
            _powerType = powerType;
            this._showPower = showPower;

            _powerValue.text = UIStringUtils.FormatIntUnitByLanguage(power);

            var cfg = await Cfg.C.CNewRally.GetByType(powerType);
            UITools.SetImage(_powerIcon, iconKey, PowerConfig.IconFolder, true);
            _powerName.text = LocalizationMgr.Get(lang);
        }

        async void OnImproveClick()
        {

            var popup = PopupManager.I.FindPopup<TroopPowerUI.Layer>();
            var troopPowerInfo = popup != null ? popup.GameObject.GetComponent<TroopPowerUI>() : null;
            int soldierType = troopPowerInfo?._heroData?.HeroCfg?.SoldiersType ?? 1;

            List<Type> panelList = new List<Type>() {
                typeof(UIFinalPreparationPanel),
                typeof(UIActivityMain),
            };

            PopupManager.I.ClosePopupList(panelList);
            UIGuidData guidData;
            switch (_powerType)
            {
                case PowerType.Hero:
                    //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.HERO);
                    break;
                case PowerType.Soldier:
                    //PopupManager.I.ShowLayer<UIEvo>();
                    break;
                // case PowerType.HeroTotalEquip:
                //     HeroEquipmentUI.Layer.OpenSafely();
                //     break;
                case PowerType.WitchMagic:
                    //WitchsBrewUI.Layer.OpenSafely();
                    break;
                case PowerType.Vip:
                case PowerType.VipNew:
                    VipManager.I.OpenVip();
                    break;
                case PowerType.HeroTreasure:
                    TaskUtils.JumpShop(ShopJumpMark.bag);
                    break;
                case PowerType.SoliderBase:
                    //var cfg = await Cfg.C.CD2CityBuilding.GetConfigAsync((int)MainCityItem.CityType.Main);
                    //if (!cfg.GetIsOpen(true))
                    //{
                    //    return;
                    //}
                    //PopupManager.I.ShowLayer<UIMainCityLevelUp>();

                    //guidData = new UIGuidData();
                    //guidData.guidItems.Add(new UIGuidItem() { UIName = "UI.UIMainCityLevelUp", UIItem = "Root/Button", slide = true });
                    //UIGuid.StartGuid(guidData, true);

                    break;
                case PowerType.Tech:
                    //var cfg1 = await Cfg.C.CD2CityBuilding.GetConfigAsync((int)MainCityItem.CityType.College);
                    //if (!cfg1.GetIsOpen(true))
                    //{
                    //    return;
                    //}
                    //PopupManager.I.ShowLayer<UITech>();

                    break;
                case PowerType.SoliderEvo:
                    //var cfg2 = await Cfg.C.CD2CityBuilding.GetConfigAsync((int)MainCityItem.CityType.Barracks);
                    //if (!cfg2.GetIsOpen(true))
                    //{
                    //    return;
                    //}
                    //PopupManager.I.ShowLayer<UIEvo>();
                    //if (LTowerDefenseType.I.isNTD)
                    //    PopupManager.I.ShowLayer<UIEvoNew>();
                    //else
                    //    PopupManager.I.ShowLayer<UIEvo>();

                    guidData = new UIGuidData();
                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UI.UIEvo", UIItem = $"Root/SoldierGrid/{soldierType}/UpgradeBtn", slide = true });
                    UIGuid.StartGuid(guidData, true);

                    break;
                case PowerType.SoldierTalent:
                    //int lv = 1;
                    //if (soldierType == 1)
                    //    lv = LEvolution.I.ArcherData.EvoLevel;
                    //else if (soldierType == 2)
                    //    lv = LEvolution.I.FireData.EvoLevel;
                    //else if (soldierType == 3)
                    //    lv = LEvolution.I.IceData.EvoLevel;
                    //else if (soldierType == 4)
                    //    lv = LEvolution.I.ToxData.EvoLevel;

                    //var evoData = TDUtils.NewEvolutionData(new EvoInfo() { evoLv = lv, exp = 0, soldierType = soldierType });
                    //TalentMgr.I.OpenTalentPanel(evoData, true);
                    break;
                case PowerType.HeroBase:
                    TaskUtils.JumpToHero(TaskUtils.HeroToDO.LevelUp);
                    break;
                case PowerType.HeroStar:
                case PowerType.HeroSkill:
                    TaskUtils.JumpToHero(TaskUtils.HeroToDO.StarUp);
                    break;
                case PowerType.Fetter:
                    if (LPlayer.I.GetMainCityLevel() < MetaConfig.OpenFetters)
                    {
                        UITools.PopTips(LocalizationMgr.Format("Unlock_level", MetaConfig.OpenFetters));
                        return;
                    }
                    TaskUtils.JumpToHero(TaskUtils.HeroToDO.Fellter);
                    break;
                case PowerType.DragonTotal:
                case PowerType.DragonRune:
                    if (!UnlockMgr.I.CheckUnlock(UnlockFuncEnum.Building_8))
                    {
                        UITools.PopTips(UnlockMgr.I.GetUnlockDesc(UnlockFuncEnum.Building_8));
                        return;
                    }
                    PopupManager.I.ShowLayer<UIDragon>();

                    guidData = new UIGuidData();
                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UI.UIDragon", UIItem = "Root/btn_upgrade", slide = true });
                    UIGuid.StartGuid(guidData, true);
                    break;
                case PowerType.DragonEquipment:
                    if (DragonEquipmentMgr.I.unlocked)
                    {
                        PopupManager.I.ShowLayer<UIDragonEquipment>();
                    }
                    else
                    {
                        UITools.PopTips(LocalizationMgr.Format("Open_chapter_equip", MetaConfig.Drangon_Equipment_open_chapter));
                    }
                    break;
                case PowerType.AllianceBuilding:
                    //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.ALLIANCE);
                    break;
                case PowerType.Skin:
                    PopupManager.I.ShowLayer<UIShopSkin>();
                    break;
                // case PowerType.HeroEquipBuild:
                //     HeroEquipmentUI.Layer.OpenSafely();
                //     break;
                // case PowerType.HeroEquip:
                //     HeroEquipmentUI.Layer.OpenSafely();
                //     break;
                case PowerType.WitchBase:
                    //WitchsBrewUI.Layer.OpenSafely();
                    break;
                case PowerType.WitchStone:
                    //WitchStoneUI.Layer.OpenSafely();
                    break;
                case PowerType.SummonAltar:
                    SummonAltarMgr.I.ReqPowerData();
                    break;
                case PowerType.Atlas:
                    CollectAtlasTaskUI.Layer.OpenSafely();
                    break;
                case PowerType.Buff:
                    DeepUI.PopupManager.I.ShowWidget<UIPlayerNewBuffs>();
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 可拆分战力显示组成详情
        /// </summary>
        async UniTask<bool> CheckSplitPower()
        {
            var cfg = await Cfg.C.CNewRally.GetByType(this._powerType);
            if (cfg != null && cfg.Split == 1)
            {
                PopupManager.I.ShowDialog<PowerDetailUI.Layer>(new PowerDetailUI.LayerData()
                {
                    config = cfg,
                    showPower = this._showPower
                });
                return true;
            }

            return false;
        }

        private void Awake()
        {
            if (this._improveButton != null)
            {
                this._improveButton.onClick.AddListener(this.OnImproveClick);
            }
        }

        private void OnDestroy()
        {
            if (this._improveButton != null)
            {
                this._improveButton.onClick.RemoveListener(this.OnImproveClick);
            }
        }
    }
}
