﻿using Common;
using DeepUI;
using Logic;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using K3;
using UI;
using Game.Data;
using TFW;

namespace TaskType
{
    public interface TaskGoTo
    {
        public void GoTo(params string[] parms);
    }

    public class TaskTypeGoTo_314 : TaskTypeGoTo_316{}
    public class TaskTypeGoTo_315 : TaskTypeGoTo_316{}
    public class TaskTypeGoTo_316: TaskGoTo
    {
        //316	回到主城（当前在主城界面，直接下一步）→点击建筑A→点击升级
        public void GoTo(params string[] parms) 
        {
            //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.PRISON);
            var ui = PopupManager.I.FindPopup<UIMainCity>();
            if (ui != null && parms.Length>0 && int.TryParse(parms[0], out int cityType)) 
            {
                PopupManager.I.ClearAllPopup();

                int cityCfgID = LPlayer.I.GetCityBuildingIDByType(cityType);
                ui.dragImage.MoveTOCfg(cityCfgID, () =>
                { 
                    var guidData = new UIGuidData();

                    var window = PopupManager.I.FindPopup<UIMainCity>();
                     
                    string path = $"FullScreenBgCanvas/Root/BG1/Content/Building/build_{cityType}";
                    // 如果当前建筑未解锁。优先触发解锁逻辑
                    if (window != null)
                    {
                        var rebuild = window.GameObject.transform.Find($"FullScreenBgCanvas/Root/BG1/Content/Building/{cityType * 1000}");

                        if(rebuild != null && rebuild.gameObject.activeInHierarchy)
                        {
                            path = $"FullScreenBgCanvas/Root/BG1/Content/Building/{cityType * 1000}";
                        }
                    }
                    
                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMainCity", UIItem = path });

                    
                        guidData.guidItems.Add(new UIGuidItem() { UIName = "UICityLevelUp", UIItem = $"Root/mBtnRoot/Button"  });
                    
                    UIGuid.StartGuid(guidData);
                });
            }
        }
    }

    public class TaskTypeGoTo_710 : TaskGoTo
    {
        //710	回到主城（当前在主城界面，直接下一步）→点击建筑A→点击升级
        public void GoTo(params string[] parms)
        {
            //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.PRISON);
            var ui = PopupManager.I.FindPopup<UIMainCity>();
            if (ui != null )
            {
                PopupManager.I.ClearAllPopup();

                int cityCfgID = LPlayer.I.GetCityBuildingIDByType(1);
                ui.dragImage.MoveTOCfg(cityCfgID, () =>
                {
                    var guidData = new UIGuidData(); 
                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMainCity", UIItem = $"FullScreenBgCanvas/Root/BG1/Content/Building/build_1" });
                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UICityLevelUp", UIItem = $"Root/mBtnRoot/Button" });
                    UIGuid.StartGuid(guidData);
                });
            }
        }
    }



    public class TaskTypeGoTo_120 : TaskGoTo
    {
        //120	回到主城（当前在主城界面，直接下一步）→点击建筑A的布阵按钮
        public void GoTo(params string[] parms)
        {
            //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.PRISON);
            var ui = PopupManager.I.FindPopup<UIMainCity>();
            if (ui != null && parms.Length > 0 && int.TryParse(parms[0], out int cityType))
            {
                PopupManager.I.ClearAllPopup();

                int cityCfgID = LPlayer.I.GetCityBuildingIDByType(cityType);
                ui.dragImage.MoveTOCfg(cityCfgID, () =>
                {
                    var guidData = new UIGuidData();

                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMainCity", UIItem = $"FullScreenBgCanvas/Root/BG1/Content/Building/build_{cityType}" });
                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UICityLevelUp", UIItem = $"Root/mBtnRoot/Button" });
                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMergeSpecialBoxSetHeroView", UIItem = $"Root/mBtn", slide = true });
                    UIGuid.StartGuid(guidData);
                });
            }
        }
    }


    public class TaskTypeGoTo_121 : TaskGoTo
    {
        //121	回到主城（当前在主城界面，直接下一步）→点击建筑A的布阵按钮
        public void GoTo(params string[] parms)
        {
            //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.PRISON);
            var ui = PopupManager.I.FindPopup<UIMainCity>();
            if (ui != null && parms.Length > 0 && int.TryParse(parms[0], out int cityType))
            {
                PopupManager.I.ClearAllPopup();

                int cityCfgID = LPlayer.I.GetCityBuildingIDByType(cityType);
                ui.dragImage.MoveTOCfg(cityCfgID, () =>
                {
                    var guidData = new UIGuidData();

                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMainCity", UIItem = $"FullScreenBgCanvas/Root/BG1/Content/Building/build_{cityType}" });
                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UICityLevelUp", UIItem = $"Root/mBtnRoot/Button" });
                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMergeSpecialBoxSetHeroView", UIItem = $"Root/mBtn", slide = true });
                    UIGuid.StartGuid(guidData);
                });
            }
        }
    }


    public class TaskTypeGoTo_122 : TaskGoTo
    {
        //122	回到主城（当前在主城界面，直接下一步）→点击建筑A的布阵按钮
        public void GoTo(params string[] parms)
        {
            //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.PRISON);
            var ui = PopupManager.I.FindPopup<UIMainCity>();
            if (ui != null && parms.Length > 0 && int.TryParse(parms[0], out int cityType))
            {
                PopupManager.I.ClearAllPopup();

                int cityCfgID = LPlayer.I.GetCityBuildingIDByType(cityType);
                ui.dragImage.MoveTOCfg(cityCfgID, () =>
                {
                    var guidData = new UIGuidData();

                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMainCity", UIItem = $"FullScreenBgCanvas/Root/BG1/Content/Building/build_{cityType}" });
                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UICityLevelUp", UIItem = $"Root/mBtnRoot/Button" });
                    UIGuid.StartGuid(guidData);
                });
            }
        }
    }

    public class TaskTypeGoTo_725 : TaskTypeGoTo_724{}
    public class TaskTypeGoTo_724 : TaskGoTo
    {
        //724	直接切到大地图（当前在大地图，直接下一步）→点击主界面雷达按钮
        public void GoTo(params string[] parms)
        {
            PopupManager.I.ClearAllPopup();

            if (GameData.I.MainData.CurrMenuType == MainMenuType.CITY)//PRISON
            {
                var ui = PopupManager.I.FindPopup<UIMainCity>();
                int cityType = (int)MainCityItem.CityType.Star;
                int cityCfgID = LPlayer.I.GetCityBuildingIDByType(cityType);
                ui.dragImage.MoveTOCfg(cityCfgID, () =>
                {
                    var guidData = new UIGuidData();
                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMainCity", UIItem = $"FullScreenBgCanvas/Root/BG1/Content/Building/build_{cityType}" });
                    UIGuid.StartGuid(guidData);
                });

            }
            else
            {

                NTimer.CountDown(0.5f, () =>
                {
                    var guidData = new UIGuidData();

                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMain2", UIItem = $"content/RightBtns/StargazingPlatform", slide = true });

                    UIGuid.StartGuid(guidData);
                });
            }
        }
    }

    public class TaskTypeGoTo_410 : TaskGoTo
    {
        //410	回到主城（当前在主城界面，直接下一步）→点击新兵营
        public void GoTo(params string[] parms)
        {
            PopupManager.I.ClearAllPopup();
            //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.PRISON);
            var ui = PopupManager.I.FindPopup<UIMainCity>();
            if (ui != null)
            {
                int cityCfgID = LPlayer.I.GetCityBuildingIDByType((int)MainCityItem.CityType.Barrack);
                ui.dragImage.MoveTOCfg(cityCfgID, () =>
                {

                    var guidData = new UIGuidData();

                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMainCity", UIItem = $"FullScreenBgCanvas/Root/BG1/Content/Building/build_{((int)MainCityItem.CityType.Barrack)}"});
                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UICreateSoldierInfoPanel", UIItem = $"Root/Content/btnAutoCreate", slide = true });
                    UIGuid.StartGuid(guidData); 
                });
            }
        }
    }

    public class TaskTypeGoTo_312 : TaskTypeGoTo_311{}
    public class TaskTypeGoTo_313 : TaskTypeGoTo_311{}
    public class TaskTypeGoTo_311 : TaskGoTo
    {
        //311	回到主城（当前在主城界面，直接下一步）→点击科技
        public void GoTo(params string[] parms)
        {
            if (!UnlockMgr.I.CheckUnlock(UnlockFuncEnum.Building_6))
            {
                FloatTips.I.FloatMsg(UnlockMgr.I.GetUnlockDesc(UnlockFuncEnum.Building_6));
                return;
            }

            //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.PRISON);
            var ui = PopupManager.I.FindPopup<UIMainCity>();
            if (ui != null)
            {
                PopupManager.I.ClearAllPopup();

                int cityCfgID = LPlayer.I.GetCityBuildingIDByType((int)MainCityItem.CityType.Academy);
                ui.dragImage.MoveTOCfg(cityCfgID, () =>
                {
                    var guidData = new UIGuidData();

                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMainCity", UIItem = $"FullScreenBgCanvas/Root/BG1/Content/Building/build_{(int)MainCityItem.CityType.Academy}" });

                    UIGuid.StartGuid(guidData);
                });
            }
        }
    }
    
    public class TaskTypeGoTo_734 : TaskGoTo
    {
        // 734 捐献联盟科技X次 点击联盟→点击联盟科技→点击推荐科技
        public void GoTo(params string[] parms)
        {
           
            PopupManager.I.ClearAllPopup();

            NTimer.CountDown(0.5f,()=>
            {
                var guidData = new UIGuidData();

                guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMain2", UIItem = $"content/MenuRoot/Alliance", slide = true });
                if (LPlayer.I.IsPlayerInUnion())
                {
                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIAlliance", UIItem = $"Root/Down/TechItem"});
                }
                UIGuid.StartGuid(guidData);
            });
        }
    }

    public class TaskTypeGoTo_726 : TaskTypeGoTo_727 { }
    public class TaskTypeGoTo_727 : TaskGoTo
    {
        // 727 发起联盟帮助XX次（请求帮助） 回到主城或大地图（当前在主城或大地图直接下一步）→点击联盟→点击帮助
        public void GoTo(params string[] parms)
        {
            PopupManager.I.ClearAllPopup();

            NTimer.CountDown(0.5f,()=>
            {
                var guidData = new UIGuidData();

                guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMain2", UIItem = $"content/MenuRoot/Alliance", slide = true });
                if (LPlayer.I.IsPlayerInUnion())
                {
                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIAlliance", UIItem = $"Root/Down/HelpItem"});
                }
                UIGuid.StartGuid(guidData);
            });
        }
    }
    
    public class TaskTypeGoTo_411 : TaskGoTo
    {
        // 727 发起联盟帮助XX次（请求帮助） 回到主城或大地图（当前在主城或大地图直接下一步）→点击联盟→点击帮助
        public void GoTo(params string[] parms)
        {
            PopupManager.I.ClearAllPopup();
            //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.PRISON);
            var ui = PopupManager.I.FindPopup<UIMainCity>();
            if (ui != null)
            {
                int cityCfgID = LPlayer.I.GetCityBuildingIDByType((int)MainCityItem.CityType.Barrack);
                ui.dragImage.MoveTOCfg(cityCfgID, () =>
                {

                    var guidData = new UIGuidData();

                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMainCity", UIItem = $"FullScreenBgCanvas/Root/BG1/Content/Building/build_{((int)MainCityItem.CityType.Barrack)}" });
                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UICreateSoldierInfoPanel", UIItem = $"Root/Content/btnAutoCreate", slide = true });
                    UIGuid.StartGuid(guidData);
                });
            }
        }
    }
    
    //public class TaskTypeGoTo_119 : TaskGoTo
    //{
    //    // 119	回到主城（当前在主城界面，直接下一步）→点击照片墙
    //    public void GoTo(params string[] parms)
    //    {
    //        var guidData = new UIGuidData();
            
    //        var uiMerge = PopupManager.I.FindPopup<UIMerge>();
    //        if (uiMerge != null)
    //        {
    //            if (uiMerge.box1.boxAnimation.gameObject.activeInHierarchy)
    //            {
    //                guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMerge", UIItem = $"Root/bottomBtns/SpecialBox", slide = true });
    //            }
    //            //else if (uiMerge.box2.boxAnimation.gameObject.activeInHierarchy)
    //            //{
    //            //    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMerge", UIItem = $"Root/bottomBtns/SpecialBox1", slide = true });
    //            //}
    //            //else if (uiMerge.box3.boxAnimation.gameObject.activeInHierarchy)
    //            //{
    //            //    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMerge", UIItem = $"Root/bottomBtns/SpecialBox2", slide = true });
    //            //}
    //            //else if (uiMerge.box4.boxAnimation.gameObject.activeInHierarchy)
    //            //{
    //            //    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMerge", UIItem = $"Root/bottomBtns/SpecialBox3", slide = true });
    //            //}
    //            //else if (uiMerge.box5.boxAnimation.gameObject.activeInHierarchy)
    //            //{
    //            //    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMerge", UIItem = $"Root/bottomBtns/SpecialBox4", slide = true });
    //            //}
    //            UIGuid.StartGuid(guidData);
    //        }
    //        else
    //        {
    //            PopupManager.I.ClearAllPopup();
    //            EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.PRISON);
    //            NTimer.CountDown(0.5f,()=>
    //            {
    //                guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMain2", UIItem = $"content/Left_Top/btnPhotoWall/btnPhotoWall", slide = true });
    //                guidData.guidItems.Add(new UIGuidItem() { UIName = "UIPhotowall", UIItem = $"Root/bottom/btncatch", slide = true });
    //                UIGuid.StartGuid(guidData);
    //            });
    //        }
    //    }
    //}
}
