﻿using Common;
using Game.Config;
using Game.Data;
using K3;
using Logic;
using TFW.Localization;

namespace UI.Utils
{

    /// <summary>
    /// 主界面功能开放工具
    /// </summary>
    public class MainFunctionOpenUtils
    {
        #region 属性数据
        /// <summary>
        /// 联盟邀请开放状态
        /// </summary>
        public static bool UnionInviteState
        {
            get
            {
                return true;// GameData.I.LevelData.CanToDoByGameLevel(MetaConfig.UnionPushUnlockStage);
            }
        }

        public static bool OpenMenuBtnsState
        {
            get
            {
                return CityOpenState || HeroOpenState || ALllianceOpenState || WorldOpenState || FinishOrderTask;//ShopOpenState ||
            }
        }
        ///// <summary>
        ///// 联盟集结战争开放状态
        ///// </summary>
        //public static bool UnlockUnionRallyState
        //{
        //    get
        //    {
        //        var curLevel = GameData.I.LevelData.CurrGameLevel;
        //        if (curLevel >= MetaConfig.UnlockUnionRally)
        //        {
        //            return true;
        //        }
        //        return false;
        //    }
        //}
        public static bool FinishOrderTask
        {
            get
            {
                return MergeTaskMgr.I.TotalCompleteTaskNum > 0;
            }
        }
        ///// <summary>
        ///// 英雄试用事件开放状态
        ///// </summary>
        //public static bool HeroTryEventState
        //{
        //    get
        //    {
        //        return GameData.I.LevelData.CanToDoByGameLevel(MetaConfig.EventsOpenStage);
        //    }
        //}

      
     

        /// <summary>
        /// 活动中心开放状态
        /// </summary>
        public static bool ActivityCenterOpenState
        {
            get
            {
                return UnlockMgr.I.CheckUnlock(UnlockFuncEnum.ActivityFunc); 
            }
        }

        ///// <summary>
        ///// 活动中心-开服冲关开放状态
        ///// </summary>
        //public static bool ActivityPsssLevelOpenState
        //{
        //    get
        //    {
        //        if (!GuideMgr.I.IsGuideDone)
        //            return false;

        //        if (!GameData.I.LevelData.CanToDoByGameLevel(MetaConfig.ActNewSerRankOpenLV))
        //        {
        //            return false;
        //        }
        //        return true;
        //    }
        //}


        ///// <summary>
        ///// 活动中心-七日登录开放状态
        ///// </summary>
        //public static bool ActivitySevenDayOpenState
        //{
        //    get
        //    {
        //        if (!GuideMgr.I.IsGuideDone)
        //            return false;

        //        if (!GameData.I.LevelData.CanToDoByGameLevel(MetaConfig.UnlockSevenDayActivity))
        //        {
        //            return false;
        //        }
        //        return true;
        //    }
        //}


        /// <summary>
        /// 玩家头像开放开放状态
        /// </summary>
        public static bool PlayerHeadOpenState
        {
            get
            {
                //if (!GuideMgr.I.IsGuideDone)
                //    return false;

                //if (!GameData.I.LevelData.CanToDoByGameLevel(MetaConfig.UnlockPlayerProfile))
                //{
                //    return false;
                //}
                return true;
            }
        }

        /// <summary>
        ///  主城及主UI按钮开放状态
        /// </summary>
        public static bool CityOpenState
        {
            get
            {
               
                return true;
            }
        }

        /// <summary>
        /// 自动合成开放状态
        /// </summary>
        public static bool AutoMergeOpenState
        {
            get
            {
                if (GameData.I.UnlockData.MaxLevel >= MetaConfig.OpenComPoseLevel)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
        }

        /// <summary>
        /// 联盟开放状态
        /// </summary>
        public static bool ALllianceOpenState
        {
            get
            {
                return UnlockMgr.I.CheckUnlock(UnlockFuncEnum.Alliance);
            }
        }

        /// <summary>
        /// 世界开放状态
        /// </summary>
        public static bool WorldOpenState
        {
            get
            {
                return UnlockMgr.I.CheckUnlock(UnlockFuncEnum.World);
            }
        }

        ///// <summary>
        ///// 部队开放状态
        ///// </summary>
        //public static bool TroopOpenState
        //{
        //    get
        //    {
        //        if (!GuideMgr.I.IsGuideDone)
        //            return false;

        //        if (!GameData.I.LevelData.CanToDoByGameLevel(MetaConfig.UnlockTroopsBtn))
        //        {
        //            return false;
        //        }
        //        return true;
        //    }
        //}

        ///// <summary>
        ///// 邮件开放状态
        ///// </summary>
        //public static bool EmailOpenState
        //{
        //    get
        //    {
        //        if (!GuideMgr.I.IsGuideDone)
        //            return false;

        //        if (!GameData.I.LevelData.CanToDoByGameLevel(MetaConfig.UnlockMailBtn))
        //        {
        //            return false;
        //        }
        //        return true;
        //    }
        //}

        /// <summary>
        /// 聊天开放状态
        /// </summary>
        public static bool ChatOpenState
        {
            get
            {
                return LSkill.I.CanToDoByCityLevel_ConfigID((int)MetaConfig.UnlockChatBtn);
            }
        }

        /// <summary>
        /// 英雄开放状态
        /// </summary>
        public static bool HeroOpenState
        {
            get
            {
                return UnlockMgr.I.CheckUnlock(UnlockFuncEnum.Hero);
            }
        }

        ///// <summary>
        ///// 商城开放状态
        ///// </summary>
        //public static bool ShopOpenState
        //{
        //    get
        //    {
        //        if (!GuideMgr.I.IsGuideDone)
        //            return false;

        //        if (!GameData.I.LevelData.CanToDoByGameLevel(MetaConfig.UnlockShopBtn))
        //        {
        //            return false;
        //        }
        //        return true;
        //    }
        //}

        ///// <summary>
        ///// 月卡开放状态
        ///// </summary>
        //public static bool MonthCardOpenState
        //{
        //    get
        //    {
        //        if (!GuideMgr.I.IsGuideDone)
        //            return false;

        //        if (!GameData.I.LevelData.CanToDoByGameLevel(MetaConfig.UnlockShopBtn))
        //        {
        //            return false;
        //        }
        //        return true;
        //    }
        //}


        #endregion


        #region 功能检测

        /// <summary>
        /// 返回传入的主界面菜单功能是否开放
        /// </summary>
        /// <param name="menuType"></param>
        /// <param name="showTip">是否弹出功能未开放提示</param>
        /// <returns></returns>
        public static bool IsMenuFunctionOpen(MainMenuType menuType, bool showTip = false)
        {
            var isFunctionOpen = false;
            UnlockFuncEnum funcEnum = UnlockFuncEnum.None;
            switch (menuType)
            {
                //case MainMenuType.PRISON:
                //    isFunctionOpen = PrisonOpenState; funcEnum = UnlockFuncEnum.Prison;
                //    break;
                //case MainMenuType.HERO:
                //    isFunctionOpen = HeroOpenState; funcEnum = UnlockFuncEnum.Hero;
                //    break;
                case MainMenuType.CITY:
                    isFunctionOpen = true;
                    break;
                //case MainMenuType.ALLIANCE:
                //    isFunctionOpen = ALllianceOpenState; funcEnum = UnlockFuncEnum.Alliance;
                //    break;
                case MainMenuType.WORLD:
                    isFunctionOpen = WorldOpenState; funcEnum = UnlockFuncEnum.World;
                    break;

                default:
                    break;
            }

            if (showTip && !isFunctionOpen)
            {
                FloatTips.I.FloatMsg(UnlockMgr.I.GetUnlockDesc(funcEnum));
            }

            return isFunctionOpen;
        }

        /// <summary>
        /// 检查功能是否解锁（根据cfgID和等级）
        /// ，解锁返回true
        /// by yhc 200608
        /// </summary>
        public static bool IsOpenState(int levelId, bool isShowFalseTips)
        {

            var isUnlock = true;//  GameData.I.LevelData.CanToDoByGameLevel(levelId);
            if (isShowFalseTips && !isUnlock)
            {
                UITools.PopTips(LocalizationMgr.Format("DEMO_37", levelId));
            }
            return isUnlock;
        }
        
        public static async void CheckCityBuildingUnlockState(int cityType)
        {
            if (!CSPlayer.I.HasUnlockMenus(cityType * 100))
            {
                MessageMgr.Send(new cspb.CityBuildingProgressUpReq() { type = cityType });

                K3Unlock.UnlockDatas.Enqueue(new K3UnlockData()
                {
                    unlockType = K3UnlockData.UnlockType.CityBuilding,
                    id = cityType
                });

                var cfg = await Cfg.C.CD2CityBuilding.GetConfigAsync(cityType * 1000);

                K3Unlock.ShowData(cfg?.UnlockPopup==1); 
            }

            // ShowMenuBtnUnlock(); 
        }

        /// <summary>
        /// 检测英雄开放状态
        /// </summary>
        /// <returns></returns>
        public static bool CheckHeroOpenState(bool isShowTips)
        {
            //if (!GuideMgr.I.IsGuideDone)
            //    return false;

            var isArriveLv = true;// GameData.I.LevelData.CurrGameLevel >= MetaConfig.Hero_System_OpenLevel;//关卡数是否大于要求
            var isUnlock = isArriveLv;//有英雄或达到关卡条件，就解锁
            if (!isUnlock)
            {
                if (!isArriveLv)
                {
                    if (isShowTips)
                    {
                        UITools.PopTips(LocalizationMgr.Format("DEMO_37", MetaConfig.Hero_System_OpenLevel + 1));
                    }
                }

                return false;
            }
            return true;
        }

        #endregion

         


        public static bool PrisonOpenState
        {
            get
            {
                return UnlockMgr.I.CheckUnlock(UnlockFuncEnum.Prison);
            }
        }
    }
}
