﻿using System;
using System.Collections.Generic;
using Common;
using Game.Data;
using TFW.Localization;
using TFW.UI;
using UnityEngine;

namespace UI
{
    public partial class UITechViewSub2Item : TFWLoopListViewItem
    {
        private UITechStageSub2Data mSub2Data;
        private HashSet<Enum_UITechViewItemPos> mNextPosSet = new HashSet<Enum_UITechViewItemPos>();

        private Dictionary<Enum_UITechViewItemPos, GameObject> mItems = new Dictionary<Enum_UITechViewItemPos, GameObject>();
        private Dictionary<Enum_UITechViewItemPos, TFWRawImage> mItemBgs = new Dictionary<Enum_UITechViewItemPos, TFWRawImage>();
        private Dictionary<Enum_UITechViewItemPos, TFWImage> mItemIcons = new Dictionary<Enum_UITechViewItemPos, TFWImage>();
        private Dictionary<Enum_UITechViewItemPos, TFWText> mItemNames = new Dictionary<Enum_UITechViewItemPos, TFWText>();
        private Dictionary<Enum_UITechViewItemPos, TFWImage> mItemProgresss = new Dictionary<Enum_UITechViewItemPos, TFWImage>();
        private Dictionary<Enum_UITechViewItemPos, TFWText> mItemProgressTxts = new Dictionary<Enum_UITechViewItemPos, TFWText>();
        private Dictionary<Enum_UITechViewItemPos, GameObject> mItemOKs = new Dictionary<Enum_UITechViewItemPos, GameObject>();
        private Dictionary<Enum_UITechViewItemPos, GameObject> mItemLocks = new Dictionary<Enum_UITechViewItemPos, GameObject>();
        private Dictionary<Enum_UITechViewItemPos, GameObject> mItemSelects = new Dictionary<Enum_UITechViewItemPos, GameObject>();
        
        public void Init()
        {
            AutoBindComponent();

            mItems = new Dictionary<Enum_UITechViewItemPos, GameObject>()
            {
                {Enum_UITechViewItemPos.Left, mItem0},
                {Enum_UITechViewItemPos.Center, mItem1},
                {Enum_UITechViewItemPos.Right, mItem2},
            };
            
            mItemBgs = new Dictionary<Enum_UITechViewItemPos, TFWRawImage>()
            {
                {Enum_UITechViewItemPos.Left, mItemBg0_TFWRawImage},
                {Enum_UITechViewItemPos.Center, mItemBg1_TFWRawImage},
                {Enum_UITechViewItemPos.Right, mItemBg2_TFWRawImage},
            };
            
            mItemIcons = new Dictionary<Enum_UITechViewItemPos, TFWImage>()
            {
                {Enum_UITechViewItemPos.Left, mItemIcon0_TFWImage},
                {Enum_UITechViewItemPos.Center, mItemIcon1_TFWImage},
                {Enum_UITechViewItemPos.Right, mItemIcon2_TFWImage},
            };
            
            mItemNames = new Dictionary<Enum_UITechViewItemPos, TFWText>()
            {
                {Enum_UITechViewItemPos.Left, mItemName0_TFWText},
                {Enum_UITechViewItemPos.Center, mItemName1_TFWText},
                {Enum_UITechViewItemPos.Right, mItemName2_TFWText},
            };
            
            mItemProgresss = new Dictionary<Enum_UITechViewItemPos, TFWImage>()
            {
                {Enum_UITechViewItemPos.Left, mItemProgress0_TFWImage},
                {Enum_UITechViewItemPos.Center, mItemProgress1_TFWImage},
                {Enum_UITechViewItemPos.Right, mItemProgress2_TFWImage},
            };
            
            mItemProgressTxts = new Dictionary<Enum_UITechViewItemPos, TFWText>()
            {
                {Enum_UITechViewItemPos.Left, mItemProgressLbl0_TFWText},
                {Enum_UITechViewItemPos.Center, mItemProgressLbl1_TFWText},
                {Enum_UITechViewItemPos.Right, mItemProgressLbl2_TFWText},
            };
            
            mItemOKs = new Dictionary<Enum_UITechViewItemPos, GameObject>()
            {
                {Enum_UITechViewItemPos.Left, mItemOk0},
                {Enum_UITechViewItemPos.Center, mItemOk1},
                {Enum_UITechViewItemPos.Right, mItemOk2},
            };
            
            mItemLocks = new Dictionary<Enum_UITechViewItemPos, GameObject>()
            {
                {Enum_UITechViewItemPos.Left, mItemLock0},
                {Enum_UITechViewItemPos.Center, mItemLock1},
                {Enum_UITechViewItemPos.Right, mItemLock2},
            };
            
            mItemSelects = new Dictionary<Enum_UITechViewItemPos, GameObject>()
            {
                {Enum_UITechViewItemPos.Left, mItemSelect0},
                {Enum_UITechViewItemPos.Center, mItemSelect1},
                {Enum_UITechViewItemPos.Right, mItemSelect2},
            };
            
            UI.UIBase.AddRemoveListener(TFW.EventTriggerType.Click, mBtn0, (x, y) =>
            {
                OnBtn(Enum_UITechViewItemPos.Left);
            });
            
            UI.UIBase.AddRemoveListener(TFW.EventTriggerType.Click, mBtn1, (x, y) =>
            {
                OnBtn(Enum_UITechViewItemPos.Center);
            });
            
            UI.UIBase.AddRemoveListener(TFW.EventTriggerType.Click, mBtn2, (x, y) =>
            {
                OnBtn(Enum_UITechViewItemPos.Right);
            });
            
            UI.UIBase.AddRemoveListener(TFW.EventTriggerType.Click, this.gameObject, (x, y) =>
            {
                UnSelect();

                UITechViewData.I.CloseTechViewBottom?.Invoke();
            });
            
            ClearLine();
            
            EventMgr.RegisterEvent(TEventType.K3NewTechSelected, _ => { RefreshSelect();}, this);
        }

        private void ClearLine()
        {
            mLine00.SetActive(false);
            mLine01.SetActive(false);
            
            mLine10.SetActive(false);
            mLine11.SetActive(false);
            mLine12.SetActive(false);
            
            mLine21.SetActive(false);
            mLine22.SetActive(false);
        }

        private void OnEnable()
        {
            EventMgr.RegisterEvent(TEventType.OnUpgradeSkillAck, OnTechUpgrade, this);
            EventMgr.RegisterEvent(TEventType.RefreshAssetAck, OnTechUpgrade, this);
            EventMgr.RegisterEvent(TEventType.RefreshTech, OnTechUpgrade, this);
        }

        private void OnDisable()
        {
            EventMgr.UnregisterEvent(this);
        }

        private void OnTechUpgrade(object[] _)
        {
            SetData(mSub2Data);
            ShowLine(mNextPosSet);
        }

        private void OnBtn(Enum_UITechViewItemPos pos)
        {
            // 通知科技界面

            var techType = mSub2Data.TechCfgs[pos];
            var techData = GameData.I.SkillData.MTechs[techType];

            if (UITechViewData.I.SelectedTechType == techData.Type)
            {
                UITechViewData.I.SelectedTechType = -1;
            }
            else
            {
                UITechViewData.I.SelectedTechType = techData.Type;
            }
            
            UITechViewData.I.ClickSub2ItemCallback?.Invoke(techData);

            EventMgr.FireEvent(TEventType.K3NewTechSelected);
        }

        public void SetData(UITechStageSub2Data sub2Data)
        {
            mSub2Data = sub2Data;
            
            UnSelect();
            Refresh();
        }

        public void Refresh()
        {
            foreach (var kv in mItems)
            {
                kv.Value.SetActive(false);
            }
            
            foreach (var pos in mSub2Data.GetPosSet())
            {
                RefreshItems(pos);
            }
        }

        private void RefreshItems(Enum_UITechViewItemPos pos)
        {
            var techType = mSub2Data.TechCfgs[pos];
            var techData = GameData.I.SkillData.MTechs[techType];
            
            mItems[pos].SetActive(true);

            var isLock = UITechViewData.I.IsTechLock(techData);
            
            mItemLocks[pos].SetActive(isLock);
            
            if (isLock)
            {
                UITools.SetDynamicRawImage(mItemBgs[pos], "Assets/K3/Res/Art/Textures/Tech/UI_Tech_ItemBg_03.png");
            }
            else
            {
                if (techData.MaxLevel)
                {
                    UITools.SetDynamicRawImage(mItemBgs[pos], "Assets/K3/Res/Art/Textures/Tech/UI_Tech_ItemBg_02.png");
                }
                else
                {
                    UITools.SetDynamicRawImage(mItemBgs[pos], "Assets/K3/Res/Art/Textures/Tech/UI_Tech_ItemBg_01.png");
                }
            }

            UITools.SetImageBySpriteName(mItemIcons[pos], techData.MConfig.Icon);

            mItemNames[pos].text = LocalizationMgr.Get(techData.MConfig.Name);

#if UNITY_EDITOR
            mItemNames[pos].text = LocalizationMgr.Get(techData.MConfig.Name) + "\n" + techData.MConfig.Id;
#endif

            mItemProgresss[pos].fillAmount = techData.Level * 1f / techData.MaxConfig.Level;
            
            mItemProgressTxts[pos].text = techData.Level + "/" + techData.MaxConfig.Level;

            mItemOKs[pos].SetActive(techData.MaxLevel);

            mItemSelects[pos].SetActive(techData.Type == UITechViewData.I.SelectedTechType);
        }

        public void UnSelect()
        {
            mItemSelect0.SetActive(false);
            mItemSelect1.SetActive(false);
            mItemSelect2.SetActive(false);
        }

        public void RefreshSelect()
        {
            UnSelect();
            
            foreach (var kv in mSub2Data.TechCfgs)
            {
                if (kv.Value == UITechViewData.I.SelectedTechType)
                {
                    mItemSelects[kv.Key].SetActive(true);
                }
            }
        }

        public void ShowLine(HashSet<Enum_UITechViewItemPos> nextPosSet)
        {
            mNextPosSet = nextPosSet;
            
            if (nextPosSet == null)
            {
                return;
            }

            var curPosSet = mSub2Data.GetPosSet();

            foreach (var curPos in curPosSet)
            {
                var techData = GameData.I.SkillData.MTechs[mSub2Data.TechCfgs[curPos]];
                var maxLv = techData.MaxLevel;
                
                switch (curPos)
                {
                    case Enum_UITechViewItemPos.Left:
                    {
                        mLine00.SetActive(nextPosSet.Contains(Enum_UITechViewItemPos.Left));
                        mLine01.SetActive(nextPosSet.Contains(Enum_UITechViewItemPos.Center));
                        
                        RefreshLineColor(mLine00, maxLv);
                        RefreshLineColor(mLine01, maxLv);
                        
                        break;
                    }
                    case Enum_UITechViewItemPos.Center:
                    {
                        mLine10.SetActive(nextPosSet.Contains(Enum_UITechViewItemPos.Left));
                        mLine11.SetActive(nextPosSet.Contains(Enum_UITechViewItemPos.Center));
                        mLine12.SetActive(nextPosSet.Contains(Enum_UITechViewItemPos.Right));
                        
                        RefreshLineColor(mLine10, maxLv);
                        RefreshLineColor(mLine11, maxLv);
                        RefreshLineColor(mLine12, maxLv);
                        
                        break;
                    }
                    case Enum_UITechViewItemPos.Right:
                    {
                        mLine21.SetActive(nextPosSet.Contains(Enum_UITechViewItemPos.Center));
                        mLine22.SetActive(nextPosSet.Contains(Enum_UITechViewItemPos.Right));
                        
                        RefreshLineColor(mLine21, maxLv);
                        RefreshLineColor(mLine22, maxLv);
                        
                        break;
                    }
                }
            }
        }
        
        private void RefreshLineColor(GameObject lineGo, bool maxLv)
        {
            var img = lineGo.GetComponent<TFWImage>();
            img.color = maxLv ? new Color(255f/ 255, 198f/ 255, 0f/ 255) : Color.black;
            
            for (int i = 0; i < lineGo.transform.childCount; i++)
            {
                var go = lineGo.transform.GetChild(i).gameObject;
                RefreshLineColor(go, maxLv);
            }
        }
    }
    
    
    public partial class UITechViewSub2Item
    {
        protected UnityEngine.GameObject GameObject => this.gameObject;
        
        
        
        
        
        
        protected UnityEngine.GameObject mItem0;

        protected UnityEngine.GameObject mItemBg0;

        protected TFW.UI.TFWRawImage mItemBg0_TFWRawImage;

        protected UnityEngine.GameObject mItemIcon0;

        protected TFW.UI.TFWImage mItemIcon0_TFWImage;

        protected UnityEngine.GameObject mItemName0;

        protected TFW.UI.TFWText mItemName0_TFWText;

        protected UnityEngine.GameObject mItemProgress0;

        protected TFW.UI.TFWImage mItemProgress0_TFWImage;

        protected UnityEngine.GameObject mItemProgressLbl0;

        protected TFW.UI.TFWText mItemProgressLbl0_TFWText;

        protected UnityEngine.GameObject mItemOk0;

        protected TFW.UI.TFWRawImage mItemOk0_TFWRawImage;

        protected UnityEngine.GameObject mItemLock0;

        protected TFW.UI.TFWRawImage mItemLock0_TFWRawImage;

        protected UnityEngine.GameObject mBtn0;

        protected UnityEngine.GameObject mItemSelect0;

        protected TFW.UI.TFWRawImage mItemSelect0_TFWRawImage;

        protected UnityEngine.GameObject mLine01;

        protected TFW.UI.TFWImage mLine01_TFWImage;

        protected UnityEngine.GameObject mLine00;

        protected TFW.UI.TFWImage mLine00_TFWImage;

        protected UnityEngine.GameObject mItem1;

        protected UnityEngine.GameObject mItemBg1;

        protected TFW.UI.TFWRawImage mItemBg1_TFWRawImage;

        protected UnityEngine.GameObject mItemIcon1;

        protected TFW.UI.TFWImage mItemIcon1_TFWImage;

        protected UnityEngine.GameObject mItemName1;

        protected TFW.UI.TFWText mItemName1_TFWText;

        protected UnityEngine.GameObject mItemProgress1;

        protected TFW.UI.TFWImage mItemProgress1_TFWImage;

        protected UnityEngine.GameObject mItemProgressLbl1;

        protected TFW.UI.TFWText mItemProgressLbl1_TFWText;

        protected UnityEngine.GameObject mItemOk1;

        protected TFW.UI.TFWRawImage mItemOk1_TFWRawImage;

        protected UnityEngine.GameObject mItemLock1;

        protected TFW.UI.TFWRawImage mItemLock1_TFWRawImage;

        protected UnityEngine.GameObject mBtn1;

        protected UnityEngine.GameObject mItemSelect1;

        protected TFW.UI.TFWRawImage mItemSelect1_TFWRawImage;

        protected UnityEngine.GameObject mLine11;

        protected TFW.UI.TFWImage mLine11_TFWImage;

        protected UnityEngine.GameObject mLine10;

        protected TFW.UI.TFWImage mLine10_TFWImage;

        protected UnityEngine.GameObject mLine12;

        protected TFW.UI.TFWImage mLine12_TFWImage;

        protected UnityEngine.GameObject mItem2;

        protected UnityEngine.GameObject mItemBg2;

        protected TFW.UI.TFWRawImage mItemBg2_TFWRawImage;

        protected UnityEngine.GameObject mItemIcon2;

        protected TFW.UI.TFWImage mItemIcon2_TFWImage;

        protected UnityEngine.GameObject mItemName2;

        protected TFW.UI.TFWText mItemName2_TFWText;

        protected UnityEngine.GameObject mItemProgress2;

        protected TFW.UI.TFWImage mItemProgress2_TFWImage;

        protected UnityEngine.GameObject mItemProgressLbl2;

        protected TFW.UI.TFWText mItemProgressLbl2_TFWText;

        protected UnityEngine.GameObject mItemOk2;

        protected TFW.UI.TFWRawImage mItemOk2_TFWRawImage;

        protected UnityEngine.GameObject mItemLock2;

        protected TFW.UI.TFWRawImage mItemLock2_TFWRawImage;

        protected UnityEngine.GameObject mBtn2;

        protected UnityEngine.GameObject mItemSelect2;

        protected TFW.UI.TFWRawImage mItemSelect2_TFWRawImage;

        protected UnityEngine.GameObject mLine21;

        protected TFW.UI.TFWImage mLine21_TFWImage;

        protected UnityEngine.GameObject mLine22;

        protected TFW.UI.TFWImage mLine22_TFWImage;


                
        protected void AutoBindComponent()
        {
            mItem0 = this.GameObject.transform.Find("Root/mItem0").gameObject;
            mItemBg0 = this.GameObject.transform.Find("Root/mItem0/mItemBg0").gameObject;
            mItemBg0_TFWRawImage = mItemBg0.GetComponent<TFW.UI.TFWRawImage>();

            mItemIcon0 = this.GameObject.transform.Find("Root/mItem0/mItemBg0/mItemIcon0").gameObject;
            mItemIcon0_TFWImage = mItemIcon0.GetComponent<TFW.UI.TFWImage>();

            mItemName0 = this.GameObject.transform.Find("Root/mItem0/mItemBg0/mItemName0").gameObject;
            mItemName0_TFWText = mItemName0.GetComponent<TFW.UI.TFWText>();

            mItemProgress0 = this.GameObject.transform.Find("Root/mItem0/mItemBg0/mItemProgress0").gameObject;
            mItemProgress0_TFWImage = mItemProgress0.GetComponent<TFW.UI.TFWImage>();

            mItemProgressLbl0 = this.GameObject.transform.Find("Root/mItem0/mItemBg0/mItemProgress0/mItemProgressLbl0").gameObject;
            mItemProgressLbl0_TFWText = mItemProgressLbl0.GetComponent<TFW.UI.TFWText>();

            mItemOk0 = this.GameObject.transform.Find("Root/mItem0/mItemBg0/mItemOk0").gameObject;
            mItemOk0_TFWRawImage = mItemOk0.GetComponent<TFW.UI.TFWRawImage>();

            mItemLock0 = this.GameObject.transform.Find("Root/mItem0/mItemBg0/mItemLock0").gameObject;
            mItemLock0_TFWRawImage = mItemLock0.GetComponent<TFW.UI.TFWRawImage>();

            mBtn0 = this.GameObject.transform.Find("Root/mItem0/mItemBg0/mBtn0").gameObject;
            mItemSelect0 = this.GameObject.transform.Find("Root/mItem0/mItemBg0/mItemSelect0").gameObject;
            mItemSelect0_TFWRawImage = mItemSelect0.GetComponent<TFW.UI.TFWRawImage>();

            mLine01 = this.GameObject.transform.Find("Root/mItem0/mLine01").gameObject;
            mLine01_TFWImage = mLine01.GetComponent<TFW.UI.TFWImage>();

            mLine00 = this.GameObject.transform.Find("Root/mItem0/mLine00").gameObject;
            mLine00_TFWImage = mLine00.GetComponent<TFW.UI.TFWImage>();

            mItem1 = this.GameObject.transform.Find("Root/mItem1").gameObject;
            mItemBg1 = this.GameObject.transform.Find("Root/mItem1/mItemBg1").gameObject;
            mItemBg1_TFWRawImage = mItemBg1.GetComponent<TFW.UI.TFWRawImage>();

            mItemIcon1 = this.GameObject.transform.Find("Root/mItem1/mItemBg1/mItemIcon1").gameObject;
            mItemIcon1_TFWImage = mItemIcon1.GetComponent<TFW.UI.TFWImage>();

            mItemName1 = this.GameObject.transform.Find("Root/mItem1/mItemBg1/mItemName1").gameObject;
            mItemName1_TFWText = mItemName1.GetComponent<TFW.UI.TFWText>();

            mItemProgress1 = this.GameObject.transform.Find("Root/mItem1/mItemBg1/mItemProgress1").gameObject;
            mItemProgress1_TFWImage = mItemProgress1.GetComponent<TFW.UI.TFWImage>();

            mItemProgressLbl1 = this.GameObject.transform.Find("Root/mItem1/mItemBg1/mItemProgress1/mItemProgressLbl1").gameObject;
            mItemProgressLbl1_TFWText = mItemProgressLbl1.GetComponent<TFW.UI.TFWText>();

            mItemOk1 = this.GameObject.transform.Find("Root/mItem1/mItemBg1/mItemOk1").gameObject;
            mItemOk1_TFWRawImage = mItemOk1.GetComponent<TFW.UI.TFWRawImage>();

            mItemLock1 = this.GameObject.transform.Find("Root/mItem1/mItemBg1/mItemLock1").gameObject;
            mItemLock1_TFWRawImage = mItemLock1.GetComponent<TFW.UI.TFWRawImage>();

            mBtn1 = this.GameObject.transform.Find("Root/mItem1/mItemBg1/mBtn1").gameObject;
            mItemSelect1 = this.GameObject.transform.Find("Root/mItem1/mItemBg1/mItemSelect1").gameObject;
            mItemSelect1_TFWRawImage = mItemSelect1.GetComponent<TFW.UI.TFWRawImage>();

            mLine11 = this.GameObject.transform.Find("Root/mItem1/mLine11").gameObject;
            mLine11_TFWImage = mLine11.GetComponent<TFW.UI.TFWImage>();

            mLine10 = this.GameObject.transform.Find("Root/mItem1/mLine10").gameObject;
            mLine10_TFWImage = mLine10.GetComponent<TFW.UI.TFWImage>();

            mLine12 = this.GameObject.transform.Find("Root/mItem1/mLine12").gameObject;
            mLine12_TFWImage = mLine12.GetComponent<TFW.UI.TFWImage>();

            mItem2 = this.GameObject.transform.Find("Root/mItem2").gameObject;
            mItemBg2 = this.GameObject.transform.Find("Root/mItem2/mItemBg2").gameObject;
            mItemBg2_TFWRawImage = mItemBg2.GetComponent<TFW.UI.TFWRawImage>();

            mItemIcon2 = this.GameObject.transform.Find("Root/mItem2/mItemBg2/mItemIcon2").gameObject;
            mItemIcon2_TFWImage = mItemIcon2.GetComponent<TFW.UI.TFWImage>();

            mItemName2 = this.GameObject.transform.Find("Root/mItem2/mItemBg2/mItemName2").gameObject;
            mItemName2_TFWText = mItemName2.GetComponent<TFW.UI.TFWText>();

            mItemProgress2 = this.GameObject.transform.Find("Root/mItem2/mItemBg2/mItemProgress2").gameObject;
            mItemProgress2_TFWImage = mItemProgress2.GetComponent<TFW.UI.TFWImage>();

            mItemProgressLbl2 = this.GameObject.transform.Find("Root/mItem2/mItemBg2/mItemProgress2/mItemProgressLbl2").gameObject;
            mItemProgressLbl2_TFWText = mItemProgressLbl2.GetComponent<TFW.UI.TFWText>();

            mItemOk2 = this.GameObject.transform.Find("Root/mItem2/mItemBg2/mItemOk2").gameObject;
            mItemOk2_TFWRawImage = mItemOk2.GetComponent<TFW.UI.TFWRawImage>();

            mItemLock2 = this.GameObject.transform.Find("Root/mItem2/mItemBg2/mItemLock2").gameObject;
            mItemLock2_TFWRawImage = mItemLock2.GetComponent<TFW.UI.TFWRawImage>();

            mBtn2 = this.GameObject.transform.Find("Root/mItem2/mItemBg2/mBtn2").gameObject;
            mItemSelect2 = this.GameObject.transform.Find("Root/mItem2/mItemBg2/mItemSelect2").gameObject;
            mItemSelect2_TFWRawImage = mItemSelect2.GetComponent<TFW.UI.TFWRawImage>();

            mLine21 = this.GameObject.transform.Find("Root/mItem2/mLine21").gameObject;
            mLine21_TFWImage = mLine21.GetComponent<TFW.UI.TFWImage>();

            mLine22 = this.GameObject.transform.Find("Root/mItem2/mLine22").gameObject;
            mLine22_TFWImage = mLine22.GetComponent<TFW.UI.TFWImage>();


        }
    }
}