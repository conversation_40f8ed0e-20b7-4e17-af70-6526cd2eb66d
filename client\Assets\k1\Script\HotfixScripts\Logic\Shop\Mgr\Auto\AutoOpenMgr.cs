﻿using Common;
using cspb;
using Cysharp.Threading.Tasks;
using DeepUI;
using Game.Config;
using Game.Data;
using GameState;
using K3;
using System;
using TFW;
using THelper;
using UI;

namespace Logic
{
    /// <summary>
    /// 自动打开面板管理
    /// </summary>
    public class AutoOpenMgr : Ins<AutoOpenMgr>,IBeforeLoginCallback,ILogoutCallback,ILoginCallback
    {
        /// <summary>
        /// 自动弹出触发包上次记录日期,该年中的第几天
        /// </summary>
        const string AutoOpenTriggerGiftLastRecordDay = nameof(AutoOpenTriggerGiftLastRecordDay);

        /// <summary>
        /// 自动弹出触发包次数
        /// </summary>
        const string AutoOpenTriggerGiftTimes = nameof(AutoOpenTriggerGiftTimes);

        /// <summary>
        /// 能否自动弹出触发包，调用时会自动更新弹出次数和弹出时间，目前要求为每天弹三次
        /// </summary>
        private bool CanAutoOpenTriggerGift(int maxTimesPerDay = 3)
        {
            return CanAutoOpen(AutoOpenTriggerGiftTimes, AutoOpenTriggerGiftLastRecordDay, maxTimesPerDay); ;
        }

        /// <summary>
        /// 能否自动弹出，调用时会自动更新弹出次数和弹出时间，目前要求为每天弹三次
        /// </summary>
        private bool CanAutoOpen(string openTimeKey, string openDayKey, int maxTimesPerDay = 3)
        {
            var canAutoOpen = false;
            var timesKey = $"{openTimeKey}_{LPlayer.I.PlayerID}";
            var dayKey = $"{openDayKey}_{LPlayer.I.PlayerID}";

            if (!GamePlayerPrefs.HasKey(dayKey) || GamePlayerPrefs.GetInt(dayKey) != DateTime.UtcNow.DayOfYear)
            {
                canAutoOpen = true;
                GamePlayerPrefs.Set(dayKey, DateTime.UtcNow.DayOfYear);
                GamePlayerPrefs.RemoveData(timesKey);
            }

            if (GamePlayerPrefs.HasKey(timesKey))
            {
                var times = GamePlayerPrefs.GetInt(timesKey);
                if (times < maxTimesPerDay)
                {
                    canAutoOpen = true;
                    GamePlayerPrefs.Set(timesKey, times + 1);
                }
            }
            else
            {
                canAutoOpen = true;
                GamePlayerPrefs.Set(timesKey, 1);
            }

            return canAutoOpen;
        }

        /// <summary>
        /// 自动弹出首充三日购上次记录日期,该年中的第几天
        /// </summary>
        const string AutoOpenFirstChargeLastRecordDay = nameof(AutoOpenFirstChargeLastRecordDay);
        const string AutoOpenFirstChargeNewLastRecordDay = nameof(AutoOpenFirstChargeNewLastRecordDay);

        /// <summary>
        /// 自动弹出首充三日购次数
        /// </summary>
        const string AutoOpenFirstChargeTimes = nameof(AutoOpenFirstChargeTimes);
        const string AutoOpenFirstChargeNewTimes = nameof(AutoOpenFirstChargeNewTimes);
        /// <summary>
        /// 能否自动弹出首充三日购，调用时会自动更新弹出次数和弹出时间，目前要求为每天弹三次
        /// </summary>
        public bool CanAutoOpenFirstCharge(int maxTimesPerDay = 3)
        {
            var timesKey = $"{AutoOpenFirstChargeTimes}_{LPlayer.I.PlayerID}";
            var dayKey = $"{AutoOpenFirstChargeLastRecordDay}_{LPlayer.I.PlayerID}";
            return CanAutoOpen(timesKey, dayKey, maxTimesPerDay);
        }
        public bool CanAutoOpenFirstChargeNew(int maxTimesPerDay = 3)
        {
            var timesKey = $"{AutoOpenFirstChargeNewTimes}_{LPlayer.I.PlayerID}";
            var dayKey = $"{AutoOpenFirstChargeNewLastRecordDay}_{LPlayer.I.PlayerID}";
            return CanAutoOpen(timesKey, dayKey, maxTimesPerDay);
        }
        public void EnqueueAutoPopup<TPopup>(object data = null) where TPopup : BasePopup, new()
        {
            PopupManager.I.ShowPanel<TPopup>(data, null, Priority.Low);
        }

        /// <summary>
        /// 自动打开逻辑
        /// </summary>
        public void AutoOpenLogic()
        {
            //if(TransServerNtfMgr.I.hasWelcome)
            //{
            //    EnqueueAutoPopup<TransServerAutoPopUpUI.Layer>();
            //    TransServerNtfMgr.I.hasWelcome = false;
            //}
            var canPopFirstChargeNew = CanPopFirstChargeNew();
            if (canPopFirstChargeNew)
            {
                NTimer.CountDown(2, () => {
                    PopupManager.I.ShowPanel<UIFirstChargeNew>();
                });
            }
            var canPopFirstCharge = CanPopFirstCharge();
            var popNormalPackageTimes = GetPopNormalPackageTimes(canPopFirstCharge);
            if (canPopFirstCharge && firstOpenRecharge)
            { 
                FirstChargeNewUI.Layer.OpenAdaptively();
            }
            bool hasPop = false;
            if (popNormalPackageTimes > 0)
            {
                var actv = GameData.I.ActivityData.GetLoginAutoPopUpActivityInfo();
                
                if (actv != null && UI.Utils.MainFunctionOpenUtils.ActivityCenterOpenState)
                {
                    if (ActivityAutoOpenConfig.IsAnniversaryActivity(actv))
                    {
                        EnqueueAutoPopup<AnniversaryActivityAutoPopUpUI.Layer>(actv);
                    }
                    else if (ActivityAutoOpenConfig.IsReturnActivity(actv))
                    {
                        EnqueueAutoPopup<ReturnActivityAutoPopUpUI.Layer>(actv);
                    }
                    else
                    {
                        //打开活动自动弹出界面
                        //这个界面不要了
                        //EnqueueAutoPopup<UIActivityAutoPopUp>(actv);
                        //D.Normal?.Log($"打开活动自动弹出界面, actvCfgID = {actv.cfgID}");
                    }
                    hasPop = true;
                }
                else
                {
                    D.Normal?.Log("没有获取到活动");
                }
            }
            if (popNormalPackageTimes > 1)
            {
                hasPop = true;
                TriggerGiftUI.Layer.OpenSafely(Priority.Low);
            }
            if(hasPop)
            {
                AddPopTimes();
            }
            
        }

        public static bool firstOpenRecharge = true;
        public bool CanShowFirstRecharge()
        {
            return FirstChargeNewMgr.I.isValid
                && UnlockMgr.I.CheckUnlock(UnlockFuncEnum.FirstRecharge)
                && FirstChargeNewMgr.I.GetGiftData() != null;
        }

        #region 新的自动弹出规则

        // 首充出现关卡
        //private int _firstChargeStartLevel;

        // (策划案里的常数2)如果没点过首充，等于这关时强制弹（只在塔防界面）(不在开局这一套流程里)
        //private int _forcePopFirstChargeLevel;

        // (策划案里的常数3)小于等于这个关卡的时候，如果没购买首充，每次登录总是先弹首充,大于本关一定不弹首充
        //private int _loginPopFirstChargeLevel;

        // (策划案里的常数4)小于这个关卡的时候，还有没领取的首充奖励，每天第一次登录会弹出来
        //private int _firstLoginPopFirstChargeLevel;

        // (策划案里的常数5)大于等于这个关卡后，才有机会弹出非新手礼包
        //private int _normalPackageStartLevel;

        // (策划案里的常数6)每天常规礼包弹出次数要小于等于这个数
        private int _normalPackageMaxTimes;

        // (策划案里的常数7)首次登录，弹出礼包数量小于等于这个数
        private int _firstLoginPopMaxNum;

        // (策划案里的常数8)非首次登录，弹出礼包数量小于等于这个数
        private int _loginPopMaxNum;


        // 今天常规礼包弹了多少次
        public string popTimes
        {
            get
            {
                return $"{_popTimes}_{LPlayer.I.PlayerID}";
            }
        }
        private const string _popTimes = nameof(_popTimes);
        // 配合常规礼包弹多少次记录的日期
        public string popDay
        {
            get
            {
                return $"{_popDay}_{LPlayer.I.PlayerID}";
            }
        }
        private const string _popDay = nameof(_popDay);
        public string loginDay
        {
            get
            {
                return $"{_loginDay}_{LPlayer.I.PlayerID}";
            }
        }
        // 每次登录记录一下时间，用于判断是否首日登录
        private const string _loginDay = nameof(_loginDay);
        // 是否点击过首充按钮
        public string hasClickFirstCharge
        {
            get
            {
                return $"{_hasClickFirstCharge}_{LPlayer.I.PlayerID}";
            }
        }
        private const string _hasClickFirstCharge = nameof(_hasClickFirstCharge);
        public string hasClickFirstChargeNew
        {
            get
            {
                return $"{_hasClickFirstChargeNew}_{LPlayer.I.PlayerID}";
            }
        }
        private const string _hasClickFirstChargeNew = nameof(_hasClickFirstChargeNew);
        private int currentLevel
        {
            get
            {
                return 1;
                //return GameData.I.LevelData.CurrGameLevel;
            }
        }

        // 本次是否是首次登录
        private bool isFirstLogin;


        private void OnLevelUpdate(object[] objs = null)
        {
            // 刚收到这个消息的时候关卡数还没加上去,需求是到达这关后
            //int level = 1; // GameData.I.LevelData.CurrGameLevel;
            //if (level == _forcePopFirstChargeLevel)
            //{
                if (UnlockMgr.I.CheckUnlock( UnlockFuncEnum.FirstRecharge))
                {
                    if(LFirstCharge.I.FirstRechargeVersionInfoNtfData!= null)
                    {
                        if (LFirstCharge.I.FirstRechargeVersionInfoNtfData.version == NewFirstRechargeVer.NewFirstRechargeVerOld)
                        {
                            if (!HasClickFirstChargeBtn())
                            {
                                FirstChargeNewUI.Layer.OpenAdaptively();
                                SetClickFirstChargeBtn();
                            }
                        }else
                        {
                            if (!HasClickFirstChargeNewBtn())
                            {
                                PopupManager.I.ShowPanel<UIFirstChargeNew>();
                                SetClickFirstChargeNewBtn();
                            }
                        }
                        
                    }
                    else
                    {
                        if (!HasClickFirstChargeBtn())
                        {
                            FirstChargeNewUI.Layer.OpenAdaptively();
                            SetClickFirstChargeBtn();
                        }
                    }
                    
                }
            //}
        }

        public void Init()
        {
            firstOpenRecharge = true;
            //_firstChargeStartLevel = (int)MetaConfig.gift_first_hud_show;
            //_forcePopFirstChargeLevel = (int)MetaConfig.gift_first_popup_show;
            //_loginPopFirstChargeLevel = (int)MetaConfig.gift_first_priority_level;
            //_firstLoginPopFirstChargeLevel = (int)MetaConfig.gift_first_claim_priority_level;
            //_normalPackageStartLevel = (int)MetaConfig.gift_popup_earliest_time;
            _normalPackageMaxTimes = (int)MetaConfig.gift_popup_max_number;
            _firstLoginPopMaxNum = (int)MetaConfig.gift_firstpopup_num;
            _loginPopMaxNum = (int)MetaConfig.gift_no_firstpopup_num;

            // 初始化的时候设置是否是首次登录
            int today = new DateTime(GameTime.Tricks).DayOfYear;
            if (GamePlayerPrefs.HasKey(loginDay))
            {
                int recordToday = GamePlayerPrefs.GetInt(loginDay);
                isFirstLogin = recordToday != today;
            }
            else
            {
                isFirstLogin = true;
            }

            // 是首次登录存一下时间
            if(isFirstLogin)
            {
                GamePlayerPrefs.Set(loginDay, today);
            }
              
        }

        

        public void SetClickFirstChargeBtn()
        {
            GamePlayerPrefs.Set(hasClickFirstCharge, true);
        }
        public void SetClickFirstChargeNewBtn()
        {
            GamePlayerPrefs.Set(hasClickFirstChargeNew, true);
        }
        // 是否首充过
        private bool HasFirstCharge()
        {
            return FirstChargeNewMgr.I.buyOk;
        }

        // 是否还有首充后续奖励
        private bool HasFirstChargeGift()
        {
            if (FirstChargeNewMgr.I.isValid)
            {
                for (int i = 1; i <= 3; i++)
                {
                    if (FirstChargeNewMgr.I.ShouldShowRedDot(i))
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        // 是否点击过首充按钮
        private bool HasClickFirstChargeNewBtn()
        {
            if (GamePlayerPrefs.HasKey(hasClickFirstChargeNew))
            {
                return GamePlayerPrefs.GetBool(hasClickFirstChargeNew);
            }
            return false;
        }
        private bool HasClickFirstChargeBtn()
        {
            if (GamePlayerPrefs.HasKey(hasClickFirstCharge))
            {
                return GamePlayerPrefs.GetBool(hasClickFirstCharge);
            }
            return false;
        }
        // 今天常规礼包弹了多少次
        private int GetPopTimes()
        {
            var nowTime = new DateTime(GameTime.Tricks);
            var today = nowTime.DayOfYear;
            if (GamePlayerPrefs.HasKey(popDay))
            {
                if(today == GamePlayerPrefs.GetInt(popDay))
                {
                    if(GamePlayerPrefs.HasKey(popTimes))
                    {
                        return GamePlayerPrefs.GetInt(popTimes);
                    }
                    else
                    {
                        return 0;
                    }
                }
                else
                {
                    return 0;
                }
            }
            else
            {
                return 0;
            }
        }

        // 确定要弹普通礼包之后，弹出普通礼包次数+1，同时刷新对应日期
        private void AddPopTimes()
        {
            int today = new DateTime(GameTime.Tricks).DayOfYear;
            if (GamePlayerPrefs.HasKey(popDay))
            {
                if (today == GamePlayerPrefs.GetInt(popDay))
                {
                    if (GamePlayerPrefs.HasKey(popTimes))
                    {
                        var time = GamePlayerPrefs.GetInt(popTimes);
                        GamePlayerPrefs.Set(popTimes, time + 1);
                        return;
                    }
                }
            }
            GamePlayerPrefs.Set(popDay, today);
            GamePlayerPrefs.Set(popTimes, 1);
        }

        private bool CanPopFirstCharge()
        {
            //if (currentLevel < _firstChargeStartLevel)
            //{
            //    return false;
            //}
            //if (currentLevel > _loginPopFirstChargeLevel)
            //{
            //    return false;
            //}
            if (LFirstCharge.I.FirstRechargeVersionInfoNtfData!= null && LFirstCharge.I.FirstRechargeVersionInfoNtfData.version != NewFirstRechargeVer.NewFirstRechargeVerOld)
                return false;
            if(HasFirstCharge())
            {
                // 首充过还有奖励没领取
                if(HasFirstChargeGift())
                {
                    // 首次登录
                    if(isFirstLogin)
                    {
                        // (currentLevel < _firstLoginPopFirstChargeLevel)
                        {
                            return true;
                        }
                        //else
                        //{
                        //    return false;
                        //}
                    }
                    else
                    {
                        return false;
                    }
                }
                // 首充过奖励都领取完了
                else
                {
                    return false;
                }
            }
            else
            {
                // 还没首充
                return true;
            }
        }
        //弹出首充和累充
        private bool CanPopFirstChargeNew()
        {
            //if (!GameData.I.LevelData.CanToDoByGameLevel(MetaConfig.FirstRechangeList_Unlock))
            //    return false;
            //if (currentLevel < _firstChargeStartLevel)
            //{
            //    return false;
            //}
            //if (currentLevel > _loginPopFirstChargeLevel)
            //{
            //    return false;
            //}
            if(LFirstCharge.I.FirstRechargeVersionInfoNtfData == null)
            {
                return false;
            }else
            {
                return !LFirstCharge.I.FirstRechargeVersionInfoNtfData.finish
                    && LFirstCharge.I.NewFirstRechargeVer1Data != null
                    && LFirstCharge.I.FirstRechargeVersionInfoNtfData.version == NewFirstRechargeVer.NewFirstRechargeVer1;
            }
            
        }
        // 获取这次能弹多少次常规礼包
        private int GetPopNormalPackageTimes(bool canPopFirstCharge)
        {
            D.Normal?.Log($"测试弹出参数: {canPopFirstCharge} {GetPopTimes()} {_normalPackageMaxTimes} {currentLevel}  {isFirstLogin} {_firstLoginPopMaxNum} {_loginPopMaxNum}");
            if(canPopFirstCharge)
            {
                return 0;
            }
            if (GetPopTimes() >= _normalPackageMaxTimes)
            {
                return 0;
            }
            //if (currentLevel <= _normalPackageStartLevel)
            //{
            //    return 0;
            //}
            // 首次登录
            if (isFirstLogin)
            {
                return _firstLoginPopMaxNum;
            }
            // 非首次登录
            else
            {
                return _loginPopMaxNum;
            }
        }

        #endregion

        #region 首充 每日特购 月卡购买后的触发逻辑

        private int popType = 0;

        public void AutoOpenPanel()
        {
            switch(popType)
            {
                case 1:
                    popType = 0;
                    FirstChargeNewUI.Layer.OpenAdaptively();
                    break;
                case 2:
                    popType = 0;
                    TaskUtils.JumpShop(ShopJumpMark.dailyDeal);
                    break;
                case 3:
                    popType = 0;
                    TaskUtils.JumpShop(ShopJumpMark.monthCard);
                    break;
            }
        }


        /// <summary>
        /// 充值完弹下一个
        /// </summary>
        public void AddAutoOpenPanel(bool result)
        {
            if (!result)
                return;
            if (!LSwitchMgr.I.IsFunctionOpen(SwitchConfig.NewMonthlycard))
                return;
            if(LFirstCharge.I.FirstRechargeVersionInfoNtfData!= null )
            {
                // 首充
                if (!FirstChargeNewMgr.I.buyOk && LFirstCharge.I.FirstRechargeVersionInfoNtfData.version == NewFirstRechargeVer.NewFirstRechargeVerOld)
                {
                    popType = 1;
                    return;
                }
            }else
            {
                if (!FirstChargeNewMgr.I.buyOk)
                {
                    popType = 1;
                    return;
                }
            }
           

            // 每日特购
            var gift = DailyDealMgr.I.giftItemData;
            if (gift != null)
            {
                var canPurchase = !GameData.I.GiftData.CheckGiftLimitBuy(gift.CfgId);
                if (canPurchase)
                {
                    popType = 2;
                    return;
                }
            }

            // 月卡
            if (!MonthCardMgr.I.GetHavaCard(MonthCardEnum.Common))
            {
                popType = 3;
            }

        }

        public UniTask OnBeforeLogin()
        {
            Init();

            return UniTask.CompletedTask;
        }

        public UniTask OnLogout()
        {
            EventMgr.UnregisterEvent(this);
            return UniTask.CompletedTask;
        }

        public UniTask OnLogin()
        {
            OnLevelUpdate(null);

            return UniTask.CompletedTask;
        }

        #endregion
    }
}
