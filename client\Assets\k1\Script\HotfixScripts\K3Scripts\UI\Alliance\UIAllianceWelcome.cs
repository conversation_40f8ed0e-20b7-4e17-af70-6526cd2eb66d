﻿using Common;
using DeepUI;
using Game.Config;
using Game.Data;
using TFW.UI;
using Logic;
using UI;
using UnityEngine;
using UnityEngine.EventSystems;

namespace K3
{
    /// <summary>
    /// 联盟推荐列表界面
    /// </summary>
    [Popup("Alliance/UIAllianceWelcome", true, true)]
    public class UIAllianceWelcome : BasePopupLayer
    {
        /// <summary>
        /// 创建按钮
        /// </summary>
        [PopupField("Panel/GroupBtns/CreatBtn")]
        private GameObject m_CreatBtn;

        /// <summary>
        /// 快速加入按钮
        /// </summary>
        [PopupField("Panel/GroupBtns/JoinQuicklyBtn")]
        private GameObject m_JoinQuicklyBtn;
        
        /// <summary>
        /// 关闭
        /// </summary>
        [PopupField("Panel/GroupBtns/BtnClose")]
        private GameObject m_CloseBtn;

        [PopupField("Panel/GroupDetails/GroupFirst/Icon")]
        private TFWImage m_Icon;
        
        [PopupField("Panel/GroupDetails/GroupFirst/Value")]
        private TFWText m_Value;
        
        protected override void OnInit()
        {
            base.OnInit();
            
        }

        MainMenuType openMenu;

        protected internal override void OnOpenStart()
        {
            base.OnOpenStart();

            openMenu = GameData.I.MainData.CurrMenuType;
            EventMgr.RegisterEvent(TEventType.AllianceJoinSuccess, objects =>
            {
                PopupManager.I.ClosePopup<UIChat>();
                CloseUI();
            }, this);
            
            EventMgr.RegisterEvent(TEventType.AllianceJoining, objects =>
            {
                //if (GameData.I.MainData.CurrMenuType != MainMenuType.ALLIANCE)
                //{
                //    EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.ALLIANCE);
                //}
                CloseUI();
                PopupManager.I.ClosePopup<UIChat>();
            }, this);
            
            EventMgr.RegisterEvent(TEventType.AllianceExiting, objects =>
            {
                CloseUI();
            }, this);
            
            AddClickListener(m_CloseBtn, (arg0, data) =>
            {
                CloseUI();
                //if (openMenu == MainMenuType.NONE || openMenu == MainMenuType.ALLIANCE)
                //    openMenu = MainMenuType.CITY;
                //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, openMenu);
            });
            AddClickListener(m_CreatBtn, OnCreatClick);
            AddClickListener(m_JoinQuicklyBtn, OnQuicklyJoinClick);

            
            UITools.SetVMIcon(m_Icon, MetaConfig.Union_Join_Get_Diamonds[0].Id);
            m_Value.text = $"X {MetaConfig.Union_Join_Get_Diamonds[0].Val}";
        }

        protected internal override void OnCloseStart()
        {
            base.OnCloseStart();

            EventMgr.UnregisterEvent(TEventType.AllianceJoinSuccess, this);
            EventMgr.UnregisterEvent(TEventType.AllianceJoining, this);
            EventMgr.UnregisterEvent(TEventType.AllianceExiting, this);
            
            RemoveClickListener(m_CloseBtn);
            RemoveClickListener(m_CreatBtn);
            RemoveClickListener(m_JoinQuicklyBtn);
        }

        private void CloseUI(object[] args = null)
        {
            PopupManager.I.ClosePopup<UIAllianceWelcome>();
        }

        /// <summary>
        /// 创建联盟
        /// </summary>
        private void OnCreatClick(GameObject gameObject, PointerEventData args)
        {
            CloseUI();
            PopupManager.I.ShowPanel<UINewAllianceStart>();
        }

		/// <summary>
		/// 快速加入联盟
		/// </summary>
		/// <param name="arg0"></param>
		/// <param name="arg1"></param>
		private void OnQuicklyJoinClick(GameObject arg0, PointerEventData arg1)
        {
            CloseUI();
            LAllianceMgr.I.QuicklyJoinUnionReq();
		}
    }
}
