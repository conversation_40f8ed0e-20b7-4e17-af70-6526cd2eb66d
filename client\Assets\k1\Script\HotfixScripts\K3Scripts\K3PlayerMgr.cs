﻿using Common;
using Config;
using cspb;
using Cysharp.Threading.Tasks;
using DeepUI;
using Game.Config;
using Game.Data;
using GameState;
using Logic;
using Public;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using TFW;
using THelper;
using UI;
using UnityEngine;
using static MainCityItem;

namespace K3
{
    public enum MoneyType
    {
        None, Gold = 11151001, Diamond = 11151002, Energy = 11171012, Maze, Star = 11151006,
    }

    public class K3PlayerMgr : Ins<K3PlayerMgr>, IBeforeLoginCallback, ILoginCallback, ILogoutCallback
    {
        public Action<int, int, int, int, bool> ChangeMoney = (gold, diamond, energy, star, isImmediately) => { };
        public Action<long> ChangeEnergyTimer = (timer) => { };
        private static DateTime UTC_1970 = new DateTime(1970, 1, 1);
        public const long UTC_1970_TICK = 621355968000000000;
        public bool isShowAllianceExitBtn = false;



        public string playerKey
        {
            get
            {
                return $"{LPlayer.I.PlayerID}_K3PlayerData";
            }
        }


        public Dictionary<int, UIMergeGridData_Maze> GridsData_Maze = new Dictionary<int, UIMergeGridData_Maze>();

        public UIMergeGridData[] GridsData = new UIMergeGridData[56];
        
        public UIMergeAreaData AreaData = new UIMergeAreaData();

        public List<int> BoxidQueue = new List<int>();

        public K3PlayerData PlayerData;

        //public UIMerge CurUIMerge;


        /// <summary>
        /// 获取本地时间戳
        /// </summary>
        public long GetLocalTimestamp()
        {
            return GameTime.Time;
        }


        internal void OnDataNtf(DataNtf msg)
        {
            //if (K1D2Config.I.IsFastLoginGold
            //        || K1D2Config.I.IsFastLoginDev || K1D2Config.I.IsFastLoginBate)
            //{
            //    //快速登录不用本地数据~· 
            //}
            //else
            //{
            //    string playerstr = PlayerPrefs.GetString(playerKey, "");

            //    if (!string.IsNullOrEmpty(playerstr) && playerstr != "null")
            //    {
            //        PlayerData = Newtonsoft.Json.JsonConvert.DeserializeObject<K3PlayerData>(playerstr);
            //    }
            //}

            if (msg != null && !string.IsNullOrEmpty(msg.data))
            {
                PlayerData = Newtonsoft.Json.JsonConvert.DeserializeObject<K3PlayerData>(msg.data); 
            }

            InitPlayeData();

            grid_dataNtf.x = 1;

            CheckNtfRecieve();
        }

        private void CheckNtfRecieve()
        {

            if (grid_dataNtf.x == 1 && grid_dataNtf.y == 1)
            {
                PlayerInfoReceived = true;
                 
                EventMgr.FireEvent(TEventType.CSInfoReceived);
                 
                //CloudEffectManager.I.PlayerCloudEffect(CloudEffectManager.PlayCloudEffectType.HideOpen);

                D.Warning?.Log($"CS数据已来到 PlayerInfoReceived！");

                grid_dataNtf = Vector2.zero;
            }
        }

        public const long TimeOffset = 1640995200;

        /// <summary>
        /// 获取ID 及 TS(ms)
        /// </summary>
        /// <param name="serverData"></param>
        /// <param name="id"></param>
        /// <param name="ts"></param>
        public void GetID(long serverData, out int id, out long ts)
        {
            ts = serverData / 10000000 * 1000; // ms
            id = (int)(serverData % 10000000);
            if (ts < 0)
                ts = -ts;
        }


        public long TOID(UIMergeGoodData goodData)
        {
            long toValue = 0;

            if (goodData != null)
            {
                toValue = goodData.Immovable ? goodData.id * -1 : goodData.id;

                if (goodData.creatTamp > 0)
                {
                    var tmp = goodData.creatTamp / 1000 * 10000000;

                    if (toValue < 0)
                    {
                        toValue = -(tmp + -toValue);
                    }
                    else
                    {
                        toValue = tmp + toValue;
                    }
                }
            }

            return toValue;
        }



        public void OnMergeNtf(MergeNtf mergeNtf)
        {
            if (CSPlayer.I.k3ToServerData?.CurStep >= mergeNtf.step)
                return;// 重连导致的 棋盘数据不同步 不进行刷新！ 以前端为准！！

            //D.Warning?.Log($" 【棋盘】CSPlayer.I.k3ToServerData?.CurStep:{CSPlayer.I.k3ToServerData?.CurStep} SyncStep:{mergeNtf.step} MergeNtf: GridData!");

            CSPlayer.I.k3ToServerData = new K3ToServerData(mergeNtf.step);// 
            //D.Error?.Log($"棋盘格子:{mergeNtf.grids.Count}");
            for (int i = 0; i < mergeNtf.grids.Count; i++)
            {
                var serverData = mergeNtf.grids[i];

                int x = i % CSPlayer.Grid_X;
                int y = i / CSPlayer.Grid_X;

                if (serverData == 0)
                {
                    GridsData[i] = new UIMergeGridData(x, y);
                }
                else
                {
                    GetID(serverData, out int id, out long ts);

                    ItemInfo itemInfo = CSVItem.GetItemInfoById(Math.Abs(id));

                    if (itemInfo != null)
                    {
                        itemInfo.X = x;
                        itemInfo.Y = y;

                        var gridData = new UIMergeGridData(itemInfo);

                        GridsData[i] = gridData;

                        if (ts > 0)//气泡
                        {
                            gridData.goodData.locked = true;
                            gridData.goodData.creatTamp = ts;
                        }

                        gridData.goodData.Immovable = id < 0;
                    }
                    else
                    {
                        GridsData[i] = new UIMergeGridData(x, y);
                    }
                }
            }
             
            CSPlayer.I.OnMergeAreaNtf(mergeNtf.areas); 
            CSPlayer.I.SpecialBoxs.Clear();
             
            for (int i = 0; i < mergeNtf.boxes.Count; i++)
            {
                var box = mergeNtf.boxes[i];
                var specialBox = new SpecialBoxData(i + 2);

                specialBox.UpdateData(box);

                CSPlayer.I.SpecialBoxs.Add(specialBox);
            }

            CSPlayer.I.mergeStoreData.UpdateData(mergeNtf);

            BoxidQueue.Clear();
            BoxidQueue.AddRange(mergeNtf.temps);

            //MStepClientDatas.GetDataByLocal();

            grid_dataNtf.y = 1;

            CheckNtfRecieve();
        }

        //public static int IDToIndex(int id)
        //{
        //    var t = id / 10000;
        //    var l = id % 100;
        //    var c = id / 100 % 100;
        //    var s = (c - 1) * 100 + l - 1;

        //    switch (t)
        //    {
        //        case 3:
        //            s += 10;
        //            break;
        //        case 4:
        //            s += 20;
        //            break;
        //        case 1:
        //            s += 30;
        //            break;
        //        case 2:
        //            s += 0;
        //            break;
        //        default:
        //            break;
        //    }

        //    return s;
        //}


        //public static int IDUnlockIndexPhoto(int id)
        //{
        //    return IDToIndex(id);
        //}


        //public static int IDUnlockIndex(int id)
        //{
        //    return IDToIndex(id) * 2;
        //}

        //public static int IDRewardIndex(int id)
        //{
        //    return IDUnlockIndex(id) + 1;
        //}

        public static byte[] Decompress(byte[] bytes)
        {
            using (var compressStream = new MemoryStream(bytes))
            {
                using (var zipStream = new GZipStream(compressStream, CompressionMode.Decompress))
                {
                    using (var resultStream = new MemoryStream())
                    {
                        zipStream.CopyTo(resultStream);
                        return resultStream.ToArray();
                    }
                }
            }
        }


        public Dictionary<int, List<K3ResText>> K3ResFlyDic;


        private Vector2 grid_dataNtf;

        public bool PlayerInfoReceived { private set; get; }

        NTimer.Timer nTimer;



        public bool CheckGoodFull(int count, bool showTip = true)
        {
            int num = 0;

            foreach (var item in GridsData)
            {
                if (item.CanInItem())
                {
                    num++;
                }
            }

            if (count > num)
            {
                if (showTip)
                    FloatTips.I.FloatMsg(TFW.Localization.LocalizationMgr.Format($"Ui_goodstips_BoardFull", count));
            }

            return count <= num;
        }

        public bool CheckGoodFull_Maze(int count, bool showTip = true)
        {
            int num = 0;

            foreach (var item in GridsData_Maze)
            {
                if (item.Value.CanInItem())
                {
                    num++;
                }
            }

            if (count > num)
            {
                if (showTip)
                    FloatTips.I.FloatMsg(TFW.Localization.LocalizationMgr.Format($"Ui_goodstips_BoardFull", count));
            }

            return count <= num;
        }

        private void InitPlayeData()
        {
            if (PlayerData == null)
            {
                PlayerData = new K3PlayerData();
                PlayerData.Init();
            }
        }

        //--用于外部获取
        public void AddMoney(MoneyType CostType, int count, string reson = "")
        {
            AddMoney((int)CostType, count, reson);
        }

        public bool CanAddEnergy(int addValue)//是否可以增加能量！
        {
            var maxOwned = Cfg.C.CRecover.I(11171012).MaxOwn;
            if (PlayerData.energy + addValue > maxOwned)
            {
                return false;
            }

            return true;
        }

        public void AddMoney(int CostType, int count, string reson = "")
        {
            if (CostType == ConfigID.VM_Gold)
            {
                CSPlayer.I.k3ToServerData.coinChange += count;
                ChangeMoney(count, 0, 0, 0, false);
            }
            else if (CostType == ConfigID.VM_Diamond)
            {
                CSPlayer.I.k3ToServerData.diamondChange += count;

                ChangeMoney(0, count, 0, 0, false);
            }
            else if (CostType == ConfigID.Recover_Energy)
            {
                CSPlayer.I.k3ToServerData.EnergyChange += count;
                ChangeMoney(0, 0, count, 0, false);
            }
            else if (CostType == ConfigID.VM_Star)
            {
                CSPlayer.I.k3ToServerData.starChange += count;
                ChangeMoney(0, 0, 0, count, false);
            }
        }
        public bool UseMoney(MoneyType CostType, int count, string reson = "")
        {
            return UseMoney((int)CostType, count, reson);
        }
        public bool UseMoney(int CostType, int count, string reson = "")
        {

            if (CostType == ConfigID.VM_Gold)
            {
                if (PlayerData.gold >= count)
                {
                    CSPlayer.I.k3ToServerData.coinChange -= count;
                    ChangeMoney(-count, 0, 0, 0, true);

                    return true;
                }
            }
            else if (CostType == ConfigID.VM_Diamond)
            {
                if (PlayerData.diamond >= count)
                {

                    CSPlayer.I.k3ToServerData.diamondChange -= count;
                    ChangeMoney(0, -count, 0, 0, true);



                    return true;
                }
            }
            else if (CostType == ConfigID.Recover_Energy)
            {
                if (PlayerData.energy >= count)
                {

                    CSPlayer.I.k3ToServerData.EnergyChange -= count;
                    ChangeMoney(0, 0, -count, 0, true);


                    return true;
                }
            }
            else if (CostType == ConfigID.VM_Star)
            {
                if (PlayerData.star >= count)
                {
                    CSPlayer.I.k3ToServerData.starChange -= count;
                    ChangeMoney(0, 0, 0, -count, true);
                    return true;
                }
            }
            return false;
        }

        public long GetOwnCountsByMoneyType(int id)
        {
            if (id == ConfigID.VM_Gold)
            {
                return PlayerData.gold;
            }
            else if (id == ConfigID.VM_Diamond)
            {
                return PlayerData.diamond;
            }
            else if (id == ConfigID.Recover_Energy)
            {
                return PlayerData.energy;
            }
            else if (id == ConfigID.VM_Star)
            {
                return PlayerData.star;
            }
            else
                BagMgr.I.GetBagItem(id);
            return 0;
        }
        
        
        public bool CheckFullMoney(MoneyType CostType, int count)
        {
            return CheckFullMoney((int)CostType, count);
        }

        public bool CheckFullMoney(int CostType, int count)
        {
            if (CostType == Config.ConfigID.VM_Gold)
            {
                if (PlayerData.gold >= count)
                {
                    return true;
                }
            }
            else if (CostType == Config.ConfigID.VM_Diamond)
            {
                if (PlayerData.diamond >= count)
                {
                    return true;
                }
            }
            else if (CostType == Config.ConfigID.Recover_Energy)
            {
                if (PlayerData.energy >= count)
                {
                    return true;
                }
            }
            else if (CostType == Config.ConfigID.VM_Star)
            {
                if (PlayerData.star >= count)
                {
                    return true;
                }
            }

            return false;
        }


        //public void SaveDataLocal()//同步数据到服务器 
        //{
        //    if (((GameTime.Time - PlayerData.SyncTime) > 1000 * 10) ||  Mathf.Abs(CSPlayer.I.k3ToServerData.EnergyChange) >=50 || PlayerData.mergeData.clickCount<10) //本地存储&同步 10SCD 
        //    {
        //        SaveDataToServer();
        //    }
        //}

        public void SavePlayerDataToServer()
        {
            if (PlayerData != null)
            {
                PlayerData.SyncTime = GameTime.Time;

                var dataStr = Newtonsoft.Json.JsonConvert.SerializeObject(PlayerData);

                D.Info?.Log("SavePlayerData");

                MessageMgr.Send(new SyncDataReq() { data = dataStr });
            }
        }

        /// <summary>
        /// 同步数据&棋盘
        /// </summary>
        //public void SaveDataToServer()
        //{
        //    PlayerData.SyncTime = GameTime.Time;

        //    var dataStr = Newtonsoft.Json.JsonConvert.SerializeObject(PlayerData);

        //    UnityEngine.Debug.Log("SaveDataToServer~~");

        //    MessageMgr.Send(new SyncDataReq() { data = dataStr });

        //    CSPlayer.I.SyncMerge();
        //}

        public TimeSpan GetDuration(long lasttime)
        {
            return TimeSpan.FromMilliseconds(Math.Max(0, GameTime.Time - lasttime));
        }


        private int lastEnery = 0;
        private long cNextTime;
        public void UpdateEnergy()
        {
            if (CheckAddEnergy())
            {
                var nextTime = LEnergyGauge.I.GetMergeRecoverNextTime(out int nextAddvalue);

                //if (cNextTime != nextTime)
                //{
                //    cNextTime = nextTime;

                    ChangeEnergyTimer(nextTime);

                //EventMgr.FireEvent(TEventType.ChangeEnergyTimer, nextTime);
                //}



                if (lastEnery != nextAddvalue)
                {
                    lastEnery = nextAddvalue;
                    ChangeMoney(0, 0, 0, 0, true);

                    CSPlayer.I.SyncMerge(false);//体力回复 就同步Step
                }
            }
        }

        public bool CheckAddEnergy()
        {
            if (PlayerData != null)
            {
                return PlayerData.energy < PlayerData.maxEnergy;
            }
            else
                return false;
        }

        public bool CheckUnlockedById(int id)
        {
            return true;//默认全解锁了 不走图签了
            
        }

        public bool CheckCollectedFirstById(int id)
        {
            return CSPlayer.I.FirstRewardItems.Contains(id);
        }

        //public bool CollectRewardById(int id)
        //{
        //    if (!CheckCollectedFirstById(id))
        //    {
        //        CSPlayer.I.FirstRewardItems.Add(id);

        //        MessageMgr.Send(new MergeReceiveUnlockRewardReq() { id = id });

        //        return true;
        //    }

        //    return false;
        //}

        public void UnlockedId(int id)
        {
            if ((id / 100000) < 5 && !CSPlayer.I.UnlockItems.Contains(id))
            {
                CSPlayer.I.UnlockItems.Add(id);

                GuidManage.TriggerGuid(GuidManage.GuidTriggerType.FirstGetMergeItem, id);

                int level = int.Parse(id.ToString().Substring(3, 2));

                CheckLevelBiLog(level);

                //CSPlayer.I.k3ToServerData.unlockItemsToServer.Add(id);

                //CheckUnlock(id);
                //int guidItem = (int)Game.Config.MetaConfig.GuideEnerge;
                //if (id == guidItem)
                //{
                //    var guidData = new UIGuidData();
                //    guidData.guidItems.Add(new UIGuidItem()
                //    {
                //        UIName = "UIMerge",
                //        UIItem = "",
                //        GetUIItemGa = () =>
                //        {
                //            var uimerge = DeepUI.PopupManager.I.FindPopup<K3.UIMerge>();
                //            if (uimerge != null)
                //            {
                //                foreach (var item in uimerge.mGoodDic)
                //                {
                //                    if (item.Value.Info.id == guidItem && item.Value.CurGrid.NoInArea() && !item.Value.mData.Immovable && !item.Value.mData.locked)
                //                    {
                //                        return item.Value.gameObject;
                //                    }
                //                }
                //            }

                //            return null;
                //        },
                //        doubleClick = true,
                //        d7Desc = TFW.Localization.LocalizationMgr.Get("dialogue3"),
                //        delayFinger = 1f,
                //        guidEventID = "12",
                //        forceGuid = true,
                //        figherOffset = new Vector3(62, -21, 0),
                //        d7ObjY = 0
                //    });

                //    guidData.guidItems.Add(new UIGuidItem()
                //    {
                //        UIName = "UIMerge",
                //        UIItem = "Root/mageSpace/clickRoot/toplayer/CreatGoodButton",
                //        slide = true,
                //        forceGuid = true,
                //    });

                //    guidData.guidItems.Add(new UIGuidItem()
                //    {
                //        UIName = "UIMerge",
                //        UIItem = "Root/mageSpace/clickRoot/toplayer/CreatGoodButton",
                //        forceGuid = true,
                //    });

                //    UIGuid.StartGuid(guidData, true); //体力引导 暂时关闭
                //}


                EventMgr.FireEvent(TEventType.K3MergeUnlockIdRefresh);
            }
        }

        private void CheckLevelBiLog(int level)
        {

            if (level >= 6 && PlayerPrefs.GetInt($"{LPlayer.I.PlayerID}_firstunlockitem_{6}", 0) == 0)
            {
                PlayerPrefs.SetInt($"{LPlayer.I.PlayerID}_firstunlockitem_{6}", 1);

                var gameEvent = new K3.GameEvent();
                gameEvent.EventKey = $"unlock_1";
                K3.K3GameEvent.I.BiLog(gameEvent);
            }

            if (level >= 7 && PlayerPrefs.GetInt($"{LPlayer.I.PlayerID}_firstunlockitem_{7}", 0) == 0)
            {
                PlayerPrefs.SetInt($"{LPlayer.I.PlayerID}_firstunlockitem_{7}", 1);

                var gameEvent = new K3.GameEvent();
                gameEvent.EventKey = $"unlock_2";
                K3.K3GameEvent.I.BiLog(gameEvent);
            }

            if (level >= 9 && PlayerPrefs.GetInt($"{LPlayer.I.PlayerID}_firstunlockitem_{9}", 0) == 0)
            {
                PlayerPrefs.SetInt($"{LPlayer.I.PlayerID}_firstunlockitem_{9}", 1);

                var gameEvent = new K3.GameEvent();
                gameEvent.EventKey = $"unlock_3";
                K3.K3GameEvent.I.BiLog(gameEvent);
            }

            if (level >= 11 && PlayerPrefs.GetInt($"{LPlayer.I.PlayerID}_firstunlockitem_{11}", 0) == 0)
            {
                PlayerPrefs.SetInt($"{LPlayer.I.PlayerID}_firstunlockitem_{11}", 1);

                var gameEvent = new K3.GameEvent();
                gameEvent.EventKey = $"unlock_4";
                K3.K3GameEvent.I.BiLog(gameEvent);
            }

            if (level >= 13 && PlayerPrefs.GetInt($"{LPlayer.I.PlayerID}_firstunlockitem_{13}", 0) == 0)
            {
                PlayerPrefs.SetInt($"{LPlayer.I.PlayerID}_firstunlockitem_{13}", 1);

                var gameEvent = new K3.GameEvent();
                gameEvent.EventKey = $"unlock_5";
                K3.K3GameEvent.I.BiLog(gameEvent);
            }

            if (level >= 15 && PlayerPrefs.GetInt($"{LPlayer.I.PlayerID}_firstunlockitem_{15}", 0) == 0)
            {
                PlayerPrefs.SetInt($"{LPlayer.I.PlayerID}_firstunlockitem_{15}", 1);

                var gameEvent = new K3.GameEvent();
                gameEvent.EventKey = $"unlock_6";
                K3.K3GameEvent.I.BiLog(gameEvent);
            }

        }

       
        //public void ShowFlyEffect(MoneyType type, Vector3 target, int count = 10, float delay = 0f)
        //{
        //    CoroutineMgr.StartCoroutine(DelayShowFlyEffect(type, target, count, delay));
        //}

        //IEnumerator DelayShowFlyEffect(MoneyType type, Vector3 targetPos, int count = 10, float delay = 0f)
        //{
        //    yield return new WaitForSeconds(delay);
        //    ShowFlyEffect((int)type, targetPos, count);
        //}

        public void ShowFlyEffect(int CostType, Vector3 targetPos, int count = 10)
        {
            if (K3ResFlyDic != null)
            {
                if (CostType == ConfigID.VM_Gold)
                {
                    if (K3ResFlyDic.TryGetValue((int)K3ResText.ResEnum.Coin, out var resList))
                    {
                        if (resList.Count > 0)
                        {
                            for (int i = resList.Count - 1; i >= 0; i--)
                            {
                                var resText = resList[i];
                                if (resText != null)
                                {
                                    resText.ResFlyAni(targetPos, count);
                                    return;
                                }
                            }
                        }
                    }
                }
                else if (CostType == ConfigID.VM_Diamond)
                {
                    if (K3ResFlyDic.TryGetValue((int)K3ResText.ResEnum.Diamond, out var resList))
                    {
                        if (resList.Count > 0)
                        {
                            for (int i = resList.Count - 1; i >= 0; i--)
                            {
                                var resText = resList[i];
                                if (resText != null)
                                {
                                    resText.ResFlyAni(targetPos, count);
                                    return;
                                }
                            }
                        }
                    }
                }
                else if (CostType == ConfigID.VM_Star)
                {

                    if (K3ResFlyDic.TryGetValue((int)K3ResText.ResEnum.Star, out var resList))
                    {
                        if (resList.Count > 0)
                        {
                            for (int i = resList.Count - 1; i >= 0; i--)
                            {
                                var resText = resList[i];
                                if (resText != null)
                                {
                                    resText.ResFlyAni(targetPos, count);
                                    return;
                                }
                            }
                        }
                    }
                }
                else if (CostType == ConfigID.Recover_Energy)
                {
                    if (K3ResFlyDic.TryGetValue((int)K3ResText.ResEnum.Energy, out var resList))
                    {
                        if (resList.Count > 0)
                        {
                            for (int i = resList.Count - 1; i >= 0; i--)
                            {
                                var resText = resList[i];
                                if (resText != null)
                                {
                                    resText.ResFlyAni(targetPos, count);
                                    return;
                                }
                            }
                        }
                    }
                }
            }
        }

        public void ShowFlyHeroStarEffect(Transform target, int count = 10)
        {
            if (K3ResFlyDic != null)
            {
                if (K3ResFlyDic.TryGetValue((int)K3ResText.ResEnum.HeroStar, out var resList))
                {
                    if (resList.Count > 0)
                    {
                        for (int i = resList.Count - 1; i >= 0; i--)
                        {
                            var resText = resList[i];
                            if (resText != null)
                            {
                                resText.ResFlyAni(target.position, count);
                                return;
                            }
                        }
                    }
                }
            }
        }

        
         
        /// <summary>
        /// 获取当前道具在任务、相册、主城升级消耗的总和
        /// </summary>
        /// <param name="itemId"></param>
        /// <returns></returns>
        public int needItemCount(int itemId)
        {
            long needCount = 0;
             

            if (UI.Utils.MainFunctionOpenUtils.CityOpenState)
            {
                Game.Data.GameData.I.SkillData.MTechs.TryGetValue(1, out var mTechData);

                if (mTechData.MConfig != null)
                {
                    foreach (var item in mTechData.UpgradeCost)
                    {
                        if (item.Id == itemId)
                        {
                            needCount += item.Val;
                        }
                    }
                }
            }

            return (int)needCount;

        }

        /// <summary>
        /// 当前数据是否足够，返回不够的第一个数据 ID及所需值
        /// </summary>
        /// <param name="cost"></param>
        /// <returns></returns>
        public bool CheckReward(List<Reward> cost, out Reward reward)
        {
            reward = new Reward();

            foreach (var item in cost)
            {
                if (item.IsK3VM)
                {
                    switch (item.RewardId)
                    {
                        case Config.ConfigID.VM_Gold:
                            if (item.RewardVal > PlayerData.gold)
                            {
                                reward = new Reward() { RewardId = item.RewardId, RewardType = item.RewardType, RewardVal = item.RewardVal - (int)PlayerData.gold };
                                return false;
                            }
                            break;
                        case Config.ConfigID.Recover_Energy:
                            if (item.RewardVal > PlayerData.energy)
                            {
                                reward = new Reward() { RewardId = item.RewardId, RewardType = item.RewardType, RewardVal = item.RewardVal - PlayerData.energy };
                                return false;
                            }
                            break;
                        case Config.ConfigID.VM_Diamond:
                            if (item.RewardVal > PlayerData.diamond)
                            {
                                reward = new Reward() { RewardId = item.RewardId, RewardType = item.RewardType, RewardVal = item.RewardVal - (int)PlayerData.diamond };
                                return false;
                            }
                            break;
                        default:
                            break;
                    }
                }
                else if (item.IsK3Item)
                {
                    var curCount = GridsData.Count(a => a.isFull && AreaData.NoPoint(a.point) && !a.goodData.locked && !a.goodData.Immovable && a.goodData.id == item.RewardId);

                    if (curCount < item.RewardVal)
                    {
                        reward = new Reward() { RewardId = item.RewardId, RewardType = item.RewardType, RewardVal = item.RewardVal - curCount };
                        return false;
                    }
                }
            }

            return true;
        }


        public bool CheckReward(Reward item, out Reward reward)
        {
            reward = new Reward();
            if (item.IsK3VM)
            {
                switch (item.RewardId)
                {
                    case Config.ConfigID.VM_Gold:
                        if (item.RewardVal > PlayerData.gold)
                        {
                            reward = new Reward() { RewardId = item.RewardId, RewardType = item.RewardType, RewardVal = item.RewardVal - (int)PlayerData.gold };
                            return false;
                        }
                        break;
                    case Config.ConfigID.Recover_Energy:
                        if (item.RewardVal > PlayerData.energy)
                        {
                            reward = new Reward() { RewardId = item.RewardId, RewardType = item.RewardType, RewardVal = item.RewardVal - PlayerData.energy };
                            return false;
                        }
                        break;
                    case Config.ConfigID.VM_Diamond:
                        if (item.RewardVal > PlayerData.diamond)
                        {
                            reward = new Reward() { RewardId = item.RewardId, RewardType = item.RewardType, RewardVal = item.RewardVal - (int)PlayerData.diamond };
                            return false;
                        }
                        break;
                    default:
                        break;
                }
            }
            else if (item.IsK3Item)
            {

                var curCount = GetItemCount(item.RewardId);
                if (curCount < item.RewardVal)
                {
                    reward = new Reward() { RewardId = item.RewardId, RewardType = item.RewardType, RewardVal = item.RewardVal - curCount };
                    return false;
                }
            }

            return true;
        }

        public int GetItemCount(int itemID)
        {
            var curCount = 0;
            if (GridsData.Length > 0)
            {
                curCount = GridsData.Count(a => a != null && a.goodData != null && a.NoInArea() && !a.goodData.locked && !a.goodData.Immovable && a.goodData.id == itemID);
            }
            return curCount;
        }

        //public int CheckNextUnlockPhoto(int number)
        //{
        //    int id = 0;
        //    List<PhotoInfo> photolist = CSVPhoto.GetCK3PhotoAlbumByAlbum(Logic.CSPlayer.I.CurPhotoAlbum);

        //    PhotoInfo photoInfo = photolist.Where(t => t.UnlockOrder == 0).FirstOrDefault();
        //    List<int> list = new List<int>();
        //    list.Add(photoInfo.Id);
        //    PhotoInfo next_photoInfo= photolist.Where(t => t.UnlockOrder == photoInfo.Id).FirstOrDefault();
        //    list.Add(next_photoInfo.Id);
        //    while (next_photoInfo != null)
        //    {
        //        next_photoInfo = photolist.Where(t => t.UnlockOrder == next_photoInfo.Id).FirstOrDefault();
        //        if (next_photoInfo != null)
        //        {
        //            list.Add(next_photoInfo.Id);
        //        }
        //    }
        //    if (number <= list.Count)
        //    {
        //        id = list[number - 1];

        //    }
        //    return id;
        //}


        public bool GetAtlasRedPoint()
        {
            List<int> unlockIdWhithOutCollectFirstReward = new List<int>();
            for (int i = 0; i < CSPlayer.I.UnlockItems.Count; i++)
            {
                if (!CSPlayer.I.FirstRewardItems.Contains(CSPlayer.I.UnlockItems[i]))
                {
                    unlockIdWhithOutCollectFirstReward.Add(CSPlayer.I.UnlockItems[i]);
                }
            }

            for (int i = 0; i < unlockIdWhithOutCollectFirstReward.Count; i++)
            {
                ItemInfo info = CSVItem.GetItemInfoById(unlockIdWhithOutCollectFirstReward[i]);
                if (info?.FstReward != 0)
                {
                    return true;
                }
            }
            return false;
        }
        public int GetAtlasRedPointNum()
        {
            List<int> unlockIdWhithOutCollectFirstReward = new List<int>();
            int count = 0;
            for (int i = 0; i < CSPlayer.I.UnlockItems.Count; i++)
            {
                if (!CSPlayer.I.FirstRewardItems.Contains(CSPlayer.I.UnlockItems[i]))
                {
                    unlockIdWhithOutCollectFirstReward.Add(CSPlayer.I.UnlockItems[i]);
                }
            }

            for (int i = 0; i < unlockIdWhithOutCollectFirstReward.Count; i++)
            {
                ItemInfo info = CSVItem.GetItemInfoById(unlockIdWhithOutCollectFirstReward[i]);
                if (info?.FstReward != 0)
                {
                    count++;
                }
            }
            return count;
        }

     
         
       
        
        public enum orderState
        {
            Default,
            Normal,
            Senior,
        }
        orderState OrderState = orderState.Default;

        public void SetOrderState(orderState order = orderState.Default)
        {
            OrderState = order;
        }

        public orderState GetOrderState()
        {
            return OrderState;
        }

        public UniTask OnLogin()
        {
            //SubShopMgr.I.GetShopInfoReq(cspb.ShopType.SHOP_MERGE, false);

            EventMgr.RegisterEvent(Common.TEventType.ApplicationPause, (parms) =>
            {
                SavePlayerDataToServer();
            }, this);


            loginAckTime = GameTime.Time + 20 * 1000;

            NTimer.TickNoPool(ref nTimer, NTimer.INF, 1f, () =>
            {
                if ((GameTime.Time - PlayerData?.SyncTime) > 1000 * 30)
                {
                    SavePlayerDataToServer();//大于一定时间数值就会自动同步PlayerData
                }

                //if (!PlayerInfoReceived && loginAckTime > GameTime.Time)
                //{
                //    //20S 内未收到 棋盘数据 重新登录处理
                //    Main.ReStart();
                //}

            });


            //LogoLoading.UpdateState(LogoLoadingState.CheckInfoRecieve);

            return UniTask.CompletedTask;
        }
        private float loginAckTime;

        public UniTask OnLogout()
        {
            nTimer?.Stop();
            PlayerInfoReceived = false;

            PlayerData=null;

            grid_dataNtf = Vector2.zero;

            EventMgr.UnregisterEvent(this);

            return UniTask.CompletedTask;
        }

        public UniTask OnBeforeLogin()
        {
            grid_dataNtf = Vector2.zero;
            PlayerInfoReceived = false;

            PlayerData=null;

            K3ResFlyDic = new Dictionary<int, List<K3ResText>>();

            MessageMgr.RegisterMsg<DataNtf>(this, OnDataNtf);

            MessageMgr.RegisterMsg<MergeNtf>(this, OnMergeNtf);

            return UniTask.CompletedTask;
        }
    }


     

    public abstract class BoxData
    {
        public Cfg.G.CCsBox BoxCfg
        {
            get
            {
                return Cfg.C.CCsBox.I(this.CfgID);
            }
        }

        protected BoxData()
        {

        }
        public BoxData(int cfgID)
        {
            this.CfgID = cfgID;
        }

        public int CfgID;


        public int BoxCost;


        public bool Unlock
        {
            get
            {
                if (BoxCfg.ChapterUnlock >= 0)
                {
                    return BoxCfg.ChapterUnlock < ChapterTaskGameData.I.mChapterQuestInfo.curChapterID;
                }
                else
                {
                    if (BoxCfg.Unlock == 0)
                        return true;
                    else
                    {
                        if (LPlayer.I.DicCityBuilding.TryGetValue(BoxCfg.Unlock, out var CityBuilding))
                        {
                            return CityBuilding.completed;
                        }

                        return false;
                    }
                }
            }
        }

        protected List<ItemInfo> CreateItems = new List<ItemInfo>();
        public virtual List<ItemInfo> GetItems()
        {
            CreateItems.Clear();
            return CreateItems;//产出道具列表
        }

        public abstract int GetHeroStar();
    }

    public class BaseBoxData : BoxData
    {
        public BaseBoxData()
        {
            this.CfgID = 1;
            this.BoxCost = BoxCfg.EnergyCost;
        }

        public override List<ItemInfo> GetItems()
        {
            CreateItems.Clear();

            var baseBoxFree = Cfg.C.CBaseBoxForNew.RawList();

            Cfg.G.CBaseBoxForNew curBox = null;

            foreach (var item in baseBoxFree)
            {
                curBox = item;
                if (LSkill.I.GetMainCityLevel() < item.FailConditions)// !CSPlayer.I.AllPhotoIdList.Contains(item.FailConditions)
                {
                    Debug.Log($"基础箱子产出：CBaseBoxForNew ：{curBox.Id}");
                    break;
                }
            }

            int code = 0, level = 0;
            if (curBox != null)
            {
                var intWights = CSVConfig.StringListToIntArray(curBox.CreatePower);

                int typeIndex = UITools.RandomByWeight(intWights);

                if (curBox.CreateType.Count > typeIndex && typeIndex >= 0)
                {
                    int.TryParse(curBox.CreateType[typeIndex], out code);
                }
            }

            int totalStar = GetHeroStar();

            //var baseLevelCfgs= Cfg.C.CBaseBoxLevel.RawList();

            //foreach (var item in baseLevelCfgs)
            //{
            //    if (item.StarMin <= totalStar && item.StarMax >= totalStar)
            //    {
            //        level = UnityEngine.Random.Range(item.CreateMin, item.CreateMax+1);
            //        break;
            //    }
            //}


            level = 1;
            if (GameData.I.SkillData.MTechs.TryGetValue(1, out var data))
            {
                level = data.MConfig.BoxLv;
            }

            int curLevel = Mathf.Min(level, CSVItem.GetMaxLevelByTypeAndCode(1, code));

            var itemNum = BoxCfg.Num;

            for (int i = 0; i < itemNum; i++)
            {
                ItemInfo itemInfo = CSVItem.GetItemInfoByTypeAndCodeAndLevel(1, code, curLevel);
                if (itemInfo != null)
                {
                    CreateItems.Add(itemInfo);
                }
            }

            return CreateItems;
        }

        public override int GetHeroStar()
        {
            int totalStar = 0;
            foreach (var item in CSPlayer.I.SpecialBoxs)
            {
                foreach (var ii in item.heroCfgIDs)
                {
                    if (ii > 0)
                    {
                        var curStar = HeroGameData.I.GetHeroByCfgId(ii)?.Star ?? 0;
                        totalStar += curStar;
                    }
                }
            }

            return totalStar;
        }
    }

    public class SpecialBoxData : BoxData
    {
        /// <summary>
        /// 当前剩余次数
        /// </summary>
        public int curCapacity
        {
            set
            {
                if (HaveMainHero())
                {
                    if (MHeroMap.TryGetValue(MainHeroID, out var mhero))
                    {
                        mhero.left = value;
                    }
                }
            }
            get
            {
                if (HaveMainHero() && MHeroMap.TryGetValue(MainHeroID, out var mhero))
                {
                    return mhero?.left ?? 1;
                }
                else
                {
                    return 1;
                }
            }
        }

        /// <summary>
        /// 当前重置CD时间终点 （ms)
        /// </summary>
        public long curCDTime
        {
            set
            {
                if (HaveMainHero())
                {
                    if (MHeroMap.TryGetValue(MainHeroID, out var mhero))
                    {
                        mhero.ts = value;
                    }
                }
            }
            get
            {
                if (HaveMainHero())
                {
                    MHeroMap.TryGetValue(MainHeroID, out var mhero);
                    return mhero?.ts ?? 0;
                }
                else
                {
                    return 0;
                }
            }
        }

        ///// <summary>
        /////技能释放所需能量即最大能量  被动进行修改
        ///// </summary>
        public int MaxPower
        {
            get
            {
                int maxPower = BoxCfg.Cost;

                var mainHero = GetHeroData();

                if (mainHero != null)
                {
                    if (mainHero?.HeroMergeSkillDatas?.Count > 0)
                    {
                        foreach (var item in mainHero?.HeroMergeSkillDatas)
                        {
                            if (item.mType == Game.Data.HeroData.HeroSkillData.SkillType.MergePassive)
                            {
                                var skillCfg = Cfg.C.CCSSkillForPassive.I(item.id);
                                if (skillCfg?.Type == 6)//减少释放技能能量
                                {
                                    maxPower -= skillCfg.Param;
                                }
                            }
                        }
                    }
                }


                return maxPower;
            }
        }

        public CityType CityType
        {
            get
            {
                var type = CityType.Meeting;
                if (BoxCfg.Id == 2)
                {
                    type = CityType.Trial;
                }
                else if (BoxCfg.Id == 3)
                {
                    type = CityType.Police;
                }
                else if (BoxCfg.Id == 4)
                {
                    type = CityType.Meeting;
                }
                else if (BoxCfg.Id == 5)
                {
                    type = CityType.Machine;
                }
                else if (BoxCfg.Id == 6)
                {
                    type = CityType.Gene;
                }
                return type;
            }
        }
        /// <summary>
        ///CD总值  
        /// </summary>
        public int Cooldown
        {
            get
            {
                var mainHero = GetHeroData();

                if (mainHero != null)
                {
                    int cooldown = mainHero.HeroCfg.Cooldown;

                    if (mainHero != null)
                    {
                        if (mainHero?.HeroMergeSkillDatas?.Count > 0)
                        {
                            foreach (var item in mainHero?.HeroMergeSkillDatas)
                            {
                                if (item.mType == Game.Data.HeroData.HeroSkillData.SkillType.MergePassive)
                                {
                                    var skillCfg = Cfg.C.CCSSkillForPassive.I(item.id);
                                    if (skillCfg?.Type == 4) //快速冷却
                                    {
                                        cooldown -= skillCfg.Param;
                                    }
                                }
                            }
                        }
                    }
                    return cooldown;
                }

                return 1;
            }
        }

        /// <summary>
        ///最大容量   
        /// </summary>
        public int Capacity
        {
            get
            {
                var mainHero = GetHeroData();

                if (mainHero != null)
                {
                    int capacity = mainHero.HeroCfg.Capacity;

                    if (mainHero?.HeroMergeSkillDatas?.Count > 0)
                    {
                        foreach (var item in mainHero?.HeroMergeSkillDatas)
                        {
                            if (item.mType == Game.Data.HeroData.HeroSkillData.SkillType.MergePassive)
                            {
                                var skillCfg = Cfg.C.CCSSkillForPassive.I(item.id);
                                if (skillCfg?.Type == 5)//增加容量
                                {
                                    capacity += skillCfg.Param;
                                }
                            }
                        }
                    }

                    return capacity;
                }

                return 1;
            }
        }

        public Dictionary<int, MHero> MHeroMap = new Dictionary<int, MHero>();

        public List<int> heroCfgIDs = new List<int>();

        /// <summary>
        /// 是否在试用英雄ID
        /// </summary>
        public bool TestHeroCfgID { get; private set; }
       
        public void TestHero(bool startTest)
        {
            bool CanTestHero = MergeTaskMgr.I.CanTestHero();
          

            if (CanTestHero)
            {
                TestHeroCfgID = startTest;

                if (startTest)
                {
                    if (K3PlayerMgr.I.PlayerData.TestHeroTimes == 0)
                    {
                        K3PlayerMgr.I.PlayerData.TestHeroTimes = (int)MetaConfig.First_recharge_trial_hero_end_count;
                    }

                    EventMgr.FireEvent(TEventType.TestUseHero);
                }
                else
                {
                    K3PlayerMgr.I.PlayerData.TestHeroTimes = -1;
                    K3PlayerMgr.I.PlayerData.TestHeroEndTime = GameTime.Time + 10 * 1000;
                    K3PlayerMgr.I.SavePlayerDataToServer();

                    FloatTips.I.FloatMsg("Trial_use_end_01".ToLocal());

                    GuidManage.TriggerGuid(GuidManage.GuidTriggerType.TestFirstHeroEnd, 0);

                    EventMgr.FireEvent(TEventType.TestUseHeroEnd);
                }
            }
        }

        public SpecialBoxData(int cfgID)
        {
            this.CfgID = cfgID;

            this.BoxCost = BoxCfg.EnergyCost;
        }

        public bool HaveMainHero()
        {
            if (heroCfgIDs.Count > 0 && heroCfgIDs[0] > 0)
            {
                return true;
            }

            return false;
        }

        public Game.Data.HeroData GetHeroData()
        {
            if (HaveMainHero())
            {
                return HeroGameData.I.GetMyHeroByCfgId(heroCfgIDs[0]);
            }
            else
            {
                return null;
            }
        }

        public int MainHeroID
        {
            get
            {
                if (heroCfgIDs.Count > 0)
                    return heroCfgIDs[0];
                else
                    return 0;
            }
        }

        public int GetHeroMerge()
        {
            int totalMerge = 0;
            foreach (var item in heroCfgIDs)
            {
                if (item > 0)
                {
                    var curMerge = HeroGameData.I.GetHeroByCfgId(item)?.GetHeroAttribute().MergeValue ?? 0;
                    totalMerge += curMerge;
                }
            }
            return totalMerge;
        }

        private readonly int mMainHeroCount = 1; // 主将数量
        private readonly int mSubHero1Count = 2; // 副将数量
        private readonly int mSubHero2Count = 3; // 辅助将数量

        public int GetMainHeroCount()
        {
            return mMainHeroCount;
        }

        public int GetSubHero1Count()
        {
            return mSubHero1Count;
        }

        public int GetSubHero2Count()
        {
            return mSubHero2Count;
        }

        // 主将列表
        public List<int> GetMainHeros()
        {
            var mainHeros = new List<int>();

            for (int i = 0; i < mMainHeroCount; i++)
            {
                if (i >= heroCfgIDs.Count)
                {
                    break;
                }

                mainHeros.Add(heroCfgIDs[i]);
            }

            return mainHeros;
        }

        // 副将列表
        public List<int> GetSubHeros1()
        {
            var subHeros = new List<int>();

            for (int i = mMainHeroCount; i < mMainHeroCount + mSubHero1Count; i++)
            {
                if (i >= heroCfgIDs.Count)
                {
                    break;
                }

                subHeros.Add(heroCfgIDs[i]);
            }

            return subHeros;
        }

        // 辅助将列表
        public List<int> GetSubHeros2()
        {
            var subHeros = new List<int>();

            for (int i = mMainHeroCount + mSubHero1Count; i < mMainHeroCount + mSubHero1Count + mSubHero2Count; i++)
            {
                if (i >= heroCfgIDs.Count)
                {
                    break;
                }

                subHeros.Add(heroCfgIDs[i]);
            }

            return subHeros;
        }

        public int GetBuildingBattlePower()
        {
            return GetBoxTotalHeroBattlePower();
        }

        public int GetBoxTotalHeroBattlePower()
        {
            var mainHeroBattlePower = GetBoxMainHeroBattlePower();
            var subHero2BattlePower = GetBoxSubHero2BattlePower();
            var subHero1BattlePower = GetBoxSubHero1BattlePower();

            return mainHeroBattlePower + subHero1BattlePower + subHero2BattlePower;
        }

        public int GetBoxMainHeroBattlePower()
        {
            var mainHeros = GetMainHeros();
            var mainHeroBattlePower = 0;

            foreach (var heroId in mainHeros)
            {
                if (heroId <= 0)
                {
                    continue;
                }

                var heroData = HeroGameData.I.GetHeroByCfgId(heroId);

                if (null == heroData)
                {
                    continue;
                }

                var curMerge = heroData.GetHeroAttribute().LevelUpIntoMerge;

                if (!heroData.HeroCfg.HeroBattlePowerEnale(CityType))
                {
                    curMerge = 0;
                }

                var battlePower = heroData.Power() * (1 + curMerge);

                mainHeroBattlePower += (int)battlePower;
            }

            return mainHeroBattlePower;
        }

        public int GetBoxSubHero1BattlePower()
        {
            var subHeros1 = GetSubHeros1();
            var subHero1BattlePower = 0;

            foreach (var heroId in subHeros1)
            {
                if (heroId <= 0)
                {
                    continue;
                }

                var heroData = HeroGameData.I.GetHeroByCfgId(heroId);

                if (null == heroData)
                {
                    continue;
                }

                var curMerge = heroData.GetHeroAttribute().LevelUpIntoMerge;

                if (!heroData.HeroCfg.HeroBattlePowerEnale(CityType))
                {
                    curMerge = 0;
                }

                var battlePower = heroData.Power() * (1 + curMerge);

                subHero1BattlePower += (int)battlePower;
            }

            return subHero1BattlePower;
        }

        public int GetBoxSubHero2BattlePower()
        {
            var subHeros2 = GetSubHeros2();
            var subHero2BattlePower = 0;

            foreach (var heroId in subHeros2)
            {
                if (heroId <= 0)
                {
                    continue;
                }

                var heroData = HeroGameData.I.GetHeroByCfgId(heroId);

                if (null == heroData)
                {
                    continue;
                }

                var curMerge = heroData.GetHeroAttribute().LevelUpIntoMerge;

                if (!heroData.HeroCfg.HeroBattlePowerEnale(CityType))
                {
                    curMerge = 0;
                }

                var battlePower = heroData.Power() * (1 + curMerge);

                subHero2BattlePower += (int)battlePower;
            }

            return subHero2BattlePower;
        }

        // 暴击参数1
        public float GetHeroCritParam1()
        {
            GameData.I.SkillData.MTechs.TryGetValue((int)CityType, out var techData);
            return int.Parse(techData.MConfig.LuckyCrt[0]) / 1000f;

            // var config = Cfg.C.CD2Config.I(10788);
            // var buildingPower = GetBuildingBattlePower();
            //
            // var cfgValueList = new List<int>();
            // config.Array.ForEach(s => cfgValueList.Add(int.Parse(s)));
            //
            // var heroCritParam1 = 0f;
            // var mainHeroCritParam = GetMainHeroCritParam(0);
            //
            // heroCritParam1 = buildingPower * 1f / (buildingPower + LPlayer.I.GetMainCityLevel() * cfgValueList[0] + cfgValueList[1]) * cfgValueList[2] / 100f + mainHeroCritParam / 100f;
            //
            //
            // return heroCritParam1;
        }

        public float GetHeroCritParam2()
        {
            GameData.I.SkillData.MTechs.TryGetValue((int)CityType, out var techData);
            return int.Parse(techData.MConfig.LuckyCrt[1]) / 1000f;

            // var config = Cfg.C.CD2Config.I(10789);
            // var buildingPower = GetBuildingBattlePower();
            //
            // var cfgValueList = new List<int>();
            // config.Array.ForEach(s => cfgValueList.Add(int.Parse(s)));
            //
            // var heroCritParam2 = 0f;
            // var mainHeroCritParam = GetMainHeroCritParam(1);
            //
            // heroCritParam2 = buildingPower * 1f / (buildingPower + LPlayer.I.GetMainCityLevel() * cfgValueList[0] + cfgValueList[1]) * cfgValueList[2] / 100 + mainHeroCritParam / 100f;
            //
            //
            // return heroCritParam2;
        }

        public float GetHeroCritParam3()
        {
            GameData.I.SkillData.MTechs.TryGetValue((int)CityType, out var techData);
            return int.Parse(techData.MConfig.LuckyCrt[2]) / 1000f;

            // var config = Cfg.C.CD2Config.I(10790);
            // var buildingPower = GetBuildingBattlePower();
            //
            // var cfgValueList = new List<int>();
            // config.Array.ForEach(s => cfgValueList.Add(int.Parse(s)));
            //
            // var heroCritParam3 = 0f;
            // var mainHeroCritParam = GetMainHeroCritParam(2);
            //
            // heroCritParam3 = buildingPower * 1f / (buildingPower + LPlayer.I.GetMainCityLevel() * cfgValueList[0] + cfgValueList[1]) * cfgValueList[2] / 100 + mainHeroCritParam / 100f;
            //
            //
            // return heroCritParam3;
        }

        // private int GetMainHeroCritParam(int idx)
        // {
        //     var critParam = 0;
        //     var mainHeros = GetMainHeros();
        //     if (mainHeros.Count > 0)
        //     {
        //         var heroId = mainHeros[0]; // 目前策划给出的公式只有一个主将
        //         var heroData = HeroGameData.I.GetHeroByCfgId(heroId);
        //
        //         if (null != heroData && idx < heroData.HeroAttribute.LevelUpCtr.Count)
        //         {
        //             critParam = int.Parse(heroData.HeroAttribute.LevelUpCtr[idx]);
        //         }
        //     }
        //
        //     return critParam;
        // }

        public Enum_LuckyType GetLuckyType(float randomNum)
        {
            var param1 = GetHeroCritParam1();
            var param2 = GetHeroCritParam2();
            var param3 = GetHeroCritParam3();

            if (randomNum < param3)
            {
                return Enum_LuckyType.Lucky3;
            }
            else if (randomNum < param2)
            {
                return Enum_LuckyType.Lucky2;
            }
            else if (randomNum < param1)
            {
                return Enum_LuckyType.Lucky1;
            }

            return Enum_LuckyType.None;
        }

        public int GetLuckAddLv(Enum_LuckyType luckyType)
        {
            var addValues = Cfg.C.CD2Config.I(10755).Array;

            switch (luckyType)
            {
                case Enum_LuckyType.Lucky3:
                    {
                        return int.Parse(addValues[2]);
                    }
                case Enum_LuckyType.Lucky2:
                    {
                        return int.Parse(addValues[1]);
                    }
                case Enum_LuckyType.Lucky1:
                    {
                        return int.Parse(addValues[0]);
                    }
            }

            return 0;
        }

        public float GetBuildingLuckyValue()
        {
            return GetHeroLuckValue1() + GetHeroLuckValue2() + GetHeroLuckValue3();
        }

        public float GetHeroLuckValue1()
        {
            return GetHeroCritParam1();
        }

        public float GetHeroLuckValue2()
        {
            return GetHeroCritParam2() * 2;
        }

        public float GetHeroLuckValue3()
        {
            return GetHeroCritParam3() * 3;
        }

        public enum Enum_LuckyType
        {
            None = 0,
            Lucky1,
            Lucky2,
            Lucky3,
        }

        public List<ItemInfo> GetItems(out Enum_LuckyType luckyType)
        {
            CreateItems.Clear();

            int code = 0, level = 0;

            int boxConstIndex = 0;

            int boxTechLv = 1;


            luckyType = Enum_LuckyType.None;

            var curHero = GetHeroData();

            if (curHero == null)
            {
                D.Error?.Log($"未上阵英雄");

            }
            else
            {
                K3PlayerMgr.I.PlayerData?.mergeData?.BoxClickCount.TryGetValue(curHero.HeroCfg.Id, out boxConstIndex);

                boxTechLv = curHero.MergeItemLevel;

                if (curHero.HeroCfg.OpenItem.Count > boxConstIndex)
                {
                    CreateItems.Add(new ItemInfo(Cfg.C.CK3Item.I(int.Parse(curHero.HeroCfg.OpenItem[boxConstIndex]))));

                    K3PlayerMgr.I.PlayerData?.mergeData?.AddClickCount(curHero.HeroCfg.Id);
                }
                else
                {
                    K3PlayerMgr.I.PlayerData?.mergeData?.AddClickCount(curHero.HeroCfg.Id);

                    int index = boxConstIndex % curHero.HeroCfg.CreateWeight.Count;

                    if (!UnlockMgr.I.CheckUnlock(UnlockFuncEnum.MergeBoxHighItem))
                    {
                        index = 0;
                    }

                    int.TryParse(BoxCfg.CreateType[int.Parse(curHero.HeroCfg.CreateWeight[index])], out code);

                    // var ramdomNum = UnityEngine.Random.Range(0, 1f);
                    //
                    // luckyType = GetLuckyType(ramdomNum);

                    int baseLevel = boxTechLv;

                    int minLv = MergeTaskMgr.I.GetMinLv(code);//订单需求 修改


                    foreach (var item in curHero.HeroMergeSkillDatas)
                    {
                        //
                        if (item.mType == Game.Data.HeroData.HeroSkillData.SkillType.MergePassive && item.unlockStar <= curHero.Star)
                        {
                            var cfg = Cfg.C.CCSSkillForPassive.I(item.id);
                            if (cfg != null)
                            {
                                if (cfg.Type == 3) //3:生产时有概率将道具等级提升X级
                                {
                                    int randomValue = UnityEngine.Random.Range(0, 100);
                                    if (randomValue <= cfg.Probability)
                                    {
                                        baseLevel += cfg.Param;
                                    }
                                }
                                else if (cfg.Type == 7)//7.产出等级+N
                                {
                                    baseLevel += cfg.Param;
                                }

                                if (baseLevel != boxTechLv)
                                {
                                    luckyType = (Enum_LuckyType)cfg.MergeEffect;
                                }
                            }
                        }
                        //else if(item.mType== UIHeroUpStarDialogSkillItem.SkillType.MergeAssist)
                        //{
                        //    var cfg = Cfg.C.CCSSkillForAssisted.I(item.id);
                        //    if (cfg != null)
                        //    {
                        //        if (cfg.EffectType == 4)//4.产出等级+N
                        //        {
                        //            baseLevel += cfg.Param;
                        //        }
                        //    }
                        //}
                    }

                
                    

                    // level = baseLevel + GetLuckAddLv(luckyType);

                    level = Mathf.Min(baseLevel, minLv);

                    var itemNum = curHero.HeroCfg.Num;

                    if (TestHeroCfgID && itemNum < 2)
                    {
                        itemNum = 2;
                    }

                    int curLevel = Mathf.Min(level, CSVItem.GetMaxLevelByTypeAndCode(1, code));

                    for (int i = 0; i < itemNum; i++)
                    {
                        ItemInfo itemInfo = CSVItem.GetItemInfoByTypeAndCodeAndLevel(1, code, curLevel);
                        if (itemInfo != null)
                        {
                            CreateItems.Add(itemInfo);
                        }
                    }
                }
            }

            return CreateItems;
        }


        public float GetProbability()
        {
            int totalMerge = GetHeroMerge();
            //var mergeA = totalMerge + ConfigID.GetConfigValueByID(MetaConfig.MergeParam_a_ID);
            //var mergeB = totalMerge * ConfigID.GetConfigValueByID(MetaConfig.MergeParam_d_ID) + ConfigID.GetConfigValueByID(MetaConfig.MergeParam_b_ID);

            float a = MetaConfig.MergeParam_a;// ConfigID.GetConfigValueByID(MetaConfig.MergeParam_a_ID);
            float b = MetaConfig.MergeParam_b;
            float d = MetaConfig.MergeParam_d;
            float c = MetaConfig.MergeParam_c;
            float x = totalMerge;

            var gailv = Mathf.Log(b * x + c, a) + d;// mergeA * 1f / mergeB;
            return gailv;
        }
        public override int GetHeroStar()
        {
            int totalStar = 0;
            foreach (var item in heroCfgIDs)
            {
                if (item > 0)
                {
                    var curStar = HeroGameData.I.GetHeroByCfgId(item)?.Star ?? 0;
                    totalStar += curStar;
                }
            }
            return totalStar;
        }

        //public bool CanSKill()
        //{
        //    return this.curHeroPower >= BoxCfg.Cost && heroCfgIDs.Count > 0 && heroCfgIDs[0] > 0;
        //}


        public void UpdateData(MBox box)
        {
            this.heroCfgIDs.Clear();
            this.heroCfgIDs.AddRange(box.heroes);

            this.MHeroMap = box.data;
        }
    }


    public class MergeStoreData
    {
        /// <summary>
        /// 当前私有仓库容量
        /// </summary>
        public int CurPrivateCapacity;

        public int PrivateCapacityMax
        {
            get
            {
                return Cfg.C.CCSMergeWarehouse.RawList().Count;
            }
        }

        public int TmpCapacityMax
        {
            get
            {
                return (int)MetaConfig.WarehouseSpace;
            }
        }

        /// <summary>
        /// 私有仓库道具
        /// </summary>
        public List<int> CurPrivateItems = new List<int>();
        /// <summary>
        /// 临时仓库道具 
        /// </summary>
        public List<int> TmpPrivateItems = new List<int>();

        public bool CurPrivateRed { get; private set; } = false;
        public bool TmpPrivateRed { get; private set; } = false;

        public void SetCurRedPoint(bool red)
        {
            CurPrivateRed = red;

            EventMgr.FireEvent(TEventType.K3MergeStoreHouseAdd);
        }

        public void SetTmpRedPoint(bool red)
        {
            TmpPrivateRed = red;

            EventMgr.FireEvent(TEventType.K3MergeStoreHouseAdd);
        }

        public void UpdateData(MergeNtf mergeNtf)
        {
            CurPrivateCapacity = mergeNtf.invCapacity;
            CurPrivateItems.Clear();

            CurPrivateItems.AddRange(mergeNtf.invs);
            TmpPrivateItems.Clear();
            TmpPrivateItems.AddRange(mergeNtf.tempInvs);
        }

        public void UpdateData(MergeChangeNtf mergeChangeNtf)
        {
            foreach (var item in mergeChangeNtf.invs)
            {
                if (CurPrivateItems.Count > item.Key)
                    CurPrivateItems[item.Key] = item.Value;
            }

            foreach (var item in mergeChangeNtf.tempInvs)
            {
                if (TmpPrivateItems.Count > item.Key)
                    TmpPrivateItems[item.Key] = item.Value;
            }
        }

    }


    //public class MergeSoldierStoreData
    //{
    //    public Dictionary<int, DGSoldiers> StoreSoldiers = new Dictionary<int, DGSoldiers>();

    //    public List<DGInTreatmentSoldiers> InTreatment = new List<DGInTreatmentSoldiers>();

    //    public Dictionary<int, int> newSildiers = new Dictionary<int, int>();

    //    public int CurSoldierNum()
    //    {
    //        int curCount = 0;
    //        foreach (var item in StoreSoldiers)
    //        {
    //            foreach (var ii in item.Value.num)
    //            {
    //                curCount += ii;
    //            }
    //        }

    //        return curCount;
    //    }

    //    public int MaxSoldierNum()
    //    {
    //        return (int)MetaConfig.MaxCapacity;
    //    }

    //    public bool CanInStore()
    //    {
    //        int maxCount = MaxSoldierNum();

    //        int curCount = CurSoldierNum();

    //        return curCount < maxCount;
    //    }

    //    public void InStore(int serverIndex, int itemID)
    //    {
    //        if (StoreSoldiers.ContainsKey(itemID))
    //        {
    //            var dg = StoreSoldiers[itemID];
    //            dg.num[0] = dg.num[0] + 1;
    //        }
    //        else
    //        {
    //            var dg = new DGSoldiers();
    //            dg.num.Add(1);
    //            dg.num.Add(0);
    //            dg.num.Add(0);

    //            StoreSoldiers.Add(itemID, dg);
    //        }

    //        var syncGround = new SyncDrillGroundReq();
    //        syncGround.action = SyncDGAction.ActionPush;
    //        syncGround.mergeInfo.Add(serverIndex, itemID);
    //        syncGround.soldierNums.Add(itemID, 1);

    //        CSPlayer.I.SyncMerge();

    //        MessageMgr.Send(syncGround);
    //    }

    //    Dictionary<int, List<int>> type_Datas = new Dictionary<int, List<int>>();
    //    List<SoldierDisplayData> Soldiers = new List<SoldierDisplayData>();

    //    public List<SoldierDisplayData> GetDisplaySoldiers()
    //    {
    //        Soldiers.Clear();
    //        type_Datas.Clear();

    //        var itemKeys = StoreSoldiers.Keys.ToList();


    //        foreach (var item in itemKeys)
    //        {
    //            if (StoreSoldiers[item].num.Count > 0 && StoreSoldiers[item].num.Count > 1 && (StoreSoldiers[item].num[0] + StoreSoldiers[item].num[1] + StoreSoldiers[item].num[2]) > 0)
    //            {
    //                int code = Cfg.C.CK3Item.I(item).Code;
    //                if (type_Datas.TryGetValue(code, out var keyList))
    //                {
    //                    keyList.Add(item);

    //                    keyList.Sort((x, y) =>
    //                    {
    //                        return y - x;
    //                    });
    //                }
    //                else
    //                {
    //                    type_Datas.Add(code, new List<int>() { item });
    //                }
    //            }
    //        }

    //        foreach (var item in InTreatment)
    //        {
    //            foreach (var item1 in item.Wounded)
    //            {
    //                if (item1.Value > 0)
    //                {
    //                    int code = Cfg.C.CK3Item.I(item1.Key).Code;
    //                    if (!type_Datas.ContainsKey(code))
    //                    {
    //                        type_Datas.Add(code, new List<int>() { item1.Key });
    //                        Debug.LogError("额外附加治疗中的ID:" + item1.Key + "数量：" + item1.Value);
    //                    }
    //                }

    //            }
    //        }




    //        var typeKeys = type_Datas.Keys.ToList();
    //        typeKeys.Sort((x, y) =>
    //        {
    //            return x - y;
    //        });


    //        foreach (var item in typeKeys)
    //        {
    //            var soldierData = new SoldierDisplayData() { displayType = item };
    //            Soldiers.Add(soldierData);

    //            foreach (var ii in type_Datas[item])//ID 列表
    //            {
    //                if (!StoreSoldiers.ContainsKey(ii))
    //                {
    //                    continue;
    //                }
    //                int maxLv = CSVItem.GetMaxLevelByTypeAndCode(1, item);

    //                int numCount = StoreSoldiers[ii].num[0];
    //                int troopCount = StoreSoldiers[ii].num[1];
    //                int severeCount = StoreSoldiers[ii].num[2];

    //                for (int i = 0; i < troopCount; i++)
    //                {
    //                    soldierData.displaySoldiers.Add(new Vector3Int(ii, 1, 0));
    //                }

    //                for (int i = 0; i < severeCount; i++)
    //                {
    //                    soldierData.displaySoldiers.Add(new Vector3Int(ii, 2, 0));
    //                }

    //                for (int i = 0; i < numCount; i++)
    //                {
    //                    if (Cfg.C.CK3Item.I(ii).Level < maxLv)
    //                    {
    //                        soldierData.displaySoldiers.Add(new Vector3Int(ii, 0, numCount > 1 ? 1 : 0));
    //                    }
    //                    else
    //                    {
    //                        soldierData.displaySoldiers.Add(new Vector3Int(ii, 0, 0));
    //                    }
    //                }
    //            }
    //        }

    //        return Soldiers;
    //    }

    //    /// <summary>
    //    /// 获取可上阵的士兵 根据类型来获取
    //    /// </summary>
    //    /// <param name="soldierType">士兵类型1~4</param>
    //    /// <returns></returns>
    //    public Dictionary<int, int> GetIdleSoldiers(int soldierType)
    //    {
    //        Dictionary<int, int> soldiers = new Dictionary<int, int>();

    //        var itemKeys = StoreSoldiers.Keys.ToList();
    //        foreach (var item in itemKeys)
    //        {
    //            if (StoreSoldiers[item].num.Count > 0 && StoreSoldiers[item].num[0] > 0)
    //            {
    //                int code = Cfg.C.CK3Item.I(item).Code;
    //                if (code == (20 + soldierType))
    //                {
    //                    soldiers.Add(item, StoreSoldiers[item].num[0]);
    //                }
    //            }
    //        }

    //        return soldiers;
    //    }

    //    public bool CanAutoLevelUp()
    //    {
    //        foreach (var item in StoreSoldiers)
    //        {
    //            var itemCfg = Cfg.C.CK3Item.I(item.Key);
    //            if (item.Value.num.Count > 1 && itemCfg != null)
    //            {
    //                int maxLv = CSVItem.GetMaxLevelByTypeAndCode(1, itemCfg.Code);
    //                if (itemCfg.Level < maxLv)
    //                {
    //                    int numCount = item.Value.num[0];
    //                    if (numCount > 1)
    //                    {
    //                        return true;
    //                    }
    //                }
    //            }
    //        }

    //        return false;
    //    }

    //    public void AutoLevelUp()
    //    {
    //        MessageMgr.Send(new DGAutoMergeReq());
    //    }


    //    public void OutStore(int itemID)
    //    {
    //        if (StoreSoldiers.TryGetValue(itemID, out var soldier))
    //        {
    //            var uiMerge = DeepUI.PopupManager.I.FindPopup<UIMerge>();
    //            if (uiMerge != null)
    //            {
    //                List<UIMergeGrid> grids = uiMerge.GetFreeGrids(1);
    //                if (grids.Count > 0)
    //                {
    //                    for (int i = 0; i < grids.Count; i++)
    //                    {
    //                        UIMergeGrid gride = grids[i];
    //                        UIMergeGood good = uiMerge.GetGood();

    //                        GameAudio.PlayAudio(6);
    //                        good.Init(gride, CSVItem.GetItemInfoById(itemID), uiMerge.MoveGoodRoot, uiMerge, false, 0, true, i * 0.15f);


    //                        var syncGround = new SyncDrillGroundReq();
    //                        syncGround.action = SyncDGAction.ActionPop;
    //                        syncGround.mergeInfo.Add(gride.Point.ServerIndex, itemID);
    //                        syncGround.soldierNums.Add(itemID, 1);

    //                        MessageMgr.Send(syncGround);
    //                    }

    //                    soldier.num[0] = soldier.num[0] - 1;

    //                    EventMgr.FireEvent(TEventType.K3MergeSoldierStoreRefesh);
    //                }
    //                else
    //                {
    //                    FloatTips.I.FloatMsg(TFW.Localization.LocalizationMgr.Get("Tips_BoardNoSpeace"));
    //                }
    //            }
    //            else
    //            {
    //                FloatTips.I.FloatMsg(TFW.Localization.LocalizationMgr.Get("Skill_No_Space"));
    //            }

    //        }
    //    }


    //    public class SoldierDisplayData
    //    {
    //        public int displayType;
    //        /// <summary>
    //        /// 显示的士兵 X为士兵ID Y为0 正常 1为出征中 Z可升级 0不可 1可
    //        /// </summary>
    //        public List<Vector3Int> displaySoldiers = new List<Vector3Int>();
    //    }
    //}
}
