#define EXLOG_REDIR

#if EXLOG_REDIR
using System;
using System.Diagnostics;

using UnityEngine;

using ExLog;

public static class Debug
{
    public static void Assert(bool condition, string msg = null)
    {
#if UNITY_EDITOR
        if (!Application.isPlaying)
        {
            UnityEngine.Debug.Assert(condition, msg);
            return;
        }
#endif

        D.Default.Error?.Assert(condition, msg);
    }

    public static void Log(object msg, UnityEngine.Object context = null)
    {
#if UNITY_EDITOR
        if (!Application.isPlaying)
        {
            UnityEngine.Debug.Log(msg, context);
            return;
        }
#endif

        D.Default.Normal?.Log(context, msg);
    }

    public static void LogFormat(string msg, params object[] args)
    {
#if UNITY_EDITOR
        if (!Application.isPlaying)
        {
            UnityEngine.Debug.LogFormat(msg, args);
            return;
        }
#endif

        D.Default.Normal?.Log(string.Format(msg, args));
    }

    public static void LogWarning(object msg)
    {
#if UNITY_EDITOR
        if (!Application.isPlaying)
        {
            UnityEngine.Debug.LogWarning(msg);
            return;
        }
#endif

        D.Default.Warning?.Log(msg);
    }

    public static void LogWarningFormat(string msg, params object[] args)
    {
#if UNITY_EDITOR
        if (!Application.isPlaying)
        {
            UnityEngine.Debug.LogWarning(msg);
            return;
        }
#endif

        D.Default.Warning?.Log(msg, args);
    }

    public static void LogError(object msg)
    {
#if UNITY_EDITOR
        if (!Application.isPlaying)
        {
            UnityEngine.Debug.LogError(msg);
            return;
        }
#endif

        D.Default.Error?.Log(msg);
    }

    public static void LogErrorFormat(string msg, params object[] args)
    {
#if UNITY_EDITOR
        if (!Application.isPlaying)
        {
            UnityEngine.Debug.LogErrorFormat(msg, args);
            return;
        }
#endif

        D.Default.Error?.Log(msg, args);
    }

    public static void LogException(Exception e, object obj = null)
    {
#if UNITY_EDITOR
        if (!Application.isPlaying)
        {
            UnityEngine.Debug.LogException(e);
            return;
        }
#endif

        D.Default.Error?.Log(e);
    }

    #region 其他UnityEngine.Debug自带的字段和方法（如果编译时出现找不到Debug类中的方法，就去UnityEngine.Debug类中拷贝方法签名到这里）
    public static ILogger unityLogger => UnityEngine.Debug.unityLogger;

    [Conditional("UNITY_EDITOR")]
    public static void DrawLine(Vector3 start, Vector3 end, Color color) => UnityEngine.Debug.DrawLine(start, end, color);

    [Conditional("UNITY_EDITOR")]
    public static void DrawLine(Vector3 start, Vector3 end, Color color, float duration) => UnityEngine.Debug.DrawLine(start, end, color, duration);

    [Conditional("UNITY_EDITOR")]
    public static void DrawRay(Vector3 start, Vector3 dir, Color color) => UnityEngine.Debug.DrawRay(start, dir, color);

    [Conditional("UNITY_EDITOR")]
    public static void Break() => UnityEngine.Debug.Break();

    #endregion
}
#endif