﻿using Cfg.C;
using Common;
using cspb;
using Cysharp.Threading.Tasks;
using DeepUI;
using Game.Config;
using Game.Data;
using Game.Sprite.Fight;
using K1;
using Logic;
using Public;
using System;
using System.Collections.Generic;
using TFW;
using TFW.Localization;
using TFW.UI;
using UnityEngine;
using UnityEngine.EventSystems;
using EventTriggerType = TFW.EventTriggerType;

namespace UI
{

    /// <summary>
    /// 集结野怪数据
    /// </summary>
    public class UIRallyBattleNpcData : PopupData
    {
        /// <summary>
        /// 结束时间
        /// </summary>
        public long endTime;

        /// <summary>
        /// 野怪配置
        /// </summary>
        public Cfg.G.CD2NpcTroopClass config;

        /// <summary>
        /// 集结目标实体id
        /// </summary>
        public long entityId;
        /// <summary>
        /// ui跳转来自哪里
        /// </summary>
        public string UIPopupFrom;

    }

    /// <summary>
    /// 集结野怪
    /// <AUTHOR>
    /// @date 2020/8/17 14:24:26
    /// @ver 1.0
    /// </summary>
    [Popup("Mass/UIMonsterFortMass_New")]  //"Mass/New/UIMonsterFortMass"
    public class UIRallyBattleNpc : HistoricPopup
    {
        ///// <summary>
        ///// 弹出类型
        ///// </summary>
        //public EPopPanelType PopPanelType { get => base.PopPanelType = EPopPanelType.ALLMask; set => base.PopPanelType = value; }
        ///// <summary>
        ///// UI界面排序显示
        ///// </summary>
        ////public override ESortingOrder SortingOrder => ESortingOrder.MaxOrder;
        //protected string assetPath => "Mass/UIMonsterFortMass_New";//"Mass/New/UIMonsterFortMass"

        ///// <summary>
        ///// 主菜单互斥
        ///// </summary>
        //public bool BottomMiddle { get; set; } = true;
        protected internal override bool PopBackEnabled => true;
        /// <summary>
        /// 活动时间 过了就消失
        /// </summary>
        private TFWText lifeTime;
        private Dictionary<int, ItemNewWidget> widgetDic = new Dictionary<int, ItemNewWidget>();
        /// <summary>
        /// ui数据
        /// </summary>
        private UIRallyBattleNpcData uidata;

        /// <summary>
        /// 计时器
        /// </summary>
        private NTimer.Timer timer;
        /// <summary>
        /// 普通宝箱按钮
        /// </summary>
        private GameObject leftReawrdBtn;
        /// <summary>
        /// 银宝箱按钮
        /// </summary>
        private GameObject rightRewardBtn;

        /// <summary>
        /// 分享按钮
        /// </summary>
        private GameObject shareBtn;

        /// <summary>
        /// 分享按钮
        /// </summary>
        private GameObject favoriteBtn;

        /// <summary>
        /// 详情按钮
        /// </summary>
        private GameObject infoBtn;

        /// <summary>
        /// 时间选择
        /// </summary>
        private UITimeSelect uiTimeSelect;
        
        /// <summary>
        /// 奖励道具的root
        /// </summary>
        private UIGrid m_ItemGridRoot;

        /// <summary>
        /// 战力数据显示
        /// </summary>
        private TFWText _powerText;
        private GameObject _powerObj;
        private TFWText middleDesText;

        private TFWText mCostTxt;

        private GameObject troopClassIcon, HaLuodeImg,AllianceBossImg1, AllianceBossImg2;

        private TimeUpdateObj autoObj;
        private TimeUpdateText autoEndTimeText;
        /// <summary>
        /// 资源奖励模块
        /// </summary>
        private GameObject ResItem;
        /// <summary>
        /// 资源显示窗口
        /// </summary>
        private TFWLoopListView listView;

        private GameObject MiddleReward;


        private List<GameObject> resObjs = new List<GameObject>();
        ///// <summary>
        ///// 初始化
        ///// </summary>
        //protected override void OnInit()
        //{
        //    base.OnInit();

        //}
        private GameObject massObj;
        //熔岩试炼相关
        private GameObject allianceBossObj;
        private TFWText sliderTitle;
        private TFWText sliderTxt;
        private TFWSlider slider;
        private TFWText rewardTitle;
        private TFWLoopListView loopListView;
        List<TypIDVal> rewards;
        protected override void OnShown()
        {
            base.OnShown();
            uidata = this.InitData as UIRallyBattleNpcData;

            if (uiTimeSelect != null)
            {
                uiTimeSelect.Reset();
            }

            var title = GetComponent<TFWText>("Root/TitleBar/Title");
            var entityInfo = LMapEntityManager.I.GetEntityInfo(uidata.entityId);
            var level = 1;
            if (entityInfo != null && entityInfo.NpcTroop != null)
            {
                level = entityInfo.NpcTroop.GetLevel();
            }

            //Todo 等级读取
            if(isFromAlianceBoss())
                title.text = uidata.config.GetName();
            else
                title.text = string.Format(LocalizationMgr.Get("BUILDING_parameter_level_parameter"), level, uidata.config.GetName());

            if (uidata.config.Id == 13330005)
            {
                middleDesText.text = LocalizationMgr.Get("Rally_Union_Gift_Title");
            }
            else if (uidata.config.Id == 13330006)
            {
                middleDesText.text = LocalizationMgr.Get("Harold_Reward_Desc");
            }

            mCostTxt.text = MetaConfig.RallyInitiateAPExtra[0].Val.ToString();

            var autoRallyBtn = GetChild("Root/AutomaticBtn");

            if (uidata.config.LeaderComposition.Count > 0)
            {
                HaLuodeImg.SetActive(uidata.config.LeaderComposition[0].Id == 9906);
                // troopClassIcon.SetActive(uidata.config.LeaderComposition[0].Id == 9905);

                autoRallyBtn.SetActive(LSwitchMgr.I.IsFunctionOpen(SwitchConfig.Offline_Rally)
                 && LPlayer.I.IsPlayerInUnion()
                 && uidata.config.LeaderComposition[0].Id == 9905);
            }
            ResItem = GetChild("Root/Middle/ScrollView/ViewPort/Content/Items/Item");
            ResItem?.SetActive(false);
            //刷新npc军队战力显示
            UpdateNpcTroopPower(entityInfo);

            SetNpcRewards();

            RefreshTime();
            if (timer != null)
            {
                StopTimer();
            }
            timer = NTimer.New();
            timer.StartTick(NTimer.INF, 1f, RefreshTime);

            //只要是跨服屏蔽分享和收藏按钮
            shareBtn.SetActive(!LPlayer.I.IsCrossServer);
            favoriteBtn.SetActive(!LPlayer.I.IsCrossServer);
            ShowAutoRally();
            UpdateAllianceBossUI();
        }
        //是否来自熔岩试炼
        private bool isFromAlianceBoss()
        {
            return uidata.UIPopupFrom == "AllianceBoss";
        }
        //获取熔岩试炼对应config
        public Cfg.G.CActivityUnionBoss GetActivityUnionBossCfg(int id)
        {
            List<Cfg.G.CActivityUnionBoss> activityUnionBossCfgList = Cfg.C.CActivityUnionBoss.RawList();
            for (int i = 0; i < activityUnionBossCfgList.Count; i++)
            {
                if (activityUnionBossCfgList[i].MonsterID == id)
                {
                    return activityUnionBossCfgList[i];
                };
            }
            return null;
        }
        private async UniTask UpdateAllianceBossUI()
        {
            massObj?.SetActive(!isFromAlianceBoss());
            allianceBossObj?.SetActive(isFromAlianceBoss());
            AllianceBossImg1?.SetActive(false);
            AllianceBossImg2?.SetActive(false);
            if (isFromAlianceBoss())
            {
                rewards = await Logic.VersionRewardMgr.I.GetDisplayRewards2(GetActivityUnionBossCfg(uidata.config.Id).Reward);
                var entityInfo = LMapEntityManager.I.GetEntityInfo(uidata.entityId);
                bool isPersonalBoss = entityInfo.NpcTroop.unionBossExt.type == 1;
                AllianceBossImg1?.SetActive(isPersonalBoss);
                AllianceBossImg2?.SetActive(entityInfo.NpcTroop.unionBossExt.type == 2);
                var cur = entityInfo.NpcTroop.combat;
                var max = entityInfo.NpcTroop.combatMax;
                var val = (float)cur / max;
                sliderTitle.text = LocalizationMgr.Get("Union_Boss_hp_tips");
                sliderTxt.fontSize = isPersonalBoss ? 45:70;
                sliderTxt.text = isPersonalBoss ? UIStringUtils.FormatPercentByLanguage(val, 2): "∞";
                rewardTitle.text = LocalizationMgr.Get("Rally_Random_Player_Chest");
                sliderTitle.GetComponent<RectTransform>().anchoredPosition = isPersonalBoss ? new Vector3(-438.6f, 82, 0): new Vector3(-438.6f, 165, 0);
                slider.value = val;
                if (!loopListView.ListViewInited)
                {
                    loopListView.InitListView(rewards.Count, OnInitListView);
                }
                else
                {
                    loopListView.SetListItemCount(rewards.Count, false);
                    loopListView.RefreshAllShownItem();
                }
                _powerObj.SetActive(entityInfo.NpcTroop.unionBossExt.type == 1);
                _powerText.text = UIStringUtils.FormatIntegerByLanguage(entityInfo.NpcTroop.combat);
            }
        }
        
        private async UniTask SetNpcRewards()
        {
            var entityInfo = LMapEntityManager.I.GetEntityInfo(uidata.entityId);
            
            if (entityInfo != null)
            {
                m_ItemGridRoot = GetComponent<UIGrid>("Root/MassNew/ScrollView/RssDetail/Grid");

                if (m_ItemGridRoot)
                {
                    m_ItemGridRoot.Clear();

                    if (entityInfo.WorldBoss != null)
                    {
                        //世界boss奖励
                        var rewards = await CWorldBossQuestExtension.GetWorldBossReward();
                        foreach (var item in rewards)
                        {
                            m_ItemGridRoot.AddItem<CommonItem>().InitData(new CommonItem.CommonItemData(item));
                        }
                    }
                    else
                    {
                        var powerConfig = CNpcTroopPowerExtension.GetNpcTroopPower(entityInfo.NpcTroop.npcTroopID, entityInfo.NpcTroop.GetLevel());
                        foreach (var item in powerConfig.DropDisplay)
                        {
                            if (item.Id == 21113001)//金币
                            {
                                CommonItem.CommonItemData itemData = new CommonItem.CommonItemData();
                                int realNum = MetaConfig.NpcTroopReward[0] + (entityInfo.NpcTroop.GetLevel() - 1) * MetaConfig.NpcTroopReward[1];
                                itemData.Val = realNum > MetaConfig.NpcTroopReward[2]
                                   ? MetaConfig.NpcTroopReward[2] : realNum;

                                itemData.Id = item.Id;
                                itemData.Typ = item.Typ;
                                m_ItemGridRoot.AddItem<CommonItem>().InitData(itemData);
                            }
                            else
                            {
                                m_ItemGridRoot.AddItem<CommonItem>().InitData(new CommonItem.CommonItemData(item));
                            }
                        }
                    }
                }
            }
        }
        
        private TFWLoopListViewItem OnInitListView(TFWLoopListView loopListView, int index)
        {
            if (index < 0)
                return null;

            if (rewards == null
                || index >= rewards.Count
                || rewards[index] == null)
                return null;

            TFWLoopListViewItem temp = loopListView.NewListViewItem("Item");
            if (temp.gameObject == null)
                return null;
            ItemNewWidget widget;
            if (!widgetDic.TryGetValue(index, out widget))
            {
                widget = new ItemNewWidget(temp.gameObject);
                widgetDic[index] = widget;
            }
            else
                widget.OnChangeRoot(temp.gameObject);
            widget.SetData(rewards[index], true, true, true);
            return temp;
        }

        /// <summary>
        /// 普通宝箱
        /// </summary>
        private TFWText leftReawrdText;

        /// <summary>
        /// 加载
        /// </summary>
        protected override void OnLoad()
        {
            var closeBtn = GetChild("Root/BG/CloseBtn");
            massObj = UIHelper.GetChild(gameObject, "Root/MassNew");
            var bgBtn = GetChild("Black");
            favoriteBtn = GetChild("Root/BG/StarBtn");
            shareBtn = GetChild("Root/BG/ShareBtn");
            infoBtn = GetChild("Root/BG/InfoBtn");

            troopClassIcon = GetChild("Root/TopImg");
            HaLuodeImg = GetChild("Root/HaLuodeImg");
            AllianceBossImg1 = GetChild("Root/AllianceBossImg1");
            AllianceBossImg2 = GetChild("Root/AllianceBossImg2");
            //活动时间
            lifeTime = GetComponent<TFWText>("Root/TitleBar/Time");
            //战力显示
            _powerText = GetComponent<TFWText>("Root/TitleBar/Power/Text");
            _powerObj = GetChild("Root/TitleBar/Power");
            uiTimeSelect = new UITimeSelect();
            uiTimeSelect.Init(GetChild("Root/Time"));

            //普通宝箱
            leftReawrdText = GetComponent<TFWText>("Root/Mass/Reward/Left/Text");

            //银宝箱
            var rightReawrdText = GetComponent<TFWText>("Root/Mass/Reward/Right/Text");

            //可能包含
            var middleTitleText = GetComponent<TFWText>("Root/Mass/Middle/Title");

            //资源滚动列表
            listView = GetComponent<TFWLoopListView>("Root/Mass/Middle/ScrollView");

            MiddleReward = GetChild("Root/Mass/Middle");

            middleDesText = GetComponent<TFWText>("Root/Mass/Middle/Text");

            //大量金币
            var buttomText = GetComponent<TFWText>("Root/Mass/Buttom/Coin/Text");

            //随机
            var cardText = GetComponent<TFWText>("Root/Mass/Buttom/Card/Text");

            //集结攻击
            var attackText = GetComponent<TFWText>("Root/MassBtn/Text");
            attackText.text = LocalizationMgr.Get("Rally_Function_Start1");

            mCostTxt = GetComponent<TFWText>("Root/MassBtn/BG (1)/Text (1)");


            var massBtn = GetChild("Root/MassBtn");
            //普通宝箱按钮
            leftReawrdBtn = GetChild("Root/Mass/Reward/Left/Icon");
            //银宝箱按钮
            rightRewardBtn = GetChild("Root/Mass/Reward/Right/Icon");


            var autoRallyBtn = GetChild("Root/AutomaticBtn");

            autoRallyBtn.SetActive(LSwitchMgr.I.IsFunctionOpen(SwitchConfig.Offline_Rally) && LPlayer.I.IsPlayerInUnion());

            autoObj = GetComponent<TimeUpdateObj>("Root/AutomaticBtn/Slider");
            autoEndTimeText = GetComponent<TimeUpdateText>("Root/AutomaticBtn/Slider/Text");

            AddListener(EventTriggerType.Click, autoRallyBtn, (x, y) =>
            {
                PopupManager.I.ShowPanel<UIAutoRallymatic_New>();
            });

            AddListener(EventTriggerType.Click, bgBtn, OnClose);
            AddListener(EventTriggerType.Click, closeBtn, OnClose);
            AddListener(EventTriggerType.Click, favoriteBtn, OnClickFavorite);
            AddListener(EventTriggerType.Click, infoBtn, OnClickInfo);
            AddListener(EventTriggerType.Click, shareBtn, OnClickShare);
            AddListener(EventTriggerType.Click, massBtn, OnMassBtnClick);
            AddListener(EventTriggerType.Click, leftReawrdBtn, OnClickLeftReawrdBtn);
            AddListener(EventTriggerType.Click, rightRewardBtn, OnClickRightRewardBtn);
            //熔岩试炼
            allianceBossObj = UIHelper.GetChild(gameObject,"Root/AllianceBoss");
            slider = UIHelper.GetComponent<TFWSlider>(gameObject, "Root/AllianceBoss/Slider/Slider");
            sliderTitle = UIHelper.GetComponent<TFWText>(gameObject, "Root/AllianceBoss/Slider/Title");
            sliderTxt = UIHelper.GetComponent<TFWText>(gameObject, "Root/AllianceBoss/Slider/Slider/Text");
            rewardTitle = UIHelper.GetComponent<TFWText>(gameObject, "Root/AllianceBoss/Reward/Title");
            loopListView = UIHelper.GetComponent<TFWLoopListView>(gameObject, "Root/AllianceBoss/Reward/ScrollView");
        }
 

        private void ShowAutoRally()
        {
            if (GameData.I.RallyData.RallyAutoStatus != null)
            {
                autoObj.InitEndTime(GameData.I.RallyData.RallyAutoStatus.enable ? GameData.I.RallyData.RallyAutoStatus.endingTime : 0);
                autoEndTimeText.InitEndTime(GameData.I.RallyData.RallyAutoStatus.endingTime);
            }
            else
            {
                autoObj.gameObject.SetActive(false);
            }
        }


        /// <summary>
        /// 检查战力是否足够
        /// </summary>
        /// <returns></returns>
        private bool CheckPower()
        {
            List<Lineup> _lineupList = GameData.I.LineUpData.GetLineUpData();

            //找到部队中含有最大战力的
            long lineupMaxPower = 0;
            List<int> _lineupLst = GameData.I.LineUpData.lockedList;
            for (int i = 0; i < _lineupLst.Count; i++)
            {
                var index = _lineupLst[i];
                var lineup = _lineupList[index];
                if (lineup.heroes.Count > 0)
                {
                    if (_lineupList[index].state != 0)
                    {
                        if (lineupMaxPower < lineup.combatPower)
                        {
                            lineupMaxPower = lineup.combatPower;
                        }
                    }
                }
            }

            //5分之一的血量
            var npcPercentage = npcPower * 0.2f;
            Debug.LogFormat("20npcPercentage={0},lineupMaxPower={1},npcPower={2}", npcPercentage, npcPower, lineupMaxPower);
            //如果玩家最强队伍战力低于选中怪物战力的五分之一，就弹二次确认
            //if (lineupMaxPower < npcPercentage)
            //{
            //    var btnParam_1 = new MsgBoxBtnParam()
            //    {
            //        str = LocalizationMgr.GetUIString("MENU_confirm_cap"),
            //        func = (_) =>
            //        {
            //            if (uidata != null)
            //            {
            //                GoRally(uidata.entityId);
            //            }
            //        }
            //    };

            //    //Rally_Power_Enough_Toast  战力悬殊，可能无法获胜，是否继续发起集结?
            //    var btnParam_2 = new MsgBoxBtnParam()
            //    {
            //        str = LocalizationMgr.GetUIString("MENU_cancel_cap"),
            //        func = null,
            //    };
            //    var title = LocalizationMgr.GetUIString("MAIL_mail_pop_up_title");
            //    var content = string.Format(LocalizationMgr.GetUIString("Rally_Power_Enough_Toast"));
            //    UITools.ShowMsgBox(EMsgBoxType.two_nc, title, content, btnParam_1, btnParam_2, ButtonColorGroup.RedBlue);
            //    return false;
            //}

            return true;
        }

        /// <summary>
        /// 检查是否已经存在npc的集结
        /// </summary>
        /// <param name="entityId"></param>
        /// <returns></returns>
        private bool CheckExsitRally(long entityId)
        {
            var isExsit = GameData.I.RallyData.CheckExsitRally(entityId);
            //熔岩试炼联盟boss只要自己没集结过就可以集结一次
            if (isFromAlianceBoss())
            {
                var entityInfo = LMapEntityManager.I.GetEntityInfo(entityId);
                var unionBossData = entityInfo.NpcTroop.unionBossExt;
                //type是2的为联盟boss
                if (unionBossData!= null && unionBossData.type == 2)
                {
                    isExsit = GameData.I.RallyData.CheckExsitRally(entityId, true);
                    if (isExsit)
                    {
                        FloatTips.I.FloatMsg(LocalizationMgr.Get("ActiveUnionBoss_Tips_301"));
                    }
                }
                //熔岩试炼个人boss不限制召唤次数
                else
                {
                    isExsit = false;
                }
            }
            
            if (isExsit)
            {
                var btnParam_1 = new MsgBoxBtnParam()
                {
                    str = LocalizationMgr.GetUIString("Quest_Go_Btn"),
                    func = (_) =>
                    {
                        PopupManager.I.ShowLayer<UIMilitaryAlertMain>();
                    }
                };

                var btnParam_2 = new MsgBoxBtnParam()
                {
                    str = LocalizationMgr.GetUIString("MENU_cancel_cap"),
                    func = null,
                };
                var title = LocalizationMgr.GetUIString("MAIL_mail_pop_up_title");
                var content = string.Format(LocalizationMgr.GetUIString("Rally_Been_Host_Toast"));
                UITools.ShowMsgBox(EMsgBoxType.two_nc, title, content, btnParam_2, btnParam_1);
                return true;
            }
            return false;

        }

        /// <summary>
        /// 集结
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        private void OnMassBtnClick(GameObject arg0, PointerEventData arg1)
        {
            if (isFromAlianceBoss())
            {
                var entityInfo = LMapEntityManager.I.GetEntityInfo(uidata.entityId);
                var unionBossData = entityInfo.NpcTroop.unionBossExt;
                if (unionBossData != null)
                { 
                    //个人boss只能自己发起无数次集结,联盟内玩家可以加入集结
                    if (unionBossData.type == 1 && unionBossData.callerId != LPlayer.I.PlayerID)
                    {
                        FloatTips.I.FloatMsg(LocalizationMgr.Get("Union_Boss_Rally_tips01"));
                        return;
                    }
                    //联盟boss只能联盟内成员发起一次集结
                    else if (unionBossData.type == 2 && unionBossData.callerId != LAllianceMgr.I.GetUnionId())
                    {
                        FloatTips.I.FloatMsg(LocalizationMgr.Get("Union_Boss_Rank_tips03"));
                        return;
                    }
                } 
            }
            WndMgr.Hide<UINpcTroopPopup>();
            Close();

            if (!LPlayer.I.IsPlayerInUnion())
            {
                //UIMain.I.TrySwitch(MainMenuConst.ALLIANCE);
                //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.ALLIANCE);
                return;
            }

            //暂时去掉距离检测。后续要改成附近是否有盟友
            //if (CheckDis())
            //{
            //    //距离太远
            //    return;
            //}

            GoRallyConfirm();
        }

        private void  TryGoRally()
        {
            if (uidata != null)
            {
                var entityId = uidata.entityId;
                GoRally(entityId);
            }
        }
        private void GoRallyConfirm()
        {
            //熔岩试炼boss不进行战力判定
            if (isFromAlianceBoss())
            {
                TryGoRally();
                return;
            }
            //if (CheckPower())
            {
                TryGoRally();
            }
        }

        /// <summary>
        /// 检测是否离盟主城堡太远
        /// </summary>
        /// <returns></returns>
        private bool CheckDis()
        {
            if (LAllianceMgr.I.IsUnionLeader())
            {
                //如果是盟主不做距离检测
                Debug.Log("CheckDis IsUnionLeader true");
                return false;
            }

            var pos = LPlayer.I.GetMainCityPosition();
            var layer = TerritoryUtils.TerritoryLayer;
            if (layer != null)
            {
                if (layer.IsInTerritory(LPlayer.I.UnionID, pos, out var isEffect))
                {
                    //如果在领地内不做距离检测
                   Debug.Log("CheckDis IsInTerritory true");
                    return false;
                }
            }
            Debug.Log("CheckDis IsInTerritory false");

            var targetPos = LAllianceAllyCity.I.LeaderPos;
            var dis = Math.Ceiling(Vector3.Distance(pos, targetPos));
            var range = MetaConfig.Rally_Far_From_Main_Castle;
            Debug.LogFormat("range={0},dis={1},targetPos={2},selfPos={3}", range, dis, targetPos, pos);

            if (dis > range)
            {
                //离大家城堡太远，集结可能会没人参加。下面两个按钮，继续=灰色，迁城=绿色
                var btnParam_1 = new MsgBoxBtnParam()
                {
                    str = LocalizationMgr.GetUIString("Shop_Text_9"),
                    func = (_) =>
                    {
                        if (uidata != null)
                        {
                            LAllianceMgr.I.ReqUnionRandomCoord();
                        }
                    }
                };

                var btnParam_2 = new MsgBoxBtnParam()
                {
                    str = LocalizationMgr.GetUIString("Player_Name_Guide_Continue"),
                    func = (_) =>
                    {
                        GoRallyConfirm();
                        //GoRally(uidata.entityId);
                    },
                };
                var title = LocalizationMgr.GetUIString("MAIL_mail_pop_up_title");
                var content = string.Format(LocalizationMgr.GetUIString("RallyTeleportTips"));
                UITools.ShowMsgBox(EMsgBoxType.two_nc, title, content, btnParam_2, btnParam_1, ButtonColorGroup.GrayGreen);

                return true;
            }
            return false;
        }
        
        /// <summary>
        /// 集结
        /// </summary>
        /// <param name="entityId"></param>
        private void GoRally(long entityId)
        {
            
            if (CheckExsitRally(entityId))
            {
                return;
            }

            var entityInfo = LMapEntityManager.I.GetEntityInfo(entityId);
            if (entityInfo != null)
            {
                var time = 0;
                if (uiTimeSelect != null)
                {
                    time = uiTimeSelect.CurTimeSeconds * 1000;
                }
                if (time > 0)
                {
                    Rally(entityId, time);
                }
                //PopupManager.I.ShowPanel<UIInitiateRally>(new UIInitiateRallyData() { entityId = this.uidata.entityId, marchAct = MarchAct.MarchActRally });
            }
            else
            {
              Debug.LogFormat("not find entityInfo by id={0}", entityId);
            }
        }


        /// <summary>
        /// 发起集结
        /// </summary>
        /// <param name="entityId"></param>
        private void Rally(long entityId, int time)
        {
            var cityId = LPlayer.I.MainCityID;
            var cityEntity = LMapEntityManager.I.GetEntityInfo(cityId);
            var targetEntity = LMapEntityManager.I.GetEntityInfo(entityId);

            if (cityEntity == null || targetEntity == null)
            {
                return;
            }

            if (uiTimeSelect != null)
            {
                if (!LPlayer.I.IsPlayerInUnion())
                {
                    //UIMain.I.TrySwitch(MainMenuConst.ALLIANCE);
                    //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.ALLIANCE);

                    //PopupManager.I.ShowPanel<UIAllianceWel_k1>();
                }
                else
                {
                    if (WorldSwitchMgr.I.ShowWorldType == WorldTypeEnum.CITY)
                    {
                        //UIMain.I.TrySwitch(MainMenuConst.WORLD);
                        EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.WORLD);
                    }

                    EventMgr.FireEvent(TEventType.UIMain_Update_Start_DispatchTroop, new MarchArgs()
                    {
                        targetId = entityId,
                        act = MarchAct.MarchActRally,
                        isCampAfterHunt = LMapEntityPlayerTroop.IsCampAfterHunt,
                        rallyTime = time
                    });
                    //UICreateTroop.Start(MarchUtils.GetMarchLineLength(cityEntity, targetEntity), MarchAct.MarchActRally,
                    //    _data.entityId, OnTroopCreated);
                }
            }
        }

        /// <summary>
        /// 点击普通宝箱按钮
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        private void OnClickLeftReawrdBtn(GameObject arg0, PointerEventData arg1)
        {
            UIBubbleTipsMgr.I.ShowNewTip(leftReawrdBtn.transform.position,
                string.Empty, LocalizationMgr.Get("Rally_Wooden_Chest_Tips"));
        }

        /// <summary>
        /// 点击银宝箱按钮
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        private void OnClickRightRewardBtn(GameObject arg0, PointerEventData arg1)
        {
            UIBubbleTipsMgr.I.ShowNewTip(rightRewardBtn.transform.position,
                string.Empty, LocalizationMgr.Get("Rally_Silver_Chest_Tips"));
        }
        /// <summary>
        /// 停止计时
        /// </summary>
        private void StopTimer()
        {
            if (timer != null)
            {
                timer.Stop();
                timer = null;
            }
        }

        protected override void OnDestroyed()
        {
            base.OnDestroyed();
            //if (uiTimeSelect != null)
            //{
            //    uiTimeSelect.Dispose();
            //    uiTimeSelect = null;
            //}

            timer?.Stop();
        }

        protected override void RegisterEvent()
        {
            base.RegisterEvent();

            EventMgr.RegisterEvent(TEventType.AutoRallyInfoRefresh, (pars) =>
            {
                ShowAutoRally();
            }, this);
        }

        protected override void OnHidden()
        {
            if (resObjs.Count > 0)
            {
                for (int i = 0; i < resObjs.Count; i++)
                {
                    GameObject.DestroyImmediate(resObjs[i]);
                }
            }
            resObjs.Clear();
            EventMgr.UnregisterEvent(this);
        }

        /// <summary>
        /// 时间刷新
        /// </summary>
        private void RefreshTime()
        {
            //应该计时器每秒刷新
            var remeingTime = uidata.endTime - GameTime.Time;
            if (lifeTime != null)
            {
                lifeTime.text = UIHelper.GetFormatTime(remeingTime);
            }
            if (remeingTime <= 0)
            {
                StopTimer();
                OnClose(null, null);
            }
        }

        /// <summary>
        /// npc战力
        /// </summary>
        private long npcPower;

        /// <summary>
        /// 刷新npc军队信息显示
        /// </summary>
        private void UpdateNpcTroopPower(EntityInfo entityInfo)
        {
            //战力显示刷新
            if (_powerText != null
                && entityInfo != null
                && entityInfo.NpcTroop != null)
            {
                var coinNum = 0;
                var stoneNum = 0;
                var woodNum = 0;
                var SteelNum = 0;
                var config = CNpcTroopPowerExtension.GetNpcTroopPower(entityInfo.NpcTroop.npcTroopID, entityInfo.NpcTroop.GetLevel());
                if (config != null)
                {
                    //得到金币数量
                    coinNum = GetRallyBattleCoinByLevel(entityInfo.NpcTroop.GetLevel(), config.RallyDropRate);
                    //石头获得数量
                    stoneNum = GetRallyBattleStoneByLevel(config.RallyDropRate);
                    //木头获得数量
                    woodNum = GetRallyBattleWoodByLevel(config.RallyDropRate);
                    //铁矿获得数量
                    SteelNum = GetRallyBattleSteelByLevel(config.RallyDropRate);
                    npcPower = config.Power;
                    _powerText.text = UIStringUtils.FormatIntUnitByLanguage(config.Power / MetaConfig.MapTroopHeroPowerParam * config.SoldierNum);

                    if (leftReawrdText)
                    {
                        if (MetaConfig.RallyBoxDisplay != null)
                        {
                            var index = config.Level - 1;
                            if (index < 0)
                            {
                                index = 0;
                            }
                            if (index >= MetaConfig.RallyBoxDisplay.Length)
                            {
                                index = MetaConfig.RallyBoxDisplay.Length - 1;
                            }
                            leftReawrdText.text = LocalizationMgr.Format("RallyBoxTips1", MetaConfig.RallyBoxDisplay[index]);
                        }
                    }

                }
                else
                {
                    _powerText.text = null;
                }
                #region 显示奖励相关信息
                if(coinNum > 0)
                {
                    OnInitRewardView(RallyRewardType.Coin, coinNum);
                }
                if(stoneNum > 0)
                {
                    OnInitRewardView(RallyRewardType.Stone, stoneNum);
                }
                if (woodNum > 0)
                {
                    OnInitRewardView(RallyRewardType.Wood, woodNum);
                }
                if (SteelNum > 0)
                {
                    OnInitRewardView(RallyRewardType.Iron, SteelNum);
                }
                #endregion
            }
        }


        /// <summary>
        /// 添加资源奖励信息
        /// </summary>
        /// <returns></returns>
        private void OnInitRewardView(RallyRewardType _type,int _number)
        {
            if (ResItem != null)
            {
                var obj = GameObject.Instantiate(ResItem);
                resObjs.Add(obj);
                RewardResWidget widget = new RewardResWidget(obj);
                widget.SetData(_type, _number, ResItem.transform.parent);
            }
        }


        /// <summary>
        /// 关闭
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        private void OnClose(GameObject arg0, PointerEventData arg1)
        {
            if (resObjs.Count > 0)
            {
                for (int i = 0; i < resObjs.Count; i++)
                {
                    GameObject.DestroyImmediate(resObjs[i]);
                }
            }
            resObjs.Clear();
            Close();
        }

        /// <summary>
        /// 收藏
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        private void OnClickFavorite(GameObject arg0, PointerEventData arg1)
        {
            if (uidata != null)
            {
                var entityId = uidata.entityId;
                var entityInfo = LMapEntityManager.I.GetEntityInfo(entityId);
                if (entityInfo != null)
                {
                    var pos = Formula.WorldToSharePosition(entityInfo.CurrentPosition);
                    var x = pos.x;
                    var z = pos.z;
                    var name = uidata.config == null ? "" : uidata.config.GetName();
                    UIFavoritesTipsPanel.Show(x, z, name);
                }
            }
        }


        /// <summary>
        /// 详情
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        private void OnClickInfo(GameObject arg0, PointerEventData arg1)
        {

            PopupManager.I.ShowDialog<UIActivityRules>(
               new UIActivityRuleData()
               {
                   title = LocalizationMgr.Get("LevelRank_Text02"),
                   rules = LocalizationMgr.Get(CD2NpcTroopClass.I(uidata.config.Id).LcDesc.Txt)
               });
        }

        /// <summary>
        /// 点击分享
        /// </summary>
        private void OnClickShare(GameObject go, PointerEventData data)
        {
            if (!LPlayer.I.IsPlayerInUnion())//没有加入联盟
            {
                OnClose(null,null);
                //WndMgr.Show<UIChat>("UIChat", new UIChatArgs() { openTab = ChatTabs.Alliance });
                //UIMain.I?.TrySwitch(MainMenuConst.ALLIANCE);
                //UIMain.I.TrySwitch(MainMenuConst.ALLIANCE, new UIChatData() { CurrentTabKey = UIMain.I.GetMainUICurrState(), CurrChatTab = ChatTabs.Alliance });

                EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.WORLD,
                    new UIChatData() { CurrentTabKey = GameData.I.MainData.CurrMenuType, CurrChatTab = ChatTabs.Alliance });
            }
            else
            {
                SendShareCoordinateToUnionChannel();
            }
        }

        /// <summary>
        /// 发送坐标分享消息到联盟频道
        /// </summary>
        private void SendShareCoordinateToUnionChannel()
        {
            var entityId = uidata.entityId;
            var entityInfo = LMapEntityManager.I.GetEntityInfo(entityId);
            var pos = Formula.WorldToSharePosition(entityInfo.CurrentPosition);
            var x = pos.x;
            var z = pos.z;

            int _entityLevel = entityInfo.property.npcTroop.level;
            int _realLevel = (FightManager.I.TrainLevel/*GameLevelManager.I.Soldier.Opensheet.TrainLevel*/ + _entityLevel) > 0 ? (FightManager.I.TrainLevel/*GameLevelManager.I.Soldier.Opensheet.TrainLevel*/ + _entityLevel) : 1;
            int _level = entityInfo.property.npcTroop.dynamic == true ? _realLevel : _entityLevel;

            var config = uidata.config;
            if (config == null)
            {
                Debug.Log("config is null!");
                return;
            }

            //分享{0}级{1}
            var unitName = string.Format(LocalizationMgr.GetUIString("Share_chatdes_1"), _level, config.GetName());
            PopupManager.I.ShowPanel<UIShare>(new UIShareData()
            {
                chatContent = UIChatHelper.I.GetShareCoordinateToAllianceChannel
               (
                  config.LcName.Txt, _level,
                  ShareDataTypeEnum.Rally,
                  unitName,
                  x,
                  z
               )
            });

        }

        #region 各种资源获得计算
        /// <summary>
        /// 获取当前集结战斗胜利后获得的金币数量
        /// </summary>
        /// <param name="Monsterlevel">怪物等级</param>
        /// <param name="ratio">掉落系数</param>
        /// <returns>
        /// 公式： 集结野怪金币产量 = 普通野怪金币产量*参与/发起掉落系数*集结野怪系数；
        ///       普通野怪金币产量 = MIN【金币最大值，金币初始值+（怪物等级-1）*金币增加值】；NpcTroopReward【0】= 金币初始值，NpcTroopReward【1】=金币增加值，NpcTroopReward【2】=金币最大值。
        ///       参与/发起掉落系数 = 发起：RallyInitiateGoldRate 、 参与：RallyJoinAPGoldRate
        ///       集结野怪系数： var config = CNpcTroopPower.GetNpcTroopPower(entityInfo.NpcTroop.npcTroopID, entityInfo.NpcTroop.Level);
        ///       查询配表中 RallyDropRate字段
        /// </returns>
        private int GetRallyBattleCoinByLevel(int Monsterlevel, float ratio)
        {
            //计算普通怪金币产量
            var NormalMonsterNum = Mathf.Min(MetaConfig.NpcTroopReward[2], MetaConfig.NpcTroopReward[0] + (Monsterlevel - 1) * MetaConfig.NpcTroopReward[1]);
            var coefficient = 0.0f;
            coefficient = MetaConfig.RallyInitiateGoldRate * ratio;
            ////检查是参与者还是发起者(都先按发起者算)
            //if (CheckExsitRally(uidata.entityId))
            //{
            //    //参与者
            //    coefficient = MetaConfig.RallyJoinAPGoldRate * ratio;
            //}
            //else
            //{
            //    //发起者
            //    coefficient = MetaConfig.RallyInitiateGoldRate * ratio;
            //}
            var num = NormalMonsterNum * coefficient;
            return (int)num;
        }

        /// <summary>
        /// 获取当前集结战斗胜利后获得的石头数量
        /// </summary>
        /// <param name="ratio">掉落系数</param>
        /// <returns>
        /// 公式： 集结野怪石头产量 = （集结石头基数+1）*参与/发起掉落系数*集结野怪系数；
        ///       集结石头基数 = INT【（玩家产出系数*10）^0.6+玩家产出系数*2】：：：：玩家产出系数=INT(玩家产出等级/10)：：：玩家产出等级=MIN(玩家等级，999)
        ///       参与/发起掉落系数 = 发起：RallyInitiateStoneRate 、 参与：RallyJoinAPStoneRate
        ///       集结野怪系数： var config = CNpcTroopPower.GetNpcTroopPower(entityInfo.NpcTroop.npcTroopID, entityInfo.NpcTroop.Level);
        ///       查询配表中 RallyDropRate字段
        /// </returns>
        private int GetRallyBattleStoneByLevel(float ratio)
        {
            //玩家产出等级
            var productionLevel = Mathf.Min(LPlayer.I.GetMainCityLevel(), MetaConfig.RallyStoneRateSeason1);
            //玩家产出系数
            var productionRatio = (int)(productionLevel / 10);
            //集结石头基数
            var BaseNum = (int)(Mathf.Pow(productionRatio * 10, 0.6f) + productionRatio * 2);
            var coefficient = 0.0f;
            coefficient = MetaConfig.RallyInitiateStoneRate * ratio;
            ////检查是参与者还是发起者
            //if (CheckExsitRally(uidata.entityId))
            //{
            //    //参与者
            //    coefficient = MetaConfig.RallyJoinAPStoneRate * ratio;
            //}
            //else
            //{
            //    //发起者
            //    coefficient = MetaConfig.RallyInitiateStoneRate * ratio;
            //}
            //集结石头产量
            //Debug.LogError(BaseNum + "==================" + coefficient);
            var num = (BaseNum + 1) * coefficient;
            return (int)num;
        }


        /// <summary>
        /// 获取当前集结战斗胜利后获得的木头数量
        /// </summary>
        /// <param name="ratio">掉落系数</param>
        /// <returns>
        /// 公式： 集结野怪木头产量 = （集结木头基数+1）*参与/发起掉落系数*集结野怪系数；
        ///       集结木头基数 = INT【（玩家产出系数*10）^0.6+玩家产出系数*2】：：：：玩家产出系数=INT(玩家产出等级/10)：：：玩家产出等级=MIN(玩家等级，999)
        ///       参与/发起掉落系数 = 发起：RallyInitiateWoodRate 、 参与：RallyJoinAPWoodRate
        ///       集结野怪系数： var config = CNpcTroopPower.GetNpcTroopPower(entityInfo.NpcTroop.npcTroopID, entityInfo.NpcTroop.Level);
        ///       查询配表中 RallyDropRate字段
        /// </returns>
        private int GetRallyBattleWoodByLevel(float ratio)
        {
            //玩家产出等级
            int level = LPlayer.I.GetMainCityLevel() - (int)MetaConfig.RallyStoneSubRateSeason2;
            var productionLevel = 0;
            if(level > 0)
                productionLevel = Mathf.Min(level, (int)MetaConfig.RallyStoneRateSeason2);
            else
                return 0;
            //玩家产出系数
            var productionRatio = (int)(productionLevel / 10);
            //集结木头基数
            var BaseNum = (int)(Mathf.Pow(productionRatio * 10, 0.6f) + productionRatio * 2);
            var coefficient = 0.0f;
            coefficient = MetaConfig.RallyInitiateWoodRate * ratio;
            ////检查是参与者还是发起者
            //if (CheckExsitRally(uidata.entityId))
            //{
            //    //参与者
            //    coefficient = MetaConfig.RallyJoinAPWoodRate * ratio;
            //}
            //else
            //{
            //    //发起者
            //    coefficient = MetaConfig.RallyInitiateWoodRate * ratio;
            //}
            //集结木头产量
            var num = (BaseNum + 1) * coefficient;
            return (int)num;
        }

        /// <summary>
        /// 获取当前集结战斗胜利后获得的铁矿数量
        /// </summary>
        /// <param name="ratio">掉落系数</param>
        /// <returns>
        /// 公式： 集结野怪铁矿产量 = （集结铁矿基数+1）*参与/发起掉落系数*集结野怪系数；
        ///       集结铁矿基数 = INT【（玩家产出系数*10）^0.6+玩家产出系数*2】：：：：玩家产出系数=INT(玩家产出等级/10)：：：玩家产出等级=MIN(玩家等级，999)
        ///       参与/发起掉落系数 = 发起：RallyInitiateSteelRate 、 参与：RallyJoinAPSteelRate
        ///       集结野怪系数： var config = CNpcTroopPower.GetNpcTroopPower(entityInfo.NpcTroop.npcTroopID, entityInfo.NpcTroop.Level);
        ///       查询配表中 RallyDropRate字段
        /// </returns>
        private int GetRallyBattleSteelByLevel(float ratio)
        {
            //玩家产出等级
            int level = LPlayer.I.GetMainCityLevel() - (int)MetaConfig.RallyStoneSubRateSeason3;
            var productionLevel = 0;
            if (level > 0)
                productionLevel = Mathf.Min(level, (int)MetaConfig.RallyStoneRateSeason3);
            else
                return 0;
            //玩家产出系数
            var productionRatio = (int)(productionLevel / 10);
            //集结铁矿基数
            var BaseNum = (int)(Mathf.Pow(productionRatio * 10, 0.6f) + productionRatio * 2);
            var coefficient = 0.0f;
            coefficient = MetaConfig.RallyInitiateSteelRate * ratio;
            ////检查是参与者还是发起者
            //if (CheckExsitRally(uidata.entityId))
            //{
            //    //参与者
            //    coefficient = MetaConfig.RallyJoinAPSteelRate * ratio;
            //}
            //else
            //{
            //    //发起者
            //    coefficient = MetaConfig.RallyInitiateSteelRate * ratio;
            //}
            //集结铁矿产量
            var num = (BaseNum + 1) * coefficient;
            return (int)num;
        }
        #endregion
    }

    #region 奖励控件
    public class RewardResWidget : UIWidgetBase
    {
        #region 控件
        //奖励数量：需要加"×"号
        private TFWText t_ResNumber;
        //资源图片
        private TFWImage m_ResIcon;
        #endregion
        /// <summary>
        /// 构造
        /// </summary>
        /// <param name="root"></param>
        public RewardResWidget(GameObject root) : base(root) { }

        /// <summary>
        /// 初始化
        /// </summary>
        public override void OnInit()
        {
            base.OnInit();
            t_ResNumber = UIHelper.GetComponent<TFWText>(rootObj, "Text");
            m_ResIcon = UIHelper.GetComponent<TFWImage>(rootObj, "Text/Icon");
        }

        /// <summary>
        /// 设置数据
        /// </summary>
        /// <param name="data"></param>
        /// <param name="isFree"></param>
        public void SetData(RallyRewardType _ResType, int _Number,Transform parent)
        {
            rootObj?.SetActive(true);
            if (rootObj)
            {
                if (t_ResNumber)
                    t_ResNumber.text = $"×{_Number.ToString()}";
                if (m_ResIcon)
                {
                    switch (_ResType)
                    {
                        case RallyRewardType.Coin:
                            UITools.SetImageBySpriteName(m_ResIcon, "UI_Item_Props_coin2");
                            break;
                        case RallyRewardType.Stone:
                            UITools.SetImageBySpriteName(m_ResIcon, "UI_Item_icon_ore");
                            break;
                        case RallyRewardType.Wood:
                            UITools.SetImageBySpriteName(m_ResIcon, "UI_Item_icon_wood");
                            break;
                        case RallyRewardType.Iron:
                            UITools.SetImageBySpriteName(m_ResIcon, "UI_Item_icon_iron");
                            break;
                    }
                }
                rootObj.transform.parent = parent;
                rootObj.transform.localScale = Vector3.one;
                rootObj.transform.localPosition = Vector3.zero;
            }
        }
    }

    /// <summary>
    /// 奖励类型（金币，石头，木材，铁矿）
    /// </summary>
    public enum RallyRewardType
    {
        Coin = 0,
        Stone = 1,
        Wood = 2,
        Iron = 3,
    }
    #endregion
}
//MODIFIED BY UIBASE2POPUP
//MODIFIED BY UIBASE2POPUP
//MODIFIED BY UIBASE2POPUP
//MODIFIED BY UIBASE2POPUP
