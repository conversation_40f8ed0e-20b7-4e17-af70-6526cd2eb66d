{"skeleton": {"hash": "FQDUE7fGGoY", "spine": "4.2.33", "x": -233.84, "y": -91.5, "width": 437.42, "height": 1755, "images": "./images/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "ALL", "parent": "root", "length": 100, "x": 252.19, "y": 1077.77}, {"name": "ALL2", "parent": "ALL", "x": -201.1, "y": -8.89, "icon": "arrows"}, {"name": "tun", "parent": "ALL2", "length": 162.21, "rotation": -114.46, "x": -2.31, "y": -6.73}, {"name": "leg_R", "parent": "tun", "x": 26.42, "y": 57.48}, {"name": "leg_L", "parent": "tun", "x": 97.04, "y": -91.14}, {"name": "body", "parent": "ALL2", "length": 71.34, "rotation": 87.02, "x": -0.09, "y": 9.64}, {"name": "body2", "parent": "body", "length": 260.77, "rotation": 109.62, "x": 71.34, "inherit": "onlyTranslation"}, {"name": "neck", "parent": "body2", "length": 74.39, "rotation": 67.53, "x": 260.77, "inherit": "onlyTranslation"}, {"name": "head", "parent": "neck", "length": 163.2, "rotation": -0.1, "x": 74.39}, {"name": "eyebrow_R", "parent": "head", "length": 25.64, "rotation": -58.78, "x": 77.95, "y": 9.63}, {"name": "eyebrow_R2", "parent": "eyebrow_R", "length": 19.92, "rotation": -52.44, "x": 25.64}, {"name": "eyebrow_R3", "parent": "eyebrow_R2", "length": 12.79, "rotation": -2.15, "x": 19.92}, {"name": "eyebrow_L", "parent": "head", "length": 11.09, "rotation": 99.08, "x": 79.19, "y": -83.79}, {"name": "eyebrow_L2", "parent": "eyebrow_L", "length": 9.21, "rotation": 16.18, "x": 11.09}, {"name": "eye_R", "parent": "head", "x": 61.44, "y": -12.49}, {"name": "eye_L", "parent": "head", "x": 55.67, "y": -64.01}, {"name": "hair_F", "parent": "head", "x": 55.2, "y": 63.55}, {"name": "hair_F2", "parent": "hair_F", "length": 29.86, "rotation": -91.64, "x": -5.26, "y": -0.34, "inherit": "noRotationOrReflection"}, {"name": "hair_F3", "parent": "hair_F2", "length": 32.79, "rotation": 32.54, "x": 29.86, "color": "abe323ff"}, {"name": "hair_F4", "parent": "hair_F3", "length": 25.75, "rotation": -13.06, "x": 32.79, "color": "abe323ff"}, {"name": "hair_F5", "parent": "hair_F4", "length": 24.67, "rotation": -27.79, "x": 25.75, "color": "abe323ff"}, {"name": "hair_F6", "parent": "hair_F5", "length": 23.58, "rotation": -42.77, "x": 24.67, "color": "abe323ff"}, {"name": "hair_F7", "parent": "hair_F6", "length": 22.57, "rotation": -37.28, "x": 23.58, "color": "abe323ff"}, {"name": "hair_F8", "parent": "hair_F7", "length": 18, "rotation": 5.53, "x": 22.57, "color": "abe323ff"}, {"name": "leg_R2", "parent": "leg_R", "length": 384.76, "rotation": 18.2, "x": 98.4, "y": 53.11}, {"name": "leg_R3", "parent": "leg_R2", "length": 446.99, "rotation": -4.97, "x": 384.76, "inherit": "noScale"}, {"name": "leg_R4", "parent": "leg_R3", "length": 163.36, "rotation": -81.79, "x": 446.99, "inherit": "onlyTranslation"}, {"name": "leg_L2", "parent": "leg_L", "length": 347.41, "rotation": 34.23, "x": 115.57, "y": 69.1}, {"name": "leg_L3", "parent": "leg_L2", "length": 351.79, "rotation": -12.26, "x": 347.41, "inherit": "noScale"}, {"name": "leg_L4", "parent": "leg_L3", "length": 153.75, "rotation": -74.85, "x": 351.79, "inherit": "onlyTranslation"}, {"name": "tun_R", "parent": "tun", "rotation": 114.46, "x": 128.79, "y": 18.98}, {"name": "tun_R2", "parent": "tun_R", "x": -17.65, "y": -6.17}, {"name": "tun_R3", "parent": "tun_R2", "x": -15.08, "y": -4.63}, {"name": "tun_L", "parent": "tun", "rotation": 114.46, "x": 197.72, "y": -69.49}, {"name": "tun_L2", "parent": "tun_L", "x": -38.24, "y": -5.61}, {"name": "tun_L3", "parent": "tun_L2", "x": -32.22, "y": -4.25}, {"name": "sh_R", "parent": "body2", "x": 221.85, "y": 19.26, "inherit": "noScale"}, {"name": "arm_R", "parent": "sh_R", "length": 253.74, "rotation": 162.3, "x": -4.1, "y": 5.49}, {"name": "arm_R2", "parent": "arm_R", "length": 231.25, "rotation": 57.31, "x": 253.74, "inherit": "noScale"}, {"name": "arm_R3", "parent": "arm_R2", "length": 79.34, "rotation": -74.75, "x": 231.25, "inherit": "onlyTranslation"}, {"name": "arm_R4", "parent": "arm_R3", "length": 36.35, "rotation": -14.04, "x": 79.34}, {"name": "arm_R5", "parent": "arm_R4", "length": 41.53, "rotation": -30.17, "x": 36.35}, {"name": "arm_R6", "parent": "arm_R3", "length": 53.47, "rotation": -10.13, "x": 68.9, "y": -26.03}, {"name": "sh_L", "parent": "body2", "x": 229.9, "y": 43.23, "inherit": "noScale"}, {"name": "arm_L", "parent": "sh_L", "length": 222.87, "rotation": 150.95, "x": -8.83, "y": 6.31}, {"name": "arm_L2", "parent": "arm_L", "length": 232.81, "rotation": 70.73, "x": 222.87}, {"name": "hair_B", "parent": "body2", "length": 61.78, "rotation": -107.06, "x": 293.98, "y": 84.5, "inherit": "onlyTranslation"}, {"name": "hair_B2", "parent": "hair_B", "length": 56.63, "rotation": 11.62, "x": 61.78, "color": "abe323ff"}, {"name": "hair_B3", "parent": "hair_B2", "length": 53.12, "rotation": 1.82, "x": 56.63, "color": "abe323ff"}, {"name": "hair_B4", "parent": "hair_B3", "length": 53.23, "rotation": -1.44, "x": 53.13, "color": "abe323ff"}, {"name": "hair_B5", "parent": "hair_B4", "length": 48.12, "rotation": -2.95, "x": 53.23, "color": "abe323ff"}, {"name": "hair_B6", "parent": "hair_B5", "length": 43.67, "rotation": 5.37, "x": 48.12, "color": "abe323ff"}, {"name": "hair_B7", "parent": "hair_B6", "length": 37.73, "rotation": -2.46, "x": 43.67, "color": "abe323ff"}, {"name": "RU", "parent": "body2", "length": 50, "x": 68.66, "y": -74.95}, {"name": "RU2", "parent": "RU", "length": 50, "x": -24.76, "y": -34.96}, {"name": "RU3", "parent": "RU2", "length": 50, "x": -7.02, "y": -21.78}, {"name": "hair_L", "parent": "head", "length": 49, "rotation": -145.7, "x": 143.02, "y": -70.87}, {"name": "hair_L2", "parent": "hair_L", "length": 52.94, "rotation": -19.09, "x": 49, "color": "abe323ff"}, {"name": "hair_L3", "parent": "hair_L2", "length": 42.84, "rotation": -31.2, "x": 52.94, "color": "abe323ff"}, {"name": "hair_L4", "parent": "hair_L3", "length": 39.59, "rotation": 32.66, "x": 42.84, "color": "abe323ff"}, {"name": "hair_L5", "parent": "hair_L4", "length": 35.16, "rotation": 40.41, "x": 40.04, "y": 0.05, "color": "abe323ff"}, {"name": "hair_L6", "parent": "hair_L5", "length": 23.24, "rotation": -41.22, "x": 35.15, "color": "abe323ff"}, {"name": "hair_L7", "parent": "hair_L6", "length": 25.97, "rotation": -60.75, "x": 23.24, "color": "abe323ff"}, {"name": "hair_L8", "parent": "hair_L7", "length": 19.94, "rotation": -62.02, "x": 25.97, "color": "abe323ff"}, {"name": "hair_L9", "parent": "hair_L8", "length": 16.85, "rotation": -81.23, "x": 19.46, "y": -1.56, "color": "abe323ff"}, {"name": "RU4", "parent": "RU3", "length": 50, "x": -4.76, "y": -13.93}, {"name": "headround3", "parent": "head", "x": 366.58, "y": -0.62, "color": "abe323ff", "icon": "arrowLeftRight"}, {"name": "headround", "parent": "headround3", "y": -71.97, "color": "abe323ff", "icon": "arrowUpDown"}, {"name": "headround2", "parent": "head", "x": 301.23, "y": -72.58, "icon": "warning"}, {"name": "bodyround", "parent": "body2", "x": 30.32, "y": -327.79, "color": "abe323ff", "icon": "arrowsB"}, {"name": "bodyround2", "parent": "body2", "x": -45.19, "y": -327.79, "icon": "warning"}, {"name": "tunround", "parent": "ALL2", "x": 326.96, "y": -157.13, "color": "abe323ff", "icon": "arrowsB"}, {"name": "tunround2", "parent": "ALL2", "x": 326.96, "y": -235.43, "icon": "warning"}, {"name": "leg_R5", "parent": "leg_R", "length": 832.34, "rotation": 15.51, "x": 98.37, "y": 53.12}, {"name": "leg_R6", "parent": "root", "x": -31.29, "y": 81.88, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_R1", "parent": "leg_R5", "rotation": 98.94, "x": 385, "y": 17.92, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_L5", "parent": "leg_L", "length": 694.7, "rotation": 28.18, "x": 114.58, "y": 68.48}, {"name": "leg_L6", "parent": "root", "x": -15.66, "y": 183.94, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_L1", "parent": "leg_L5", "rotation": 86.39, "x": 345.74, "y": 37.39, "color": "ff3f00ff", "icon": "ik"}, {"name": "sh_R2", "parent": "sh_R", "length": 278.62, "rotation": 155.85, "x": -3.22, "y": 6.04}, {"name": "sh_R3", "parent": "sh_R2", "length": 248.22, "rotation": 72.05, "x": 278.62, "color": "abe323ff"}, {"name": "arm_R7", "parent": "tun", "rotation": 114.46, "x": 31.31, "y": 142.42, "color": "ff3f00ff", "icon": "ik"}, {"name": "arm_R1", "parent": "sh_R", "rotation": -109.62, "x": -245.83, "y": 82.64, "color": "ff3f00ff", "icon": "ik"}, {"name": "sh_L2", "parent": "sh_L", "length": 243.92, "rotation": 143.38, "x": -7.98, "y": 6.69}, {"name": "sh_L3", "parent": "sh_L2", "length": 259.06, "rotation": 84.44, "x": 243.92, "color": "abe323ff"}, {"name": "arm_L3", "parent": "tun", "rotation": 114.46, "x": 25.91, "y": 69.7, "color": "ff3f00ff", "icon": "ik"}, {"name": "arm_L1", "parent": "sh_L", "rotation": -109.62, "x": -203.66, "y": 114.54, "color": "ff3f00ff", "icon": "ik"}], "slots": [{"name": "hand_R", "bone": "root", "attachment": "hand_R"}, {"name": "arm_L", "bone": "root", "attachment": "arm_L"}, {"name": "hat", "bone": "root", "attachment": "hat"}, {"name": "leg_L", "bone": "root", "attachment": "leg_L"}, {"name": "body", "bone": "root", "attachment": "body"}, {"name": "arm_R", "bone": "root", "attachment": "arm_R"}, {"name": "eyewhite_L", "bone": "root", "attachment": "eyewhite_L"}, {"name": "eyewhite_R", "bone": "root", "attachment": "eyewhite_R"}, {"name": "eye_L", "bone": "root", "attachment": "eye_L"}, {"name": "eye_R", "bone": "root", "attachment": "eye_R"}, {"name": "head_B", "bone": "root", "attachment": "head_B"}, {"name": "eye_LL", "bone": "root", "attachment": "eye_LL"}, {"name": "head", "bone": "root", "attachment": "head"}, {"name": "head2", "bone": "root", "attachment": "head"}, {"name": "eye_RR", "bone": "root", "attachment": "eye_RR"}, {"name": "hair_F", "bone": "root", "attachment": "hair_F"}], "ik": [{"name": "arm_L", "order": 2, "bones": ["sh_L2", "sh_L3"], "target": "arm_L3"}, {"name": "arm_L1", "order": 4, "bones": ["arm_L"], "target": "arm_L1", "compress": true, "stretch": true}, {"name": "arm_L2", "order": 5, "bones": ["arm_L2"], "target": "arm_L3", "compress": true, "stretch": true}, {"name": "arm_R", "order": 8, "bones": ["sh_R2", "sh_R3"], "target": "arm_R7"}, {"name": "arm_R1", "order": 10, "bones": ["arm_R"], "target": "arm_R1", "compress": true, "stretch": true}, {"name": "arm_R2", "order": 11, "bones": ["arm_R2"], "target": "arm_R7", "compress": true, "stretch": true}, {"name": "leg_L", "order": 13, "bones": ["leg_L5"], "target": "leg_L6", "compress": true, "stretch": true}, {"name": "leg_L1", "order": 14, "bones": ["leg_L2"], "target": "leg_L1", "compress": true, "stretch": true}, {"name": "leg_L2", "order": 15, "bones": ["leg_L3"], "target": "leg_L6", "compress": true, "stretch": true}, {"name": "leg_R", "order": 17, "bones": ["leg_R5"], "target": "leg_R6", "compress": true, "stretch": true}, {"name": "leg_R1", "order": 18, "bones": ["leg_R2"], "target": "leg_R1", "compress": true, "stretch": true}, {"name": "leg_R2", "order": 19, "bones": ["leg_R3"], "target": "leg_R6", "compress": true, "stretch": true}], "transform": [{"name": "arm_L3", "order": 3, "bones": ["arm_L1"], "target": "sh_L3", "rotation": 22.46, "x": 27.63, "y": 25.28, "mixRotate": 0.8, "mixX": 0.8, "mixScaleX": 0.8, "mixShearY": 0.8}, {"name": "arm_R3", "order": 9, "bones": ["arm_R1"], "target": "sh_R3", "rotation": 22.43, "x": 19.44, "y": 33.51, "mixRotate": 0.8, "mixX": 0.8, "mixScaleX": 0.8, "mixShearY": 0.8}, {"name": "bodyround", "order": 20, "bones": ["bodyround2"], "target": "bodyround", "x": -75.51, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "bodyround1", "order": 7, "bones": ["sh_R"], "target": "bodyround", "x": 191.52, "y": 347.04, "mixRotate": 0, "mixX": 0.04, "mixScaleX": 0, "mixShearY": 0}, {"name": "bodyround2", "order": 1, "bones": ["sh_L"], "target": "bodyround", "x": 199.58, "y": 371.02, "mixRotate": 0, "mixX": -0.02, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround", "order": 21, "bones": ["headround2"], "target": "headround", "x": -65.35, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround1", "order": 22, "bones": ["eyebrow_R"], "target": "headround", "rotation": -58.78, "x": -288.63, "y": 82.22, "mixRotate": 0, "mixX": 0.04, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround2", "order": 23, "bones": ["eyebrow_L"], "target": "headround", "rotation": 99.08, "x": -287.39, "y": -11.21, "mixRotate": 0, "mixX": 0.02, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround3", "order": 24, "bones": ["hair_F"], "target": "headround", "x": -311.38, "y": 136.14, "mixRotate": 0, "mixX": 0.02, "mixScaleX": 0, "mixShearY": 0}, {"name": "tunround", "order": 25, "bones": ["tunround2"], "target": "tunround", "y": -78.3, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "tunround1", "order": 16, "bones": ["leg_R"], "target": "tunround", "rotation": -114.46, "x": -287.89, "y": 102.55, "mixRotate": 0, "mixX": 0.03, "mixScaleX": 0, "mixShearY": 0}, {"name": "tunround2", "order": 12, "bones": ["leg_L"], "target": "tunround", "rotation": -114.46, "x": -452.41, "y": 99.81, "mixRotate": 0, "mixX": -0.02, "mixScaleX": 0, "mixShearY": 0}, {"name": "tunround3", "order": 6, "bones": ["arm_R7"], "target": "tunround", "x": -212.59, "y": 62.93, "mixRotate": 0, "mixX": 0.03, "mixScaleX": 0, "mixShearY": 0}, {"name": "tunround4", "bones": ["arm_L3"], "target": "tunround", "x": -276.55, "y": 97.96, "mixRotate": 0, "mixX": -0.02, "mixScaleX": 0, "mixShearY": 0}], "physics": [{"name": "hair_B2", "order": 26, "bone": "hair_B2", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_B3", "order": 27, "bone": "hair_B3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_B4", "order": 28, "bone": "hair_B4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_B5", "order": 29, "bone": "hair_B5", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_B6", "order": 30, "bone": "hair_B6", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_B7", "order": 31, "bone": "hair_B7", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_F3", "order": 40, "bone": "hair_F3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_F4", "order": 41, "bone": "hair_F4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_F5", "order": 42, "bone": "hair_F5", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_F6", "order": 43, "bone": "hair_F6", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_F7", "order": 44, "bone": "hair_F7", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_F8", "order": 45, "bone": "hair_F8", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_L2", "order": 32, "bone": "hair_L2", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_L3", "order": 33, "bone": "hair_L3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_L4", "order": 34, "bone": "hair_L4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_L5", "order": 35, "bone": "hair_L5", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_L6", "order": 36, "bone": "hair_L6", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_L7", "order": 37, "bone": "hair_L7", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_L8", "order": 38, "bone": "hair_L8", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_L9", "order": 39, "bone": "hair_L9", "rotate": 1, "inertia": 0.5, "damping": 0.85}], "skins": [{"name": "default", "attachments": {"arm_L": {"arm_L": {"type": "mesh", "uvs": [0.09457, 0.23513, 0.14498, 0.07448, 0.1673, 0.00334, 0.28151, 0.01321, 0.45948, 0.02859, 0.56632, 0.03783, 0.53682, 0.11571, 0.49399, 0.27288, 0.44523, 0.46344, 0.40505, 0.53708, 0.37313, 0.57124, 0.43078, 0.59335, 0.49698, 0.62097, 0.59508, 0.66572, 0.83112, 0.77339, 0.99984, 0.85035, 0.91549, 0.9971, 0.7274, 0.96103, 0.48605, 0.90387, 0.28098, 0.83622, 0.16864, 0.78403, 0.07691, 0.73618, 0.00749, 0.69628, 0.00972, 0.64135, 0.02509, 0.5739, 0.0336, 0.50287, 0.05454, 0.39273, 0.12397, 0.65272, 0.25407, 0.59681, 0.29374, 0.5409, 0.33023, 0.4555, 0.38418, 0.27251, 0.42067, 0.10282, 0.25407, 0.09266, 0.216, 0.24921, 0.16046, 0.44948, 0.14142, 0.53691, 0.12715, 0.60502, 0.18012, 0.69307, 0.26897, 0.74492, 0.33878, 0.61988, 0.42287, 0.64936, 0.81, 0.86386, 0.52828, 0.70309, 0.38335, 0.79118], "triangles": [40, 28, 10, 40, 10, 11, 41, 40, 11, 41, 11, 12, 38, 27, 28, 22, 23, 27, 43, 12, 13, 41, 12, 43, 21, 22, 27, 21, 27, 38, 40, 38, 28, 39, 40, 41, 39, 38, 40, 20, 21, 38, 20, 38, 39, 44, 39, 41, 44, 41, 43, 19, 39, 44, 20, 39, 19, 14, 43, 13, 42, 14, 15, 42, 43, 14, 44, 43, 42, 18, 44, 42, 19, 44, 18, 17, 18, 42, 16, 42, 15, 17, 42, 16, 33, 2, 3, 1, 2, 33, 32, 3, 4, 33, 3, 32, 6, 4, 5, 32, 4, 6, 0, 1, 33, 34, 0, 33, 32, 34, 33, 7, 31, 32, 31, 34, 32, 6, 7, 32, 26, 0, 34, 35, 26, 34, 30, 35, 34, 31, 30, 34, 8, 30, 31, 7, 8, 31, 25, 26, 35, 36, 25, 35, 9, 30, 8, 29, 35, 30, 29, 30, 9, 36, 35, 29, 10, 29, 9, 24, 25, 36, 28, 36, 29, 28, 29, 10, 37, 24, 36, 37, 36, 28, 23, 24, 37, 27, 23, 37, 28, 27, 37], "vertices": [1, 45, 116.61, -28.62, 1, 1, 45, 72.39, -27.12, 1, 1, 45, 52.81, -26.46, 1, 1, 45, 52.2, -6.54, 1, 1, 45, 51.25, 24.52, 1, 1, 45, 50.68, 43.16, 1, 1, 45, 72.26, 41.57, 1, 1, 45, 115.34, 41.22, 1, 2, 45, 167.48, 41.32, 0.97377, 46, 20.73, 65.93, 0.02623, 2, 45, 188.23, 37.73, 0.82677, 46, 24.18, 45.15, 0.17323, 2, 45, 198.23, 33.79, 0.55806, 46, 23.77, 34.41, 0.44194, 2, 45, 202.49, 44.61, 0.21932, 46, 35.38, 33.96, 0.78068, 2, 45, 207.97, 57.13, 0.06419, 46, 49.01, 32.92, 0.93581, 2, 45, 217.11, 75.85, 0.00275, 46, 69.7, 30.47, 0.99725, 1, 46, 119.47, 24.58, 1, 1, 46, 155.05, 20.37, 1, 1, 46, 161.28, -21.39, 1, 1, 46, 128.06, -28.48, 1, 1, 46, 84.03, -34.99, 1, 1, 46, 44.14, -36, 1, 1, 46, 20.32, -32.98, 1, 2, 45, 250.56, -9.47, 0.02986, 46, 0.2, -29.26, 0.97014, 2, 45, 241.9, -23.08, 0.20764, 46, -15.51, -25.58, 0.79236, 2, 45, 227.21, -25.13, 0.53464, 46, -22.29, -12.39, 0.46536, 2, 45, 208.81, -25.49, 0.94686, 46, -28.7, 4.86, 0.05314, 1, 45, 189.65, -27.18, 1, 1, 45, 159.72, -28.48, 1, 2, 45, 227, -5.13, 0.32432, 46, -3.48, -5.59, 0.67568, 2, 45, 208.42, 14.6, 0.64336, 46, 9.02, 18.46, 0.35664, 2, 45, 192.4, 18.9, 0.84511, 46, 7.79, 35, 0.15489, 2, 45, 168.62, 21.35, 0.98164, 46, 2.25, 58.25, 0.01836, 1, 45, 118.35, 22.46, 1, 1, 45, 72.12, 21.18, 1, 1, 45, 74.14, -7.7, 1, 1, 45, 116.92, -7.27, 1, 1, 45, 171.83, -7.89, 1, 1, 45, 195.66, -7.27, 1, 2, 45, 214.21, -6.7, 0.97659, 46, -9.18, 5.97, 0.02341, 1, 46, 10.27, -10.48, 1, 1, 46, 30.48, -15.38, 1, 2, 45, 212.16, 30.08, 0.28661, 46, 24.86, 20.03, 0.71339, 2, 45, 217.63, 45.73, 0.08144, 46, 41.44, 20.04, 0.91856, 1, 46, 128, 1.4, 1, 2, 45, 228.95, 66.1, 0.00266, 46, 64.4, 16.07, 0.99734, 1, 46, 53.83, -16.83, 1], "hull": 27, "edges": [10, 12, 16, 18, 18, 20, 30, 32, 34, 36, 36, 38, 42, 44, 20, 22, 22, 24, 44, 46, 46, 48, 38, 40, 40, 42, 32, 34, 24, 26, 26, 28, 28, 30, 12, 14, 14, 16, 4, 2, 2, 0, 0, 52, 48, 50, 50, 52, 56, 58, 58, 60, 60, 62, 62, 64, 8, 10, 64, 8, 4, 6, 6, 8, 6, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 54, 54, 76, 76, 78, 56, 80, 80, 82, 82, 86, 86, 84, 78, 88, 88, 84], "width": 173, "height": 270}}, "arm_R": {"arm_R": {"type": "mesh", "uvs": [0.25344, 0.00524, 0.3077, 0.02772, 0.33514, 0.06611, 0.33622, 0.10436, 0.31952, 0.1425, 0.31115, 0.17696, 0.30964, 0.20966, 0.31425, 0.24519, 0.33435, 0.34918, 0.33999, 0.39189, 0.34164, 0.42939, 0.34328, 0.45108, 0.35849, 0.46413, 0.41579, 0.48434, 0.48057, 0.50931, 0.74881, 0.62909, 0.83436, 0.66309, 0.87022, 0.67689, 0.90527, 0.68871, 0.93868, 0.7036, 0.95183, 0.72149, 0.95933, 0.74322, 0.97172, 0.77744, 0.9882, 0.82297, 0.99827, 0.8505, 0.99492, 0.87981, 0.997, 0.92978, 0.96138, 0.96258, 0.94515, 0.99887, 0.88454, 0.99636, 0.82003, 0.95947, 0.81989, 0.94287, 0.85257, 0.91556, 0.87837, 0.89283, 0.87959, 0.86448, 0.87055, 0.84001, 0.84973, 0.8125, 0.83526, 0.7856, 0.8293, 0.76082, 0.81533, 0.74003, 0.78298, 0.73109, 0.74776, 0.72148, 0.65016, 0.69435, 0.38127, 0.62566, 0.26686, 0.58154, 0.20626, 0.55562, 0.15451, 0.54183, 0.14083, 0.52418, 0.13283, 0.499, 0.11053, 0.45733, 0.08012, 0.37727, 0.05777, 0.29925, 0.05047, 0.25063, 0.04866, 0.2074, 0.04043, 0.18078, 0.01026, 0.14464, 0.00584, 0.10538, 0.01941, 0.06404, 0.06339, 0.02664, 0.11899, 0.00803, 0.18752, 0.00057, 0.18523, 0.04788, 0.11458, 0.07374, 0.0921, 0.12604, 0.1028, 0.16684, 0.12956, 0.20361, 0.2548, 0.07892, 0.25801, 0.12431, 0.25052, 0.16396, 0.23125, 0.19959, 0.12421, 0.24739, 0.14348, 0.34565, 0.1681, 0.43989, 0.17989, 0.48237, 0.19229, 0.51291, 0.2302, 0.53248, 0.30166, 0.55714, 0.36544, 0.50706, 0.31111, 0.48951, 0.28315, 0.4672, 0.2776, 0.43384, 0.26593, 0.33911, 0.24044, 0.24014, 0.40252, 0.5913, 0.45923, 0.54148, 0.69468, 0.65219, 0.03297, 0.12757, 0.04283, 0.08197, 0.07468, 0.04004], "triangles": [27, 30, 31, 26, 32, 33, 27, 32, 26, 27, 31, 32, 29, 30, 27, 28, 29, 27, 25, 34, 24, 33, 34, 25, 33, 25, 26, 39, 20, 38, 19, 39, 18, 20, 39, 19, 38, 20, 21, 38, 21, 22, 37, 38, 22, 36, 37, 22, 36, 22, 23, 35, 36, 23, 35, 23, 24, 34, 35, 24, 77, 12, 13, 78, 12, 77, 75, 74, 78, 14, 77, 13, 84, 77, 14, 46, 74, 75, 45, 46, 75, 76, 75, 78, 76, 78, 77, 45, 75, 76, 44, 45, 76, 84, 76, 77, 83, 76, 84, 83, 44, 76, 43, 44, 83, 84, 14, 15, 85, 84, 15, 83, 84, 85, 42, 43, 83, 85, 42, 83, 16, 41, 85, 16, 85, 15, 42, 85, 41, 40, 41, 16, 40, 16, 17, 39, 40, 17, 39, 17, 18, 55, 86, 54, 88, 58, 59, 62, 88, 61, 87, 57, 88, 62, 87, 88, 67, 66, 3, 63, 87, 62, 63, 86, 87, 4, 67, 3, 62, 66, 67, 66, 62, 61, 68, 67, 4, 67, 63, 62, 68, 63, 67, 64, 63, 68, 5, 68, 4, 64, 86, 63, 64, 54, 86, 69, 64, 68, 69, 68, 5, 65, 64, 69, 53, 54, 64, 65, 53, 64, 6, 69, 5, 82, 69, 6, 82, 6, 7, 70, 53, 65, 52, 53, 70, 51, 52, 70, 81, 82, 7, 65, 82, 70, 82, 65, 69, 71, 82, 81, 71, 70, 82, 51, 70, 71, 81, 7, 8, 50, 51, 71, 9, 80, 81, 9, 81, 8, 80, 9, 10, 72, 71, 81, 72, 50, 71, 81, 80, 72, 80, 10, 11, 49, 50, 72, 79, 80, 11, 72, 80, 79, 73, 72, 79, 49, 72, 73, 12, 78, 79, 12, 79, 11, 48, 49, 73, 74, 73, 79, 74, 79, 78, 48, 73, 74, 47, 48, 74, 46, 47, 74, 86, 56, 87, 55, 56, 86, 56, 57, 87, 57, 58, 88, 61, 59, 60, 61, 60, 0, 88, 59, 61, 1, 61, 0, 66, 1, 2, 66, 61, 1, 66, 2, 3], "vertices": [2, 37, 23, -22.62, 0.98286, 7, 244.85, -3.36, 0.01714, 3, 37, 5.36, -33.96, 0.88057, 38, -21, 34.71, 0.01817, 7, 227.2, -14.7, 0.10127, 3, 37, -18.07, -34.52, 0.56129, 38, 1.15, 42.37, 0.2336, 7, 203.77, -15.26, 0.20511, 3, 37, -38.72, -27.51, 0.26971, 38, 22.95, 41.97, 0.50985, 7, 183.13, -8.25, 0.22044, 3, 37, -57.48, -15.39, 0.08104, 38, 44.51, 36.13, 0.78432, 7, 164.36, 3.86, 0.13464, 3, 37, -75.13, -6.39, 0.01127, 38, 64.05, 32.91, 0.96301, 7, 146.72, 12.87, 0.02571, 2, 37, -92.53, 0.31, 0.00013, 38, 82.66, 31.83, 0.99987, 1, 38, 102.95, 32.56, 1, 1, 38, 162.4, 36.72, 1, 2, 38, 186.79, 37.63, 0.98802, 39, -4.49, 76.67, 0.01198, 2, 38, 208.17, 37.41, 0.88768, 39, 6.88, 58.56, 0.11232, 2, 38, 220.54, 37.5, 0.70779, 39, 13.64, 48.19, 0.29221, 2, 38, 228.13, 41.91, 0.48801, 39, 21.44, 44.18, 0.51199, 2, 38, 240.23, 59.04, 0.14763, 39, 42.4, 43.25, 0.85237, 2, 38, 255.12, 78.38, 0.02053, 39, 66.72, 41.16, 0.97947, 1, 39, 172.19, 24.49, 1, 1, 39, 204.6, 21.22, 1, 2, 39, 218.05, 20.08, 0.97415, 40, -23.44, 5.29, 0.02585, 2, 39, 230.72, 19.77, 0.68738, 40, -14.11, 13.86, 0.31262, 2, 39, 243.84, 17.71, 0.25691, 40, -3.24, 21.49, 0.74309, 2, 39, 252.52, 11.01, 0.05601, 40, 7.66, 22.69, 0.94399, 1, 40, 20.21, 21.65, 1, 1, 40, 40.03, 20.17, 1, 2, 40, 66.39, 18.21, 0.9638, 41, -16.98, 14.53, 0.0362, 2, 40, 82.35, 17.05, 0.47908, 41, -1.22, 17.27, 0.52092, 2, 40, 98.19, 11.67, 0.01385, 41, 15.46, 15.9, 0.98615, 2, 41, 43.95, 15.93, 0.36028, 42, -1.43, 17.59, 0.63972, 1, 42, 20.2, 17.1, 1, 1, 42, 40.71, 22.77, 1, 1, 42, 48.44, 5.85, 1, 2, 41, 59.72, -38.57, 0.0023, 42, 39.59, -21.6, 0.9977, 2, 41, 50.26, -38.41, 0.02034, 42, 31.33, -26.22, 0.97966, 2, 41, 34.91, -28.09, 0.23845, 42, 12.87, -25, 0.76155, 3, 40, 95.97, -24.69, 0.01311, 41, 22.12, -19.92, 0.74477, 42, -2.29, -24.37, 0.24212, 3, 40, 80.48, -20.08, 0.32626, 41, 5.98, -19.2, 0.66206, 42, -16.61, -31.86, 0.01168, 2, 40, 66.3, -19.08, 0.85333, 41, -8.03, -21.67, 0.14667, 2, 40, 49.49, -21.1, 0.99836, 41, -23.84, -27.71, 0.00164, 2, 39, 240.56, -38.64, 0.00762, 40, 33.53, -21.34, 0.99238, 2, 39, 231.77, -27.44, 0.11989, 40, 19.42, -19.38, 0.88011, 2, 39, 222.03, -19.44, 0.55182, 40, 6.87, -20.39, 0.44818, 2, 39, 210.92, -20.12, 0.8916, 40, -0.66, -28.6, 0.1084, 2, 39, 198.85, -20.93, 0.99036, 40, -8.77, -37.55, 0.00964, 1, 39, 165.28, -22.92, 1, 1, 39, 74.53, -31.36, 1, 1, 39, 31.58, -27.67, 1, 2, 38, 278.69, -6.4, 0.07861, 39, 8.09, -24.46, 0.92139, 2, 38, 270.3, -21.96, 0.44754, 39, -9.54, -25.8, 0.55246, 2, 38, 260.11, -25.81, 0.65884, 39, -18.28, -19.3, 0.34116, 2, 38, 245.68, -27.78, 0.92491, 39, -27.73, -8.22, 0.07509, 1, 38, 221.72, -33.8, 1, 1, 38, 175.8, -41.57, 1, 1, 38, 131.12, -46.91, 1, 2, 38, 103.35, -48.22, 0.96571, 7, 133.95, 102.11, 0.03429, 1, 38, 78.7, -47.95, 1, 2, 38, 63.45, -49.95, 0.93143, 7, 172.49, 91.63, 0.06857, 2, 38, 42.55, -58.49, 0.53945, 7, 194.99, 93.41, 0.46055, 3, 37, -5.32, 67.91, 0.00344, 38, 20.14, -59.09, 0.57802, 7, 216.53, 87.17, 0.41854, 3, 37, 15.48, 56.09, 0.05305, 38, -3.27, -54.15, 0.58679, 7, 237.33, 75.35, 0.36016, 3, 37, 31.04, 36.25, 0.24381, 38, -24.13, -39.99, 0.49653, 7, 252.89, 55.51, 0.25966, 3, 37, 35.32, 16.67, 0.54807, 38, -34.16, -22.63, 0.31669, 7, 257.17, 35.92, 0.13524, 2, 37, 32.28, -4.51, 0.93449, 38, -37.7, -1.53, 0.06551, 3, 37, 7.11, 5.2, 0.78132, 38, -10.77, -3.13, 0.2017, 70, 198.64, 352.25, 0.01698, 3, 37, 0.49, 30.52, 0.06721, 38, 3.24, -25.23, 0.91553, 70, 192.01, 377.56, 0.01727, 2, 38, 32.8, -33.11, 0.98288, 70, 166.25, 394.05, 0.01712, 2, 38, 56.15, -30.61, 0.98288, 70, 143.24, 398.77, 0.01712, 2, 38, 77.37, -23.13, 0.98248, 70, 120.75, 398.1, 0.01752, 3, 37, -16.7, -8.91, 0.45347, 38, 7.62, 17.55, 0.52982, 70, 174.83, 338.13, 0.0167, 3, 37, -41.4, -1.15, 0.08662, 38, 33.51, 17.67, 0.89607, 70, 150.12, 345.9, 0.01731, 3, 37, -61.92, 8.6, 0.0096, 38, 56.03, 14.62, 0.97449, 70, 129.61, 355.65, 0.01591, 2, 38, 76.12, 8.05, 0.98381, 70, 112.46, 368.02, 0.01619, 2, 38, 102.26, -25.6, 0.98309, 70, 97.79, 408.02, 0.01691, 2, 38, 158.43, -21.59, 0.98359, 70, 43.06, 421.27, 0.01641, 2, 38, 212.37, -15.86, 0.99305, 70, -10.07, 432.22, 0.00695, 2, 38, 236.69, -13.06, 0.99784, 70, -34.09, 436.95, 0.00216, 2, 38, 254.21, -9.86, 0.69746, 39, -8.04, -5.72, 0.30254, 2, 38, 265.75, 1.36, 0.05269, 39, 7.64, -9.37, 0.94731, 1, 39, 33.62, -10.27, 1, 2, 38, 252.66, 43.21, 0.13963, 39, 35.79, 24.25, 0.86037, 2, 38, 242.1, 26.93, 0.41244, 39, 16.38, 24.34, 0.58756, 3, 38, 229.11, 18.8, 0.76591, 39, 2.52, 30.89, 0.23111, 70, -36.55, 404.28, 0.00298, 3, 38, 210.05, 17.74, 0.93627, 39, -8.66, 46.36, 0.05692, 70, -18.07, 399.5, 0.00681, 2, 38, 155.96, 15.99, 0.98386, 70, 33.98, 384.73, 0.01614, 2, 38, 99.32, 10.08, 0.98407, 70, 89.74, 373.13, 0.01593, 1, 39, 70.1, -11.21, 1, 2, 38, 273.23, 71.24, 0.00581, 39, 70.49, 22.07, 0.99419, 1, 39, 164.69, 4.7, 1, 3, 37, -20.02, 64.34, 0.00084, 38, 33.06, -51.22, 0.86149, 7, 201.83, 83.6, 0.13767, 3, 37, 3.45, 52.77, 0.0629, 38, 7.19, -47.33, 0.828, 7, 225.29, 72.03, 0.1091, 3, 37, 22.69, 35.56, 0.23343, 38, -16.38, -36.79, 0.67255, 7, 244.54, 54.82, 0.09402], "hull": 61, "edges": [0, 2, 12, 14, 38, 40, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 66, 68, 84, 86, 98, 100, 106, 108, 118, 120, 116, 118, 114, 116, 112, 114, 108, 110, 110, 112, 104, 106, 100, 102, 102, 104, 2, 4, 120, 0, 6, 8, 4, 6, 8, 10, 10, 12, 14, 16, 16, 18, 18, 20, 20, 22, 28, 30, 26, 28, 22, 24, 24, 26, 96, 98, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 74, 76, 76, 78, 36, 38, 30, 32, 82, 84, 78, 80, 80, 82, 32, 34, 34, 36, 40, 42, 72, 74, 68, 70, 70, 72, 50, 52, 46, 48, 48, 50, 62, 64, 64, 66, 42, 44, 44, 46, 124, 126, 126, 128, 128, 130, 132, 134, 134, 136, 136, 138, 130, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 138, 152, 166, 154, 168, 168, 170, 170, 166, 108, 172, 172, 174, 174, 176, 176, 118], "width": 306, "height": 570}}, "body": {"body": {"type": "mesh", "uvs": [0.76698, 0.00463, 0.81029, 0.01296, 0.8398, 0.0217, 0.87367, 0.03111, 0.89271, 0.04113, 0.89319, 0.05025, 0.88057, 0.05887, 0.89694, 0.06665, 0.91381, 0.07658, 0.91761, 0.08718, 0.90923, 0.09553, 0.87466, 0.1022, 0.84838, 0.1017, 0.81859, 0.10906, 0.80193, 0.11638, 0.80028, 0.12349, 0.80786, 0.12987, 0.82281, 0.13575, 0.80979, 0.13992, 0.8288, 0.14671, 0.83359, 0.15625, 0.81435, 0.16561, 0.76878, 0.17091, 0.73362, 0.17027, 0.70962, 0.16517, 0.70907, 0.15852, 0.73153, 0.15289, 0.74052, 0.15324, 0.72466, 0.15874, 0.72507, 0.16281, 0.73963, 0.16586, 0.76607, 0.16634, 0.79105, 0.16323, 0.801, 0.15702, 0.79736, 0.15123, 0.77941, 0.14531, 0.76318, 0.14226, 0.73715, 0.15019, 0.70024, 0.1567, 0.66734, 0.1604, 0.62897, 0.16324, 0.68598, 0.17694, 0.77161, 0.18373, 0.86951, 0.19186, 0.94248, 0.20256, 0.97413, 0.21106, 0.99605, 0.22374, 1, 0.23668, 0.98876, 0.24773, 0.96161, 0.25853, 0.91396, 0.26762, 0.86276, 0.27187, 0.84534, 0.29029, 0.83525, 0.30099, 0.82914, 0.3115, 0.8426, 0.32352, 0.85659, 0.3359, 0.8743, 0.35062, 0.8841, 0.36776, 0.88907, 0.38391, 0.91413, 0.40352, 0.9435, 0.4334, 0.95759, 0.45311, 0.95739, 0.47396, 0.95462, 0.49861, 0.93501, 0.53175, 0.87568, 0.57843, 0.8398, 0.59954, 0.81578, 0.61548, 0.79852, 0.62908, 0.79306, 0.64496, 0.78692, 0.66067, 0.77813, 0.67485, 0.7747, 0.68781, 0.76979, 0.70878, 0.75881, 0.73574, 0.62772, 0.82017, 0.57871, 0.86627, 0.56567, 0.8806, 0.56527, 0.89353, 0.55853, 0.90244, 0.56447, 0.91375, 0.56892, 0.92422, 0.58658, 0.9365, 0.59601, 0.95327, 0.60652, 0.96266, 0.68418, 0.96959, 0.69127, 0.98255, 0.63256, 0.9952, 0.57589, 0.99697, 0.45515, 0.99107, 0.43313, 1, 0.41418, 1, 0.3997, 0.99805, 0.40035, 0.9572, 0.37477, 0.93413, 0.37909, 0.92127, 0.41617, 0.90601, 0.40299, 0.89753, 0.4265, 0.87964, 0.4373, 0.86363, 0.44398, 0.85203, 0.44905, 0.81802, 0.44744, 0.72522, 0.48003, 0.68906, 0.52267, 0.66562, 0.54819, 0.6521, 0.55215, 0.63827, 0.55537, 0.62482, 0.56507, 0.60899, 0.57071, 0.59233, 0.55803, 0.56819, 0.5034, 0.51911, 0.49095, 0.49715, 0.48769, 0.48117, 0.48442, 0.46518, 0.42688, 0.4587, 0.39254, 0.4492, 0.3596, 0.45838, 0.30192, 0.47459, 0.23538, 0.48643, 0.16897, 0.46526, 0.12958, 0.44183, 0.12141, 0.42619, 0.13199, 0.41074, 0.15322, 0.39847, 0.22176, 0.37568, 0.32341, 0.3548, 0.40873, 0.33854, 0.45235, 0.3293, 0.49413, 0.32102, 0.50689, 0.30711, 0.50565, 0.29445, 0.49711, 0.28326, 0.47653, 0.27361, 0.41936, 0.25739, 0.37399, 0.24369, 0.37434, 0.25298, 0.36479, 0.26136, 0.33646, 0.26815, 0.30281, 0.27224, 0.29656, 0.27722, 0.30192, 0.28111, 0.32568, 0.28346, 0.29667, 0.284, 0.28095, 0.27856, 0.25921, 0.28232, 0.21628, 0.28668, 0.19986, 0.29184, 0.20827, 0.29785, 0.23505, 0.30283, 0.26499, 0.30732, 0.28216, 0.31594, 0.25918, 0.32803, 0.19219, 0.33468, 0.14431, 0.33615, 0.11703, 0.33244, 0.11512, 0.32231, 0.10239, 0.3132, 0.0539, 0.31184, 0.02724, 0.30472, 0.02248, 0.29739, 0.03476, 0.28993, 0.04508, 0.29002, 0.0398, 0.29749, 0.04593, 0.30299, 0.06827, 0.30569, 0.09543, 0.30418, 0.11562, 0.2977, 0.10157, 0.28811, 0.06895, 0.28428, 0.06341, 0.2761, 0.08907, 0.26701, 0.03509, 0.26137, 0.01962, 0.25248, 0.04097, 0.2458, 0.08845, 0.24235, 0.05173, 0.23547, 0.02582, 0.22818, 0.00249, 0.22036, 0.00333, 0.21116, 0.02226, 0.20362, 0.06686, 0.19695, 0.11479, 0.19314, 0.13595, 0.18591, 0.12967, 0.17826, 0.1061, 0.18117, 0.0763, 0.17907, 0.05873, 0.16526, 0.07221, 0.15282, 0.10756, 0.14323, 0.10782, 0.13262, 0.12425, 0.12485, 0.16107, 0.11746, 0.16591, 0.10884, 0.19588, 0.09905, 0.24852, 0.09275, 0.28479, 0.08607, 0.3013, 0.08152, 0.31424, 0.06762, 0.33059, 0.05485, 0.35822, 0.04249, 0.38709, 0.03483, 0.42279, 0.02819, 0.45671, 0.02188, 0.49765, 0.01471, 0.53811, 0.00935, 0.57545, 0.00544, 0.6129, 0.00281, 0.66334, 0.0005, 0.71786, 0.00062, 0.33176, 0.22748, 0.31019, 0.21322, 0.30628, 0.20057, 0.30538, 0.18906, 0.31046, 0.17444, 0.32498, 0.15914, 0.36058, 0.14139, 0.40256, 0.12415, 0.44279, 0.1178, 0.46373, 0.11359, 0.47832, 0.10593, 0.48594, 0.09562, 0.48721, 0.07563, 0.48721, 0.08513, 0.59229, 0.16018, 0.55198, 0.15271, 0.55078, 0.14545, 0.55234, 0.13831, 0.57135, 0.12785, 0.59431, 0.12099, 0.62459, 0.1136, 0.65114, 0.10752, 0.68511, 0.09969, 0.32197, 0.18691, 0.3433, 0.19742, 0.39066, 0.20643, 0.31859, 0.17395, 0.32885, 0.16012, 0.36084, 0.1477, 0.40217, 0.14149, 0.43499, 0.13968, 0.54564, 0.18561, 0.55889, 0.17337, 0.55786, 0.16081, 0.51805, 0.15257, 0.4762, 0.14579, 0.45634, 0.20695, 0.51518, 0.19888, 0.43956, 0.16628, 0.40078, 0.13735, 0.37679, 0.13353, 0.40014, 0.13208, 0.42501, 0.13245, 0.4581, 0.13637, 0.49101, 0.14149, 0.53428, 0.14956, 0.57316, 0.158, 0.61453, 0.16633, 0.59133, 0.16862, 0.31757, 0.21113, 0.3579, 0.22542, 0.40364, 0.24094, 0.58672, 0.18319, 0.54466, 0.2021, 0.55178, 0.22296, 0.58992, 0.2419, 0.67392, 0.25665, 0.7587, 0.26724, 0.62466, 0.17681, 0.6593, 0.17573, 0.82166, 0.20793, 0.81255, 0.23462, 0.84819, 0.25661, 0.95964, 0.22209, 0.96609, 0.23902, 0.68926, 0.19884, 0.72519, 0.18888, 0.67564, 0.22029, 0.70227, 0.24668, 0.90122, 0.21306, 0.89369, 0.23074, 0.91739, 0.24852, 0.4816, 0.23245, 0.46107, 0.21863, 0.60673, 0.26246, 0.62973, 0.27661, 0.64353, 0.29171, 0.64813, 0.30699, 0.63007, 0.31731, 0.58502, 0.32973, 0.51536, 0.347, 0.4548, 0.36078, 0.39291, 0.37862, 0.3492, 0.40344, 0.35401, 0.42849, 0.75287, 0.28035, 0.74835, 0.29439, 0.75106, 0.30932, 0.75106, 0.32314, 0.75921, 0.33909, 0.78093, 0.35441, 0.78929, 0.3668, 0.80989, 0.38962, 0.56511, 0.46527, 0.65357, 0.45947, 0.72808, 0.45002, 0.78075, 0.43138, 0.80343, 0.41149, 0.61288, 0.34244, 0.70536, 0.34634, 0.48448, 0.40711, 0.44701, 0.44638, 0.53405, 0.4499, 0.62216, 0.44471, 0.68005, 0.42778, 0.70795, 0.40514, 0.70084, 0.38298, 0.64996, 0.36543, 0.56237, 0.36123, 0.46847, 0.3682, 0.41133, 0.38478, 0.3834, 0.40547, 0.39916, 0.42932, 0.49561, 0.38381, 0.56874, 0.38626, 0.60031, 0.4014, 0.58286, 0.4192, 0.52968, 0.42984, 0.45323, 0.42697, 0.42248, 0.41224, 0.4416, 0.39301, 0.31878, 0.45164, 0.19753, 0.39861, 0.15847, 0.41995, 0.22085, 0.41305, 0.21398, 0.4326, 0.1731, 0.44393, 0.30671, 0.3778, 0.24472, 0.4638, 0.38969, 0.35539, 0.67988, 0.33106, 0.69093, 0.32057, 0.5026, 0.25099, 0.5389, 0.26848, 0.56179, 0.28091, 0.57441, 0.29218, 0.57678, 0.30675, 0.56336, 0.31686, 0.52943, 0.32715, 0.48603, 0.3359, 0.44499, 0.34425, 0.8328, 0.45459, 0.83001, 0.43058, 0.57862, 0.48022, 0.587, 0.50085, 0.60236, 0.52423, 0.64984, 0.57788, 0.77135, 0.58338, 0.82023, 0.53214, 0.83978, 0.50188, 0.84746, 0.47222, 0.71688, 0.47369, 0.71967, 0.50051, 0.71548, 0.53008, 0.63927, 0.59981, 0.63351, 0.61491, 0.62109, 0.62866, 0.61126, 0.64345, 0.60307, 0.65757, 0.57419, 0.67521, 0.54765, 0.69616, 0.52841, 0.72564, 0.53388, 0.81933, 0.68036, 0.73204, 0.69678, 0.70339, 0.70773, 0.68182, 0.71184, 0.66261, 0.72479, 0.64855, 0.73413, 0.63371, 0.73862, 0.62017, 0.75321, 0.60442, 0.45145, 0.04703, 0.43945, 0.05732, 0.45936, 0.07916, 0.50951, 0.06689, 0.54907, 0.04942, 0.52882, 0.05814, 0.57795, 0.04224, 0.64337, 0.03728, 0.66178, 0.03394, 0.6774, 0.02983, 0.6873, 0.02287, 0.74318, 0.03023, 0.77051, 0.03577, 0.78867, 0.03658, 0.50304, 0.06341, 0.49629, 0.05192, 0.47793, 0.04662, 0.78605, 0.04935, 0.77467, 0.06212, 0.74936, 0.07537, 0.71962, 0.08783, 0.76398, 0.02292, 0.80422, 0.02303, 0.84112, 0.02929, 0.8623, 0.03828, 0.87475, 0.04819, 0.81294, 0.03844, 0.83211, 0.04635, 0.84217, 0.05555, 0.8714, 0.07289, 0.87284, 0.08386, 0.86373, 0.09283, 0.85415, 0.06416, 0.81429, 0.05392, 0.81956, 0.06406, 0.82147, 0.07397, 0.80854, 0.08306, 0.77547, 0.09155, 0.75086, 0.09969, 0.72355, 0.10914, 0.71732, 0.11775, 0.7217, 0.12794, 0.7366, 0.13595, 0.59764, 0.14518, 0.62766, 0.13462, 0.63974, 0.1232, 0.67093, 0.14806, 0.63545, 0.1543, 0.70289, 0.13933, 0.71085, 0.01271, 0.75631, 0.01075, 0.80135, 0.01527, 0.67714, 0.00502, 0.72065, 0.00365, 0.6295, 0.01936, 0.59852, 0.02612, 0.54443, 0.02687, 0.49331, 0.02871, 0.46111, 0.03665, 0.60071, 0.01205, 0.55926, 0.01592, 0.51695, 0.02161, 0.52416, 0.03812, 0.36405, 0.05578, 0.37773, 0.04546, 0.36292, 0.06709, 0.38389, 0.07832, 0.40827, 0.0906, 0.41167, 0.10318, 0.38389, 0.11309, 0.33926, 0.12145, 0.28619, 0.12985, 0.24938, 0.14211, 0.23856, 0.1565, 0.24361, 0.17143, 0.25516, 0.18742, 0.23856, 0.19974, 0.1967, 0.21058, 0.17615, 0.2218, 0.19058, 0.23193, 0.22667, 0.24152, 0.25626, 0.26312, 0.24254, 0.27538, 0.26203, 0.21303, 0.25842, 0.22582, 0.29451, 0.23879, 0.31904, 0.24981, 0.3176, 0.26029, 0.25326, 0.25229, 0.34402, 0.07919, 0.34186, 0.09216, 0.32381, 0.10033, 0.28629, 0.11082, 0.24155, 0.11721, 0.21254, 0.12622, 0.19089, 0.13386, 0.17935, 0.14654, 0.1779, 0.16022, 0.18729, 0.17317, 0.19522, 0.18561, 0.17718, 0.19723, 0.13677, 0.2054, 0.10574, 0.21393, 0.10213, 0.2253, 0.13027, 0.23667, 0.15586, 0.24644, 0.15875, 0.25852, 0.14431, 0.2674, 0.11434, 0.27766, 0.1574, 0.29928, 0.17859, 0.3087, 0.19021, 0.3193, 0.17175, 0.32654, 0.09399, 0.25377, 0.05298, 0.25377, 0.10697, 0.16751, 0.10151, 0.15489, 0.44501, 0.07044, 0.40554, 0.03698, 0.47529, 0.02516, 0.43858, 0.03065, 0.63643, 0.00387, 0.66396, 0.01671, 0.63294, 0.01018, 0.53447, 0.146, 0.52567, 0.137, 0.51723, 0.12494, 0.51022, 0.11375, 0.51137, 0.10558, 0.51556, 0.09657, 0.52586, 0.087, 0.53539, 0.07939, 0.54555, 0.12884, 0.55661, 0.1202, 0.57033, 0.11232, 0.58635, 0.10518, 0.60045, 0.09833, 0.61609, 0.09129, 0.46804, 0.12653, 0.49414, 0.13567, 0.51785, 0.14283], "triangles": [332, 336, 295, 118, 332, 117, 120, 339, 119, 120, 121, 339, 119, 332, 118, 119, 339, 332, 121, 337, 339, 121, 122, 337, 337, 336, 339, 339, 336, 332, 334, 336, 337, 334, 337, 123, 123, 337, 122, 123, 124, 334, 333, 124, 125, 335, 334, 124, 336, 335, 295, 336, 334, 335, 335, 124, 333, 28, 26, 27, 28, 25, 26, 23, 30, 22, 23, 24, 30, 30, 24, 29, 29, 25, 28, 29, 24, 25, 30, 31, 22, 22, 32, 21, 22, 31, 32, 32, 33, 21, 21, 33, 20, 33, 34, 20, 34, 19, 20, 37, 38, 428, 19, 34, 18, 428, 430, 37, 430, 424, 37, 37, 424, 36, 34, 35, 18, 35, 36, 18, 18, 36, 16, 36, 424, 16, 18, 16, 17, 428, 426, 430, 426, 427, 430, 430, 423, 424, 430, 427, 423, 424, 15, 16, 424, 423, 15, 427, 422, 423, 423, 422, 15, 15, 422, 14, 427, 232, 422, 422, 232, 421, 422, 421, 14, 421, 420, 14, 421, 233, 420, 14, 420, 13, 420, 419, 13, 13, 419, 12, 12, 413, 11, 11, 413, 10, 419, 418, 12, 12, 418, 413, 10, 413, 9, 413, 412, 9, 413, 418, 412, 412, 8, 9, 418, 417, 412, 417, 411, 412, 412, 411, 8, 418, 401, 417, 417, 400, 416, 411, 7, 8, 417, 414, 411, 417, 416, 414, 411, 414, 7, 414, 6, 7, 416, 410, 414, 414, 410, 6, 416, 415, 410, 410, 407, 6, 6, 407, 5, 410, 409, 407, 407, 4, 5, 409, 406, 407, 407, 406, 4, 406, 3, 4, 406, 405, 3, 155, 494, 154, 155, 156, 494, 494, 493, 154, 154, 493, 153, 156, 157, 494, 152, 153, 493, 152, 493, 151, 151, 493, 150, 494, 157, 493, 157, 492, 493, 492, 158, 491, 168, 491, 167, 158, 492, 157, 493, 492, 150, 159, 166, 158, 166, 167, 158, 491, 158, 167, 160, 165, 159, 159, 165, 166, 492, 149, 150, 492, 491, 149, 160, 164, 165, 160, 161, 164, 491, 148, 149, 148, 491, 169, 491, 168, 169, 163, 164, 162, 164, 161, 162, 147, 148, 490, 148, 169, 490, 169, 170, 490, 490, 489, 147, 147, 464, 146, 147, 489, 464, 170, 171, 490, 144, 142, 143, 144, 145, 142, 146, 464, 145, 145, 141, 142, 141, 145, 140, 171, 172, 490, 490, 172, 489, 140, 145, 464, 489, 488, 464, 464, 463, 140, 464, 488, 463, 140, 469, 139, 140, 463, 469, 488, 489, 495, 173, 496, 172, 489, 172, 495, 172, 496, 495, 488, 470, 463, 463, 470, 469, 173, 174, 496, 495, 487, 488, 488, 462, 470, 488, 487, 462, 496, 176, 495, 487, 176, 486, 487, 495, 176, 174, 175, 496, 496, 175, 176, 470, 462, 467, 487, 461, 462, 487, 486, 461, 486, 176, 485, 462, 461, 466, 486, 460, 461, 176, 177, 485, 486, 485, 460, 177, 178, 485, 461, 460, 466, 485, 178, 484, 460, 459, 466, 466, 459, 465, 484, 178, 179, 484, 179, 180, 485, 484, 460, 484, 180, 181, 484, 483, 460, 460, 483, 459, 181, 182, 484, 483, 182, 183, 483, 484, 182, 459, 458, 465, 483, 482, 459, 459, 482, 458, 483, 183, 482, 482, 481, 458, 458, 481, 457, 183, 184, 482, 482, 184, 481, 481, 456, 457, 184, 185, 481, 185, 480, 481, 481, 480, 456, 185, 186, 497, 186, 187, 497, 187, 188, 497, 185, 497, 480, 497, 479, 480, 480, 479, 456, 479, 455, 456, 188, 498, 497, 497, 498, 479, 188, 189, 498, 479, 498, 478, 498, 190, 478, 498, 189, 190, 478, 190, 477, 190, 191, 477, 191, 192, 477, 479, 478, 455, 455, 478, 454, 478, 477, 454, 477, 476, 454, 454, 476, 453, 477, 193, 476, 476, 475, 453, 476, 193, 475, 91, 92, 90, 92, 93, 90, 93, 94, 90, 88, 89, 85, 89, 90, 85, 87, 85, 86, 87, 88, 85, 85, 94, 84, 84, 94, 83, 83, 94, 82, 81, 82, 95, 85, 90, 94, 97, 81, 96, 97, 80, 81, 95, 82, 94, 96, 81, 95, 97, 98, 80, 80, 98, 79, 98, 99, 79, 79, 99, 78, 99, 100, 78, 78, 100, 77, 100, 101, 77, 101, 373, 77, 77, 373, 76, 101, 102, 373, 75, 76, 374, 374, 76, 373, 374, 373, 372, 373, 102, 372, 102, 103, 372, 75, 374, 74, 374, 375, 74, 374, 372, 375, 372, 371, 375, 372, 103, 371, 103, 104, 371, 74, 375, 73, 371, 370, 375, 375, 376, 73, 375, 370, 376, 371, 104, 370, 104, 105, 370, 73, 376, 72, 370, 369, 376, 376, 377, 72, 376, 369, 377, 370, 105, 369, 369, 105, 106, 72, 377, 71, 377, 378, 71, 377, 369, 378, 71, 378, 70, 369, 368, 378, 369, 106, 368, 106, 107, 368, 314, 327, 315, 378, 379, 70, 378, 368, 379, 70, 379, 69, 368, 367, 379, 368, 107, 367, 107, 108, 367, 379, 367, 380, 379, 380, 69, 380, 367, 366, 69, 380, 68, 367, 108, 366, 108, 109, 366, 380, 381, 68, 380, 366, 381, 68, 381, 67, 366, 365, 381, 366, 109, 365, 109, 110, 365, 381, 358, 67, 381, 365, 358, 365, 357, 358, 365, 110, 357, 67, 358, 66, 110, 111, 357, 66, 358, 359, 359, 358, 364, 66, 359, 65, 358, 357, 364, 364, 357, 356, 356, 357, 111, 111, 112, 356, 65, 359, 360, 360, 359, 363, 65, 360, 64, 359, 364, 363, 364, 356, 363, 112, 355, 356, 356, 355, 363, 112, 113, 355, 360, 361, 64, 361, 360, 362, 113, 354, 355, 355, 354, 363, 354, 362, 363, 360, 363, 362, 64, 361, 63, 113, 114, 354, 114, 304, 354, 114, 115, 304, 354, 305, 362, 354, 304, 305, 63, 361, 62, 362, 352, 361, 362, 306, 352, 362, 305, 306, 361, 352, 62, 115, 313, 304, 304, 314, 305, 304, 313, 314, 116, 312, 115, 115, 312, 313, 305, 314, 306, 306, 307, 352, 307, 353, 352, 352, 61, 62, 352, 353, 61, 314, 315, 306, 306, 315, 307, 353, 60, 61, 315, 316, 307, 307, 308, 353, 307, 316, 308, 353, 308, 60, 60, 308, 303, 303, 59, 60, 308, 316, 303, 316, 317, 303, 116, 117, 312, 294, 333, 338, 338, 333, 126, 333, 294, 335, 293, 127, 340, 127, 293, 338, 333, 125, 126, 338, 126, 127, 340, 128, 351, 340, 127, 128, 351, 128, 129, 335, 294, 295, 294, 338, 293, 293, 340, 292, 117, 295, 323, 295, 294, 322, 323, 295, 322, 117, 323, 312, 331, 322, 321, 330, 322, 331, 312, 328, 313, 313, 328, 314, 312, 329, 328, 312, 323, 329, 328, 327, 314, 327, 328, 311, 329, 323, 330, 327, 326, 315, 315, 326, 316, 328, 329, 311, 329, 330, 311, 327, 311, 326, 311, 330, 331, 311, 325, 326, 311, 324, 325, 311, 331, 324, 326, 317, 316, 326, 325, 317, 331, 321, 324, 324, 319, 325, 325, 318, 317, 325, 319, 318, 324, 321, 320, 324, 320, 319, 302, 318, 310, 319, 320, 291, 319, 309, 318, 318, 309, 310, 320, 292, 291, 319, 291, 309, 351, 350, 291, 291, 290, 309, 291, 349, 290, 291, 350, 349, 309, 341, 310, 310, 341, 300, 299, 341, 342, 341, 299, 300, 309, 290, 341, 350, 130, 349, 350, 129, 130, 290, 289, 341, 341, 289, 342, 349, 348, 290, 290, 348, 289, 349, 130, 348, 130, 131, 348, 292, 351, 291, 293, 292, 320, 351, 129, 350, 321, 293, 320, 330, 323, 322, 322, 294, 321, 294, 293, 321, 332, 295, 117, 340, 351, 292, 317, 302, 303, 303, 58, 59, 303, 302, 58, 302, 57, 58, 310, 301, 302, 302, 301, 57, 310, 300, 301, 301, 56, 57, 301, 300, 56, 300, 55, 56, 300, 299, 55, 302, 317, 318, 299, 54, 55, 299, 298, 54, 299, 342, 298, 289, 288, 342, 342, 288, 298, 348, 347, 289, 289, 347, 288, 348, 131, 347, 54, 298, 53, 288, 297, 298, 298, 297, 53, 131, 132, 347, 347, 287, 288, 288, 287, 297, 132, 346, 347, 347, 346, 287, 53, 297, 52, 132, 345, 346, 297, 296, 52, 297, 287, 296, 287, 346, 286, 346, 345, 286, 287, 286, 296, 52, 296, 51, 132, 133, 345, 133, 344, 345, 133, 134, 344, 345, 285, 286, 345, 344, 285, 286, 267, 296, 296, 268, 51, 296, 267, 268, 286, 285, 267, 134, 343, 344, 134, 135, 343, 268, 273, 51, 51, 273, 50, 344, 343, 285, 139, 469, 138, 50, 282, 49, 50, 273, 282, 267, 279, 268, 273, 268, 272, 343, 266, 285, 285, 266, 267, 138, 469, 137, 469, 468, 137, 469, 470, 468, 49, 282, 48, 135, 136, 262, 135, 262, 343, 267, 266, 279, 268, 279, 272, 273, 281, 282, 273, 272, 281, 468, 136, 137, 470, 467, 468, 262, 283, 343, 343, 283, 266, 468, 467, 136, 282, 275, 48, 282, 281, 275, 48, 275, 47, 266, 278, 279, 279, 278, 272, 467, 211, 136, 211, 261, 136, 136, 261, 262, 283, 265, 266, 266, 265, 278, 462, 466, 467, 262, 261, 284, 262, 284, 283, 284, 236, 247, 236, 284, 261, 281, 274, 275, 275, 46, 47, 275, 274, 46, 467, 466, 211, 212, 466, 465, 466, 212, 211, 272, 278, 271, 271, 278, 276, 271, 276, 277, 272, 271, 281, 271, 277, 42, 265, 284, 264, 264, 284, 248, 284, 265, 283, 281, 280, 274, 281, 271, 280, 211, 212, 261, 212, 260, 261, 261, 260, 236, 274, 45, 46, 265, 264, 278, 274, 280, 45, 278, 264, 276, 276, 264, 263, 276, 263, 269, 284, 247, 248, 260, 212, 213, 271, 43, 280, 280, 44, 45, 280, 43, 44, 465, 458, 213, 212, 465, 213, 213, 457, 214, 457, 213, 458, 260, 235, 236, 260, 213, 235, 271, 42, 43, 248, 247, 249, 249, 247, 236, 264, 242, 263, 264, 248, 242, 213, 214, 235, 236, 235, 249, 269, 270, 276, 276, 270, 277, 214, 234, 235, 235, 234, 249, 234, 214, 215, 270, 41, 277, 277, 41, 42, 214, 457, 215, 457, 456, 215, 215, 237, 234, 234, 237, 249, 242, 243, 263, 263, 259, 269, 263, 243, 259, 259, 258, 269, 269, 258, 270, 258, 40, 270, 41, 270, 40, 238, 237, 215, 238, 215, 216, 216, 215, 456, 456, 455, 216, 244, 257, 259, 259, 257, 258, 257, 225, 258, 258, 225, 40, 40, 429, 39, 40, 225, 429, 39, 428, 38, 39, 429, 428, 429, 225, 425, 238, 216, 239, 455, 454, 216, 239, 216, 217, 217, 454, 453, 216, 454, 217, 256, 226, 257, 425, 225, 226, 225, 257, 226, 429, 425, 428, 227, 226, 506, 226, 256, 506, 226, 227, 425, 255, 522, 256, 256, 522, 506, 425, 426, 428, 239, 217, 240, 227, 506, 228, 507, 228, 506, 227, 228, 425, 507, 506, 522, 426, 425, 229, 507, 522, 521, 217, 250, 240, 522, 255, 521, 255, 254, 521, 217, 453, 251, 217, 251, 250, 251, 453, 452, 250, 253, 241, 507, 514, 228, 425, 228, 229, 251, 252, 250, 250, 252, 253, 254, 520, 521, 254, 253, 520, 507, 521, 508, 252, 251, 218, 252, 218, 253, 253, 219, 520, 253, 218, 219, 218, 251, 452, 452, 451, 218, 248, 249, 242, 242, 249, 243, 244, 249, 245, 249, 244, 243, 237, 238, 249, 243, 244, 259, 238, 239, 249, 239, 240, 249, 240, 241, 249, 249, 246, 245, 249, 241, 246, 245, 256, 244, 244, 256, 257, 246, 255, 245, 245, 255, 256, 241, 254, 246, 246, 254, 255, 240, 250, 241, 241, 253, 254, 228, 514, 229, 514, 507, 508, 229, 230, 426, 426, 230, 427, 192, 193, 477, 453, 474, 452, 453, 475, 474, 229, 514, 515, 514, 508, 515, 229, 515, 230, 219, 220, 520, 520, 509, 508, 520, 220, 509, 508, 509, 515, 218, 451, 219, 230, 231, 427, 427, 231, 232, 474, 473, 452, 452, 473, 451, 515, 516, 230, 230, 516, 231, 515, 509, 516, 220, 219, 450, 193, 194, 475, 194, 195, 475, 475, 196, 474, 475, 195, 196, 220, 221, 509, 509, 510, 516, 509, 221, 510, 516, 517, 231, 231, 517, 232, 219, 451, 450, 220, 450, 221, 451, 473, 450, 472, 450, 473, 450, 472, 449, 448, 472, 471, 472, 448, 449, 516, 510, 517, 474, 196, 473, 517, 518, 232, 221, 222, 510, 221, 450, 222, 510, 511, 517, 510, 222, 511, 517, 511, 518, 450, 449, 222, 196, 197, 473, 473, 197, 472, 511, 512, 518, 511, 222, 512, 222, 449, 224, 222, 224, 512, 224, 449, 384, 197, 198, 472, 472, 198, 471, 449, 448, 384, 198, 199, 471, 521, 520, 508, 421, 232, 233, 518, 519, 232, 232, 519, 233, 233, 402, 420, 420, 402, 419, 233, 519, 402, 518, 512, 519, 402, 401, 419, 419, 401, 418, 512, 513, 519, 519, 387, 386, 386, 388, 519, 402, 519, 401, 519, 388, 389, 401, 519, 389, 401, 389, 390, 400, 401, 391, 391, 401, 390, 512, 224, 513, 224, 223, 513, 224, 384, 223, 400, 417, 401, 223, 385, 513, 385, 387, 513, 519, 513, 387, 471, 447, 448, 471, 199, 447, 448, 499, 384, 384, 499, 223, 448, 447, 499, 223, 396, 385, 223, 499, 396, 399, 400, 393, 393, 400, 391, 396, 383, 397, 397, 383, 382, 382, 398, 397, 447, 383, 499, 383, 396, 499, 199, 200, 447, 447, 445, 383, 447, 200, 445, 385, 396, 387, 400, 415, 416, 396, 397, 387, 400, 399, 415, 399, 393, 394, 387, 397, 386, 445, 446, 383, 383, 446, 382, 446, 445, 201, 415, 409, 410, 445, 200, 201, 415, 399, 409, 386, 397, 444, 397, 398, 444, 386, 444, 388, 399, 408, 409, 399, 395, 408, 399, 394, 395, 446, 500, 382, 382, 500, 440, 382, 440, 398, 440, 500, 502, 398, 440, 444, 409, 408, 406, 446, 202, 500, 446, 201, 202, 444, 438, 388, 388, 437, 389, 388, 438, 437, 408, 405, 406, 405, 408, 404, 440, 439, 444, 444, 439, 438, 438, 439, 443, 389, 437, 390, 500, 203, 502, 500, 202, 203, 440, 501, 439, 440, 502, 501, 404, 408, 395, 393, 403, 394, 395, 394, 404, 394, 403, 404, 437, 436, 390, 390, 436, 391, 502, 204, 501, 502, 203, 204, 391, 392, 393, 393, 392, 403, 436, 504, 391, 391, 504, 392, 404, 2, 405, 3, 405, 2, 439, 501, 443, 438, 442, 437, 438, 443, 442, 437, 441, 436, 437, 442, 441, 443, 501, 205, 403, 433, 404, 404, 433, 2, 392, 431, 403, 431, 432, 403, 403, 432, 433, 392, 504, 431, 501, 204, 205, 433, 1, 2, 443, 206, 442, 443, 205, 206, 436, 505, 504, 436, 441, 505, 504, 505, 434, 434, 505, 503, 504, 434, 431, 434, 503, 209, 442, 207, 441, 442, 206, 207, 1, 433, 0, 433, 432, 0, 431, 435, 432, 431, 434, 435, 441, 208, 505, 441, 207, 208, 432, 435, 0, 505, 208, 503, 434, 210, 435, 434, 209, 210, 503, 208, 209, 0, 435, 210], "vertices": [3, 9, 174.69, -28.32, 0.99432, 5, -616.44, -101.18, 0, 68, -191.89, 44.26, 0.00568, 3, 9, 168.52, -50.68, 0.99251, 5, -611.02, -78.64, 0, 68, -198.05, 21.9, 0.00749, 3, 9, 159.47, -67.93, 0.98999, 5, -602.54, -61.1, 0, 68, -207.11, 4.66, 0.01001, 3, 57, 3.45, 17.55, 0.94311, 58, -48.79, 1.69, 0.04372, 68, -216.52, -14.73, 0.01317, 3, 57, 21.91, 21.93, 0.93058, 58, -32.78, 11.86, 0.05428, 68, -229.3, -28.75, 0.01514, 3, 57, 37.24, 18.96, 0.61602, 58, -17.31, 14.07, 0.36848, 68, -243.65, -34.93, 0.0155, 3, 57, 50.63, 10.74, 0.16263, 58, -1.98, 10.68, 0.82247, 68, -259.33, -35.68, 0.01491, 2, 58, 10.37, 19.24, 0.98319, 68, -269.01, -47.19, 0.01681, 3, 58, 26.33, 28.48, 0.96572, 59, -37.52, 10.57, 0.01546, 68, -281.98, -60.29, 0.01883, 3, 58, 44.15, 32.4, 0.81958, 59, -24.31, 23.16, 0.16088, 68, -298.15, -68.74, 0.01954, 3, 58, 58.79, 30.73, 0.60781, 59, -10.92, 29.31, 0.37261, 68, -312.72, -70.97, 0.01957, 3, 58, 72, 17.73, 0.32291, 59, 7.12, 25.03, 0.65937, 68, -328.87, -61.89, 0.01772, 3, 58, 72.58, 6.62, 0.1117, 59, 13.36, 15.83, 0.87275, 68, -332.34, -51.32, 0.01556, 4, 59, 31.07, 13.87, 0.86295, 60, -2.42, 18.03, 0.12328, 64, 42.68, -97.51, 0, 68, -348.82, -44.55, 0.01377, 5, 59, 45.25, 16.19, 0.15173, 60, 10.77, 12.33, 0.83299, 61, -14.33, 28.33, 0.00204, 64, 40.13, -83.37, 0, 68, -363.1, -42.88, 0.01323, 6, 57, 152.19, -44.95, 0, 59, 55.22, 23.24, 0.00959, 60, 22.97, 12.89, 0.88269, 61, -4.68, 20.85, 0.09373, 64, 32.92, -73.51, 0, 68, -374.62, -46.91, 0.01399, 4, 57, 163.56, -44.04, 0, 60, 33.52, 17.2, 0.43697, 61, 6.15, 17.28, 0.5476, 68, -383.5, -54.06, 0.01543, 4, 57, 174.7, -39.91, 0, 60, 42.88, 24.51, 0.06652, 61, 18.02, 16.78, 0.91627, 68, -390.37, -63.75, 0.01722, 4, 3, -315.8, -79.87, 0, 57, 180.59, -46.74, 0, 61, 20.81, 8.2, 0.98341, 68, -399.09, -61.42, 0.01659, 6, 5, -405.57, 23.39, 0, 57, 193.61, -41.25, 0, 60, 61.31, 28.95, 0.00188, 61, 34.94, 8.22, 0.68785, 62, -5.58, 6.04, 0.29146, 68, -406.75, -73.3, 0.0188, 4, 5, -391.52, 32, 0, 60, 77.38, 32.65, 0.00011, 62, 10.43, 9.96, 0.98048, 68, -421.08, -81.44, 0.01941, 5, 5, -373.56, 31.25, 0, 62, 27.3, 3.77, 0.44999, 63, -1.3, 5.38, 0.52966, 64, -17.55, -21.56, 0.00046, 68, -439, -80.1, 0.01989, 4, 5, -357.33, 17.51, 0, 63, 19.94, 6.4, 0.97814, 64, -8.49, -2.33, 0.00448, 68, -454.77, -65.83, 0.01738, 4, 57, 224.99, -88.78, 0, 63, 33.22, -0.3, 0.08381, 64, 3.66, 6.26, 0.90182, 68, -459.46, -51.71, 0.01437, 3, 5, -355.95, -9.29, 0, 64, 17.04, 5.95, 0.98799, 68, -455.27, -39, 0.01201, 4, 57, 203.16, -94.83, 0, 64, 24.47, -2.7, 0.8419, 65, 1.89, 4.77, 0.14702, 68, -444.83, -34.41, 0.01108, 3, 5, -378.93, -9.59, 0, 65, 15.02, 1.55, 0.98785, 68, -432.29, -39.46, 0.01215, 3, 57, 197.01, -80, 0, 65, 16.43, -2.03, 0.98711, 68, -431.39, -43.2, 0.01289, 4, 5, -368.61, -8.07, 0, 63, 29.14, -20, 0, 65, 4.92, -1.08, 0.9877, 68, -442.66, -40.63, 0.0123, 4, 5, -362.32, -5.02, 0, 64, 14.57, -1.31, 0.84688, 65, -1, -4.79, 0.14018, 68, -449.04, -43.47, 0.01294, 4, 5, -360.12, 2.73, 0, 63, 27.99, -6.31, 0.02075, 64, 6.51, -1.18, 0.96475, 68, -451.5, -51.15, 0.0145, 4, 57, 221.17, -74.01, 0, 63, 17.99, -1.27, 0.95908, 64, -2.62, -7.65, 0.02434, 68, -447.98, -61.77, 0.01658, 5, 3, -276.16, -70.52, 0, 5, -373.2, 20.62, 0, 62, 24.41, -6.47, 0.10722, 63, 6.22, -2.15, 0.87494, 68, -439.01, -69.46, 0.01784, 4, 3, -287.59, -71.11, 0, 57, 208.53, -56.33, 0, 62, 13.34, -3.55, 0.98203, 68, -427.57, -69.25, 0.01797, 6, 5, -393.03, 14.52, 0, 61, 33.8, -7.1, 0.40138, 62, 3.66, -6.23, 0.5815, 63, -4.13, -20.13, 4e-05, 64, 3.65, -36.03, 0, 68, -418.99, -64.02, 0.01708, 5, 5, -399.12, 3.43, 0, 61, 21.16, -7.6, 0.98503, 63, -1.02, -32.4, 0, 64, 15.94, -39.04, 0, 68, -412.54, -53.13, 0.01497, 7, 3, -304.01, -96.11, 0, 5, -401.05, -4.97, 0, 60, 56.58, 0.63, 0.0397, 61, 12.97, -10.28, 0.94693, 63, 3.3, -39.86, 0, 64, 24.56, -38.73, 0, 68, -410.34, -44.8, 0.01337, 5, 3, -287.08, -100.48, 0, 5, -384.12, -9.34, 0, 60, 71.23, -8.9, 0.1611, 61, 17.95, -27.04, 0.82662, 68, -427.11, -39.88, 0.01228, 5, 7, 190.33, -74.11, 0.42132, 5, -367.52, -18.9, 0, 60, 83.93, -23.25, 0.26675, 61, 18.31, -46.19, 0.30176, 68, -443.39, -29.77, 0.01017, 4, 7, 189.02, -58.91, 0.77727, 5, -356.01, -28.91, 0, 60, 91.66, -36.4, 0.21467, 68, -454.57, -19.39, 0.00806, 2, 7, 189.87, -42.02, 0.96098, 5, -344.87, -41.64, 0.03902, 3, 7, 159.68, -56.8, 0.95, 57, 232.09, -110.79, 0, 54, 91.01, 18.16, 0.05, 4, 7, 136.57, -86.92, 0.78459, 57, 250.84, -77.78, 0.00783, 70, 106.25, 240.86, 0.00948, 54, 67.91, -11.97, 0.1981, 4, 7, 109.57, -121.16, 0.78688, 57, 272.88, -40.16, 0, 70, 79.25, 206.63, 0.0164, 55, 65.66, -11.25, 0.19672, 4, 7, 81.96, -144, 0.78231, 57, 297.09, -13.74, 0, 70, 51.63, 183.78, 0.02211, 56, 45.07, -12.32, 0.19558, 5, 7, 63.76, -151.7, 0.78351, 6, 188.5, -115.54, 0.00136, 57, 314.06, -3.61, 0, 70, 33.43, 176.09, 0.01891, 66, 31.62, -6.09, 0.19622, 5, 7, 40.18, -153.11, 0.75831, 6, 167.28, -125.91, 0.00656, 57, 337.22, 1.02, 0, 70, 9.85, 174.68, 0.01941, 66, 8.05, -7.5, 0.21573, 5, 7, 18.72, -147.23, 0.7493, 6, 145.21, -128.73, 0.01639, 57, 359.28, -1.85, 0, 70, -11.6, 180.55, 0.01835, 66, -13.41, -1.62, 0.21596, 5, 7, 2.48, -136.41, 0.75434, 6, 126.05, -124.98, 0.03072, 57, 376.86, -10.34, 0, 70, -27.85, 191.38, 0.01867, 66, -29.66, 9.2, 0.19627, 5, 7, -11.11, -119.4, 0.72912, 6, 106.97, -114.5, 0.05579, 57, 392.65, -25.32, 0, 70, -41.43, 208.39, 0.01886, 56, -48, 12.28, 0.19623, 5, 7, -19.04, -95.22, 0.6786, 6, 90.36, -95.23, 0.1121, 57, 403.83, -48.18, 0, 70, -49.36, 232.56, 0.01163, 55, -62.95, 14.69, 0.19767, 4, 7, -18.65, -72.43, 0.55797, 6, 81.96, -74.03, 0.24203, 57, 406.58, -70.82, 0, 54, -87.31, 2.53, 0.2, 4, 7, -45.91, -54.91, 0.27868, 6, 50.06, -68.33, 0.69785, 4, -113.84, -16.99, 0.02347, 57, 435.98, -84.43, 0, 4, 7, -61.75, -44.73, 0.11138, 6, 31.52, -65.03, 0.78069, 4, -95.38, -13.27, 0.10244, 3, -68.96, 44.21, 0.00549, 4, 7, -77.86, -36.25, 0.02709, 6, 13.39, -63.39, 0.69879, 4, -77.91, -8.15, 0.24307, 3, -51.49, 49.33, 0.03105, 4, 7, -99.17, -34.69, 0.00141, 6, -6.88, -70.14, 0.46229, 4, -61.51, 5.55, 0.48445, 3, -35.09, 63.03, 0.05184, 5, 6, -27.77, -77.14, 0.22951, 4, -44.65, 19.7, 0.73839, 3, -18.22, 77.18, 0.02791, 25, -146.32, 12.93, 0.00419, 57, 513.49, -95.67, 0, 4, 6, -52.57, -85.91, 0.06464, 4, -24.78, 36.95, 0.88242, 25, -122.06, 23.12, 0.05293, 57, 539.71, -93.47, 0, 4, 6, -81.69, -91.57, 0.00486, 4, 0.25, 52.88, 0.78584, 25, -93.31, 30.43, 0.20931, 64, -260.54, 227.19, 0, 3, 4, 24.58, 66.25, 0.53772, 25, -66.03, 35.54, 0.46228, 64, -279.75, 247.22, 0, 3, 4, 50.8, 89.79, 0.21672, 25, -33.77, 49.72, 0.78328, 64, -309.28, 266.44, 0, 3, 4, 92.29, 122.28, 0.01263, 25, 15.79, 67.62, 0.98737, 64, -351.41, 298.1, 0, 2, 25, 48.73, 77.22, 1, 57, 718.86, -94.76, 0, 2, 25, 84.25, 81.03, 1, 57, 753.83, -102.1, 0, 2, 25, 126.39, 84.48, 1, 57, 794.97, -111.84, 0, 2, 25, 183.76, 82.46, 1, 57, 848.91, -131.48, 0, 2, 25, 266.02, 66.3, 1, 57, 922.16, -172.26, 0, 2, 25, 303.63, 55.19, 1, 57, 954.51, -194.43, 0, 2, 25, 331.89, 48.1, 1, 57, 979.19, -209.91, 0, 3, 25, 355.87, 43.4, 0.9348, 26, -32.54, 40.73, 0.0652, 57, 1000.55, -221.78, 0, 3, 25, 383.17, 44.08, 0.51608, 26, -5.4, 43.77, 0.48392, 57, 1026.72, -229.57, 0, 3, 25, 410.22, 44.44, 0.11547, 26, 21.51, 46.48, 0.88453, 57, 1052.56, -237.57, 0, 3, 25, 434.78, 43.41, 0.00746, 26, 46.08, 47.58, 0.99254, 57, 1075.6, -246.15, 0, 2, 26, 68.16, 50.48, 1, 57, 1097.07, -252.08, 0, 2, 26, 103.82, 55.46, 1, 57, 1131.84, -261.41, 0, 2, 26, 150.04, 59.91, 1, 57, 1176.13, -275.34, 0, 2, 26, 302.76, 33.85, 1, 57, 1306.58, -358.91, 0, 2, 26, 384.31, 28.95, 1, 57, 1379.75, -395.22, 0, 3, 26, 409.47, 28.34, 0.97012, 27, -25.95, 39.21, 0.02988, 57, 1402.68, -405.6, 0, 3, 26, 431.24, 32.49, 0.74891, 27, -4.03, 35.88, 0.25109, 57, 1424.35, -410.27, 0, 3, 26, 446.78, 32.68, 0.35543, 27, 10.68, 30.89, 0.64457, 57, 1438.72, -416.16, 0, 3, 26, 465.31, 38.91, 0.05599, 27, 30.22, 30.6, 0.94401, 57, 1458.22, -417.64, 0, 3, 26, 482.54, 44.25, 0.00118, 27, 48.25, 29.9, 0.99882, 57, 1476.16, -419.45, 0, 2, 27, 70.15, 34.27, 1, 57, 1498.29, -416.43, 0, 2, 27, 99.16, 34.11, 1, 57, 1527.24, -418.37, 0, 2, 27, 115.72, 36.2, 1, 57, 1543.89, -417.3, 0, 2, 27, 132.16, 66.94, 1, 57, 1562.19, -387.63, 0, 2, 27, 154.58, 66.73, 1, 57, 1584.56, -389.21, 0, 2, 27, 172.5, 39.11, 1, 57, 1600.74, -417.88, 0, 2, 27, 172.09, 15.01, 1, 57, 1598.85, -441.91, 0, 2, 27, 154.81, -33.98, 1, 64, -799.97, 1166.96, 0, 2, 27, 168.63, -45.36, 1, 64, -802.53, 1184.68, 0, 2, 27, 167.49, -53.27, 1, 64, -796.36, 1189.76, 0, 2, 27, 163.32, -58.85, 1, 64, -789.52, 1191.07, 0, 2, 27, 94.05, -48.58, 1, 64, -745.21, 1136.85, 0, 2, 27, 53.37, -53.62, 1, 64, -711.74, 1113.19, 0, 3, 26, 493.18, -35.31, 0.00296, 27, 31.81, -48.67, 0.99704, 64, -699.13, 1095.01, 0, 3, 26, 464.48, -25.06, 0.17489, 27, 8.15, -29.45, 0.82511, 64, -694.59, 1064.87, 0, 3, 26, 451.31, -33.34, 0.51042, 27, -7.03, -32.88, 0.48958, 64, -681.05, 1057.18, 0, 3, 26, 419.3, -29.58, 0.98986, 27, -35.95, -18.68, 0.01014, 64, -669.22, 1027.21, 0, 2, 26, 391.5, -30.46, 1, 64, -655.3, 1003.14, 0, 2, 26, 371.44, -31.57, 1, 64, -644.83, 985.99, 0, 2, 26, 313.84, -40.83, 1, 64, -609.42, 939.63, 0, 2, 26, 157.96, -72.48, 1, 64, -507.79, 817.28, 0, 2, 26, 94.49, -71.06, 1, 64, -479.01, 760.7, 0, 3, 25, 430.82, -65.48, 0.03844, 26, 51.57, -61.24, 0.96156, 64, -467.36, 718.25, 0, 3, 25, 406.6, -57.3, 0.2011, 26, 26.73, -55.2, 0.7989, 64, -460.93, 693.51, 0, 3, 25, 382.86, -58.23, 0.53394, 26, 3.16, -58.17, 0.46606, 64, -447.15, 674.15, 0, 3, 25, 359.81, -59.39, 0.82223, 26, -19.71, -61.33, 0.17777, 64, -433.55, 655.5, 0, 3, 25, 332.38, -58.29, 0.97764, 26, -47.13, -62.6, 0.02236, 64, -419.46, 631.95, 0, 2, 25, 303.73, -59.03, 1, 64, -403.14, 608.38, 0, 3, 3, 396.36, 127.35, 0.00228, 25, 263.19, -68.87, 0.99772, 64, -372.71, 579.85, 0, 2, 25, 182.09, -100.96, 1, 64, -301.44, 529.57, 0, 3, 5, 200.21, 142.31, 8e-05, 25, 145.25, -110.29, 0.99992, 64, -273.46, 503.85, 0, 4, 3, 272.88, 38.57, 0.04977, 25, 118.17, -114.65, 0.90023, 64, -254.98, 483.58, 0, 31, -41.83, -139.27, 0.05, 4, 3, 248.52, 25.97, 0.22185, 25, 91.09, -119.01, 0.56831, 72, -408.52, -86.57, 0.01229, 31, -43.21, -111.88, 0.19754, 4, 3, 248.45, -0.74, 0.26514, 25, 82.68, -144.36, 0.52634, 72, -432.8, -75.45, 0.01065, 31, -67.49, -100.76, 0.19787, 5, 3, 239.63, -20.67, 0.34799, 25, 68.07, -160.54, 0.44679, 72, -447.29, -59.16, 0.01575, 31, -81.98, -84.48, 0.14026, 34, 27.09, -58.36, 0.04921, 6, 5, 162.68, 64.34, 0.05951, 25, 85.25, -172.64, 0.21378, 72, -461.2, -74.91, 0.02682, 28, 36.27, -30.44, 0.51256, 31, -95.88, -100.22, 0.04866, 34, 13.19, -74.11, 0.13868, 4, 5, 198.05, 53.69, 0.13598, 72, -485.54, -102.7, 0.02389, 28, 59.52, -59.14, 0.64491, 34, -11.15, -101.89, 0.19522, 3, 5, 228.14, 36.53, 0.01922, 28, 74.75, -90.26, 0.78078, 34, -39.23, -122.18, 0.2, 3, 5, 206.71, -4.01, 0.07148, 28, 34.23, -111.71, 0.72852, 35, -29.01, -80.28, 0.2, 3, 5, 177.04, -35.77, 0.14355, 28, -8.16, -121.28, 0.65645, 36, -13.42, -35.87, 0.2, 3, 5, 154.06, -50, 0.1982, 28, -35.17, -120.13, 0.6018, 36, -16.86, -9.06, 0.2, 3, 5, 128.12, -56.9, 0.2596, 28, -60.5, -111.24, 0.5404, 36, -12.4, 17.41, 0.2, 3, 5, 105.26, -57.46, 0.32266, 28, -79.72, -98.84, 0.47734, 36, -3.44, 38.45, 0.2, 3, 5, 57.73, -47.3, 0.54137, 28, -113.3, -63.7, 0.25863, 35, -6.73, 73.26, 0.2, 3, 5, 7.39, -23.07, 0.73233, 28, -141.28, -15.35, 0.06767, 34, -2.08, 103.43, 0.2, 5, 6, -42.11, 111.37, 0.02696, 3, 64.15, -92.98, 0.17827, 5, -32.89, -1.84, 0.69477, 64, -73.84, 316.05, 0, 34, 33.92, 131.31, 0.1, 7, 7, -53.21, 123.76, 0.00275, 6, -25.35, 93.81, 0.09892, 3, 42.12, -82.78, 0.39479, 5, -54.92, 8.36, 0.48349, 50, 161.7, 121.19, 5e-05, 64, -77.99, 292.13, 0, 34, 52.33, 147.14, 0.02, 5, 7, -45.76, 102.39, 0.02401, 6, -10.26, 76.94, 0.25776, 3, 21.9, -72.6, 0.47106, 5, -75.13, 18.54, 0.24716, 64, -82.58, 269.97, 0, 5, 7, -25.11, 89.31, 0.11997, 6, 13.83, 72.8, 0.49127, 3, -2.03, -77.57, 0.29028, 5, -99.07, 13.57, 0.09848, 64, -71.58, 248.14, 0, 5, 7, -4.5, 82.52, 0.31934, 6, 35.47, 74.45, 0.5365, 3, -21.56, -87.03, 0.11723, 5, -118.6, 4.11, 0.02694, 64, -57.39, 231.73, 0, 4, 7, 14.78, 79.47, 0.57994, 6, 54.44, 79.05, 0.40409, 3, -37.53, -98.25, 0.01597, 64, -42.41, 219.21, 0, 4, 7, 33.28, 82.1, 0.77186, 6, 70.5, 88.58, 0.22248, 5, -146.03, -21.87, 0.00566, 64, -25.19, 211.97, 0, 4, 7, 67.57, 95.48, 0.942, 6, 97.02, 114.12, 0.05774, 5, -161.36, -55.34, 0.00026, 64, 11.11, 205.83, 0, 3, 7, 96.12, 105.64, 0.99031, 6, 119.47, 134.46, 0.00969, 64, 40.82, 199.88, 0, 5, 7, 81.07, 110.84, 0.99423, 6, 103.58, 133.49, 0.00456, 5, -160.37, -75.77, 0, 64, 30.58, 212.08, 0, 70, 50.75, 438.63, 0.0012, 6, 7, 68.9, 119.46, 0.79987, 6, 89.03, 136.77, 0.00069, 5, -145.63, -73.49, 0, 51, -8.09, 73.78, 0.19631, 64, 24.57, 225.72, 0, 70, 38.58, 447.25, 0.00312, 5, 7, 61.94, 134.63, 0.49351, 5, -130.07, -79.55, 0, 51, 5.12, 63.57, 0.50022, 64, 26.39, 242.32, 0, 70, 31.62, 462.42, 0.00627, 6, 7, 60.1, 150.37, 0.21789, 5, -117.81, -89.57, 0, 50, 69.85, 49.69, 0.00813, 51, 14.05, 50.49, 0.76442, 64, 32.89, 256.76, 0, 70, 29.78, 478.15, 0.00955, 5, 7, 52.95, 155.72, 0.12204, 5, -108.95, -88.44, 0, 51, 22.86, 49.06, 0.86763, 64, 29.5, 265.02, 0, 70, 22.63, 483.5, 0.01032, 5, 7, 45.92, 155.82, 0.10092, 5, -103.83, -83.62, 0, 51, 29.14, 52.23, 0.88926, 64, 23.52, 268.72, 0, 70, 15.59, 483.61, 0.00982, 5, 7, 38.76, 147.73, 0.16991, 5, -104.31, -72.83, 0, 51, 31.73, 62.72, 0.82248, 64, 13.22, 265.46, 0, 70, 8.43, 475.52, 0.0076, 5, 7, 41.99, 159.57, 0.05839, 5, -98.39, -83.58, 0, 51, 34.37, 50.73, 0.93145, 64, 22.08, 273.97, 0, 70, 11.66, 487.36, 0.01016, 6, 7, 53, 162.69, 0.04576, 5, -104.13, -93.48, 0, 51, 26.06, 42.86, 0.94224, 52, -17.95, 44.74, 0.00026, 64, 33.12, 270.98, 0, 70, 22.67, 490.48, 0.01174, 7, 7, 50.01, 173.5, 0.01448, 5, -94.47, -99.17, 0, 50, 88.69, 32.89, 0.01475, 51, 33.72, 34.67, 0.94561, 52, -11.09, 35.87, 0.01157, 64, 36.11, 281.79, 0, 70, 19.69, 501.29, 0.01359, 6, 7, 49.05, 193.07, 0.0022, 5, -80.17, -112.56, 0, 51, 43.64, 17.78, 0.76148, 52, -2.79, 18.12, 0.21957, 64, 45.35, 299.07, 0, 70, 18.73, 520.86, 0.01676, 6, 7, 43.06, 202.56, 0.00034, 5, -69.26, -115.21, 0, 51, 53.35, 12.15, 0.25942, 52, 6.35, 11.61, 0.72407, 64, 45.08, 310.3, 0, 70, 12.74, 530.35, 0.01617, 6, 5, -61.35, -107.72, 0, 51, 63.06, 17.1, 0.01901, 52, 16.48, 15.63, 0.94303, 53, -27.84, 14.45, 0.02402, 64, 35.79, 315.99, 0, 70, 1.84, 530.47, 0.01394, 4, 52, 24.48, 27.31, 0.76645, 53, -20.34, 26.46, 0.22224, 64, 21.64, 315.4, 0, 70, -9.99, 522.69, 0.01131, 4, 52, 31.6, 40.28, 0.55104, 53, -13.79, 39.73, 0.4405, 64, 6.99, 313.32, 0, 70, -21.49, 513.38, 0.00846, 4, 52, 46.02, 48.2, 0.39878, 53, 0.28, 48.26, 0.59612, 64, -7.99, 320.11, 0, 70, -37.84, 511.51, 0.0051, 4, 52, 67.16, 39.47, 0.16967, 53, 21.78, 40.44, 0.82791, 64, -13.68, 342.27, 0, 70, -54.1, 527.6, 0.00242, 3, 53, 35.64, 13.3, 0.99837, 64, 0.9, 369.04, 0, 70, -55.34, 558.06, 0.00163, 3, 53, 39.95, -6.6, 0.99894, 57, 452.83, -390.06, 0, 70, -50.93, 577.93, 0.00106, 4, 52, 77.48, -20.11, 0.00921, 53, 34.64, -18.64, 0.98892, 57, 444.27, -400.04, 0, 70, -41.08, 586.65, 0.00186, 5, 5, -6.9, -126.13, 0, 52, 60.18, -21.71, 0.1381, 53, 17.43, -20.98, 0.85682, 57, 427.11, -397.31, 0, 70, -24.46, 581.58, 0.00508, 5, 5, -18.9, -137.49, 0, 52, 44.82, -27.8, 0.58255, 53, 2.34, -27.72, 0.41003, 57, 410.72, -399.39, 0, 70, -7.94, 581.39, 0.00742, 5, 5, -12.54, -157.08, 0, 52, 43.45, -48.34, 0.79586, 53, 1.85, -48.31, 0.19875, 57, 404.29, -418.96, 0, 70, 1.12, 599.89, 0.00539, 6, 5, -18.99, -172.37, 0, 51, 85.39, -56.9, 3e-05, 52, 31.78, -60.15, 0.83757, 53, -9.3, -60.6, 0.1572, 57, 390.05, -427.49, 0, 70, 16.39, 606.39, 0.0052, 6, 5, -29.6, -179.41, 0, 51, 73.22, -60.65, 6e-05, 52, 19.31, -62.73, 0.85778, 53, -21.65, -63.72, 0.13617, 57, 377.34, -426.9, 0, 70, 28.91, 604.05, 0.00599, 6, 5, -43.39, -179.99, 0, 51, 59.83, -57.3, 7e-05, 52, 6.29, -58.14, 0.86621, 53, -34.85, -59.69, 0.12603, 57, 365.86, -419.23, 0, 70, 39.22, 594.88, 0.00769, 6, 5, -45.05, -175.96, 0, 51, 59.38, -52.97, 8e-05, 52, 6.25, -53.79, 0.86655, 53, -35.07, -55.34, 0.1249, 57, 366.91, -415, 0, 70, 37.6, 590.83, 0.00847, 6, 5, -32.47, -172.68, 0, 51, 72.37, -53.39, 6e-05, 52, 19.14, -55.42, 0.85652, 53, -22.13, -56.42, 0.13619, 57, 378.99, -419.78, 0, 70, 26.29, 597.23, 0.00723, 6, 5, -24.96, -166.43, 0, 51, 81.34, -49.51, 2e-05, 52, 28.44, -52.4, 0.8312, 53, -12.97, -53.01, 0.16201, 57, 388.75, -419.16, 0, 70, 16.54, 597.96, 0.00676, 5, 5, -24.66, -155.93, 0, 52, 32.62, -42.77, 0.78302, 53, -9.2, -43.21, 0.20935, 57, 395.19, -410.87, 0, 70, 9.02, 590.63, 0.00763, 6, 5, -31.76, -146.57, 0, 51, 80.45, -28.54, 0.00783, 52, 29.51, -31.44, 0.8044, 53, -12.8, -32.02, 0.17825, 57, 394.98, -399.12, 0, 70, 7.61, 578.96, 0.00953, 6, 5, -45.39, -143.41, 0, 51, 68.26, -21.65, 0.09732, 52, 18.02, -23.45, 0.85785, 53, -24.62, -24.52, 0.03262, 57, 385.84, -388.53, 0, 70, 15.21, 567.21, 0.0122, 5, 50, 104.43, -32.5, 0.01341, 51, 52.81, -29.82, 0.57589, 52, 1.88, -30.13, 0.39774, 57, 368.54, -390.99, 0, 70, 32.69, 567.28, 0.01295, 5, 50, 99.11, -46.79, 0.03689, 51, 48.24, -44.36, 0.74104, 52, -4.04, -44.18, 0.21133, 57, 359.32, -403.14, 0, 70, 43.49, 578.04, 0.01074, 6, 49, 137.19, -52.49, 0.00216, 50, 85.36, -50.35, 0.07367, 51, 34.69, -48.63, 0.7719, 52, -17.93, -47.16, 0.14172, 57, 345.12, -402.58, 0, 70, 57.47, 575.54, 0.01055, 6, 49, 120.95, -42.66, 0.02447, 50, 68.88, -40.94, 0.2674, 51, 17.74, -40.08, 0.66164, 52, -34, -37.06, 0.03461, 57, 332.06, -388.81, 0, 70, 68.52, 560.1, 0.01188, 6, 49, 112.75, -66.01, 0.06467, 50, 61.26, -64.49, 0.4735, 51, 11.35, -63.99, 0.45427, 52, -42.6, -60.26, 0.00062, 57, 317.97, -409.15, 0, 70, 85.27, 578.32, 0.00694, 5, 49, 97.95, -73.49, 0.07655, 50, 46.66, -72.34, 0.499, 51, -2.83, -72.58, 0.41992, 57, 301.72, -412.44, 0, 70, 101.82, 579.35, 0.00453, 5, 49, 85.96, -65.22, 0.10263, 50, 34.46, -64.37, 0.51438, 51, -15.42, -65.25, 0.37841, 57, 292.35, -401.29, 0, 70, 109.57, 567.01, 0.00458, 6, 5, -127, -193.12, 0, 49, 78.79, -45.59, 0.27195, 50, 26.8, -44.93, 0.54091, 51, -24.08, -46.23, 0.18126, 57, 290.63, -380.47, 0, 70, 108.42, 546.15, 0.00588, 7, 5, -131.31, -212.11, 0, 48, 126.56, -59.61, 0.00128, 49, 68, -61.8, 0.58418, 50, 16.42, -61.41, 0.3689, 51, -33.59, -63.22, 0.0421, 57, 275.93, -393.25, 0, 70, 124.73, 556.79, 0.00354, 7, 5, -138.16, -227.24, 0, 48, 115.16, -71.69, 0.00694, 49, 56.22, -73.51, 0.71007, 50, 4.95, -73.41, 0.26853, 51, -44.43, -75.8, 0.01008, 57, 261.48, -401.42, 0, 70, 140.16, 562.9, 0.00437, 7, 5, -146.29, -241.75, 0, 48, 102.74, -82.75, 0.01712, 49, 43.46, -84.18, 0.77024, 50, -7.54, -84.39, 0.20462, 51, -56.34, -87.41, 0.00101, 57, 246.35, -408.33, 0, 70, 156.1, 567.67, 0.00701, 6, 5, -160.79, -247.96, 0, 48, 87.01, -83.9, 0.03142, 49, 27.7, -84.82, 0.79365, 50, -23.28, -85.43, 0.16772, 57, 230.98, -404.78, 0, 70, 170.84, 562.04, 0.00721, 6, 5, -175.86, -246.04, 0, 48, 73.39, -77.17, 0.05779, 49, 14.3, -77.67, 0.80659, 50, -36.86, -78.62, 0.12968, 57, 219.95, -394.33, 0, 70, 180.33, 550.18, 0.00594, 6, 5, -194.06, -233.64, 0, 48, 60.22, -59.52, 0.14161, 49, 1.69, -59.6, 0.77824, 50, -49.91, -60.88, 0.07733, 57, 212.58, -373.58, 0, 70, 184.78, 528.61, 0.00282, 5, 5, -208.38, -217.93, 0, 47, 120.57, -28.75, 0.00128, 48, 51.8, -40, 0.39882, 49, -6.09, -39.83, 0.5999, 57, 210.3, -352.45, 0, 4, 5, -223.36, -214.94, 0, 47, 106.1, -23.85, 0.02486, 48, 38.61, -32.29, 0.76666, 49, -19.03, -31.7, 0.20848, 4, 47, 94.35, -30.23, 0.09611, 48, 25.82, -36.17, 0.87462, 49, -31.94, -35.17, 0.02924, 70, 206.05, 492.88, 4e-05, 4, 47, 102.04, -38.27, 0.21196, 48, 31.73, -45.6, 0.78005, 49, -26.33, -44.78, 0.00643, 70, 204.69, 503.93, 0.00156, 3, 47, 102.28, -51.36, 0.26248, 48, 29.33, -58.46, 0.7334, 70, 212.31, 514.56, 0.00411, 4, 8, -119.61, 142.35, 0.01702, 47, 81.82, -65.39, 0.25906, 48, 6.47, -68.09, 0.71695, 70, 237.1, 513.6, 0.00696, 4, 8, -97.74, 145.23, 0.06808, 47, 59.78, -66.2, 0.26381, 48, -15.29, -64.44, 0.66086, 70, 255.26, 501.08, 0.00725, 4, 8, -76.84, 137.74, 0.16477, 47, 39.68, -56.76, 0.31338, 48, -33.08, -51.15, 0.51614, 70, 265.74, 481.51, 0.0057, 5, 8, -60, 144.58, 0.28795, 47, 22.27, -61.99, 0.30885, 48, -51.18, -52.76, 0.3962, 57, 108.13, -334.25, 0, 70, 282.83, 475.3, 0.00701, 5, 8, -45.03, 143.27, 0.38308, 47, 7.49, -59.27, 0.27541, 48, -65.11, -47.13, 0.33489, 57, 96.49, -324.75, 0, 70, 293.05, 464.29, 0.00663, 5, 8, -27.39, 133.75, 0.48337, 47, -9.17, -48.13, 0.41878, 48, -79.19, -32.86, 0.09302, 57, 87.25, -306.96, 0, 70, 299.77, 445.41, 0.00483, 6, 9, -87.61, 137.34, 0.07018, 8, -12.97, 137.5, 0.5065, 3, -251.79, -349.26, 0, 47, -23.89, -50.51, 0.41757, 57, 73.21, -301.96, 0, 70, 312.99, 438.52, 0.00575, 6, 9, -67.26, 132.1, 0.17212, 8, 7.38, 132.23, 0.55892, 3, -272.31, -344.69, 0, 47, -43.64, -43.34, 0.26394, 57, 59.35, -286.17, 0, 70, 324.55, 420.97, 0.00502, 6, 9, -48.76, 115.74, 0.15197, 8, 25.84, 115.83, 0.74037, 3, -291.33, -328.95, 0, 47, -60.48, -25.27, 0.10469, 57, 53.29, -262.23, 0, 70, 327.26, 396.43, 0.00297, 5, 9, -32.31, 106, 0.27723, 8, 42.27, 106.06, 0.72128, 3, -308.09, -319.75, 0, 5, -405.13, -228.61, 0, 70, 332.91, 378.16, 0.00149, 5, 9, -22.43, 102.56, 0.35979, 8, 52.15, 102.6, 0.63943, 3, -318.08, -316.64, 0, 5, -415.12, -225.5, 0, 70, 337.92, 368.98, 0.00078, 4, 9, 1.65, 106.66, 0.68255, 8, 76.24, 106.66, 0.31682, 3, -342.02, -321.53, 0, 70, 358.52, 355.84, 0.00064, 3, 9, 24.52, 108.69, 0.99749, 3, -364.8, -324.32, 1e-05, 68, -342.06, 181.27, 0.0025, 2, 9, 48.56, 106.06, 0.99241, 68, -318.02, 178.64, 0.00759, 2, 9, 65.35, 99.84, 0.98725, 68, -301.23, 172.42, 0.01275, 2, 9, 81.65, 90.3, 0.98183, 68, -284.93, 162.89, 0.01817, 2, 9, 97.12, 81.23, 0.97773, 68, -269.46, 153.82, 0.02227, 2, 9, 115.11, 70, 0.9746, 68, -251.47, 142.58, 0.0254, 2, 9, 130.15, 57.76, 0.97436, 68, -236.43, 130.34, 0.02564, 2, 9, 142.38, 45.78, 0.97645, 68, -224.19, 118.36, 0.02355, 2, 9, 152.61, 32.92, 0.97986, 68, -213.96, 105.5, 0.02014, 2, 9, 164.44, 14.78, 0.98588, 68, -202.14, 87.36, 0.01412, 3, 9, 173.08, -6.54, 0.99269, 5, -614.12, -122.9, 0, 68, -193.5, 66.04, 0.00731, 4, 7, 128.27, 113.09, 0.9999, 6, 146.29, 153.71, 0.0001, 5, -192.71, -110.22, 0, 64, 72.24, 189.76, 0, 3, 7, 154.35, 113.46, 1, 5, -211.19, -128.62, 0, 64, 94.8, 176.68, 0, 3, 7, 175.33, 107.73, 1, 5, -230.25, -139.1, 0, 64, 109.86, 160.99, 0, 2, 7, 194.03, 101.47, 1, 5, -248.04, -147.61, 0, 1, 7, 216.93, 91.03, 1, 1, 7, 239.56, 76.46, 1, 2, 7, 263.17, 52.09, 1, 57, 144.53, -232.86, 0, 3, 8, 0.95, 35.19, 0.33276, 7, 285.06, 25.48, 0.66724, 57, 119.2, -209.51, 0, 3, 8, 17.49, 23.65, 0.72868, 7, 289.6, 5.83, 0.27042, 68, -423.53, 96.13, 0.00091, 3, 8, 27.54, 18.25, 0.88425, 7, 293.45, -4.92, 0.11399, 68, -413.46, 90.75, 0.00176, 2, 8, 42.02, 17.57, 0.99609, 68, -398.98, 90.1, 0.00391, 3, 9, -14.85, 21.33, 0.05375, 8, 59.58, 21.36, 0.93992, 68, -381.43, 93.91, 0.00633, 2, 9, 17, 33.99, 0.98939, 68, -349.58, 106.57, 0.01061, 3, 9, 1.95, 27.74, 0.52261, 8, 76.39, 27.73, 0.46806, 68, -364.62, 100.32, 0.00933, 2, 7, 200.01, -29.2, 1, 5, -343.23, -57.9, 0, 2, 7, 217.79, -17.48, 1, 5, -347.85, -78.69, 0, 3, 8, -8.89, -36.57, 0.1, 7, 229.67, -21.18, 0.89471, 5, -358.96, -84.3, 0.00529, 3, 8, 2.67, -32.5, 0.29834, 7, 240.97, -25.91, 0.66239, 5, -370.37, -88.76, 0.03927, 3, 8, 22.3, -33.06, 0.79387, 7, 255.16, -39.48, 0.20613, 5, -390.01, -88.88, 0, 3, 8, 36.88, -37.52, 0.96138, 7, 263, -52.56, 0.03862, 5, -404.74, -84.94, 0, 4, 9, -20.84, -44.52, 0.04841, 8, 53.47, -44.49, 0.95159, 3, -324.52, -169.69, 0, 5, -421.55, -78.55, 0, 4, 9, -6.92, -50.87, 0.44659, 8, 67.37, -50.86, 0.55341, 3, -338.64, -163.8, 0, 5, -435.67, -72.66, 0, 4, 9, 10.97, -58.96, 0.77052, 8, 85.25, -58.98, 0.22948, 3, -356.79, -156.31, 0, 5, -453.82, -65.17, 0, 2, 37, -26.69, 74.38, 0.21374, 7, 195.15, 93.64, 0.78626, 5, 37, -46.67, 71.95, 0.14837, 7, 175.17, 91.2, 0.83986, 5, -241.63, -127.12, 0, 64, 101.23, 146.89, 0, 70, 144.85, 418.99, 0.01177, 5, 37, -67.94, 58.31, 0.11428, 7, 153.9, 77.57, 0.86227, 5, -235.84, -102.53, 0, 64, 75.98, 146.12, 0, 70, 123.58, 405.36, 0.02346, 2, 37, -5.3, 68.26, 0.30146, 7, 216.55, 87.52, 0.69854, 2, 37, 15.59, 56.22, 0.41127, 7, 237.44, 75.48, 0.58873, 3, 37, 31.11, 36.36, 0.51125, 7, 252.96, 55.61, 0.48773, 70, 222.63, 383.4, 0.00101, 3, 37, 35.27, 16.36, 0.59569, 7, 257.12, 35.61, 0.39871, 70, 226.79, 363.4, 0.0056, 4, 37, 33.55, 2.27, 0.59402, 7, 255.4, 21.52, 0.3965, 57, 148.03, -201.52, 0, 70, 225.08, 349.31, 0.00949, 3, 37, -56.29, -15.28, 0.43007, 7, 165.56, 3.98, 0.55691, 70, 135.24, 331.77, 0.01303, 4, 37, -38.41, -27.59, 0.5381, 7, 183.44, -8.33, 0.45094, 57, 215.21, -162.07, 0, 70, 153.12, 319.46, 0.01096, 3, 37, -17.97, -34.41, 0.61583, 7, 203.87, -15.16, 0.37968, 70, 173.55, 312.63, 0.00449, 4, 37, 0.96, -23.33, 0.66184, 7, 222.81, -4.07, 0.32921, 5, -342.13, -91.81, 0, 70, 192.48, 323.72, 0.00896, 4, 37, 17.85, -10.59, 0.60265, 7, 239.7, 8.66, 0.38336, 57, 161.82, -186.62, 0, 70, 209.37, 336.45, 0.01399, 4, 37, -78.08, 32.5, 0.16521, 7, 143.76, 51.76, 0.80355, 64, 54.02, 129.18, 0, 70, 113.44, 379.54, 0.03124, 4, 37, -73.39, 4.47, 0.27629, 7, 148.46, 23.72, 0.69845, 64, 43.65, 102.72, 0, 70, 118.14, 351.51, 0.02526, 2, 37, -10.05, 15.77, 0.81999, 7, 211.8, 35.02, 0.18001, 4, 37, 42.16, 14.53, 0.3558, 7, 264.01, 33.78, 0.63585, 57, 141.19, -214.84, 0, 70, 233.68, 361.57, 0.00835, 3, 8, -18.06, 39.09, 0.06, 7, 273.57, 41.12, 0.94, 57, 132.72, -223.43, 0, 4, 37, 50.75, 11.75, 0.24821, 7, 272.6, 31.01, 0.74721, 57, 132.3, -213.28, 0, 70, 242.27, 358.8, 0.00458, 4, 37, 46.63, 2.08, 0.4077, 7, 268.48, 21.33, 0.58845, 57, 135.05, -203.13, 0, 70, 238.15, 349.12, 0.00385, 3, 37, 35.62, -8.82, 0.55281, 7, 257.47, 10.43, 0.44719, 57, 144.46, -190.82, 0, 3, 37, 22.69, -18.96, 0.63312, 7, 244.53, 0.3, 0.36688, 57, 155.88, -179.01, 0, 3, 37, 3.53, -31.51, 0.60616, 7, 225.38, -12.26, 0.39384, 5, -349.67, -87.72, 0, 3, 37, -15.61, -42.11, 0.41977, 7, 206.24, -22.85, 0.58023, 5, -343.29, -66.79, 0, 3, 37, -34.93, -53.76, 0.24288, 7, 186.92, -34.5, 0.7456, 70, 156.6, 293.29, 0.01152, 3, 37, -35.32, -43.22, 0.39456, 7, 186.52, -23.97, 0.59341, 70, 156.2, 303.82, 0.01203, 4, 7, 156.67, 109.33, 0.99213, 5, -215.73, -127.27, 0, 64, 94.67, 171.94, 0, 70, 126.35, 437.11, 0.00787, 5, 7, 127.9, 101.52, 0.98204, 6, 150.4, 142.87, 0.00047, 5, -200.5, -101.64, 0, 64, 65.97, 180.02, 0, 70, 97.58, 429.3, 0.01749, 4, 7, 96.35, 92.27, 0.96911, 6, 124.83, 122.22, 0.01525, 64, 34.15, 188.29, 0, 70, 66.03, 420.06, 0.01563, 4, 37, -58.21, -33, 0.24899, 7, 163.64, -13.74, 0.72973, 57, 234.08, -153.99, 0, 70, 133.32, 314.05, 0.02128, 4, 37, -82.78, -5.4, 0.19588, 7, 139.07, 13.86, 0.77731, 64, 30.53, 99.07, 0, 70, 108.75, 341.65, 0.02681, 5, 37, -117.46, 3.78, 0.0635, 7, 104.39, 23.04, 0.90278, 5, -238.21, -28.91, 0, 64, 5.49, 124.76, 0, 70, 74.07, 350.82, 0.03372, 5, 7, 68.4, 18.78, 0.91502, 6, 127.27, 43.62, 0.00221, 64, -27.58, 139.59, 0, 70, 38.08, 346.56, 0.03449, 54, -0.26, 93.73, 0.04828, 3, 7, 32.7, -6.13, 0.86648, 70, 2.37, 321.66, 0.03724, 54, -35.96, 68.82, 0.09628, 4, 7, 3.58, -33.73, 0.57265, 6, 87.61, -29.76, 0.20096, 70, -26.74, 294.06, 0.03298, 54, -65.08, 41.22, 0.1934, 5, 37, -53.27, -51.76, 0.11653, 7, 168.58, -32.5, 0.81041, 57, 226.61, -136.08, 0, 70, 138.26, 295.29, 0.02427, 54, 99.92, 42.45, 0.04879, 5, 37, -56.45, -66.14, 0.06109, 7, 165.4, -46.88, 0.86859, 57, 227.78, -121.4, 0, 70, 135.08, 280.9, 0.02139, 54, 96.74, 28.07, 0.04893, 4, 7, 90.42, -92.89, 0.768, 57, 295.74, -65.52, 0, 70, 60.09, 234.89, 0.04, 55, 46.51, 17.02, 0.192, 5, 7, 48.61, -73.91, 0.74948, 6, 144.62, -49.55, 0.01273, 57, 339.76, -78.59, 0, 70, 18.28, 253.88, 0.04724, 55, 4.7, 36, 0.19055, 5, 7, 8.05, -75.42, 0.66054, 6, 107.76, -66.53, 0.10746, 57, 379.72, -71.52, 0, 70, -22.27, 252.37, 0.04, 55, -35.85, 34.49, 0.192, 5, 7, 48, -139.59, 0.76304, 6, 169.3, -110.42, 0.00496, 57, 331.34, -13.44, 0, 70, 17.68, 188.2, 0.04, 66, 15.87, 6.02, 0.192, 5, 7, 19.75, -132.41, 0.74709, 6, 140.46, -114.65, 0.02091, 57, 360.3, -16.68, 0, 70, -10.57, 195.38, 0.04, 66, -12.38, 13.2, 0.192, 4, 7, 123.86, -45.5, 0.76776, 57, 269.12, -117.07, 0, 70, 93.53, 282.29, 0.0403, 54, 55.2, 29.45, 0.19194, 4, 7, 134.84, -65.51, 0.77714, 57, 255.5, -98.75, 0, 70, 104.51, 262.28, 0.02858, 54, 66.17, 9.44, 0.19428, 4, 7, 91.15, -27.74, 0.76216, 57, 303.96, -130.17, 0, 70, 60.83, 300.05, 0.0473, 54, 22.49, 47.22, 0.19054, 4, 7, 44.78, -23.14, 0.7648, 57, 350.53, -128.35, 0, 70, 14.45, 304.65, 0.044, 54, -23.89, 51.82, 0.1912, 5, 7, 70.86, -121.57, 0.76707, 6, 183.48, -84.99, 0.00093, 57, 311.16, -34.44, 0, 70, 40.54, 206.22, 0.04, 56, 33.97, 10.12, 0.192, 5, 7, 43.38, -108.4, 0.7514, 6, 153.04, -83.4, 0.01297, 57, 340.2, -43.71, 0, 70, 13.05, 219.39, 0.04455, 56, 6.49, 23.29, 0.19109, 5, 7, 11.32, -107.58, 0.72041, 6, 123.13, -94.97, 0.04759, 57, 372.07, -40.11, 0, 70, -19.01, 220.21, 0.04, 56, -25.57, 24.1, 0.192, 4, 7, 99.02, 56.39, 0.96485, 6, 141.08, 90.12, 0.0056, 64, 18.01, 156.14, 0, 70, 68.69, 384.18, 0.02954, 4, 37, -97.61, 37.34, 0.09986, 7, 124.24, 56.6, 0.87104, 64, 39.76, 143.36, 0, 70, 93.91, 384.39, 0.0291, 4, 7, 32.84, 23.92, 0.91543, 6, 92.46, 34.71, 0.04948, 64, -55.45, 162.27, 0, 70, 2.52, 351.71, 0.03508, 5, 7, 6.73, 22.93, 0.55628, 6, 68.74, 23.75, 0.41153, 64, -78.36, 174.83, 0, 70, -23.59, 350.72, 0.02854, 72, -347.2, 236.65, 0.00365, 6, 7, -19.6, 26.13, 0.06119, 6, 43.2, 16.59, 0.89862, 3, -49.94, -36.02, 0.00569, 64, -99.31, 191.11, 0, 70, -49.93, 353.92, 0.02326, 72, -341.38, 210.77, 0.01124, 6, 7, -44.93, 33.1, 0.00948, 6, 17.13, 13.29, 0.88063, 3, -26.89, -23.4, 0.07355, 64, -117.46, 210.1, 0, 70, -75.26, 360.89, 0.01647, 72, -339.44, 184.57, 0.01988, 6, 7, -59.03, 46.22, 0.00629, 6, -0.93, 19.98, 0.59843, 3, -7.64, -23.02, 0.36007, 64, -122.82, 228.59, 0, 70, -89.36, 374.01, 0.01036, 72, -347.06, 166.88, 0.02484, 8, 7, -72.7, 71.27, 0.00377, 6, -23.17, 37.86, 0.17331, 3, 19.6, -31.51, 0.75173, 64, -121.68, 257.11, 0, 70, -103.02, 399.06, 0.00238, 72, -366.07, 145.6, 0.03051, 31, -0.75, 120.29, 0.01896, 34, 108.32, 146.41, 0.01934, 5, 6, -54.26, 65.68, 0.04611, 3, 58.72, -46.01, 0.72562, 64, -117.8, 298.65, 0, 72, -395.47, 116, 0.03533, 31, -30.15, 90.69, 0.19293, 6, 6, -79.17, 89.97, 0.01121, 3, 90.8, -59.5, 0.77088, 64, -113.09, 333.12, 0, 72, -421.02, 92.39, 0.03447, 31, -55.7, 67.07, 0.0869, 34, 53.37, 93.19, 0.09655, 4, 3, 129.45, -70.61, 0.78459, 72, -447.14, 61.8, 0.03137, 31, -81.82, 36.49, 0.08718, 34, 27.25, 62.6, 0.09686, 5, 3, 175.8, -69.79, 0.72281, 25, -7.89, -187.28, 0.06285, 72, -465.58, 19.27, 0.03004, 31, -100.26, -6.04, 0.0873, 34, 8.81, 20.07, 0.097, 5, 3, 214.06, -50.16, 0.61932, 25, 34.57, -180.57, 0.16954, 72, -463.55, -23.68, 0.0261, 31, -98.24, -48.99, 0.08765, 34, 10.83, -22.87, 0.09739, 4, 7, -16.77, -23.86, 0.35264, 6, 65.03, -28.48, 0.61702, 70, -47.09, 303.92, 0.02713, 72, -295.23, 230.23, 0.00321, 5, 7, -38.79, -13.99, 0.04766, 6, 40.9, -27.82, 0.91744, 4, -90.49, -51.33, 0.00947, 70, -69.11, 313.8, 0.01632, 72, -297.14, 206.17, 0.00911, 6, 7, -63.28, -6.47, 0.00617, 6, 15.41, -30.3, 0.84237, 4, -67.67, -39.69, 0.10219, 3, -41.25, 17.79, 0.02608, 70, -93.6, 321.32, 0.00581, 72, -296, 180.58, 0.01737, 4, 6, -8.24, -31.53, 0.51549, 4, -46.11, -29.89, 0.25006, 3, -19.69, 27.59, 0.21029, 72, -296, 156.9, 0.02415, 5, 6, -35.36, -36.38, 0.15027, 4, -22.66, -15.44, 0.54119, 3, 3.77, 42.04, 0.23707, 72, -292.56, 129.56, 0.02261, 31, 72.76, 104.25, 0.04887, 5, 6, -61.11, -46.9, 0.00821, 4, -2.55, 3.78, 0.86984, 25, -111.3, -15.34, 0.00631, 72, -283.39, 103.3, 0.01738, 31, 81.92, 77.99, 0.09826, 6, 4, 15.32, 15.78, 0.65935, 3, 41.74, 73.26, 0.02104, 25, -90.58, -9.52, 0.10457, 64, -228.61, 251.35, 0, 72, -279.87, 82.07, 0.0188, 31, 85.45, 56.76, 0.19624, 6, 4, 47.32, 39.89, 0.35435, 3, 73.75, 97.37, 0.03445, 25, -52.65, 3.39, 0.39204, 64, -260.18, 276.02, 0, 72, -271.17, 42.96, 0.02395, 31, 94.14, 17.65, 0.19521, 5, 3, 234.55, 57.03, 0.26939, 25, 87.51, -85.14, 0.51052, 64, -262.88, 441.78, 0, 72, -374.47, -86.71, 0.02511, 31, -9.15, -112.02, 0.19498, 5, 3, 210.05, 86.89, 0.17384, 25, 73.56, -49.12, 0.59922, 64, -285.37, 410.37, 0, 72, -337.14, -76.77, 0.03368, 31, 28.18, -102.08, 0.19326, 5, 3, 182.29, 108.81, 0.05894, 25, 54.03, -19.63, 0.71471, 64, -299.35, 377.89, 0, 72, -305.7, -60.58, 0.03293, 31, 59.62, -85.89, 0.19341, 5, 3, 143.99, 115.81, 0.00189, 25, 19.84, -1.02, 0.77566, 64, -296.19, 339.08, 0, 72, -283.47, -28.62, 0.02807, 31, 81.85, -53.93, 0.19439, 6, 4, 82.57, 52.93, 0.07454, 3, 109, 110.41, 0.00852, 25, -15.09, 4.77, 0.69649, 64, -281.91, 306.69, 0, 72, -273.9, 5.47, 0.02556, 31, 91.42, -19.84, 0.19489, 6, 6, -44.31, 24.99, 0.01139, 4, 8.14, -69.27, 0.04389, 3, 34.56, -11.79, 0.71272, 64, -144.6, 266.45, 0, 72, -354.31, 123.82, 0.04, 31, 11.01, 98.51, 0.192, 6, 6, -48.96, -14.34, 0.00448, 4, -1.93, -30.98, 0.29502, 3, 24.5, 26.5, 0.47395, 25, -121.57, -48.55, 0.00039, 72, -315.28, 117.13, 0.0327, 31, 50.03, 91.82, 0.19346, 4, 3, 157.89, -15.22, 0.6762, 25, -7.87, -129.84, 0.0138, 72, -408.49, 12.98, 0.08, 33, -10.45, -1.53, 0.23, 4, 3, 225.71, -1.74, 0.41663, 25, 60.77, -138.22, 0.35221, 72, -424.31, -54.34, 0.03895, 32, -41.34, -73.48, 0.19221, 4, 3, 215.99, 34.19, 0.39049, 25, 62.75, -101.05, 0.37214, 72, -387.58, -60.36, 0.04671, 32, -4.61, -79.51, 0.19066, 5, 3, 192.51, 64.35, 0.31633, 25, 49.86, -65.06, 0.44047, 64, -259.06, 399.27, 0, 72, -350.39, -51.47, 0.05399, 32, 32.57, -70.62, 0.1892, 6, 4, 129.56, 17.1, 0.00404, 3, 155.98, 74.58, 0.24566, 25, 18.35, -43.94, 0.50921, 64, -259.47, 361.34, 0, 72, -325.97, -22.46, 0.05135, 32, 57, -41.6, 0.18973, 6, 4, 89.35, 11.74, 0.09188, 3, 115.78, 69.23, 0.25214, 25, -21.51, -36.47, 0.41746, 64, -243.89, 323.9, 0, 72, -314.19, 16.35, 0.04815, 32, 68.77, -2.79, 0.19037, 6, 4, 56.03, -6.71, 0.25028, 3, 82.46, 50.77, 0.33768, 25, -58.93, -43.59, 0.17387, 64, -217.43, 296.5, 0, 72, -317.19, 54.32, 0.04771, 32, 65.77, 35.18, 0.19046, 5, 4, 37.53, -38.71, 0.11837, 3, 63.95, 18.77, 0.61826, 25, -86.5, -68.22, 0.0219, 72, -338.66, 84.42, 0.05184, 32, 44.3, 65.28, 0.18963, 6, 6, -77.58, 44.6, 0.00275, 4, 46.28, -75.34, 0.05184, 3, 72.7, -17.86, 0.70141, 64, -148.62, 304.86, 0, 72, -375.63, 91.62, 0.055, 32, 7.34, 72.47, 0.189, 6, 6, -91.58, 83.55, 0.00171, 4, 73.57, -106.46, 0.03899, 3, 99.99, -48.98, 0.7175, 64, -125.63, 339.28, 0, 72, -415.25, 79.66, 0.05224, 32, -32.29, 60.52, 0.18955, 3, 3, 135.84, -59.17, 0.75985, 72, -439.37, 51.25, 0.05019, 32, -56.4, 32.11, 0.18996, 4, 3, 173, -55.21, 0.73001, 25, -6.01, -172.55, 0.03042, 72, -451.15, 15.78, 0.04947, 32, -68.19, -3.36, 0.19011, 4, 3, 207.46, -32.23, 0.64477, 25, 33.91, -161.48, 0.11908, 72, -444.5, -25.1, 0.04518, 32, -61.54, -44.25, 0.19096, 4, 4, 93.17, -84.96, 0.02976, 3, 119.59, -27.48, 0.71424, 72, -403.8, 52.92, 0.07, 33, -5.76, 38.4, 0.186, 6, 4, 84.22, -55.13, 0.04901, 3, 110.65, 2.35, 0.69155, 25, -47.27, -98.4, 0.00344, 64, -177.97, 336.27, 0, 72, -372.94, 48.71, 0.07, 33, 25.1, 34.19, 0.186, 6, 4, 102.33, -32.26, 0.08225, 3, 128.75, 25.22, 0.57061, 25, -22.93, -82.32, 0.09114, 64, -204.75, 347.83, 0, 72, -359.61, 22.76, 0.07, 33, 38.43, 8.24, 0.186, 6, 4, 133.15, -26.33, 0.07699, 3, 159.57, 31.15, 0.52189, 25, 8.2, -86.31, 0.14512, 64, -218.46, 376.06, 0, 72, -366.98, -7.75, 0.07, 33, 31.06, -22.26, 0.186, 6, 4, 159.04, -39.21, 0.05967, 3, 185.46, 18.27, 0.54076, 25, 28.78, -106.63, 0.14542, 64, -212.73, 404.41, 0, 72, -389.42, -25.98, 0.06769, 33, 8.62, -40.5, 0.18646, 5, 4, 167.93, -70.61, 0.03208, 3, 194.35, -13.13, 0.59691, 25, 27.42, -139.24, 0.11961, 72, -421.68, -21.08, 0.06425, 33, -23.64, -35.59, 0.18715, 5, 4, 150.32, -92.87, 0.02467, 3, 176.74, -35.39, 0.68929, 25, 3.74, -154.89, 0.03343, 72, -434.66, 4.17, 0.06577, 33, -36.62, -10.34, 0.18685, 4, 4, 116.97, -99.18, 0.02551, 3, 143.4, -41.7, 0.71849, 72, -426.59, 37.14, 0.07, 33, -28.55, 22.62, 0.186, 6, 5, 159.29, 43.88, 0.12718, 25, 75.64, -191.02, 0.05287, 70, -262.12, 575.06, 0.00881, 72, -478.42, -63.36, 0.03224, 28, 21.96, -45.45, 0.58711, 35, 34.21, -56.94, 0.19179, 5, 5, 97.74, -40.33, 0.52016, 70, -159.32, 592.74, 0.01055, 72, -529.59, 27.54, 0.01785, 28, -76.3, -80.45, 0.25712, 36, 15.26, 38.2, 0.19432, 5, 5, 137.85, -40.2, 0.40936, 70, -188.23, 620.54, 0.00706, 72, -546.07, -9.03, 0.01527, 28, -43.06, -102.9, 0.3239, 36, -1.22, 1.63, 0.24442, 5, 5, 116.19, -21.13, 0.33652, 70, -185.93, 591.77, 0.01433, 72, -519.75, 2.79, 0.0276, 28, -50.24, -74.95, 0.42993, 36, 25.1, 13.46, 0.19161, 5, 5, 147.89, -9.89, 0.29673, 70, -216.52, 605.75, 0.01505, 72, -522.65, -30.71, 0.02811, 28, -17.71, -83.49, 0.46874, 36, 22.2, -20.05, 0.19137, 5, 5, 172.71, -17.56, 0.3105, 70, -229.02, 628.53, 0.01056, 72, -539.9, -50.14, 0.01644, 28, -1.5, -103.79, 0.4679, 36, 4.95, -39.47, 0.1946, 5, 5, 46.2, -13.16, 0.62183, 70, -141.2, 537.36, 0.01, 72, -483.51, 63.2, 0.02763, 28, -103.62, -28.99, 0.14807, 35, 29.11, 69.62, 0.19247, 6, 5, 191.21, 24.06, 0.16883, 25, 99.77, -219.81, 5e-05, 70, -271.26, 611.5, 0.01, 72, -509.67, -84.2, 0.0224, 28, 37.2, -79.79, 0.60521, 35, 2.95, -77.78, 0.19352, 8, 6, -71.38, 117.89, 0.00599, 3, 93.77, -88.33, 0.09671, 5, -3.27, 2.81, 0.63798, 64, -86.01, 343.46, 0, 70, -116.77, 491.47, 0.00302, 72, -448.5, 101.61, 0.02795, 28, -135.54, 12.04, 0.03455, 34, 25.89, 102.42, 0.19381, 5, 4, -21.32, -51.61, 0.09867, 3, 5.1, 5.87, 0.81764, 70, -118.61, 362.12, 0.0048, 72, -326.04, 143.32, 0.03066, 31, 39.28, 118.01, 0.04823, 5, 6, -5.16, -5.96, 0.65132, 4, -39.62, -54.81, 0.05914, 3, -13.2, 2.67, 0.25403, 70, -103.23, 351.69, 0.01064, 72, -321.38, 161.31, 0.02487, 4, 7, 66.11, 58.72, 0.92348, 6, 109.8, 79.61, 0.04994, 64, -9.03, 175.04, 0, 70, 35.78, 386.51, 0.02658, 4, 7, 32.73, 54.35, 0.77522, 6, 80.66, 62.76, 0.20006, 64, -39.91, 188.44, 0, 70, 2.41, 382.14, 0.02471, 6, 7, 9.41, 52.41, 0.52877, 6, 59.88, 52, 0.44713, 3, -52.5, -75.08, 0.00448, 64, -60.92, 198.75, 0, 70, -20.91, 380.2, 0.01892, 72, -375.87, 229.28, 0.0007, 6, 7, -10.57, 53.88, 0.26354, 6, 40.87, 45.68, 0.64912, 3, -37.12, -62.23, 0.06848, 64, -77.31, 210.27, 0, 70, -40.9, 381.67, 0.0128, 72, -370.54, 209.96, 0.00606, 6, 7, -34.43, 61.33, 0.08288, 6, 15.98, 43.38, 0.65421, 3, -14.8, -50.98, 0.24447, 64, -93.96, 228.92, 0, 70, -64.76, 389.11, 0.0047, 72, -369.55, 184.98, 0.01374, 5, 7, -48.84, 72.47, 0.03895, 6, -1.61, 48.13, 0.45947, 3, 3.3, -48.97, 0.48228, 64, -100.6, 245.89, 0, 72, -375.21, 167.67, 0.0193, 6, 7, -60.66, 91.89, 0.01558, 6, -19.98, 61.52, 0.26571, 3, 25.3, -54.69, 0.69423, 5, -71.74, 36.45, 0.00137, 64, -100.76, 268.61, 0, 72, -389.53, 150.02, 0.02311, 8, 7, -68.63, 114.17, 0.00279, 6, -35.9, 79.03, 0.12674, 3, 46.52, -65.16, 0.63762, 5, -50.51, 25.98, 0.13906, 50, 171.7, 136.34, 4e-05, 64, -96.15, 291.83, 0, 72, -407.84, 135.03, 0.02554, 34, 66.54, 135.83, 0.06821, 6, 6, -51.1, 95.58, 0.05446, 3, 66.73, -74.99, 0.48109, 5, -30.31, 16.15, 0.32189, 64, -91.89, 313.89, 0, 72, -425.16, 120.71, 0.02563, 34, 49.23, 121.51, 0.11692, 4, 25, 56.99, 25.15, 0.92694, 57, 710.64, -146.84, 0, 72, -261.51, -68.4, 0.02428, 31, 103.81, -93.72, 0.04879, 5, 4, 107.73, 76.69, 0.00043, 25, 16.22, 19.49, 0.87797, 57, 670.12, -139.63, 0, 72, -262.68, -27.26, 0.02401, 31, 102.63, -52.57, 0.0976, 5, 3, 255.52, 72.83, 0.21409, 25, 112.37, -76.68, 0.71597, 64, -283.57, 457.94, 0, 72, -368.77, -112.34, 0.02099, 31, -3.45, -137.65, 0.04895, 4, 3, 286.24, 90.69, 0.11345, 25, 147.13, -69.31, 0.86667, 64, -308.78, 482.99, 0, 72, -365.23, -147.7, 0.01988, 4, 3, 320.04, 113.18, 0.03699, 25, 186.26, -58.49, 0.94404, 64, -339.26, 509.8, 0, 72, -358.75, -187.78, 0.01898, 4, 3, 395.43, 169.49, 5e-05, 25, 275.47, -28.54, 0.98057, 64, -413.17, 568.04, 0, 72, -338.71, -279.72, 0.01938, 3, 25, 279.25, 23.45, 0.98027, 57, 921.51, -217.09, 0, 72, -287.44, -289.15, 0.01973, 3, 25, 189.71, 34.38, 0.98021, 57, 839.72, -179.05, 0, 72, -266.81, -201.34, 0.01979, 3, 25, 137.25, 36.92, 0.97948, 57, 790.62, -160.43, 0, 72, -258.56, -149.47, 0.02052, 3, 25, 86.35, 34.6, 0.97753, 57, 741.49, -146.92, 0, 72, -255.32, -98.62, 0.02247, 5, 3, 221.17, 121.3, 0.04194, 25, 94.87, -19.91, 0.88123, 64, -321.49, 412.2, 0, 72, -310.42, -101.14, 0.02824, 31, 54.89, -126.45, 0.04859, 4, 3, 262.52, 141.41, 0.01204, 25, 140.44, -13.72, 0.96115, 64, -351.62, 446.94, 0, 72, -309.24, -147.11, 0.02681, 4, 3, 309.39, 160.78, 0.00208, 25, 191.01, -9.95, 0.97526, 64, -382.48, 487.19, 0, 72, -311.01, -197.8, 0.02267, 3, 25, 313.33, -28.87, 0.98055, 64, -433.64, 599.9, 0, 72, -343.17, -317.32, 0.01945, 4, 25, 339.31, -28.47, 0.96948, 26, -42.81, -32.3, 0.01119, 64, -448.2, 621.41, 0, 72, -345.61, -343.19, 0.01933, 4, 25, 363.32, -31.11, 0.85772, 26, -18.66, -32.85, 0.12265, 64, -459.14, 642.94, 0, 72, -350.84, -366.77, 0.01964, 4, 25, 388.98, -32.47, 0.39699, 26, 7.02, -31.98, 0.58373, 64, -472.06, 665.15, 0, 72, -355, -392.12, 0.01928, 4, 25, 413.4, -33.26, 0.10088, 26, 31.43, -30.65, 0.88015, 64, -484.77, 686.03, 0, 72, -358.45, -416.32, 0.01896, 4, 25, 444.79, -42.08, 0.00818, 26, 63.46, -36.72, 0.97373, 64, -494.59, 717.11, 0, 72, -370.64, -446.55, 0.01809, 3, 26, 100.85, -40.71, 0.98412, 64, -508.76, 751.95, 0, 72, -381.84, -482.45, 0.01588, 3, 26, 152, -38.83, 0.98898, 64, -534.62, 796.12, 0, 72, -389.96, -532.99, 0.01102, 2, 26, 309.08, -5.27, 1, 64, -638.49, 918.62, 0, 3, 26, 150.28, 26.2, 0.98924, 57, 1163.2, -306.47, 0, 72, -325.84, -543.96, 0.01076, 3, 26, 100.76, 23.44, 0.98516, 57, 1116.53, -289.7, 0, 72, -318.9, -494.86, 0.01484, 3, 26, 63.59, 20.77, 0.98275, 57, 1081.27, -277.66, 0, 72, -314.28, -457.89, 0.01725, 3, 26, 30.96, 16.05, 0.98191, 57, 1049.38, -269.28, 0, 72, -312.55, -424.96, 0.01809, 4, 25, 392.43, 16.11, 0.25335, 26, 6.25, 16.72, 0.72883, 57, 1026.9, -259.03, 0, 72, -307.08, -400.86, 0.01782, 4, 25, 366.72, 17.25, 0.95262, 26, -19.46, 15.63, 0.02922, 57, 1002.79, -250, 0, 72, -303.14, -375.42, 0.01816, 3, 25, 343.44, 16.6, 0.98121, 57, 980.45, -243.43, 0, 72, -301.25, -352.21, 0.01879, 3, 25, 315.93, 19.78, 0.98092, 57, 955.26, -231.91, 0, 72, -295.09, -325.21, 0.01908, 2, 9, 56.46, 66.73, 0.98029, 68, -310.11, 139.32, 0.01971, 2, 9, 38.24, 64.64, 0.98048, 68, -328.34, 137.23, 0.01952, 3, 9, 6.9, 42.52, 0.63767, 8, 81.36, 42.5, 0.35233, 68, -359.68, 115.1, 0.01, 2, 9, 34.45, 31.05, 0.97184, 68, -332.13, 103.63, 0.02816, 2, 9, 68.5, 27.13, 0.97008, 68, -298.08, 99.71, 0.02992, 2, 9, 51.41, 29.28, 0.9703, 68, -315.17, 101.86, 0.0297, 2, 9, 84.55, 20.6, 0.96856, 68, -282.03, 93.18, 0.03144, 4, 9, 102.99, -1.63, 0.96284, 3, -446.87, -216.64, 0, 5, -543.9, -125.5, 0, 68, -263.59, 70.95, 0.03716, 4, 9, 111.26, -6.61, 0.96337, 3, -455.29, -211.93, 0, 5, -552.33, -120.79, 0, 68, -255.32, 65.97, 0.03663, 4, 9, 120.29, -10, 0.96448, 3, -464.43, -208.85, 0, 5, -561.47, -117.71, 0, 68, -246.29, 62.59, 0.03552, 4, 9, 132.91, -9.27, 0.9675, 3, -477.03, -209.99, 0, 5, -574.06, -118.85, 0, 68, -233.67, 63.31, 0.0325, 4, 9, 130.31, -35.89, 0.96915, 3, -475.3, -183.3, 0, 5, -572.34, -92.16, 0, 68, -236.27, 36.69, 0.03085, 4, 9, 125.97, -50.19, 0.97342, 3, -471.43, -168.87, 0, 5, -568.47, -77.72, 0, 68, -240.61, 22.4, 0.02658, 4, 9, 127.63, -57.79, 0.97695, 3, -473.35, -161.32, 0, 5, -570.38, -70.18, 0, 68, -238.95, 14.79, 0.02305, 2, 9, 38.9, 35.86, 0.97285, 68, -327.67, 108.44, 0.02715, 2, 9, 56, 46.05, 0.97519, 68, -310.58, 118.63, 0.02481, 2, 9, 61.41, 56.68, 0.9784, 68, -305.17, 129.27, 0.0216, 4, 9, 106.99, -65.18, 0.985, 3, -452.96, -153.26, 0, 5, -550, -62.12, 0, 68, -259.58, 7.41, 0.015, 4, 9, 84.93, -69.14, 0.995, 3, -431.04, -148.57, 0, 5, -528.08, -57.43, 0, 68, -281.65, 3.44, 0.005, 3, 9, 59.88, -67.99, 1, 3, -405.96, -148.9, 0, 5, -503, -57.75, 0, 3, 9, 35.34, -64.6, 1, 3, -381.33, -151.47, 0, 5, -478.36, -60.33, 0, 4, 9, 145.26, -39.18, 0.96702, 3, -490.35, -180.5, 0, 5, -587.39, -89.36, 0, 68, -221.32, 33.4, 0.03298, 4, 9, 151.59, -54.94, 0.97069, 3, -497.2, -164.96, 0, 5, -594.23, -73.82, 0, 68, -214.99, 17.64, 0.02931, 3, 9, 147.67, -73.43, 0.94899, 57, -2.4, 4.74, 0.02585, 68, -218.91, -0.85, 0.02516, 3, 57, 14.51, 10.36, 0.96463, 58, -35.99, -1.5, 0.01398, 68, -229.71, -15.02, 0.02139, 3, 57, 32.21, 12.05, 0.76274, 58, -19.81, 5.9, 0.21873, 68, -243.38, -26.39, 0.01853, 4, 9, 128.62, -68.48, 0.9789, 3, -474.68, -150.67, 0, 5, -571.72, -59.53, 0, 68, -237.96, 4.11, 0.0211, 6, 9, 119.21, -81.15, 0.55789, 3, -465.7, -137.7, 0, 5, -562.74, -46.56, 0, 57, 25.46, -4.93, 0.33636, 58, -20.64, -12.36, 0.08395, 68, -247.37, -8.56, 0.0218, 6, 9, 106.28, -91.12, 0.05937, 3, -453.1, -127.31, 0, 5, -550.14, -36.17, 0, 57, 41.76, -3.97, 0.45283, 58, -5.54, -6.12, 0.46825, 68, -260.3, -18.54, 0.01955, 2, 58, 22.36, 9.92, 0.98197, 68, -283.01, -41.34, 0.01803, 3, 58, 40.93, 12.94, 0.89718, 59, -16.98, 4.84, 0.08631, 68, -300.15, -49.11, 0.01651, 3, 58, 56.66, 11.1, 0.50003, 59, -2.57, 11.42, 0.48388, 68, -315.81, -51.46, 0.01609, 2, 58, 8.45, 0.78, 0.9802, 68, -271.99, -28.87, 0.0198, 6, 9, 104.34, -79.18, 0.42477, 3, -450.77, -139.18, 0, 5, -547.81, -48.03, 0, 57, 36.64, -14.93, 0.20902, 58, -6.8, -18.15, 0.35684, 68, -262.24, -6.6, 0.00937, 5, 9, 89.14, -87.91, 0.28311, 3, -435.86, -129.95, 0, 5, -532.9, -38.81, 0, 58, 10.16, -13.72, 0.7065, 68, -277.44, -15.33, 0.01038, 5, 9, 73.76, -95.18, 0.16196, 3, -420.74, -122.18, 0, 5, -517.78, -31.04, 0, 58, 26.9, -10.73, 0.82686, 68, -292.81, -22.59, 0.01118, 6, 9, 57.29, -96.11, 0.09255, 3, -404.31, -120.71, 0, 5, -501.34, -29.56, 0, 58, 43.04, -14.15, 0.62767, 59, -1.14, -17.24, 0.26895, 68, -309.29, -23.53, 0.01083, 7, 9, 38.49, -88.82, 0.28443, 3, -385.28, -127.38, 0, 5, -482.31, -36.24, 0, 57, 96.46, -44.07, 0.01507, 59, 18.94, -19.07, 0.63308, 60, -30.41, -3.16, 0.05849, 68, -328.09, -16.23, 0.00892, 7, 9, 21.62, -84.58, 0.17853, 3, -368.27, -131.05, 0, 5, -465.31, -39.91, 0, 57, 108.02, -57.08, 0.0014, 59, 36.33, -18.49, 0.43229, 60, -15.46, -12.05, 0.38009, 68, -344.96, -12, 0.0077, 7, 9, 2.25, -80.15, 0.1259, 8, 76.49, -80.16, 0.06562, 3, -348.77, -134.84, 0, 5, -445.8, -43.7, 0, 60, 1.83, -21.85, 0.79501, 61, -43.29, 8.1, 0.00701, 68, -364.33, -7.57, 0.00646, 7, 9, -12.39, -83.39, 0.0268, 8, 61.85, -83.37, 0.02774, 3, -334.24, -131.12, 0, 5, -431.28, -39.98, 0, 60, 16.78, -22.95, 0.88959, 61, -32.62, -2.43, 0.04867, 68, -378.97, -10.81, 0.00721, 5, 3, -319.1, -122.21, 0, 5, -416.14, -31.07, 0, 60, 33.97, -19.31, 0.76188, 61, -17.18, -10.8, 0.22939, 68, -394.39, -19.22, 0.00873, 5, 3, -309.21, -110.8, 0, 5, -406.25, -19.66, 0, 60, 46.97, -11.65, 0.33813, 61, -2.3, -13.4, 0.65122, 68, -404.66, -30.29, 0.01065, 3, 7, 223.46, -39.96, 0.88373, 5, -367.57, -66.49, 0.11533, 68, -441.77, 17.78, 0.00094, 5, 8, 20.66, -59.46, 0.0461, 7, 236.25, -57.97, 0.3293, 5, -389.28, -62.45, 0, 60, 49.45, -57.61, 0.62235, 68, -420.2, 13.03, 0.00225, 5, 8, 40.69, -56.69, 0.51423, 3, -312.17, -157.05, 0, 5, -409.21, -65.91, 0, 60, 29.45, -54.55, 0.48373, 68, -400.17, 15.84, 0.00205, 5, 7, 208.43, -67.43, 0.38382, 5, -375.88, -36.29, 0, 60, 70.47, -37.08, 0.49147, 61, -0.9, -47.99, 0.11776, 68, -434.46, -12.67, 0.00695, 4, 7, 203.39, -49.74, 0.81726, 5, -359.95, -45.49, 0, 60, 82.65, -50.87, 0.17788, 68, -450.08, -2.95, 0.00485, 5, 3, -298.05, -121.35, 0, 5, -395.09, -30.21, 0, 60, 54.2, -25.2, 0.51017, 61, -5.59, -28.4, 0.48134, 68, -415.46, -19.38, 0.00849, 4, 9, 152.81, -11.77, 0.96787, 3, -496.99, -208.15, 0, 5, -594.03, -117.01, 0, 68, -213.77, 60.82, 0.03213, 3, 9, 163.27, -28.19, 0.97574, 5, -605.03, -100.94, 0, 68, -203.31, 44.39, 0.02426, 3, 9, 163.42, -48.72, 0.98167, 5, -605.85, -80.43, 0, 68, -203.16, 23.87, 0.01833, 2, 9, 159.51, 6.43, 0.97658, 68, -207.07, 79.01, 0.02342, 3, 9, 168.73, -9.62, 0.98507, 5, -609.87, -119.68, 0, 68, -197.85, 62.96, 0.01493, 2, 9, 129.11, 15.56, 0.96849, 68, -237.47, 88.15, 0.03151, 2, 9, 113.38, 23.18, 0.96874, 68, -253.19, 95.76, 0.03126, 2, 9, 103.43, 43.76, 0.9721, 68, -263.15, 116.35, 0.0279, 2, 9, 92.25, 62.48, 0.97492, 68, -274.33, 135.06, 0.02508, 2, 9, 74.47, 69.8, 0.98068, 68, -292.11, 142.39, 0.01932, 2, 9, 136.01, 31.59, 0.97323, 68, -230.57, 104.17, 0.02677, 2, 9, 123.17, 45.19, 0.97309, 68, -243.41, 117.78, 0.02691, 2, 9, 107.31, 57.94, 0.97397, 68, -259.27, 130.52, 0.02603, 2, 9, 82.35, 44.26, 0.97481, 68, -284.23, 116.85, 0.02519, 3, 9, 28.47, 95.04, 0.98718, 68, -338.11, 167.62, 0.0098, 70, 370.58, 329.22, 0.00303, 2, 9, 47.02, 96.5, 0.98734, 68, -319.56, 169.09, 0.01266, 4, 9, 10.39, 88.04, 0.73286, 8, 84.94, 88.02, 0.25277, 68, -356.19, 160.63, 0.00747, 70, 352.49, 336.18, 0.00691, 4, 9, -3.99, 72.48, 0.48995, 8, 70.53, 72.49, 0.49199, 68, -370.57, 145.06, 0.00772, 70, 331.38, 334.31, 0.01034, 4, 9, -19.48, 54.9, 0.18622, 8, 55.01, 54.94, 0.79444, 68, -386.06, 127.49, 0.0064, 70, 308.1, 331.69, 0.01294, 5, 9, -38.84, 45.3, 0.08424, 8, 35.63, 45.37, 0.82633, 7, 317.63, 9.79, 0.07223, 68, -405.42, 117.88, 0.00256, 70, 287.3, 337.58, 0.01463, 4, 8, 15.46, 49.71, 0.57464, 7, 305.56, 26.53, 0.40878, 57, 99.03, -213.37, 0, 70, 275.24, 354.32, 0.01659, 5, 8, -4.98, 61.65, 0.40283, 7, 298.4, 49.09, 0.53474, 47, -24.69, 25.76, 0.04373, 57, 109.23, -234.73, 0, 70, 268.07, 376.87, 0.0187, 5, 8, -26.84, 76.84, 0.2999, 7, 292.35, 75.02, 0.49863, 47, -4.35, 8.58, 0.18244, 57, 118.78, -259.58, 0, 70, 262.03, 402.8, 0.01903, 5, 8, -52.2, 83.16, 0.16816, 7, 277.77, 96.7, 0.34224, 47, 20.29, -0.11, 0.47052, 57, 136.2, -279.06, 0, 70, 247.45, 424.49, 0.01907, 5, 8, -76.74, 77.96, 0.01962, 7, 256.07, 109.29, 0.47111, 47, 45.21, 2.76, 0.49018, 57, 159.42, -288.55, 0, 70, 225.75, 437.08, 0.01909, 3, 7, 231.25, 115.87, 0.42681, 48, 9.61, 10.59, 0.55726, 70, 200.93, 443.66, 0.01593, 4, 7, 203.8, 120.49, 0.42928, 5, -241.82, -168.07, 0, 48, 36.43, 18.04, 0.5549, 70, 173.48, 448.28, 0.01582, 6, 7, 186.27, 134.17, 0.22485, 5, -219.71, -165.71, 0, 48, 58.11, 13.06, 0.35575, 49, 1.89, 13.01, 0.40172, 64, 132.83, 178.06, 0, 70, 155.95, 461.96, 0.01768, 3, 49, 21.55, -3.44, 0.98084, 57, 246.58, -324.68, 0, 70, 144.38, 484.84, 0.01916, 5, 48, 98.25, -9.57, 0.00437, 49, 41.3, -10.88, 0.96892, 50, -11.55, -11.18, 0.00705, 57, 263.66, -337.08, 0, 70, 129.17, 499.47, 0.01966, 4, 50, 5.2, -3.58, 0.82769, 51, -47.78, -6.05, 0.15373, 57, 281.89, -334.64, 0, 70, 110.77, 499.56, 0.01858, 5, 7, 120.49, 162.95, 0.31179, 5, -152.44, -140.62, 0, 50, 20.24, 13.04, 0.66861, 64, 91.17, 236.54, 0, 70, 90.17, 490.74, 0.0196, 6, 7, 81.44, 163.62, 0.05668, 5, -123.92, -113.93, 0, 50, 56, 28.75, 0.2283, 51, 1.29, 28.85, 0.69813, 64, 58, 257.17, 0, 70, 51.11, 491.4, 0.01688, 6, 7, 63.59, 176.12, 0.01376, 5, -102.4, -110.5, 0, 50, 77.45, 24.84, 0.02048, 51, 22.91, 26.05, 0.95195, 64, 49.11, 277.07, 0, 70, 33.26, 503.91, 0.01381, 5, 7, 161.49, 132.49, 0.5875, 5, -203.08, -147.26, 0, 49, 23.99, 24.33, 0.39666, 64, 110.7, 189.35, 0, 70, 131.17, 460.28, 0.01583, 6, 7, 141.35, 141.29, 0.52023, 5, -182.49, -139.57, 0, 49, 45.97, 24.2, 0.33726, 50, -7.76, 24.01, 0.12539, 64, 97.94, 207.24, 0, 70, 111.02, 469.08, 0.01712, 6, 7, 115.29, 134.41, 0.80564, 6, 126.12, 168.4, 0.0001, 5, -168.56, -116.5, 0, 50, 13.04, 41.14, 0.17962, 64, 72.05, 214.72, 0, 70, 84.97, 462.2, 0.01464, 7, 7, 94.03, 131, 0.80617, 6, 107.8, 157.08, 0.00089, 5, -155.66, -99.26, 0, 50, 30.94, 53.12, 0.03705, 51, -24.99, 51.9, 0.14334, 64, 52.05, 222.72, 0, 70, 63.71, 458.79, 0.01254, 7, 7, 77.31, 137.61, 0.47695, 6, 89.82, 156.75, 1e-05, 5, -139.05, -92.37, 0, 50, 48.89, 54.1, 0.06676, 51, -7.12, 53.81, 0.44394, 64, 41.1, 236.97, 0, 70, 46.99, 465.39, 0.01234, 6, 7, 99.34, 158.58, 0.1361, 5, -140.28, -122.76, 0, 50, 37.64, 25.85, 0.53932, 51, -16.9, 25.01, 0.30656, 64, 70.77, 243.65, 0, 70, 69.01, 486.37, 0.01802, 4, 9, -11.83, 87.44, 0.43456, 8, 62.72, 87.46, 0.54949, 68, -378.41, 160.03, 0.00482, 70, 335.62, 350.66, 0.01113, 5, 9, -32.71, 79.75, 0.11839, 8, 41.83, 79.81, 0.86329, 5, -405.6, -202.37, 0, 68, -399.29, 152.34, 0.00277, 70, 314.99, 358.98, 0.01555, 7, 9, -48.57, 81.41, 0.03138, 8, 25.97, 81.5, 0.82257, 7, 334.67, 43.07, 0.08821, 47, -57.38, 8.92, 0.03869, 57, 72.47, -233.75, 0, 68, -415.14, 153.99, 0.00179, 70, 304.35, 370.86, 0.01736, 6, 9, -71.24, 89.13, 0.0041, 8, 3.32, 89.26, 0.55818, 7, 323.07, 64.02, 0.22301, 47, -35.55, -0.95, 0.19768, 57, 86.85, -252.91, 0, 70, 292.74, 391.81, 0.01703, 4, 8, -14.03, 102.52, 0.43844, 47, -19.53, -15.79, 0.54594, 57, 93.74, -273.63, 0, 70, 288.76, 413.28, 0.01562, 4, 8, -32.97, 107.93, 0.28647, 47, -1.18, -22.96, 0.69651, 57, 106.37, -288.75, 0, 70, 278.32, 429.99, 0.01702, 5, 8, -48.57, 111.37, 0.20512, 47, 14.01, -27.85, 0.3116, 48, -52.39, -17.66, 0.46671, 57, 117.34, -300.35, 0, 70, 269.06, 442.99, 0.01657, 4, 8, -70.51, 107.57, 0.10944, 47, 36.22, -26.14, 0.44241, 48, -30.3, -20.45, 0.43031, 70, 250.22, 454.88, 0.01783, 4, 8, -92.41, 99.17, 0.01122, 47, 58.82, -19.84, 0.45259, 48, -6.89, -18.84, 0.51744, 70, 228.34, 463.33, 0.01876, 2, 48, 14.83, -12.79, 0.98056, 70, 206.1, 467.05, 0.01944, 3, 48, 35.73, -7.44, 0.97226, 49, -21.12, -6.77, 0.00823, 70, 184.89, 471.06, 0.01951, 4, 48, 56.28, -13.13, 0.55685, 49, -0.77, -13.11, 0.42347, 57, 222.51, -328.09, 0, 70, 168.7, 484.91, 0.01968, 5, 5, -193.09, -200.79, 0, 48, 71.84, -28.78, 0.18378, 49, 14.29, -29.25, 0.79932, 57, 232.76, -347.64, 0, 70, 161.23, 505.68, 0.0169, 7, 5, -174.36, -206.66, 0, 48, 87.64, -40.43, 0.05634, 49, 29.71, -41.39, 0.79263, 50, -22.37, -41.97, 0.13627, 51, -73.33, -45.81, 0.00012, 57, 244.41, -363.43, 0, 70, 151.85, 522.93, 0.01464, 7, 5, -155.99, -199.97, 0, 48, 107.18, -40.1, 0.00651, 49, 49.26, -41.68, 0.65879, 50, -2.82, -41.76, 0.30426, 51, -53.82, -44.6, 0.01532, 57, 263.19, -368.88, 0, 70, 134.01, 530.91, 0.01511, 5, 49, 67.96, -28.6, 0.33766, 50, 15.55, -28.21, 0.58833, 51, -36.17, -30.12, 0.05644, 57, 284.68, -361.21, 0, 70, 111.66, 526.26, 0.01758, 5, 49, 83.98, -16.76, 0.00456, 50, 31.27, -15.98, 0.88194, 51, -21.11, -17.09, 0.09391, 57, 303.26, -354.04, 0, 70, 92.27, 521.71, 0.01958, 4, 50, 51.79, -12.94, 0.53394, 51, -0.77, -13, 0.44647, 57, 323.79, -357.06, 0, 70, 72.35, 527.52, 0.01959, 6, 49, 120.15, -19.35, 0.0081, 50, 67.49, -17.66, 0.14937, 51, 15.16, -16.9, 0.82355, 52, -34.4, -13.74, 0.00154, 57, 337.46, -366.12, 0, 70, 60.06, 538.37, 0.01744, 6, 49, 138.49, -30.87, 0.00166, 50, 86.11, -28.71, 0.05384, 51, 34.32, -26.98, 0.78789, 52, -16.26, -25.57, 0.14344, 57, 352.1, -382.07, 0, 70, 47.75, 556.19, 0.01317, 3, 52, 19.92, -5.7, 0.98646, 57, 392.08, -371.81, 0, 70, 6.73, 551.51, 0.01354, 4, 52, 35.65, 3.97, 0.85151, 53, -8.19, 3.62, 0.13787, 64, 33.63, 338.32, 0, 70, -11.48, 548.52, 0.01062, 4, 52, 53.57, 9.71, 0.09328, 53, 9.47, 10.12, 0.89979, 64, 18.3, 349.23, 0, 70, -30.24, 550, 0.00693, 3, 53, 22.52, 3.47, 0.99551, 64, 16.42, 363.75, 0, 70, -39.31, 561.5, 0.00449, 6, 49, 98.18, -42.03, 0.09054, 50, 46.1, -40.88, 0.50703, 51, -5.01, -41.19, 0.39251, 52, -56.76, -36.03, 0.00058, 57, 310.27, -382.16, 0, 70, 89.19, 550.53, 0.00935, 6, 49, 99.27, -59.29, 0.08228, 50, 47.62, -58.11, 0.49417, 51, -2.6, -58.33, 0.41647, 52, -55.96, -53.32, 0.00023, 57, 306.75, -399.11, 0, 70, 95, 566.83, 0.00685, 4, 8, -115.4, 122.06, 0.01709, 47, 79.55, -44.79, 0.19225, 48, 8.39, -47.45, 0.78771, 70, 226.62, 495.72, 0.00295, 4, 8, -96.29, 132.46, 0.06256, 47, 59.54, -53.34, 0.27262, 48, -12.93, -51.8, 0.66006, 70, 247.77, 490.63, 0.00477, 3, 9, 18.37, 53.84, 0.84102, 8, 92.86, 53.81, 0.14291, 68, -348.21, 126.43, 0.01608, 2, 9, 64.94, 91.24, 0.98318, 68, -301.63, 163.83, 0.01682, 2, 9, 94.95, 71.84, 0.9762, 68, -271.63, 144.42, 0.0238, 2, 9, 80.31, 82.53, 0.98031, 68, -286.26, 155.11, 0.01969, 2, 9, 154.74, 23.05, 0.98121, 68, -211.84, 95.63, 0.01879, 2, 9, 138.89, 3.88, 0.96711, 68, -227.69, 76.46, 0.03289, 2, 9, 144.19, 20.26, 0.97362, 68, -222.39, 92.84, 0.02638, 4, 8, -12.39, -30.57, 0.05384, 7, 231.09, -14.38, 0.93801, 5, -355.25, -90.17, 0, 70, 200.77, 313.41, 0.00815, 4, 8, 0.44, -21.24, 0.25628, 7, 246.87, -16.06, 0.72257, 5, -367.75, -99.94, 0.00906, 70, 216.54, 311.73, 0.01209, 4, 8, 18.19, -10.05, 0.6876, 7, 267.53, -19.65, 0.29779, 5, -385.1, -111.74, 0, 70, 237.21, 308.14, 0.0146, 3, 8, 34.79, 0.01, 0.87059, 7, 286.6, -23.3, 0.11551, 70, 256.28, 304.48, 0.0139, 2, 8, 47.91, 4.92, 0.98635, 70, 269.3, 299.33, 0.01365, 2, 8, 62.86, 9.18, 0.98609, 70, 283.25, 292.47, 0.01391, 3, 9, 5.27, 11.45, 0.70512, 8, 79.68, 11.44, 0.28012, 70, 297.25, 282.87, 0.01476, 2, 9, 18.85, 12.74, 0.9842, 70, 308.18, 274.71, 0.0158, 4, 8, 16.58, -23.65, 0.58854, 7, 257.23, -28.66, 0.40221, 5, -383.96, -98.09, 0, 70, 226.9, 299.13, 0.00925, 4, 8, 32.04, -22.3, 0.86084, 7, 269.6, -38.03, 0.12853, 5, -399.37, -99.97, 0, 70, 239.28, 289.76, 0.01063, 3, 8, 46.74, -22.49, 0.98769, 5, -414.07, -100.3, 0, 70, 250.07, 279.77, 0.01231, 5, 9, -13.72, -24.09, 0.0795, 8, 60.63, -24.06, 0.90885, 3, -330.96, -190.35, 0, 5, -428, -99.21, 0, 70, 259.31, 269.3, 0.01165, 5, 9, -0.59, -25.08, 0.53438, 8, 73.75, -25.08, 0.45444, 3, -344.11, -189.8, 0, 5, -441.15, -98.65, 0, 70, 268.38, 259.74, 0.01118, 5, 9, 13.08, -26.54, 0.95034, 8, 87.43, -26.56, 0.03826, 3, -357.83, -188.79, 0, 5, -454.87, -97.65, 0, 70, 277.53, 249.48, 0.01141, 5, 8, 7.74, 8.09, 0.41495, 37, 50.1, -18.44, 0.23243, 7, 271.95, 0.82, 0.34471, 57, 128.8, -183.28, 0, 70, 241.62, 328.61, 0.0079, 5, 8, -2.53, -8.07, 0.1547, 37, 31.64, -23.55, 0.22964, 7, 253.48, -4.29, 0.60791, 5, -364.32, -112.99, 0, 70, 223.16, 323.49, 0.00775, 4, 37, 16.71, -28.85, 0.20438, 7, 238.56, -9.6, 0.78912, 5, -357.29, -98.81, 0, 70, 208.24, 318.19, 0.0065], "hull": 211, "edges": [6, 8, 22, 24, 24, 26, 30, 32, 40, 42, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 108, 110, 144, 146, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 200, 202, 202, 204, 204, 206, 206, 208, 212, 214, 214, 216, 220, 222, 222, 224, 224, 226, 240, 242, 242, 244, 248, 250, 250, 252, 252, 254, 260, 262, 262, 264, 264, 266, 266, 268, 268, 270, 270, 272, 282, 284, 284, 286, 302, 304, 304, 306, 306, 308, 312, 314, 314, 316, 316, 318, 340, 342, 342, 344, 344, 346, 346, 348, 348, 350, 350, 352, 358, 360, 360, 362, 378, 380, 380, 382, 394, 396, 400, 402, 414, 416, 416, 418, 418, 420, 402, 404, 404, 406, 396, 398, 398, 400, 388, 390, 390, 392, 392, 394, 382, 384, 384, 386, 386, 388, 376, 378, 374, 376, 370, 372, 372, 374, 366, 368, 368, 370, 362, 364, 364, 366, 356, 358, 352, 354, 354, 356, 322, 324, 318, 320, 320, 322, 308, 310, 310, 312, 324, 326, 326, 328, 328, 330, 330, 332, 332, 334, 334, 336, 338, 340, 336, 338, 272, 274, 278, 280, 280, 282, 286, 288, 288, 290, 290, 292, 292, 294, 294, 296, 296, 298, 298, 300, 300, 302, 274, 276, 276, 278, 2, 0, 0, 420, 2, 4, 4, 6, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 26, 28, 28, 30, 32, 34, 34, 36, 36, 38, 38, 40, 42, 44, 44, 46, 46, 48, 48, 50, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 88, 90, 90, 92, 254, 256, 244, 246, 246, 248, 238, 240, 230, 232, 232, 234, 226, 228, 228, 230, 234, 236, 236, 238, 102, 104, 104, 106, 106, 108, 110, 112, 112, 114, 118, 120, 120, 122, 114, 116, 116, 118, 122, 124, 124, 126, 126, 128, 128, 130, 132, 134, 130, 132, 216, 218, 218, 220, 208, 210, 210, 212, 146, 148, 148, 150, 142, 144, 138, 140, 140, 142, 134, 136, 136, 138, 192, 194, 194, 196, 196, 198, 198, 200, 150, 152, 166, 168, 162, 164, 164, 166, 272, 422, 422, 424, 424, 426, 426, 428, 428, 430, 430, 432, 432, 434, 436, 438, 438, 440, 440, 442, 442, 444, 444, 448, 448, 446, 80, 450, 450, 452, 452, 454, 454, 456, 456, 458, 458, 460, 460, 462, 462, 464, 464, 466, 468, 470, 470, 472, 468, 474, 474, 476, 476, 478, 478, 480, 480, 482, 484, 486, 486, 488, 488, 490, 490, 492, 492, 482, 472, 494, 494, 496, 484, 496, 482, 500, 434, 502, 502, 436, 502, 504, 504, 506, 506, 508, 508, 510, 510, 512, 512, 514, 514, 516, 488, 518, 426, 520, 520, 522, 522, 524, 526, 528, 528, 530, 530, 532, 532, 534, 534, 536, 536, 102, 526, 538, 538, 540, 516, 540, 518, 538, 86, 88, 86, 542, 542, 544, 544, 546, 546, 100, 90, 548, 548, 550, 550, 96, 538, 552, 540, 554, 554, 552, 552, 556, 556, 558, 558, 536, 88, 560, 560, 562, 562, 564, 564, 98, 230, 608, 608, 610, 610, 612, 612, 614, 614, 616, 616, 606, 606, 604, 604, 620, 620, 618, 618, 582, 582, 584, 624, 626, 626, 628, 628, 630, 630, 632, 632, 634, 634, 636, 636, 638, 638, 640, 640, 642, 642, 644, 644, 646, 646, 624, 648, 650, 650, 652, 652, 654, 654, 656, 656, 658, 658, 660, 660, 662, 662, 648, 586, 588, 586, 584, 588, 590, 590, 234, 250, 666, 666, 670, 670, 672, 672, 674, 674, 244, 252, 676, 664, 678, 678, 242, 254, 680, 256, 258, 258, 260, 708, 710, 710, 712, 712, 714, 716, 718, 718, 720, 704, 722, 722, 720, 724, 726, 726, 728, 714, 730, 730, 732, 732, 734, 734, 736, 736, 738, 738, 740, 740, 742, 742, 744, 744, 746, 746, 748, 748, 750, 750, 752, 752, 754, 754, 756, 756, 758, 758, 760, 760, 762, 762, 716, 764, 766, 796, 764, 796, 794, 794, 792, 792, 770, 770, 774, 774, 772, 772, 776, 776, 778, 778, 780, 780, 782, 782, 784, 784, 786, 786, 788, 788, 790, 790, 798, 798, 800, 800, 802, 802, 804, 804, 466, 74, 72, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 766, 998, 998, 768, 406, 408, 408, 410, 410, 412, 412, 414, 452, 1012, 1012, 1014, 1014, 1016, 1016, 1018, 1018, 1020, 1020, 1022, 1022, 1024, 1024, 1026, 1028, 1030, 1030, 1032, 1032, 1034, 1034, 1036, 1036, 1038], "width": 422, "height": 1714}}, "eyewhite_L": {"eyewhite_L": {"type": "mesh", "uvs": [0.26747, 0.06482, 0.04628, 0.43287, 0.40283, 0.93724, 0.7289, 0.8463, 0.94331, 0.47619, 0.74948, 0.08185], "triangles": [2, 1, 3, 1, 0, 3, 3, 5, 4, 3, 0, 5], "vertices": [2, 9, 62.4, -55.39, 0.97206, 68, -304.18, 17.2, 0.02794, 2, 9, 49.15, -53.23, 0.97206, 68, -317.43, 19.35, 0.02794, 2, 9, 39.09, -69.77, 0.97732, 68, -327.49, 2.82, 0.02268, 2, 9, 45.7, -78.32, 0.98003, 68, -320.88, -5.74, 0.01997, 2, 9, 58.93, -80.25, 0.98143, 68, -307.65, -7.67, 0.01857, 2, 9, 67.83, -69.83, 0.97522, 68, -298.75, 2.75, 0.02478], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 32, "height": 31}}, "eyewhite_R": {"eyewhite_R": {"type": "mesh", "uvs": [0.20382, 0.07454, 0.04022, 0.38792, 0.32719, 0.87652, 0.80726, 0.94055, 0.95477, 0.76532, 0.71339, 0.16215], "triangles": [4, 3, 5, 1, 0, 2, 3, 2, 5, 2, 0, 5], "vertices": [2, 9, 70.02, 4.71, 0.96347, 68, -296.56, 77.3, 0.03653, 2, 9, 55.66, 7.42, 0.96357, 68, -310.92, 80.01, 0.03643, 2, 9, 43.46, -12.88, 0.96089, 68, -323.12, 59.71, 0.03911, 2, 9, 50.18, -35.56, 0.96291, 68, -316.4, 37.03, 0.03709, 2, 9, 59.27, -39.61, 0.96346, 68, -307.31, 32.98, 0.03654, 2, 9, 76.45, -19.66, 0.96126, 68, -290.13, 52.93, 0.03874], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 49, "height": 39}}, "eye_L": {"eye_L": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 16, -4.6, -11.01, 0.97922, 68, -315.5, -2.44, 0.02078, 2, 16, -11, 4.4, 0.97376, 68, -321.91, 12.97, 0.02624, 2, 16, 3.77, 10.54, 0.97621, 68, -307.13, 19.11, 0.02379, 2, 16, 10.18, -4.87, 0.97645, 68, -300.73, 3.7, 0.02355], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 14, "height": 16}}, "eye_LL": {"eye_LL": {"type": "mesh", "uvs": [0.40489, 0.03503, 0.52494, 0.09835, 0.65204, 0.27104, 0.78421, 0.21208, 0.92711, 0.41081, 0.95483, 0.58075, 0.86531, 0.68238, 0.68164, 0.68359, 0.60251, 0.82945, 0.48717, 0.94654, 0.35356, 0.90773, 0.21897, 0.74883, 0.1313, 0.59543, 0.06358, 0.41555, 0.04429, 0.28473, 0.05611, 0.18415, 0.15061, 0.11791, 0.25033, 0.03613, 0.10886, 0.27, 0.18265, 0.20334, 0.26413, 0.17692, 0.37483, 0.18194, 0.48706, 0.26119, 0.58007, 0.39327, 0.59467, 0.56812, 0.57315, 0.70397, 0.38175, 0.73918, 0.27874, 0.65113, 0.19418, 0.52534, 0.12423, 0.3895, 0.48811, 0.75905], "triangles": [21, 20, 17, 19, 16, 17, 0, 21, 17, 20, 19, 17, 1, 21, 0, 22, 21, 1, 18, 15, 16, 18, 16, 19, 22, 1, 2, 14, 15, 18, 29, 18, 19, 23, 22, 2, 13, 14, 18, 13, 18, 29, 28, 19, 20, 29, 19, 28, 24, 23, 2, 12, 29, 28, 13, 29, 12, 21, 28, 20, 27, 21, 22, 27, 28, 21, 4, 7, 3, 6, 4, 5, 7, 2, 3, 6, 7, 4, 24, 2, 7, 27, 22, 26, 23, 26, 22, 25, 24, 7, 26, 23, 24, 24, 30, 26, 11, 28, 27, 12, 28, 11, 25, 30, 24, 8, 25, 7, 30, 25, 8, 26, 11, 27, 10, 11, 26, 30, 10, 26, 9, 30, 8, 9, 10, 30], "vertices": [2, 9, 62.23, -67.39, 0.97633, 68, -304.35, 5.2, 0.02367, 2, 9, 62.6, -71.91, 0.97763, 68, -303.97, 0.67, 0.02237, 2, 9, 60.85, -77.6, 0.97928, 68, -305.73, -5.01, 0.02072, 2, 9, 63.88, -81.49, 0.98039, 68, -302.7, -8.91, 0.01961, 2, 9, 61.81, -87.92, 0.98223, 68, -304.76, -15.34, 0.01777, 2, 9, 58.74, -90.28, 0.98295, 68, -307.83, -17.69, 0.01705, 2, 9, 55.44, -88.16, 0.9823, 68, -311.14, -15.58, 0.0177, 2, 9, 52.88, -82.06, 0.98055, 68, -313.7, -9.48, 0.01945, 2, 9, 48.82, -80.67, 0.98016, 68, -317.75, -8.08, 0.01984, 2, 9, 44.85, -77.82, 0.97933, 68, -321.73, -5.24, 0.02067, 2, 9, 43.79, -73.05, 0.97795, 68, -322.78, -0.47, 0.02205, 2, 9, 45.16, -67.24, 0.97628, 68, -321.42, 5.35, 0.02372, 2, 9, 47.07, -63.03, 0.97509, 68, -319.51, 9.56, 0.02491, 2, 9, 49.79, -59.26, 0.97398, 68, -316.79, 13.33, 0.02602, 2, 9, 52.18, -57.51, 0.97349, 68, -314.4, 15.07, 0.02651, 2, 9, 54.38, -57.05, 0.97336, 68, -312.19, 15.53, 0.02664, 2, 9, 57.04, -59.64, 0.97412, 68, -309.54, 12.95, 0.02588, 2, 9, 60.07, -62.26, 0.97487, 68, -306.5, 10.32, 0.02513, 2, 9, 53.37, -59.53, 0.97406, 68, -313.21, 13.05, 0.02594, 2, 9, 55.74, -61.42, 0.97464, 68, -310.84, 11.16, 0.02536, 2, 9, 57.4, -63.91, 0.97535, 68, -309.17, 8.68, 0.02465, 2, 9, 58.83, -67.63, 0.97643, 68, -307.75, 4.95, 0.02357, 2, 9, 58.77, -72.03, 0.9777, 68, -307.81, 0.55, 0.0223, 2, 9, 57.38, -76.24, 0.9789, 68, -309.2, -3.65, 0.0211, 2, 9, 54.02, -78.2, 0.97943, 68, -312.55, -5.61, 0.02057, 2, 9, 50.97, -78.63, 0.97956, 68, -315.61, -6.05, 0.02044, 2, 9, 47.61, -72.57, 0.97783, 68, -318.97, 0.02, 0.02217, 2, 9, 47.97, -68.4, 0.97663, 68, -318.61, 4.19, 0.02337, 2, 9, 49.36, -64.52, 0.9755, 68, -317.22, 8.06, 0.0245, 2, 9, 51.15, -61.05, 0.97451, 68, -315.43, 11.53, 0.02549, 2, 9, 48.67, -76.27, 0.97889, 68, -317.91, -3.68, 0.02111], "hull": 18, "edges": [0, 34, 8, 10, 18, 20, 6, 8, 4, 6, 0, 2, 2, 4, 30, 32, 32, 34, 28, 30, 10, 12, 16, 18, 12, 14, 14, 16, 24, 26, 26, 28, 20, 22, 22, 24, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 52, 54, 54, 56, 56, 58, 58, 36, 50, 60, 60, 52], "width": 36, "height": 22}}, "eye_R": {"eye_R": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 15, -4.68, -11.06, 0.96239, 68, -309.82, 49.03, 0.03761, 2, 15, -10.82, 3.71, 0.96196, 68, -315.96, 63.8, 0.03804, 2, 15, 3.96, 9.85, 0.96223, 68, -301.19, 69.94, 0.03777, 2, 15, 10.1, -4.92, 0.96225, 68, -295.04, 55.17, 0.03775], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 16, "height": 16}}, "eye_RR": {"eye_RR": {"type": "mesh", "uvs": [0.32246, 0.05212, 0.41488, 0.22667, 0.54807, 0.23747, 0.67486, 0.27333, 0.79421, 0.36889, 0.8832, 0.50916, 0.9383, 0.70149, 0.96814, 0.88199, 0.94605, 0.94712, 0.84903, 0.9472, 0.70584, 0.97304, 0.54876, 0.97238, 0.35369, 0.87817, 0.23273, 0.74444, 0.15967, 0.55812, 0.15267, 0.34549, 0.01234, 0.10458, 0.14536, 0.04278, 0.27627, 0.37552, 0.30103, 0.50824, 0.37528, 0.63846, 0.47725, 0.73362, 0.60101, 0.79748, 0.74061, 0.82502, 0.87624, 0.82127, 0.86337, 0.70607, 0.80595, 0.56083, 0.72476, 0.4569, 0.60497, 0.38303, 0.47923, 0.3655, 0.37132, 0.3655], "triangles": [11, 22, 10, 10, 23, 9, 10, 22, 23, 12, 21, 11, 11, 21, 22, 9, 24, 8, 9, 23, 24, 8, 24, 7, 24, 6, 7, 12, 20, 21, 12, 13, 20, 24, 23, 25, 25, 23, 26, 24, 25, 6, 23, 22, 26, 22, 27, 26, 27, 22, 28, 13, 19, 20, 13, 14, 19, 22, 21, 28, 21, 29, 28, 21, 20, 29, 6, 25, 5, 25, 26, 5, 19, 30, 20, 20, 30, 29, 5, 26, 4, 14, 18, 19, 14, 15, 18, 26, 27, 4, 19, 18, 30, 28, 3, 27, 27, 3, 4, 29, 2, 28, 28, 2, 3, 30, 18, 1, 30, 1, 29, 29, 1, 2, 0, 1, 18, 16, 17, 15, 18, 15, 0, 15, 17, 0], "vertices": [2, 9, 71.63, 1.18, 0.9635, 68, -294.95, 73.77, 0.0365, 2, 9, 67.68, -4.77, 0.96302, 68, -298.9, 67.82, 0.03698, 2, 9, 69.54, -10.2, 0.96256, 68, -297.04, 62.39, 0.03744, 2, 9, 70.5, -15.7, 0.9621, 68, -296.08, 56.89, 0.0379, 2, 9, 69.47, -21.68, 0.96248, 68, -297.11, 50.9, 0.03752, 2, 9, 66.54, -27.05, 0.96342, 68, -300.04, 45.54, 0.03658, 2, 9, 61.41, -31.75, 0.9645, 68, -305.17, 40.84, 0.0355, 2, 9, 56.23, -35.29, 0.96525, 68, -310.34, 37.3, 0.03475, 2, 9, 53.82, -35.26, 0.96527, 68, -312.75, 37.33, 0.03473, 2, 9, 52.22, -31.41, 0.96458, 68, -314.36, 41.18, 0.03542, 2, 9, 49.05, -26.06, 0.96361, 68, -317.53, 46.53, 0.03639, 2, 9, 46.47, -19.81, 0.96259, 68, -320.1, 52.77, 0.03741, 2, 9, 46.21, -10.84, 0.962, 68, -320.37, 61.75, 0.038, 2, 9, 48.42, -4.29, 0.96234, 68, -318.16, 68.29, 0.03766, 2, 9, 53.06, 1.04, 0.96302, 68, -313.52, 73.63, 0.03698, 2, 9, 59.62, 4.09, 0.96359, 68, -306.96, 76.68, 0.03641, 2, 9, 64.87, 12.81, 0.96528, 68, -301.71, 85.4, 0.03472, 2, 9, 69, 8.34, 0.9646, 68, -297.58, 80.92, 0.0354, 2, 9, 60.72, -1.2, 0.96322, 68, -305.86, 71.38, 0.03678, 2, 9, 56.96, -3.92, 0.9629, 68, -309.62, 68.66, 0.0371, 2, 9, 54.1, -8.57, 0.96244, 68, -312.48, 64.02, 0.03756, 2, 9, 52.79, -13.86, 0.96213, 68, -313.79, 58.73, 0.03787, 2, 9, 52.83, -19.61, 0.96254, 68, -313.75, 52.98, 0.03746, 2, 9, 54.27, -25.51, 0.9635, 68, -312.31, 47.08, 0.0365, 2, 9, 56.62, -30.84, 0.96444, 68, -309.95, 41.74, 0.03556, 2, 9, 60.03, -28.83, 0.96402, 68, -306.55, 43.75, 0.03598, 2, 9, 63.64, -24.65, 0.96315, 68, -302.94, 47.93, 0.03685, 2, 9, 65.56, -20.08, 0.96244, 68, -301.02, 52.51, 0.03756, 2, 9, 65.91, -14.35, 0.96226, 68, -300.67, 58.23, 0.03774, 2, 9, 64.38, -9.13, 0.96268, 68, -302.2, 63.45, 0.03732, 2, 9, 62.6, -4.85, 0.96299, 68, -303.98, 67.74, 0.03701], "hull": 18, "edges": [0, 34, 0, 2, 6, 8, 12, 14, 22, 24, 24, 26, 32, 34, 14, 16, 16, 18, 8, 10, 10, 12, 30, 32, 26, 28, 28, 30, 18, 20, 20, 22, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 36, 2, 4, 4, 6], "width": 43, "height": 34}}, "hair_F": {"hair_F": {"type": "mesh", "uvs": [0.56223, 0.00378, 0.71575, 0.0201, 0.63848, 0.11885, 0.6034, 0.2156, 0.67404, 0.33064, 0.80828, 0.41547, 0.95906, 0.53265, 0.99035, 0.63354, 0.97932, 0.73224, 0.91952, 0.84372, 0.82036, 0.92261, 0.70029, 0.971, 0.53959, 0.99129, 0.35816, 0.99547, 0.31051, 0.91619, 0.13044, 0.90617, 1e-05, 0.9162, 0.11493, 0.8756, 0.3037, 0.87957, 0.47966, 0.87512, 0.6443, 0.85711, 0.77131, 0.8048, 0.86986, 0.73514, 0.87605, 0.64283, 0.82949, 0.55342, 0.71741, 0.45484, 0.55984, 0.34719, 0.48032, 0.21631, 0.50089, 0.09965], "triangles": [15, 17, 18, 16, 17, 15, 14, 18, 19, 15, 18, 14, 12, 19, 11, 14, 19, 12, 13, 14, 12, 21, 22, 9, 10, 21, 9, 20, 21, 10, 11, 20, 10, 19, 20, 11, 8, 23, 7, 22, 23, 8, 9, 22, 8, 24, 25, 6, 24, 6, 7, 23, 24, 7, 26, 3, 4, 25, 4, 5, 26, 4, 25, 25, 5, 6, 28, 0, 2, 2, 27, 28, 3, 27, 2, 26, 27, 3, 2, 0, 1], "vertices": [2, 17, 2.89, 4.64, 0.99889, 18, -9.39, -1.74, 0.00111, 1, 17, 4.74, -6.07, 1, 2, 17, -10.71, -6.71, 0.00086, 18, 7.37, 4.01, 0.99914, 1, 18, 21.65, 1.99, 1, 1, 19, 11.17, 1.59, 1, 1, 19, 26.62, 3.14, 1, 1, 20, 12.87, 6.29, 1, 2, 20, 27.65, 3.8, 0.31634, 21, -0.1, 4.24, 0.68366, 1, 21, 14.32, 6, 1, 2, 21, 31.18, 4.77, 0.05003, 22, 1.54, 7.92, 0.94997, 2, 22, 14.01, 13, 0.98545, 23, -15.49, 4.55, 0.01455, 2, 22, 24.91, 13.64, 0.63894, 23, -7.21, 11.66, 0.36106, 2, 22, 35.54, 9.3, 0.08513, 23, 3.88, 14.64, 0.91487, 1, 23, 16.4, 15.26, 1, 2, 23, 19.69, 3.6, 0.9784, 24, -2.52, 3.86, 0.0216, 2, 23, 32.11, 2.13, 1e-05, 24, 9.71, 1.2, 0.99999, 2, 23, 41.11, 3.6, 0, 24, 18.81, 1.8, 1, 1, 24, 10.34, -3.38, 1, 1, 23, 20.16, -1.78, 1, 1, 23, 8.02, -2.44, 1, 1, 22, 17.84, -2.02, 1, 2, 21, 27.31, -6.29, 0.01846, 22, 6.21, -2.83, 0.98154, 1, 21, 16.05, -1.36, 1, 2, 20, 26.53, -4.13, 0.19974, 21, 2.61, -3.29, 0.80026, 1, 20, 13.03, -3.16, 1, 2, 19, 28.37, -5.22, 0.9511, 20, -3.13, -6.08, 0.0489, 1, 19, 9.21, -6.42, 1, 1, 18, 22, -6.49, 1, 1, 18, 4.82, -5.56, 1], "hull": 29, "edges": [0, 56, 0, 2, 6, 8, 12, 14, 14, 16, 20, 22, 22, 24, 24, 26, 26, 28, 32, 34, 46, 48, 52, 54, 54, 56, 2, 4, 4, 6, 8, 10, 10, 12, 48, 50, 50, 52, 42, 44, 44, 46, 16, 18, 18, 20, 38, 40, 40, 42, 34, 36, 36, 38, 28, 30, 30, 32], "width": 69, "height": 147}}, "hand_R": {"hand_R": {"type": "mesh", "uvs": [0.53922, 0.0612, 0.66608, 0.11598, 0.7578, 0.19287, 0.82984, 0.29985, 0.87202, 0.37454, 0.93618, 0.48813, 0.97627, 0.58583, 0.8043, 0.60413, 0.63233, 0.64338, 0.53528, 0.69957, 0.51768, 0.78611, 0.58966, 0.87091, 0.60521, 0.96071, 0.51618, 0.99999, 0.35641, 0.9756, 0.23723, 0.88922, 0.16735, 0.78296, 0.11074, 0.67844, 0.05778, 0.56127, 0.09108, 0.42663, 0.16335, 0.29648, 0.12233, 0.20919, 0.01444, 0.1215, 0.28474, 0.00087], "triangles": [9, 17, 8, 16, 17, 9, 10, 16, 9, 15, 16, 10, 15, 10, 11, 15, 11, 12, 14, 15, 12, 13, 14, 12, 8, 18, 7, 21, 1, 2, 20, 21, 2, 20, 2, 3, 5, 19, 4, 4, 20, 3, 4, 19, 20, 7, 5, 6, 5, 18, 19, 7, 18, 5, 17, 18, 8, 22, 23, 0, 1, 21, 22, 1, 22, 0], "vertices": [2, 39, 226.43, 10.81, 0.98982, 40, -10.98, 4.43, 0.01018, 2, 39, 235.05, 6.25, 0.40402, 40, -1.61, 7.14, 0.59598, 1, 40, 10.63, 7.6, 1, 1, 40, 27.08, 6.11, 1, 1, 40, 38.47, 4.75, 1, 1, 40, 55.81, 2.68, 1, 1, 40, 70.56, 0.32, 1, 1, 40, 71.43, -7.05, 1, 2, 40, 75.38, -15.25, 0.61509, 43, 4.48, 11.75, 0.38491, 2, 40, 82.59, -21.24, 0.31545, 43, 12.64, 7.12, 0.68455, 2, 40, 95.1, -25.38, 0.03714, 43, 25.68, 5.25, 0.96286, 1, 43, 38.78, 6.96, 1, 1, 43, 52.43, 6.36, 1, 1, 43, 58.06, 2.28, 1, 1, 43, 53.79, -3.75, 1, 1, 43, 40.29, -7.33, 1, 2, 40, 90.95, -38.78, 0.09429, 43, 23.95, -8.67, 0.90571, 2, 40, 75.03, -36.78, 0.34, 43, 7.93, -9.5, 0.66, 2, 40, 57.29, -34.14, 0.59001, 43, -10, -10.02, 0.40999, 2, 40, 37.89, -27.47, 0.99137, 43, -30.27, -6.87, 0.00863, 2, 39, 231.8, -27.61, 0.04884, 40, 19.57, -19.48, 0.95116, 3, 39, 223.6, -17.05, 0.4191, 40, 6.34, -17.57, 0.57976, 43, -63.07, -2.67, 0.00114, 2, 39, 213.08, -7.8, 0.94866, 40, -7.66, -18.23, 0.05134, 1, 39, 212.99, 13.49, 1], "hull": 24, "edges": [0, 2, 2, 4, 10, 12, 16, 18, 22, 24, 24, 26, 26, 28, 28, 30, 38, 40, 40, 42, 42, 44, 44, 46, 34, 36, 36, 38, 30, 32, 32, 34, 18, 20, 20, 22, 12, 14, 14, 16, 8, 10, 4, 6, 6, 8, 46, 0], "width": 40, "height": 152}}, "hat": {"hat": {"type": "mesh", "uvs": [0.76293, 1e-05, 0.84362, 0.02893, 0.90936, 0.08474, 0.95511, 0.17337, 0.98866, 0.29463, 0.99592, 0.42638, 0.975, 0.53202, 0.91681, 0.63141, 0.79694, 0.7593, 0.6644, 0.84303, 0.51915, 0.91279, 0.31339, 0.9807, 0.15401, 0.99534, 0.07427, 0.95648, 0.01569, 0.86868, 0.00469, 0.71963, 0.03831, 0.58185, 0.07904, 0.50665, 0.22259, 0.37915, 0.35772, 0.26057, 0.50717, 0.10892, 0.65498, 0.02751, 0.22295, 0.86999, 0.31566, 0.72412, 0.48188, 0.5255, 0.63896, 0.39004, 0.80524, 0.32321, 0.90817, 0.32625], "triangles": [26, 0, 1, 26, 1, 2, 27, 26, 2, 3, 27, 2, 4, 27, 3, 25, 20, 21, 26, 25, 21, 26, 21, 0, 27, 4, 5, 24, 19, 20, 24, 20, 25, 6, 27, 5, 7, 27, 6, 24, 23, 18, 24, 18, 19, 17, 18, 23, 8, 25, 26, 7, 8, 26, 7, 26, 27, 9, 25, 8, 24, 25, 9, 22, 14, 15, 22, 17, 23, 16, 17, 22, 22, 15, 16, 10, 24, 9, 23, 24, 10, 13, 14, 22, 11, 22, 23, 11, 23, 10, 12, 13, 22, 12, 22, 11], "vertices": [2, 9, 197.93, 43.24, 0.9829, 68, -168.65, 115.83, 0.0171, 2, 9, 201.06, 25.93, 0.98495, 68, -165.52, 98.51, 0.01505, 2, 9, 199.73, 10.21, 0.98804, 68, -166.85, 82.8, 0.01196, 2, 9, 192.83, -3.21, 0.99251, 68, -173.74, 69.38, 0.00749, 2, 9, 181.02, -15.86, 0.99715, 68, -185.56, 56.73, 0.00285, 2, 9, 165.8, -23.86, 0.99658, 69, -135.43, 48.73, 0.00342, 1, 9, 151.41, -25.01, 1, 2, 9, 134.72, -18.53, 0.99174, 69, -166.51, 54.06, 0.00826, 2, 9, 109.57, -1.33, 0.98485, 69, -191.66, 71.25, 0.01515, 2, 9, 88.68, 20.56, 0.97848, 69, -212.55, 93.14, 0.02152, 2, 9, 68.43, 45.65, 0.97766, 69, -232.8, 118.23, 0.02234, 2, 9, 43.46, 82.73, 0.98409, 69, -257.77, 155.31, 0.01591, 2, 9, 28.67, 113.34, 0.99284, 69, -272.56, 185.93, 0.00716, 2, 9, 26.81, 130.97, 0.99701, 69, -274.42, 203.55, 0.00299, 2, 9, 32.57, 146.87, 0.99893, 68, -334.01, 219.45, 0.00107, 2, 9, 49.56, 156.47, 0.99219, 68, -317.02, 229.05, 0.00781, 2, 9, 68.85, 156.73, 0.9886, 68, -297.73, 229.32, 0.0114, 2, 9, 81.2, 152.47, 0.98595, 68, -285.37, 225.06, 0.01405, 2, 9, 108.24, 130.6, 0.97901, 68, -258.33, 203.19, 0.02099, 2, 9, 133.53, 109.94, 0.97812, 68, -233.05, 182.52, 0.02188, 2, 9, 163.95, 88.11, 0.98022, 68, -202.63, 160.7, 0.01978, 2, 9, 185.81, 63.1, 0.98069, 68, -180.77, 135.69, 0.01931, 2, 9, 49.35, 106.04, 0.99064, 68, -317.23, 178.62, 0.00936, 2, 9, 74.44, 95.08, 0.98358, 68, -292.14, 167.67, 0.01642, 2, 9, 111.87, 72.3, 0.97431, 68, -254.7, 144.89, 0.02569, 2, 9, 140.98, 48.16, 0.97734, 68, -225.6, 120.75, 0.02266, 2, 9, 162.59, 18.79, 0.9867, 68, -203.98, 91.38, 0.0133, 2, 9, 170.64, -1.6, 0.99215, 68, -195.93, 70.98, 0.00785], "hull": 22, "edges": [0, 42, 0, 2, 2, 4, 4, 6, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 40, 42, 18, 20, 20, 22, 6, 8, 34, 36, 36, 38, 38, 40, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54], "width": 213, "height": 130}}, "head": {"head": {"type": "mesh", "uvs": [0.66506, 0.00052, 0.81315, 0.07116, 0.88627, 0.12453, 0.9363, 0.13237, 0.9863, 0.19453, 0.99467, 0.28287, 0.97804, 0.38448, 0.97721, 0.42762, 0.97351, 0.45143, 0.93395, 0.44589, 0.89322, 0.4424, 0.85216, 0.44177, 0.83413, 0.43353, 0.80041, 0.45255, 0.77479, 0.4872, 0.77343, 0.55513, 0.7861, 0.61071, 0.78565, 0.64566, 0.75607, 0.67259, 0.6977, 0.69027, 0.70746, 0.73174, 0.72333, 0.75194, 0.72078, 0.78952, 0.68414, 0.83198, 0.6736, 0.85276, 0.65, 0.86689, 0.62183, 0.86924, 0.62158, 0.88681, 0.63701, 0.92715, 0.60824, 0.96512, 0.57369, 0.98866, 0.50999, 0.9919, 0.45139, 0.96891, 0.30821, 0.8625, 0.16733, 0.75507, 0.12573, 0.70661, 0.10368, 0.64882, 0.10747, 0.54644, 0.05675, 0.5413, 0.0097, 0.42472, 0.01046, 0.31626, 0.05654, 0.22978, 0.10519, 0.22678, 0.15638, 0.27609, 0.17272, 0.38755, 0.1905, 0.4206, 0.24176, 0.33705, 0.29595, 0.2535, 0.37288, 0.18515, 0.5485, 0.13825, 0.59728, 0.10605, 0.63773, 0.06734, 0.67499, 0.72165, 0.65008, 0.70253, 0.60507, 0.70174, 0.55398, 0.71878, 0.51465, 0.72999, 0.48553, 0.72954, 0.4794, 0.71429, 0.44773, 0.73402, 0.47583, 0.74568, 0.50188, 0.80487, 0.45437, 0.76272, 0.48272, 0.77056, 0.54275, 0.83625, 0.5877, 0.86091, 0.47861, 0.73515, 0.50078, 0.74363, 0.52831, 0.75147, 0.56192, 0.75524, 0.5891, 0.75053, 0.62021, 0.75712, 0.64452, 0.77282, 0.66633, 0.78976, 0.67134, 0.80546, 0.65704, 0.802, 0.63379, 0.79573, 0.60555, 0.78192, 0.57909, 0.7656, 0.68135, 0.81824, 0.54328, 0.78252, 0.57984, 0.80659, 0.62051, 0.82464, 0.65798, 0.83186, 0.545, 0.86252, 0.58737, 0.94021, 0.51639, 0.92675, 0.43842, 0.87449, 0.35033, 0.79875, 0.27046, 0.69911, 0.26148, 0.5751, 0.32615, 0.43239, 0.41359, 0.25439, 0.45524, 0.2484, 0.54629, 0.23719, 0.57597, 0.24604, 0.71316, 0.36744, 0.64485, 0.3087, 0.61209, 0.27491, 0.67649, 0.34348, 0.74004, 0.35859, 0.74844, 0.33303, 0.61965, 0.22097, 0.59044, 0.20524, 0.54918, 0.20685, 0.45725, 0.22617, 0.40743, 0.24112, 0.68657, 0.27602, 0.65283, 0.24898, 0.71918, 0.30502, 0.50189, 0.21498, 0.50055, 0.23915, 0.83757, 0.42041, 0.85938, 0.40058, 0.96375, 0.41015, 0.89774, 0.40092, 0.93761, 0.40273, 0.72334, 0.45793, 0.71428, 0.53441, 0.65738, 0.65941, 0.60228, 0.6501, 0.58258, 0.61945, 0.60252, 0.58546, 0.66194, 0.56762, 0.61938, 0.61391, 0.66224, 0.60472, 0.69866, 0.5966, 0.71272, 0.64835, 0.75568, 0.46463, 0.74989, 0.54093, 0.75375, 0.61666, 0.72612, 0.41408, 0.43489, 0.34974, 0.47719, 0.34755, 0.51658, 0.34901, 0.55681, 0.3592, 0.58417, 0.38031, 0.60325, 0.41162, 0.61486, 0.44183, 0.44775, 0.38359, 0.47097, 0.41489, 0.5087, 0.43382, 0.54644, 0.44328, 0.58293, 0.44401, 0.42162, 0.3228, 0.47802, 0.31407, 0.53193, 0.31698, 0.58293, 0.33263, 0.60988, 0.36466, 0.62025, 0.4047, 0.62813, 0.44438, 0.39534, 0.35186, 0.414, 0.41119, 0.47952, 0.46069, 0.55209, 0.47489, 0.61968, 0.4647, 0.70868, 0.14264, 0.80846, 0.21421, 0.89964, 0.26364, 0.45971, 0.5334, 0.41973, 0.66012, 0.60858, 0.52766], "triangles": [158, 116, 115, 114, 116, 6, 7, 114, 6, 9, 115, 116, 9, 116, 114, 9, 114, 7, 10, 115, 9, 8, 9, 7, 115, 113, 158, 113, 112, 100, 11, 112, 113, 12, 112, 11, 10, 113, 115, 11, 113, 10, 12, 13, 112, 109, 107, 157, 101, 109, 157, 109, 99, 97, 109, 97, 107, 96, 99, 109, 109, 101, 96, 148, 97, 99, 100, 96, 101, 100, 101, 113, 131, 96, 100, 96, 149, 99, 112, 131, 100, 113, 101, 158, 102, 103, 156, 95, 103, 102, 108, 102, 156, 107, 108, 156, 98, 95, 102, 98, 102, 108, 97, 98, 108, 97, 108, 107, 146, 95, 98, 147, 98, 97, 104, 49, 103, 104, 110, 49, 110, 105, 49, 94, 110, 104, 111, 105, 110, 111, 110, 94, 106, 48, 105, 95, 104, 103, 94, 104, 95, 93, 106, 105, 93, 105, 111, 92, 106, 93, 145, 93, 111, 146, 111, 94, 146, 94, 95, 144, 92, 93, 92, 47, 106, 51, 0, 1, 156, 51, 1, 50, 51, 156, 103, 49, 50, 103, 50, 156, 49, 105, 48, 157, 156, 1, 157, 1, 2, 47, 48, 106, 158, 2, 3, 158, 3, 4, 157, 2, 158, 157, 107, 156, 158, 4, 5, 145, 111, 146, 144, 93, 145, 147, 146, 98, 101, 157, 158, 145, 132, 144, 134, 133, 145, 146, 134, 145, 133, 132, 145, 47, 144, 151, 144, 47, 92, 151, 144, 132, 46, 47, 151, 135, 146, 147, 134, 146, 135, 148, 147, 97, 136, 135, 147, 148, 136, 147, 139, 132, 133, 6, 158, 5, 6, 116, 158, 149, 148, 99, 136, 148, 149, 139, 152, 151, 139, 151, 132, 137, 136, 149, 140, 139, 133, 152, 139, 140, 41, 42, 43, 40, 41, 43, 91, 46, 151, 91, 151, 152, 45, 46, 91, 112, 13, 131, 134, 140, 133, 141, 134, 135, 141, 135, 136, 142, 141, 136, 141, 140, 134, 138, 137, 149, 137, 142, 136, 143, 142, 137, 138, 143, 137, 149, 131, 150, 131, 149, 96, 138, 149, 150, 117, 150, 131, 128, 117, 131, 153, 140, 141, 152, 140, 153, 13, 128, 131, 155, 138, 150, 154, 142, 143, 141, 142, 154, 153, 141, 154, 155, 154, 143, 155, 143, 138, 14, 128, 13, 161, 154, 155, 159, 152, 153, 117, 155, 150, 118, 117, 128, 129, 118, 128, 118, 155, 117, 161, 155, 118, 14, 129, 128, 40, 44, 39, 44, 40, 43, 45, 39, 44, 39, 37, 38, 45, 37, 39, 15, 129, 14, 123, 161, 118, 90, 45, 91, 37, 45, 90, 161, 159, 154, 122, 161, 123, 126, 123, 118, 125, 123, 126, 122, 123, 125, 124, 122, 125, 130, 129, 15, 130, 15, 16, 161, 122, 159, 121, 122, 124, 17, 130, 16, 126, 130, 127, 129, 126, 118, 130, 126, 129, 125, 126, 127, 36, 37, 90, 120, 121, 124, 119, 124, 125, 119, 125, 127, 120, 124, 119, 159, 91, 152, 159, 90, 91, 160, 90, 159, 159, 153, 154, 121, 159, 122, 18, 130, 17, 127, 130, 18, 19, 119, 127, 19, 127, 18, 89, 90, 160, 36, 90, 89, 54, 120, 119, 53, 54, 119, 53, 119, 19, 35, 36, 89, 121, 160, 159, 55, 58, 121, 120, 55, 121, 58, 160, 121, 54, 55, 120, 52, 53, 19, 56, 57, 58, 55, 56, 58, 52, 19, 20, 59, 160, 58, 66, 59, 58, 57, 66, 58, 67, 57, 56, 66, 57, 67, 60, 59, 66, 60, 66, 67, 70, 55, 54, 68, 56, 55, 69, 68, 55, 67, 56, 68, 34, 35, 89, 70, 69, 55, 71, 54, 53, 71, 53, 52, 70, 54, 71, 62, 59, 60, 78, 69, 70, 63, 60, 67, 62, 60, 63, 72, 71, 52, 77, 70, 71, 77, 71, 72, 78, 70, 77, 80, 68, 69, 80, 69, 78, 21, 73, 20, 20, 73, 72, 20, 72, 52, 22, 73, 21, 76, 77, 72, 76, 72, 73, 59, 88, 89, 59, 89, 160, 88, 59, 62, 75, 76, 73, 68, 63, 67, 80, 63, 68, 80, 61, 63, 74, 73, 22, 75, 73, 74, 81, 78, 77, 80, 78, 81, 79, 74, 22, 82, 77, 76, 81, 77, 82, 83, 75, 74, 83, 74, 79, 82, 76, 75, 83, 82, 75, 23, 79, 22, 83, 79, 23, 64, 61, 80, 64, 80, 81, 24, 83, 23, 65, 81, 82, 64, 81, 65, 88, 34, 89, 33, 34, 88, 84, 64, 65, 25, 82, 83, 25, 83, 24, 26, 65, 82, 25, 26, 82, 87, 88, 62, 61, 87, 62, 61, 62, 63, 84, 87, 61, 84, 61, 64, 33, 88, 87, 27, 65, 26, 84, 65, 27, 86, 87, 84, 85, 84, 27, 85, 27, 28, 86, 84, 85, 29, 85, 28, 32, 87, 86, 33, 87, 32, 30, 86, 85, 30, 85, 29, 31, 32, 86, 31, 86, 30], "vertices": [2, 9, 133.02, -9.27, 0.9675, 68, -233.56, 63.32, 0.0325, 2, 9, 130.26, -35.76, 0.96915, 68, -236.32, 36.83, 0.03085, 2, 9, 125.83, -50.11, 0.97342, 68, -240.75, 22.47, 0.02658, 2, 9, 127.56, -57.95, 0.97695, 68, -239.02, 14.63, 0.02305, 2, 9, 120.26, -69.54, 0.97994, 68, -246.32, 3.04, 0.02006, 2, 9, 106.08, -76.87, 0.97984, 68, -260.5, -4.28, 0.02016, 3, 9, 88.19, -81.46, 0.52622, 68, -278.39, -8.87, 0.0106, 13, 0.88, -9.25, 0.46319, 3, 68, -285.61, -11.73, 0.00138, 69, -220.27, -11.73, 0.0018, 13, -0.8, -1.67, 0.99683, 4, 68, -289.8, -12.84, 0.00142, 69, -224.45, -12.84, 0.00192, 14, -11.1, 5.96, 1e-05, 13, -1.23, 2.63, 0.99665, 4, 68, -291.28, -6.68, 0.00359, 69, -225.93, -6.68, 0.00102, 14, -4.9, 4.67, 0.00961, 13, 5.08, 3.12, 0.98578, 4, 68, -293.17, -0.5, 0.00581, 69, -227.82, -0.5, 0.0003, 14, 1.5, 3.75, 0.66491, 13, 11.48, 4.02, 0.32899, 4, 68, -295.55, 5.53, 0.00799, 69, -230.2, 5.53, 8e-05, 14, 7.97, 3.33, 0.99111, 13, 17.82, 5.42, 0.00082, 3, 68, -295.27, 8.73, 0.00906, 69, -229.93, 8.73, 1e-05, 14, 10.75, 1.71, 0.99092, 3, 9, 66.1, -60.25, 0.73195, 68, -300.48, 12.34, 0.02598, 14, 16.23, 4.88, 0.24207, 2, 9, 58.78, -58.9, 0.96846, 68, -307.79, 13.68, 0.03154, 2, 9, 47.41, -63.4, 0.96793, 68, -319.17, 9.19, 0.03207, 2, 9, 38.94, -69.09, 0.96761, 68, -327.64, 3.5, 0.03239, 2, 9, 33.11, -71.43, 0.96745, 68, -333.47, 1.15, 0.03255, 2, 9, 26.84, -68.98, 0.96749, 68, -339.74, 3.6, 0.03251, 2, 9, 20.36, -61.68, 0.96909, 68, -346.22, 10.9, 0.03091, 2, 9, 12.64, -63.09, 0.96775, 68, -353.94, 9.5, 0.03225, 2, 9, 10.24, -66.8, 0.96871, 68, -356.34, 5.79, 0.03129, 2, 9, 5.17, -68.47, 0.97046, 68, -361.41, 4.11, 0.02954, 2, 9, -4.11, -66.06, 0.97467, 68, -370.69, 6.53, 0.02533, 2, 9, -7.41, -65.92, 0.97229, 68, -373.99, 6.67, 0.02771, 2, 9, -10.56, -64.08, 0.97128, 68, -377.14, 8.51, 0.02872, 2, 9, -13.82, -61.06, 0.97091, 68, -380.4, 11.52, 0.02909, 2, 9, -17.65, -61.87, 0.97284, 68, -384.23, 10.71, 0.02716, 2, 9, -22.7, -69.2, 0.97991, 68, -389.27, 3.39, 0.02009, 2, 9, -30.75, -67.62, 0.97664, 68, -397.33, 4.96, 0.02336, 2, 9, -36.76, -64.21, 0.97548, 68, -403.34, 8.38, 0.02452, 2, 9, -41.16, -55.14, 0.97214, 68, -407.74, 17.45, 0.02786, 2, 9, -40.89, -45, 0.97032, 68, -407.47, 27.58, 0.02968, 2, 9, -31.89, -16.76, 0.97068, 68, -398.47, 55.83, 0.02932, 2, 9, -22.58, 11.22, 0.97374, 68, -389.16, 83.8, 0.02626, 2, 9, -17.04, 20.64, 0.97418, 68, -383.62, 93.22, 0.02582, 2, 9, -8.78, 27.85, 0.9742, 68, -375.36, 100.43, 0.0258, 2, 9, 8.47, 34.37, 0.9743, 68, -358.11, 106.95, 0.0257, 2, 9, 6.25, 42.12, 0.97659, 68, -360.33, 114.71, 0.02341, 2, 9, 22.77, 57.04, 0.9798, 68, -343.81, 129.62, 0.0202, 2, 9, 40.85, 64.42, 0.98048, 68, -325.73, 137.01, 0.01952, 2, 9, 58.02, 63.67, 0.98029, 68, -308.56, 136.26, 0.01971, 2, 9, 61.46, 56.78, 0.9784, 68, -305.11, 129.37, 0.0216, 2, 9, 56.37, 45.91, 0.97519, 68, -310.21, 118.49, 0.02481, 2, 9, 38.84, 35.82, 0.97285, 68, -327.74, 108.41, 0.02715, 2, 9, 34.42, 30.95, 0.97184, 68, -332.16, 103.53, 0.02816, 2, 9, 51.42, 29.24, 0.9703, 68, -315.16, 101.82, 0.0297, 2, 9, 68.59, 27.11, 0.97008, 68, -297.99, 99.69, 0.02992, 2, 9, 84.62, 20.6, 0.96856, 68, -281.96, 93.19, 0.03144, 2, 9, 103.06, -1.78, 0.96284, 68, -263.52, 70.81, 0.03716, 2, 9, 111.37, -6.67, 0.96337, 68, -255.21, 65.91, 0.03663, 2, 9, 120.26, -9.9, 0.96448, 68, -246.32, 62.69, 0.03552, 2, 9, 13.76, -60.54, 0.96695, 68, -352.81, 12.05, 0.03305, 2, 9, 15.43, -55.58, 0.96671, 68, -351.15, 17, 0.03329, 2, 9, 12.83, -48.96, 0.9661, 68, -353.74, 23.62, 0.0339, 2, 9, 6.9, -42.69, 0.96615, 68, -359.67, 29.9, 0.03385, 2, 9, 2.66, -37.72, 0.96637, 68, -363.92, 34.86, 0.03363, 2, 9, 0.96, -33.44, 0.96645, 68, -365.61, 39.14, 0.03355, 2, 9, 3.13, -31.49, 0.96603, 68, -363.45, 41.09, 0.03397, 2, 9, -2.07, -28.24, 0.96568, 68, -368.65, 44.35, 0.03432, 2, 9, -2.31, -33.14, 0.96647, 68, -368.89, 39.44, 0.03353, 2, 9, -10.57, -41.03, 0.96672, 68, -377.14, 31.55, 0.03328, 2, 9, -6.44, -31.19, 0.96636, 68, -373.02, 41.4, 0.03364, 2, 9, -6.03, -35.87, 0.96643, 68, -372.6, 36.72, 0.03357, 2, 9, -13.3, -49.16, 0.96785, 68, -379.88, 23.42, 0.03215, 2, 9, -14.68, -57.43, 0.96905, 68, -381.26, 15.16, 0.03095, 2, 9, -0.39, -32.82, 0.96637, 68, -366.97, 39.76, 0.03363, 2, 9, -0.45, -36.64, 0.96694, 68, -367.03, 35.94, 0.03306, 2, 9, -0.09, -41.2, 0.96729, 68, -366.67, 31.39, 0.03271, 2, 9, 1.33, -46.36, 0.96763, 68, -365.25, 26.22, 0.03237, 2, 9, 3.76, -50, 0.96711, 68, -362.82, 22.58, 0.03289, 2, 9, 4.55, -55, 0.96729, 68, -362.03, 17.59, 0.03271, 2, 9, 3.41, -59.63, 0.96893, 68, -363.17, 12.96, 0.03107, 2, 9, 1.92, -63.98, 0.97158, 68, -364.66, 8.6, 0.02842, 2, 9, -0.39, -65.8, 0.97294, 68, -366.97, 6.79, 0.02706, 2, 9, -0.68, -63.47, 0.97177, 68, -367.26, 9.11, 0.02823, 2, 9, -1.05, -59.65, 0.97022, 68, -367.62, 12.94, 0.02978, 2, 9, -0.46, -54.57, 0.96856, 68, -367.04, 18.01, 0.03144, 2, 9, 0.64, -49.58, 0.96791, 68, -365.93, 23, 0.03209, 2, 9, -0.76, -66.26, 0.9739, 68, -367.34, 6.32, 0.0261, 2, 9, -4.34, -45.53, 0.96545, 68, -370.92, 27.06, 0.03455, 2, 9, -6.12, -52.52, 0.96567, 68, -372.7, 20.06, 0.03433, 2, 9, -6.66, -59.71, 0.96714, 68, -373.24, 12.88, 0.03286, 2, 9, -5.59, -65.67, 0.97044, 68, -372.16, 6.91, 0.02956, 2, 9, -17.53, -51.31, 0.96875, 68, -384.11, 21.28, 0.03125, 2, 9, -27.88, -62.86, 0.97075, 68, -394.45, 9.73, 0.02925, 2, 9, -29.94, -51.57, 0.96711, 68, -396.52, 21.01, 0.03289, 2, 9, -25.99, -36.58, 0.96593, 68, -392.56, 36, 0.03407, 2, 9, -18.74, -18.5, 0.96528, 68, -385.32, 54.09, 0.03472, 2, 9, -7.02, 0.04, 0.96601, 68, -373.6, 72.62, 0.03399, 2, 9, 13.05, 9.92, 0.96466, 68, -353.53, 82.5, 0.03534, 2, 9, 40.69, 10.34, 0.96254, 68, -325.89, 82.93, 0.03746, 4, 69, -225.65, 82.47, 0.00613, 10, -1.44, -1.9, 0.98408, 11, -15, -22.62, 0.00255, 12, -34.04, -23.91, 0.00724, 4, 69, -222.13, 76.8, 0.00415, 10, 5.23, -1.82, 0.98556, 11, -10.99, -17.29, 0.00587, 12, -30.24, -18.43, 0.00443, 3, 10, 19.75, -1.99, 0.91392, 11, -2.01, -5.88, 0.08311, 12, -21.69, -6.69, 0.00297, 3, 10, 24.15, -4.27, 0.38584, 11, 2.48, -3.78, 0.60967, 12, -17.28, -4.43, 0.00449, 3, 69, -226.28, 30.95, 0.00977, 10, 42.3, -29.13, 0.01193, 12, 13.5, -4.05, 0.9783, 4, 69, -220.66, 44.97, 0.0039, 10, 33.21, -17.06, 0.0322, 11, 18.14, -4.39, 0.60631, 12, -1.61, -4.45, 0.35759, 4, 69, -217.03, 52.09, 0.00129, 10, 29.01, -10.27, 0.06345, 11, 10.2, -3.58, 0.90865, 12, -9.58, -3.94, 0.02661, 4, 69, -224.52, 37.95, 0.007, 10, 37.21, -24, 0.02234, 11, 26.08, -5.45, 0.05158, 12, 6.37, -5.22, 0.91908, 3, 69, -223.18, 27.64, 0.01052, 10, 46.73, -28.2, 0.00135, 12, 15.3, 0.11, 0.98813, 3, 69, -218.42, 28.18, 0.00968, 11, 32.99, 3.78, 0.00493, 12, 12.92, 4.26, 0.98539, 2, 11, 4.34, 4.25, 0.9991, 12, -15.73, 3.67, 0.0009, 2, 10, 27.51, 2.65, 0.39479, 11, -0.95, 3.1, 0.60521, 3, 69, -209.53, 65.97, 0.00014, 10, 21.03, 3.34, 0.98934, 11, -5.46, -1.62, 0.01051, 2, 69, -218.32, 78.04, 0.0048, 10, 6.14, 2.08, 0.9952, 3, 69, -223.82, 84.28, 0.00695, 10, -2.04, 0.61, 0.99169, 12, -36.44, -22.95, 0.00136, 3, 69, -212.69, 41.14, 0.00416, 11, 18.83, 4.42, 0.63397, 12, -1.25, 4.37, 0.36187, 3, 69, -210.25, 47.93, 0.00142, 11, 11.61, 4.24, 0.9857, 12, -8.46, 3.93, 0.01289, 3, 69, -215.54, 34.38, 0.00702, 11, 26.16, 4.22, 0.04256, 12, 6.08, 4.45, 0.95042, 2, 69, -213.75, 72.31, 0.00265, 10, 13.42, 3.02, 0.99735, 4, 69, -217.85, 70.83, 0.00197, 10, 12.56, -1.25, 0.98401, 11, -6.98, -11.13, 0.01146, 12, -26.46, -12.13, 0.00257, 3, 68, -292.88, 9.14, 0.00895, 69, -227.54, 9.14, 1e-05, 14, 10.09, -0.62, 0.99105, 4, 68, -288.27, 7.33, 0.00776, 69, -222.92, 7.33, 5e-05, 14, 6.48, -4.02, 0.97814, 13, 18.44, -2.06, 0.01406, 4, 68, -283.53, -8.56, 0.00211, 69, -218.18, -8.56, 0.00136, 14, -9.91, -1.53, 0.0054, 13, 2, -4.23, 0.99114, 4, 68, -286, 1.7, 0.00569, 69, -220.65, 1.7, 0.0002, 14, 0.43, -3.68, 0.59591, 13, 12.53, -3.41, 0.3982, 4, 68, -283.88, -4.24, 0.00353, 69, -218.53, -4.24, 0.00073, 14, -5.84, -3.06, 0.02863, 13, 6.33, -4.57, 0.96712, 2, 9, 60.53, -49.37, 0.96601, 68, -306.05, 23.21, 0.03399, 2, 9, 47.27, -53.34, 0.96454, 68, -319.31, 19.25, 0.03546, 2, 9, 23.04, -53.67, 0.9673, 68, -343.54, 18.91, 0.0327, 2, 9, 21.25, -44.99, 0.96785, 68, -345.33, 27.6, 0.03215, 2, 9, 25.15, -40, 0.96633, 68, -341.43, 32.59, 0.03367, 2, 9, 32.01, -40.56, 0.96633, 68, -334.57, 32.03, 0.03367, 2, 9, 38.57, -47.99, 0.96449, 68, -328, 24.59, 0.03551, 2, 9, 28.3, -44.98, 0.96458, 68, -338.28, 27.6, 0.03542, 2, 9, 32.43, -50.6, 0.96374, 68, -334.15, 21.98, 0.03626, 2, 9, 35.99, -55.35, 0.96332, 68, -330.59, 17.23, 0.03668, 2, 9, 28.24, -60.98, 0.96448, 68, -338.34, 11.6, 0.03552, 2, 9, 61.38, -54.55, 0.96655, 68, -305.2, 18.03, 0.03345, 2, 9, 48.35, -58.98, 0.9654, 68, -318.23, 13.6, 0.0346, 2, 9, 35.99, -64.78, 0.96259, 68, -330.59, 7.81, 0.03741, 3, 9, 67.99, -46.75, 0.69301, 68, -298.59, 25.83, 0.02386, 12, 20.95, -8.42, 0.28313, 2, 9, 61.02, 0.19, 0.96416, 68, -305.56, 72.77, 0.03584, 2, 9, 63.95, -5.83, 0.96283, 68, -302.63, 66.75, 0.03717, 2, 9, 66.1, -11.68, 0.96242, 68, -300.48, 60.9, 0.03758, 2, 9, 66.84, -18.26, 0.96233, 68, -299.74, 54.33, 0.03767, 2, 9, 64.99, -23.71, 0.96296, 68, -301.59, 48.88, 0.03704, 2, 9, 60.95, -28.65, 0.96426, 68, -305.63, 43.93, 0.03574, 2, 9, 56.63, -32.43, 0.96521, 68, -309.95, 40.15, 0.03479, 2, 9, 56.17, -4.03, 0.96331, 68, -310.41, 68.56, 0.03669, 2, 9, 52.38, -9.58, 0.96259, 68, -314.2, 63, 0.03741, 2, 9, 51.52, -16.39, 0.96263, 68, -315.06, 56.19, 0.03737, 2, 9, 52.24, -22.55, 0.96318, 68, -314.34, 50.03, 0.03682, 2, 9, 54.33, -27.93, 0.96434, 68, -312.25, 44.66, 0.03566, 2, 9, 64.69, 3.98, 0.96487, 68, -301.89, 76.57, 0.03513, 2, 9, 69.57, -3.64, 0.96351, 68, -297.01, 68.94, 0.03649, 2, 9, 72.35, -11.71, 0.96231, 68, -294.23, 60.88, 0.03769, 2, 9, 72.84, -20.23, 0.96229, 68, -293.74, 52.35, 0.03771, 2, 9, 69.15, -26.38, 0.96369, 68, -297.43, 46.21, 0.03631, 2, 9, 63.13, -30.66, 0.96481, 68, -303.45, 41.93, 0.03519, 2, 9, 57.01, -34.55, 0.9657, 68, -309.57, 38.04, 0.0343, 2, 9, 58.27, 5.81, 0.96466, 68, -308.31, 78.39, 0.03534, 2, 9, 49.54, -1.01, 0.96228, 68, -317.04, 71.57, 0.03772, 2, 9, 45.28, -13.99, 0.96217, 68, -321.29, 58.59, 0.03783, 2, 9, 47.33, -25.56, 0.96364, 68, -319.25, 47.02, 0.03636, 2, 9, 53.12, -34.72, 0.96572, 68, -313.46, 37.87, 0.03428, 2, 9, 112.05, -25.45, 0.9636, 68, -254.53, 47.13, 0.0364, 2, 9, 106.2, -44.95, 0.96717, 68, -260.38, 27.63, 0.03283, 2, 9, 103.52, -61.67, 0.97275, 68, -263.06, 10.91, 0.02725, 2, 9, 32, -16.12, 0.96, 68, -334.58, 56.46, 0.04, 2, 9, 8.51, -19.05, 0.9629, 68, -358.07, 53.54, 0.0371, 2, 9, 41.98, -37.45, 0.96521, 68, -324.6, 35.14, 0.03479], "hull": 52, "edges": [0, 102, 10, 12, 62, 64, 78, 80, 80, 82, 82, 84, 98, 100, 100, 102, 24, 26, 26, 28, 28, 30, 12, 14, 14, 16, 6, 8, 8, 10, 2, 4, 4, 6, 0, 2, 96, 98, 94, 96, 84, 86, 86, 88, 88, 90, 76, 78, 72, 74, 74, 76, 68, 70, 70, 72, 64, 66, 66, 68, 58, 60, 60, 62, 56, 58, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 34, 36, 36, 38, 42, 44, 44, 46, 104, 106, 40, 42, 104, 40, 38, 40, 106, 108, 108, 110, 110, 112, 112, 114, 116, 118, 124, 118, 120, 126, 126, 122, 122, 128, 128, 130, 130, 52, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 138, 148, 158, 158, 46, 160, 162, 162, 164, 164, 166, 168, 54, 170, 172, 174, 176, 176, 178, 178, 180, 180, 182, 184, 186, 188, 190, 190, 196, 196, 194, 192, 198, 198, 194, 192, 200, 200, 202, 204, 206, 206, 208, 210, 212, 212, 184, 204, 216, 216, 214, 202, 218, 218, 214, 208, 220, 220, 210, 186, 222, 222, 188, 24, 224, 224, 226, 226, 230, 20, 22, 22, 24, 16, 18, 18, 20, 228, 232, 232, 230, 234, 236, 238, 240, 240, 242, 242, 244, 244, 246, 248, 250, 236, 252, 252, 254, 256, 258, 258, 260, 30, 32, 32, 34, 90, 92, 92, 94, 264, 266, 266, 268, 268, 270, 270, 272, 272, 274, 274, 276, 264, 278, 278, 280, 280, 282, 282, 284, 284, 286, 286, 276, 288, 290, 290, 292, 292, 294, 294, 296, 296, 298, 298, 300, 302, 304, 304, 306, 306, 308, 308, 310, 318, 320, 322, 244], "width": 158, "height": 180}}, "head2": {"head": {"type": "mesh", "uvs": [0.27046, 0.69911, 0.41973, 0.66012, 0.58258, 0.61945, 0.60228, 0.6501, 0.65738, 0.65941, 0.6977, 0.69027, 0.70746, 0.73174, 0.72333, 0.75194, 0.72078, 0.78952, 0.68414, 0.83198, 0.6736, 0.85276, 0.65, 0.86689, 0.62183, 0.86924, 0.62158, 0.88681, 0.63701, 0.92715, 0.60824, 0.96512, 0.57369, 0.98866, 0.50999, 0.9919, 0.45139, 0.96891, 0.30821, 0.8625, 0.16733, 0.75507, 0.67499, 0.72165, 0.65008, 0.70253, 0.60507, 0.70174, 0.55398, 0.71878, 0.51465, 0.72999, 0.48553, 0.72954, 0.4794, 0.71429, 0.44773, 0.73402, 0.47583, 0.74568, 0.50188, 0.80487, 0.45437, 0.76272, 0.48272, 0.77056, 0.54275, 0.83625, 0.5877, 0.86091, 0.47861, 0.73515, 0.50078, 0.74363, 0.52831, 0.75147, 0.56192, 0.75524, 0.5891, 0.75053, 0.62021, 0.75712, 0.64452, 0.77282, 0.66633, 0.78976, 0.67134, 0.80546, 0.65704, 0.802, 0.63379, 0.79573, 0.60555, 0.78192, 0.57909, 0.7656, 0.68135, 0.81824, 0.54328, 0.78252, 0.57984, 0.80659, 0.62051, 0.82464, 0.65798, 0.83186, 0.545, 0.86252, 0.58737, 0.94021, 0.51639, 0.92675, 0.43842, 0.87449, 0.35033, 0.79875], "triangles": [23, 3, 4, 22, 23, 4, 22, 4, 5, 27, 1, 2, 24, 27, 2, 3, 24, 2, 23, 24, 3, 21, 22, 5, 25, 26, 27, 24, 25, 27, 21, 5, 6, 28, 1, 27, 35, 28, 27, 26, 35, 27, 36, 26, 25, 35, 26, 36, 29, 28, 35, 29, 35, 36, 39, 24, 23, 37, 25, 24, 38, 37, 24, 36, 25, 37, 39, 38, 24, 40, 23, 22, 40, 22, 21, 39, 23, 40, 31, 28, 29, 47, 38, 39, 32, 29, 36, 31, 29, 32, 41, 40, 21, 46, 39, 40, 46, 40, 41, 47, 39, 46, 49, 37, 38, 49, 38, 47, 7, 42, 6, 6, 42, 41, 6, 41, 21, 8, 42, 7, 45, 46, 41, 45, 41, 42, 28, 57, 0, 28, 0, 1, 57, 28, 31, 44, 45, 42, 37, 32, 36, 49, 32, 37, 49, 30, 32, 43, 42, 8, 44, 42, 43, 50, 47, 46, 49, 47, 50, 48, 43, 8, 51, 46, 45, 50, 46, 51, 52, 44, 43, 52, 43, 48, 51, 45, 44, 52, 51, 44, 9, 48, 8, 52, 48, 9, 33, 30, 49, 33, 49, 50, 10, 52, 9, 34, 50, 51, 33, 50, 34, 57, 20, 0, 19, 20, 57, 53, 33, 34, 11, 51, 52, 11, 52, 10, 12, 34, 51, 11, 12, 51, 56, 57, 31, 30, 56, 31, 30, 31, 32, 53, 56, 30, 53, 30, 33, 19, 57, 56, 13, 34, 12, 53, 34, 13, 55, 56, 53, 54, 53, 13, 54, 13, 14, 55, 53, 54, 15, 54, 14, 18, 56, 55, 19, 56, 18, 16, 55, 54, 16, 54, 15, 17, 18, 55, 17, 55, 16], "vertices": [2, 9, -7.02, 0.04, 0.96601, 68, -373.6, 72.62, 0.03399, 2, 9, 8.51, -19.05, 0.9629, 68, -358.07, 53.54, 0.0371, 2, 9, 25.15, -40, 0.96633, 68, -341.43, 32.59, 0.03367, 2, 9, 21.25, -44.99, 0.96785, 68, -345.33, 27.6, 0.03215, 2, 9, 23.04, -53.67, 0.9673, 68, -343.54, 18.91, 0.0327, 2, 9, 20.36, -61.68, 0.96909, 68, -346.22, 10.9, 0.03091, 2, 9, 14.06, -65.97, 0.96775, 68, -352.52, 6.61, 0.03225, 2, 9, 11.66, -69.69, 0.96871, 68, -354.92, 2.9, 0.03129, 2, 9, 5.26, -71.91, 0.97046, 68, -361.32, 0.68, 0.02954, 2, 9, -4.02, -69.5, 0.97467, 68, -370.6, 3.09, 0.02533, 2, 9, -8.11, -69.39, 0.97229, 68, -374.69, 3.19, 0.02771, 2, 9, -11.89, -66.93, 0.97128, 68, -378.47, 5.66, 0.02872, 2, 9, -13.99, -62.98, 0.97091, 68, -380.57, 9.6, 0.02909, 2, 9, -16.93, -64.16, 0.97284, 68, -383.51, 8.43, 0.02716, 2, 9, -22.7, -69.2, 0.97991, 68, -389.27, 3.39, 0.02009, 2, 9, -30.75, -67.62, 0.97664, 68, -397.33, 4.96, 0.02336, 2, 9, -36.76, -64.21, 0.97548, 68, -403.34, 8.38, 0.02452, 2, 9, -41.16, -55.14, 0.97214, 68, -407.74, 17.45, 0.02786, 2, 9, -40.89, -45, 0.97032, 68, -407.47, 27.58, 0.02968, 2, 9, -31.89, -16.76, 0.97068, 68, -398.47, 55.83, 0.02932, 2, 9, -22.58, 11.22, 0.97374, 68, -389.16, 83.8, 0.02626, 2, 9, 13.76, -60.54, 0.96695, 68, -352.81, 12.05, 0.03305, 2, 9, 15.43, -55.58, 0.96671, 68, -351.15, 17, 0.03329, 2, 9, 12.83, -48.96, 0.9661, 68, -353.74, 23.62, 0.0339, 2, 9, 6.9, -42.69, 0.96615, 68, -359.67, 29.9, 0.03385, 2, 9, 2.66, -37.72, 0.96637, 68, -363.92, 34.86, 0.03363, 2, 9, 0.96, -33.44, 0.96645, 68, -365.61, 39.14, 0.03355, 2, 9, 3.13, -31.49, 0.96603, 68, -363.45, 41.09, 0.03397, 2, 9, -2.07, -28.24, 0.96568, 68, -368.65, 44.35, 0.03432, 2, 9, -2.31, -33.14, 0.96647, 68, -368.89, 39.44, 0.03353, 2, 9, -10.57, -41.03, 0.96672, 68, -377.14, 31.55, 0.03328, 2, 9, -6.44, -31.19, 0.96636, 68, -373.02, 41.4, 0.03364, 2, 9, -6.03, -35.87, 0.96643, 68, -372.6, 36.72, 0.03357, 2, 9, -13.3, -49.16, 0.96785, 68, -379.88, 23.42, 0.03215, 2, 9, -14.68, -57.43, 0.96905, 68, -381.26, 15.16, 0.03095, 2, 9, -0.39, -32.82, 0.96637, 68, -366.97, 39.76, 0.03363, 2, 9, -0.45, -36.64, 0.96694, 68, -367.03, 35.94, 0.03306, 2, 9, -0.09, -41.2, 0.96729, 68, -366.67, 31.39, 0.03271, 2, 9, 1.33, -46.36, 0.96763, 68, -365.25, 26.22, 0.03237, 2, 9, 3.76, -50, 0.96711, 68, -362.82, 22.58, 0.03289, 2, 9, 4.55, -55, 0.96729, 68, -362.03, 17.59, 0.03271, 2, 9, 3.41, -59.63, 0.96824, 68, -363.17, 12.96, 0.03176, 2, 9, 1.92, -63.98, 0.97079, 68, -364.66, 8.6, 0.02921, 2, 9, -0.39, -65.8, 0.97208, 68, -366.97, 6.79, 0.02792, 2, 9, -0.68, -63.47, 0.97096, 68, -367.26, 9.11, 0.02904, 2, 9, -1.05, -59.65, 0.96949, 68, -367.62, 12.94, 0.03051, 2, 9, -0.46, -54.57, 0.96856, 68, -367.04, 18.01, 0.03144, 2, 9, 0.64, -49.58, 0.96791, 68, -365.93, 23, 0.03209, 2, 9, -1.9, -68.14, 0.97299, 68, -368.48, 4.44, 0.02701, 2, 9, -4.34, -45.53, 0.96545, 68, -370.92, 27.06, 0.03455, 2, 9, -6.12, -52.52, 0.96567, 68, -372.7, 20.06, 0.03433, 2, 9, -6.66, -59.71, 0.96714, 68, -373.24, 12.88, 0.03286, 2, 9, -5.59, -65.67, 0.97044, 68, -372.16, 6.91, 0.02956, 2, 9, -17.53, -51.31, 0.96875, 68, -384.11, 21.28, 0.03125, 2, 9, -27.88, -62.86, 0.97075, 68, -394.45, 9.73, 0.02925, 2, 9, -29.94, -51.57, 0.96711, 68, -396.52, 21.01, 0.03289, 2, 9, -25.99, -36.58, 0.96593, 68, -392.56, 36, 0.03407, 2, 9, -18.74, -18.5, 0.96528, 68, -385.32, 54.09, 0.03472], "hull": 21, "edges": [34, 36, 36, 38, 38, 40, 30, 32, 32, 34, 28, 30, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 14, 16, 16, 18, 42, 44, 12, 14, 42, 12, 10, 12, 44, 46, 46, 48, 48, 50, 50, 52, 54, 56, 62, 56, 58, 64, 64, 60, 60, 66, 66, 68, 68, 24, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 76, 86, 96, 96, 18, 98, 100, 100, 102, 102, 104, 106, 26, 108, 110, 112, 114, 114, 0, 8, 6, 6, 4, 4, 2, 2, 0, 0, 40, 8, 10], "width": 158, "height": 180}}, "head_B": {"head_B": {"type": "mesh", "uvs": [0.9752, 0.13032, 0.98525, 0.21615, 0.98408, 0.25926, 0.95674, 0.31076, 0.85441, 0.40695, 0.80708, 0.46426, 0.78498, 0.59223, 0.72098, 0.67486, 0.52182, 0.81583, 0.32945, 0.94293, 0.27881, 0.99165, 1e-05, 0.8683, 0.05636, 0.78448, 0.21586, 0.54724, 0.44426, 0.2075, 0.75673, 0.00299, 0.72376, 0.2354, 0.7498, 0.24355, 0.82974, 0.24472, 0.90788, 0.24995, 0.56297, 0.35035, 0.60159, 0.33644, 0.65576, 0.33053, 0.70832, 0.33331, 0.76089, 0.34756, 0.79521, 0.37224, 0.80272, 0.40422, 0.76035, 0.44106, 0.71208, 0.4421, 0.66005, 0.4282, 0.61232, 0.40456, 0.5796, 0.37189, 0.79253, 0.43028, 0.54538, 0.34522, 0.59692, 0.3131, 0.66321, 0.30208, 0.73741, 0.2995, 0.80763, 0.31294, 0.83954, 0.34345, 0.85151, 0.37964, 0.54306, 0.36121, 0.58615, 0.40878, 0.66354, 0.45686, 0.74732, 0.4734, 0.65589, 0.5604, 0.56111, 0.67505, 0.42541, 0.8048, 0.30094, 0.86138, 0.30103, 0.8859, 0.37002, 0.62453, 0.44071, 0.46748, 0.26701, 0.7422, 0.61857, 0.25118, 0.49518, 0.31815], "triangles": [0, 19, 18, 19, 37, 18, 1, 19, 0, 3, 19, 2, 2, 19, 1, 18, 17, 16, 37, 17, 18, 36, 16, 17, 16, 0, 18, 15, 52, 14, 16, 15, 0, 16, 52, 15, 35, 52, 16, 36, 35, 16, 36, 17, 37, 52, 53, 14, 34, 52, 35, 34, 53, 52, 22, 34, 35, 23, 35, 36, 22, 35, 23, 21, 34, 22, 3, 37, 19, 3, 38, 37, 33, 53, 34, 20, 33, 34, 24, 36, 37, 24, 37, 38, 23, 36, 24, 21, 20, 34, 40, 53, 33, 40, 33, 20, 31, 20, 21, 40, 20, 31, 25, 24, 38, 39, 38, 3, 25, 38, 39, 26, 25, 39, 22, 31, 21, 30, 31, 22, 4, 39, 3, 26, 39, 4, 41, 31, 30, 40, 31, 41, 23, 30, 22, 29, 23, 24, 28, 29, 24, 24, 25, 28, 29, 30, 23, 28, 25, 26, 32, 27, 26, 26, 27, 28, 42, 29, 28, 41, 30, 29, 42, 41, 29, 4, 32, 26, 5, 32, 4, 27, 32, 5, 53, 13, 14, 50, 53, 40, 50, 40, 41, 43, 28, 27, 43, 27, 5, 42, 28, 43, 53, 50, 13, 42, 50, 41, 44, 42, 43, 44, 50, 42, 6, 43, 5, 44, 43, 6, 49, 13, 50, 49, 50, 44, 7, 44, 6, 45, 49, 44, 45, 44, 7, 51, 13, 49, 12, 13, 51, 46, 49, 45, 51, 49, 46, 8, 46, 45, 8, 45, 7, 47, 51, 46, 12, 51, 47, 11, 12, 47, 48, 47, 46, 11, 47, 48, 9, 48, 46, 9, 46, 8, 10, 11, 48, 10, 48, 9], "vertices": [2, 9, 91.21, -78.53, 0.97954, 68, -275.37, -5.95, 0.02046, 2, 69, -219.61, -10.82, 0.0004, 13, 0, -2.46, 0.9996, 2, 69, -224.63, -12.8, 0.0005, 13, -1.17, 2.81, 0.9995, 2, 9, 69.81, -85.81, 0.9818, 68, -296.77, -13.23, 0.0182, 2, 9, 55.53, -82.77, 0.98263, 68, -311.05, -10.19, 0.01737, 2, 9, 47.44, -81.98, 0.98259, 68, -319.14, -9.4, 0.01741, 2, 9, 31.98, -86.47, 0.98357, 68, -334.6, -13.88, 0.01643, 2, 9, 20.45, -85.65, 0.98331, 68, -346.13, -13.06, 0.01669, 2, 9, -2.01, -77.51, 0.98148, 68, -368.59, -4.93, 0.01852, 2, 9, -22.66, -69.22, 0.97991, 68, -389.24, 3.36, 0.02009, 2, 9, -29.86, -67.77, 0.97664, 68, -396.44, 4.81, 0.02336, 2, 9, -24.29, -41, 0.96929, 68, -390.87, 31.58, 0.03071, 2, 9, -12.86, -41.19, 0.9693, 68, -379.44, 31.39, 0.0307, 2, 9, 19.48, -41.74, 0.96882, 68, -347.1, 30.84, 0.03118, 2, 9, 65.8, -42.53, 0.96745, 68, -300.78, 30.06, 0.03255, 2, 9, 99.12, -56.09, 0.971, 68, -267.46, 16.5, 0.029, 2, 68, -295.31, 7.81, 0.009, 14, 9.93, 2.14, 0.991, 3, 68, -295.45, 5.48, 0.0079, 14, 7.87, 3.26, 0.99121, 13, 17.74, 5.32, 0.00089, 3, 68, -293.09, -0.56, 0.0055, 14, 1.41, 3.71, 0.66125, 13, 11.41, 3.95, 0.33325, 3, 69, -225.92, -6.65, 0.00065, 14, -4.88, 4.66, 0.00999, 13, 5.11, 3.11, 0.98936, 2, 9, 53, -58.26, 0.97319, 68, -313.58, 14.33, 0.02681, 2, 9, 55.8, -60.48, 0.97366, 68, -310.78, 12.1, 0.02634, 2, 9, 58.17, -64.25, 0.97458, 68, -308.41, 8.34, 0.02542, 2, 9, 59.48, -68.31, 0.9758, 68, -307.1, 4.27, 0.0242, 2, 9, 59.47, -72.93, 0.97758, 68, -307.11, -0.34, 0.02242, 2, 9, 57.69, -76.68, 0.97924, 68, -308.89, -4.09, 0.02076, 2, 9, 54.23, -78.78, 0.9804, 68, -312.34, -6.19, 0.0196, 2, 9, 48.66, -77.37, 0.97998, 68, -317.91, -4.79, 0.02002, 2, 9, 47.04, -73.81, 0.97846, 68, -319.54, -1.23, 0.02154, 2, 9, 47.03, -69.25, 0.97664, 68, -319.55, 3.33, 0.02336, 2, 9, 48.27, -64.55, 0.97503, 68, -318.3, 8.03, 0.02497, 2, 9, 51.03, -60.54, 0.97379, 68, -315.55, 12.05, 0.02621, 2, 9, 50.91, -79.26, 0.9808, 68, -315.67, -6.68, 0.0192, 2, 9, 53.04, -56.7, 0.97281, 68, -313.54, 15.89, 0.02719, 2, 9, 58.35, -59.01, 0.97329, 68, -308.23, 13.57, 0.02671, 2, 9, 61.69, -63.44, 0.97454, 68, -304.89, 9.14, 0.02546, 2, 9, 64.29, -68.87, 0.97647, 68, -302.29, 3.72, 0.02353, 2, 9, 64.92, -74.76, 0.97886, 68, -301.66, -2.18, 0.02114, 2, 9, 62.39, -78.61, 0.98047, 68, -304.19, -6.03, 0.01953, 2, 9, 58.59, -81.24, 0.98162, 68, -307.99, -8.66, 0.01838, 2, 9, 51.13, -57.29, 0.973, 68, -315.45, 15.29, 0.027, 2, 9, 46.97, -62.8, 0.97473, 68, -319.6, 9.79, 0.02527, 2, 9, 43.83, -70.89, 0.97782, 68, -322.75, 1.69, 0.02218, 2, 9, 44.53, -77.95, 0.98082, 68, -322.05, -5.37, 0.01918, 2, 9, 31.64, -75.29, 0.97677, 68, -334.94, -2.7, 0.02323, 2, 9, 15.46, -73.7, 0.97686, 68, -351.12, -1.11, 0.02314, 2, 9, -3.73, -69.77, 0.97792, 68, -370.31, 2.81, 0.02208, 2, 9, -14.13, -63.18, 0.97091, 68, -380.71, 9.41, 0.02909, 2, 9, -16.96, -64.36, 0.97284, 68, -383.54, 8.22, 0.02716, 2, 9, 15.35, -56.98, 0.97438, 68, -351.23, 15.6, 0.02562, 2, 9, 35.68, -54.73, 0.97276, 68, -330.9, 17.85, 0.02724, 2, 9, -1.43, -54.92, 0.97411, 68, -368.01, 17.66, 0.02589, 3, 9, 66.17, -57.66, 0.74276, 68, -300.41, 14.93, 0.0273, 14, 18.54, 3.71, 0.22994, 2, 9, 54.61, -51.64, 0.97083, 68, -311.97, 20.94, 0.02917], "hull": 16, "edges": [0, 2, 6, 8, 8, 10, 20, 22, 2, 4, 4, 6, 30, 0, 28, 30, 32, 34, 34, 36, 36, 38, 38, 4, 10, 12, 12, 14, 14, 16, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 54, 56, 58, 60, 60, 62, 62, 40, 56, 58, 52, 64, 64, 54, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 80, 82, 82, 84, 84, 86, 88, 90, 90, 92, 92, 94, 94, 96, 18, 20, 96, 18, 16, 18, 98, 100, 98, 102, 22, 24, 24, 26, 26, 28, 104, 106, 106, 100], "width": 81, "height": 125}}, "leg_L": {"leg_L": {"type": "mesh", "uvs": [0.84286, 0.03687, 0.8188, 0.07042, 0.78885, 0.11044, 0.79338, 0.1307, 0.82705, 0.163, 0.87658, 0.21051, 0.90892, 0.27634, 0.8859, 0.37225, 0.85551, 0.42604, 0.82747, 0.46161, 0.82013, 0.48867, 0.80453, 0.51627, 0.76595, 0.54344, 0.75165, 0.57181, 0.73833, 0.63348, 0.70486, 0.7215, 0.7116, 0.80007, 0.71826, 0.81661, 0.72937, 0.83492, 0.74481, 0.85175, 0.78668, 0.87417, 0.81106, 0.90399, 0.87479, 0.94734, 0.90785, 0.94992, 0.981, 0.95448, 1, 0.96444, 1, 0.97124, 0.913, 0.98645, 0.78547, 0.98616, 0.64948, 0.97853, 0.57781, 0.96932, 0.57917, 0.99917, 0.51849, 0.99907, 0.51779, 0.92526, 0.48207, 0.89319, 0.48298, 0.87293, 0.50338, 0.85904, 0.50407, 0.8424, 0.49862, 0.82478, 0.4826, 0.80597, 0.44311, 0.77357, 0.33363, 0.6943, 0.3039, 0.65566, 0.30302, 0.61224, 0.3203, 0.57926, 0.36698, 0.55, 0.41135, 0.52657, 0.45203, 0.50521, 0.46342, 0.49038, 0.46286, 0.46622, 0.44454, 0.43936, 0.38849, 0.39715, 0.29046, 0.32759, 0.22092, 0.27269, 0.17594, 0.24979, 0.0717, 0.21414, 0.01189, 0.17476, 0, 0.14774, 0.01421, 0.12213, 0.04715, 0.10116, 0.15526, 0.06246, 0.31389, 0.02789, 0.44514, 0.00024, 0.31791, 0.23177, 0.46443, 0.21191, 0.58953, 0.1867, 0.67626, 0.15534, 0.38263, 0.26707, 0.44562, 0.33058, 0.53084, 0.40393, 0.69387, 0.23342, 0.73092, 0.31318, 0.74945, 0.39047, 0.73482, 0.45681, 0.73482, 0.48627, 0.72265, 0.51213, 0.6821, 0.53979, 0.66318, 0.56493, 0.56046, 0.46076, 0.5537, 0.48447, 0.54019, 0.50818, 0.52397, 0.53332, 0.47801, 0.5581, 0.41313, 0.5931, 0.39962, 0.63908, 0.57668, 0.76444, 0.62128, 0.63915, 0.63885, 0.60124, 0.20044, 0.21116, 0.33762, 0.18137, 0.41145, 0.13972, 0.40781, 0.09072, 0.30519, 0.06034], "triangles": [32, 30, 31, 32, 33, 30, 28, 22, 27, 22, 23, 27, 26, 27, 25, 25, 27, 23, 28, 29, 22, 22, 29, 21, 25, 23, 24, 29, 30, 21, 30, 33, 21, 33, 34, 20, 20, 34, 35, 36, 20, 35, 33, 20, 21, 20, 36, 19, 19, 37, 18, 37, 19, 36, 18, 38, 17, 18, 37, 38, 38, 16, 17, 38, 39, 16, 39, 85, 16, 39, 40, 85, 85, 15, 16, 40, 41, 85, 15, 85, 86, 84, 85, 41, 86, 85, 84, 15, 86, 14, 41, 42, 84, 42, 43, 84, 86, 87, 14, 87, 86, 83, 86, 84, 83, 84, 43, 83, 14, 87, 13, 43, 44, 83, 83, 82, 87, 87, 77, 13, 87, 82, 77, 44, 45, 83, 83, 45, 82, 13, 77, 12, 12, 77, 76, 82, 81, 77, 77, 81, 76, 45, 46, 82, 82, 46, 81, 76, 75, 12, 12, 75, 11, 81, 80, 76, 76, 80, 75, 46, 47, 81, 81, 47, 80, 11, 75, 10, 80, 79, 75, 47, 48, 80, 10, 75, 74, 75, 79, 74, 80, 48, 79, 48, 49, 79, 10, 74, 9, 79, 78, 74, 78, 73, 74, 74, 73, 9, 79, 49, 78, 49, 50, 78, 9, 73, 8, 73, 78, 69, 78, 50, 69, 73, 72, 8, 73, 69, 72, 50, 51, 69, 8, 72, 7, 72, 69, 68, 69, 51, 68, 51, 52, 68, 68, 71, 72, 72, 71, 7, 7, 71, 6, 52, 67, 68, 71, 67, 70, 71, 68, 67, 67, 64, 70, 52, 53, 67, 71, 70, 6, 70, 5, 6, 53, 63, 67, 53, 54, 63, 67, 63, 64, 54, 88, 63, 54, 55, 88, 64, 65, 70, 70, 65, 4, 70, 4, 5, 63, 89, 64, 63, 88, 89, 55, 56, 88, 64, 89, 65, 88, 56, 89, 58, 89, 57, 89, 58, 59, 90, 89, 60, 60, 89, 59, 89, 90, 65, 65, 90, 66, 60, 91, 90, 89, 56, 57, 66, 90, 91, 91, 60, 92, 92, 61, 91, 91, 61, 62, 92, 60, 61, 91, 62, 1, 4, 65, 66, 66, 3, 4, 3, 66, 2, 2, 66, 91, 2, 91, 1, 1, 62, 0], "vertices": [1, 3, 53.68, 19.91, 1, 1, 3, 87.39, 28.1, 1, 2, 3, 127.76, 37.58, 0.9956, 28, -36.61, 97.02, 0.0044, 2, 3, 145.98, 47.21, 0.92736, 28, -16.12, 94.73, 0.07264, 2, 3, 172.09, 69.08, 0.6462, 28, 17.76, 98.12, 0.3538, 2, 3, 210.49, 101.23, 0.28034, 28, 67.6, 103.11, 0.71966, 2, 3, 267.76, 136.88, 0.05746, 28, 135, 100.36, 0.94254, 2, 3, 359.04, 171.57, 0.0002, 28, 229.98, 77.7, 0.9998, 1, 28, 282.44, 60.34, 1, 2, 28, 316.77, 46.74, 0.98071, 29, -39.87, 39.17, 0.01929, 2, 28, 343.52, 40.12, 0.72963, 29, -12.32, 38.38, 0.27037, 2, 28, 370.44, 31.22, 0.18027, 29, 15.88, 35.4, 0.81973, 1, 29, 43.91, 26.19, 1, 1, 29, 72.88, 23.59, 1, 1, 29, 135.63, 22.72, 1, 1, 29, 225.37, 17.59, 1, 1, 29, 305.05, 22.88, 1, 2, 29, 321.76, 25.41, 0.9655, 30, -20.91, 33.32, 0.0345, 2, 29, 340.21, 29.22, 0.65362, 30, -2.17, 31.35, 0.34638, 2, 29, 357.12, 34.13, 0.20442, 30, 15.43, 30.9, 0.79558, 2, 29, 379.38, 46.41, 0.00556, 30, 40.36, 35.86, 0.99444, 1, 30, 71.33, 34.29, 1, 1, 30, 118.34, 39.39, 1, 1, 30, 123.21, 47.32, 1, 1, 30, 132.84, 65.17, 1, 1, 30, 143.95, 67.48, 1, 1, 30, 150.62, 65.67, 1, 1, 30, 159.39, 38.96, 1, 1, 30, 150.11, 5.8, 1, 1, 30, 133.03, -27.61, 1, 1, 30, 118.94, -43.84, 1, 1, 30, 148.31, -51.42, 1, 1, 30, 143.92, -67.2, 1, 1, 30, 71.5, -47.79, 1, 1, 30, 37.52, -48.57, 1, 2, 29, 381.7, -35.56, 0.0414, 30, 17.72, -42.96, 0.9586, 2, 29, 367.35, -30.68, 0.20633, 30, 5.53, -33.95, 0.79367, 2, 29, 350.46, -31.23, 0.62047, 30, -10.73, -29.36, 0.37953, 2, 29, 332.63, -33.48, 0.9349, 30, -28.4, -26.09, 0.0651, 2, 29, 313.73, -38.63, 0.99864, 30, -47.98, -25.27, 0.00136, 1, 29, 281.31, -50.71, 1, 1, 29, 202.13, -83.75, 1, 1, 29, 163.26, -93.48, 1, 1, 29, 119.19, -95.64, 1, 2, 28, 411.33, -108.49, 0.00011, 29, 85.52, -92.43, 0.99989, 2, 28, 384.17, -91.03, 0.01752, 29, 55.26, -81.14, 0.98248, 2, 28, 362.75, -75.18, 0.10899, 29, 30.96, -70.21, 0.89101, 2, 28, 343.22, -60.67, 0.3701, 29, 8.8, -60.18, 0.6299, 2, 28, 328.89, -55.09, 0.62988, 29, -6.39, -57.76, 0.37012, 2, 28, 304.68, -51.07, 0.92364, 29, -30.9, -58.98, 0.07636, 2, 28, 276.94, -51.31, 0.99788, 29, -57.96, -65.11, 0.00212, 1, 28, 232.11, -58.95, 1, 1, 28, 157.97, -73.04, 1, 3, 5, 244.26, 57.39, 0.00548, 28, 99.82, -82.07, 0.94452, 34, -26.91, -145.49, 0.05, 3, 5, 228.11, 36.7, 0.01922, 28, 74.83, -90.09, 0.78078, 34, -39.05, -122.23, 0.2, 3, 5, 206.79, -3.91, 0.07148, 28, 34.35, -111.68, 0.72852, 35, -28.96, -80.39, 0.2, 3, 5, 177.06, -35.18, 0.14355, 28, -7.81, -120.81, 0.65645, 36, -12.89, -36.14, 0.2, 3, 5, 153.4, -49.47, 0.1982, 28, -35.42, -119.31, 0.6018, 36, -16.1, -8.68, 0.2, 3, 5, 128.13, -56.74, 0.2596, 28, -60.4, -111.11, 0.5404, 36, -12.26, 17.34, 0.2, 3, 5, 105.05, -57.47, 0.32266, 28, -79.9, -98.73, 0.47734, 36, -3.37, 38.65, 0.2, 3, 5, 57.18, -47.18, 0.54137, 28, -113.69, -63.29, 0.25863, 35, -6.4, 73.71, 0.2, 3, 5, 7.47, -22.74, 0.73233, 28, -141.04, -15.12, 0.06767, 34, -1.81, 103.23, 0.2, 4, 3, 64.27, -93.25, 0.17269, 5, -32.77, -2.11, 0.7004, 6, -42.12, 111.67, 0.02691, 34, 33.63, 131.31, 0.1, 4, 5, 195.58, 64.02, 0.00996, 28, 63.29, -49.21, 0.77409, 72, -475.11, -104.73, 0.01994, 34, -0.72, -103.92, 0.19601, 4, 5, 160.83, 91.67, 0.00054, 28, 50.12, -6.8, 0.78389, 72, -435.55, -84.55, 0.01947, 34, 38.84, -83.74, 0.19611, 4, 3, 220.57, 20.67, 0.16341, 28, 30.61, 30.83, 0.62265, 72, -401.77, -58.93, 0.01743, 34, 72.61, -58.13, 0.19651, 4, 3, 181.87, 28.8, 0.55665, 28, 3.19, 59.32, 0.23827, 72, -378.36, -27.07, 0.00636, 34, 96.03, -26.27, 0.19873, 4, 5, 220.98, 94.77, 0.00072, 28, 101.6, -38.07, 0.9307, 72, -457.64, -140.58, 0.01956, 34, 16.75, -139.78, 0.04902, 2, 28, 168.07, -32.27, 0.98058, 72, -440.63, -205.11, 0.01942, 2, 28, 245.43, -22.24, 0.98032, 72, -417.62, -279.64, 0.01968, 3, 3, 252.11, 65.97, 0.11597, 28, 82.17, 50.54, 0.86436, 72, -373.6, -106.4, 0.01967, 3, 3, 321.73, 108.63, 0.00848, 28, 163.73, 46.65, 0.97123, 72, -363.6, -187.43, 0.02029, 2, 28, 241.97, 38.25, 0.98236, 72, -358.6, -265.96, 0.01764, 2, 28, 307.72, 22.92, 0.98095, 72, -362.55, -333.36, 0.01905, 3, 28, 337.21, 17.84, 0.82944, 29, -13.75, 15.26, 0.15182, 72, -362.55, -363.29, 0.01874, 3, 28, 362.55, 10.14, 0.07693, 29, 12.64, 13.13, 0.90535, 72, -365.83, -389.56, 0.01772, 2, 29, 41.19, 3.41, 0.98508, 72, -376.78, -417.66, 0.01492, 2, 29, 66.94, -0.58, 0.98575, 72, -381.89, -443.21, 0.01425, 3, 28, 303.69, -24.16, 0.96551, 29, -37.59, -32.9, 0.01514, 72, -409.62, -337.38, 0.01935, 3, 28, 327.11, -30.04, 0.78559, 29, -13.45, -33.67, 0.19513, 72, -411.45, -361.46, 0.01929, 3, 28, 350.23, -37.73, 0.34723, 29, 10.77, -36.27, 0.63385, 72, -415.1, -385.55, 0.01892, 3, 28, 374.66, -46.38, 0.07115, 29, 36.49, -39.53, 0.91081, 72, -419.48, -411.1, 0.01804, 3, 28, 397.37, -62.88, 0.00972, 29, 62.18, -50.83, 0.97461, 72, -431.88, -436.28, 0.01567, 2, 29, 98.47, -66.78, 0.98642, 72, -449.4, -471.84, 0.01358, 2, 29, 145.3, -68.39, 0.99132, 72, -453.05, -518.55, 0.00868, 1, 29, 270.47, -15.09, 1, 2, 29, 142.77, -8.6, 0.98993, 72, -393.2, -518.62, 0.01007, 2, 29, 104.08, -5.54, 0.98507, 72, -388.46, -480.1, 0.01493, 4, 5, 189.64, 26.48, 0.0462, 28, 37.27, -76.91, 0.73383, 72, -506.83, -83.78, 0.02496, 35, 5.8, -77.36, 0.19501, 4, 5, 146.76, 47.66, 0.03485, 28, 13.73, -35.27, 0.739, 72, -469.79, -53.51, 0.03269, 35, 42.84, -47.1, 0.19346, 5, 3, 197.03, -42.86, 0.10903, 5, 99.99, 48.28, 0.08569, 28, -24.59, -8.45, 0.57774, 72, -449.85, -11.2, 0.03443, 35, 62.77, -4.79, 0.19311, 5, 3, 152.12, -64.36, 0.22504, 5, 55.08, 26.78, 0.34378, 28, -73.81, -0.97, 0.20197, 72, -450.84, 38.58, 0.03652, 35, 61.79, 45, 0.1927, 5, 3, 135.49, -102.37, 0.02784, 5, 38.45, -11.22, 0.60297, 28, -108.94, -23.03, 0.1474, 72, -478.54, 69.45, 0.02724, 35, 34.08, 75.87, 0.19455], "hull": 63, "edges": [4, 6, 10, 12, 12, 14, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 72, 74, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 94, 96, 96, 98, 98, 100, 104, 106, 106, 108, 108, 110, 118, 120, 122, 124, 126, 128, 128, 130, 130, 132, 110, 112, 112, 114, 114, 116, 116, 118, 102, 104, 124, 0, 0, 2, 2, 4, 6, 8, 8, 10, 100, 102, 92, 94, 14, 16, 16, 18, 18, 20, 20, 22, 88, 90, 90, 92, 36, 38, 38, 40, 32, 34, 34, 36, 74, 76, 76, 78, 68, 70, 70, 72, 40, 42, 42, 44, 134, 136, 136, 138, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 138, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 154, 176, 178, 178, 180, 126, 108, 176, 110, 180, 182, 120, 122, 120, 184, 184, 182], "width": 270, "height": 1016}}}}], "animations": {"idle": {"bones": {"ALL2": {"translate": [{"y": -24.14, "curve": [0.444, 0, 0.889, 0, 0.444, -24.14, 0.889, 23.25]}, {"time": 1.3333, "y": 23.25, "curve": [1.778, 0, 2.222, 0, 1.778, 23.25, 2.222, -24.14]}, {"time": 2.6667, "y": -24.14, "curve": [3.111, 0, 3.556, 0, 3.111, -24.14, 3.556, 23.25]}, {"time": 4, "y": 23.25, "curve": [4.444, 0, 4.889, 0, 4.444, 23.25, 4.889, -24.14]}, {"time": 5.3333, "y": -24.14, "curve": [5.667, 0, 6, 0, 5.667, -24.14, 6, 23.25]}, {"time": 6.3333, "y": 23.25, "curve": [6.778, 0, 7.222, 0, 6.778, 23.25, 7.222, -0.45]}, {"time": 7.6667, "y": -0.45, "curve": [8.111, 0, 8.556, 0, 8.111, -0.45, 8.556, 23.25]}, {"time": 9, "y": 23.25, "curve": [9.444, 0, 9.889, 0, 9.444, 23.25, 9.889, -24.14]}, {"time": 10.3333, "y": -24.14, "curve": [10.778, 0, 11.222, 0, 10.778, -24.14, 11.222, 23.25]}, {"time": 11.6667, "y": 23.25, "curve": [12.111, 0, 12.556, 0, 12.111, 23.25, 12.556, -24.14]}, {"time": 13, "y": -24.14}]}, "tun": {"rotate": [{"value": -1.86, "curve": [0.444, -1.86, 0.889, 2.2]}, {"time": 1.3333, "value": 2.2, "curve": [1.778, 2.2, 2.222, -1.86]}, {"time": 2.6667, "value": -1.86, "curve": [3.111, -1.86, 3.556, 2.2]}, {"time": 4, "value": 2.2, "curve": [4.444, 2.2, 4.889, -1.86]}, {"time": 5.3333, "value": -1.86, "curve": [5.667, -1.86, 6, 2.2]}, {"time": 6.3333, "value": 2.2, "curve": [6.778, 2.2, 7.222, 0.17]}, {"time": 7.6667, "value": 0.17, "curve": [8.111, 0.17, 8.556, 2.2]}, {"time": 9, "value": 2.2, "curve": [9.444, 2.2, 9.889, -1.86]}, {"time": 10.3333, "value": -1.86, "curve": [10.778, -1.86, 11.222, 2.2]}, {"time": 11.6667, "value": 2.2, "curve": [12.111, 2.2, 12.556, -1.86]}, {"time": 13, "value": -1.86}]}, "body": {"rotate": [{"value": -3.15, "curve": [0.444, -3.15, 0.889, 3.1]}, {"time": 1.3333, "value": 3.1, "curve": [1.778, 3.1, 2.222, -3.15]}, {"time": 2.6667, "value": -3.15, "curve": [3.111, -3.15, 3.556, 3.1]}, {"time": 4, "value": 3.1, "curve": [4.444, 3.1, 4.889, -3.15]}, {"time": 5.3333, "value": -3.15, "curve": [5.667, -3.15, 6, 3.1]}, {"time": 6.3333, "value": 3.1, "curve": [6.778, 3.1, 7.222, -0.03]}, {"time": 7.6667, "value": -0.03, "curve": [8.111, -0.03, 8.556, 3.1]}, {"time": 9, "value": 3.1, "curve": [9.444, 3.1, 9.889, -3.15]}, {"time": 10.3333, "value": -3.15, "curve": [10.778, -3.15, 11.222, 3.1]}, {"time": 11.6667, "value": 3.1, "curve": [12.111, 3.1, 12.556, -3.15]}, {"time": 13, "value": -3.15}], "translate": [{"y": -10.44, "curve": [0.057, 0, 0.112, 0, 0.057, -10.95, 0.112, -11.34]}, {"time": 0.1667, "y": -11.34, "curve": [0.611, 0, 1.056, 0, 0.611, -11.34, 1.056, 7.94]}, {"time": 1.5, "y": 7.94, "curve": [1.944, 0, 2.389, 0, 1.944, 7.94, 2.389, -11.34]}, {"time": 2.8333, "y": -11.34, "curve": [3.278, 0, 3.722, 0, 3.278, -11.34, 3.722, 7.94]}, {"time": 4.1667, "y": 7.94, "curve": [4.611, 0, 5.056, 0, 4.611, 7.94, 5.056, -11.34]}, {"time": 5.5, "y": -11.34, "curve": [5.833, 0, 6.167, 0, 5.833, -11.34, 6.167, 7.94]}, {"time": 6.5, "y": 7.94, "curve": [6.944, 0, 7.389, 0, 6.944, 7.94, 7.389, -1.7]}, {"time": 7.8333, "y": -1.7, "curve": [8.278, 0, 8.722, 0, 8.278, -1.7, 8.722, 7.94]}, {"time": 9.1667, "y": 7.94, "curve": [9.611, 0, 10.056, 0, 9.611, 7.94, 10.056, -11.34]}, {"time": 10.5, "y": -11.34, "curve": [10.944, 0, 11.389, 0, 10.944, -11.34, 11.389, 7.94]}, {"time": 11.8333, "y": 7.94, "curve": [12.223, 0, 12.613, 0, 12.223, 7.94, 12.613, -6.78]}, {"time": 13, "y": -10.44}], "scale": [{"y": 1.041, "curve": [0.057, 1, 0.112, 1, 0.057, 1.043, 0.112, 1.045]}, {"time": 0.1667, "y": 1.045, "curve": [0.611, 1, 1.056, 1, 0.611, 1.045, 1.056, 0.955]}, {"time": 1.5, "y": 0.955, "curve": [1.944, 1, 2.389, 1, 1.944, 0.955, 2.389, 1.045]}, {"time": 2.8333, "y": 1.045, "curve": [3.278, 1, 3.722, 1, 3.278, 1.045, 3.722, 0.955]}, {"time": 4.1667, "y": 0.955, "curve": [4.611, 1, 5.056, 1, 4.611, 0.955, 5.056, 1.045]}, {"time": 5.5, "y": 1.045, "curve": [5.833, 1, 6.167, 1, 5.833, 1.045, 6.167, 0.955]}, {"time": 6.5, "y": 0.955, "curve": [6.944, 1, 7.389, 1, 6.944, 0.955, 7.389, 1]}, {"time": 7.8333, "curve": [8.278, 1, 8.722, 1, 8.278, 1, 8.722, 0.955]}, {"time": 9.1667, "y": 0.955, "curve": [9.611, 1, 10.056, 1, 9.611, 0.955, 10.056, 1.045]}, {"time": 10.5, "y": 1.045, "curve": [10.944, 1, 11.389, 1, 10.944, 1.045, 11.389, 0.955]}, {"time": 11.8333, "y": 0.955, "curve": [12.223, 1, 12.613, 1, 12.223, 0.955, 12.613, 1.024]}, {"time": 13, "y": 1.041}]}, "body2": {"rotate": [{"value": 1.87, "curve": [0.057, 1.97, 0.112, 2.04]}, {"time": 0.1667, "value": 2.04, "curve": [0.611, 2.04, 1.056, -1.74]}, {"time": 1.5, "value": -1.74, "curve": [1.944, -1.74, 2.389, 2.04]}, {"time": 2.8333, "value": 2.04, "curve": [3.278, 2.04, 3.722, -1.74]}, {"time": 4.1667, "value": -1.74, "curve": [4.611, -1.74, 5.056, 2.04]}, {"time": 5.5, "value": 2.04, "curve": [5.833, 2.04, 6.167, -1.74]}, {"time": 6.5, "value": -1.74, "curve": [6.944, -1.74, 7.389, 0.15]}, {"time": 7.8333, "value": 0.15, "curve": [8.278, 0.15, 8.722, -1.74]}, {"time": 9.1667, "value": -1.74, "curve": [9.611, -1.74, 10.056, 2.04]}, {"time": 10.5, "value": 2.04, "curve": [10.944, 2.04, 11.389, -1.74]}, {"time": 11.8333, "value": -1.74, "curve": [12.223, -1.74, 12.613, 1.15]}, {"time": 13, "value": 1.87}], "translate": [{"x": -5.04, "curve": [0.114, -6.25, 0.224, -7.14, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -7.14, "curve": [0.778, -7.14, 1.222, 6, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 6, "curve": [2.111, 6, 2.556, -7.14, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -7.14, "curve": [3.444, -7.14, 3.889, 6, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 6, "curve": [4.778, 6, 5.222, -7.14, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -7.14, "curve": [6, -7.14, 6.333, 6, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 6, "curve": [7.111, 6, 7.556, -0.57, 7.111, 0, 7.556, 0]}, {"time": 8, "x": -0.57, "curve": [8.444, -0.57, 8.889, 6, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 6, "curve": [9.778, 6, 10.222, -7.14, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -7.14, "curve": [11.111, -7.14, 11.556, 6, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 6, "curve": [12.335, 6, 12.67, -1.36, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -5.04}], "scale": [{"x": 1.005, "y": 1.005, "curve": [0.114, 1.002, 0.224, 1, 0.114, 1.002, 0.224, 1]}, {"time": 0.3333, "curve": [0.778, 1, 1.222, 1.028, 0.778, 1, 1.222, 1.028]}, {"time": 1.6667, "x": 1.028, "y": 1.028, "curve": [2.111, 1.028, 2.556, 1, 2.111, 1.028, 2.556, 1]}, {"time": 3, "curve": [3.444, 1, 3.889, 1.028, 3.444, 1, 3.889, 1.028]}, {"time": 4.3333, "x": 1.028, "y": 1.028, "curve": [4.778, 1.028, 5.222, 1, 4.778, 1.028, 5.222, 1]}, {"time": 5.6667, "curve": [6, 1, 6.333, 1.028, 6, 1, 6.333, 1.028]}, {"time": 6.6667, "x": 1.028, "y": 1.028, "curve": [7.111, 1.028, 7.556, 1.014, 7.111, 1.028, 7.556, 1.014]}, {"time": 8, "x": 1.014, "y": 1.014, "curve": [8.444, 1.014, 8.889, 1.028, 8.444, 1.014, 8.889, 1.028]}, {"time": 9.3333, "x": 1.028, "y": 1.028, "curve": [9.778, 1.028, 10.222, 1, 9.778, 1.028, 10.222, 1]}, {"time": 10.6667, "curve": [11.111, 1, 11.556, 1.028, 11.111, 1, 11.556, 1.028]}, {"time": 12, "x": 1.028, "y": 1.028, "curve": [12.335, 1.028, 12.67, 1.012, 12.335, 1.028, 12.67, 1.012]}, {"time": 13, "x": 1.005, "y": 1.005}]}, "neck": {"rotate": [{"value": -0.36, "curve": [0.168, -0.78, 0.334, -1.12]}, {"time": 0.5, "value": -1.12, "curve": [0.944, -1.12, 1.389, 1.27]}, {"time": 1.8333, "value": 1.27, "curve": [2.278, 1.27, 2.722, -1.12]}, {"time": 3.1667, "value": -1.12, "curve": [3.611, -1.12, 4.056, 1.27]}, {"time": 4.5, "value": 1.27, "curve": [4.944, 1.27, 5.389, -1.12]}, {"time": 5.8333, "value": -1.12, "curve": [6.167, -1.12, 6.5, 8.93]}, {"time": 6.8333, "value": 8.93, "curve": [7.278, 8.93, 7.722, 3.9]}, {"time": 8.1667, "value": 3.9, "curve": [8.611, 3.9, 9.056, 8.93]}, {"time": 9.5, "value": 8.93, "curve": [9.944, 8.93, 10.389, -1.12]}, {"time": 10.8333, "value": -1.12, "curve": [11.278, -1.12, 11.722, 1.27]}, {"time": 12.1667, "value": 1.27, "curve": [12.445, 1.27, 12.724, 0.34]}, {"time": 13, "value": -0.36}]}, "head": {"rotate": [{"value": 0.08, "curve": [0.225, -0.52, 0.446, -1.12]}, {"time": 0.6667, "value": -1.12, "curve": [1.111, -1.12, 1.556, 1.27]}, {"time": 2, "value": 1.27, "curve": [2.444, 1.27, 2.889, -1.12]}, {"time": 3.3333, "value": -1.12, "curve": [3.778, -1.12, 4.222, 1.27]}, {"time": 4.6667, "value": 1.27, "curve": [5.111, 1.27, 5.556, -1.12]}, {"time": 6, "value": -1.12, "curve": [6.333, -1.12, 6.667, 8.93]}, {"time": 7, "value": 8.93, "curve": [7.444, 8.93, 7.889, 3.9]}, {"time": 8.3333, "value": 3.9, "curve": [8.778, 3.9, 9.222, 8.93]}, {"time": 9.6667, "value": 8.93, "curve": [10.111, 8.93, 10.556, -1.12]}, {"time": 11, "value": -1.12, "curve": [11.444, -1.12, 11.889, 1.27]}, {"time": 12.3333, "value": 1.27, "curve": [12.557, 1.27, 12.781, 0.68]}, {"time": 13, "value": 0.08}]}, "eyebrow_R2": {"rotate": [{"time": 6.3333, "curve": [6.444, 0, 6.556, 5.04]}, {"time": 6.6667, "value": 5.04, "curve": "stepped"}, {"time": 9.6667, "value": 5.04, "curve": [9.889, 5.04, 10.111, 0]}, {"time": 10.3333}]}, "eyebrow_R3": {"rotate": [{"time": 6.3333, "curve": [6.444, 0, 6.556, 5.38]}, {"time": 6.6667, "value": 5.38, "curve": "stepped"}, {"time": 9.6667, "value": 5.38, "curve": [9.889, 5.38, 10.111, 0]}, {"time": 10.3333}]}, "eyebrow_L": {"rotate": [{"time": 6.3333, "curve": [6.444, 0, 6.556, -8.8]}, {"time": 6.6667, "value": -8.8, "curve": "stepped"}, {"time": 9.6667, "value": -8.8, "curve": [9.889, -8.8, 10.111, 0]}, {"time": 10.3333}]}, "eyebrow_L2": {"rotate": [{"time": 6.3333, "curve": [6.444, 0, 6.556, -11.61]}, {"time": 6.6667, "value": -11.61, "curve": "stepped"}, {"time": 9.6667, "value": -11.61, "curve": [9.889, -11.61, 10.111, 0]}, {"time": 10.3333}]}, "hair_F2": {"rotate": [{"value": -1.77, "curve": [0.279, 0.59, 0.556, 3.73]}, {"time": 0.8333, "value": 3.73, "curve": [1.278, 3.73, 1.722, -4.34]}, {"time": 2.1667, "value": -4.34, "curve": [2.611, -4.34, 3.056, 3.73]}, {"time": 3.5, "value": 3.73, "curve": [3.944, 3.73, 4.389, -4.34]}, {"time": 4.8333, "value": -4.34, "curve": [5.278, -4.34, 5.722, 3.73]}, {"time": 6.1667, "value": 3.73, "curve": [6.5, 3.73, 6.833, -4.34]}, {"time": 7.1667, "value": -4.34, "curve": [7.611, -4.34, 8.056, -0.3]}, {"time": 8.5, "value": -0.3, "curve": [8.944, -0.3, 9.389, -4.34]}, {"time": 9.8333, "value": -4.34, "curve": [10.278, -4.34, 10.722, 3.73]}, {"time": 11.1667, "value": 3.73, "curve": [11.611, 3.73, 12.056, -4.34]}, {"time": 12.5, "value": -4.34, "curve": [12.667, -4.34, 12.835, -3.2]}, {"time": 13, "value": -1.77}]}, "leg_R2": {"rotate": [{"time": 6.3333, "curve": [6.778, 0, 7.222, 0.04]}, {"time": 7.6667, "value": 0.04, "curve": [8.111, 0.04, 8.556, 0]}, {"time": 9}]}, "leg_R3": {"rotate": [{"curve": "stepped"}, {"time": 6.3333, "curve": [6.778, 0, 7.222, -0.45]}, {"time": 7.6667, "value": -0.45, "curve": [8.111, -0.45, 8.556, 0]}, {"time": 9}]}, "leg_L2": {"rotate": [{"time": 6.3333, "curve": [6.778, 0, 7.222, -0.23]}, {"time": 7.6667, "value": -0.23, "curve": [8.111, -0.23, 8.556, 0]}, {"time": 9}]}, "leg_L3": {"rotate": [{"value": -0.01, "curve": "stepped"}, {"time": 6.3333, "value": -0.01, "curve": [6.778, -0.01, 7.222, 0.03]}, {"time": 7.6667, "value": 0.03, "curve": [8.111, 0.03, 8.556, -0.01]}, {"time": 9, "value": -0.01}]}, "tun_R": {"translate": [{"x": 11.34, "y": 5.7, "curve": [0.057, 12.3, 0.112, 13.03, 0.057, 6.12, 0.112, 6.44]}, {"time": 0.1667, "x": 13.03, "y": 6.44, "curve": [0.611, 13.03, 1.056, -23, 0.611, 6.44, 1.056, -9.41]}, {"time": 1.5, "x": -23, "y": -9.41, "curve": [1.944, -23, 2.389, 13.03, 1.944, -9.41, 2.389, 6.44]}, {"time": 2.8333, "x": 13.03, "y": 6.44, "curve": [3.278, 13.03, 3.722, -23, 3.278, 6.44, 3.722, -9.41]}, {"time": 4.1667, "x": -23, "y": -9.41, "curve": [4.611, -23, 5.056, 13.03, 4.611, -9.41, 5.056, 6.44]}, {"time": 5.5, "x": 13.03, "y": 6.44, "curve": [5.833, 13.03, 6.167, -23, 5.833, 6.44, 6.167, -9.41]}, {"time": 6.5, "x": -23, "y": -9.41, "curve": [6.944, -23, 7.389, 13.03, 6.944, -9.41, 7.389, 6.44]}, {"time": 7.8333, "x": 13.03, "y": 6.44, "curve": [8.278, 13.03, 8.722, -23, 8.278, 6.44, 8.722, -9.41]}, {"time": 9.1667, "x": -23, "y": -9.41, "curve": [9.611, -23, 10.056, 13.03, 9.611, -9.41, 10.056, 6.44]}, {"time": 10.5, "x": 13.03, "y": 6.44, "curve": [10.944, 13.03, 11.389, -23, 10.944, 6.44, 11.389, -9.41]}, {"time": 11.8333, "x": -23, "y": -9.41, "curve": [12.223, -23, 12.613, 4.51, 12.223, -9.41, 12.613, 2.69]}, {"time": 13, "x": 11.34, "y": 5.7}], "scale": [{"x": 1.046, "y": 0.97, "curve": [0.056, 1.056, 0.111, 1.063, 0.056, 0.956, 0.111, 0.945]}, {"time": 0.1667, "x": 1.063, "y": 0.945, "curve": [0.389, 1.063, 0.611, 0.957, 0.389, 0.945, 0.611, 1.1]}, {"time": 0.8333, "x": 0.957, "y": 1.1, "curve": [1.056, 0.957, 1.278, 1.063, 1.056, 1.1, 1.278, 0.945]}, {"time": 1.5, "x": 1.063, "y": 0.945, "curve": [1.722, 1.063, 1.944, 0.957, 1.722, 0.945, 1.944, 1.1]}, {"time": 2.1667, "x": 0.957, "y": 1.1, "curve": [2.389, 0.957, 2.611, 1.063, 2.389, 1.1, 2.611, 0.945]}, {"time": 2.8333, "x": 1.063, "y": 0.945, "curve": [3.056, 1.063, 3.278, 0.957, 3.056, 0.945, 3.278, 1.1]}, {"time": 3.5, "x": 0.957, "y": 1.1, "curve": [3.722, 0.957, 3.944, 1.063, 3.722, 1.1, 3.944, 0.945]}, {"time": 4.1667, "x": 1.063, "y": 0.945, "curve": [4.389, 1.063, 4.611, 0.957, 4.389, 0.945, 4.611, 1.1]}, {"time": 4.8333, "x": 0.957, "y": 1.1, "curve": [5.056, 0.957, 5.278, 1.063, 5.056, 1.1, 5.278, 0.945]}, {"time": 5.5, "x": 1.063, "y": 0.945, "curve": [5.667, 1.063, 5.833, 0.957, 5.667, 0.945, 5.833, 1.1]}, {"time": 6, "x": 0.957, "y": 1.1, "curve": [6.167, 0.957, 6.333, 1.063, 6.167, 1.1, 6.333, 0.945]}, {"time": 6.5, "x": 1.063, "y": 0.945, "curve": [6.722, 1.063, 6.944, 0.957, 6.722, 0.945, 6.944, 1.1]}, {"time": 7.1667, "x": 0.957, "y": 1.1, "curve": [7.389, 0.957, 7.611, 1.063, 7.389, 1.1, 7.611, 0.945]}, {"time": 7.8333, "x": 1.063, "y": 0.945, "curve": [8.056, 1.063, 8.278, 0.957, 8.056, 0.945, 8.278, 1.1]}, {"time": 8.5, "x": 0.957, "y": 1.1, "curve": [8.722, 0.957, 8.944, 1.063, 8.722, 1.1, 8.944, 0.945]}, {"time": 9.1667, "x": 1.063, "y": 0.945, "curve": [9.389, 1.063, 9.611, 0.957, 9.389, 0.945, 9.611, 1.1]}, {"time": 9.8333, "x": 0.957, "y": 1.1, "curve": [10.056, 0.957, 10.278, 1.063, 10.056, 1.1, 10.278, 0.945]}, {"time": 10.5, "x": 1.063, "y": 0.945, "curve": [10.722, 1.063, 10.944, 0.957, 10.722, 0.945, 10.944, 1.1]}, {"time": 11.1667, "x": 0.957, "y": 1.1, "curve": [11.389, 0.957, 11.611, 1.063, 11.389, 1.1, 11.611, 0.945]}, {"time": 11.8333, "x": 1.063, "y": 0.945, "curve": [12.056, 1.063, 12.278, 0.957, 12.056, 0.945, 12.278, 1.1]}, {"time": 12.5, "x": 0.957, "y": 1.1, "curve": [12.667, 0.957, 12.833, 1.016, 12.667, 1.1, 12.833, 1.014]}, {"time": 13, "x": 1.046, "y": 0.97}]}, "tun_R2": {"translate": [{"x": 0.72, "y": -11.74, "curve": [0.114, 0.67, 0.224, 0.64, 0.114, -16.29, 0.224, -19.68]}, {"time": 0.3333, "x": 0.64, "y": -19.68, "curve": [0.778, 0.64, 1.222, 1.15, 0.778, -19.68, 1.222, 29.99]}, {"time": 1.6667, "x": 1.15, "y": 29.99, "curve": [2.111, 1.15, 2.556, 0.64, 2.111, 29.99, 2.556, -19.68]}, {"time": 3, "x": 0.64, "y": -19.68, "curve": [3.444, 0.64, 3.889, 1.15, 3.444, -19.68, 3.889, 29.99]}, {"time": 4.3333, "x": 1.15, "y": 29.99, "curve": [4.778, 1.15, 5.222, 0.64, 4.778, 29.99, 5.222, -19.68]}, {"time": 5.6667, "x": 0.64, "y": -19.68, "curve": [6, 0.64, 6.333, 1.15, 6, -19.68, 6.333, 29.99]}, {"time": 6.6667, "x": 1.15, "y": 29.99, "curve": [7.111, 1.15, 7.556, 0.64, 7.111, 29.99, 7.556, -19.68]}, {"time": 8, "x": 0.64, "y": -19.68, "curve": [8.444, 0.64, 8.889, 1.15, 8.444, -19.68, 8.889, 29.99]}, {"time": 9.3333, "x": 1.15, "y": 29.99, "curve": [9.778, 1.15, 10.222, 0.64, 9.778, 29.99, 10.222, -19.68]}, {"time": 10.6667, "x": 0.64, "y": -19.68, "curve": [11.111, 0.64, 11.556, 1.15, 11.111, -19.68, 11.556, 29.99]}, {"time": 12, "x": 1.15, "y": 29.99, "curve": [12.335, 1.15, 12.67, 0.86, 12.335, 29.99, 12.67, 2.17]}, {"time": 13, "x": 0.72, "y": -11.74}], "scale": [{"x": 1.01, "y": 1.023, "curve": [0.111, 1.036, 0.222, 1.063, 0.111, 0.984, 0.222, 0.945]}, {"time": 0.3333, "x": 1.063, "y": 0.945, "curve": [0.556, 1.063, 0.778, 0.957, 0.556, 0.945, 0.778, 1.1]}, {"time": 1, "x": 0.957, "y": 1.1, "curve": [1.222, 0.957, 1.444, 1.063, 1.222, 1.1, 1.444, 0.945]}, {"time": 1.6667, "x": 1.063, "y": 0.945, "curve": [1.889, 1.063, 2.111, 0.957, 1.889, 0.945, 2.111, 1.1]}, {"time": 2.3333, "x": 0.957, "y": 1.1, "curve": [2.556, 0.957, 2.778, 1.063, 2.556, 1.1, 2.778, 0.945]}, {"time": 3, "x": 1.063, "y": 0.945, "curve": [3.222, 1.063, 3.444, 0.957, 3.222, 0.945, 3.444, 1.1]}, {"time": 3.6667, "x": 0.957, "y": 1.1, "curve": [3.889, 0.957, 4.111, 1.063, 3.889, 1.1, 4.111, 0.945]}, {"time": 4.3333, "x": 1.063, "y": 0.945, "curve": [4.556, 1.063, 4.778, 0.957, 4.556, 0.945, 4.778, 1.1]}, {"time": 5, "x": 0.957, "y": 1.1, "curve": [5.222, 0.957, 5.444, 1.063, 5.222, 1.1, 5.444, 0.945]}, {"time": 5.6667, "x": 1.063, "y": 0.945, "curve": [5.833, 1.063, 6, 0.957, 5.833, 0.945, 6, 1.1]}, {"time": 6.1667, "x": 0.957, "y": 1.1, "curve": [6.333, 0.957, 6.5, 1.063, 6.333, 1.1, 6.5, 0.945]}, {"time": 6.6667, "x": 1.063, "y": 0.945, "curve": [6.889, 1.063, 7.111, 0.957, 6.889, 0.945, 7.111, 1.1]}, {"time": 7.3333, "x": 0.957, "y": 1.1, "curve": [7.556, 0.957, 7.778, 1.063, 7.556, 1.1, 7.778, 0.945]}, {"time": 8, "x": 1.063, "y": 0.945, "curve": [8.222, 1.063, 8.444, 0.957, 8.222, 0.945, 8.444, 1.1]}, {"time": 8.6667, "x": 0.957, "y": 1.1, "curve": [8.889, 0.957, 9.111, 1.063, 8.889, 1.1, 9.111, 0.945]}, {"time": 9.3333, "x": 1.063, "y": 0.945, "curve": [9.556, 1.063, 9.778, 0.957, 9.556, 0.945, 9.778, 1.1]}, {"time": 10, "x": 0.957, "y": 1.1, "curve": [10.222, 0.957, 10.444, 1.063, 10.222, 1.1, 10.444, 0.945]}, {"time": 10.6667, "x": 1.063, "y": 0.945, "curve": [10.889, 1.063, 11.111, 0.957, 10.889, 0.945, 11.111, 1.1]}, {"time": 11.3333, "x": 0.957, "y": 1.1, "curve": [11.556, 0.957, 11.778, 1.063, 11.556, 1.1, 11.778, 0.945]}, {"time": 12, "x": 1.063, "y": 0.945, "curve": [12.222, 1.063, 12.444, 0.957, 12.222, 0.945, 12.444, 1.1]}, {"time": 12.6667, "x": 0.957, "y": 1.1, "curve": [12.778, 0.957, 12.889, 0.983, 12.778, 1.1, 12.889, 1.061]}, {"time": 13, "x": 1.01, "y": 1.023}]}, "tun_R3": {"translate": [{"x": 0.84, "y": -8.11, "curve": [0.168, 0.81, 0.334, 0.77, 0.168, -16.78, 0.334, -23.9]}, {"time": 0.5, "x": 0.77, "y": -23.9, "curve": [0.944, 0.77, 1.389, 0.99, 0.944, -23.9, 1.389, 25.77]}, {"time": 1.8333, "x": 0.99, "y": 25.77, "curve": [2.278, 0.99, 2.722, 0.77, 2.278, 25.77, 2.722, -23.9]}, {"time": 3.1667, "x": 0.77, "y": -23.9, "curve": [3.611, 0.77, 4.056, 0.99, 3.611, -23.9, 4.056, 25.77]}, {"time": 4.5, "x": 0.99, "y": 25.77, "curve": [4.944, 0.99, 5.389, 0.77, 4.944, 25.77, 5.389, -23.9]}, {"time": 5.8333, "x": 0.77, "y": -23.9, "curve": [6.167, 0.77, 6.5, 0.99, 6.167, -23.9, 6.5, 25.77]}, {"time": 6.8333, "x": 0.99, "y": 25.77, "curve": [7.278, 0.99, 7.722, 0.77, 7.278, 25.77, 7.722, -23.9]}, {"time": 8.1667, "x": 0.77, "y": -23.9, "curve": [8.611, 0.77, 9.056, 0.99, 8.611, -23.9, 9.056, 25.77]}, {"time": 9.5, "x": 0.99, "y": 25.77, "curve": [9.944, 0.99, 10.389, 0.77, 9.944, 25.77, 10.389, -23.9]}, {"time": 10.8333, "x": 0.77, "y": -23.9, "curve": [11.278, 0.77, 11.722, 0.99, 11.278, -23.9, 11.722, 25.77]}, {"time": 12.1667, "x": 0.99, "y": 25.77, "curve": [12.445, 0.99, 12.724, 0.91, 12.445, 25.77, 12.724, 6.46]}, {"time": 13, "x": 0.84, "y": -8.11}], "scale": [{"x": 0.974, "y": 1.075, "curve": [0.167, 1.003, 0.333, 1.063, 0.167, 1.032, 0.333, 0.945]}, {"time": 0.5, "x": 1.063, "y": 0.945, "curve": [0.722, 1.063, 0.944, 0.957, 0.722, 0.945, 0.944, 1.1]}, {"time": 1.1667, "x": 0.957, "y": 1.1, "curve": [1.389, 0.957, 1.611, 1.063, 1.389, 1.1, 1.611, 0.945]}, {"time": 1.8333, "x": 1.063, "y": 0.945, "curve": [2.056, 1.063, 2.278, 0.957, 2.056, 0.945, 2.278, 1.1]}, {"time": 2.5, "x": 0.957, "y": 1.1, "curve": [2.722, 0.957, 2.944, 1.063, 2.722, 1.1, 2.944, 0.945]}, {"time": 3.1667, "x": 1.063, "y": 0.945, "curve": [3.389, 1.063, 3.611, 0.957, 3.389, 0.945, 3.611, 1.1]}, {"time": 3.8333, "x": 0.957, "y": 1.1, "curve": [4.056, 0.957, 4.278, 1.063, 4.056, 1.1, 4.278, 0.945]}, {"time": 4.5, "x": 1.063, "y": 0.945, "curve": [4.722, 1.063, 4.944, 0.957, 4.722, 0.945, 4.944, 1.1]}, {"time": 5.1667, "x": 0.957, "y": 1.1, "curve": [5.389, 0.957, 5.611, 1.063, 5.389, 1.1, 5.611, 0.945]}, {"time": 5.8333, "x": 1.063, "y": 0.945, "curve": [6, 1.063, 6.167, 0.957, 6, 0.945, 6.167, 1.1]}, {"time": 6.3333, "x": 0.957, "y": 1.1, "curve": [6.5, 0.957, 6.667, 1.063, 6.5, 1.1, 6.667, 0.945]}, {"time": 6.8333, "x": 1.063, "y": 0.945, "curve": [7.056, 1.063, 7.278, 0.957, 7.056, 0.945, 7.278, 1.1]}, {"time": 7.5, "x": 0.957, "y": 1.1, "curve": [7.722, 0.957, 7.944, 1.063, 7.722, 1.1, 7.944, 0.945]}, {"time": 8.1667, "x": 1.063, "y": 0.945, "curve": [8.389, 1.063, 8.611, 0.957, 8.389, 0.945, 8.611, 1.1]}, {"time": 8.8333, "x": 0.957, "y": 1.1, "curve": [9.056, 0.957, 9.278, 1.063, 9.056, 1.1, 9.278, 0.945]}, {"time": 9.5, "x": 1.063, "y": 0.945, "curve": [9.722, 1.063, 9.944, 0.957, 9.722, 0.945, 9.944, 1.1]}, {"time": 10.1667, "x": 0.957, "y": 1.1, "curve": [10.389, 0.957, 10.611, 1.063, 10.389, 1.1, 10.611, 0.945]}, {"time": 10.8333, "x": 1.063, "y": 0.945, "curve": [11.056, 1.063, 11.278, 0.957, 11.056, 0.945, 11.278, 1.1]}, {"time": 11.5, "x": 0.957, "y": 1.1, "curve": [11.722, 0.957, 11.944, 1.063, 11.722, 1.1, 11.944, 0.945]}, {"time": 12.1667, "x": 1.063, "y": 0.945, "curve": [12.389, 1.063, 12.611, 0.957, 12.389, 0.945, 12.611, 1.1]}, {"time": 12.8333, "x": 0.957, "y": 1.1, "curve": [12.889, 0.957, 12.944, 0.964, 12.889, 1.1, 12.944, 1.09]}, {"time": 13, "x": 0.974, "y": 1.075}]}, "tun_L": {"translate": [{"x": 11.34, "y": 5.7, "curve": [0.057, 12.3, 0.112, 13.03, 0.057, 6.12, 0.112, 6.44]}, {"time": 0.1667, "x": 13.03, "y": 6.44, "curve": [0.611, 13.03, 1.056, -23, 0.611, 6.44, 1.056, -9.41]}, {"time": 1.5, "x": -23, "y": -9.41, "curve": [1.944, -23, 2.389, 13.03, 1.944, -9.41, 2.389, 6.44]}, {"time": 2.8333, "x": 13.03, "y": 6.44, "curve": [3.278, 13.03, 3.722, -23, 3.278, 6.44, 3.722, -9.41]}, {"time": 4.1667, "x": -23, "y": -9.41, "curve": [4.611, -23, 5.056, 13.03, 4.611, -9.41, 5.056, 6.44]}, {"time": 5.5, "x": 13.03, "y": 6.44, "curve": [5.833, 13.03, 6.167, -23, 5.833, 6.44, 6.167, -9.41]}, {"time": 6.5, "x": -23, "y": -9.41, "curve": [6.944, -23, 7.389, 13.03, 6.944, -9.41, 7.389, 6.44]}, {"time": 7.8333, "x": 13.03, "y": 6.44, "curve": [8.278, 13.03, 8.722, -23, 8.278, 6.44, 8.722, -9.41]}, {"time": 9.1667, "x": -23, "y": -9.41, "curve": [9.611, -23, 10.056, 13.03, 9.611, -9.41, 10.056, 6.44]}, {"time": 10.5, "x": 13.03, "y": 6.44, "curve": [10.944, 13.03, 11.389, -23, 10.944, 6.44, 11.389, -9.41]}, {"time": 11.8333, "x": -23, "y": -9.41, "curve": [12.223, -23, 12.613, 4.51, 12.223, -9.41, 12.613, 2.69]}, {"time": 13, "x": 11.34, "y": 5.7}], "scale": [{"x": 1.046, "y": 0.97, "curve": [0.056, 1.056, 0.111, 1.063, 0.056, 0.956, 0.111, 0.945]}, {"time": 0.1667, "x": 1.063, "y": 0.945, "curve": [0.389, 1.063, 0.611, 0.957, 0.389, 0.945, 0.611, 1.1]}, {"time": 0.8333, "x": 0.957, "y": 1.1, "curve": [1.056, 0.957, 1.278, 1.063, 1.056, 1.1, 1.278, 0.945]}, {"time": 1.5, "x": 1.063, "y": 0.945, "curve": [1.722, 1.063, 1.944, 0.957, 1.722, 0.945, 1.944, 1.1]}, {"time": 2.1667, "x": 0.957, "y": 1.1, "curve": [2.389, 0.957, 2.611, 1.063, 2.389, 1.1, 2.611, 0.945]}, {"time": 2.8333, "x": 1.063, "y": 0.945, "curve": [3.056, 1.063, 3.278, 0.957, 3.056, 0.945, 3.278, 1.1]}, {"time": 3.5, "x": 0.957, "y": 1.1, "curve": [3.722, 0.957, 3.944, 1.063, 3.722, 1.1, 3.944, 0.945]}, {"time": 4.1667, "x": 1.063, "y": 0.945, "curve": [4.389, 1.063, 4.611, 0.957, 4.389, 0.945, 4.611, 1.1]}, {"time": 4.8333, "x": 0.957, "y": 1.1, "curve": [5.056, 0.957, 5.278, 1.063, 5.056, 1.1, 5.278, 0.945]}, {"time": 5.5, "x": 1.063, "y": 0.945, "curve": [5.667, 1.063, 5.833, 0.957, 5.667, 0.945, 5.833, 1.1]}, {"time": 6, "x": 0.957, "y": 1.1, "curve": [6.167, 0.957, 6.333, 1.063, 6.167, 1.1, 6.333, 0.945]}, {"time": 6.5, "x": 1.063, "y": 0.945, "curve": [6.722, 1.063, 6.944, 0.957, 6.722, 0.945, 6.944, 1.1]}, {"time": 7.1667, "x": 0.957, "y": 1.1, "curve": [7.389, 0.957, 7.611, 1.063, 7.389, 1.1, 7.611, 0.945]}, {"time": 7.8333, "x": 1.063, "y": 0.945, "curve": [8.056, 1.063, 8.278, 0.957, 8.056, 0.945, 8.278, 1.1]}, {"time": 8.5, "x": 0.957, "y": 1.1, "curve": [8.722, 0.957, 8.944, 1.063, 8.722, 1.1, 8.944, 0.945]}, {"time": 9.1667, "x": 1.063, "y": 0.945, "curve": [9.389, 1.063, 9.611, 0.957, 9.389, 0.945, 9.611, 1.1]}, {"time": 9.8333, "x": 0.957, "y": 1.1, "curve": [10.056, 0.957, 10.278, 1.063, 10.056, 1.1, 10.278, 0.945]}, {"time": 10.5, "x": 1.063, "y": 0.945, "curve": [10.722, 1.063, 10.944, 0.957, 10.722, 0.945, 10.944, 1.1]}, {"time": 11.1667, "x": 0.957, "y": 1.1, "curve": [11.389, 0.957, 11.611, 1.063, 11.389, 1.1, 11.611, 0.945]}, {"time": 11.8333, "x": 1.063, "y": 0.945, "curve": [12.056, 1.063, 12.278, 0.957, 12.056, 0.945, 12.278, 1.1]}, {"time": 12.5, "x": 0.957, "y": 1.1, "curve": [12.667, 0.957, 12.833, 1.016, 12.667, 1.1, 12.833, 1.014]}, {"time": 13, "x": 1.046, "y": 0.97}]}, "tun_L2": {"translate": [{"x": 0.72, "y": -11.74, "curve": [0.114, 0.67, 0.224, 0.64, 0.114, -16.29, 0.224, -19.69]}, {"time": 0.3333, "x": 0.64, "y": -19.69, "curve": [0.778, 0.64, 1.222, 1.15, 0.778, -19.69, 1.222, 29.99]}, {"time": 1.6667, "x": 1.15, "y": 29.99, "curve": [2.111, 1.15, 2.556, 0.64, 2.111, 29.99, 2.556, -19.69]}, {"time": 3, "x": 0.64, "y": -19.69, "curve": [3.444, 0.64, 3.889, 1.15, 3.444, -19.69, 3.889, 29.99]}, {"time": 4.3333, "x": 1.15, "y": 29.99, "curve": [4.778, 1.15, 5.222, 0.64, 4.778, 29.99, 5.222, -19.69]}, {"time": 5.6667, "x": 0.64, "y": -19.69, "curve": [6, 0.64, 6.333, 1.15, 6, -19.69, 6.333, 29.99]}, {"time": 6.6667, "x": 1.15, "y": 29.99, "curve": [7.111, 1.15, 7.556, 0.64, 7.111, 29.99, 7.556, -19.69]}, {"time": 8, "x": 0.64, "y": -19.69, "curve": [8.444, 0.64, 8.889, 1.15, 8.444, -19.69, 8.889, 29.99]}, {"time": 9.3333, "x": 1.15, "y": 29.99, "curve": [9.778, 1.15, 10.222, 0.64, 9.778, 29.99, 10.222, -19.69]}, {"time": 10.6667, "x": 0.64, "y": -19.69, "curve": [11.111, 0.64, 11.556, 1.15, 11.111, -19.69, 11.556, 29.99]}, {"time": 12, "x": 1.15, "y": 29.99, "curve": [12.335, 1.15, 12.67, 0.86, 12.335, 29.99, 12.67, 2.17]}, {"time": 13, "x": 0.72, "y": -11.74}], "scale": [{"x": 1.01, "y": 1.023, "curve": [0.111, 1.036, 0.222, 1.063, 0.111, 0.984, 0.222, 0.945]}, {"time": 0.3333, "x": 1.063, "y": 0.945, "curve": [0.556, 1.063, 0.778, 0.957, 0.556, 0.945, 0.778, 1.1]}, {"time": 1, "x": 0.957, "y": 1.1, "curve": [1.222, 0.957, 1.444, 1.063, 1.222, 1.1, 1.444, 0.945]}, {"time": 1.6667, "x": 1.063, "y": 0.945, "curve": [1.889, 1.063, 2.111, 0.957, 1.889, 0.945, 2.111, 1.1]}, {"time": 2.3333, "x": 0.957, "y": 1.1, "curve": [2.556, 0.957, 2.778, 1.063, 2.556, 1.1, 2.778, 0.945]}, {"time": 3, "x": 1.063, "y": 0.945, "curve": [3.222, 1.063, 3.444, 0.957, 3.222, 0.945, 3.444, 1.1]}, {"time": 3.6667, "x": 0.957, "y": 1.1, "curve": [3.889, 0.957, 4.111, 1.063, 3.889, 1.1, 4.111, 0.945]}, {"time": 4.3333, "x": 1.063, "y": 0.945, "curve": [4.556, 1.063, 4.778, 0.957, 4.556, 0.945, 4.778, 1.1]}, {"time": 5, "x": 0.957, "y": 1.1, "curve": [5.222, 0.957, 5.444, 1.063, 5.222, 1.1, 5.444, 0.945]}, {"time": 5.6667, "x": 1.063, "y": 0.945, "curve": [5.833, 1.063, 6, 0.957, 5.833, 0.945, 6, 1.1]}, {"time": 6.1667, "x": 0.957, "y": 1.1, "curve": [6.333, 0.957, 6.5, 1.063, 6.333, 1.1, 6.5, 0.945]}, {"time": 6.6667, "x": 1.063, "y": 0.945, "curve": [6.889, 1.063, 7.111, 0.957, 6.889, 0.945, 7.111, 1.1]}, {"time": 7.3333, "x": 0.957, "y": 1.1, "curve": [7.556, 0.957, 7.778, 1.063, 7.556, 1.1, 7.778, 0.945]}, {"time": 8, "x": 1.063, "y": 0.945, "curve": [8.222, 1.063, 8.444, 0.957, 8.222, 0.945, 8.444, 1.1]}, {"time": 8.6667, "x": 0.957, "y": 1.1, "curve": [8.889, 0.957, 9.111, 1.063, 8.889, 1.1, 9.111, 0.945]}, {"time": 9.3333, "x": 1.063, "y": 0.945, "curve": [9.556, 1.063, 9.778, 0.957, 9.556, 0.945, 9.778, 1.1]}, {"time": 10, "x": 0.957, "y": 1.1, "curve": [10.222, 0.957, 10.444, 1.063, 10.222, 1.1, 10.444, 0.945]}, {"time": 10.6667, "x": 1.063, "y": 0.945, "curve": [10.889, 1.063, 11.111, 0.957, 10.889, 0.945, 11.111, 1.1]}, {"time": 11.3333, "x": 0.957, "y": 1.1, "curve": [11.556, 0.957, 11.778, 1.063, 11.556, 1.1, 11.778, 0.945]}, {"time": 12, "x": 1.063, "y": 0.945, "curve": [12.222, 1.063, 12.444, 0.957, 12.222, 0.945, 12.444, 1.1]}, {"time": 12.6667, "x": 0.957, "y": 1.1, "curve": [12.778, 0.957, 12.889, 0.983, 12.778, 1.1, 12.889, 1.061]}, {"time": 13, "x": 1.01, "y": 1.023}]}, "tun_L3": {"translate": [{"x": 0.84, "y": -8.11, "curve": [0.168, 0.81, 0.334, 0.77, 0.168, -16.78, 0.334, -23.9]}, {"time": 0.5, "x": 0.77, "y": -23.9, "curve": [0.944, 0.77, 1.389, 0.99, 0.944, -23.9, 1.389, 25.77]}, {"time": 1.8333, "x": 0.99, "y": 25.77, "curve": [2.278, 0.99, 2.722, 0.77, 2.278, 25.77, 2.722, -23.9]}, {"time": 3.1667, "x": 0.77, "y": -23.9, "curve": [3.611, 0.77, 4.056, 0.99, 3.611, -23.9, 4.056, 25.77]}, {"time": 4.5, "x": 0.99, "y": 25.77, "curve": [4.944, 0.99, 5.389, 0.77, 4.944, 25.77, 5.389, -23.9]}, {"time": 5.8333, "x": 0.77, "y": -23.9, "curve": [6.167, 0.77, 6.5, 0.99, 6.167, -23.9, 6.5, 25.77]}, {"time": 6.8333, "x": 0.99, "y": 25.77, "curve": [7.278, 0.99, 7.722, 0.77, 7.278, 25.77, 7.722, -23.9]}, {"time": 8.1667, "x": 0.77, "y": -23.9, "curve": [8.611, 0.77, 9.056, 0.99, 8.611, -23.9, 9.056, 25.77]}, {"time": 9.5, "x": 0.99, "y": 25.77, "curve": [9.944, 0.99, 10.389, 0.77, 9.944, 25.77, 10.389, -23.9]}, {"time": 10.8333, "x": 0.77, "y": -23.9, "curve": [11.278, 0.77, 11.722, 0.99, 11.278, -23.9, 11.722, 25.77]}, {"time": 12.1667, "x": 0.99, "y": 25.77, "curve": [12.445, 0.99, 12.724, 0.91, 12.445, 25.77, 12.724, 6.46]}, {"time": 13, "x": 0.84, "y": -8.11}], "scale": [{"x": 0.974, "y": 1.075, "curve": [0.167, 1.003, 0.333, 1.063, 0.167, 1.032, 0.333, 0.945]}, {"time": 0.5, "x": 1.063, "y": 0.945, "curve": [0.722, 1.063, 0.944, 0.957, 0.722, 0.945, 0.944, 1.1]}, {"time": 1.1667, "x": 0.957, "y": 1.1, "curve": [1.389, 0.957, 1.611, 1.063, 1.389, 1.1, 1.611, 0.945]}, {"time": 1.8333, "x": 1.063, "y": 0.945, "curve": [2.056, 1.063, 2.278, 0.957, 2.056, 0.945, 2.278, 1.1]}, {"time": 2.5, "x": 0.957, "y": 1.1, "curve": [2.722, 0.957, 2.944, 1.063, 2.722, 1.1, 2.944, 0.945]}, {"time": 3.1667, "x": 1.063, "y": 0.945, "curve": [3.389, 1.063, 3.611, 0.957, 3.389, 0.945, 3.611, 1.1]}, {"time": 3.8333, "x": 0.957, "y": 1.1, "curve": [4.056, 0.957, 4.278, 1.063, 4.056, 1.1, 4.278, 0.945]}, {"time": 4.5, "x": 1.063, "y": 0.945, "curve": [4.722, 1.063, 4.944, 0.957, 4.722, 0.945, 4.944, 1.1]}, {"time": 5.1667, "x": 0.957, "y": 1.1, "curve": [5.389, 0.957, 5.611, 1.063, 5.389, 1.1, 5.611, 0.945]}, {"time": 5.8333, "x": 1.063, "y": 0.945, "curve": [6, 1.063, 6.167, 0.957, 6, 0.945, 6.167, 1.1]}, {"time": 6.3333, "x": 0.957, "y": 1.1, "curve": [6.5, 0.957, 6.667, 1.063, 6.5, 1.1, 6.667, 0.945]}, {"time": 6.8333, "x": 1.063, "y": 0.945, "curve": [7.056, 1.063, 7.278, 0.957, 7.056, 0.945, 7.278, 1.1]}, {"time": 7.5, "x": 0.957, "y": 1.1, "curve": [7.722, 0.957, 7.944, 1.063, 7.722, 1.1, 7.944, 0.945]}, {"time": 8.1667, "x": 1.063, "y": 0.945, "curve": [8.389, 1.063, 8.611, 0.957, 8.389, 0.945, 8.611, 1.1]}, {"time": 8.8333, "x": 0.957, "y": 1.1, "curve": [9.056, 0.957, 9.278, 1.063, 9.056, 1.1, 9.278, 0.945]}, {"time": 9.5, "x": 1.063, "y": 0.945, "curve": [9.722, 1.063, 9.944, 0.957, 9.722, 0.945, 9.944, 1.1]}, {"time": 10.1667, "x": 0.957, "y": 1.1, "curve": [10.389, 0.957, 10.611, 1.063, 10.389, 1.1, 10.611, 0.945]}, {"time": 10.8333, "x": 1.063, "y": 0.945, "curve": [11.056, 1.063, 11.278, 0.957, 11.056, 0.945, 11.278, 1.1]}, {"time": 11.5, "x": 0.957, "y": 1.1, "curve": [11.722, 0.957, 11.944, 1.063, 11.722, 1.1, 11.944, 0.945]}, {"time": 12.1667, "x": 1.063, "y": 0.945, "curve": [12.389, 1.063, 12.611, 0.957, 12.389, 0.945, 12.611, 1.1]}, {"time": 12.8333, "x": 0.957, "y": 1.1, "curve": [12.889, 0.957, 12.944, 0.964, 12.889, 1.1, 12.944, 1.09]}, {"time": 13, "x": 0.974, "y": 1.075}]}, "sh_R": {"translate": [{"x": -4.28, "y": 1.75, "curve": [0.114, -5.23, 0.224, -5.94, 0.114, 2.1, 0.224, 2.36]}, {"time": 0.3333, "x": -5.94, "y": 2.36, "curve": [0.778, -5.94, 1.222, 4.44, 0.778, 2.36, 1.222, -1.43]}, {"time": 1.6667, "x": 4.44, "y": -1.43, "curve": [2.111, 4.44, 2.556, -5.94, 2.111, -1.43, 2.556, 2.36]}, {"time": 3, "x": -5.94, "y": 2.36, "curve": [3.444, -5.94, 3.889, 4.44, 3.444, 2.36, 3.889, -1.43]}, {"time": 4.3333, "x": 4.44, "y": -1.43, "curve": [4.778, 4.44, 5.222, -5.94, 4.778, -1.43, 5.222, 2.36]}, {"time": 5.6667, "x": -5.94, "y": 2.36, "curve": [6, -5.94, 6.333, 4.44, 6, 2.36, 6.333, -1.43]}, {"time": 6.6667, "x": 4.44, "y": -1.43, "curve": [7.111, 4.44, 7.556, -0.75, 7.111, -1.43, 7.556, 0.46]}, {"time": 8, "x": -0.75, "y": 0.46, "curve": [8.444, -0.75, 8.889, 4.44, 8.444, 0.46, 8.889, -1.43]}, {"time": 9.3333, "x": 4.44, "y": -1.43, "curve": [9.778, 4.44, 10.222, -5.94, 9.778, -1.43, 10.222, 2.36]}, {"time": 10.6667, "x": -5.94, "y": 2.36, "curve": [11.111, -5.94, 11.556, 4.44, 11.111, 2.36, 11.556, -1.43]}, {"time": 12, "x": 4.44, "y": -1.43, "curve": [12.335, 4.44, 12.67, -1.37, 12.335, -1.43, 12.67, 0.69]}, {"time": 13, "x": -4.28, "y": 1.75}]}, "arm_R": {"rotate": [{"curve": "stepped"}, {"time": 6.3333, "curve": [6.778, 0, 7.222, -0.01]}, {"time": 7.6667, "value": -0.01, "curve": [8.111, -0.01, 8.556, 0]}, {"time": 9}]}, "arm_R2": {"rotate": [{"curve": "stepped"}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 0.16]}, {"time": 7.6667, "value": 0.16, "curve": [8.111, 0.16, 8.556, 0]}, {"time": 9}]}, "arm_R3": {"rotate": [{"time": 5.3333, "curve": [5.667, 0, 6, -3.19]}, {"time": 6.3333, "value": -3.19, "curve": [6.778, -3.19, 7.222, -1.6]}, {"time": 7.6667, "value": -1.6, "curve": [8.111, -1.6, 8.556, -3.19]}, {"time": 9, "value": -3.19, "curve": [9.444, -3.19, 9.889, 0]}, {"time": 10.3333}]}, "arm_R4": {"rotate": [{"time": 5.3333, "curve": [5.667, 0, 6, -3.19]}, {"time": 6.3333, "value": -3.19, "curve": [6.778, -3.19, 7.222, -1.6]}, {"time": 7.6667, "value": -1.6, "curve": [8.111, -1.6, 8.556, -3.19]}, {"time": 9, "value": -3.19, "curve": [9.444, -3.19, 9.889, 0]}, {"time": 10.3333}]}, "arm_R5": {"rotate": [{"time": 5.3333, "curve": [5.667, 0, 6, -3.19]}, {"time": 6.3333, "value": -3.19, "curve": [6.778, -3.19, 7.222, -1.6]}, {"time": 7.6667, "value": -1.6, "curve": [8.111, -1.6, 8.556, -3.19]}, {"time": 9, "value": -3.19, "curve": [9.444, -3.19, 9.889, 0]}, {"time": 10.3333}]}, "arm_R6": {"rotate": [{"time": 5.3333, "curve": [5.667, 0, 6, -7.43]}, {"time": 6.3333, "value": -7.43, "curve": [6.778, -7.43, 7.222, -3.72]}, {"time": 7.6667, "value": -3.72, "curve": [8.111, -3.72, 8.556, -7.43]}, {"time": 9, "value": -7.43, "curve": [9.444, -7.43, 9.889, 0]}, {"time": 10.3333}]}, "sh_L": {"translate": [{"x": -2.52, "y": 1.09, "curve": [0.114, -3.73, 0.224, -4.63, 0.114, 1.51, 0.224, 1.82]}, {"time": 0.3333, "x": -4.63, "y": 1.82, "curve": [0.778, -4.63, 1.222, 8.5, 0.778, 1.82, 1.222, -2.77]}, {"time": 1.6667, "x": 8.5, "y": -2.77, "curve": [2.111, 8.51, 2.556, -4.62, 2.111, -2.77, 2.556, 1.82]}, {"time": 3, "x": -4.63, "y": 1.82, "curve": [3.444, -4.63, 3.889, 8.5, 3.444, 1.82, 3.889, -2.77]}, {"time": 4.3333, "x": 8.5, "y": -2.77, "curve": [4.778, 8.51, 5.222, -4.62, 4.778, -2.77, 5.222, 1.82]}, {"time": 5.6667, "x": -4.63, "y": 1.82, "curve": [6, -4.63, 6.333, 8.5, 6, 1.82, 6.333, -2.77]}, {"time": 6.6667, "x": 8.5, "y": -2.77, "curve": [7.111, 8.51, 7.556, -4.62, 7.111, -2.77, 7.556, 1.82]}, {"time": 8, "x": -4.63, "y": 1.82, "curve": [8.444, -4.63, 8.889, 8.5, 8.444, 1.82, 8.889, -2.77]}, {"time": 9.3333, "x": 8.5, "y": -2.77, "curve": [9.778, 8.51, 10.222, -4.62, 9.778, -2.77, 10.222, 1.82]}, {"time": 10.6667, "x": -4.63, "y": 1.82, "curve": [11.111, -4.63, 11.556, 8.5, 11.111, 1.82, 11.556, -2.77]}, {"time": 12, "x": 8.5, "y": -2.77, "curve": [12.335, 8.5, 12.67, 1.15, 12.335, -2.77, 12.67, -0.2]}, {"time": 13, "x": -2.52, "y": 1.09}]}, "arm_L": {"rotate": [{"time": 6.3333, "curve": [6.778, 0, 7.222, 0.43]}, {"time": 7.6667, "value": 0.43, "curve": [8.111, 0.43, 8.556, 0]}, {"time": 9}]}, "arm_L2": {"rotate": [{"curve": "stepped"}, {"time": 6.3333, "curve": [6.778, 0, 7.222, -0.42]}, {"time": 7.6667, "value": -0.42, "curve": [8.111, -0.42, 8.556, 0]}, {"time": 9}]}, "hair_B": {"rotate": [{"value": 1.5, "curve": [0.168, 3.01, 0.334, 4.24]}, {"time": 0.5, "value": 4.24, "curve": [0.944, 4.24, 1.389, -4.38]}, {"time": 1.8333, "value": -4.38, "curve": [2.278, -4.38, 2.722, 4.24]}, {"time": 3.1667, "value": 4.24, "curve": [3.611, 4.24, 4.056, -4.38]}, {"time": 4.5, "value": -4.38, "curve": [4.944, -4.38, 5.389, 4.24]}, {"time": 5.8333, "value": 4.24, "curve": [6.167, 4.24, 6.5, -4.38]}, {"time": 6.8333, "value": -4.38, "curve": [7.278, -4.38, 7.722, -0.07]}, {"time": 8.1667, "value": -0.07, "curve": [8.611, -0.07, 9.056, -4.38]}, {"time": 9.5, "value": -4.38, "curve": [9.944, -4.38, 10.389, 4.24]}, {"time": 10.8333, "value": 4.24, "curve": [11.278, 4.24, 11.722, -4.38]}, {"time": 12.1667, "value": -4.38, "curve": [12.445, -4.38, 12.724, -1.03]}, {"time": 13, "value": 1.5}]}, "RU": {"translate": [{"x": -9.92, "y": 5.93, "curve": [0.168, -22.26, 0.334, -32.38, 0.168, 10.61, 0.334, 14.45]}, {"time": 0.5, "x": -32.38, "y": 14.45, "curve": [0.944, -32.38, 1.389, 38.25, 0.944, 14.45, 1.389, -12.34]}, {"time": 1.8333, "x": 38.25, "y": -12.34, "curve": [2.278, 38.25, 2.722, -32.38, 2.278, -12.34, 2.722, 14.45]}, {"time": 3.1667, "x": -32.38, "y": 14.45, "curve": [3.611, -32.38, 4.056, 38.25, 3.611, 14.45, 4.056, -12.34]}, {"time": 4.5, "x": 38.25, "y": -12.34, "curve": [4.944, 38.25, 5.389, -32.38, 4.944, -12.34, 5.389, 14.45]}, {"time": 5.8333, "x": -32.38, "y": 14.45, "curve": [6.167, -32.38, 6.5, 38.25, 6.167, 14.45, 6.5, -12.34]}, {"time": 6.8333, "x": 38.25, "y": -12.34, "curve": [7.278, 38.25, 7.722, 2.94, 7.278, -12.34, 7.722, 1.05]}, {"time": 8.1667, "x": 2.94, "y": 1.05, "curve": [8.611, 2.94, 9.056, 38.25, 8.611, 1.05, 9.056, -12.34]}, {"time": 9.5, "x": 38.25, "y": -12.34, "curve": [9.944, 38.25, 10.389, -32.38, 9.944, -12.34, 10.389, 14.45]}, {"time": 10.8333, "x": -32.38, "y": 14.45, "curve": [11.278, -32.38, 11.722, 38.25, 11.278, 14.45, 11.722, -12.34]}, {"time": 12.1667, "x": 38.25, "y": -12.34, "curve": [12.445, 38.25, 12.724, 10.79, 12.445, -12.34, 12.724, -1.93]}, {"time": 13, "x": -9.92, "y": 5.93}]}, "RU2": {"translate": [{"x": 1.6, "y": 5.08, "curve": [0.225, -13.5, 0.446, -28.81, 0.225, 8.24, 0.446, 11.44]}, {"time": 0.6667, "x": -28.81, "y": 11.44, "curve": [1.111, -28.81, 1.556, 32.02, 1.111, 11.44, 1.556, -1.28]}, {"time": 2, "x": 32.02, "y": -1.28, "curve": [2.444, 32.02, 2.889, -28.81, 2.444, -1.28, 2.889, 11.44]}, {"time": 3.3333, "x": -28.81, "y": 11.44, "curve": [3.778, -28.81, 4.222, 32.02, 3.778, 11.44, 4.222, -1.28]}, {"time": 4.6667, "x": 32.02, "y": -1.28, "curve": [5.111, 32.02, 5.556, -28.81, 5.111, -1.28, 5.556, 11.44]}, {"time": 6, "x": -28.81, "y": 11.44, "curve": [6.333, -28.81, 6.667, 32.02, 6.333, 11.44, 6.667, -1.28]}, {"time": 7, "x": 32.02, "y": -1.28, "curve": [7.444, 32.02, 7.889, 1.6, 7.444, -1.28, 7.889, 5.08]}, {"time": 8.3333, "x": 1.6, "y": 5.08, "curve": [8.778, 1.6, 9.222, 32.02, 8.778, 5.08, 9.222, -1.28]}, {"time": 9.6667, "x": 32.02, "y": -1.28, "curve": [10.111, 32.02, 10.556, -28.81, 10.111, -1.28, 10.556, 11.44]}, {"time": 11, "x": -28.81, "y": 11.44, "curve": [11.444, -28.81, 11.889, 32.02, 11.444, 11.44, 11.889, -1.28]}, {"time": 12.3333, "x": 32.02, "y": -1.28, "curve": [12.557, 32.02, 12.781, 16.91, 12.557, -1.28, 12.781, 1.88]}, {"time": 13, "x": 1.6, "y": 5.08}]}, "RU3": {"translate": [{"x": 2.69, "y": 1.81, "curve": [0.257, -11.77, 0.512, -28.81, 0.257, 6.23, 0.512, 11.44]}, {"time": 0.7667, "x": -28.81, "y": 11.44, "curve": [1.211, -28.81, 1.656, 22.74, 1.211, 11.44, 1.656, -4.32]}, {"time": 2.1, "x": 22.74, "y": -4.32, "curve": [2.544, 22.74, 2.989, -28.81, 2.544, -4.32, 2.989, 11.44]}, {"time": 3.4333, "x": -28.81, "y": 11.44, "curve": [3.878, -28.81, 4.322, 22.74, 3.878, 11.44, 4.322, -4.32]}, {"time": 4.7667, "x": 22.74, "y": -4.32, "curve": [5.211, 22.74, 5.656, -28.81, 5.211, -4.32, 5.656, 11.44]}, {"time": 6.1, "x": -28.81, "y": 11.44, "curve": [6.433, -28.81, 6.767, 22.74, 6.433, 11.44, 6.767, -4.32]}, {"time": 7.1, "x": 22.74, "y": -4.32, "curve": [7.544, 22.74, 7.989, -3.03, 7.544, -4.32, 7.989, 3.56]}, {"time": 8.4333, "x": -3.03, "y": 3.56, "curve": [8.878, -3.03, 9.322, 22.74, 8.878, 3.56, 9.322, -4.32]}, {"time": 9.7667, "x": 22.74, "y": -4.32, "curve": [10.211, 22.74, 10.656, -28.81, 10.211, -4.32, 10.656, 11.44]}, {"time": 11.1, "x": -28.81, "y": 11.44, "curve": [11.544, -28.81, 11.989, 22.74, 11.544, 11.44, 11.989, -4.32]}, {"time": 12.4333, "x": 22.74, "y": -4.32, "curve": [12.623, 22.74, 12.813, 13.45, 12.623, -4.32, 12.813, -1.48]}, {"time": 13, "x": 2.69, "y": 1.81}]}, "hair_L": {"rotate": [{"value": 1.76, "curve": [0.279, -0.4, 0.556, -3.27]}, {"time": 0.8333, "value": -3.27, "curve": [1.278, -3.27, 1.722, 4.1]}, {"time": 2.1667, "value": 4.1, "curve": [2.611, 4.1, 3.056, -3.27]}, {"time": 3.5, "value": -3.27, "curve": [3.944, -3.27, 4.389, 4.1]}, {"time": 4.8333, "value": 4.1, "curve": [5.278, 4.1, 5.722, -3.27]}, {"time": 6.1667, "value": -3.27, "curve": [6.5, -3.27, 6.833, 4.1]}, {"time": 7.1667, "value": 4.1, "curve": [7.611, 4.1, 8.056, 0.41]}, {"time": 8.5, "value": 0.41, "curve": [8.944, 0.41, 9.389, 4.1]}, {"time": 9.8333, "value": 4.1, "curve": [10.278, 4.1, 10.722, -3.27]}, {"time": 11.1667, "value": -3.27, "curve": [11.611, -3.27, 12.056, 4.1]}, {"time": 12.5, "value": 4.1, "curve": [12.667, 4.1, 12.835, 3.06]}, {"time": 13, "value": 1.76}]}, "RU4": {"translate": [{"x": 6.82, "y": 0.41, "curve": [0.292, -4.77, 0.579, -21.26, 0.292, 3.73, 0.579, 8.45]}, {"time": 0.8667, "x": -21.26, "y": 8.45, "curve": [1.311, -21.26, 1.756, 17.96, 1.311, 8.45, 1.756, -2.78]}, {"time": 2.2, "x": 17.96, "y": -2.78, "curve": [2.644, 17.96, 3.089, -21.26, 2.644, -2.78, 3.089, 8.45]}, {"time": 3.5333, "x": -21.26, "y": 8.45, "curve": [3.978, -21.26, 4.422, 17.96, 3.978, 8.45, 4.422, -2.78]}, {"time": 4.8667, "x": 17.96, "y": -2.78, "curve": [5.311, 17.96, 5.756, -21.26, 5.311, -2.78, 5.756, 8.45]}, {"time": 6.2, "x": -21.26, "y": 8.45, "curve": [6.533, -21.26, 6.867, 17.96, 6.533, 8.45, 6.867, -2.78]}, {"time": 7.2, "x": 17.96, "y": -2.78, "curve": [7.644, 17.96, 8.089, -1.65, 7.644, -2.78, 8.089, 2.83]}, {"time": 8.5333, "x": -1.65, "y": 2.83, "curve": [8.978, -1.65, 9.422, 17.96, 8.978, 2.83, 9.422, -2.78]}, {"time": 9.8667, "x": 17.96, "y": -2.78, "curve": [10.311, 17.96, 10.756, -21.26, 10.311, -2.78, 10.756, 8.45]}, {"time": 11.2, "x": -21.26, "y": 8.45, "curve": [11.644, -21.26, 12.089, 17.96, 11.644, 8.45, 12.089, -2.78]}, {"time": 12.5333, "x": 17.96, "y": -2.78, "curve": [12.69, 17.96, 12.847, 13.15, 12.69, -2.78, 12.847, -1.4]}, {"time": 13, "x": 6.82, "y": 0.41}]}, "headround3": {"translate": [{"x": 45.2, "curve": [0.279, -40.12, 0.556, -153.6, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -153.6, "curve": [1.278, -153.6, 1.722, 137.9, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 137.9, "curve": [2.611, 137.9, 3.056, -153.6, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -153.6, "curve": [3.944, -153.6, 4.389, 137.9, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 137.9, "curve": [5.278, 137.9, 5.722, -153.6, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -153.6, "curve": [6.5, -153.6, 6.833, 137.9, 6.5, 0, 6.833, 0]}, {"time": 7.1667, "x": 137.9, "curve": [7.611, 137.9, 8.056, -7.85, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": -7.85, "curve": [8.944, -7.85, 9.389, 137.9, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 137.9, "curve": [10.278, 137.9, 10.722, -153.6, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -153.6, "curve": [11.611, -153.6, 12.056, 137.9, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 137.9, "curve": [12.667, 137.9, 12.835, 96.76, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 45.2}]}, "headround": {"translate": [{"y": 180.93, "curve": [0.336, 0, 0.668, 0, 0.336, 26.21, 0.668, -279.18]}, {"time": 1, "y": -279.18, "curve": [1.444, 0, 1.889, 0, 1.444, -279.18, 1.889, 268.57]}, {"time": 2.3333, "y": 268.57, "curve": [2.778, 0, 3.222, 0, 2.778, 268.57, 3.222, -279.18]}, {"time": 3.6667, "y": -279.18, "curve": [4.111, 0, 4.556, 0, 4.111, -279.18, 4.556, 268.57]}, {"time": 5, "y": 268.57, "curve": [5.444, 0, 5.889, 0, 5.444, 268.57, 5.889, -279.18]}, {"time": 6.3333, "y": -279.18, "curve": [6.667, 0, 7, 0, 6.667, -279.18, 7, 268.57]}, {"time": 7.3333, "y": 268.57, "curve": [7.778, 0, 8.222, 0, 7.778, 268.57, 8.222, -5.3]}, {"time": 8.6667, "y": -5.3, "curve": [9.111, 0, 9.556, 0, 9.111, -5.3, 9.556, 268.57]}, {"time": 10, "y": 268.57, "curve": [10.444, 0, 10.889, 0, 10.444, 268.57, 10.889, -279.18]}, {"time": 11.3333, "y": -279.18, "curve": [11.778, 0, 12.222, 0, 11.778, -279.18, 12.222, 268.57]}, {"time": 12.6667, "y": 268.57, "curve": [12.779, 0, 12.892, 0, 12.779, 268.57, 12.892, 233.43]}, {"time": 13, "y": 180.93}]}, "bodyround": {"translate": [{"x": -46.31, "y": -103.59, "curve": [0.168, -76.13, 0.334, -100.6, 0.168, -185.8, 0.334, -253.24]}, {"time": 0.5, "x": -100.6, "y": -253.24, "curve": [0.944, -100.6, 1.389, 70.13, 0.944, -253.24, 1.389, 217.36]}, {"time": 1.8333, "x": 70.13, "y": 217.36, "curve": [2.278, 70.13, 2.722, -100.6, 2.278, 217.36, 2.722, -253.24]}, {"time": 3.1667, "x": -100.6, "y": -253.24, "curve": [3.611, -100.6, 4.056, 70.13, 3.611, -253.24, 4.056, 217.36]}, {"time": 4.5, "x": 70.13, "y": 217.36, "curve": [4.944, 70.13, 5.389, -100.6, 4.944, 217.36, 5.389, -253.24]}, {"time": 5.8333, "x": -100.6, "y": -253.24, "curve": [6.167, -100.6, 6.5, 70.13, 6.167, -253.24, 6.5, 217.36]}, {"time": 6.8333, "x": 70.13, "y": 217.36, "curve": [7.278, 70.13, 7.722, -15.23, 7.278, 217.36, 7.722, -17.94]}, {"time": 8.1667, "x": -15.23, "y": -17.94, "curve": [8.611, -15.23, 9.056, 70.13, 8.611, -17.94, 9.056, 217.36]}, {"time": 9.5, "x": 70.13, "y": 217.36, "curve": [9.944, 70.13, 10.389, -100.6, 9.944, 217.36, 10.389, -253.24]}, {"time": 10.8333, "x": -100.6, "y": -253.24, "curve": [11.278, -100.6, 11.722, 70.13, 11.278, -253.24, 11.722, 217.36]}, {"time": 12.1667, "x": 70.13, "y": 217.36, "curve": [12.445, 70.13, 12.724, 3.76, 12.445, 217.36, 12.724, 34.4]}, {"time": 13, "x": -46.31, "y": -103.59}]}, "tunround": {"translate": [{"x": 249.25, "curve": [0.057, 264.15, 0.112, 275.5, 0.057, 0, 0.112, 0]}, {"time": 0.1667, "x": 275.5, "curve": [0.611, 275.5, 1.056, -283.03, 0.611, 0, 1.056, 0]}, {"time": 1.5, "x": -283.03, "curve": [1.944, -283.03, 2.389, 275.5, 1.944, 0, 2.389, 0]}, {"time": 2.8333, "x": 275.5, "curve": [3.278, 275.5, 3.722, -283.03, 3.278, 0, 3.722, 0]}, {"time": 4.1667, "x": -283.03, "curve": [4.611, -283.03, 5.056, 275.5, 4.611, 0, 5.056, 0]}, {"time": 5.5, "x": 275.5, "curve": [5.833, 275.5, 6.167, -283.03, 5.833, 0, 6.167, 0]}, {"time": 6.5, "x": -283.03, "curve": [6.944, -283.03, 7.389, -3.76, 6.944, 0, 7.389, 0]}, {"time": 7.8333, "x": -3.76, "curve": [8.278, -3.76, 8.722, -283.03, 8.278, 0, 8.722, 0]}, {"time": 9.1667, "x": -283.03, "curve": [9.611, -283.03, 10.056, 275.5, 9.611, 0, 10.056, 0]}, {"time": 10.5, "x": 275.5, "curve": [10.944, 275.5, 11.389, -283.03, 10.944, 0, 11.389, 0]}, {"time": 11.8333, "x": -283.03, "curve": [12.223, -283.03, 12.613, 143.37, 12.223, 0, 12.613, 0]}, {"time": 13, "x": 249.25}]}, "leg_R5": {"rotate": [{"value": 0.01, "curve": "stepped"}, {"time": 6.3333, "value": 0.01, "curve": [6.778, 0.01, 7.222, -0.19]}, {"time": 7.6667, "value": -0.19, "curve": [8.111, -0.19, 8.556, 0.01]}, {"time": 9, "value": 0.01}]}, "leg_R1": {"translate": [{"y": 11.78, "curve": [0.444, 0, 0.889, 0, 0.444, 11.78, 0.889, -8.54]}, {"time": 1.3333, "y": -8.54, "curve": [1.778, 0, 2.222, 0, 1.778, -8.54, 2.222, 11.78]}, {"time": 2.6667, "y": 11.78, "curve": [3.111, 0, 3.556, 0, 3.111, 11.78, 3.556, -8.54]}, {"time": 4, "y": -8.54, "curve": [4.444, 0, 4.889, 0, 4.444, -8.54, 4.889, 11.78]}, {"time": 5.3333, "y": 11.78, "curve": [5.667, 0, 6, 0, 5.667, 11.78, 6, -8.54]}, {"time": 6.3333, "y": -8.54, "curve": [6.778, 0, 7.222, 0, 6.778, -8.54, 7.222, 1.62]}, {"time": 7.6667, "y": 1.62, "curve": [8.111, 0, 8.556, 0, 8.111, 1.62, 8.556, -8.54]}, {"time": 9, "y": -8.54, "curve": [9.444, 0, 9.889, 0, 9.444, -8.54, 9.889, 11.78]}, {"time": 10.3333, "y": 11.78, "curve": [10.778, 0, 11.222, 0, 10.778, 11.78, 11.222, -8.54]}, {"time": 11.6667, "y": -8.54, "curve": [12.111, 0, 12.556, 0, 12.111, -8.54, 12.556, 11.78]}, {"time": 13, "y": 11.78}]}, "leg_L5": {"rotate": [{"value": -0.12, "curve": "stepped"}, {"time": 6.3333, "value": -0.12, "curve": [6.778, -0.12, 7.222, -0.34]}, {"time": 7.6667, "value": -0.34, "curve": [8.111, -0.34, 8.556, -0.12]}, {"time": 9, "value": -0.12}]}, "leg_L1": {"translate": [{"y": 14.85, "curve": [0.444, 0, 0.889, 0, 0.444, 14.85, 0.889, -15.05]}, {"time": 1.3333, "y": -15.05, "curve": [1.778, 0, 2.222, 0, 1.778, -15.05, 2.222, 14.85]}, {"time": 2.6667, "y": 14.85, "curve": [3.111, 0, 3.556, 0, 3.111, 14.85, 3.556, -15.05]}, {"time": 4, "y": -15.05, "curve": [4.444, 0, 4.889, 0, 4.444, -15.05, 4.889, 14.85]}, {"time": 5.3333, "y": 14.85, "curve": [5.667, 0, 6, 0, 5.667, 14.85, 6, -15.05]}, {"time": 6.3333, "y": -15.05, "curve": [6.778, 0, 7.222, 0, 6.778, -15.05, 7.222, -0.1]}, {"time": 7.6667, "y": -0.1, "curve": [8.111, 0, 8.556, 0, 8.111, -0.1, 8.556, -15.05]}, {"time": 9, "y": -15.05, "curve": [9.444, 0, 9.889, 0, 9.444, -15.05, 9.889, 14.85]}, {"time": 10.3333, "y": 14.85, "curve": [10.778, 0, 11.222, 0, 10.778, 14.85, 11.222, -15.05]}, {"time": 11.6667, "y": -15.05, "curve": [12.111, 0, 12.556, 0, 12.111, -15.05, 12.556, 14.85]}, {"time": 13, "y": 14.85}]}, "sh_R2": {"rotate": [{"value": 0.11, "curve": "stepped"}, {"time": 6.3333, "value": 0.11, "curve": [6.778, 0.11, 7.222, 0.12]}, {"time": 7.6667, "value": 0.12, "curve": [8.111, 0.12, 8.556, 0.11]}, {"time": 9, "value": 0.11}]}, "sh_R3": {"rotate": [{"value": -0.07, "curve": "stepped"}, {"time": 6.3333, "value": -0.07, "curve": [6.778, -0.07, 7.222, 0.08]}, {"time": 7.6667, "value": 0.08, "curve": [8.111, 0.08, 8.556, -0.07]}, {"time": 9, "value": -0.07}]}, "sh_L2": {"rotate": [{"value": 0.06, "curve": "stepped"}, {"time": 6.3333, "value": 0.06, "curve": [6.778, 0.06, 7.222, 0.55]}, {"time": 7.6667, "value": 0.55, "curve": [8.111, 0.55, 8.556, 0.06]}, {"time": 9, "value": 0.06}]}, "sh_L3": {"rotate": [{"value": 0.04, "curve": "stepped"}, {"time": 6.3333, "value": 0.04, "curve": [6.778, 0.04, 7.222, -0.49]}, {"time": 7.6667, "value": -0.49, "curve": [8.111, -0.49, 8.556, 0.04]}, {"time": 9, "value": 0.04}]}, "eye_R": {"translate": [{"time": 1.6667, "curve": [1.7, 0, 1.733, -0.6, 1.7, 0, 1.733, -1.63]}, {"time": 1.7667, "x": -0.6, "y": -1.63, "curve": [2.144, -0.6, 2.522, -0.6, 2.144, -1.63, 2.522, -1.62]}, {"time": 2.9, "x": -0.6, "y": -1.63, "curve": [2.933, -0.6, 2.967, -1.56, 2.933, -1.63, 2.967, 0.27]}, {"time": 3, "x": -1.56, "y": 0.27, "curve": [3.133, -1.56, 3.267, -1.56, 3.133, 0.27, 3.267, 0.27]}, {"time": 3.4, "x": -1.56, "y": 0.27, "curve": [3.433, -1.56, 3.467, 0, 3.433, 0.27, 3.467, 0]}, {"time": 3.5, "curve": [4.5, 0, 5.5, -0.01, 4.5, 0, 5.5, 0.02]}, {"time": 6.5, "curve": [6.533, 0, 6.567, -0.7, 6.533, 0, 6.567, 3.08]}, {"time": 6.6, "x": -0.7, "y": 3.08, "curve": [6.922, -0.7, 7.244, -0.7, 6.922, 3.08, 7.244, 3.08]}, {"time": 7.5667, "x": -0.7, "y": 3.08, "curve": [7.6, -0.7, 7.633, -2.03, 7.6, 3.08, 7.633, 3.66]}, {"time": 7.6667, "x": -2.03, "y": 3.66, "curve": [8, -2.03, 8.333, -2.03, 8, 3.66, 8.333, 3.66]}, {"time": 8.6667, "x": -2.03, "y": 3.66, "curve": [8.7, -2.03, 8.733, -1.29, 8.7, 3.67, 8.733, 2.14]}, {"time": 8.7667, "x": -1.29, "y": 2.14, "curve": [8.889, -1.29, 9.011, -1.29, 8.889, 2.14, 9.011, 2.14]}, {"time": 9.1333, "x": -1.29, "y": 2.14, "curve": [9.167, -1.29, 9.2, -0.7, 9.167, 2.14, 9.2, 3.08]}, {"time": 9.2333, "x": -0.7, "y": 3.08, "curve": "stepped"}, {"time": 10, "x": -0.7, "y": 3.08, "curve": [10.033, -0.7, 10.067, 0, 10.033, 3.08, 10.067, 0]}, {"time": 10.1}]}, "eye_L": {"translate": [{"time": 1.6667, "curve": [1.7, 0, 1.733, -0.6, 1.7, 0, 1.733, -1.63]}, {"time": 1.7667, "x": -0.6, "y": -1.63, "curve": [2.144, -0.6, 2.522, -0.6, 2.144, -1.63, 2.522, -1.62]}, {"time": 2.9, "x": -0.6, "y": -1.63, "curve": [2.933, -0.6, 2.967, -1.56, 2.933, -1.63, 2.967, 0.27]}, {"time": 3, "x": -1.56, "y": 0.27, "curve": [3.133, -1.56, 3.267, -1.56, 3.133, 0.27, 3.267, 0.27]}, {"time": 3.4, "x": -1.56, "y": 0.27, "curve": [3.433, -1.56, 3.467, 0, 3.433, 0.27, 3.467, 0]}, {"time": 3.5, "curve": [4.5, 0, 5.5, -0.01, 4.5, 0, 5.5, 0.02]}, {"time": 6.5, "curve": [6.533, 0, 6.567, -0.7, 6.533, 0, 6.567, 3.08]}, {"time": 6.6, "x": -0.7, "y": 3.08, "curve": [6.922, -0.7, 7.244, -0.7, 6.922, 3.08, 7.244, 3.08]}, {"time": 7.5667, "x": -0.7, "y": 3.08, "curve": [7.6, -0.7, 7.633, -2.03, 7.6, 3.08, 7.633, 3.66]}, {"time": 7.6667, "x": -2.03, "y": 3.66, "curve": [8, -2.03, 8.333, -2.03, 8, 3.66, 8.333, 3.66]}, {"time": 8.6667, "x": -2.03, "y": 3.66, "curve": [8.7, -2.03, 8.733, -1.29, 8.7, 3.67, 8.733, 2.14]}, {"time": 8.7667, "x": -1.29, "y": 2.14, "curve": [8.889, -1.29, 9.011, -1.29, 8.889, 2.14, 9.011, 2.14]}, {"time": 9.1333, "x": -1.29, "y": 2.14, "curve": [9.167, -1.29, 9.2, -0.7, 9.167, 2.14, 9.2, 3.08]}, {"time": 9.2333, "x": -0.7, "y": 3.08, "curve": "stepped"}, {"time": 10, "x": -0.7, "y": 3.08, "curve": [10.033, -0.7, 10.067, 0, 10.033, 3.08, 10.067, 0]}, {"time": 10.1}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "vertices": [-10.68372, 0.08691, -10.68469, 0.08749, -11.48767, -0.39697, -11.49011, -0.39658, -7.95374, -0.06415, -7.95532, -0.06277, -8.48486, -0.14056, -8.48633, -0.13901, -5.66577, 0.26208, -5.66675, 0.26318, -4.31921, 1.12842, -4.32043, 1.1293, -3.84558, 0.79449, -3.84595, 0.79572, -2.60266, 0.37097, -2.60303, 0.37167, -1.17273, 0.01971, -1.17273, 0.02008, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.89734, -0.13208, -0.89758, -0.13208, -2.78577, -0.04456, -2.78625, -0.04431, -7.01013, -0.60248, -7.0116, -0.60242, -0.98474, 0.09265, -0.98486, 0.09283, -4.72119, 0.49976, -4.72168, 0.49982, -7.79968, 0.21222, -7.80103, 0.21249, -10.80847, 0.29761, -10.80981, 0.29788, -11.37134, 0.00928, -11.37268, 0.00912, -9.13879, -0.06152, -9.13989, -0.06168, -4.34473, -0.34076, -4.3457, -0.34042, -0.94116, -0.01984, -0.94153, -0.01974], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6.3333, "curve": [6.389, 0, 6.444, 1]}, {"time": 6.5, "vertices": [-10.68372, 0.08691, -10.68469, 0.08749, -11.48767, -0.39697, -11.49011, -0.39658, -7.95374, -0.06415, -7.95532, -0.06277, -8.48486, -0.14056, -8.48633, -0.13901, -5.66577, 0.26208, -5.66675, 0.26318, -4.31921, 1.12842, -4.32043, 1.1293, -3.84558, 0.79449, -3.84595, 0.79572, -2.60266, 0.37097, -2.60303, 0.37167, -1.17273, 0.01971, -1.17273, 0.02008, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.89734, -0.13208, -0.89758, -0.13208, -2.78577, -0.04456, -2.78625, -0.04431, -7.01013, -0.60248, -7.0116, -0.60242, -0.98474, 0.09265, -0.98486, 0.09283, -4.72119, 0.49976, -4.72168, 0.49982, -7.79968, 0.21222, -7.80103, 0.21249, -10.80847, 0.29761, -10.80981, 0.29788, -11.37134, 0.00928, -11.37268, 0.00912, -9.13879, -0.06152, -9.13989, -0.06168, -4.34473, -0.34076, -4.3457, -0.34042, -0.94116, -0.01984, -0.94153, -0.01974], "curve": [6.556, 0, 6.611, 1]}, {"time": 6.6667, "curve": "stepped"}, {"time": 9.8333, "curve": [9.889, 0, 9.944, 1]}, {"time": 10, "vertices": [-10.68372, 0.08691, -10.68469, 0.08749, -11.48767, -0.39697, -11.49011, -0.39658, -7.95374, -0.06415, -7.95532, -0.06277, -8.48486, -0.14056, -8.48633, -0.13901, -5.66577, 0.26208, -5.66675, 0.26318, -4.31921, 1.12842, -4.32043, 1.1293, -3.84558, 0.79449, -3.84595, 0.79572, -2.60266, 0.37097, -2.60303, 0.37167, -1.17273, 0.01971, -1.17273, 0.02008, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.89734, -0.13208, -0.89758, -0.13208, -2.78577, -0.04456, -2.78625, -0.04431, -7.01013, -0.60248, -7.0116, -0.60242, -0.98474, 0.09265, -0.98486, 0.09283, -4.72119, 0.49976, -4.72168, 0.49982, -7.79968, 0.21222, -7.80103, 0.21249, -10.80847, 0.29761, -10.80981, 0.29788, -11.37134, 0.00928, -11.37268, 0.00912, -9.13879, -0.06152, -9.13989, -0.06168, -4.34473, -0.34076, -4.3457, -0.34042, -0.94116, -0.01984, -0.94153, -0.01974], "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667}]}}, "eye_RR": {"eye_RR": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "vertices": [-3.98743, 1.71967, -3.98743, 1.72012, -4.1416, 0.63599, -4.14417, 0.63638, -10.33936, 1.11517, -10.34375, 1.11539, -13.74316, 1.03607, -13.74951, 1.03687, -12.66663, 0.76013, -12.67151, 0.76102, -8.38525, 0.16235, -8.38916, 0.16333, -1.8197, -0.11224, -1.82092, -0.11182, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.8291, -0.03998, -0.8291, -0.03986, -0.8291, -0.03998, -0.8291, -0.03986, -2.54919, 0.47607, -2.54919, 0.47629, -0.98108, 0.12793, -0.98218, 0.12823, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.77979, 0.09613, -4.7832, 0.09698, -10.24695, 0.43524, -10.25049, 0.43588, -13.38269, 0.66034, -13.38574, 0.66104, -13.38269, 0.66034, -13.38574, 0.66104, -10.71729, 0.33771, -10.71948, 0.33832, -6.30115, 0.52399, -6.30249, 0.52396], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6.3333, "curve": [6.389, 0, 6.444, 1]}, {"time": 6.5, "vertices": [-3.98743, 1.71967, -3.98743, 1.72012, -4.1416, 0.63599, -4.14417, 0.63638, -10.33936, 1.11517, -10.34375, 1.11539, -13.74316, 1.03607, -13.74951, 1.03687, -12.66663, 0.76013, -12.67151, 0.76102, -8.38525, 0.16235, -8.38916, 0.16333, -1.8197, -0.11224, -1.82092, -0.11182, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.8291, -0.03998, -0.8291, -0.03986, -0.8291, -0.03998, -0.8291, -0.03986, -2.54919, 0.47607, -2.54919, 0.47629, -0.98108, 0.12793, -0.98218, 0.12823, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.77979, 0.09613, -4.7832, 0.09698, -10.24695, 0.43524, -10.25049, 0.43588, -13.38269, 0.66034, -13.38574, 0.66104, -13.38269, 0.66034, -13.38574, 0.66104, -10.71729, 0.33771, -10.71948, 0.33832, -6.30115, 0.52399, -6.30249, 0.52396], "curve": [6.556, 0, 6.611, 1]}, {"time": 6.6667, "curve": "stepped"}, {"time": 9.8333, "curve": [9.889, 0, 9.944, 1]}, {"time": 10, "vertices": [-3.98743, 1.71967, -3.98743, 1.72012, -4.1416, 0.63599, -4.14417, 0.63638, -10.33936, 1.11517, -10.34375, 1.11539, -13.74316, 1.03607, -13.74951, 1.03687, -12.66663, 0.76013, -12.67151, 0.76102, -8.38525, 0.16235, -8.38916, 0.16333, -1.8197, -0.11224, -1.82092, -0.11182, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.8291, -0.03998, -0.8291, -0.03986, -0.8291, -0.03998, -0.8291, -0.03986, -2.54919, 0.47607, -2.54919, 0.47629, -0.98108, 0.12793, -0.98218, 0.12823, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.77979, 0.09613, -4.7832, 0.09698, -10.24695, 0.43524, -10.25049, 0.43588, -13.38269, 0.66034, -13.38574, 0.66104, -13.38269, 0.66034, -13.38574, 0.66104, -10.71729, 0.33771, -10.71948, 0.33832, -6.30115, 0.52399, -6.30249, 0.52396], "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667}]}}, "head": {"head": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "offset": 620, "vertices": [-5.65234, 0.40118, -5.65393, 0.40161, -10.51257, 0.22498, -10.51453, 0.2254, -12.35291, 0.08105, -12.3551, 0.08142, -9.79224, -0.00201, -9.79553, -0.00137, -3.67041, -0.47418, -3.67175, -0.47379], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6.3333, "curve": [6.389, 0, 6.444, 1]}, {"time": 6.5, "offset": 620, "vertices": [-5.65234, 0.40118, -5.65393, 0.40161, -10.51257, 0.22498, -10.51453, 0.2254, -12.35291, 0.08105, -12.3551, 0.08142, -9.79224, -0.00201, -9.79553, -0.00137, -3.67041, -0.47418, -3.67175, -0.47379], "curve": [6.556, 0, 6.611, 1]}, {"time": 6.6667, "curve": "stepped"}, {"time": 9.8333, "curve": [9.889, 0, 9.944, 1]}, {"time": 10, "offset": 620, "vertices": [-5.65234, 0.40118, -5.65393, 0.40161, -10.51257, 0.22498, -10.51453, 0.2254, -12.35291, 0.08105, -12.3551, 0.08142, -9.79224, -0.00201, -9.79553, -0.00137, -3.67041, -0.47418, -3.67175, -0.47379], "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667}]}}, "head2": {"head": {"deform": [{"time": 6.3333, "curve": [6.444, 0, 6.556, 1]}, {"time": 6.6667, "offset": 24, "vertices": [-2.2478, 0.0322, -2.24927, 0.03236, -2.70508, 0.20436, -2.70703, 0.20459, -2.07739, 0.35234, -2.07837, 0.35243, 0, 0, 0, 0, 0.9292, 0.0163, 0.92725, 0.01642, 0.9292, 0.0163, 0.92725, 0.01642, 1.16516, -0.00812, 1.16321, -0.00797, 0.47241, 0.06747, 0.47168, 0.06738, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.2478, 0.0322, -2.24927, 0.03236, -2.2478, 0.0322, -2.24927, 0.03236, -1.6925, -0.24214, -1.69299, -0.24203, -0.85413, -0.12225, -0.85461, -0.12219, -0.74841, -0.04895, -0.7489, -0.04892, -0.63452, -0.03265, -0.63489, -0.03262, 0, 0, 0, 0, 0, 0, 0, 0, 0.75793, -0.00813, 0.75635, -0.00803, 1.1095, -0.01636, 1.10571, -0.01611, 0, 0, 0, 0, 0.77454, -0.12209, 0.77234, -0.12198, 0.51428, 0.07312, 0.51172, 0.07333, 1.17346, -0.06509, 1.17114, -0.06494, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.34167, -0.04892, -0.3418, -0.04889, -1.63721, -0.17386, -1.63745, -0.1736, -2.05542, -0.17554, -2.05579, -0.17528, -1.85205, 0.02802, -1.85229, 0.02824, -0.80872, -0.11572, -0.80896, -0.11563, 0.44836, 0.1221, 0.44739, 0.12216, 0.79797, 0.11397, 0.79773, 0.11401, 1.81628, -0.08951, 1.81396, -0.08932, 1.81628, -0.08951, 1.81396, -0.08932, 0.60413, -0.14656, 0.6012, -0.14642, 0.27698, 0.09767, 0.27649, 0.09772, 1.35303, -0.09766, 1.34998, -0.09747, 2.58337, -0.15462, 2.578, -0.15427, 2.58337, -0.15462, 2.578, -0.15427, 1.35303, -0.09766, 1.34998, -0.09747, 0.47241, 0.06747, 0.47168, 0.06738], "curve": "stepped"}, {"time": 9.6667, "offset": 24, "vertices": [-2.2478, 0.0322, -2.24927, 0.03236, -2.70508, 0.20436, -2.70703, 0.20459, -2.07739, 0.35234, -2.07837, 0.35243, 0, 0, 0, 0, 0.9292, 0.0163, 0.92725, 0.01642, 0.9292, 0.0163, 0.92725, 0.01642, 1.16516, -0.00812, 1.16321, -0.00797, 0.47241, 0.06747, 0.47168, 0.06738, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.2478, 0.0322, -2.24927, 0.03236, -2.2478, 0.0322, -2.24927, 0.03236, -1.6925, -0.24214, -1.69299, -0.24203, -0.85413, -0.12225, -0.85461, -0.12219, -0.74841, -0.04895, -0.7489, -0.04892, -0.63452, -0.03265, -0.63489, -0.03262, 0, 0, 0, 0, 0, 0, 0, 0, 0.75793, -0.00813, 0.75635, -0.00803, 1.1095, -0.01636, 1.10571, -0.01611, 0, 0, 0, 0, 0.77454, -0.12209, 0.77234, -0.12198, 0.51428, 0.07312, 0.51172, 0.07333, 1.17346, -0.06509, 1.17114, -0.06494, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.34167, -0.04892, -0.3418, -0.04889, -1.63721, -0.17386, -1.63745, -0.1736, -2.05542, -0.17554, -2.05579, -0.17528, -1.85205, 0.02802, -1.85229, 0.02824, -0.80872, -0.11572, -0.80896, -0.11563, 0.44836, 0.1221, 0.44739, 0.12216, 0.79797, 0.11397, 0.79773, 0.11401, 1.81628, -0.08951, 1.81396, -0.08932, 1.81628, -0.08951, 1.81396, -0.08932, 0.60413, -0.14656, 0.6012, -0.14642, 0.27698, 0.09767, 0.27649, 0.09772, 1.35303, -0.09766, 1.34998, -0.09747, 2.58337, -0.15462, 2.578, -0.15427, 2.58337, -0.15462, 2.578, -0.15427, 1.35303, -0.09766, 1.34998, -0.09747, 0.47241, 0.06747, 0.47168, 0.06738], "curve": [9.889, 0, 10.111, 1]}, {"time": 10.3333}]}}, "head_B": {"head_B": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "offset": 90, "vertices": [-2.69824, 0.53479, -2.69873, 0.53491, -6.98938, 0.63464, -6.99023, 0.63489, -9.79578, 0.82312, -9.79761, 0.8233, -10.36536, 0.72449, -10.36731, 0.72467, -7.65161, 0.62085, -7.65332, 0.62085, -3.01355, -0.37695, -3.01404, -0.37671], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6.3333, "curve": [6.389, 0, 6.444, 1]}, {"time": 6.5, "offset": 90, "vertices": [-2.69824, 0.53479, -2.69873, 0.53491, -6.98938, 0.63464, -6.99023, 0.63489, -9.79578, 0.82312, -9.79761, 0.8233, -10.36536, 0.72449, -10.36731, 0.72467, -7.65161, 0.62085, -7.65332, 0.62085, -3.01355, -0.37695, -3.01404, -0.37671], "curve": [6.556, 0, 6.611, 1]}, {"time": 6.6667, "curve": "stepped"}, {"time": 9.8333, "curve": [9.889, 0, 9.944, 1]}, {"time": 10, "offset": 90, "vertices": [-2.69824, 0.53479, -2.69873, 0.53491, -6.98938, 0.63464, -6.99023, 0.63489, -9.79578, 0.82312, -9.79761, 0.8233, -10.36536, 0.72449, -10.36731, 0.72467, -7.65161, 0.62085, -7.65332, 0.62085, -3.01355, -0.37695, -3.01404, -0.37671], "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667}]}}}}}, "touch": {"bones": {"ALL2": {"translate": [{"y": -24.14, "curve": [0.078, 0, 0.156, 0, 0.024, -23.65, 0.059, 27.44]}, {"time": 0.2333, "y": 27.44, "curve": [0.544, 0, 0.856, 0, 0.544, 27.44, 0.856, -24.14]}, {"time": 1.1667, "y": -24.14}]}, "tun": {"rotate": [{"value": -1.86, "curve": [0.024, -1.81, 0.059, 2.83]}, {"time": 0.2333, "value": 2.83, "curve": [0.544, 2.83, 0.856, -1.86]}, {"time": 1.1667, "value": -1.86}]}, "body": {"rotate": [{"value": -3.15, "curve": [0.024, -3.08, 0.059, 4.6]}, {"time": 0.2333, "value": 4.6, "curve": [0.544, 4.6, 0.856, -3.15]}, {"time": 1.1667, "value": -3.15}], "translate": [{"y": -10.44, "curve": [0.078, 0, 0.156, 0, 0.024, -10.22, 0.059, 12.13]}, {"time": 0.2333, "y": 12.13, "curve": [0.544, 0, 0.856, 0, 0.544, 12.13, 0.856, -10.44]}, {"time": 1.1667, "y": -10.44}], "scale": [{"y": 1.041, "curve": [0.078, 1, 0.156, 1, 0.024, 1.04, 0.059, 0.955]}, {"time": 0.2333, "y": 0.955, "curve": [0.544, 1, 0.856, 1, 0.544, 0.955, 0.856, 1.041]}, {"time": 1.1667, "y": 1.041}]}, "body2": {"rotate": [{"value": 1.87, "curve": [0.028, 1.81, 0.067, -3.55]}, {"time": 0.2667, "value": -3.55, "curve": [0.567, -3.55, 0.867, 1.87]}, {"time": 1.1667, "value": 1.87}], "translate": [{"x": -5.04, "curve": [0.028, -4.87, 0.067, 11.07, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 11.07, "curve": [0.567, 11.07, 0.867, -5.04, 0.567, 0, 0.867, 0]}, {"time": 1.1667, "x": -5.04}], "scale": [{"x": 1.005, "y": 1.005, "curve": [0.028, 1.005, 0.067, 1.028, 0.028, 1.005, 0.067, 1.028]}, {"time": 0.2667, "x": 1.028, "y": 1.028, "curve": [0.567, 1.028, 0.867, 1.005, 0.567, 1.028, 0.867, 1.005]}, {"time": 1.1667, "x": 1.005, "y": 1.005}]}, "neck": {"rotate": [{"value": -0.36, "curve": [0.031, -0.21, 0.076, 4.16]}, {"time": 0.3, "value": 4.16, "curve": [0.589, 4.16, 0.878, -0.36]}, {"time": 1.1667, "value": -0.36}], "translate": [{"curve": [0.1, 0, 0.2, 4.57, 0.1, 0, 0.2, 5.43]}, {"time": 0.3, "x": 4.57, "y": 5.43, "curve": [0.589, 4.57, 0.878, 0, 0.589, 5.43, 0.878, 0]}, {"time": 1.1667}]}, "head": {"rotate": [{"value": 0.08, "curve": [0.031, 0.22, 0.076, 8.15]}, {"time": 0.3, "value": 8.15, "curve": [0.589, 8.15, 0.878, 0.08]}, {"time": 1.1667, "value": 0.08}]}, "hair_F2": {"rotate": [{"value": -1.77, "curve": [0.123, 0.59, 0.245, 3.73]}, {"time": 0.3667, "value": 3.73, "curve": [0.562, 3.73, 0.772, -4.34]}, {"time": 0.9667, "value": -4.34, "curve": [1.04, -4.34, 1.094, -3.2]}, {"time": 1.1667, "value": -1.77}]}, "leg_R3": {"rotate": [{}]}, "leg_L3": {"rotate": [{"value": -0.01}]}, "tun_R": {"translate": [{"x": 11.34, "y": 5.7, "curve": [0.025, 12.3, 0.043, 13.03, 0.025, 6.12, 0.043, 6.44]}, {"time": 0.0667, "x": 13.03, "y": 6.44, "curve": [0.262, 13.03, 0.472, -23, 0.262, 6.44, 0.472, -9.41]}, {"time": 0.6667, "x": -23, "y": -9.41, "curve": [0.838, -23, 0.997, 4.51, 0.838, -9.41, 0.997, 2.69]}, {"time": 1.1667, "x": 11.34, "y": 5.7}], "scale": [{"x": 1.046, "y": 0.97, "curve": [0.024, 1.056, 0.042, 1.063, 0.024, 0.956, 0.042, 0.945]}, {"time": 0.0667, "x": 1.063, "y": 0.945, "curve": [0.164, 1.063, 0.269, 0.957, 0.164, 0.945, 0.269, 1.1]}, {"time": 0.3667, "x": 0.957, "y": 1.1, "curve": [0.464, 0.957, 0.569, 1.063, 0.464, 1.1, 0.569, 0.945]}, {"time": 0.6667, "x": 1.063, "y": 0.945, "curve": [0.764, 1.063, 0.869, 0.957, 0.764, 0.945, 0.869, 1.1]}, {"time": 0.9667, "x": 0.957, "y": 1.1, "curve": [1.04, 0.957, 1.094, 1.016, 1.04, 1.1, 1.094, 1.013]}, {"time": 1.1667, "x": 1.046, "y": 0.97}]}, "tun_R2": {"translate": [{"x": 0.72, "y": -11.74, "curve": [0.05, 0.67, 0.085, 0.64, 0.05, -16.29, 0.085, -19.68]}, {"time": 0.1333, "x": 0.64, "y": -19.68, "curve": [0.328, 0.64, 0.538, 1.15, 0.328, -19.68, 0.538, 29.99]}, {"time": 0.7333, "x": 1.15, "y": 29.99, "curve": [0.88, 1.15, 1.022, 0.86, 0.88, 29.99, 1.022, 2.17]}, {"time": 1.1667, "x": 0.72, "y": -11.74}], "scale": [{"x": 1.01, "y": 1.023, "curve": [0.049, 1.036, 0.085, 1.063, 0.049, 0.984, 0.085, 0.945]}, {"time": 0.1333, "x": 1.063, "y": 0.945, "curve": [0.231, 1.063, 0.336, 0.957, 0.231, 0.945, 0.336, 1.1]}, {"time": 0.4333, "x": 0.957, "y": 1.1, "curve": [0.531, 0.957, 0.636, 1.063, 0.531, 1.1, 0.636, 0.945]}, {"time": 0.7333, "x": 1.063, "y": 0.945, "curve": [0.831, 1.063, 0.936, 0.957, 0.831, 0.945, 0.936, 1.1]}, {"time": 1.0333, "x": 0.957, "y": 1.1, "curve": [1.082, 0.957, 1.119, 0.983, 1.082, 1.1, 1.119, 1.061]}, {"time": 1.1667, "x": 1.01, "y": 1.023}]}, "tun_R3": {"translate": [{"x": 0.84, "y": -8.11, "curve": [0.074, 0.81, 0.16, 0.77, 0.074, -16.78, 0.16, -23.9]}, {"time": 0.2333, "x": 0.77, "y": -23.9, "curve": [0.428, 0.77, 0.605, 0.99, 0.428, -23.9, 0.605, 25.77]}, {"time": 0.8, "x": 0.99, "y": 25.77, "curve": [0.922, 0.99, 1.045, 0.91, 0.922, 25.77, 1.045, 6.46]}, {"time": 1.1667, "x": 0.84, "y": -8.11}], "scale": [{"x": 0.974, "y": 1.075, "curve": [0.073, 1.003, 0.16, 1.063, 0.073, 1.032, 0.16, 0.945]}, {"time": 0.2333, "x": 1.063, "y": 0.945, "curve": [0.331, 1.063, 0.402, 0.957, 0.331, 0.945, 0.402, 1.1]}, {"time": 0.5, "x": 0.957, "y": 1.1, "curve": [0.598, 0.957, 0.702, 1.063, 0.598, 1.1, 0.702, 0.945]}, {"time": 0.8, "x": 1.063, "y": 0.945, "curve": [0.898, 1.063, 1.002, 0.957, 0.898, 0.945, 1.002, 1.1]}, {"time": 1.1, "x": 0.957, "y": 1.1, "curve": [1.125, 0.957, 1.143, 0.964, 1.125, 1.1, 1.143, 1.09]}, {"time": 1.1667, "x": 0.974, "y": 1.075}]}, "tun_L": {"translate": [{"x": 11.34, "y": 5.7, "curve": [0.025, 12.3, 0.043, 13.03, 0.025, 6.12, 0.043, 6.44]}, {"time": 0.0667, "x": 13.03, "y": 6.44, "curve": [0.262, 13.03, 0.472, -23, 0.262, 6.44, 0.472, -9.41]}, {"time": 0.6667, "x": -23, "y": -9.41, "curve": [0.838, -23, 0.997, 4.51, 0.838, -9.41, 0.997, 2.69]}, {"time": 1.1667, "x": 11.34, "y": 5.7}], "scale": [{"x": 1.046, "y": 0.97, "curve": [0.024, 1.056, 0.042, 1.063, 0.024, 0.956, 0.042, 0.945]}, {"time": 0.0667, "x": 1.063, "y": 0.945, "curve": [0.164, 1.063, 0.269, 0.957, 0.164, 0.945, 0.269, 1.1]}, {"time": 0.3667, "x": 0.957, "y": 1.1, "curve": [0.464, 0.957, 0.569, 1.063, 0.464, 1.1, 0.569, 0.945]}, {"time": 0.6667, "x": 1.063, "y": 0.945, "curve": [0.764, 1.063, 0.869, 0.957, 0.764, 0.945, 0.869, 1.1]}, {"time": 0.9667, "x": 0.957, "y": 1.1, "curve": [1.04, 0.957, 1.094, 1.016, 1.04, 1.1, 1.094, 1.013]}, {"time": 1.1667, "x": 1.046, "y": 0.97}]}, "tun_L2": {"translate": [{"x": 0.72, "y": -11.74, "curve": [0.05, 0.67, 0.085, 0.64, 0.05, -16.29, 0.085, -19.69]}, {"time": 0.1333, "x": 0.64, "y": -19.69, "curve": [0.328, 0.64, 0.538, 1.15, 0.328, -19.69, 0.538, 29.99]}, {"time": 0.7333, "x": 1.15, "y": 29.99, "curve": [0.88, 1.15, 1.022, 0.86, 0.88, 29.99, 1.022, 2.17]}, {"time": 1.1667, "x": 0.72, "y": -11.74}], "scale": [{"x": 1.01, "y": 1.023, "curve": [0.049, 1.036, 0.085, 1.063, 0.049, 0.984, 0.085, 0.945]}, {"time": 0.1333, "x": 1.063, "y": 0.945, "curve": [0.231, 1.063, 0.336, 0.957, 0.231, 0.945, 0.336, 1.1]}, {"time": 0.4333, "x": 0.957, "y": 1.1, "curve": [0.531, 0.957, 0.636, 1.063, 0.531, 1.1, 0.636, 0.945]}, {"time": 0.7333, "x": 1.063, "y": 0.945, "curve": [0.831, 1.063, 0.936, 0.957, 0.831, 0.945, 0.936, 1.1]}, {"time": 1.0333, "x": 0.957, "y": 1.1, "curve": [1.082, 0.957, 1.119, 0.983, 1.082, 1.1, 1.119, 1.061]}, {"time": 1.1667, "x": 1.01, "y": 1.023}]}, "tun_L3": {"translate": [{"x": 0.84, "y": -8.11, "curve": [0.074, 0.81, 0.16, 0.77, 0.074, -16.78, 0.16, -23.9]}, {"time": 0.2333, "x": 0.77, "y": -23.9, "curve": [0.428, 0.77, 0.605, 0.99, 0.428, -23.9, 0.605, 25.77]}, {"time": 0.8, "x": 0.99, "y": 25.77, "curve": [0.922, 0.99, 1.045, 0.91, 0.922, 25.77, 1.045, 6.46]}, {"time": 1.1667, "x": 0.84, "y": -8.11}], "scale": [{"x": 0.974, "y": 1.075, "curve": [0.073, 1.003, 0.16, 1.063, 0.073, 1.032, 0.16, 0.945]}, {"time": 0.2333, "x": 1.063, "y": 0.945, "curve": [0.331, 1.063, 0.402, 0.957, 0.331, 0.945, 0.402, 1.1]}, {"time": 0.5, "x": 0.957, "y": 1.1, "curve": [0.598, 0.957, 0.702, 1.063, 0.598, 1.1, 0.702, 0.945]}, {"time": 0.8, "x": 1.063, "y": 0.945, "curve": [0.898, 1.063, 1.002, 0.957, 0.898, 0.945, 1.002, 1.1]}, {"time": 1.1, "x": 0.957, "y": 1.1, "curve": [1.125, 0.957, 1.143, 0.964, 1.125, 1.1, 1.143, 1.09]}, {"time": 1.1667, "x": 0.974, "y": 1.075}]}, "sh_R": {"translate": [{"x": -4.28, "y": 1.75, "curve": [0.028, -4.11, 0.067, 15.67, 0.028, 1.7, 0.067, -4.68]}, {"time": 0.2667, "x": 15.67, "y": -4.68, "curve": [0.567, 15.67, 0.867, -4.28, 0.567, -4.68, 0.867, 1.75]}, {"time": 1.1667, "x": -4.28, "y": 1.75}]}, "arm_R": {"rotate": [{}]}, "arm_R2": {"rotate": [{}]}, "sh_L": {"translate": [{"x": -2.52, "y": 1.09, "curve": [0.028, -2.33, 0.067, 19.74, 0.028, 1.02, 0.067, -6.02]}, {"time": 0.2667, "x": 19.74, "y": -6.02, "curve": [0.567, 19.74, 0.867, -2.52, 0.567, -6.02, 0.867, 1.09]}, {"time": 1.1667, "x": -2.52, "y": 1.09}]}, "arm_L2": {"rotate": [{}]}, "hair_B": {"rotate": [{"value": 1.5, "curve": [0.074, 3.01, 0.16, 4.24]}, {"time": 0.2333, "value": 4.24, "curve": [0.428, 4.24, 0.605, -4.38]}, {"time": 0.8, "value": -4.38, "curve": [0.922, -4.38, 1.045, -1.03]}, {"time": 1.1667, "value": 1.5}]}, "RU": {"translate": [{"x": -9.92, "y": 5.93, "curve": [0.074, -22.26, 0.16, -32.38, 0.074, 10.61, 0.16, 14.45]}, {"time": 0.2333, "x": -32.38, "y": 14.45, "curve": [0.428, -32.38, 0.605, 38.25, 0.428, 14.45, 0.605, -12.34]}, {"time": 0.8, "x": 38.25, "y": -12.34, "curve": [0.922, 38.25, 1.045, 10.79, 0.922, -12.34, 1.045, -1.93]}, {"time": 1.1667, "x": -9.92, "y": 5.93}]}, "RU2": {"translate": [{"x": 1.6, "y": 5.08, "curve": [0.099, -13.5, 0.203, -28.81, 0.099, 8.24, 0.203, 11.44]}, {"time": 0.3, "x": -28.81, "y": 11.44, "curve": [0.495, -28.81, 0.672, 32.02, 0.495, 11.44, 0.672, -1.28]}, {"time": 0.8667, "x": 32.02, "y": -1.28, "curve": [0.965, 32.02, 1.07, 16.91, 0.965, -1.28, 1.07, 1.88]}, {"time": 1.1667, "x": 1.6, "y": 5.08}]}, "RU3": {"translate": [{"x": 2.69, "y": 1.81, "curve": [0.113, -11.77, 0.221, -28.81, 0.113, 6.23, 0.221, 11.44]}, {"time": 0.3333, "x": -28.81, "y": 11.44, "curve": [0.528, -28.81, 0.738, 22.74, 0.528, 11.44, 0.738, -4.32]}, {"time": 0.9333, "x": 22.74, "y": -4.32, "curve": [1.017, 22.74, 1.084, 13.45, 1.017, -4.32, 1.084, -1.48]}, {"time": 1.1667, "x": 2.69, "y": 1.81}]}, "hair_L": {"rotate": [{"value": 1.76, "curve": [0.123, -0.4, 0.245, -3.27]}, {"time": 0.3667, "value": -3.27, "curve": [0.562, -3.27, 0.772, 4.1]}, {"time": 0.9667, "value": 4.1, "curve": [1.04, 4.1, 1.094, 3.06]}, {"time": 1.1667, "value": 1.76}]}, "RU4": {"translate": [{"x": 6.82, "y": 0.41, "curve": [0.128, -4.77, 0.24, -21.26, 0.128, 3.73, 0.24, 8.45]}, {"time": 0.3667, "x": -21.26, "y": 8.45, "curve": [0.562, -21.26, 0.772, 17.96, 0.562, 8.45, 0.772, -2.78]}, {"time": 0.9667, "x": 17.96, "y": -2.78, "curve": [1.036, 17.96, 1.1, 13.15, 1.036, -2.78, 1.1, -1.4]}, {"time": 1.1667, "x": 6.82, "y": 0.41}]}, "headround3": {"translate": [{"x": 45.2, "curve": [0.035, 47.64, 0.084, 156.25, 0.035, -0.03, 0.084, -2.47]}, {"time": 0.3333, "x": 156.25, "y": -2.47, "curve": [0.611, 156.25, 0.889, 45.2, 0.611, -2.47, 0.889, 0]}, {"time": 1.1667, "x": 45.2}]}, "headround": {"translate": [{"y": 180.93, "curve": [0.035, 0.03, 0.084, 1.94, 0.035, 182.85, 0.084, 321.43]}, {"time": 0.3333, "x": 1.94, "y": 321.43, "curve": [0.611, 1.94, 0.889, 0, 0.611, 321.43, 0.889, 180.93]}, {"time": 1.1667, "y": 180.93}]}, "bodyround": {"translate": [{"x": -46.31, "y": -103.59, "curve": [0.031, -44.62, 0.076, 132.09, 0.031, -98.75, 0.076, 431.86]}, {"time": 0.3, "x": 132.09, "y": 431.86, "curve": [0.589, 132.09, 0.878, -46.31, 0.589, 431.86, 0.878, -103.59]}, {"time": 1.1667, "x": -46.31, "y": -103.59}]}, "tunround": {"translate": [{"x": 249.25, "curve": [0.024, 242.94, 0.059, -413.24, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": -413.24, "curve": [0.544, -413.24, 0.856, 249.25, 0.544, 0, 0.856, 0]}, {"time": 1.1667, "x": 249.25}]}, "leg_R5": {"rotate": [{"value": 0.01}]}, "leg_R1": {"translate": [{"y": 11.78, "curve": [0.078, 0, 0.156, 0, 0.024, 11.51, 0.059, -16.51]}, {"time": 0.2333, "y": -16.51, "curve": [0.544, 0, 0.856, 0, 0.544, -16.51, 0.856, 11.78]}, {"time": 1.1667, "y": 11.78}]}, "leg_L5": {"rotate": [{"value": -0.12}]}, "leg_L1": {"translate": [{"y": 14.85, "curve": [0.078, 0, 0.156, 0, 0.024, 14.45, 0.059, -27.1]}, {"time": 0.2333, "y": -27.1, "curve": [0.544, 0, 0.856, 0, 0.544, -27.1, 0.856, 14.85]}, {"time": 1.1667, "y": 14.85}]}, "sh_R2": {"rotate": [{"value": 0.11}]}, "sh_R3": {"rotate": [{"value": -0.07}]}, "sh_L2": {"rotate": [{"value": 0.06}]}, "sh_L3": {"rotate": [{"value": 0.04}]}, "eyebrow_L": {"rotate": [{"curve": [0.023, 0, 0.06, -12.87]}, {"time": 0.2333, "value": -12.93, "curve": [0.544, -13.04, 0.856, 0]}, {"time": 1.1667}], "translate": [{"curve": [0.023, 0, 0.06, 1.8, 0.023, 0, 0.06, -0.02]}, {"time": 0.2333, "x": 1.81, "y": -0.03, "curve": [0.544, 1.83, 0.856, 0, 0.544, -0.03, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_L2": {"rotate": [{"curve": [0.023, 0, 0.06, -14.71]}, {"time": 0.2333, "value": -14.78, "curve": [0.544, -14.91, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_R3": {"rotate": [{"curve": [0.023, 0, 0.06, 10.48]}, {"time": 0.2333, "value": 10.53, "curve": [0.544, 10.62, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_R2": {"rotate": [{"curve": [0.023, 0, 0.06, 6.61]}, {"time": 0.2333, "value": 6.64, "curve": [0.544, 6.7, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_R": {"translate": [{"curve": [0.023, 0, 0.06, 1.8, 0.023, 0, 0.06, -0.02]}, {"time": 0.2333, "x": 1.81, "y": -0.03, "curve": [0.544, 1.83, 0.856, 0, 0.544, -0.03, 0.856, 0]}, {"time": 1.1667}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"curve": [0.026, 0, 0.058, 1]}, {"time": 0.2333, "vertices": [-2.79201, 0.02271, -2.79226, 0.02287, -3.00211, -0.10374, -3.00275, -0.10364, -2.07857, -0.01676, -2.07899, -0.01641, -2.21738, -0.03673, -2.21776, -0.03633, -1.48065, 0.06849, -1.48091, 0.06878, -1.12875, 0.29489, -1.12907, 0.29512, -1.00498, 0.20763, -1.00507, 0.20795, -0.68016, 0.09695, -0.68026, 0.09713, -0.30647, 0.00515, -0.30647, 0.00525, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.2345, -0.03452, -0.23457, -0.03452, -0.72801, -0.01164, -0.72814, -0.01158, -1.83198, -0.15745, -1.83236, -0.15743, -0.25735, 0.02421, -0.25738, 0.02426, -1.2338, 0.1306, -1.23393, 0.13062, -2.03831, 0.05546, -2.03867, 0.05553, -2.82461, 0.07777, -2.82496, 0.07785, -2.97171, 0.00242, -2.97206, 0.00238, -2.38827, -0.01608, -2.38856, -0.01612, -1.13542, -0.08905, -1.13568, -0.08896, -0.24596, -0.00518, -0.24605], "curve": [0.544, 0, 0.856, 1]}, {"time": 1.1667}]}}, "eye_RR": {"eye_RR": {"deform": [{"curve": [0.026, 0, 0.058, 1]}, {"time": 0.2333, "vertices": [-1.04205, 0.44941, -1.04205, 0.44953, -1.08234, 0.1662, -1.08301, 0.16631, -2.70202, 0.29143, -2.70316, 0.29149, -3.59154, 0.27076, -3.5932, 0.27097, -3.31021, 0.19865, -3.31148, 0.19888, -2.19134, 0.04243, -2.19236, 0.04268, -0.47555, -0.02933, -0.47587, -0.02922, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.21667, -0.01045, -0.21667, -0.01042, -0.21667, -0.01045, -0.21667, -0.01042, -0.66619, 0.12441, -0.66619, 0.12447, -0.25639, 0.03343, -0.25668, 0.03351, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.24912, 0.02512, -1.25001, 0.02535, -2.67787, 0.11374, -2.67879, 0.11391, -3.49734, 0.17257, -3.49814, 0.17275, -3.49734, 0.17257, -3.49814, 0.17275, -2.80078, 0.08825, -2.80135, 0.08841, -1.6467, 0.13694, -1.64705, 0.13693], "curve": [0.544, 0, 0.856, 1]}, {"time": 1.1667}]}}, "head": {"head": {"deform": [{"curve": [0.026, 0, 0.058, 1]}, {"time": 0.2333, "offset": 620, "vertices": [-1.47714, 0.10484, -1.47756, 0.10495, -2.74728, 0.05879, -2.74779, 0.05891, -3.22822, 0.02118, -3.2288, 0.02128, -2.55903, -0.00053, -2.5599, -0.00036, -0.9592, -0.12392, -0.95955, -0.12382], "curve": [0.544, 0, 0.856, 1]}, {"time": 1.1667}]}}, "head2": {"head": {"deform": [{"curve": [0.026, 0, 0.058, 1]}, {"time": 0.2333, "offset": 24, "vertices": [-2.2478, 0.0322, -2.24927, 0.03236, -2.70508, 0.20436, -2.70703, 0.20459, -2.07739, 0.35234, -2.07837, 0.35243, 0, 0, 0, 0, 0.9292, 0.0163, 0.92725, 0.01642, 0.9292, 0.0163, 0.92725, 0.01642, 1.16516, -0.00812, 1.16321, -0.00797, 0.47241, 0.06747, 0.47168, 0.06738, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.2478, 0.0322, -2.24927, 0.03236, -2.2478, 0.0322, -2.24927, 0.03236, -1.6925, -0.24214, -1.69299, -0.24203, -0.85413, -0.12225, -0.85461, -0.12219, -0.74841, -0.04895, -0.7489, -0.04892, -0.63452, -0.03265, -0.63489, -0.03262, 0, 0, 0, 0, 0, 0, 0, 0, 0.75793, -0.00813, 0.75635, -0.00803, 1.1095, -0.01636, 1.10571, -0.01611, 0, 0, 0, 0, 0.77454, -0.12209, 0.77234, -0.12198, 0.51428, 0.07312, 0.51172, 0.07333, 1.17346, -0.06509, 1.17114, -0.06494, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.34167, -0.04892, -0.3418, -0.04889, -1.63721, -0.17386, -1.63745, -0.1736, -2.05542, -0.17554, -2.05579, -0.17528, -1.85205, 0.02802, -1.85229, 0.02824, -0.80872, -0.11572, -0.80896, -0.11563, 0.44836, 0.1221, 0.44739, 0.12216, 0.79797, 0.11397, 0.79773, 0.11401, 1.81628, -0.08951, 1.81396, -0.08932, 1.81628, -0.08951, 1.81396, -0.08932, 0.60413, -0.14656, 0.6012, -0.14642, 0.27698, 0.09767, 0.27649, 0.09772, 1.35303, -0.09766, 1.34998, -0.09747, 2.58337, -0.15462, 2.578, -0.15427, 2.58337, -0.15462, 2.578, -0.15427, 1.35303, -0.09766, 1.34998, -0.09747, 0.47241, 0.06747, 0.47168, 0.06738], "curve": [0.544, 0, 0.856, 1]}, {"time": 1.1667}]}}, "head_B": {"head_B": {"deform": [{"curve": [0.026, 0, 0.058, 1]}, {"time": 0.2333, "offset": 90, "vertices": [-0.70514, 0.13976, -0.70527, 0.13979, -1.82656, 0.16585, -1.82678, 0.16592, -2.55996, 0.21511, -2.56044, 0.21516, -2.70881, 0.18933, -2.70932, 0.18938, -1.99962, 0.16225, -2.00007, 0.16225, -0.78754, -0.09851, -0.78767, -0.09845], "curve": [0.544, 0, 0.856, 1]}, {"time": 1.1667}]}}}}}}}