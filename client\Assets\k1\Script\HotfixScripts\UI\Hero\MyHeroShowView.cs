﻿using Cfg;
using Common;
using cspb;
using Cysharp.Threading.Tasks;
using DeepUI;
using DG.Tweening;
using Game.Config;
using Game.Data;
using Game.Utils;
using K3;
using Logic;
using Public;
using Spine.Unity;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TFW;
using TFW.Localization;
using TFW.Map;
using TFW.UI;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using EventTriggerType = TFW.EventTriggerType;


namespace UI
{
    [Popup("Hero/MyHeroShowView", true, true)]
    public class MyHeroShowView : BasePopupLayer
    {

        [Popup<PERSON>ield("Root/HeroSpine/Main")]
        private Transform mainHeroSpine;

        [PopupField("Root/HeroSpine/Left")]
        private Transform leftHeroSpine;

        [PopupField("Root/HeroSpine/Right")]
        private Transform rightHeroSpine;

        [PopupField("Root/HeroSpine")]
        private GameObject HeroSpineObj;

        [<PERSON>upField("Root")]
        private GameObject rootObj;
         
        [<PERSON>up<PERSON>ield("Root/HeroInfo")]
        private GameObject heroInfoGa;

        [<PERSON>up<PERSON>ield("Root/HeroStar")]
        private GameObject heroStarGa;

        //[PopupField("Root/HeroGood")]
        //private GameObject heroGoodGa;

        [PopupField("Root/Tab")]
        private TFWTabGroup _tabGroup;
        [PopupField("Root/Tab/tab1")]
        private TFWTab _tab1;
        [PopupField("Root/Tab/tab2")]
        private TFWTab _tab2;
        
        [PopupField("Root/Tab/tab1/RedDot")]
        private GameObject tabRed1;

        [PopupField("Root/Tab/tab2/RedDot")]
        private GameObject tabRed2;
         
        [PopupField("Root/HeroStar/Star/UpStar")]
        private UIGrid starGrid_new;
          
        [PopupField("BG/Name/Image/name/Icon")]
        private TFWImage attributeImage;
        [PopupField("BG/Name/Image/ssr")]
        private TFWImage ssrImage;
        [PopupField("BG/Name/Image/name")]
        private TFWText nameText;
        
        [PopupField("BG/Overlay")]
        private TFWRawImage m_heroBg;

        [PopupField("Root/Items/ItemBg")]
        private GameObject itemBg;

        [PopupField("mask")]
        private GameObject mask;
        [PopupField("Root/HeroInfo/lvText")]
        private TFWText lvText;
        [PopupField("Root/HeroInfo/power")]
        private TFWText powerText;
        [PopupField("Root/HeroInfo/Image/CapacityValue")]
        private TFWText troopValueText;

        [PopupField("Root/HeroInfo/Image/ageValue")]
        private TFWText ageValue;

        [PopupField("Root/HeroInfo/Image/bustValue")]
        private TFWText bustValue;

        [PopupField("Root/HeroInfo/Image/hrightValue")]
        private TFWText hrightValue;

        [PopupField("Root/HeroInfo/LevelUpBtn/expText")]
        private TFWText expText;

        [PopupField("Root/BtnClose")]
        private GameObject btnClose;

        [PopupField("Root/btn_video")]
        private GameObject btn_HeroVideo;

        [PopupField("Root/HeroInfo/LevelUpBtn")]
        private GameObject levelUpBtn;

        //[PopupField("Root/HeroInfo/LevelUpBtn/redPoint")]
        //private GameObject levelUpBtnRed;

        [PopupField("Root/effect_lvUp/Eff_ui_sj_lq")]
        private ParticleSystem levelUpEffect;

        //[PopupField("Root/GoodwillBtn")]
        //private GameObject btnGoodwill;

        private Game.Data.HeroData mHeroData;

        [PopupField("Root/HeroList/TabGroup")]
        private UIHeroTabGroup uiHeroTabGroup;

        //[PopupField("Root/HeroClick")]
        //private GameObject heroClick;

        [PopupField("Root/HeroInfo/lvText/btnExChangeHero")]
        private GameObject btnExChangeHero;

        [PopupField("Root/HeroInfo/lvText/btnExChangeHero/cding")]
        private GameObject exChangeHeroCd;

        [PopupField("Root/HeroInfo/lvText/btnExChangeHero/cding/Text")]
        private TimeUpdateText exChangeHeroCDText;

        //[PopupField("Root/HeroGoodInfo/btnGoodInfo")]
        //private GameObject btnGoodInfoTip;

      
        private UIHeroShowViewData mShowData;

        private cspb.ShowPower mTroopShowPower;

        private HeroUIData centerHero, leftHero, rightHero;
        private int curCount;


        private Cfg.G.CK3Item mNormalItem1 = null;
        private Cfg.G.CK3Item mNormalItem2 = null;

        bool initMsgEvent = false;
        protected override void OnInit()
        {
            centerHero = new HeroUIData()
            {
                spineParent = mainHeroSpine.gameObject,
                ShowPos = 0
            };
            leftHero = new HeroUIData()
            {
                spineParent = leftHeroSpine.gameObject,
                ShowPos = 1
            };
            rightHero = new HeroUIData()
            {
                spineParent = rightHeroSpine.gameObject,
                ShowPos = 2
            };

            BindClickListener(btnClose, (x, y) =>
            {
                Close();
            });

            //BindClickListener(btnGoodInfoTip, (x, y) =>
            //{
            //    PopupManager.I.ShowDialog<UIHeroIntimacyInfo>(new UIHeroIntimacyInfoData() { quality = mHeroData.HeroCfg.HeroType });
            //});

            BindClickListener(btn_HeroVideo, (x, y) =>
            {
                PopupManager.I.ShowLayer<MyHeroVideoView>(new MyHeroVideoViewData() {  mainHeroData=mHeroData });
            });
            
            BindClickListener(btnStarUp, (x, y) => 
            {
                if (mHeroData.CanUpStarUp(out var curCard, out var needCard))
                {
                    InitCreateItem();

                    var message = new HeroStepsUpgradeReq();
                    message.heroCfgID = mHeroData.ModelId;
                    MessageMgr.Send(message);
                }
                else
                {
                    if (needCard > 0)
                    {
                        //Ӣ����Ƭ������ʱ��ʾ 
                        HeroUtils.ShowViewTOGetHero(mHeroData.HeroCfg, false);
                    }
                }
            });

            //BindClickListener(btnGoodwill, (x, y) =>
            //{
            //    if (!mShowData.showGetBtn)
            //    {
            //        //if (LSwitchMgr.I.IsFunctionOpen(SwitchConfig.HeroGoodwill))
            //        //{
            //        //    PopupManager.I.ShowLayer<UIHeroGoodwill>(mHeroData);
            //        //    Close();
            //        //}
            //        //else
            //        {
            //            FloatTips.I.FloatMsg(LocalizationMgr.GetUIString("DEMO_30"));
            //        }
            //    }
            //    else
            //    {
            //        FloatTips.I.FloatMsg(LocalizationMgr.GetUIString("Tips_UnobtainedHero_HeroGoodwill"));
            //    }
            //});



            BindClickListener(levelUpBtn, (x, y) =>
            {
                if (mHeroData != null)
                {
                    BtnToLevelUp().Forget();
                }
            });
            
            BindClickListener(btnExChangeHero, (x, y) =>
            {
                if (UnlockMgr.I.CheckUnlock(UnlockFuncEnum.ExChangeHero) && HeroGameData.I.ExChangeCDTime <= GameTime.Time)
                {
                    if (!(mHeroData.IsBattle == HeroStatus.HeroStatusIdle || mHeroData.IsBattle == HeroStatus.HeroStatusDefender))
                    {
                        "Tips_Dispatch_Hero_Used".ToShowFloatTips();
                    }
                    else
                    {
                        var heroList= HeroGameData.I.GetAllHeroData();
                        var ShowHeroList = heroList.Where(a => a.HeroCfg.HeroType > 2 && a.Level > mHeroData.Level && a.HeroId != mHeroData.HeroId &&
                                                               a.HeroCfg.HeroMergeType == mHeroData.HeroCfg.HeroMergeType &&(!(mHeroData.IsBattle == HeroStatus.HeroStatusIdle || mHeroData.IsBattle == HeroStatus.HeroStatusDefender))).OrderByDescending(a => a.Level).ToList();
                       
                        if (ShowHeroList.Count > 0)
                        {
                            PopupManager.I.ShowPanel<UIHeroExChangeDialog>(new UIHeroExChangeDialogData()
                            {
                                heroData = mHeroData,
                        
                                defaultSelectHero = ShowHeroList[0],
                            });
                        }
                        else
                        {
                            PopupManager.I.ShowPanel<UIHeroExChangeDialog>(new UIHeroExChangeDialogData()
                            {
                                heroData = mHeroData,
                            });
                        }
                    }
                  

                  
                }

                if (HeroGameData.I.ExChangeCDTime > GameTime.Time)
                {
                    PopupManager.I.ShowPanel<UIMergeCoolingSpeed>(new CoolingSpeedData()
                    {
                        BeginCDTime = TFW.Common.ConvertUnixTimeStampToDateTime(HeroGameData.I.ExChangeCDTime - (long)(MetaConfig.Hero_level_exchange_cd * 1000)),
                        EndCDTime = TFW.Common.ConvertUnixTimeStampToDateTime(HeroGameData.I.ExChangeCDTime),
                        CdConvertDiamondConst = MetaConfig.Hero_level_exchange_cd / MetaConfig.Hero_level_exchange_cost,
                        type = 1,
                        IsChangeHero = true,
                        useDiaPassCD = (_) =>
                        {
                            HeroGameData.I.ResetExchangeHeroCdTime();
                        },
                    });
                }
            });

            UIBase.AddRemoveListener(TFW.EventTriggerType.Click, leftHero.spineParent, (x, y) =>
            {
                if (spineClickCDTime > Time.time || leftHero.spineObj == null)
                    return;

                mask.SetActive(true);
                spineClickCDTime = Time.time + 0.8f;
                leftHero.PlayAudio(true);

                if (curCount == 2)
                {
                    PlayTween(leftHero, centerHero, true).Play();
                    var mainSeq = PlayTween(centerHero, leftHero, false);

                    mainSeq.AppendCallback(() =>
                    {
                        mask.SetActive(false);
                        EventMgr.FireEvent(TEventType.UIHeroShowViewDeputyShowPos, new int[] { leftHero.DeputyPos, centerHero.DeputyPos, rightHero.DeputyPos });
                    });

                    mainSeq.Play();
                }
                else if (curCount == 3)
                {
                    PlayTween(leftHero, centerHero, true).Play();
                    PlayTween(centerHero, rightHero, false).Play();
                    var mainSeq = PlayTween(rightHero, leftHero, false);

                    mainSeq.AppendCallback(() =>
                    {
                        mask.SetActive(false);
                        EventMgr.FireEvent(TEventType.UIHeroShowViewDeputyShowPos, new int[] { leftHero.DeputyPos, rightHero.DeputyPos, centerHero.DeputyPos });
                    });

                    mainSeq.Play();
                }
            });


            UIBase.AddRemoveListener(TFW.EventTriggerType.Click, rightHero.spineParent, (x, y) =>
            {
                if (spineClickCDTime > Time.time || rightHero.spineObj == null)
                    return;
                mask.SetActive(true);
                spineClickCDTime = Time.time + 0.8f;
                rightHero.PlayAudio(true);

                if (curCount == 2)
                {
                    PlayTween(rightHero, centerHero, true).Play();
                    var mainSeq = PlayTween(centerHero, rightHero, false);

                    mainSeq.AppendCallback(() =>
                    {
                        mask.SetActive(false);
                        EventMgr.FireEvent(TEventType.UIHeroShowViewDeputyShowPos, new int[] { rightHero.DeputyPos, leftHero.DeputyPos, centerHero.DeputyPos });
                    });

                    mainSeq.Play();
                }
                else if (curCount == 3)
                {
                    PlayTween(rightHero, centerHero, true).Play();
                    PlayTween(centerHero, leftHero, false).Play();
                    var mainSeq = PlayTween(leftHero, rightHero, false);

                    mainSeq.AppendCallback(() =>
                    {
                        mask.SetActive(false);
                        EventMgr.FireEvent(TEventType.UIHeroShowViewDeputyShowPos, new int[] { rightHero.DeputyPos, centerHero.DeputyPos, leftHero.DeputyPos });
                    });

                    mainSeq.Play();
                }
            });

            _tab1.AddTabClickEvent(OnClickTab);
            _tab2.AddTabClickEvent(OnClickTab);
           
            //UIBase.AddRemoveListener(EventTriggerType.Click, heroClick, OnHeroSpineClick);
             
        }
        private string curTabTag = "1";

        private void OnClickTab(TFWTab target, PointerEventData eventData)
        {
            //if (target.TabTag == "3")
            //{
            //    if (!UnlockMgr.I.CheckUnlock(UnlockFuncEnum.HeroIntimacy))
            //    {
            //        FloatTips.I.FloatMsg(UnlockMgr.I.GetUnlockDesc(UnlockFuncEnum.HeroIntimacy));
            //        _tabGroup.TurnTabOnByTag(curTabTag);
            //        return;
            //    }

            //    DisPlayHeroGood();
            //}
            //else
            if (target.TabTag == "2")
            {
                if (!UnlockMgr.I.CheckUnlock(UnlockFuncEnum.HeroStar))
                {
                    FloatTips.I.FloatMsg(UnlockMgr.I.GetUnlockDesc(UnlockFuncEnum.HeroStar));
                    _tabGroup.TurnTabOnByTag(curTabTag);
                    return;
                }
                DisPlayHeroStar();
            }
            else
            {
                DisPlayHeroInfo();
            }

            curTabTag =target.TabTag;


            RefreshTabRed().Forget();
        }

        //private void DisPlayHeroGood()
        //{
        //    heroGoodGa.SetActive(true);
        //    heroStarGa.SetActive(false);
        //    heroInfoGa.SetActive(false); 
        //    //HeroGoodInfo.gameObject.SetActive(true);

        //    RefreshHeroGood().Forget();
        //}

        private void DisPlayHeroStar()
        {
            //heroGoodGa.SetActive(false);
            heroStarGa.SetActive(true);
            heroInfoGa.SetActive(false);

          
            //HeroGoodInfo.gameObject.SetActive(false); 
        }

        private void DisPlayHeroInfo()
        {
            //heroGoodGa.SetActive(false);
            heroStarGa.SetActive(false);
            heroInfoGa.SetActive(true);

            //heroStarGridObj.SetActive(true);
            //HeroGoodInfo.gameObject.SetActive(false); 
        }

        private async UniTaskVoid RefreshTabRed()
        {
            tabRed1.SetActive(false);
            tabRed2.SetActive(curTabTag != "2" && mHeroData != null && mHeroData.CanUpStarUp(out var curCard, out var needCard));
            //tabRed3.SetActive(curTabTag != "3" && mHeroData != null && (await mHeroData.CanIntimacyUp() || (HeroGameData.I.UnlockSkins.TryGetValue(mHeroData.HeroCfg.Id, out var skins) && skins.Count > 0)));
        }

        //private bool isPlay = false;

        //private void OnHeroSpineClick(GameObject arg0, PointerEventData arg1)
        //{
        //    if (isPlay)
        //    {
        //        return;
        //    }
        //    isPlay = true;
        //    var anim = centerHero.spineObj.GetComponent<SkeletonGraphic>();

        //    if (anim.AnimationState != null && anim.Skeleton.Data.FindAnimation("touch") != null)
        //    {
        //        anim.AnimationState.SetAnimation(0, "touch", false);
        //        anim.AnimationState.Complete += OnAnimationComplete;
        //    }
        //}

        /// <summary>
        /// ��������
        /// </summary>
        /// <param name="trackEntry"></param>
        //private void OnAnimationComplete(Spine.TrackEntry trackEntry)
        //{
        //    var anim = centerHero.spineObj.GetComponent<SkeletonGraphic>();
        //    if (trackEntry != null)
        //    {
        //        if (trackEntry.Animation.Name != "idle")
        //        {
        //            anim.AnimationState.SetAnimation(0, "idle", true);
        //        }
        //        anim.AnimationState.Complete -= OnAnimationComplete;
        //        isPlay = false;
        //    }
        //}

        public void InitCreateItem()
        {
            var createType = new List<string>();
            var boxDic = Cfg.C.CCsBox.RawDict();

            mNormalItem1 = null;
            mNormalItem2 = null;

            foreach (var item in boxDic)
            {
                if (item.Value.Unlock == mHeroData.HeroCfg.HeroMergeType)
                {
                    createType.AddRange(item.Value.CreateType);
                }
            }

            if (createType.Count > 0)
            {
                int type = int.Parse(createType[0]);
                var config = Cfg.C.CK3Item.RawList().Find(x => x.Code == type && x.Level == mHeroData.MergeItemLevel);

                mNormalItem1 = config;
            }

            if (createType.Count > 1 && UnlockMgr.I.CheckUnlock(UnlockFuncEnum.MergeBoxHighItem))
            {
                int type = int.Parse(createType[1]);
                var config = Cfg.C.CK3Item.RawList().Find(x => x.Code == type && x.Level == mHeroData.MergeItemLevel);

                mNormalItem2 = config;
            }
        }

        private void CheckHeroCreateItem(int from)
        {
            if (mNormalItem1 == null && mNormalItem2 == null)
            {
                return;
            }

            if(from==0 && mHeroData.Level>=MetaConfig.Item_upgrade_pop_hero_level_limit)
                return;


            var item1 = mNormalItem1;
            var item2 = mNormalItem2;

            InitCreateItem();

            var info = new SpecialBoxUpgradeInfo();

            info.LayerRoot = this.GameObject;
            info.TargetRoot = UIHelper.GetChild(itemBg, "itemFly");
            info.HeroData = this.mHeroData;
            info.From = from;

            var needUpdate = false;

            if (mNormalItem1?.Level > item1?.Level)
            {
                info.Item1 = item1;
                info.ItemNew1 = mNormalItem1;

                needUpdate = true;
            }

            if (mNormalItem2 != null && mNormalItem2.Level > item2?.Level)
            {
                info.Item2 = item2;
                info.ItemNew2 = mNormalItem2;

                needUpdate = true;
            }

            if (!needUpdate)
            {
                return;
            }

            if (info.Item1?.Level >= MetaConfig.MergePropUpgradeEffect || info.Item2?.Level >= MetaConfig.MergePropUpgradeEffect)
            {
                return;
            }

            PopupManager.I.ShowDialog<UIItemUnlockPanel>(info);

            mNormalItem1 = null;
            mNormalItem2 = null;
        }

        private async UniTask BtnToLevelUp()
        {
            InitCreateItem();

            long _maxLvExp = 0;

            int toLevel = Mathf.Min(mHeroData.Level + 1, GameData.I.UnlockData.MaxHeroLevel);
            _maxLvExp = HeroUtils.GetExpToLevel(mHeroData, toLevel);

            if (_maxLvExp > 0)
            {
                var hasExp = GetHasExp();
                var needExp = mHeroData.HeroExp.Exp;

                if (hasExp < needExp)
                {
                    //var expItemId = 3000027;
                    //var expItem = Cfg.C.CItem.I(expItemId);
                    //var access = new List<int>();
                    //foreach (var a in expItem.Access)
                    //{
                    //    access.Add(int.Parse(a));
                    //}
                    //var needData = new CommonItem.CommonItemData() { Id = expItemId, Typ = AssetType.Item, Val = 1 };
                    //var showDataList = await ItemStoreMgr.I.GetProShowDatas(UIComplementPop.UIComplementPopEnum.Item, access, itemData: needData, itemCfg: expItem);
                    //PopupManager.I.ShowPanel<UIComplementPop>(new UIComplementPopData() { popShowEnum = UIComplementPop.UIComplementPopEnum.HeroExp, datas = showDataList, needData = needData });
                    ItemStoreMgr.I.OpenGetAssetPop(GameCurrencyEnum.HERO_EXP, needExp - hasExp);
                    return;
                }

                var message = new HeroLvlUpReq();
                message.heroId = mHeroData.HeroId;
                message.costExp = needExp;

                //var costHeros = HeroGameData.I.GetExpHeros();

                //for (int i = 0; i < costHeros.Count; i++)
                //{
                //    if (_maxLvExp <= 0)
                //        break;

                //    var heroData = costHeros[i];

                //    if (HeroGameData.I.mCultivateMap.TryGetValue(heroData.ModelId, out var CurHeroCount))
                //    {
                //        if (CurHeroCount <= 1)
                //        {
                //            continue;
                //        }

                //        var iiExp = HeroUtils.GetHeroExp(heroData);
                        
                //        long costHeroNum =0;

                //        if ((_maxLvExp % iiExp) == 0)
                //        {
                //            costHeroNum = _maxLvExp / iiExp;
                //        }
                //        else
                //        {
                //            costHeroNum = (_maxLvExp / iiExp) + 1;
                //        }
                //        costHeroNum = costHeroNum > CurHeroCount ? CurHeroCount : costHeroNum;
                          
                //        if (costHeroNum > 0)
                //        {
                //            _maxLvExp -= (iiExp* costHeroNum);

                //            message.costHeroes.Add(heroData.HeroId);

                //            if (!message.consumeHeroes.ContainsKey(heroData.ModelId))
                //            {
                //                message.consumeHeroes.Add(heroData.ModelId, costHeroNum);
                //            }
                //            else
                //            {
                //                message.consumeHeroes[heroData.ModelId] = message.consumeHeroes[heroData.ModelId] + costHeroNum;
                //            }
                //        }
                //    }
                //}

                MessageMgr.Send(message);
                //var sendMsg = new Action<int, HeroLvlUpReq>((toLevel, msg) =>
                //{
                //    if (toLevel < 0)
                //    {
                //        PopupManager.I.ShowWidget<UIHeroRetainCard>(new UIHeroRetainCardData()
                //        {
                //            heroUpLvMsg = msg,
                //            showCheck = false
                //        });
                //    }
                //    else
                //    {
                //        if (GameData.I.MaskData.SecondConfirm.TryGetValue((int)UIMsgBoxInitData.OpenSecondEnum.HeroRetainCard, out var retainCard) && retainCard)
                //        {
                //            MessageMgr.Send(msg);
                //        }
                //        else
                //        {
                //            PopupManager.I.ShowWidget<UIHeroRetainCard>(new UIHeroRetainCardData()
                //            {
                //                heroUpLvMsg = msg,
                //                showCheck = true
                //            });
                //        }
                //    }
                //});

                //sendMsg(1, message);
            }
            else if (mHeroData.Exp >= mHeroData.HeroExp.Exp
                && mHeroData.Level < GameData.I.UnlockData.MaxHeroLevel)
            {
                //��ǰ�ȼ�������������������Կ�����ôֱ������
                var message = new HeroLvlUpReq();
                message.heroId = mHeroData.HeroId;
                MessageMgr.Send(message);
            }
            else if (mHeroData.Level == GameData.I.UnlockData.MaxHeroLevel)
            {
                FloatTips.I.FloatMsg(LocalizationMgr.Get("Hero_Errotext_2"));
            }
        }

        private long GetHasExp()
        {
           return PlayerAssetsMgr.I.GetVMCount(Config.ConfigID.vm_heroExp);//
            //long curExp =  0;
            //var costHeros = HeroGameData.I.GetExpHeros();

            //for (int i = 0; i < costHeros.Count; i++)
            //{
            //    var heroData = costHeros[i];

            //    if (HeroGameData.I.mCultivateMap.TryGetValue(heroData.ModelId, out var CurHeroCount))
            //    {
            //        if (CurHeroCount <= 1)
            //        {
            //            continue;
            //        }

            //        var iiExp = HeroUtils.GetHeroExp(heroData);
            //        curExp = curExp + (iiExp * (CurHeroCount - 1));
            //    }
            //}

            //return curExp;
        }

        float spineClickCDTime = 0;

        private DG.Tweening.Sequence PlayTween(HeroUIData from, HeroUIData to, bool Bigger)
        {
            Vector3 pos0 = from.spineObj.transform.localPosition;
            Vector3 pos1 = from.SpineLocalPostion + to.spineParent.transform.localPosition;

            Vector3 scale = from.SpineLocalScale * to.spineParent.transform.localScale.x * (to.ShowPos == 0 ? 1f : 0.6f);

            var fromMaterial = from.spineObj.GetComponent<SkeletonGraphic>().material;
            float curValue = fromMaterial.GetFloat("_GrayPercent");

            float totalTime = 0.7f;

            var mainSeq = DG.Tweening.DOTween.Sequence();
            mainSeq.Append(from.spineObj.transform.DOLocalMove(pos1, totalTime).SetEase(Ease.InCubic));

            if (from.ShowPos * to.ShowPos == 2)
            {
                from.spineObj.transform.SetSiblingIndex(0);
                fromMaterial.renderQueue = 3000;
                mainSeq.Insert(0, from.spineObj.transform.DOScale(scale * 0.6f, totalTime * 0.7f));
                mainSeq.Insert(totalTime * 0.7f, from.spineObj.transform.DOScale(scale, totalTime * 0.3f));
            }
            else
            {
                fromMaterial.renderQueue = 3002 - from.ShowPos;
                mainSeq.Insert(0.2f, from.spineObj.transform.DOScale(scale, totalTime - 0.2f));
            }

            mainSeq.InsertCallback(totalTime * 0.7f, () =>
            {
                fromMaterial.renderQueue = 3000;
                from.spineObj.transform.SetSiblingIndex(3 - to.ShowPos);
            });


            mainSeq.Insert(0, DOTween.To(() => curValue
            , (v) =>
            {
                curValue = v;
                fromMaterial.SetFloat("_GrayPercent", curValue);
            }, Bigger ? 0 : 0.6f
            , totalTime));


            mainSeq.Insert(0.2f, fromMaterial.DOColor(Bigger ? new Color32(255, 255, 255, 255) : new Color32(156, 156, 156, 255), totalTime - 0.2f));


            return mainSeq;
        }

        protected internal override void OnOpenStart()
        {
            curTabTag = "1";

            
            mShowData = Data as UIHeroShowViewData;

            if (mShowData == null)
                return;

            mTroopShowPower = mShowData.showPower;

            _tabGroup.SetDefaultTabOn(_tab1);

            DisPlayHeroInfo();

            EventMgr.RegisterEvent(TEventType.HeroTabClick, OnHeroTabClick, this);
            MessageMgr.RegisterMsg<HeroStepsUpgradeAck>(this, OnHeroStepsUpgradeAck);
            MessageMgr.RegisterMsg<HeroLvlUpAck>(this, OnHeroLvlUpAck);

            EventMgr.RegisterEvent(TEventType.ExChangeHeroEnd, (a) =>
            {
                if (mHeroData != null)
                {
                    mHeroData = HeroGameData.I.GetHeroById(mHeroData.HeroId);

                    RefreshHeroData();

                    uiHeroTabGroup.InitData(mHeroData);
                }
            }, this);

            EventMgr.RegisterEvent(TEventType.RefreshHeroData, RefreshHeroByEvent, this);

            curCount = 0;
            if (mShowData.mainHeroData != null)
                curCount++;

            if (mShowData.leftHeroData != null)
                curCount++;

            if (mShowData.rightHeroData != null)
                curCount++;

            ResetDepPos();
            mHeroData = GetHero(curShowDepPos[0]);

            RefreshHeroData();
            uiHeroTabGroup.InitData(mHeroData);
            centerHero.PlayAudio();
        }

        private async UniTaskVoid RefreshHeroByEvent(object[] objs)
        {
            var preHero = mHeroData;

            mHeroData = HeroGameData.I.GetHeroById(mHeroData.HeroId);

            if (preHero != null)
            {
                var preLv= (await preHero.IntimacyLevel()).Item1;
                var curLv = (await mHeroData.IntimacyLevel()).Item1;
                if (preLv < curLv)
                {

                    bool unlockVideo = false;
                    if (mHeroData.HeroCfg.UnlockVideo.Count > 0)
                    {
                        var videoLenth = mHeroData.HeroCfg.UnlockVideo.Count / 2;
                         
                        List<Vector3> _flyTargets = new List<Vector3>();


                        for (int i = 0; i < videoLenth; i++)
                        {
                            if (mHeroData.HeroCfg.UnlockVideo[i * 2] == curLv.ToString())
                            {
                                if (HeroGameData.I.UnlockSkins.ContainsKey(mHeroData.HeroCfg.Id))
                                {
                                    HeroGameData.I.UnlockSkins[mHeroData.HeroCfg.Id].Add(mHeroData.HeroCfg.UnlockVideo[i * 2 + 1]);
                                }
                                else
                                {
                                    HeroGameData.I.UnlockSkins.Add(mHeroData.HeroCfg.Id, new List<string>() { mHeroData.HeroCfg.UnlockVideo[i * 2 + 1] });
                                }

                                //switch (i)
                                //{
                                //    case 0:
                                //    default:
                                //        //_flyTargets.Add(GameObject.transform.TransformPoint(btnVideo1.transform.position));
                                        
                                //        break;
                                //    case 1:
                                //        _flyTargets.Add(GameObject.transform.TransformPoint(btnVideo2.transform.position));
                                //        if (HeroGameData.I.UnlockSkins.ContainsKey(mHeroData.HeroCfg.Id))
                                //        {
                                //            HeroGameData.I.UnlockSkins[mHeroData.HeroCfg.Id].Add(btnVideo2.videoName);
                                //        }
                                //        else
                                //        {
                                //            HeroGameData.I.UnlockSkins.Add(mHeroData.HeroCfg.Id, new List<string>() { btnVideo2.videoName });
                                //        }
                                //        break;
                                //    case 2:
                                //        _flyTargets.Add(GameObject.transform.TransformPoint(btnVideo3.transform.position));
                                //        if (HeroGameData.I.UnlockSkins.ContainsKey(mHeroData.HeroCfg.Id))
                                //        {
                                //            HeroGameData.I.UnlockSkins[mHeroData.HeroCfg.Id].Add(btnVideo3.videoName);
                                //        }
                                //        else
                                //        {
                                //            HeroGameData.I.UnlockSkins.Add(mHeroData.HeroCfg.Id, new List<string>() { btnVideo3.videoName });
                                //        }
                                //        break;
                                //    case 3:
                                //        _flyTargets.Add(GameObject.transform.TransformPoint(btnVideo4.transform.position));
                                //        if (HeroGameData.I.UnlockSkins.ContainsKey(mHeroData.HeroCfg.Id))
                                //        {
                                //            HeroGameData.I.UnlockSkins[mHeroData.HeroCfg.Id].Add(btnVideo4.videoName);
                                //        }
                                //        else
                                //        {
                                //            HeroGameData.I.UnlockSkins.Add(mHeroData.HeroCfg.Id, new List<string>() { btnVideo4.videoName });
                                //        }
                                //        break;
                                //    case 4:
                                //        _flyTargets.Add(GameObject.transform.TransformPoint(btnVideo5.transform.position));
                                //        if (HeroGameData.I.UnlockSkins.ContainsKey(mHeroData.HeroCfg.Id))
                                //        {
                                //            HeroGameData.I.UnlockSkins[mHeroData.HeroCfg.Id].Add(btnVideo5.videoName);
                                //        }
                                //        else
                                //        {
                                //            HeroGameData.I.UnlockSkins.Add(mHeroData.HeroCfg.Id, new List<string>() { btnVideo5.videoName });
                                //        }
                                //        break; 
                                //}

                                unlockVideo = true;

                                PopupManager.I.ShowDialog<HeroUnlockSkillPanel>(new HeroUnlockSkillPanelData()
                                {
                                    HeroData = mHeroData,
                                    videoIconIndex = i,
                                    CloseCallBack = () =>
                                    {
                                        CheckHeroCreateItem(2);
                                    },
                                    flyTargets = _flyTargets
                                });

                                break;
                            }
                        }
                    }
                   
                    if(!unlockVideo)
                    {
                        CheckHeroCreateItem(2);
                    }


                }
            }


            RefreshHeroData();
        }


        protected internal override void OnCloseStart()
        {
            base.OnCloseStart();

            
            exChangeTimer?.Stop();

            EventMgr.FireEvent(TEventType.RefreshHeroData);
            EventMgr.FireEvent(TEventType.MainBuildBubbleRefresh);

            EventMgr.UnregisterEvent(this);
            MessageMgr.UnregisterMsg(this);
        }

        

        private void OnHeroLvlUpAck(HeroLvlUpAck obj)
        {
            if (obj.errCode == ErrCode.ErrCodeSuccess)
            {
              
                mHeroData = HeroGameData.I.GetHeroByCfgId(obj.hero.cfgId);

                CheckHeroCreateItem(0);

                RefreshHeroData();
                uiHeroTabGroup.InitData(mHeroData);

                //if (!levelUpEffect.isPlaying)
                {
                    levelUpEffect.Play();
                }

                if (obj.hero.level >= 10 && PlayerPrefs.GetInt($"{LPlayer.I.PlayerID}_herouplv_10", 0) == 0)
                {
                    PlayerPrefs.SetInt($"{LPlayer.I.PlayerID}_herouplv_10", 1);
                    PlayerPrefs.Save();

                    var gameEvent = new K3.GameEvent();
                    gameEvent.EventKey = $"hero_lv10";
                    K3.K3GameEvent.I.BiLog(gameEvent);
                }
                else if (obj.hero.level >= 20 && PlayerPrefs.GetInt($"{LPlayer.I.PlayerID}_herouplv_20", 0) == 0)
                {
                    PlayerPrefs.SetInt($"{LPlayer.I.PlayerID}_herouplv_20", 1);
                    PlayerPrefs.Save();

                    var gameEvent = new K3.GameEvent();
                    gameEvent.EventKey = $"hero_lv20";
                    K3.K3GameEvent.I.BiLog(gameEvent);
                }
            }
        }

        private void OnHeroStepsUpgradeAck(HeroStepsUpgradeAck obj)
        {
            if (obj.err == ErrCode.ErrCodeSuccess)
            {
                var hero = new Game.Data.HeroData(obj.hero.heroId, obj.hero.cfgId, obj.hero.star, obj.hero.level, obj.hero.exp, obj.hero.steps, obj.hero.status, obj.hero);
                mHeroData = hero;

                
                HeroGameData.I.GetPower(true);
                HeroGameData.I.ChangeHero(hero);
                HeroGameData.I.ChangeHeroSkill(hero);
                foreach (var item in obj.heroRemain)
                {
                    HeroGameData.I.mCultivateMap[item.Key] = item.Value;
                }
                RefreshHeroData();
                uiHeroTabGroup.InitData(mHeroData);

                var unlockSkills= mHeroData.UnlockCurStar();
                if (unlockSkills?.Count > 0)
                {
                    
                     
                    List<Vector3> _flyTargets=new List<Vector3>();
                    foreach (var item in unlockSkills)
                    {
                        foreach (var ii in mergeSkills)
                        {
                            if (ii.mHeroSkill!=null && ii.mHeroSkill.id == item.id && ii.mHeroSkill.mType == item.mType)
                            {
                                _flyTargets.Add(GameObject.transform.TransformPoint(ii.transform.position));
                            }
                        }

                        foreach (var ii in battleSkills)
                        {
                            if (ii.mHeroSkill != null && ii.mHeroSkill.id == item.id && ii.mHeroSkill.mType == item.mType)
                            {
                                _flyTargets.Add(GameObject.transform.TransformPoint(ii.transform.position));
                            }
                        }
                    }

                    PopupManager.I.ShowDialog<HeroUnlockSkillPanel>(new HeroUnlockSkillPanelData()
                    {
                        HeroData = mHeroData,
                        skillDatas = unlockSkills,
                        CloseCallBack = () =>
                        {
                            CheckHeroCreateItem(1);
                        },
                        flyTargets= _flyTargets
                    });
                }
                else
                {
                    CheckHeroCreateItem(1);
                }
            }
        }

        private void OnHeroTabClick(object[] obj)
        {
            if (obj != null)
            {
                var heroData = obj[0] as Game.Data.HeroData;
                mHeroData = HeroGameData.I.GetHeroByCfgId(heroData.HeroCfg.Id);
                RefreshHeroData();
                centerHero.PlayAudio();
                //var popData = new UIHeroShowViewData()
                //{
                //    mainHeroData = heroData,
                //    showGetBtn = false,
                //};
                //PopupManager.I.ShowPanel<UIHeroShowView>(popData);
            }
        }
        protected override void OnDispose()
        {
            base.OnDispose();
            EventMgr.UnregisterEvent(TEventType.HeroTabClick);
            MessageMgr.UnregisterMsg<HeroStepsUpgradeAck>(this);
            MessageMgr.UnregisterMsg<HeroLvlUpAck>(this);
        }
        protected internal override void OnCloseComplete()
        {
            base.OnCloseComplete();


            centerHero?.ResetSpine();
            leftHero?.ResetSpine();
            rightHero?.ResetSpine();
        }

        private void ResetDepPos()
        {
            curShowDepPos[0] = 0;
            curShowDepPos[1] = 1;
            curShowDepPos[2] = 2;
        }

        private int[] curShowDepPos = new int[3];

        [PopupEvent(TEventType.UIHeroShowViewDeputyShowPos)]
        private void OnUIHeroDeputyShowPos(object[] objs)
        {
            if (objs.Length > 0)
            {
                curShowDepPos = objs[0] as int[];
                RefreshHeroData();
            }
        }

        private Game.Data.HeroData GetHero(int pos)
        {
            switch (pos)
            {
                case 0:
                    return mShowData.mainHeroData;
                case 1:
                    return mShowData.leftHeroData;
                case 2:
                    return mShowData.rightHeroData;
            }

            return null;
        }

        private bool canUpdateLevel;
        private long maxLvExp;

        private NTimer.Timer exChangeTimer;

        private void RefreshHeroData()
        {
            //isPlay = false;
            //mHeroData = GetHero(curShowDepPos[0]);
            centerHero.InitData(mHeroData, curShowDepPos[0], true);
            leftHero.InitData(GetHero(curShowDepPos[1]), curShowDepPos[1]);
            rightHero.InitData(GetHero(curShowDepPos[2]), curShowDepPos[2]);
            mask.SetActive(false);


            //btnGoodwill.SetActive(false);//&& mHeroData != null && Cfg.C.CHeroGoodwill.I(mHeroData.HeroCfg.Id) != null

            // levelUpBtnRed.SetActive(mHeroData.CanUpLevelUp(mHeroData));
            //levelUpBtnRed.SetActive(false);


            for (int i = HeroUIData.AllRemoveSpineObj.Count - 1; i >= 0; i--)
            {
                if (HeroUIData.AllRemoveSpineObj[i] != null
                && centerHero.spineObj != HeroUIData.AllRemoveSpineObj[i]
                && leftHero.spineObj != HeroUIData.AllRemoveSpineObj[i]
                && rightHero.spineObj != HeroUIData.AllRemoveSpineObj[i])
                {
                    var spinObj = HeroUIData.AllRemoveSpineObj[i];

                    var spineGraphic = spinObj.GetComponent<SkeletonGraphic>();
                    spineGraphic.material.SetFloat("_GrayPercent", 0);
                    spineGraphic.material.color = new Color32(255, 255, 255, 255);
                    spineGraphic.material.renderQueue = 3000;

                    HeroUIData.AllRemoveSpineObj.Remove(spinObj);
                    ResourceMgr.ReleaseInstance(spinObj);
                }
            }

            btnExChangeHero.SetActive(UnlockMgr.I.CheckUnlock(UnlockFuncEnum.ExChangeHero));

            exChangeHeroCd.SetActive(HeroGameData.I.ExChangeCDTime > GameTime.Time);
            exChangeHeroCDText.InitEndTime(HeroGameData.I.ExChangeCDTime);

            exChangeTimer?.Stop();

            if (UnlockMgr.I.CheckUnlock(UnlockFuncEnum.ExChangeHero) && HeroGameData.I.ExChangeCDTime > GameTime.Time)
            {
                NTimer.TickNoPool(ref exChangeTimer, NTimer.INF, 1, () =>
                {
                    if (PopupManager.I.FindPopup<MyHeroShowView>() != null)
                    {
                        if (HeroGameData.I.ExChangeCDTime > GameTime.Time)
                        {
                            exChangeHeroCd.SetActive(true);
                        }
                        else
                        {
                            exChangeHeroCd.SetActive(false);
                            exChangeTimer?.Stop();
                        }
                    }
                    else
                    {
                        exChangeTimer?.Stop();
                    }
                });
            }

            RefreshStar();
            RefreshSkill(true);

            RefreshHeroGood().Forget();

            UITools.SetImageBySpriteName(attributeImage, UITools.GetAttributeDisplayKey(mHeroData.HeroCfg.SoldiersType));
            UITools.SetImageBySpriteName(ssrImage, UITools.GetSSRQuality(mHeroData.HeroCfg.HeroType));
            nameText.text = LocalizationMgr.Get(mHeroData.HeroCfg.Name);
            
            UITools.SetDynamicRawImage(m_heroBg, mHeroData.HeroCfg.Image);
            List<string> createType = new List<string>();
            var boxDic = Cfg.C.CCsBox.RawDict();
            foreach (var item in boxDic)
            {
                if (item.Value.Unlock == mHeroData.HeroCfg.HeroMergeType)
                {
                    createType.AddRange(item.Value.CreateType);
                }
            }

            // ������߱���
            for (int i = 0; i < createType.Count; i++)
            {
                int type = int.Parse(createType[i]);
                int index = i + 1;
                var config = Cfg.C.CK3Item.RawList().Find(x => x.Code == type && x.Level == mHeroData.MergeItemLevel);

                GameObject obj = UIHelper.GetChild(itemBg, $"Item_{index}");
                // UITools.SetQualityIcon( UIHelper.GetComponent<TFWImage>(obj, "Bg"), config.Quality);
                UITools.SetImageBySpriteName(UIHelper.GetComponent<TFWImage>(obj, "Icon"), config.Icon);
                UIHelper.GetComponent<TFWText>(obj, "Text").text = $"Lv.{mHeroData.MergeItemLevel}";

                if (i > 0)
                {
                    obj.SetActive(UnlockMgr.I.CheckUnlock(UnlockFuncEnum.MergeBoxHighItem));
                }
            }


            //var _type = (HeroType)mHeroData.HeroCfg.HeroType;
            //var arr = nameText.GetComponent<Gradient2>().EffectGradient.colorKeys;
            //arr[0].color = HeroUtils.GetColorByType(_type);
            //arr[1].color = HeroUtils.GetColorByTwoType(_type);
            //nameText.GetComponent<Gradient2>().EffectGradient.colorKeys = arr;
            powerText.text = mTroopShowPower == null ? UIStringUtils.FormatIntegerByLanguage(mHeroData.Power()) : UIStringUtils.FormatIntegerByLanguage(mTroopShowPower.total); ;
            troopValueText.text = UIStringUtils.FormatIntegerByLanguage(mHeroData.HeroAttribute.LevelUpSoldierCapacity);

            ageValue.text = mHeroData.HeroCfg.Info[1];
            hrightValue.text = mHeroData.HeroCfg.Info[2];
            bustValue.text = mHeroData.HeroCfg.Info[3];

            int toLevel = Mathf.Min(mHeroData.Level + 1, GameData.I.UnlockData.MaxHeroLevel);
            maxLvExp = HeroUtils.GetExpToLevel(mHeroData, toLevel);

            canUpdateLevel = false;
            //if (maxLvExp > 0)
            //{
            long curExp =  PlayerAssetsMgr.I.GetVMCount(Config.ConfigID.vm_heroExp);  
            //var costHeros = HeroGameData.I.GetExpHeros();

            //for (int i = 0; i < costHeros.Count; i++)
            //{
            //    var heroData = costHeros[i];

            //    if (HeroGameData.I.mCultivateMap.TryGetValue(heroData.ModelId, out var CurHeroCount))
            //    {
            //        if (CurHeroCount <= 1)
            //        {
            //            continue;
            //        }

            //        var iiExp = HeroUtils.GetHeroExp(heroData);
            //        curExp = curExp + (iiExp * (CurHeroCount - 1));
            //    }
            //}

            if (curExp >= mHeroData.HeroExp.Exp)
            {
                canUpdateLevel = true;
                expText.text = $"<color=#66ef69>{UIStringUtils.FormatIntUnitByLanguage(curExp, 2)}</color>/{UIStringUtils.FormatIntUnitByLanguage(mHeroData.HeroExp.Exp, 2)}";
            }
            else
            {
                expText.text = $"<color=#ff0000>{UIStringUtils.FormatIntUnitByLanguage(curExp, 2)}</color>/{UIStringUtils.FormatIntUnitByLanguage(mHeroData.HeroExp.Exp, 2)}";
            }
            //}
            //else
            //{
            //    expText.text = $"{mHeroData.Exp}/{mHeroData.HeroExp.Exp}";
            //}

            lvText.text = "Atlas_Level".ToLocal(mHeroData.Level);

            levelUpBtn.SetActive(mHeroData.Level < GameData.I.UnlockData.MaxHeroLevel);


            //lupCurrExpSlider.value = (mHeroData.Exp / (float)mHeroData.HeroExp.Exp);
            //lupCurrExpSlider_new.value = lupCurrExpSlider.value;

            //expSliderText_new.text = string.Format("{0}/{1}", mHeroData.Exp, mHeroData.HeroExp.Exp);


            //var fitters = powerRect.GetComponentsInChildren<ContentSizeFitter>();
            //for (int i = fitters.Length - 1; i >= 0; i--)
            //{
            //    LayoutRebuilder.ForceRebuildLayoutImmediate(fitters[i].transform as RectTransform);
            //}


            //if (curTabTag == "3")
            //{
            //    if (mHeroData.Level < MetaConfig.Hero_Intimacy_unlock_level)
            //    {
            //        FloatTips.I.FloatMsg("Hero_intimacy_desc_03".ToLocal(MetaConfig.Hero_Intimacy_unlock_level));
            //        _tabGroup.TurnTabOnByTag("1");
            //        curTabTag = "1";

            //        DisPlayHeroInfo();
            //        RefreshTabRed().Forget();
            //        return;
            //    }
            //}

            RefreshTabRed().Forget();
        }

        /// <summary>
        /// ˢ���Ǽ�
        /// </summary>
        /// <param name="star">Ӣ��ӵ�е��Ǽ�</param>
        /// <param name="heroMaxStar">Ӣ�������Ǽ�</param>
        private void RefreshStar()
        {
            starGrid_new.Clear();
            if (mHeroData != null)
            {
                for (int i = 0; i < mHeroData.HeroCfg.StarRating; i++)
                {
                    var star = starGrid_new.AddItem<Transform>();
                    if (mHeroData.Star > i)
                    {
                        UIHelper.SetStarActive(star, 10);
                    }
                    else if (mHeroData.Star == i)
                    {
                        UIHelper.SetStarActive(star, mHeroData.StarStep - 1);
                    }
                    else
                    {
                        UIHelper.SetStarActive(star, -1);
                    }
                }
            }
        }

        #region Ӣ���������
        //[PopupField("Root/HeroStar/InfoBg/MergeSkillGrid")]
        //private GameObject mergeSkillParent;
        //[PopupField("Root/HeroStar/InfoBg/BattleSkillGrid")]
        //private GameObject battleSkillParent;
        [PopupField("Root/HeroStar/StarUpBtn")]
        private GameObject btnStarUp;
        [PopupField("Root/HeroStar/StarUpBtn/Icon")]
        private TFWImage btnStarIcon;
        [PopupField("Root/HeroStar/StarUpBtn/expText")] 
        private TFWText starCostText;
        [PopupField("Root/HeroStar/skillDesc")]
        private TFWText selectSKillDesc;
        [PopupField("Root/HeroStar/unlockDesc")]
        private TFWText selectSKillunlockDesc;

        private List<UIHeroUpStarDialogSkillItem> mergeSkills, battleSkills;
        private async void RefreshSkill(bool initSelct = false)
        {
            mHeroData.CanUpStarUp(out var curCard, out var needCard);
            if (curCard >= needCard)
            {
                starCostText.text = $"<color=#00ff00>{curCard}</color>/{needCard}";
            }
            else
            {
                starCostText.text = $"<color=#ff0000>{curCard}</color>/{needCard}";
            }

            UITools.SetImageBySpriteName(btnStarIcon, mHeroData.HeroCfg.Icon1);

            if (initSelct)
                selectSkill = null;

            if (mergeSkills == null)
            {
                mergeSkills = new List<UIHeroUpStarDialogSkillItem>();
                //mergeSkills.AddRange(mergeSkillParent.GetComponentsInChildren<UIHeroUpStarDialogSkillItem>());
            }

            if (battleSkills == null)
            {
                battleSkills = new List<UIHeroUpStarDialogSkillItem>();
                //battleSkills.AddRange(battleSkillParent.GetComponentsInChildren<UIHeroUpStarDialogSkillItem>());
            }


            if (mHeroData != null)
            {
                var mergeskillDatas = mHeroData.HeroMergeSkillDatas;
                for (int i = 0; i < mergeSkills.Count; i++)
                {
                    if (mergeskillDatas.Count > i)
                    {
                        if (initSelct && selectSkill == null)
                            selectSkill = mergeskillDatas[i];

                        mergeSkills[i].SetSkill(mHeroData, mergeskillDatas[i], selectSkill, mHeroData.Star > mHeroData.Star);
                    }
                    else
                    {
                        mergeSkills[i].gameObject.SetActive(false);
                    }
                }

                var battleskillDatas = mHeroData.HeroSkillDatas.Values.ToList();
                for (int i = 0; i < battleSkills.Count; i++)
                {
                    if (battleskillDatas.Count > i)
                    {
                        if (initSelct && selectSkill == null)
                            selectSkill = battleskillDatas[i];

                        battleSkills[i].SetSkill(mHeroData, battleskillDatas[i], selectSkill, mHeroData.Star > mHeroData.Star);
                    }
                    else
                    {
                        battleSkills[i].gameObject.SetActive(false);
                    }
                }
            }

            if (selectSkill != null)
            {
                switch (selectSkill.mType)
                {
                    case Game.Data.HeroData.HeroSkillData.SkillType.HeroPassive:
                        //selSkillTitle.text = Cfg.C.CD2HeroPassiveSkill.I(selectSkill.id)?.Name.ToLocal();
                        var skillCfg = Cfg.C.CD2HeroPassiveSkill.I(selectSkill.id);
                        if (skillCfg != null)
                        {
                            selectSKillDesc.text = await skillCfg.GetSkillDesc();
                        }

                        break;
                    case Game.Data.HeroData.HeroSkillData.SkillType.MergePassive:
                        //selSkillTitle.text = Cfg.C.CCSSkillForPassive.I(selectSkill.id)?.Name.ToLocal();
                        selectSKillDesc.text = GetSkillLocalizationMgr.I.GetForPassive(Cfg.C.CCSSkillForPassive.I(selectSkill.id));
                        break;
                    //case UIHeroUpStarDialogSkillItem.SkillType.MergeAssist:
                    //    selSkillTitle.text = Cfg.C.CCSSkillForAssisted.I(selectSkill.id)?.Name.ToLocal();
                    //    selSkillDesc.text = GetSkillLocalizationMgr.I.GetForAssisted(Cfg.C.CCSSkillForAssisted.I(selectSkill.id));
                    //    break;
                    default:
                        break;
                }

                if (mHeroData.Star < selectSkill.unlockStar)
                {
                    selectSKillunlockDesc.gameObject.SetActive(true);
                    selectSKillunlockDesc.text = "Hero_Skill_Desc_Unlock".ToLocal(selectSkill.unlockStar);
                }
                else
                {
                    selectSKillunlockDesc.gameObject.SetActive(false);
                }
            }
        }


        private Game.Data.HeroData.HeroSkillData selectSkill;

        [PopupEvent(TEventType.K3UIHero_Skill_Select)]
        private void SelSkill(object[] objs)
        {
            if (objs.Length > 0)
            {
                selectSkill = objs[0] as Game.Data.HeroData.HeroSkillData;

                RefreshSkill();
            }
        }
        #endregion

        //[PopupField("Root/HeroGood/itemGrid")]
        //private UIGrid heroGoodGrid;

        //[PopupField("Root/HeroInfo/node/HeroGoodInfo")]
        //private HeroGoodHeartInfo heroGoodHeartInfo;

        //[PopupField("Root/HeroGoodInfo")]
        //private HeroGoodHeartInfo HeroGoodInfo;

       
        private async UniTask RefreshHeroGood()
        {
             
            ////heroGoodHeartInfo.SetLevel(mHeroData);
            ////HeroGoodInfo.SetLevel(mHeroData);

            ////heroGoodGrid.Clear();
            ////int index = 1;
            ////foreach (var item in heroGoodItemList)
            ////{
            ////    var heroItem = heroGoodGrid.AddItem<HeroViewGoodItem>();
            ////    heroItem.SetItem(mHeroData,item, selectItem);
            ////    heroItem.gameObject.name = $"item{index}";
            ////    index++;
            ////}

            //btnsSkinObj.SetActive(true);
            //var rewardHeros=await Cfg.C.CChapterReward.GetEnumerableAsync();
            ////foreach (var item in rewardHeros)
            ////{
            ////    if (item.Hero == mHeroData?.HeroCfg.Id)
            ////    {
            ////        btnsSkinObj.SetActive(true);
            ////    }
            ////}



            //if (rewardHeros.Count(a => a.Hero == mHeroData?.HeroCfg.Id) > 0)
            //{
            //    btnSkin1.SetData(mHeroData, "01", "", true);
            //    btnSkin2.SetData(mHeroData, "02", "", true);
            //    btnSkin3.SetData(mHeroData, "03", "", true);
            //    btnSkin4.SetData(mHeroData, "04", "", true);
            //    btnSkin5.SetData(mHeroData, "05", "", true);
            //}
            //else
            //{
            //    btnSkin1.SetData(mHeroData, "01", "", true);
            //    btnSkin2.SetData(mHeroData, "", "", true);
            //    btnSkin3.SetData(mHeroData, "", "", true);
            //    btnSkin4.SetData(mHeroData, "", "", true);
            //    btnSkin5.SetData(mHeroData, "", "", true);
            //}

            //if (mHeroData.HeroCfg.UnlockVideo.Count > 0)
            //{
            //    btnsVideoObj.SetActive(true);
            //    var videoLenth = mHeroData.HeroCfg.UnlockVideo.Count / 2;
            //    if (mHeroData.HeroCfg.UnlockVideo.Count >= 2)
            //    {
            //        /*btnVideo1.SetData(mHeroData, "", mHeroData.HeroCfg.UnlockVideo[1], (await mHeroData.IntimacyLevel()).Item1 >= int.Parse(mHeroData.HeroCfg.UnlockVideo[0])
            //            , "Hero_intimacy_desc_06".ToLocal(mHeroData.HeroCfg.UnlockVideo[0]));*/
            //        var unlockStr = mHeroData.HeroCfg.UnlockVideo[0];
            //        btnVideo1.SetData(mHeroData, "", mHeroData.HeroCfg.UnlockVideo[1],
            //            (await mHeroData.IntimacyLevel()).Item1 >= int.Parse(mHeroData.HeroCfg.UnlockVideo[0])
            //            , LocalizationMgr.Format("Hero_intimacy_desc_06", FindStrFormat(unlockStr)));
            //    }
            //    else
            //    {
            //        btnVideo1.SetData(mHeroData, "", "", false);
            //    }

            //    if (mHeroData.HeroCfg.UnlockVideo.Count >= 4)
            //    {
            //        btnVideo2.SetData(mHeroData, "", mHeroData.HeroCfg.UnlockVideo[3], (await mHeroData.IntimacyLevel()).Item1 >= int.Parse(mHeroData.HeroCfg.UnlockVideo[2])
            //            , LocalizationMgr.Format("Hero_intimacy_desc_06", FindStrFormat(mHeroData.HeroCfg.UnlockVideo[2])));
            //    }
            //    else
            //    {
            //        btnVideo2.SetData(mHeroData, "", "", false);
            //    }

            //    if (mHeroData.HeroCfg.UnlockVideo.Count >= 6)
            //    {
            //        btnVideo3.SetData(mHeroData, "", mHeroData.HeroCfg.UnlockVideo[5], (await mHeroData.IntimacyLevel()).Item1 >= int.Parse(mHeroData.HeroCfg.UnlockVideo[4])
            //            , LocalizationMgr.Format("Hero_intimacy_desc_06", FindStrFormat(mHeroData.HeroCfg.UnlockVideo[4])));
            //    }
            //    else
            //    {
            //        btnVideo3.SetData(mHeroData, "", "", false);
            //    }

            //    if (mHeroData.HeroCfg.UnlockVideo.Count >= 8)
            //    {
            //        btnVideo4.SetData(mHeroData, "", mHeroData.HeroCfg.UnlockVideo[7], (await mHeroData.IntimacyLevel()).Item1 >= int.Parse(mHeroData.HeroCfg.UnlockVideo[6])
            //            , LocalizationMgr.Format("Hero_intimacy_desc_06", FindStrFormat(mHeroData.HeroCfg.UnlockVideo[6])));
            //    }
            //    else
            //    {
            //        btnVideo4.SetData(mHeroData, "", "", false);
            //    }

            //    if (mHeroData.HeroCfg.UnlockVideo.Count >= 10)
            //    {
            //        btnVideo5.SetData(mHeroData, "", mHeroData.HeroCfg.UnlockVideo[9], (await mHeroData.IntimacyLevel()).Item1 >= int.Parse(mHeroData.HeroCfg.UnlockVideo[8])
            //            , LocalizationMgr.Format("Hero_intimacy_desc_06", FindStrFormat(mHeroData.HeroCfg.UnlockVideo[8])));
            //    }
            //    else
            //    {
            //        btnVideo5.SetData(mHeroData, "", "", false);
            //    }

            //    //switch (videoLenth)
            //    //{
            //    //    case 1:
            //    //        SkinHeroObjMap.Add(mHeroData.HeroCfg.UnlockVideo[1], btnVideo1);
            //    //        //SkinHeroObjMap.Add(string.Empty, btnVideo2);
            //    //        //SkinHeroObjMap.Add(string.Empty, btnVideo3);
            //    //        //SkinHeroObjMap.Add(string.Empty, btnVideo4);
            //    //        //SkinHeroObjMap.Add(string.Empty, btnVideo5);

            //    //        UIBase.AddRemoveListener(EventTriggerType.Click,btnVideo1, (x, y) =>
            //    //        {
            //    //            VideoBG.PlayVideo(mHeroData.HeroCfg.UnlockVideo[1]);
            //    //        });

            //    //        break;
            //    //    case 2:
            //    //        SkinHeroObjMap.Add(mHeroData.HeroCfg.UnlockVideo[1], btnVideo1);
            //    //        SkinHeroObjMap.Add(mHeroData.HeroCfg.UnlockVideo[3], btnVideo2);
            //    //        //SkinHeroObjMap.Add(string.Empty, btnVideo3);
            //    //        //SkinHeroObjMap.Add(string.Empty, btnVideo4);
            //    //        //SkinHeroObjMap.Add(string.Empty, btnVideo5);

            //    //        UIBase.AddRemoveListener(EventTriggerType.Click, btnVideo1, (x, y) =>
            //    //        {
            //    //            VideoBG.PlayVideo(mHeroData.HeroCfg.UnlockVideo[1]);
            //    //        });
            //    //        break;
            //    //    case 3:
            //    //        SkinHeroObjMap.Add(mHeroData.HeroCfg.UnlockVideo[1], btnVideo1);
            //    //        SkinHeroObjMap.Add(mHeroData.HeroCfg.UnlockVideo[3], btnVideo2);
            //    //        SkinHeroObjMap.Add(mHeroData.HeroCfg.UnlockVideo[5], btnVideo3);
            //    //        //SkinHeroObjMap.Add(string.Empty, btnVideo4);
            //    //        //SkinHeroObjMap.Add(string.Empty, btnVideo5);

            //    //        UIBase.AddRemoveListener(EventTriggerType.Click, btnVideo1, (x, y) =>
            //    //        {
            //    //            VideoBG.PlayVideo(mHeroData.HeroCfg.UnlockVideo[1]);
            //    //        });
            //    //        break;
            //    //    case 4:
            //    //        SkinHeroObjMap.Add(mHeroData.HeroCfg.UnlockVideo[1], btnVideo1);
            //    //        SkinHeroObjMap.Add(mHeroData.HeroCfg.UnlockVideo[3], btnVideo2);
            //    //        SkinHeroObjMap.Add(mHeroData.HeroCfg.UnlockVideo[5], btnVideo3);
            //    //        SkinHeroObjMap.Add(mHeroData.HeroCfg.UnlockVideo[7], btnVideo4);
            //    //        //SkinHeroObjMap.Add(string.Empty, btnVideo5);

            //    //        UIBase.AddRemoveListener(EventTriggerType.Click, btnVideo1, (x, y) =>
            //    //        {
            //    //            VideoBG.PlayVideo(mHeroData.HeroCfg.UnlockVideo[1]);
            //    //        });
            //    //        break;
            //    //    case 5:
            //    //        SkinHeroObjMap.Add(mHeroData.HeroCfg.UnlockVideo[1], btnVideo1);
            //    //        SkinHeroObjMap.Add(mHeroData.HeroCfg.UnlockVideo[3], btnVideo2);
            //    //        SkinHeroObjMap.Add(mHeroData.HeroCfg.UnlockVideo[5], btnVideo3);
            //    //        SkinHeroObjMap.Add(mHeroData.HeroCfg.UnlockVideo[7], btnVideo4);
            //    //        SkinHeroObjMap.Add(mHeroData.HeroCfg.UnlockVideo[9], btnVideo5);

            //    //        UIBase.AddRemoveListener(EventTriggerType.Click, btnVideo1, (x, y) =>
            //    //        {
            //    //            VideoBG.PlayVideo(mHeroData.HeroCfg.UnlockVideo[1]);
            //    //        });
            //    //        break;
            //    //    default:
            //    //        SkinHeroObjMap.Add(string.Empty, btnVideo1);
            //    //        SkinHeroObjMap.Add(string.Empty, btnVideo2);
            //    //        SkinHeroObjMap.Add(string.Empty, btnVideo3);
            //    //        SkinHeroObjMap.Add(string.Empty, btnVideo4);
            //    //        SkinHeroObjMap.Add(string.Empty, btnVideo5);
            //    //        break;
            //    //}
            //}
            //else
            //{
            //    btnsVideoObj.SetActive(false);
            //}

            var curHeroVideoSkin = ChapterTaskMgr.I.GetHeroVedioSkin(mHeroData.HeroCfg.Id);
        
            await  VideoBG.PlayVideo(curHeroVideoSkin);

            if (string.IsNullOrEmpty(curHeroVideoSkin))
            {
                VideoBG.gameObject.SetActive(false);
                HeroSpineObj.SetActive(true);
            }
            else
            {
                VideoBG.gameObject.SetActive(true);
                HeroSpineObj.SetActive(false);
            }
        }

        #region Ӣ�����ܶ����


      


       
        
        //[PopupEvent(TEventType.HeroVedioSkin)]
        //private void PlaySkinVedio(object[] objs)
        //{
             
        //    var rootCanvas= rootObj.GetComponent<CanvasGroup>();
        //    DOTween.Kill(rootCanvas);

        //    rootObj.GetComponent<CanvasGroup>().alpha = 1;
        //    rootCanvas.DOFade(0, 1f).SetEase(Ease.Linear).OnComplete(() =>
        //    {
        //        rootObj.SetActive(false);
        //    }); 
        //}

        [PopupEvent(TEventType.HeroSkinSelect)]
        private void RefreshGoolSkin(object[] objs)
        {
            RefreshHeroGood().Forget();
            centerHero.InitData(mHeroData, curShowDepPos[0],true);
        }

        #endregion

        private class UseItemEffectRecord
        {
            public string effectPath { get; set; }

            public bool effectPlaying { get; set; }
        }

        



        #region Ӣ�ۻ�Ƥ&��Ƶ����
        //[PopupField("Root/HeroGood/SkinGroupBtns")]
        //private GameObject btnsSkinObj;
        //[PopupField("Root/HeroGood/SkinGroupBtns/BtnSkin01")]
        //private HeroVideoSkinBtn btnSkin1;
        //[PopupField("Root/HeroGood/SkinGroupBtns/BtnSkin02")]
        //private HeroVideoSkinBtn btnSkin2;
        //[PopupField("Root/HeroGood/SkinGroupBtns/BtnSkin03")]
        //private HeroVideoSkinBtn btnSkin3;
        //[PopupField("Root/HeroGood/SkinGroupBtns/BtnSkin04")]
        //private HeroVideoSkinBtn btnSkin4;
        //[PopupField("Root/HeroGood/SkinGroupBtns/BtnSkin05")]
        //private HeroVideoSkinBtn btnSkin5;

        //[PopupField("Root/HeroGood/VideoGroupBtns")]
        //private GameObject btnsVideoObj;
        //[PopupField("Root/HeroGood/VideoGroupBtns/BtnSkin01")]
        //private HeroVideoSkinBtn btnVideo1;
        
        //[PopupField("Root/HeroGood/VideoGroupBtns/BtnSkin02")]
        //private HeroVideoSkinBtn btnVideo2;
      

        //[PopupField("Root/HeroGood/VideoGroupBtns/BtnSkin03")]
        //private HeroVideoSkinBtn btnVideo3;
        

        //[PopupField("Root/HeroGood/VideoGroupBtns/BtnSkin04")]
        //private HeroVideoSkinBtn btnVideo4;
       

        //[PopupField("Root/HeroGood/VideoGroupBtns/BtnSkin05")]
        //private HeroVideoSkinBtn btnVideo5;
        

        [PopupField("VideoCanvas/HeroVideo")]
        private PlayAddressableVideo VideoBG;

         
        #endregion
    }
}