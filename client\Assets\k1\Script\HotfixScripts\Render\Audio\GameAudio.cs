







using System;
using System.Collections.Generic;
using Cfg.C;
using Cysharp.Threading.Tasks;
using TFW;
using UnityEngine;

namespace Render
{
    /// <summary>
    /// 游戏音频管理
    /// 频道管理
    /// </summary>
    public static partial class GameAudio
    {
        //readonly static Dictionary<int, int> _localToManagerId = new Dictionary<int, int>();
        
        /// <summary>
        /// 初始化音频注册
        /// </summary>
        public static void Initialize()
        {
            //_localToManagerId.Clear();
            InitializeRegistry();
        }

        /// <summary>
        /// 反向初始化音频注册
        /// </summary>
        public static void UnInitialize()
        {
            UnInitializeRegistry();
        }

        /// <summary>
        /// 播放一个音频资源
        /// </summary>
        /// <param name="id"></param>
        /// <param name="attachNode"></param>
        /// <returns></returns>
        public static int PlayAudio(int id, Transform attachNode = null)
        {
            var localId = Guid.NewGuid().GetHashCode();
            //_localToManagerId[localId] = -1;

            return PlayAudioAsync(localId, id, attachNode);
        }



        public static int PlayBGM(int id, Transform attachNode = null)
        {

            //AudioManager.Instance.StopAudioChannel(AudioChannelType.BGM);

            var localId = Guid.NewGuid().GetHashCode();


            return PlayAudioAsync(localId, id, attachNode);

             
        }



        private static int PlayAudioAsync(int localId, int id, Transform attachNode = null, float volume = 1)
        {
            var cfg =  CAudioList.I(id);
            if (cfg == null || AudioManager.Instance==null)
                return -1;
             
            string path = cfg.Asset;
            if (!path.Contains("Assets"))
            {
                path = $"{ResourceDict.Audio}/{cfg.Asset}";
            }

            ////D.Error?.Assert(cfg != null, $"[GameAudio] Audio config {id} not found.");
           
            return AudioManager.Instance.Play((AudioChannelType)cfg.Scene,path,
                cfg.Time, volume * cfg.VolScale, 1, cfg.Priority, (AudioPlayMode)cfg.Typ, cfg.Mode == 1, attachNode);
        }

          
        /// <summary>
        /// 播放一个音频资源
        /// </summary>
        /// <param name="id"></param>
        /// <param name="volume"></param>
        /// <returns></returns>
        public static int PlayAudio(int id, float volume)
        {
            var localId = Guid.NewGuid().GetHashCode();


            return PlayAudioAsync(localId, id, null, volume);
        }

        /// <summary>
        /// 播放一个音频资源
        /// </summary>
        /// <param name="assetPath"></param>
        /// <param name="time"></param>
        /// <param name="volume"></param>
        /// <param name="is3DAudio"></param>
        /// <returns></returns>
        public static int PlayAudio(AudioChannelType channelType, string assetPath, float time, float volume = 1f, bool is3DAudio = false)
        {
            if (AudioManager.Instance == null)
                return -1;
            return  AudioManager.Instance.Play(channelType, assetPath, time, volume, 1);
        }

        /// <summary>
        /// 音频播放暂停或者恢复
        /// </summary>
        /// <param name="id"></param>
        /// <param name="pause"></param>
        public static void PauseAudio(int id, bool pause)
        {
            if(pause)
            {
                AudioManager.Instance.Pause(id);
            }
            else
            {
                AudioManager.Instance.UnPause(id);
            }
        }

        /// <summary>
        /// 停止某一个声音的播放
        /// </summary>
        /// <param name="audioId"></param>
        public static void StopAudio(int audioId, float lerpTime = -1f)
        { 
            AudioManager.Instance.Stop(audioId, lerpTime); 
        }
    }
}