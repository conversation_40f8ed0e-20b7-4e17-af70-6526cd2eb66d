﻿using Common;
using cspb;
using DeepUI;
using Game.Config;
using Game.Data;
using GameState;
using K3;
using Public;
using System;
using System.Collections.Generic;
using THelper;
using UI;
using UI.Alliance;
using UI.Utils;

namespace Logic
{
    /// <summary>
    /// 联盟挖人
    /// </summary>
    [UnityEngine.Scripting.Preserve]
    public class LAllianceRecruit :Ins<LAllianceRecruit>, ILogoutCallback, ILoginCallback
    {

        UnionRecruitNtf catchNtf;
        bool isAgreeRecruit = false;

        Cysharp.Threading.Tasks.UniTask ILoginCallback.OnLogin()
        {
            MessageMgr.RegisterMsg<DoUnionRecruitAck>(this, OnDoUnionRecruitAck);
            MessageMgr.RegisterMsg<UnionRecruitNtf>(this, OnUnionRecruitNtf);
            EventMgr.RegisterEvent(TEventType.UIMain_Update_GameEnd_State, OnUIMain_Update_ShowMenu, this);
            
            return Cysharp.Threading.Tasks.UniTask.CompletedTask;
        }

        Cysharp.Threading.Tasks.UniTask ILogoutCallback.OnLogout()
        {
            MessageMgr.UnregisterMsg(this);
            EventMgr.UnregisterEvent(this);
            catchNtf = null;
            return Cysharp.Threading.Tasks.UniTask.CompletedTask;
        }

        void OnDoUnionRecruitAck(DoUnionRecruitAck ack) 
        {
            if (ack.ErrCode != ErrCode.ErrCodeSuccess)
            {
                ack.ErrCode.ToFloatErrCode();
                return;
            }
            /////提示迁城面板
            //LAllianceMgr.I.ReqUnionRandomCoord();

            //参考 UIMainMenuAlliance的方法ClickChatBtn
            //修改为当玩家接受邀请后跳转到联盟主界面
            //by tanshengqi 2024/4/22
            if (isAgreeRecruit)
            {
                //�����˽���
                //if (!GuideMgr.I.IsGuideDone)
                //    return;
                UI.Alliance.UIAllianceMainData alliacneData = null;


                if (!MainFunctionOpenUtils.ALllianceOpenState)
                {
                    return;
                }

                if (LPlayer.I.UnionID == 0)
                {
                    //if (GameData.I.MainData.CurrMenuType == MainMenuType.HERO)
                    //{
                    //    GameInstance.CityMainCameraEnable(true);
                    //}
                    PopupManager.I.ShowDialog<UIAllianceWelcome>();
                    //LAllianceMgr.I.ShowAllianceMain(null);
                }
                else
                {
                   PopupManager.I.ShowLayer<UI.Alliance.UIAllianceMain>(alliacneData);
                }
                if (SceneManager.I.IsUIMainShow())
                {
                    //if (GameData.I.MainData.CurrMenuType != MainMenuType.ALLIANCE)
                    //    EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.ALLIANCE);
                }
            }
        }


        void OnUnionRecruitNtf(UnionRecruitNtf ntf) 
        {
            catchNtf = ntf;
        }


        public void ReqDoUnionRecruitReq(bool flag) 
        {
            MessageMgr.Send(new DoUnionRecruitReq()
            {
                jion = flag
            });
            catchNtf = null;
            isAgreeRecruit = flag;

        }

        void OnUIMain_Update_ShowMenu(object[] arr)
        {
            if (catchNtf == null)
                return;
            if (!CheckCanShowAllianceRecuit())
                return;
            WndMgr.Show<UIMainAllianceRecruit>(new UIMainAllianceRecruitData()
            {
                data = catchNtf.tarUnion,
                rewawrd = catchNtf.joinReward
            });
        }


        /// <summary>
        /// 只有在这些界面里才可以显示 联盟邀请
        /// </summary>
        private List<Type> filterPanel = new List<Type>
        {
            typeof(UIChat),
            typeof(UIMain2),
            //typeof(UIMain1),
            typeof(GoldFly),
            //typeof(TreasureChestFly),
            typeof(NewTreasureChestFly),
            //typeof(BattleWinGetTechFly),
            typeof(HurtFlyText),
            typeof(UIFloatTips),
            typeof(UIFloatingDamages),
            typeof(BossRedPanel),
            typeof(UICreateTroop),
            //typeof(UILevelTips)
        };


        /// <summary>
        /// 检查是否能展示
        /// </summary>
        /// <returns></returns>
        public bool CheckCanShowAllianceRecuit()
        {
            var condition1 = LPlayer.I.IsPlayerInUnion();
            if (!condition1)
                return false;

            var showUI = WndMgr.GetShowUI();
            foreach (var item in showUI)
            {
                if (item.Value != null && item.Value.IsShow)
                {
                    //如果只有有一个界面不在我过滤的这些界面里,则不显示联盟邀请
                    if (!filterPanel.Contains(item.Value.GetType()))
                    {
                        //如果有显示这些界面 则不显示联盟邀请
                        return false;
                    }
                }
            }
            return true;
        }

    }
}