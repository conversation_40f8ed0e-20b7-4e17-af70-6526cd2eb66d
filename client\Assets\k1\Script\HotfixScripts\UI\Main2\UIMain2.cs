﻿using Common;
using DeepUI;
using Game.Data;
using Logic;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Cysharp.Threading.Tasks;
using TFW;
using UI.Alliance;

namespace UI
{
    public class UIMain2Data : UIData
    {
        public MainMenuType ShowType;
    }
    /// <summary>
    /// 主界面信息数据
    /// </summary>
    [Popup("MainCity/New/UIMain2", true, true)]
    [System.Obsolete("主界面各个模块入口不要再放进UIMain2类中，单独拆分为MonoBehaviour，自行管理生命周期和监听相关事件进行状态更新及显隐控制，方便将来优化启动时间做各模块入口的资源延迟加载及延迟初始化，具体修改实现参考 ActivityEntranceButton 及 VipEntranceButton")]
    public partial class UIMain2 : BasePopupLayer
    {

        protected internal override PopupOverlayType OverlayType =>  PopupOverlayType.None;

        #region 军队数据更新开关
        private bool canUpdateTroop = false;
        private float intervalTime = 0;
        #endregion

        #region 初始化

        /// <summary>
        /// 数据初始化
        /// </summary>
        protected override void OnInit()
        {

            //初始化各个功能模块
            InitFunctionModule();

            //启用刷帧控制
            UpdateEnable = true;
            //记录存储主界面Canvas
            GameData.I.MainData.UpdateMainCanvas(canvas);

            UIMainAllianceBuildInvite.ShowCountDown();
               
        }




        /// <summary>
        /// 界面关闭动画完成
        /// </summary>
        protected override void OnDispose()
        {
            base.OnDispose();

            //GameData.I.MainData.UpdateCurrMenuType(MainMenuType.NONE);

            //反向初始化各个功能模块
            UnInitFunctionModule();
        }


        /// <summary>
        /// 初始化功能模块
        /// </summary>
        private void InitFunctionModule()
        {
            //初始化联盟相关
            InitAlliance();

            //初始化通用按钮
            InitButton();

            //初始化左侧按钮列表
            //InitLeftBtnsList();

            //初始化右侧按钮列表
            InitRightBtnsButton();

            //初始化战斗相关
            InitFight();

            //初始化主界面动画相关
            InitAnim();

            //初始化飞金币
            InitFlyCoin();

            //初始化GM
            InitGM();

            //初始化头像
            InitHead();

            //初始化地图相关
            //InitMap();

            //初始化菜单栏
            InitMenu();

            //初始化保护罩
            InitProtect();

            //初始化客诉
            //InitService();

            //初始化设置
            InitSetting();

            //初始化城堡皮肤
            //InitSkin();

            //迁服
            InitMigration();

            //初始化月卡
            InitMonthCard();
            //InitMonthCard();
            //首充3日购
            InitFirstCharge();
            //InitWorldGuide();
            //首充and累充
            //InitFirstChargeNew();
            //调查问卷
            // InitAnswer();
            //cp弹窗
            // InitCpWindow();
            //怪物图鉴
            //InitMonsterIiiustrated();

            //情人节入口
            Initvalentine();

            //周年庆收集搜索
            InitAnniversarySearch();

            //破碎试炼爬塔怪召唤按钮
            InitCallTrialsBtn();

            //计时赛
            //InitTrial();

            InitRightTop();

            //初始化红点
            //InitUIMainRed();

            InitTask();

            //InitChatMsg();

        }

        /// <summary>
        /// 反向初始化功能模块
        /// </summary>
        private void UnInitFunctionModule()
        {
            //反向初始化联盟相关
            UnInitAlliance();

            UnRechargeMonsterCard();

            //反向初始化通用按钮
            UnInitButton();

            ///反向初始化月卡
            UnInitMonthCard();

            //反向初始化右侧按钮列表
            UnInitRightBtnsButton();
            //UnInitWorldGuide();
            //反向初始化战斗相关
            UnInitFight();

            //反向初始化主界面动画相关
            UnInitAnim();

            //反向初始化飞金币
            UnInitFlyCoin();

            //反向初始化GM
            UnInitGM();

            //反向初始化头像
            UnInitHead();

            //反向初始化地图相关
            UnInitMap();

            //反向初始化菜单栏
            UnInitMenu();

            //反向初始化保护罩
            UnInitProtect();
            UnCastleEffectSkill();
            //反向初始化客诉
            //UnInitService();

            //反向初始化设置
            UnInitSetting();

            //反向初始化地图显示过滤
            UnInitMapShowWidgetFilter();

            //反向初始化城堡皮肤
            //UnInitSkin();

            //反向初始化迁服
            UnInitMigration();
            //反向初始化三日购
            UnInitFirstCharge();
            //UnInitFirstChargeNew();
            //反向初始化图鉴
            //UnInitMonsterIiiustrated();

            //反向初始化情人节入口
            Unvalentine();

            //计时赛
            //UnInitTrial();

            //清理邮件显示数量
            GameData.I.MainData.MailDisplayCount = 0;

            //周年庆收集搜索
            DeinitAnniversarySearch();

            DeInitRightTop();

            UnInitTask();

            //UnInitChatMsg();


            EventMgr.UnregisterEvent(this);
        }


        #endregion

        #region 数据刷新显示

        /// <summary>
        /// 当界面第一次打开时调用
        /// </summary>
        protected internal override void OnOpenStart()
        {
            base.OnOpenStart();

            //刷新菜单栏显示
            //UpdateMainMenuInfo(MainMenuType.TRAIN);


            if (FloatTips.GetUIInstance() != null)
            {
                //只是访问下 让其生成实例,不然首次floatMsg gameobjcet会为null，导致不显示tips的概率
            }

            //刷新留言板数据
            AllianceGameData.I.AlliancMsgBoardData.StartTimerReq();

            OnShowStart();
        }


        /// <summary>
        /// 界面每次显示时调用
        /// </summary>
        protected internal override void OnShowStart()
        {
            base.OnShowStart();
            //刷新游戏关卡相关信息
            UpdateGameLevelInfo();

            //每次显示界面右侧列表都需要关闭
            OnOpenStartRightBtnsList();

            //刷新左侧列表布局
            //UpdateLeftBtnsListLayout();

            ////联盟帮助加速
            //UpdateAllianceHelpSpeedupRootState(true);//Logic.LPlayer.I.IsPlayerInUnion()

            ///刷新红点
            RefushTaskRed();


            var mData = Data as UIMain2Data;
            if (mData != null)
            {

                UpdateMainMenuInfo(mData.ShowType);

                //GameData.I.MainData.UpdateCurrMenuType(mData.ShowType);

                //for (int i = 1; i <= 5; i++)
                //{
                //    var menuFuc = GetMenuFunctionType((MainMenuType)i);

                //    if (i != (int)mData.ShowType)
                //    {
                //        menuFuc.ExitMenuPanel(mData.ShowType);
                //    }
                //}

                switch (mData.ShowType)
                {
                    //case MainMenuType.PRISON:
                    //    WndMgr.Show<UI.UIMainCity>();
                    //    break;
                    case MainMenuType.CITY: 
                        break;
                    default:
                        break;
                }

                //var menuFuc2 = GetMenuFunctionType(mData.ShowType);
                //menuFuc2.EnterMenuPanel();
            }

        }
        /// <summary>
        /// 刷新玩家信息数据
        /// </summary>
        private async UniTaskVoid UpdateGameLevelInfo()
        {
            if (!IsInitReady)
                return;

            //刷新战斗相关信息数据
            UpdateFightData();
            //刷新功能开放状态
            UpdateFunctionOpenState();
            //刷新商城红点信息显示
            //UpdateShopRedInfo();
            //刷新资产信息
            OnUpdateAssetInfo(null);
            //刷新新怪物图鉴提示
            //await UpdateCheckIsHaveMonsterFirstTime();

            //UpdateAllianceHelpSpeedupRootState(true);//Logic.LPlayer.I.IsPlayerInUnion()


            //刷新商城hud相关
            EventMgr.FireEvent(TEventType.ShopHudVisible);
        }



        #endregion


        #region 数据刷帧处理

        /// <summary>
        /// 刷帧处理
        /// </summary>
        /// <param name="deltaTime"></param>
        protected internal override void OnUpdate(float deltaTime)
        {
            base.OnUpdate(deltaTime);
            //更新部队数据
            OnUpdateTroopInfoByInterval();
            //刷新大世界位置坐标
            MapCenterPos();

            //刷新技能cd
            //HeroSkillCDUpdate();
            //kvk召唤Boss按钮
            OnUpdateKVKTrialsBtnState();

            //计时赛按钮
            //CheckTrialUnlock();
            //计时赛重置倒计时
            //RefreshTrialTimer(); 

            //暂时不用 先关闭
            //CheckTowerGuid(); 

            RefreshGuideTask();
        }


        #endregion




        #region 事件监听

        /// <summary>
        /// 刷新玩家相关信息数据
        /// </summary>
        [PopupEvent(TEventType.Update_GameLevel_Info)]
        private void OnUpdateGameLevelInfo(object[] objs)
        {
            UpdateGameLevelInfo();
        }

        #endregion

    }
}
