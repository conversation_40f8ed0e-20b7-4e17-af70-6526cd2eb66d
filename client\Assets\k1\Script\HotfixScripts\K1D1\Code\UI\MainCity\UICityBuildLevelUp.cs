﻿ 
using Cfg.C;
using Common;
using cspb;
using DeepUI;
using Game.Config;
using Game.Data;
using K3;
using K3.Scripts;
using TFW.Localization;
using Logic;
using Public;
using Render;
using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using TFW;
using UnityEngine;
using UnityEngine.UI;
using static MainCityItem;
using System.Threading.Tasks;

namespace UI
{

    public class UICityBuildLevelUpData : UIData
    {
        public int cityType;
    }
    
    public class UICityBuildLevelUpBuffPropertyData
    {
        public int PropertyId = 0;
        public float PropertyValue1 = 0;
        public float PropertyValue2 = 0;

        public string DisplayValue1 = "";
        public string DisplayValue2 = "";

        public string Icon = "";
        public string Desc = "";
        public bool ShowValue1 = true;
    }

    [Popup("LevelUp/UICityLevelUp", true, true)]
    public partial class UICityLevelUp : BasePopup
    {
        private BtnUpgrade btnClick;

        private UIGrid mCostGrid;
        private TechData mTechData;
        
        private Dictionary<int, ItemData> itemMap = null;
        private List<Asset> vmAsset = null;
         
        private UICityBuildLevelUpData mData;
        private bool IsDiscountActivity = false;

        private float DiscountPrices = 1;

        CityType mCurCityType;

        private Dictionary<CityType, string> mBg2Dict = new Dictionary<CityType, string>()
        {
            {CityType.Trial, "Assets/K3/Res/Art/Textures/BuildingUpgrade/UI_Upgrade_Bg_04.png"},
            {CityType.Police, "Assets/K3/Res/Art/Textures/BuildingUpgrade/UI_Upgrade_Bg_02.png"},
            {CityType.Meeting, "Assets/K3/Res/Art/Textures/BuildingUpgrade/UI_Upgrade_Bg_07.png"},
            {CityType.Machine, "Assets/K3/Res/Art/Textures/BuildingUpgrade/UI_Upgrade_Bg_05.png"},
            {CityType.Gene, "Assets/K3/Res/Art/Textures/BuildingUpgrade/UI_Upgrade_Bg_06.png"},
            {CityType.Offline, "Assets/K3/Res/Art/Textures/BuildingUpgrade/UI_Treasury_Banner.png"},
        };

        // private readonly int mRwdAttrId = 12064;
        
        protected override void OnInit()
        {
           base.OnInit();
            
            AddListener(EventTriggerType.Click, mClose, (x, y) => Close());
            
            // AddListener(EventTriggerType.Click, mBtnInfo, (x, y) => { ShowInfoPanel(); });

            btnClick = GetComponent<BtnUpgrade>("Root/mBtnRoot/Button");
            mCostGrid = mCanUp.GetComponent<UIGrid>();

            mOrderTips_TFWText.text = LocalizationMgr.Get("Completed_Wanted_Count");
        }

        protected internal override void OnOpenStart()
        { 
            OnShowStart();
        }
  
        protected internal override void OnShowStart()
        { 
            OnShown();
        }


        void OnShown()
        {
            mData = Data as UICityBuildLevelUpData;
            
            IsDiscountActivity = false;
            DiscountPrices = 1;

            mCurCityType = (CityType)mData.cityType;
             
            GameData.I.SkillData.MTechs.TryGetValue(mData.cityType, out mTechData);

            PlayerAssetsMgr.I.GetCurAssetAndItem(out itemMap, out vmAsset);

            Refresh();
        }

        private void ShowInfoPanel()
        {
            foreach (var item in CSPlayer.I.SpecialBoxs)
            {
                if ((int) item.CityType == mData.cityType)
                {
                    PopupManager.I.ShowPanel<UICityLevelUpInfo>(item);
                }
            }
        }

        private async void Refresh()
        {
            if (mTechData == null)
            {
                return;
            }

            var cfg = await CD2CityBuilding.GetConfigAsync((int) mCurCityType * 1000);
            var title = LocalizationMgr.Get(cfg.Name);
            mTitle_TFWText.text = title;
            
            RefreshIcon();
            
            RefreshBg();

            var upCost = PlayerAssetsMgr.I.HaveAssets(mTechData.UpgradeCost, itemMap, vmAsset, IsDiscountActivity);
            var upResCondition = string.IsNullOrEmpty(upCost);

            var upTechCondition = true;
            
            foreach (var tech in mTechData.MConfig.TechCondition)
            {
                var techId = int.Parse(tech);
                var techCfg = await Cfg.C.CD2Tech.GetConfigAsync(techId);

                if (techCfg == null)
                {
                    D.Error?.Log($"科技找不到：{techId}");
                    break; 
                }

                var techType = techCfg.Type;
            
                var conditionOk = GameData.I.SkillData.MTechs.TryGetValue(techType, out var techData) && techData.Level >= techCfg.Level;
            
                if (!conditionOk)
                {
                    upTechCondition = false;
                    break;
                }
            }
            
            var upOrderCondition = MergeTaskMgr.I.TotalCompleteTaskNum >= mTechData.MConfig.WantedsLimit;

            if (upResCondition && upTechCondition && upOrderCondition)//&& chapterCondition
            {
                PlayerAssetsMgr.I.GetCurAssetAndItem(out var cacitemMap, out var cacvmAsset);
                btnClick.InitData(LocalizationMgr.Get("Tech_Upgrade"), BtnUpgrade.BtnColor.Yellow, BtnClick);
            }
            else
            {
                btnClick.InitData(LocalizationMgr.Get("Ui_goto"), BtnUpgrade.BtnColor.Blue, BtnClick);
            }

            if (mTechData.MaxLevel)
            {
                mBtnRoot.SetActive(false);
            }
            else
            {
                mBtnRoot.SetActive(true);
            }
            
            RefreshLv();
            
            RefreshRwd();
            
            RefreshOrder();
            
            RefreshAttrItems();
            
            RefreshCanUp();
            
            LayoutRebuilder.ForceRebuildLayoutImmediate(mContent.transform as RectTransform);
            
            LayoutRebuilder.ForceRebuildLayoutImmediate(mRoot.transform as RectTransform);
        }

        private async void BtnClick()
        {
            foreach (var tech in mTechData.MConfig.TechCondition)
            {
                var techId = int.Parse(tech);
                var techCfg = await Cfg.C.CD2Tech.GetConfigAsync(techId);
                var techType = techCfg.Type;

                var conditionOk = GameData.I.SkillData.MTechs.TryGetValue(techType, out var techData) && techData.Level >= techCfg.Level;

                if (!conditionOk)
                {
                    var jumpCityType = (CityType) techType;
                    
                    Close();

                    var ui = PopupManager.I.FindPopup<UIMainCity>();
                    if (ui != null)
                    {
                        PopupManager.I.ClearAllPopup();
                    
                        int cityCfgID = LPlayer.I.GetCityBuildingIDByType(techType);
                        ui.dragImage.MoveTOCfg(cityCfgID, () =>
                        {
                           var guidData = new UIGuidData();
                            guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMainCity", UIItem = $"FullScreenBgCanvas/Root/BG1/Content/Building/build_{techType}" });
                            UIGuid.StartGuid(guidData);
                        });
                    }
                    
                    return;
                }
            }
            
            if (!string.IsNullOrEmpty(PlayerAssetsMgr.I.HaveAssets(mTechData?.UpgradeCost, itemMap, vmAsset, IsDiscountActivity)))
            {
                if (!PlayerAssetsMgr.I.HaveAssetsToGO(mTechData?.UpgradeCost, IsDiscountActivity))
                {
                    PlayerAssetsMgr.I.GetCurAssetAndItem(out itemMap, out vmAsset);
                    Refresh();
                }
                return;
            }

            if (MergeTaskMgr.I.TotalCompleteTaskNum < mTechData.MConfig.WantedsLimit)
            {
                this.Close();
                
                var uimerge = PopupManager.I.FindPopup<UIMerge>();
                if (uimerge != null)
                {
                    return;//已经打开
                }

                EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.CITY); 
            
                var guidData = new UIGuidData();
                guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMain2", UIItem = "content/MenuRoot/Battle", slide = true });
                UIGuid.StartGuid(guidData, true);

                return;
            }
            
            UpgradeSkill(); 
        }

        private void UpgradeSkill()
        { 
            PlayerAssetsMgr.I.CalAssets(mTechData.UpgradeCost, itemMap, vmAsset, IsDiscountActivity);

            GameAudio.PlayAudio(AudioConst.SOUND_UI_SKILL_UPGRADE);
 
            LSkill.I.UpgradeSkill(mTechData);
        }
         
        [PopupEvent(TEventType.RefreshTech)]
        private void RefreshTech(object[] objs)
        {
             RefeshTechToClose();

            EventMgr.FireEvent(TEventType.K3BuildingLvUp, mData.cityType);
        }

        private void RefeshTechToClose()
        {
            GameData.I.SkillData.MTechs.TryGetValue(mData.cityType, out mTechData);

            Refresh();
             
            var m_TaskList = ChapterTaskMgr.I.GetCurrentChapterList();
            foreach (var item in m_TaskList)
            { 
                var cfg = Cfg.C.CQuestList.I(Cfg.C.CChapterQuest.I(item.cfgID).QuestType);// await Cfg.C.CQuestList.GetConfigAsync(await item.GetCfgQuestType());
                if (item.state == QuestState.QuestStateFinish && (cfg.Jump == 122 || cfg.Jump==105))  //122 建筑升级
                {
                    if (cfg.Jump == 122)
                    {
                        var parms = item.ConfigData.Params;
                        if (parms?.Count > 0 && int.TryParse(parms[0], out int cityType))
                        {
                            if (cityType == mData.cityType)
                            {
                                if (item.ConfigData.Count == mTechData.Level)
                                {
                                    //UniTask.Void(async () => //防止同一帧 关闭导致事件契合重复
                                    //{
                                    //    await UniTask.NextFrame();
                                    PopupManager.I.ClosePopup<UICityLevelUp>();
                                    //}); 
                                }
                            }
                        }
                    }
                    else if (cfg.Jump == 105)//主城升级
                    {
                        if (mData.cityType==1 && item.ConfigData.Count == mTechData.Level)
                        {
                            PopupManager.I.ClosePopup<UICityLevelUp>();
                        }
                    }
                }
            } 
        }

        [PopupEvent(TEventType.QuestChangeNtf)]
        private void QuestChangeNtf(object[] objs)
        {
             RefeshTechToClose();
        }


        [PopupEvent(TEventType.RefreshAssetAck)]
        private void RefreshAsset(object[] objs)
        {
            PlayerAssetsMgr.I.GetCurAssetAndItem(out itemMap, out vmAsset);
            Refresh();
        }

        private void RefreshIcon()
        {
            if (mCurCityType == CityType.Castle)
            {
                mTitleIcon.SetActive(false);
            }
            else
            {
                mTitleIcon.SetActive(true);
                
                SpecialBoxData boxData = null;
                
                foreach (var item in CSPlayer.I.SpecialBoxs)
                {
                    if (mCurCityType == item.CityType)
                    {
                        boxData = item;
                        break;
                    }
                }

                if (boxData == null)
                {
                    mTitleIcon.SetActive(false);
                    
                    return;
                }
                
                UITools.SetImageBySpriteName(mTitleIcon_TFWImage, UITools.GetAttributeDisplayKey((int)this.mCurCityType-10));
            }
        }

        private void RefreshBg()
        {
            if (mCurCityType == CityType.Castle)
            {
                mBG1.SetActive(true);
                mBG2.SetActive(false);
            }
            else
            {
                mBG1.SetActive(false);
                mBG2.SetActive(true);

                var img = "Assets/K3/Res/Art/Textures/BuildingUpgrade/UI_Upgrade_Bg_02.png";

                if (mBg2Dict.ContainsKey(mCurCityType))
                {
                    img = mBg2Dict[mCurCityType];
                }
                
                UITools.SetDynamicRawImage(mBG2_TFWRawImage, img);

                var spine = mUICommonHeroSpineItem.GetComponent<UICommonHeroSpineItem>();

                SpecialBoxData boxData = null;
                
                foreach (var item in CSPlayer.I.SpecialBoxs)
                {
                    if (mCurCityType == item.CityType)
                    {
                        boxData = item;
                        break;
                    }
                }

                if (boxData == null)
                {
                    mUICommonHeroSpineItem.SetActive(false);
                    
                    return;
                }

                if (!boxData.HaveMainHero())
                {
                    mUICommonHeroSpineItem.SetActive(false);
                    
                    return;
                }

                mUICommonHeroSpineItem.SetActive(true);
                
                spine.SetHeroSpine(boxData.MainHeroID);
            }
        }

        private void RefreshLv()
        {
            var nextData = new TechData() { Level = mTechData.Level + 1, Type = mTechData.Type };
            var nextDataCfg = nextData.MConfig ?? nextData.MaxConfig;

            mLv1_TFWText.text = mTechData.Level.ToString();
            mLv2_TFWText.text = nextDataCfg.Level.ToString();
            mLvMax_TFWText.text = mTechData.Level.ToString();

            if (mTechData.MaxLevel)
            {
                mLv1.SetActive(false);
                mLvArr.SetActive(false);
                mLv2.SetActive(false);
                
                mLvMax.SetActive(true);
            }
            else
            {
                mLv1.SetActive(true);
                mLvArr.SetActive(true);
                mLv2.SetActive(true);
                
                mLvMax.SetActive(false);
            }
        }
        
        private void RefreshRwd()
        {
            mRwdRoot.SetActive(false);
            return;
            
            // var nextData = new TechData() { Level = mTechData.Level + 1, Type = mTechData.Type };
            // var nextDataCfg = nextData.MConfig ?? nextData.MaxConfig;
            //
            // var datas = GetPropertyDatas(mTechData.MConfig.AddBuffProperty, mTechData.MConfig.AddBuffPropertyValue, nextDataCfg.AddBuffProperty, nextDataCfg.AddBuffPropertyValue);
            //
            // var rwdData = datas.Find(d => d.PropertyId == mRwdAttrId);
            //
            // if (null == rwdData)
            // {
            //     mRwdRoot.SetActive(false);
            //     return;
            // }
            //
            // mRwdRoot.SetActive(true);
            //
            // mRwdTitle_TFWText.text = rwdData.Desc;
            // mRwd1_TFWText.text = rwdData.DisplayValue1;
            // mRwd2_TFWText.text = rwdData.DisplayValue2;
            //
            // if (mTechData.MaxLevel)
            // {
            //     mRwd1.SetActive(false);
            //     mRwdArr.SetActive(false);
            // }
            // else
            // {
            //     mRwd1.SetActive(true);
            //     mRwdArr.SetActive(true);
            // }
        }

        private void RefreshOrder()
        {
            if (mTechData.MConfig.WantedsLimit <= 0)
            {
                mOrderRoot.SetActive(false);
                return;
            }

            if (mTechData.MaxLevel)
            {
                mOrderRoot.SetActive(false);
                return;
            }
            
            mOrderRoot.SetActive(true);

            mWantedProgressLbl_TFWText.text = MergeTaskMgr.I.TotalCompleteTaskNum + "/" + mTechData.MConfig.WantedsLimit;

            var scale = MergeTaskMgr.I.TotalCompleteTaskNum / (float) mTechData.MConfig.WantedsLimit;

            scale = Math.Max(0, scale);
            scale = Math.Min(scale, 1);

            var rect = mWantedProgress.transform as RectTransform;

            rect.localScale = new Vector3(scale, 1, 1);
            
            mOrderCheck.SetActive(MergeTaskMgr.I.TotalCompleteTaskNum >= mTechData.MConfig.WantedsLimit);
        }
        
        private async void RefreshAttrItems()
        {
            var nextData = new TechData() { Level = mTechData.Level + 1, Type = mTechData.Type };
            var nextDataCfg = nextData.MConfig ?? nextData.MaxConfig;

            if (mTechData.MConfig.AddBuffProperty.Count <= 0 && nextDataCfg.AddBuffProperty.Count <= 0)
            {
                mAttrRoot.SetActive(false);
                return;
            }

            mAttrRoot_UIGrid.Clear();

            var datas = await GetPropertyDatas(mTechData.MConfig.AddBuffProperty, mTechData.MConfig.AddBuffPropertyValue, nextDataCfg.AddBuffProperty, nextDataCfg.AddBuffPropertyValue);

            var powerData = new UICityBuildLevelUpBuffPropertyData()
            {
                Desc = LocalizationMgr.Get("Power_Details_14"),
                DisplayValue1 = UIStringUtils.FormatIntUnitByLanguage(mTechData.MConfig.Power),
                DisplayValue2 = UIStringUtils.FormatIntUnitByLanguage(nextDataCfg.Power),
                Icon = "UI_Item_Icon_Power",
            };

            datas.Add(powerData);

            foreach (var data in datas)
            {
                // if (data.PropertyId == mRwdAttrId)
                // {
                //     continue;
                // }

                var attrItem = mAttrRoot_UIGrid.AddItem<UICityBuildLevelUpAttrItem>();
                attrItem.SetData(data);
            }
            
            LayoutRebuilder.ForceRebuildLayoutImmediate(mAttrRoot.transform as RectTransform);
        }
        
        private async void RefreshCanUp()
        {
            if (mTechData.MaxLevel)
            {
                mCanUp.SetActive(false);
                return;
            }
            
            mCostGrid.Clear();

            foreach (var tech in mTechData.MConfig.TechCondition)
            {
                var techId = int.Parse(tech);
                var techCfg = await Cfg.C.CD2Tech.GetConfigAsync(techId);
                var techType = techCfg.Type;
            
                var conditionOk = GameData.I.SkillData.MTechs.TryGetValue(techType, out var techData) && techData.Level >= techCfg.Level;
            
                if (!conditionOk)
                { 
                    //条件不满足
                    mCostGrid.AddItem<CostItemUI>().SetData(new TypIDVal()
                    {
                        ID = techId,
                        typ = "tech",
                        val = techCfg.Level,
                    }, false, 0);
                }
            }

            foreach (var item in mTechData.UpgradeCost)
            {
                if (item.Val > 0)
                {
                    mCostGrid.AddItem<CostItemUI>().SetData(item, false, 0, itemMap, vmAsset);
                }
            }
            
            mCanUp.SetActive(mCostGrid.MList.Count > 0);
        }
        
        private async UniTask<List<UICityBuildLevelUpBuffPropertyData>> GetPropertyDatas(List<string> pId1, List<string> pValue1, List<string> pId2, List<string> pValue2)
        {
            var propertyDatas = new List<UICityBuildLevelUpBuffPropertyData>();
            var hashSet = new HashSet<int>();
            
            pId1.ForEach(id => hashSet.Add(int.Parse(id)));
            pId2.ForEach(id => hashSet.Add(int.Parse(id)));

            foreach (var propertyId in hashSet)
            {
                var cfg = await CBuffProperty.GetConfigAsync(propertyId);

                if (null == cfg)
                {
                    continue;
                }

                var idx1 = pId1.FindIndex(id => id == propertyId.ToString());
                var idx2 = pId2.FindIndex(id => id == propertyId.ToString());

                var value1 = idx1 < 0 ? 0 : float.Parse(pValue1[idx1]);
                var value2 = idx2 < 0 ? 0 : float.Parse(pValue2[idx2]);

                UICityBuildLevelUpBuffPropertyData propertyData;

                if (propertyId == (int)PropTypeEnum.GoldSpeed)
                {
                    propertyData = new UICityBuildLevelUpBuffPropertyData()
                    {
                        PropertyId = propertyId,
                        PropertyValue1 = value1*3600,
                        PropertyValue2 = value2 * 3600,
                        DisplayValue1 = "ui_idle_text_by_hour".ToLocal( UIStringUtils.FormatIntUnitByLanguage(value1*3600) ),
                        DisplayValue2 = "ui_idle_text_by_hour".ToLocal(UIStringUtils.FormatIntUnitByLanguage(value2 * 3600)) ,
                        Icon = cfg.Icon,
                        Desc = LocalizationMgr.Get(cfg.Name),
                    };
                }
                else
                {
                    propertyData = new UICityBuildLevelUpBuffPropertyData()
                    {
                        PropertyId = propertyId,
                        PropertyValue1 = value1,
                        PropertyValue2 = value2,
                        DisplayValue1 = cfg.ValueType == 0 ? UIStringUtils.FormatIntUnitByLanguage(value1) : string.Format("{0}%", value1 * 100),
                        DisplayValue2 = cfg.ValueType == 0 ? UIStringUtils.FormatIntUnitByLanguage(value2) : string.Format("{0}%", value2 * 100),
                        Icon = cfg.Icon,
                        Desc = LocalizationMgr.Get(cfg.Name),
                    };
                }

                

                if (string.IsNullOrEmpty(cfg.Icon))
                    D.Warning?.Log($"属性{cfg.Id} Icon不存在！");
                
                propertyDatas.Add(propertyData);
            }

            return propertyDatas;
        }
    }
}
