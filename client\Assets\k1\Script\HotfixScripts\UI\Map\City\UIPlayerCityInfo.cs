﻿using Common;
using cspb;
using Cysharp.Threading.Tasks;
using DeepUI;
using Game.Data;
using Logic;
using TFW;
using TFW.Localization;
using TFW.UI;
using UnityEngine;
using UnityEngine.EventSystems;
using EventTriggerType = TFW.EventTriggerType;

namespace UI
{

    /// <summary>
    /// 玩家城堡信息数据
    /// </summary>
    public class UIPlayerCityInfoData
    {
        /// <summary>
        /// 玩家实体Id
        /// </summary>
        public long entityId;
    }


    /// <summary>
    /// 玩家城堡信息
    /// <AUTHOR>
    /// @date 2021/11/16 20:44:38
    /// @ver 1.0
    /// </summary>
    [Popup("Map/ui4104MyCity", true, true)]
    public partial class UIPlayerCityInfo : BasePopup, IMapZoomChangeHandler
    {

        #region 属性信息数据

        /// <summary>
        /// 设置点击空白区域可以关闭界面
        /// </summary>
        protected internal override bool PopBackEnabled => true;

        /// <summary>
        /// 玩家城堡类型枚举
        /// </summary>
        private PlayerCityTypeEnum cityType;


        /// <summary>
        /// 收藏按钮
        /// </summary>
        [PopupField("centre_other/animation/ContentDetail/RightTopBtns/StarBtn")]
        private GameObject _starBtn;

        /// <summary>
        /// 分享按钮
        /// </summary>
        [PopupField("centre_other/animation/ContentDetail/RightTopBtns/ShareBtn")]
        private GameObject _shareBtn;

        /// <summary>
        /// 玩家头像
        /// </summary>
        [PopupField("centre_other/animation/ContentDetail/Info/HeadIcon/Head_k1")]
        private GameObject _headObj;

        /// <summary>
        /// 玩家旗帜
        /// </summary>
        [PopupField("centre_other/animation/ContentDetail/Info/Flag")]
        private TFWImage _flagImg;

        /// <summary>
        /// 玩家城堡血量进度条
        /// </summary>
        [PopupField("centre_other/animation/ContentDetail/Info/Slider")]
        private TFWSlider _hpSlider;

        /// <summary>
        /// 玩家城堡血量增加按钮
        /// </summary>
        [PopupField("centre_other/animation/ContentDetail/Info/Slider/AddBtn")]
        private GameObject _hpAddBtn;

        /// <summary>
        /// 玩家城堡等级文本
        /// </summary>
        [PopupField("centre_other/animation/ContentDetail/Info/Level/Text")]
        private TFWText _levelText;

        /// <summary>
        /// 玩家名字文本
        /// </summary>
        [PopupField("centre_other/animation/ContentDetail/Info/Name/Text")]
        private TFWText _nameText;

        /// <summary>
        /// 聊天按钮
        /// </summary>
        [PopupField("centre_other/animation/ContentDetail/Info/Chat")]
        private GameObject _chatBtn;

        /// <summary>
        /// 玩家城堡实体
        /// </summary>
        private EntityInfo playerCity;

        //玩家信息
        private UnifyPlayerInfo playerInfo;

        /// <summary>
        /// 玩家城堡信息数据
        /// </summary>
        private UIPlayerCityInfoData data;

        /// <summary>
        /// 其他玩家Icon widget
        /// </summary>
        private PlayerIconWidget playerIconWidget;
        /// <summary>
        /// 玩家自己Icon widget 
        /// </summary>
        private SelfIconWidget selfIconWidget;

        /// <summary>
        /// 当前城防值数量
        /// </summary>
        private long cityDefenseCount;

        #endregion


        #region 数据初始化

        /// <summary>
        /// 初始化脚本
        /// </summary>
        protected override async void OnInit()
        {
            playerIconWidget = new PlayerIconWidget(_headObj);
            playerIconWidget.AddListener(EventTriggerType.Click, OnClickPlayerHeadBtn);

            selfIconWidget = new SelfIconWidget(_headObj);
            selfIconWidget.AddListener(EventTriggerType.Click, OnClickPlayerHeadBtn);

            //初始化城市按钮文本
            InitPlayerCityButtonText();
            //初始化侦查消耗
            InitScoutPhysical();
            //初始化援助
            InitAllianceSupport();
            //初始化集结信息
            InitRallyTime();
            //初始化国王技能信息
            await InitKillSkillInfo();
            //初始化光明道具
            //InitBrightBattleItemInfo();
            // //万圣节变装
            // InitHalloween();
        }

        /// <summary>
        /// 当界面第一次打开时调用
        /// </summary>
        protected internal override void OnOpenStart()
        {
            //添加事件监听
            AddListener();

            //刷新数据显示
            OnShowStart();
        }

        /// <summary>
        /// 界面销毁时调用
        /// </summary>
        protected internal override void OnCloseStart()
        {
            //反注册LOD事件监听
            MapZoomChangeRegistry.Unregister(this);

            //移除事件监听
            RemoveListener();

            ////清理集结信息
            //ClearRallyInfo();

            //清理援助信息
            ClearReinforceInfo();
        }

        /// <summary>
        /// 界面每次显示时调用
        /// </summary>
        protected internal override void OnShowStart()
        {

            data = Data as UIPlayerCityInfoData;
            if (data == null)
                return;

            //注册LOD事件监听
            MapZoomChangeRegistry.Register(this);

            //刷新玩家城市信息
            UpdatePlayerCityInfo();

            //刷新页面信息数据
            UpdateShowPanelInfo().Forget();
        }

        /// <summary>
        /// 数据清理销毁
        /// </summary>
        protected override void OnDispose()
        {
            //清理集结信息
            ClearRallyInfo();
            base.OnDispose();
        }

        /// <summary>
        /// 添加按钮事件监听
        /// </summary>
        private void AddListener()
        {
            EventMgr.RegisterEvent(TEventType.UpdateHalloweenVisible, Refresh, this);
            //绑定收藏按钮事件
            BindClickListener(_starBtn, OnClickStarBtn);
            //绑定分享按钮事件
            BindClickListener(_shareBtn, OnClickShareBtn);
            //绑定聊天按钮事件
            BindClickListener(_chatBtn, OnClickChatBtn);
            //绑定血量增加按钮事件
            BindClickListener(_hpAddBtn, OnClickAddHpBtn);

            //添加玩家城堡按钮监听
            AddPlayerCityButtonListener();
        }

        /// <summary>
        /// 移除事件监听
        /// </summary>
        private void RemoveListener()
        {
            //移除绑定收藏按钮事件
            RemoveClickListener(_starBtn);
            //移除绑定分享按钮事件
            RemoveClickListener(_shareBtn);
            //移除绑定聊天按钮事件
            RemoveClickListener(_chatBtn);
            //移除绑定血量增加按钮事件
            RemoveClickListener(_hpAddBtn);

            //移除玩家城堡按钮监听
            RemovePlayerCityButtonListener();
        }


        #endregion


        #region 数据显示刷新

        /// <summary>
        /// 刷新玩家城市信息
        /// </summary>
        private void UpdatePlayerCityInfo()
        {
            if (data == null
                || data.entityId <= 0)
                return;

            playerCity = LMapEntityManager.I.GetEntityInfo(data.entityId);


            if (playerCity != null)
            {
                //刷新城堡类型
                if (LPlayer.I.PlayerID == playerCity.owner.ID)
                    cityType = PlayerCityTypeEnum.SELF;
                else if (LPlayer.I.IsPlayerSelfUnion(playerCity.owner.unionID))
                    cityType = PlayerCityTypeEnum.ALLY;
                else if (LPlayer.I.IsPlayerSelfLegion(playerCity.owner.LegionId))
                    cityType = PlayerCityTypeEnum.LEGION;
                else if (LPlayer.I.IsPlayerSelfFaction(playerCity.owner.factionId))
                    cityType = PlayerCityTypeEnum.FACTION;
                else
                    cityType = PlayerCityTypeEnum.ENEMY;

                //请求获取最新的数据进行刷新显示
                LPlayer.I.TargetPlayerBaseReq(playerCity.owner.ID, true, true);
            }
            else
            {
                Close();
            }
        }



        /// <summary>
        /// 刷新显示玩家信息数据
        /// </summary>
        private async UniTask UpdateShowPlayerInfo()
        {

            ////是否为自己
            //var isSelf = playerInfo != null ? playerInfo.ID == LPlayer.I.PlayerID
            //    : playerCity.owner.ID == LPlayer.I.PlayerID;

            //刷新头像显示
            if (cityType == PlayerCityTypeEnum.SELF)
            {
                selfIconWidget.RefreshData();

            }
            else
            {
                if (playerInfo != null)
                {
                    playerIconWidget.InitData(playerInfo.head);
                    if (playerCity != null)
                    {
                        if ((playerCity.owner.sId != LPlayer.I.ServerId || LPlayer.I.IsCrossServer) && LPlayer.I.NotGetCrossServerPlayerInfo)
                        {
                            playerIconWidget.RemoveListener(EventTriggerType.Click);
                        }
                        else
                        {
                            playerIconWidget.AddListener(EventTriggerType.Click, OnClickPlayerHeadBtn);
                        }
                    }
                }
                else
                {
                    if (playerCity != null)
                    {
                        if ((playerCity.owner.sId != LPlayer.I.ServerId || LPlayer.I.IsCrossServer) && LPlayer.I.NotGetCrossServerPlayerInfo)
                        {
                            playerIconWidget.RemoveListener(EventTriggerType.Click);
                        }
                        else
                        {
                            playerIconWidget.AddListener(EventTriggerType.Click, OnClickPlayerHeadBtn);
                        }
                    }
                }
            }

            if (playerCity != null)
            {
                //刷新旗子显示
                var flagConfig = await Cfg.C.CNationFlag.GetConfigAsync(playerCity.owner.nationalFlag);
                if (flagConfig != null)
                    UITools.SetImage(_flagImg, flagConfig.DisplayKey, "Flag");
            }

            //刷新血量显示
            long totalTime = 0;
            if (playerInfo != null)
            {
                if (playerInfo.cityArmorRecoverTs == 0)
                    totalTime = (long)(GameData.I.CityDefenseData.MaxCityDefine * GameData.I.CityDefenseData.RecoverInterval * 1000);
                else
                    totalTime = (long)(GameData.I.CityDefenseData.MaxCityDefine * GameData.I.CityDefenseData.RecoverInterval * 1000) + GameTime.Time - playerInfo.cityArmorRecoverTs;
            }

            cityDefenseCount = totalTime / (long)(GameData.I.CityDefenseData.RecoverInterval * 1000);
            var isShowAddHpBtn = true;
            if (cityDefenseCount >= GameData.I.CityDefenseData.MaxCityDefine)
            {
                cityDefenseCount = (int)GameData.I.CityDefenseData.MaxCityDefine;
                isShowAddHpBtn = false;
            }
            if (playerInfo == null)
            {
                cityDefenseCount = playerCity.PlayerCity.armor;
            }
            _hpSlider.maxValue = GameData.I.CityDefenseData.MaxCityDefine;
            _hpSlider.value = cityDefenseCount;
            //显隐增加城防值，血量的按钮
            _hpAddBtn.SetActive(cityType == PlayerCityTypeEnum.SELF && isShowAddHpBtn);

            if (playerCity != null)
            {
                //跨服玩家从新录入城防值
                if (LPlayer.I.ServerId != playerCity.owner?.sId)
                {
                    cityDefenseCount = playerCity.PlayerCity.armor;
                    _hpSlider.value = cityDefenseCount;
                }
                //显示城堡等级
                _levelText.text = $"{LocalizationMgr.Get("Skill_title_Level")}{playerCity.PlayerCity.level}";
                string serverId = $"";
                if (playerCity.owner?.sId > 0)
                    serverId = $"<color=#FFCE4A>[S{playerCity.owner?.sId}]</color>";

                //显示玩家名字
                if (cityType == PlayerCityTypeEnum.SELF)
                {
                    if (LPlayer.I.IsPlayerInUnion())
                        _nameText.text = $"<color=#FFCE4A>[S{LPlayer.I.ServerId}]</color>[<color=#9ECAFF>{LAllianceMgr.I.GetUnionNickName()}</color>] {LPlayer.I.PlayerName}";
                    else
                        _nameText.text = $"<color=#FFCE4A>[S{LPlayer.I.ServerId}]</color>{LPlayer.I.PlayerName}";
                }
                else if (this.playerCity.GetIsAlly())
                {
                    //自己联盟的
                    var unionName = string.IsNullOrEmpty(playerCity.owner?.unionNickName) ? "" : $"[<color=#FFD675FF>{playerCity.owner.unionNickName}</color>]";
                    _nameText.text = $"{serverId}{unionName}<color=#4095ff>{playerCity.owner.name}</color>";
                }
                else
                {
                    var unionName = string.IsNullOrEmpty(playerCity.owner?.unionNickName) ? "" : $"[<color=#FFD675FF>{playerCity.owner.unionNickName}</color>]";
                    _nameText.text = $"{serverId}{unionName} <color=#fb9b9e>{playerCity.owner.name}</color>";
                }
            }
            _chatBtn.SetActive(true);

            if (cityType == PlayerCityTypeEnum.SELF)
            {
                //如果跨服了（要屏蔽收藏和分享功能）
                //自己如果跨服了打开谁的界面都不显示这些按钮
                if (LCrossServer.I.CurrSceneType != SceneType.SceneTypeKvk)
                {
                    _starBtn.SetActive(!LPlayer.I.IsCrossServer);
                    _shareBtn.SetActive(!LPlayer.I.IsCrossServer);
                    // _chatBtn.SetActive(!LPlayer.I.IsCrossServer);
                }
                else
                {
                    _starBtn.SetActive(true);
                    _shareBtn.SetActive(true);
                    // _chatBtn.SetActive(true);
                }
            }
            else
            {
                if (playerCity != null)
                {
                    //自己查看的其它玩家是否是跨服的
                    if (playerCity.owner.sId != LPlayer.I.ServerId)
                    {
                        if (LCrossServer.I.CurrSceneType != SceneType.SceneTypeKvk)
                        {
                            _starBtn.SetActive(false);
                            _shareBtn.SetActive(false);
                            // _chatBtn.SetActive(false);
                        }
                        else
                        {
                            _starBtn.SetActive(true);
                            _shareBtn.SetActive(true);
                            // _chatBtn.SetActive(true);
                        }
                    }
                }
            }
            // await UpdateHalloweenUI();
        }


        /// <summary>
        /// 刷新显示页面信息
        /// </summary>
        private async UniTask UpdateShowPanelInfo()
        {
            //刷新城堡按钮显示
            await UpdatePlayerCityButtonVisible();

            //刷新集结信息
            ResetRallyTime();

            //刷新玩家信息
            if (LCrossServer.I.CurrSceneType == SceneType.SceneTypeWMap)
            {
                await UpdateShowPlayerInfo();
            }
            else
            {
                if (playerCity != null)
                {
                    LPlayer.I.ReqMapPlayerUnifyInfo(playerCity.owner.ID, playerCity.owner.sId);
                }
            }

            //刷新国王技能信息
            UpdateKingSkillInfo();
            //刷新光明道具
            //UpdateBrightBattleInfo();
        }


        #endregion

        #region 地图Zoom更改时数据刷新

        /// <summary>
        /// 地图Zoom更改时自动调用
        /// </summary>
        /// <param name="lod"></param>
        /// <param name="height"></param>
        public void OnZoomChanged(float zoom)
        {
            //关闭当前界面
            Close();
        }

        #endregion


        #region 事件处理


        /// <summary>
        /// 点击玩家头像按钮
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        private void OnClickPlayerHeadBtn(GameObject arg0, PointerEventData arg1)
        {
            if (playerCity != null)
            {
                if (cityType == PlayerCityTypeEnum.SELF)
                {
                    //if (!GuideMgr.I.IsGuideDone) 
                    //    return;

                    Close();
                    PopupManager.I.ShowLayer<UIPlayerMain>();
                }
                else
                {
                    Close();
                    var popData = new UIOtherPlayerIdData();
                    popData.PlayerID = playerCity.owner.ID;
                    LPlayer.I.TargetPlayerBaseTODO(popData.PlayerID, playerCity.owner.sId, (info) =>
                    {
                        if (info != null)
                        {
                            popData.playerInfo = info;
                            PopupManager.I.ShowLayer<UIOtherPlayerData>(popData);
                        }
                    });
                }
            }
        }

        /// <summary>
        /// 点击收藏按钮
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        private void OnClickStarBtn(GameObject arg0, PointerEventData arg1)
        {
            if (playerInfo != null)
            {
                var pos = Formula.WorldToSharePosition(this.playerCity.GetPosition());
                UIFavoritesTipsPanel.Show(pos.x, pos.z, playerInfo.head.name);
            }
        }

        /// <summary>
        /// 点击分享按钮
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        private void OnClickShareBtn(GameObject arg0, PointerEventData arg1)
        {
            if (!LPlayer.I.IsPlayerInUnion())//没有加入联盟
            {
                PopupManager.I.ClosePopup<UIRallyBattleNpc>();
                //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.ALLIANCE,
                //    new UIChatData() { CurrentTabKey = GameData.I.MainData.CurrMenuType, CurrChatTab = ChatTabs.Alliance });
                return;
            }

            SendShareCoordinateToUnionChannel();
        }

        /// <summary>
        /// 发送坐标分享消息到联盟频道
        /// </summary>
        private void SendShareCoordinateToUnionChannel()
        {
            if (playerInfo == null)
            {
                D.Debug?.Log("m_PlayerInfo is null!");
                return;
            }

            if (playerCity == null)
            {
                D.Debug?.Log("m_PlayerCity is null!");
                return;
            }

            var pos = Formula.WorldToSharePosition(this.playerCity.GetPosition());
            var x = pos.x;
            var z = pos.z;

            var name = playerInfo.head.name;
            var level = playerInfo.cityLevel;

            //分享{0}级{1}
            var unitName = string.Format(LocalizationMgr.GetUIString("Share_chatdes_1"), level, name);
            PopupManager.I.ShowPanel<UIShare>(new UIShareData()
            {
                chatContent = UIChatHelper.I.GetShareCoordinateToAllianceChannel
                (
                    name, level,
                    ShareDataTypeEnum.PlayerCastle,
                    unitName,
                    x,
                    z
                )
            });

        }


        /// <summary>
        /// 点击聊天按钮
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        private void OnClickChatBtn(GameObject arg0, PointerEventData arg1)
        {
            if (playerInfo != null)
            {
                if (UIChat.StartChatWithSomeone(playerInfo))
                {
                    Close();
                }
            }
        }

        /// <summary>
        /// 点击增加血量按钮
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        private void OnClickAddHpBtn(GameObject arg0, PointerEventData arg1)
        {
            if (cityDefenseCount == GameData.I.CityDefenseData.MaxCityDefine)
            {
                UITools.PopTips(LocalizationMgr.Get("Castle_Durability_Full_Tips"));
                return;
            }
            PopupManager.I.ShowPanel<UICityDefenseRecover>("UICityDefenseRecover");
            Close();
        }

        [PopupEvent(TEventType.UpdateMapPlayerInfoPanel)]
        private async UniTaskVoid OnUpdateMapPlayerInfo(object[] objs)
        {
            if (objs == null || objs.Length == 0 || playerCity == null)
            {
                return;
            }
            var info = objs[0] as UnifyPlayerInfo;
            if (info == null)
                return;
            if (playerCity.owner.ID == info.ID)
            {
                playerInfo = info;
                await UpdateShowPlayerInfo();
                return;
            }
        }

        /// <summary>
        /// 刷新玩家信息数据
        /// </summary>
        /// <param name="objs"></param>
        [PopupEvent(TEventType.PlayerTargetPlayerBase)]
        private async UniTaskVoid OnUpdatePlayerInfo(object[] objs)
        {
            if (objs == null || objs.Length == 0)
            {
                Close();
                return;
            }

            //刷新玩家数据信息
            playerInfo = objs[0] as UnifyPlayerInfo;
            //刷新界面显示
            await UpdateShowPanelInfo();
        }
        #endregion

    }
    /// <summary>
    /// 玩家城堡类型枚举
    /// </summary>
    public enum PlayerCityTypeEnum
    {
        /// <summary>
        /// 自己
        /// </summary>
        SELF,
        /// <summary>
        /// 盟友
        /// </summary>
        ALLY,
        /// <summary>
        /// 敌人
        /// </summary>
        ENEMY,
        /// <summary>
        /// 军团盟友
        /// </summary>
        LEGION,
        /// <summary>
        /// 阵营盟友
        /// </summary>
        FACTION
    }
}
