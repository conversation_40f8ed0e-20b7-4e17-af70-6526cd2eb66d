﻿#if USE_ADDRESSABLE
using Cysharp.Threading.Tasks;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Security.Policy;
using System.Threading;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UnityEngine.ResourceManagement.AsyncOperations;
using UnityEngine.ResourceManagement.ResourceLocations;
using UnityEngine.ResourceManagement.ResourceProviders;
using UnityEngine.UI;
using Object = UnityEngine.Object;

 

namespace TFW
{
    internal static class ResourceMgr2
    {
        static Dictionary<string, ResourceMgr2LoadData> _pendingAsset = new Dictionary<string, ResourceMgr2LoadData>();
 
        private static bool EditorLog=false;//资源加载  查找问题所用 打包关掉
 

#if UNITY_EDITOR
        /// <summary>
        /// 编辑器模式下修正Shader
        /// </summary>
        /// <param name="go"></param>
        [UnityEditor.MenuItem("Bundle/Fix Shader")]
        public static void FixShader()
        {
            foreach (var item in GameObject.FindObjectsOfType<Renderer>())
            {
                var materials = item.sharedMaterials;
                foreach (var m in materials)
                {
                    if (m != null)
                    {
                        var shader = Shader.Find(m.shader.name);
                        if (shader == null)
                        {
                            UnityEngine.Debug.LogError($"FixShader: {m.name} 找不到shader {m.shader.name}");
                        }
                        m.shader = shader;
                    }
                }
            }

            foreach (var item in GameObject.FindObjectsOfType<ParticleSystemRenderer>())
            {
                var mat = item.sharedMaterial;
                if (mat != null)
                {
                    var shader = Shader.Find(mat.shader.name);
                    if (shader == null)
                    {
                        UnityEngine.Debug.LogError($"FixShader: {mat.name} 找不到shader {mat.shader.name}");
                    }
                    mat.shader = shader;
                }
            }
        }



#endif

//        public static async UniTask AddResourceToAddressUniTask(string fullPath)
//        {

//#if UNITY_EDITOR
//            UnityEditor.AddressableAssets.Settings.AddressableAssetSettings settings = UnityEditor.AddressableAssets.AddressableAssetSettingsDefaultObject.Settings;

//            // 获取默认的Group
//            UnityEditor.AddressableAssets.Settings.AddressableAssetGroup defaultGroup = settings.DefaultGroup;

//            string guid = UnityEditor.AssetDatabase.AssetPathToGUID(fullPath);

//            if (!string.IsNullOrEmpty(guid))
//            {
//                // 加载要添加到默认Group的资源 
//                settings.CreateOrMoveEntry(guid, defaultGroup);

//                // 刷新Addressable Settings
//                settings.SetDirty(UnityEditor.AddressableAssets.Settings.AddressableAssetSettings.ModificationEvent.GroupAdded, defaultGroup, true, true);
//                UnityEditor.AssetDatabase.Refresh();

//                await Addressables.InitializeAsync();
//            }
//#else
//            await  UniTask.CompletedTask;
//#endif


//        }

//        public static void AddResourceToAddressable(string fullPath)
//        {

//#if UNITY_EDITOR
//            UnityEngine.Debug.LogWarning($"同步加载资源 后续需要处理改成异步:{fullPath}");

//            UnityEditor.AddressableAssets.Settings.AddressableAssetSettings settings = UnityEditor.AddressableAssets.AddressableAssetSettingsDefaultObject.Settings;

//            // 获取默认的Group
//            UnityEditor.AddressableAssets.Settings.AddressableAssetGroup defaultGroup = settings.DefaultGroup;

//            string guid = UnityEditor.AssetDatabase.AssetPathToGUID(fullPath);

//            if (!string.IsNullOrEmpty(guid))
//            {
//                // 加载要添加到默认Group的资源 
//                settings.CreateOrMoveEntry(guid, defaultGroup);

//                // 刷新Addressable Settings
//                settings.SetDirty(UnityEditor.AddressableAssets.Settings.AddressableAssetSettings.ModificationEvent.GroupAdded, defaultGroup, true, true);
//                UnityEditor.AssetDatabase.Refresh();

//                Addressables.InitializeAsync().WaitForCompletion();
//            }
//#else
          
//#endif


//        }

        public static string FixPath(string assetPath)
        {
            if (!string.IsNullOrEmpty(assetPath))
            {
                var ret = assetPath;
                if (assetPath.StartsWith("Assets/Res/Config/"))
                {
                    ret = assetPath.Replace("Assets/Res/Config/", "Assets/Res/config/");
                }
                //else if (assetPath.StartsWith("Assets/K1/"))
                //{
                //    ret = assetPath.Replace("Assets/K1/", "Assets/k1/");
                //}
                else if (assetPath.Contains(@"\"))
                {
                    ret = assetPath.Replace(@"\", "/");
                }

                return ret;
            }
            else
                return string.Empty;
        }

        /// <summary>
        /// 检查Resource文件是否存在
        /// </summary>
        /// <param name="assetPath">资源路径</param>
        public static bool Exists(string assetPath)
        {
            return Exists<Object>(assetPath);
        }

        public static bool Exists<T>(string assetPath)
        {
            assetPath = FixPath(assetPath);

            if (string.IsNullOrEmpty(assetPath))
            {
               return false;
            }

            foreach (var l in Addressables.ResourceLocators)
            {
                IList<IResourceLocation> locs;
                if (l.Locate(assetPath, typeof(T), out locs))
                    return true;
            }
            return false;
            //return true;
            //return Assets.Exists(assetPath);
        }

#region 加载资产泛型接口，需配套 UnloadAsset 释放资产
        /// <summary>
        /// 加载资产，需配套 Release 释放资产
        /// </summary>
        public static T LoadAsset<T>(string assetKey) where T : Object
        {
            assetKey = FixPath(assetKey);

            if (string.IsNullOrEmpty(assetKey))
            {
                ResourceMgr.DebugLog($"LoadResources: Empty path, T is {typeof(T).Name}");
                return null;
            }

#if UNITY_WEBGL
            UnityEngine.Debug.LogError($"同步！！！（需要修改）请求加载资产实例 {assetKey}");
#endif

            if (_pendingAsset != null && _pendingAsset.TryGetValue(assetKey.ToLower(), out var opHandle))
            {
                var typedHandle = opHandle.operationHandle.Convert<T>();
                opHandle.AddRef();
                return typedHandle.WaitForCompletion();
            }

            try
            { 
                if (!Exists(assetKey))
                {
                    UnityEngine.Debug.LogWarning($"资产找不到 {assetKey}");
                    return null ;
                }

                var op = Addressables.LoadAssetAsync<T>(assetKey);
                _pendingAsset.Add(assetKey.ToLower(), new ResourceMgr2LoadData(assetKey,typeof(T).Name,op));
                var ret = op.WaitForCompletion(); 
                return ret;
            }
            catch (Exception e)
            {
                UnityEngine.Debug.LogError(e.Message);
                return null;
            }
        }

        

        /// <summary>
        /// 异步加载资产，需配套 Release 释放资产
        /// </summary>
        public static async UniTask<T> LoadAssetAsync<T>(string assetKey, CancellationToken cancellationToken = default) where T : Object
        {
            assetKey = FixPath(assetKey);

            if (string.IsNullOrEmpty(assetKey))
            {
                ResourceMgr.DebugLog($"LoadResources: Empty path, T is {typeof(T).Name}");
                return null;
            } 

            if (_pendingAsset != null && _pendingAsset.TryGetValue(assetKey.ToLower(), out var opHandle))
            {
                try
                {
                    var typedHandle = opHandle.operationHandle.Convert<T>();
                    opHandle.AddRef();
                    return await typedHandle;
                }
                catch (Exception ex)
                {
                    ResourceMgr.DebugLog($"LoadResources: Exception{ex.Message}, T is {typeof(T).Name}");
                    return null;
                }
            }

            try
            {
                //if (!Exists(assetKey))
                //{
                //    await AddResourceToAddressUniTask(assetKey);
                //}
                if (!Exists(assetKey))
                {
                    UnityEngine.Debug.LogWarning($"资产找不到 {assetKey}");
                    return null;
                }

                var op = Addressables.LoadAssetAsync<T>(assetKey);
                _pendingAsset.Add(assetKey.ToLower(),new ResourceMgr2LoadData(assetKey, typeof(T).Name, op));
                return await op.WithCancellation(cancellationToken);
            }
            catch (Exception e)
            {
                UnityEngine.Debug.LogError(e.Message);
                return null;
            }
        }
#endregion

#region TextAsset 相关接口，直接返回读取后的数据，无需手动释放资产
        /// <summary>
        /// 加载 TextStream，无需释放资产
        /// </summary>
        public static Stream LoadTextStream(string assetPath)
        {
            var textAsset = LoadAsset<TextAsset>(assetPath);

            if (textAsset == null) { return null; }

            var bytes = textAsset.bytes;
            
            return new MemoryStream(bytes);
        }

        /// <summary>
        /// 异步加载 TextStream，无需释放资产
        /// </summary>
        public static async UniTask<Stream> LoadTextStreamAsync(string assetPath, CancellationToken cancellationToken = default)
        {
            var textAsset = await LoadAssetAsync<TextAsset>(assetPath, cancellationToken);

            if (textAsset == null) { return null; }

            var bytes = textAsset.bytes;

            Release(assetPath);

            return new MemoryStream(bytes);
        }

        public static async UniTask<string> LoadAllTextLineAsync(string assetPath)
        {
            using (var stream = await LoadTextStreamAsync(assetPath))
            {
                if (stream != null)
                {
                    using (var sr = new StreamReader(stream))
                    {
                        return await sr.ReadToEndAsync();
                    }
                }
                else
                {
                    UnityEngine.Debug.LogError($"[Text] 【{assetPath}】 LoadTextLines Failed ");
                }
            }

            return string.Empty;
        }

        //public static async IAsyncEnumerable<string> LoadTextLinesAsync(string assetPath, [EnumeratorCancellation] CancellationToken cancellationToken = default)
        //{
        //    using (var stream = await LoadTextStreamAsync(assetPath, cancellationToken))
        //    {
        //        if (stream != null)
        //        {
        //            using (var sr = new StreamReader(stream))
        //            {
        //                string line;
        //                while ((line = await sr.ReadLineAsync()) != null)
        //                {
        //                    yield return line;
        //                }
        //            }
        //        }
        //        else
        //        {
        //            UnityEngine.Debug.LogError($"[Text] 【{assetPath}】 LoadTextLines Failed ");
        //        }
        //    }
        //}

        /// <summary>
        /// 异步加载资产字符串，并自动释放资产
        /// </summary>
        public static async UniTask<string> LoadTextAsync(string assetPath)
        {
            var textAsset = await LoadAssetAsync<TextAsset>(assetPath);

            if (textAsset == null) { return null; }

            var ret = textAsset.text;

            if (string.IsNullOrEmpty(ret))
            {
                var bytes = textAsset.bytes;
                if (bytes != null && bytes.Length > 0)
                {
                    ret = System.Text.Encoding.UTF8.GetString(bytes);
                }
            }

            Release(assetPath);

            return ret;
        }

        /// <summary>
        /// 加载资产字byte[]，并自动释放资产
        /// </summary>
        public static byte[] LoadTextBytes(string assetPath)
        {
            var textAsset = LoadAsset<TextAsset>(assetPath);

            if (textAsset == null) { return null; }

            var ret = textAsset.bytes;

            Release(assetPath);

            return ret;
        }

        /// <summary>
        /// 异步加载资产字byte[]，并自动释放资产
        /// </summary>
        public static async UniTask<byte[]> LoadTextBytesAsync(string assetPath, CancellationToken cancellationToken = default)
        {
            var textAsset = await LoadAssetAsync<TextAsset>(assetPath, cancellationToken);

            if (textAsset == null) { return null; }

            var ret = textAsset.bytes;

            Release(assetPath);

            return ret;
        }
#endregion

        public static async UniTask PreloadAsync(string assetKey)
        {
            //if (!Exists(assetKey))
            //{
            //    await AddResourceToAddressUniTask(assetKey);
            //}

            if (Exists(assetKey))
            { 
                var downloadHandle = Addressables.DownloadDependenciesAsync(assetKey, true);

                try
                {
                    await downloadHandle.ToUniTask();

                    if (downloadHandle.Status == AsyncOperationStatus.Succeeded)
                    {
                        Debug.Log($"资源 {assetKey} 的依赖项预加载成功。");
                    }
                    else
                    {
                        Debug.LogError($"资源 {assetKey} 的依赖项预加载失败：{downloadHandle.OperationException}");
                    }
                }
                catch (Exception ex)
                {
                    Debug.LogError($"预加载资源 {assetKey} 时发生异常：{ex.Message}");
                    throw;
                }
                finally
                {
                    Addressables.Release(downloadHandle);
                }
            }
        }

        /// <summary>
        /// 同步加载 GameObject 实例 
        /// </summary>
        [Obsolete("同步加载 后续不能使用")]
        public static GameObject LoadInstance(string assetKey, Transform parent = null)
        {
            assetKey = FixPath(assetKey);

            if (string.IsNullOrEmpty(assetKey))
            {
                ResourceMgr.DebugLog($"LoadResources: Empty path {assetKey}");
                return null;
            }

#if UNITY_WEBGL
                UnityEngine.Debug.LogError($"同步！！！（需要修改）请求加载资产实例 {assetKey}"); 
#endif
           
            //if (_pendingInstance != null && _pendingInstance.TryGetValue(assetKey, out var opHandle))
            //{
            //    var typedHandle = opHandle.Convert<GameObject>();
            //    return typedHandle.WaitForCompletion();
            //}
             
            try
            {
                if (!Exists(assetKey))
                {
                    return null;
                    //AddResourceToAddressable(assetKey);
                }

                var op = Addressables.InstantiateAsync(assetKey, parent); 
                var ret = op.WaitForCompletion();

                return ret;
            }
            catch (Exception e)
            {
                UnityEngine.Debug.LogError(e.Message);
                return null;
            }
        }

        /// <summary>
        /// 加载 GameObject 实例，需配套 Release 释放资产
        /// </summary>
        public static async UniTask<GameObject> LoadInstanceAsync(string assetKey, Transform parent = null, CancellationToken cancellationToken = default)
        {
            //if (!Exists(assetKey))
            //{
            //    await AddResourceToAddressUniTask(assetKey);
            //}

            if (Exists(assetKey))
            {
                var obj = await LoadAssetAsync<GameObject>(assetKey, cancellationToken);

                if (obj != null)
                {
                    if (parent != null)
                    {
                        return GameObject.Instantiate(obj, parent);
                    }
                    else
                    {
                        return GameObject.Instantiate(obj);
                    }
                }
                else
                {
                    ResourceMgr.DebugLog($"LoadResources: 资源不存在！ {assetKey}");
                    return null;
                }
            }
            else
            {
                ResourceMgr.DebugLog($"LoadResources: 资源不存在！ {assetKey}");
                return null;
            }
            
//            assetKey = FixPath(assetKey);

//            if (string.IsNullOrEmpty(assetKey))
//            {
//                ResourceMgr.DebugLog($"LoadResources: Empty path {assetKey}");
//                return null;
//            }

//#if UNITY_EDITOR
//            if (EditorLog)
//            {
//                UnityEngine.Debug.LogWarning($"请求加载资产实例 {assetKey}");
//            }
//#endif
//            //if (_pendingInstance != null && _pendingInstance.TryGetValue(assetKey, out var opHandle))
//            //{
//            //    var typedHandle = opHandle.Convert<GameObject>();
//            //    return typedHandle.WaitForCompletion();
//            //}

//            try
//            {
//                var op = Addressables.InstantiateAsync(assetKey, parent);
//                _pendingInstance[assetKey] = op;

//                return await op;
//            }
//            catch (Exception e)
//            {
//                UnityEngine.Debug.LogError(e.Message);
//                return null;
//            }
        }

        public static void ReleaseInstance(string assetKey)
        {
            Release(assetKey);
        }

        static float timeGC = 0f;
        public static void LateUpdate()
        {
            foreach (var item in _pendingAsset)
            {
                if (item.Value.CanRelease())
                {
                    var isValid = item.Value.operationHandle.IsValid();
                    if (isValid)
                    {
                        Addressables.Release(item.Value.operationHandle);
                    }

                 
                    if (EditorLog)
                    {
                        ResourceMgr.DebugLog($"请求释放资产 {item.Key} {item.Value.KeyType}, isValid {isValid}");
                    }

                    _pendingAsset.Remove(item.Key);

                    break;
                }
            }

#if UNITY_WEBGL
            timeGC += Time.deltaTime;
            if (timeGC > 360) //先临时 固定时间 GC 清理当前游戏 后续移除
            {
                timeGC = 0;
                Resources.UnloadUnusedAssets();
                System.GC.Collect();
            }
#endif
        }

        public static void ReleaseMemory()//收到内存警告 不在等待 直接删除
        {
            var keys= _pendingAsset.Keys.ToList();
            foreach (var key in keys) 
            {
                if (_pendingAsset[key]?.CanRelease(true)==true)
                {
                    if (_pendingAsset[key].operationHandle.IsValid())
                    {
                        Addressables.Release(_pendingAsset[key].operationHandle);
                    }

                  
 
                    if (EditorLog)
                    {
                        ResourceMgr.DebugLog($"请求释放资产 {key} {_pendingAsset[key].KeyType}");
                    }

                    _pendingAsset.Remove(key);

                }
            }

            Resources.UnloadUnusedAssets();
            System.GC.Collect();
        }


        public static void Release(string assetKey)
        {
            if (_pendingAsset.TryGetValue(assetKey.ToLower(), out var handle))
            {
                handle.RemoveRef(); 
            }
            else
            {
                if (EditorLog)
                {
                    ResourceMgr.DebugLog($"请求释放了一个未加载过的资产 {assetKey}");
                }
 
            }
        }

        /// <summary>
        /// 加载Sprite 需配套 Release 释放
        /// </summary>
        public static async UniTask<Sprite> LoadSpriteInstanceAsync(string assetPath, CancellationToken cancellationToken = default)
        {
            
            if (!Exists(assetPath)) 
            {
                ResourceMgr.DebugLog($"LoadSprite [{assetPath}] error. Asset is null.");
                return null; 
            }

            var asset = await LoadAssetAsync<Object>(assetPath, cancellationToken);

            if (asset == null)
            {
                ResourceMgr.DebugLog($"LoadSprite [{assetPath}] error. Asset is null.");
                return null;
            }

            Sprite ret = null;

            if (asset is Texture2D tex2D)
            {
                ret = Sprite.Create(tex2D, new Rect(0, 0, tex2D.width, tex2D.height), new Vector2(0.5f, 0.5f));
            }
            else if (asset is Sprite sp)
            {
                ret = sp;
            }
            else if (asset is GameObject go)
            {
                if (go.TryGetComponent<SpriteRenderer>(out var spriteRenderer))
                {
                    ret = spriteRenderer.sprite;
                }
                else if (go.TryGetComponent<Image>(out var img))
                {
                    ret = img.sprite;
                }
                else if (go.TryGetComponent<RawImage>(out var img2))
                {
                    var texture = (Texture2D)img2.mainTexture;
                    ret = Sprite.Create(texture,
                        new Rect(0, 0, texture.width, texture.height),
                        new Vector2(0.5f, 0.5f)); 
                }
            }
            else
            {
                ResourceMgr.DebugLog($"Invalid Image asset type [{asset.GetType()}], assetpath = {assetPath}");
            }
             
            Release(assetPath);
            return ret;
        }
        public static Color ColorBy(this Color color, float a)
        {
            return new Color(color.r, color.g, color.b, a);
        }

        /// <summary>
        /// 需配套 Release 释放
        /// </summary>
        /// <param name="image"></param>
        /// <param name="spriteAssetPath"></param>
        /// <param name="useNaticeSize"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public static async UniTask SetImageSpriteAsync(Image image, string spriteAssetPath, bool useNaticeSize = false, CancellationToken cancellationToken = default)
        {
            if (image == null)
            {
                ResourceMgr.DebugLog("Null target image." + spriteAssetPath);
                return;
            }

             //var previousColor = image.color;
            // image.color = Color.clear;
            //image.color=image.color.ColorBy(0);

            var spriteInstance = await LoadSpriteInstanceAsync(spriteAssetPath);

            if (image != null && spriteInstance!=null)
            {
                if (!cancellationToken.IsCancellationRequested)
                {
                    var previousSprite = image.sprite;
                    image.sprite = spriteInstance;

                    if (previousSprite != spriteInstance && useNaticeSize)
                    {
                        image.SetNativeSize();
                    }
                }
                // D.Debug?.Log($"path:{spriteAssetPath}--spriteInstance:{spriteInstance == null}");
                //image.color = previousColor;
                //image.color = image.color.ColorBy(1);
            }
        }

        /// <summary>
        /// 需配套 Release 释放
        /// </summary>
        /// <param name="rawImage"></param>
        /// <param name="textureAssetPath"></param>
        /// <param name="useNaticeSize"></param>
        /// <returns></returns>
        public static async UniTaskVoid SetRawImageTextureAsync(RawImage rawImage, string textureAssetPath, bool useNaticeSize = false, CancellationToken cancellationToken = default)
        {
            if (rawImage == null)
            {
                ResourceMgr.DebugLog("Null target image." + textureAssetPath);
                return;
            }

            //if (!Exists(textureAssetPath))
            //    await AddResourceToAddressUniTask(textureAssetPath);

            if (Exists(textureAssetPath))
            {
                D.Warning?.Log($"加载资源：{textureAssetPath}");
                //rawImage.color = rawImage.color.ColorBy(0);

                var textureInstance = await LoadTextureAssetAsync(textureAssetPath, cancellationToken);

                if (rawImage == null)
                {
                    ResourceMgr.DebugLog("Null target image." + textureAssetPath);
                    return;
                }

                var previousTexture = rawImage.texture;
                rawImage.texture = textureInstance;

                //rawImage.color = rawImage.color.ColorBy(1);

                if (previousTexture != textureInstance && useNaticeSize)
                {
                    rawImage.SetNativeSize();
                }
            }
            else
            {
                D.Warning?.Log($"资源不存在：{textureAssetPath}");
            }
        }

        /// <summary>
        /// 加载Texture，需配套 Release 释放
        /// </summary>
        public static async UniTask<Texture> LoadTextureAssetAsync(string assetPath, CancellationToken cancellationToken = default)
        {
            Texture ret = null;

            //if (!Exists(assetPath))
            //    await AddResourceToAddressUniTask(assetPath);

            if (Exists(assetPath))
            {
                var asset = await LoadAssetAsync<Object>(assetPath, cancellationToken);

                if (asset == null)
                {
                    ResourceMgr.DebugLog($"LoadSprite [{assetPath}] error. Asset is null.");
                    return null;
                }

               

                if (asset is Texture2D tex2D)
                {
                    ret = tex2D;
                }
                else if (asset is GameObject go)
                {
                    if (go.TryGetComponent<RawImage>(out var img))
                    {
                        ret = img.texture;
                    }
                }
                else
                {
                    ResourceMgr.DebugLog($"Invalid Image asset type [{asset.GetType()}], assetpath = {assetPath}");
                } 
            }

            return ret;
        }


        public class ResourceMgr2LoadData
        {
            public string mKey;

            public string KeyType;
            private int refCount;
            public AsyncOperationHandle operationHandle;
            public DateTime ReleaseTime { private set; get; }
            public ResourceMgr2LoadData(string _key, string _keyType, AsyncOperationHandle _operationHandle)
            {
                this.operationHandle = _operationHandle;
                this.mKey = _key;
                this.KeyType = _keyType;
                this.refCount = 1;
 
                if (EditorLog)
                {
                    ResourceMgr.DebugLog($"资源加载计数:{this.mKey}===>>>{this.refCount}");
                }
  
               
            }

            public void AddRef()
            {
                this.refCount++;

 
                if (EditorLog)
                {
                    ResourceMgr.DebugLog($"资源加载计数:{this.mKey}===>>>{this.refCount}");
                }
 
               
            }

#if UNITY_EDITOR
            const int cacheseconds = 0; // 60S后清除不用的缓存 
#else
            const int cacheseconds = 10; // 60S后清除不用的缓存 
#endif


            public void RemoveRef()
            {
                if (this.refCount == 1)
                {
                    this.ReleaseTime = DateTime.UtcNow.AddSeconds(cacheseconds);
                }
                this.refCount--;

 
                if (EditorLog)
                {
                    ResourceMgr.DebugLog($"资源加载计数:{this.mKey}<<<==={this.refCount}");
                }  
            }

            public bool CanRelease(bool ingoreTime = false)
            {
                if (refCount <= 0 && (ingoreTime || this.ReleaseTime < DateTime.UtcNow)) 
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
        }
    }


  
}

#endif