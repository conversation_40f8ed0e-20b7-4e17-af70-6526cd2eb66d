﻿using Common;
using Cysharp.Threading.Tasks;
using DeepUI;
using DG.Tweening;
using Game.Config;
using Game.Data;
using K1;
using K3;
using Logic;
using Logic.Alliance.Achievement;
using Public;
using TFW;
using TFW.UI;
using UI.Alliance;
using UI.Utils;
using UnityEngine;
using UnityEngine.EventSystems;

namespace UI
{

    /// <summary>
    /// 主界面 菜单栏联盟按钮
    /// </summary>
    public class UIMainMenuAlliance :MonoBehaviour
    {

        #region 属性字段信息

        public GameObject lockObj,unlockObj;

        public GameObject RedPoint;


        #endregion

        private void Start()
        {
            UIBase.AddRemoveListener(TFW.EventTriggerType.Click, gameObject, ClickMenuBtn);
        }

        /// <summary>
        /// 事件信息监听
        /// </summary>
        protected void OnEnable()
        { 
            //注册联盟礼物信息刷新
            EventMgr.RegisterEvent(TEventType.UnionGiftInfoAck, OnUpdateAllianceRedInfo, this); 
            //留言板
            EventMgr.RegisterEvent(TEventType.GetUnionMessageAck, OnUpdateAllianceRedInfo, this);
            //科技
            EventMgr.RegisterEvent(TEventType.UnionTechNtf, OnUpdateAllianceRedInfo, this);
            //圣坛
            EventMgr.RegisterEvent(TEventType.UnionAltarAck, OnUpdateAllianceRedInfo, this);
            //圣坛收集
            EventMgr.RegisterEvent(TEventType.UnionAltarCollectAck, OnUpdateAllianceRedInfo, this); 
            //聊天收到新消息
            EventMgr.RegisterEvent(TEventType.ChatReceiveNewMsg, OnUpdateAllianceRedInfo, this);
            //聊天消息已读
            EventMgr.RegisterEvent(TEventType.ChatMessageUnreadChanged, OnUpdateAllianceRedInfo, this); 
            //军情警报相关
            EventMgr.RegisterEvent(TEventType.AllianceMillitaryAlertRed, OnUpdateAllianceRedInfo, this);

            EventMgr.RegisterEvent(TEventType.AutoRallyRedDotRefush, OnUpdateAllianceRedInfo, this);

            //#region 军情警报相关
            //EventMgr.RegisterEvent(TEventType.InformationNtf, OnUpdateAllianceRedInfo, this);
            //EventMgr.RegisterEvent(TEventType.InformationDelNtf, OnUpdateAllianceRedInfo, this);
            //EventMgr.RegisterEvent(TEventType.AllianceRallyDataUpdate, OnUpdateAllianceRedInfo, this);
            //EventMgr.RegisterEvent(TEventType.AllianceRallyDelNtf, OnUpdateAllianceRedInfo, this);
            //EventMgr.RegisterEvent(TEventType.UnionFlagsInfo, OnUpdateAllianceRedInfo, this);
            //EventMgr.RegisterEvent(TEventType.UnionFortsInfo, OnUpdateAllianceRedInfo, this);
            //EventMgr.RegisterEvent(TEventType.ActiveShieldSucceed, OnUpdateAllianceRedInfo, this);
            //#endregion

            //联盟成就
            EventMgr.RegisterEvent(TEventType.AllianceAchievementQuestReward, OnUpdateAllianceRedInfo, this);
            EventMgr.RegisterEvent(TEventType.AllianceAchievementQuestUpdate, OnUpdateAllianceRedInfo, this);

            //联盟挑战(任务) 里程碑
            EventMgr.RegisterEvent(TEventType.ChestChangeNtf, OnUpdateAllianceRedInfo, this);
            EventMgr.RegisterEvent(TEventType.MilestoneRewardAck, OnUpdateAllianceRedInfo, this);

            // 联盟商店免费奖励
            EventMgr.RegisterEvent(TEventType.NewShopBuyAck, OnUpdateAllianceRedInfo, this);


            EventMgr.RegisterEvent(TEventType.OnUnlockConditionChanged, UpdateMenuOpenState, this);

            OnUpdateAllianceRedInfo(null);
            UpdateMenuOpenState(null);
        }

        /// <summary>
        /// 数据清理
        /// </summary>
        public   void OnDisable()
        {
            EventMgr.UnregisterEvent(this);
        }



        #region 数据刷新



        /// <summary>
        /// 刷新联盟红点信息
        /// </summary>
        public async UniTaskVoid UpdateAllianceRedInfo()
        {
            var num = 0;

            if (LGameRedPoint.I.OpenClientRed)
            {
                var isOpen = MainFunctionOpenUtils.ALllianceOpenState;

                if (!isOpen || LPlayer.I.UnionID == 0)
                {
                    RedPoint.SetActive(false);
                    return;
                }


                //有权限才添加 联盟申请的数量
                if (LAllianceMgr.I.HavePermission(AlliancePermissionEnum.UpdateJoinApply))
                {
                    var unionList = LAllianceMgr.I.GetUnionApplies();
                    num += unionList.Count;
                }

                if (AllianceMobilizeMgr.I.IsCanOpenMobilize(false))
                {
                    num += await GameData.I.AllianceMobilizeData.GetMobilizeRedCount();

                }

                //联盟礼物红点
                num += await GameData.I.AllianceGiftData.TotalRedPoint();

                //圣坛领取
                num += (LAllianceAltar.I.CanCollect ? 1 : 0);


                //联盟留言数据
                num += AllianceGameData.I.AlliancMsgBoardData.HaveSelfUnionNewMsgCount;// ( ? 1 : 0);



                ///私聊
                ///num += LSocialRedPoint.I.GetChatRedPointsCount();
                //联盟帮助条目
                num += AllianceGameData.I.AllianceHelp.GetHelpInfoCount();


                ///联盟成就
                //num += LAllianceAchievement.I.RedCount;
                //num += LAllianceNewAchievement.I.GetRedCount(); 联盟成就先不要，change by yujiawei


                num += await GameData.I.AllianceTechData.GetTechRedCount();


                num += await GameData.I.AllianceShopData.GetShopRedCount();


                var waropen = MainFunctionOpenUtils.IsOpenState((int)MetaConfig.UnlockUnionRally, true);
                var worldclose = UnlockMgr.I.CheckUnlock(UnlockFuncEnum.World);

                if (waropen && !worldclose)
                {
                    //军情警报
                    num += GameData.I.MainData.MassRedCount; //LAllianceMillitaryAlert.I.GetTotalRedPoint;
                    num += GameData.I.RallyData.IsNeedShowRallyRed ? 1 : 0;

                }
            }
            else
            {
                num = GetAllianceRedCount();
            }

            RedPoint.SetActive(num > 0); 
        }

        /// <summary>
        /// 获取联盟红点
        /// </summary>
        /// <returns></returns>
        public int GetAllianceRedCount()
        {
            return LGameRedPoint.I.GetRedCount(RedPointType.RedPointUnionGift, RedPointType.RedPointUnionHelp,
                RedPointType.RedPointUnionTech, RedPointType.RedPointUnionLand, RedPointType.RedPointUnionShop,
                RedPointType.RedPointUnionMobilize, RedPointType.RedPointUnionAchieve);
        }
 


        ///// <summary>
        ///// 退出菜单栏界面
        ///// </summary>
        ///// <param name="nextType"></param>
        //public override void ExitMenuPanel(MainMenuType nextType)
        //{
        //    base.ExitMenuPanel(nextType);

        //    //PopupManager.I.ClosePopup<Alliance.UIAllianceMenber>();
        //    PopupManager.I.ClosePopup<UIAllianceMain>();
        //    PopupManager.I.ClosePopup<UIAllianceWel_k1>();
        //    //PopupManager.I.ClosePopup<UIAllianceSettings>();
        //    PopupManager.I.ClosePopup<UIAllianceList_k1>();
        //    PopupManager.I.ClosePopup<UIAllianceReview>();
        //    //PopupManager.I.ClosePopup<UITask>();
        //    //PopupManager.I.ClosePopup<UIAllianceInfoPop>(false);

        //    PopupManager.I.ClosePopup<UIPlayerReName>();
        //    PopupManager.I.ClosePopup<UIAllianceInvite>();
        //    PopupManager.I.ClosePopup<UIAllianceWarTerritoryList>();
        //    PopupManager.I.ClosePopup<UIOtherPlayerData>(false);
        //    //PopupManager.I.ClosePopup<UIAllianceMessageBoard>(false);
        //    //PopupManager.I.ClearAllDialog();
        //}

        /// <summary>
        /// 刷新解锁状态
        /// </summary>
        public  void UpdateMenuOpenState(object[] objs)
        { 
            var isOpen = MainFunctionOpenUtils.ALllianceOpenState;
             
            lockObj.SetActive(!isOpen);
            unlockObj.SetActive(isOpen);
        }

        #endregion


        #region 事件监听

        

        /// <summary>
        /// 刷新联盟礼物信息数据
        /// </summary>
        /// <param name="objs"></param>
        private void OnUpdateAllianceRedInfo(object[] objs)
        {
            UpdateAllianceRedInfo().Forget();
        }
         


        /// <summary>
        /// 点击菜单按钮
        /// </summary>
        public void ClickMenuBtn(GameObject ga, PointerEventData po)
        { 
            if (!MainFunctionOpenUtils.ALllianceOpenState)
            {
                //if (lockAnim != null)
                //    lockAnim.Play();
                FloatTips.I.FloatMsg(UnlockMgr.I.GetUnlockDesc(UnlockFuncEnum.Alliance));
                
                return ;
            }

            if (LPlayer.I.UnionID == 0)
            {
                PopupManager.I.ShowDialog<UIAllianceWelcome>();
            }
            else
            {
                PopupManager.I.ShowLayer<UI.Alliance.UIAllianceMain>();
            }

        } 

        #endregion
         

    }
}
