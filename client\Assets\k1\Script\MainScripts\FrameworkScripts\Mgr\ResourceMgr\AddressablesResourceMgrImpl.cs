﻿#if USE_ADDRESSABLE
using System;
using System.IO;
using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.U2D;
using UnityEngine.UI;
using Object = UnityEngine.Object;

namespace TFW
{
    [CreateAssetMenu(menuName = "ResourceMgrImpl/AddressablesResourceMgrImpl")]
    public class AddressablesResourceMgrImpl : ScriptableObject, IResourceMgrImpl
    {
        public SpriteAtlas LoadBuiltRes(string tag)
        {
            throw new NotImplementedException();
        }

        public bool Exists(string assetPath)
        {
            return ResourceMgr2.Exists(assetPath);
        }

        public T LoadAsset<T>(string assetKey) where T : Object
        {
            return ResourceMgr2.LoadAsset<T>(assetKey);
        }

        public UniTask<T> LoadAssetAsync<T>(string assetKey) where T : Object
        {
            return ResourceMgr2.LoadAssetAsync<T>(assetKey);
        }

        public Stream LoadTextStream(string assetPath)
        {
            return ResourceMgr2.LoadTextStream(assetPath);
        }

        public UniTask<Stream> LoadTextStreamAsync(string assetPath)
        {
            return ResourceMgr2.LoadTextStreamAsync(assetPath);
        }

        public UniTask<byte[]> LoadTextBytesAsync(string assetPath)
        {
            return ResourceMgr2.LoadTextBytesAsync(assetPath);
        }

        public GameObject LoadInstance(string assetKey, Transform parent = null)
        {
            return ResourceMgr2.LoadInstance(assetKey, parent);
        }

        public UniTask<GameObject> LoadInstanceAsync(string assetKey, Transform parent = null)
        {
            return ResourceMgr2.LoadInstanceAsync(assetKey, parent);
        }

        public UniTask<Texture> LoadTextureAssetAsync(string assetPath)
        {
            return ResourceMgr2.LoadTextureAssetAsync(assetPath);
        }

        public void Release(string assetKey)
        {
            ResourceMgr2.Release(assetKey);
        }

        public void ReleaseInstance(string assetKey)
        {
            ResourceMgr2.ReleaseInstance(assetKey);
        }

        public UniTask<Sprite> LoadSpriteInstanceAsync(string assetPath)
        {
            return ResourceMgr2.LoadSpriteInstanceAsync(assetPath);
        }

        public UniTask SetImageSpriteAsync(Image image, string spriteAssetPath, bool useNaticeSize = false)
        {
            return ResourceMgr2.SetImageSpriteAsync(image, spriteAssetPath, useNaticeSize);
        }

        public UniTaskVoid SetRawImageTextureAsync(RawImage rawImage, string textureAssetPath, bool useNaticeSize = false)
        {
            return ResourceMgr2.SetRawImageTextureAsync(rawImage, textureAssetPath, useNaticeSize);
        }

        public (bool isUseDefaultAsset, T asset) LoadResource<T>(string path, string defaultAssetPath = null, bool fixExt = false,
            bool isClone = true) where T : Object
        {
            if (typeof(T) == typeof(GameObject) && isClone)
            {
                return (false, this.LoadInstance(path) as T);
            }
            else
            {
                return (false, this.LoadAsset<T>(path));
            }
        }

        public string LoadTextAutoUnload(string assetPath)
        {
            var ta = LoadAsset<TextAsset>(assetPath);
            var ret = ta.text;
            Release(assetPath);
            return ret;
        }

        public string LoadText(string assetPath)
        {
            return LoadTextAutoUnload(assetPath);
        }

        public byte[] LoadTextBytes(string assetPath)
        {
            return ResourceMgr2.LoadTextBytes(assetPath);
        }

        public void Unload(GameObject obj)
        {
            if (obj == null)
            {
                return;
            }

            Destroy(obj);
        }

        public void Unload(GameObject obj, float delayTime)
        {
            if (obj == null)
            {
                return;
            }

            Destroy(obj, delayTime);
        }

        public void Unload(string path, Object obj, bool isInstance = true, Action action = null)
        {
            Destroy(obj);
        }

        public void Unload(string path, bool fixExt = false)
        {
            this.Release(path);
        }

        public void UnloadText(string path)
        {
            this.Release(path);
        }

        public void LoadGameObjectAsync(string assetPath, string defaultAssetPath, UnityAction<string, GameObject> onOver)
        {
            this.LoadInstanceAsync(assetPath).ContinueWith(x => { onOver?.Invoke(assetPath, x); });
        }

        public void UnloadAssetBundle(string assetPath, bool unloadAll = false)
        {
        }

        public void DestroyGameObj(Object obj)
        {
            if (obj == null)
            {
                return;
            }

            if (obj is GameObject go)
            {
                Destroy(go);
            }
        }

        public void ReleaseInstance(GameObject instance)
        {
            if (instance == null)
            {
                return;
            }


            Destroy(instance); 
        }

        public void LateUpdate()
        {
            ResourceMgr2.LateUpdate();
        }

        public void ReleaseMemory()
        {
            ResourceMgr2.ReleaseMemory();
        }
    }
}
#endif