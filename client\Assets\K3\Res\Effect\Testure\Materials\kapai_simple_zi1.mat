%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: kapai_simple_zi1
  m_Shader: {fileID: 4800000, guid: d37d8e6bb58de2a4381e2657f226c908, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 3500
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BackTex:
        m_Texture: {fileID: 2800000, guid: 3b96c025d8aae1345bbb27fdf4686fee, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainAlphaTex:
        m_Texture: {fileID: 2800000, guid: add40394e92ee81468bf3e7edd20977c, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: a419b54525ffde843b166322890fc6e5, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OverlayTex:
        m_Texture: {fileID: 2800000, guid: fcdcfc699f058da49a4cf8c845c19002, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _BumpScale: 1
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _GlossMapScale: 1
    - _Glossiness: 0.5
    - _GlossyReflections: 1
    - _MainAlphaIntensity: 0
    - _MainAlphaRotation: 0
    - _Metallic: 0
    - _Mode: 0
    - _OcclusionStrength: 1
    - _OffsetDuration: 1
    - _OverlayAlpha: 0.409
    - _OverlayRotation: 77
    - _OverlaySpeed: 1
    - _Parallax: 0.02
    - _PauseDuration: 1
    - _PauseTime: 1.65
    - _SharpValue: 0.5
    - _Sharpness: 0.5
    - _SmoothValue: 1
    - _Smoothness: 0
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _SweepSpeed: 1
    - _UVSec: 0
    - _ZWrite: 1
    m_Colors:
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _MainAlphaColor: {r: 0.82622486, g: 0.3537736, b: 1, a: 1}
    - _MainAlphaScale: {r: 1, g: 1, b: 1, a: 1}
    - _MainAlphaSpeed: {r: 0, g: 0, b: 0, a: 0}
    - _OffsetDirection: {r: 1, g: 1, b: 0, a: 0}
    - _OffsetSpeed: {r: 0, g: 3, b: 0, a: 0}
    - _OverlayColor: {r: 0.8501048, g: 0.83647794, b: 1, a: 1}
    - _OverlayOffset: {r: 0, g: 0, b: 0, a: 0}
    - _OverlayScale: {r: 1, g: 0.73, b: 1, a: 1}
    - _OverlaySpeed: {r: 0, g: 2, b: 0, a: 0}
  m_BuildTextureStacks: []
