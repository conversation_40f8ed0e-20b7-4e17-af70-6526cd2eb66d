using Cfg.G;
using Common;
using DeepUI;
using DG.Tweening;
using Febucci.UI;
using Febucci.UI.Core;
using Game.Config;
using Game.Utils;
using K3;
using Logic;
using Public;
using Render;
using Spine.Unity;
using System;

using System.Collections.Generic;
using TFW;
using TFW.Localization;
using TFW.Map;
using TFW.UI;
using TMPro;
using UnityEngine;
using UnityEngine.UI;


namespace UI
{
    /// <summary>
    /// 剧情引导数据类，包含引导项和回调
    /// </summary>
    public class UIEpisodeGuidItemData : UIData
    {
        /// <summary>
        /// 剧情配置ID
        /// </summary>
        public int EpisodeCfgID;

        /// <summary>
        /// 引导项ID列表
        /// </summary>
        public List<int> guidItems = new List<int>();

        /// <summary>
        /// 引导结束回调
        /// </summary>
        public Action EndCallBack;

    }

    /// <summary>
    /// 剧情引导UI类，负责显示剧情对话和引导
    /// </summary>
    public class UIEpisodeGuid : UIBase
    {
        /// <summary>
        /// UI资源路径
        /// </summary>
        protected override string assetPath =>
                "Guide/UIEpisodeGuid";

        /// <summary>
        /// 弹出面板类型：全屏
        /// </summary>
        public override EPopPanelType PopPanelType
        {
            get;
            set;
        } = EPopPanelType.FullScreen;

        /// <summary>
        /// 排序层级：联盟集结
        /// </summary>
        public override ESortingOrder SortingOrder
        {
            get;
            set;
        } = ESortingOrder.AllianceRally;

        /// <summary>
        /// 引导数据
        /// </summary>
        private UIEpisodeGuidItemData UIData;

        /// <summary>
        /// 当前引导项数据
        /// </summary>
        private Cfg.G.CBubble mUIGuidItemData;

        /// <summary>
        /// 引导UI的RectTransform
        /// </summary>
        private RectTransform guideTr;

        /// <summary>
        /// 目标触发器
        /// </summary>
        private EventTriggerListener targetTrigger;

        /// <summary>
        /// 目标按钮
        /// </summary>
        private TFWButton targetBtn;

        /// <summary>
        /// 合成物品按钮
        /// </summary>
        private K3.UIMergeGood mergeGoodBtn;

        /// <summary>
        /// 手指引导Transform
        /// </summary>
        private Transform fingerTr;

        /// <summary>
        /// 目标Tab
        /// </summary>
        private TFWTab targetTab;

        /// <summary>
        /// 手势动画
        /// </summary>
        private Animation handObj;

        /// <summary>
        /// 当前对话框
        /// </summary>
        private EpisodeDialog CurDialog;

        /// <summary>
        /// 跳过按钮
        /// </summary>
        private GameObject btnSkip;

        /// <summary>
        /// 是否点击跳过
        /// </summary>
        private bool btn_skip = false;

        /// <summary>
        /// 手指动画Tween
        /// </summary>
        private Tweener fingerTween;

        /// <summary>
        /// 对话框类，负责显示对话内容和角色
        /// </summary>
        public class EpisodeDialog
        {
            /// <summary>
            /// 底部Transform
            /// </summary>
            public Transform bottomTr;

            /// <summary>
            /// 描述对象
            /// </summary>
            public GameObject descObj;

            /// <summary>
            /// 描述对象动画
            /// </summary>
            public Animation descObjAni;

            /// <summary>
            /// 角色Spine对象
            /// </summary>
            public GameObject roleSpineTr;

            /// <summary>
            /// 角色名称文本
            /// </summary>
            public TFWText desRoleName;

            /// <summary>
            /// 描述文本
            /// </summary>
            public TextMeshProUGUI descText;

            /// <summary>
            /// 打字机效果组件
            /// </summary>
            public TypewriterByCharacter typewriter;

            /// <summary>
            /// 下一步按钮
            /// </summary>
            public TextMeshProUGUI btnNext;

            /// <summary>
            /// 对话气泡数据
            /// </summary>
            public Cfg.G.CBubble mBubble;

            /// <summary>
            /// 画布组件
            /// </summary>
            private CanvasGroup trCanvas;

            /// <summary>
            /// Spine对象
            /// </summary>
            private GameObject spineObj;

            /// <summary>
            /// 是否居中显示
            /// </summary>
            private bool mCenter;

            /// <summary>
            /// 文本动画名称
            /// </summary>
            private string txtAniName;

            /// <summary>
            /// 选项根节点
            /// </summary>
            public GameObject SelectRoot;

            /// <summary>
            /// 构造函数，初始化对话框
            /// </summary>
            /// <param name="tr">对话框GameObject</param>
            /// <param name="lastBubble">上一个对话气泡数据</param>
            /// <param name="bubble">当前对话气泡数据</param>
            /// <param name="center">是否居中显示</param>
            /// <param name="NextCallBack">下一步回调</param>
            /// <param name="ActiveSelectAndTextCallback">激活选项卡和文本回调</param>
            public EpisodeDialog(GameObject tr, Cfg.G.CBubble lastBubble, Cfg.G.CBubble bubble, bool center, System.Action NextCallBack, System.Action ActiveSelectAndTextCallback = null)
            {
                // 初始化基本属性
                mBubble = bubble;
                mCenter = center;
                bottomTr = tr.transform;
                tr.name = mBubble.Id.ToString();

                // 获取组件引用
                trCanvas = tr.GetComponent<CanvasGroup>();
                roleSpineTr = UIHelper.GetChild(tr, "desBg/spineTr/spine");
                descObj = UIHelper.GetChild(tr, "desBg");
                descObjAni = descObj.GetComponent<Animation>();
                btnNext = UIHelper.GetComponent<TextMeshProUGUI>(tr, "desBg/tip");
                btnNext.text = LocalizationMgr.Get("dialog_continue");
                btnNext.gameObject.SetActive(false);
                descText = UIHelper.GetComponent<TextMeshProUGUI>(tr, "desBg/des");
                if (typewriter == null)
                {
                    typewriter = UIHelper.GetComponent<TypewriterByCharacter>(tr, "desBg/des");

                    // 根据语言设置打字机速度
                    float typewriterSpeed = LocalizationMgr.CurrentLanguage.Equals("cn") ? 0.02f : 0.01f;

                    typewriter.waitForNormalChars = typewriterSpeed;
                    typewriter.waitLong = typewriterSpeed;
                    typewriter.waitMiddle = typewriterSpeed;
                    typewriter.onTextShowed.AddListener(() =>
                    {
                        if (descText.text.Equals(LocalizationMgr.Get(mBubble.Text).Replace("\\n", "\n")))//确认是当前的文字播完了
                        {
                            // 文本显示完成后，显示下一步按钮
                            btnNext.gameObject.SetActive(true);
                            //选择框
                            ActiveSelectAndTextCallback?.Invoke();
                        }
                    });
                }

                desRoleName = UIHelper.GetComponent<TFWText>(tr, "desBg/roleName");
                SelectRoot = UIHelper.GetChild(tr, "Options");
                SelectRoot.SetActive(false);

                UIBase.AddRemoveListener(EventTriggerType.Click, btnNext.gameObject, (x, y) =>
                {
                    NextCallBack?.Invoke();
                });

                // 初始状态隐藏底部
                bottomTr.gameObject.SetActive(false);

                // 根据角色位置确定动画名称
                txtAniName = "xinshouyindao_zuo";

                if (mBubble.RolePosition == 1)
                {
                    txtAniName = "xinshouyindao_you";
                }
                else if (mBubble.RolePosition == 0)
                {
                    if (lastBubble != null && lastBubble.RolePosition != 0)
                    {
                        txtAniName = lastBubble.RolePosition == 1 ? "xinshouyindao_zuo" : "xinshouyindao_you";
                    }
                    else
                    {
                        txtAniName = "xinshouyindao_zuo";
                    }
                }
                descObj.SetActive(false);
            }


            /// <summary>
            /// 立即显示全部文本
            /// </summary>
            public void DisplayImmediately()
            {
                // 跳过打字机效果，立即显示全部文本
                if (typewriter != null)
                {
                    typewriter.SkipTypewriter();
                    // 确保按钮显示
                    btnNext.gameObject.SetActive(true);
                }
            }

            /// <summary>
            /// 检查是否正在显示文本
            /// </summary>
            /// <returns>是否正在显示文本</returns>
            public bool IsTyping()
            {
                return typewriter != null && typewriter.isShowingText;
            }

            /// <summary>
            /// 显示文本内容
            /// </summary>
            private void TypeText()
            {
                // 获取本地化文本
                string fullText = LocalizationMgr.Get(mBubble.Text);
                fullText = fullText.Replace("\\n", "\n");
                // 使用打字机效果显示文本
                typewriter.ShowText(fullText);
            }

            /// <summary>
            /// 显示Spine角色和对话内容
            /// </summary>
            public void ShowSpine()
            {
                // 显示描述对象
                descObj.SetActive(true);


                // 设置文本颜色
                if (!mBubble.TextColor.Equals("0"))
                {
                    descText.color = ColorUtils.ColorFromRGBAString(mBubble.TextColor);
                }
                else
                {
                    descText.color = Color.white;
                }

                // 根据文本内容决定是否显示底部
                bottomTr.gameObject.SetActive(!string.IsNullOrEmpty(mBubble.Text));

                // 设置角色名称
                desRoleName.text = LocalizationMgr.Get(mBubble.RoleName);

                // 根据角色位置播放不同动画
                if (mBubble.RolePosition > 0)
                {
                    descObjAni.Play(mBubble.RolePosition == 1 ? "xinshouyindao_zuo" : "xinshouyindao_you", () =>
                    {
                        TypeText();
                    });
                }
                else
                {
                    TypeText();
                }

                // 全屏对话模式特殊处理
                var episodeConfig = Cfg.C.CEpisode.I(mBubble.EpisodeId);
                if (episodeConfig.DialogueFormat == 3)
                {
                    return;
                }

                // 设置裁剪区域
                if (mBubble.CuttingArea.Count > 2)
                {
                    roleSpineTr.GetComponent<RectTransform>().anchoredPosition = new Vector2(float.Parse(mBubble.CuttingArea[0]), float.Parse(mBubble.CuttingArea[1]));
                    roleSpineTr.GetComponent<RectTransform>().sizeDelta = new Vector2(2000, float.Parse(mBubble.CuttingArea[2]));
                }

                // 加载并设置角色Spine
                if (mBubble.Role.Count > 2)
                {
                    if (spineObj != null && spineObj.name == mBubble.Role[0])
                    {
                        // 更新已存在的Spine对象
                        var SpineLocalScale = HeroUtils.Parse(mBubble.Role[2]);
                        var SpineLocalPostion = HeroUtils.Parse(mBubble.Role[1]);
                        spineObj.transform.localScale = SpineLocalScale;
                        spineObj.GetComponent<RectTransform>().anchoredPosition3D = new Vector3(SpineLocalPostion.x, SpineLocalPostion.y, 0);
                    }
                    else
                    {
                        // 加载新的Spine对象
                        spineObj = ResourceMgr.LoadInstance(mBubble.Role[0]);
                        spineObj.name = mBubble.Role[0];
                        spineObj.transform.SetParent(roleSpineTr.transform);

                        descObjAni.Play(txtAniName);

                        var SpineLocalScale = HeroUtils.Parse(mBubble.Role[2]);
                        var SpineLocalPostion = HeroUtils.Parse(mBubble.Role[1]);
                        spineObj.transform.localScale = SpineLocalScale;

                        if (!mCenter)
                        {
                            spineObj.GetComponent<RectTransform>().anchoredPosition3D = new Vector3(SpineLocalPostion.x, SpineLocalPostion.y, 0);
                        }
                        else
                        {
                            spineObj.GetComponent<RectTransform>().anchoredPosition3D = new Vector3(SpineLocalPostion.x, SpineLocalPostion.y, 0);
                        }
                    }
                }
                else
                {
                    // 无角色情况下，根据上一对话决定是否播放动画
                    if (!string.IsNullOrEmpty(txtAniName))
                    {
                        descObjAni.Play(txtAniName);
                    }
                }

                // 强制重建布局
                LayoutRebuilder.ForceRebuildLayoutImmediate(descText.rectTransform);
                LayoutRebuilder.ForceRebuildLayoutImmediate(descObj.GetComponent<RectTransform>());
            }


            /// <summary>
            /// 激活/禁用对话框
            /// </summary>
            /// <param name="active">是否激活</param>
            public void ActiveDialog(bool active)
            {
                bottomTr.gameObject.SetActive(active);
            }

            /// <summary>
            /// 隐藏Spine对象
            /// </summary>
            public void HideSpineObj()
            {
                if (spineObj != null)
                {
                    // 延迟销毁画布
                    UnityEngine.Object.Destroy(trCanvas.gameObject, 1f);
                    spineObj = null;
                }
                else
                {
                    if (trCanvas != null)
                    {
                        // 立即销毁画布
                        UnityEngine.Object.DestroyImmediate(trCanvas.gameObject);
                    }
                }
            }

            /// <summary>
            /// 销毁对话框
            /// </summary>
            public void Dispose()
            {
                if (bottomTr != null)
                {
                    UnityEngine.Object.DestroyImmediate(bottomTr.gameObject);
                }
            }
        }

        private RectTransform bottomTr;
        private RectTransform groupDialog;

        private GameObject bgMask;
        private GameObject specialMask;
        private SkeletonGraphic worldHand;

        // 新增
        private TFWRawImage m_FullSpineBg;
        private GameObject m_FullSpineRoot;
        private GameObject m_FullRoot;
        private GameObject m_FullSpineObj;
        private GameObject m_FullChatObj;
        private GameObject m_InteractionObject;
        private GameObject m_InteractionEffect;
        private GameObject m_FullGuide;
        private GameObject m_FullGuideEffect;
        private Canvas m_GroupSpine;
        private TFWText m_FullChatDesc;
        private string m_SpineAssetPath;
        private GameObject m_HandEffect;

        private GameObject fingerMask;


        protected override void OnInit()
        {
            nextClickIDs = new List<int>();
        }

        private List<int> nextClickIDs;

        private float findBtnTime = 0;

        private int m_AudioId;

        private void NextDialog()
        {
            if (!nextClickIDs.Contains(mUIGuidItemData.Id))
            {
                nextClickIDs.Add(mUIGuidItemData.Id);
                this.UIData.guidItems.Remove(mUIGuidItemData.Id);

                K3GameEventEnd(mUIGuidItemData);

                NextGuid();
            }
        }

        protected override void OnLoad()
        {
            guideTr = GetChild("content/guide").GetComponent<RectTransform>();
            fingerTr = GetChild("content/guide/fingerOffset").transform;
            btnSkip = GetChild("btnSkip");
            bgMask = GetChild("btnClose");
            specialMask = GetChild("MaskTop");
            worldHand = GetComponent<SkeletonGraphic>("content/worldHand");
            handObj = GetChild("content/guide/fingerOffset/GuideFinger/hand/hand").GetComponent<Animation>();
            m_HandEffect = GetChild("content/guide/fingerOffset/GuideFinger/hand/ui_zhiyin_old");
            bottomTr = GetChild("bottom").GetComponent<RectTransform>();
            groupDialog = GetChild("GroupDialog").GetComponent<RectTransform>();

            m_FullRoot = GetChild("GroupFull");
            m_FullSpineRoot = GetChild("GroupFull/SpineRoot");
            m_FullSpineBg = GetChild("GroupFull/Bg").GetComponent<TFWRawImage>();
            m_FullChatObj = GetChild("GroupFull/SpineRoot/GroupSpine/GroupText");
            m_FullChatDesc = GetChild("GroupFull/SpineRoot/GroupSpine/GroupText/Text").GetComponent<TFWText>();
            m_FullGuide = GetChild("GroupFull/SpineRoot/BtnGuide");
            m_FullGuideEffect = GetChild("GroupFull/SpineRoot/BtnGuide/Eff_ui_hz_dianji_l/Eff_ui_dj_qg/Eff_ui_dj_shou");
            m_GroupSpine = GetChild("GroupFull/SpineRoot/GroupSpine").GetComponent<Canvas>();

            fingerMask = GetChild("content/guide/Mask");

            m_GroupSpine.sortingLayerID = CustomSortingLayer.Widget;


            fullBgTexture = m_FullSpineBg.texture;

            // 过场
            InitCutScenes();
        }

        private Texture fullBgTexture;

        protected override void OnShown()
        {
            base.OnShown();


            m_FullSpineBg.texture = fullBgTexture;

            UpdateEnable = true;
            targetTrigger = null;
            targetBtn = null;
            mergeGoodBtn = null;
            targetTab = null;

            btn_skip = false;

            this.UIData = this.InitData as UIEpisodeGuidItemData;

            if (this.UIData == null || this.UIData.guidItems.Count == 0)
            {
                CloseSelf();
            }
            else
            {
                findBtnTime = 0;

                var GuidEvent = new GuidEvent();

                GuidEvent.EventKey = "episode_guid_open";
                GuidEvent.Properties.Add("cfgid", this.UIData.EpisodeCfgID.ToString());

                K3GameEvent.I.TaLog(GuidEvent);


                DoShowUIGuid();
            }

            canvas.sortingLayerID = CustomSortingLayer.Dialog;
        }

        NTimer.Timer nextGroupIDTimer;
        protected override void OnHidden()
        {
            AudioManager.Instance.Stop(m_AudioId);

            CurDialog?.HideSpineObj();
            CurDialog = null;

            var GuidEvent = new GuidEvent();

            GuidEvent.EventKey = "episode_guid_close";
            GuidEvent.Properties.Add("cfgid", this.UIData.EpisodeCfgID.ToString());

            K3GameEvent.I.TaLog(GuidEvent);



            if (UIData.guidItems.Count == 0 && !btn_skip)
            {
                UIData.EndCallBack?.Invoke(); //对话执行完毕后才进行下一剧情对话的执行
            }
            else
            {
                int nextGroupID = Cfg.C.CEpisode.I(K3.K3PlayerMgr.I.PlayerData.CurGuidGroupID)?.NextEpisode ?? 0;

                K3.K3PlayerMgr.I.PlayerData.CurGuidGroupID = 0;
                K3.K3PlayerMgr.I.SavePlayerDataToServer();

                nextGroupIDTimer?.Stop();
                NTimer.CountDownNoPool(ref nextGroupIDTimer, 0.1f, () =>
                {
                    if (K3.K3PlayerMgr.I.PlayerData.CurGuidGroupID == 0) //期间尚未触发对话引导
                    {
                        Logic.GuidManage.TriggerGuid(nextGroupID);
                    }
                });
            }

            closeUITimer?.Stop();
            fingerTimer?.Stop();

            nextClickIDs.Clear();

            if (fingerTween != null)
            {
                fingerTween.Kill();
            }

            targetTrigger = null;
            targetBtn = null;
            mergeGoodBtn = null;
            targetTab = null;
            base.OnHidden();
        }

        protected override void Update(float deltaTime)
        {
            base.Update(deltaTime);

            //计时 5S 找不到对应的按钮就进行关闭引导
            findBtnTime += deltaTime;
            float waitTime = mUIGuidItemData != null ? mUIGuidItemData.SearchTime * 0.001f : 1;
            if (findBtnTime > waitTime)
            {
                // 找不到直接退出
                UIData.guidItems.Clear();
                CloseSelf();

                UpdateEnable = false;

                return;
            }

            DoShowUIGuid();
        }

        public List<Type> GuidCloseUIs = new List<Type>()
        {
            typeof(UIGuid), // typeof(UIActivityReminder),
        };


        private bool m_Interactive;

        NTimer.Timer m_FullSpineBgTimer;
        /// <summary>
        /// 处理全屏的引导模式
        /// </summary>
        private void HandleFullGuid()
        {
            var episodeConfig = Cfg.C.CEpisode.I(this.UIData.EpisodeCfgID);
            if (episodeConfig.DialogueFormat == 3)
            {


                m_FullSpineBgTimer?.Stop();
                if (mUIGuidItemData?.Delay > 0)
                {
                    NTimer.CountDownNoPool(ref m_FullSpineBgTimer, mUIGuidItemData.Delay * 0.001f, () =>
                    {
                        UIHelper.SetRawImage(m_FullSpineBg, $"{mUIGuidItemData.Image}.png");

                    });
                }
                else
                {
                    UIHelper.SetRawImage(m_FullSpineBg, $"{mUIGuidItemData.Image}.png");
                }

                LogoIn(() =>
                {
                    CurDialog?.ActiveDialog(true);

                    if (mUIGuidItemData.Role.Count > 0)
                    {
                        string path = mUIGuidItemData.Role[0];
                        Vector3 pos = HeroUtils.Parse(mUIGuidItemData.Role[1]);
                        Vector3 scale = HeroUtils.Parse(mUIGuidItemData.Role[2]); ;
                        Vector3 rotation = FingerOffset(mUIGuidItemData.RoleRotate);
                        if (mUIGuidItemData != null && mUIGuidItemData.Delay > 0)
                        {
                            NTimer.CountDown(mUIGuidItemData.Delay * 0.001f, () =>
                            {
                                RoleSetData(path, pos, scale, rotation);
                            });
                        }
                        else
                        {
                            RoleSetData(path, pos, scale, rotation);
                        }
                    }

                    if (mUIGuidItemData?.GuideAction > 0)
                    {
                        UpdateEnable = false; //对话相关

                        // 增加对话框延迟操作
                        if (mUIGuidItemData != null && mUIGuidItemData.Delay > 0)
                        {
                            NTimer.CountDown(mUIGuidItemData.Delay * 0.001f, () =>
                            {
                                CurDialog.ShowSpine();
                            });
                        }
                        else
                        {
                            CurDialog.ShowSpine();
                        }
                    }
                    else
                    {
                        UpdateEnable = false; //对话相关
                        guideTr.gameObject.SetActive(false);
                        if (mUIGuidItemData != null && mUIGuidItemData.Delay > 0)
                        {
                            NTimer.CountDown(mUIGuidItemData.Delay * 0.001f, () =>
                            {
                                CurDialog.ShowSpine();
                            });
                        }
                        else
                        {
                            CurDialog.ShowSpine();
                        }
                    }

                    // 增加音效
                    if (mUIGuidItemData.Dubbing > 0)
                    {
                        m_AudioId = GameAudio.PlayAudio(mUIGuidItemData.Dubbing);
                    }
                });
            }
        }

        private void RoleSetData(string path, Vector3 pos, Vector3 scale, Vector3 rotation)
        {

            if (m_FullSpineObj == null || !path.Equals(m_SpineAssetPath))
            {
                m_SpineAssetPath = path;
                if (m_FullSpineObj != null)
                {
                    GameObject.Destroy(m_FullSpineObj);
                    m_FullSpineObj = null;
                }

                m_FullSpineObj = ResourceMgr.LoadInstance(path);
                m_FullSpineObj.transform.SetParent(m_FullSpineRoot.transform);
            }
            if (mUIGuidItemData.Role.Count >= 4)
            {
                ChangedSkin(m_FullSpineObj, mUIGuidItemData.Role[3]);
            }
            m_FullSpineObj.SetActive(true);
            m_FullSpineObj.transform.localPosition = pos;
            m_FullSpineObj.transform.localScale = scale;
            m_FullSpineObj.transform.localRotation = Quaternion.Euler(rotation.x, rotation.y, rotation.z);
            if (mUIGuidItemData.CuttingArea.Count > 2)
            {
                m_FullSpineRoot.GetComponent<RectTransform>().anchoredPosition = new Vector2(float.Parse(mUIGuidItemData.CuttingArea[0]), float.Parse(mUIGuidItemData.CuttingArea[1]));
                m_FullSpineRoot.GetComponent<RectTransform>().sizeDelta = new Vector2(2000, float.Parse(mUIGuidItemData.CuttingArea[2]));
            }
        }

        /// <summary>
        /// 激活选项卡和文本
        /// </summary>
        private void ActiveSelectAndText()
        {
            SetupInteractionText();
            SetupDialogOptions();
            SetupGuideInteraction();


        }

        /// <summary>
        /// 设置交互文本显示
        /// </summary>
        private void SetupInteractionText()
        {
            bool hasText = !string.IsNullOrEmpty(mUIGuidItemData.ActTxt) && !mUIGuidItemData.ActTxt.Equals("0");
            m_FullChatObj.SetActive(hasText);

            if (hasText)
            {
                Vector3 position = FingerOffset(mUIGuidItemData.ActPos);

                m_FullChatObj.transform.localPosition = position;
                m_FullChatDesc.text = LocalizationMgr.Get(mUIGuidItemData.ActTxt);
            }
        }

        /// <summary>
        /// 设置对话选项
        /// </summary>
        private void SetupDialogOptions()
        {
            bool hasOptions = mUIGuidItemData.DialogOptions.Count > 0;
            CurDialog?.SelectRoot.SetActive(hasOptions);

            if (!hasOptions || CurDialog?.SelectRoot == null) return;

            int childCount = CurDialog.SelectRoot.transform.childCount;
            for (int i = 0; i < childCount; i++)
            {
                var item = CurDialog.SelectRoot.transform.GetChild(i);
                bool shouldShow = i < mUIGuidItemData.DialogOptions.Count;
                item.gameObject.SetActive(shouldShow);

                if (shouldShow)
                {
                    var text = item.GetComponentInChildren<TFWText>();
                    text.text = LocalizationMgr.Get(mUIGuidItemData.DialogOptions[i]);
                    AddRemoveListener(EventTriggerType.Click, item.gameObject, OnDialogOptionClicked);
                }
            }
        }

        /// <summary>
        /// 设置引导交互
        /// </summary>
        private void SetupGuideInteraction()
        {
            if (mUIGuidItemData?.GuideAction == 0) return;

            Vector3 gesturePosition = FingerOffset(mUIGuidItemData.GestureOffset);


            m_FullGuide.SetActive(true);
            m_FullGuide.transform.localPosition = gesturePosition;

            CreateInteractionObject();
            AddRemoveListener(EventTriggerType.Click, m_FullGuide, OnGuideClicked);
        }

        /// <summary>
        /// 创建交互对象
        /// </summary>
        private void CreateInteractionObject()
        {
            if (mUIGuidItemData.Act.Count <= 0) return;

            string path = mUIGuidItemData.Act[0];
            Vector3 position = HeroUtils.Parse(mUIGuidItemData.Act[1]);
            Vector3 scale = HeroUtils.Parse(mUIGuidItemData.Act[2]);

            m_FullGuideEffect.SetActive(scale == Vector3.zero);

            if (m_InteractionObject == null)
            {
                m_InteractionObject = ResourceMgr.LoadInstance(path);
                m_InteractionObject.transform.SetParent(m_GroupSpine.transform);
            }

            m_InteractionObject.transform.localPosition = position;
            m_InteractionObject.transform.localScale = scale;
        }

        /// <summary>
        /// 对话选项点击回调
        /// </summary>
        private void OnDialogOptionClicked(GameObject ga, UnityEngine.EventSystems.PointerEventData point)
        {
            if (nextClickIDs.Contains(mUIGuidItemData.Id)) return;

            ProcessGuideCompletion();
        }

        /// <summary>
        /// 引导点击回调
        /// </summary>
        private void OnGuideClicked(GameObject ga, UnityEngine.EventSystems.PointerEventData point)
        {
            if (nextClickIDs.Contains(mUIGuidItemData.Id)) return;

            ProcessGuideCompletion();
            HandleFullScreenGuideInteraction();
        }

        /// <summary>
        /// 处理引导完成逻辑
        /// </summary>
        private void ProcessGuideCompletion()
        {
            nextClickIDs.Add(mUIGuidItemData.Id);
            this.UIData.guidItems.Remove(mUIGuidItemData.Id);
            K3GameEventEnd(mUIGuidItemData);
            NextGuid();
        }

        /// <summary>
        /// 处理全屏引导交互
        /// </summary>
        private void HandleFullScreenGuideInteraction()
        {
            var episodeConfig = Cfg.C.CEpisode.I(this.UIData.EpisodeCfgID);
            if (episodeConfig.DialogueFormat == 3 && mUIGuidItemData.RoleAct.Count > 0 && !m_Interactive)
            {
                m_Interactive = true;
                HandleStoryInteraction();
            }
        }

        /// <summary>
        /// 处理剧情交互
        /// </summary>
        private void HandleStoryInteraction()
        {
            int index = mUIGuidItemData.RoleAct.Count == 4 ? 2 : 3;
            int spineAudio = 0, actAudio = 0;
            // 播放spine交互
            string spineAct = mUIGuidItemData.RoleAct[0];
            int spineDelay = Convert.ToInt32(mUIGuidItemData.RoleAct[1]);
            string act = mUIGuidItemData.RoleAct[index];
            int actDelay = Convert.ToInt32(mUIGuidItemData.RoleAct[index + 1]);
            if (index == 3)
            {
                spineAudio = Convert.ToInt32(mUIGuidItemData.RoleAct[2]);
                actAudio = Convert.ToInt32(mUIGuidItemData.RoleAct[index + 2]);
            }
            PlaySpineAnim(m_FullSpineObj, spineAct, spineDelay, spineAudio);
            PlaySpineAnim(m_InteractionObject, act, actDelay, actAudio);
            PlayEffectInteraction();
        }

        /// <summary>
        /// 播放交互剧情特效
        /// </summary>
        private void PlayEffectInteraction()
        {
            if (mUIGuidItemData.ActVFX == null || mUIGuidItemData.ActVFX.Count == 0)
            {
                return;
            }
            string path = mUIGuidItemData.ActVFX[0];
            Vector3 pos = HeroUtils.Parse(mUIGuidItemData.ActVFX[1]);
            Vector3 scale = HeroUtils.Parse(mUIGuidItemData.ActVFX[2]);
            if (m_InteractionEffect == null)
            {
                m_InteractionEffect = ResourceMgr.LoadInstance(path);
                m_InteractionEffect.transform.SetParent(m_FullSpineRoot.transform);
            }
            m_InteractionEffect.transform.localPosition = pos;
            m_InteractionEffect.transform.localScale = scale;
        }

        /// <summary>
        /// 播放spine动画
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="splitName"></param>
        /// <param name="delay"></param>
        private void PlaySpineAnim(GameObject obj, string splitName, int delay, int audioId)
        {
            NTimer.CountDown(delay * 0.001f, () =>
            {
                if (!splitName.Equals("None"))
                {
                    var anim = obj.GetComponent<SkeletonGraphic>();
                    anim.AnimationState.SetAnimation(0, splitName, false);
                    // anim.AnimationState.Complete
                    // 人物spine

                    if (obj.name.Equals(m_FullSpineObj.name))
                    {
                        anim.AnimationState.Complete += OnAnimationComplete;
                    }
                    else
                    {
                        anim.AnimationState.Complete += OnInteractionAnimationComplete;
                    }
                }

                if (audioId > 0)
                {
                    var cfg = Cfg.C.CAudioList.I(audioId);
                    if (cfg != null)
                    {
                        AudioManager.Instance.Play((AudioChannelType)cfg.Scene, cfg.Asset, cfg.Time, cfg.VolScale, 1, cfg.Priority, (AudioPlayMode)cfg.Typ, cfg.Mode == 1, null);
                    }
                }
            });
        }

        /// <summary>
        /// 监听动画
        /// </summary>
        /// <param name="trackEntry"></param>
        private void OnInteractionAnimationComplete(Spine.TrackEntry trackEntry)
        {
            var anim = m_FullSpineObj.GetComponent<SkeletonGraphic>();
            if (trackEntry != null)
            {
                if (m_InteractionObject != null && m_InteractionObject.gameObject.activeInHierarchy)
                {
                    m_InteractionObject.SetActive(false);
                }
                anim.AnimationState.Complete -= OnInteractionAnimationComplete;
            }
        }

        /// <summary>
        /// 监听动画
        /// </summary>
        /// <param name="trackEntry"></param>
        private void OnAnimationComplete(Spine.TrackEntry trackEntry)
        {
            var anim = m_FullSpineObj.GetComponent<SkeletonGraphic>();
            if (trackEntry != null)
            {
                if (trackEntry.Animation.Name != "idle")
                {
                    anim.AnimationState.SetAnimation(0, "idle", true);
                }
                anim.AnimationState.Complete -= OnAnimationComplete;
            }
        }

        private void DoShowUIGuid()
        {
            if (!ValidateGuideData()) return;

            CloseConflictingUIs();
            InitializeGuideData();
            SetupEventListeners();
            ConfigureUIElements();
            ProcessGuideLogic();
        }

        /// <summary>
        /// 验证引导数据有效性
        /// </summary>
        private bool ValidateGuideData()
        {
            if (this.UIData.guidItems.Count == 0 || gameObject == null)
            {
                UpdateEnable = false;
                CloseSelf();
                return false;
            }
            return true;
        }

        /// <summary>
        /// 关闭冲突的UI
        /// </summary>
        private void CloseConflictingUIs()
        {
            foreach (var item in GuidCloseUIs)
            {
                var findUI = PopupManager.I.FindPopup(item);
                if (findUI != null)
                {
                    PopupManager.I.ClosePopup(item);
                }

                var findUI2 = WndMgr.Get(item.ToString());
                if (findUI2 != null)
                {
                    findUI2.Hide();
                }
            }
        }

        /// <summary>
        /// 初始化引导数据
        /// </summary>
        private void InitializeGuideData()
        {
            mUIGuidItemData = Cfg.C.CBubble.I(this.UIData.guidItems[0]);

            // 清理现有监听器
            RemoveListener(EventTriggerType.Click, bgMask);
            RemoveListener(EventTriggerType.Drag, bgMask);
            RemoveListener(EventTriggerType.Click, btnSkip);
        }

        /// <summary>
        /// 设置事件监听器
        /// </summary>
        private void SetupEventListeners()
        {
            NTimer.CountDown(mUIGuidItemData.ActDelay * 0.001f, () =>
            {
                AddListener(EventTriggerType.Click, bgMask, OnBgMaskClick);
                AddListener(EventTriggerType.Drag, bgMask, OnBgMaskDrag);
                AddListener(EventTriggerType.Click, btnSkip, OnSkipButtonClick);
            });
        }

        /// <summary>
        /// 配置UI元素
        /// </summary>
        private void ConfigureUIElements()
        {
            // 关闭全部UI流程
            if (mUIGuidItemData.CloseUIType == 1)
            {
                PopupManager.I.ClearAllPopup();
            }

            // 配置手势焦点
            fingerMask.SetActive(mUIGuidItemData?.GestureFocus == 1);
            fingerMask.GetComponent<EpisodeCircleMask>().SetValue(mUIGuidItemData?.GestureFocusScale ?? 8);

            // 重置引导手状态
            worldHand.gameObject.SetActive(false);
            RemoveListener(EventTriggerType.Click, guideTr.gameObject);
        }

        /// <summary>
        /// 处理引导逻辑
        /// </summary>
        private void ProcessGuideLogic()
        {
            if (HasActiveTargets()) return;

            CleanupAnimations();
            var episodeConfig = InitializeEpisodeConfig();

            if (bgMask == null)
            {
                this.UIData.guidItems.Clear();
                CloseSelf();
                return;
            }

            SetupDialogSystem(episodeConfig);
            ConfigureDisplayMode(episodeConfig);

            if (episodeConfig.DialogueFormat == 3)
            {
                LogGuideEvent();
                HandleFullGuid();
                return;
            }

            if (IsDialogOnlyMode())
            {
                HandleDialogOnlyMode();
                return;
            }

            HandleTargetGuideMode(episodeConfig);
        }

        /// <summary>
        /// 检查是否有活跃的目标
        /// </summary>
        private bool HasActiveTargets()
        {
            return targetTrigger != null || targetBtn != null || mergeGoodBtn != null;
        }

        /// <summary>
        /// 清理动画
        /// </summary>
        private void CleanupAnimations()
        {
            fingerTween?.Kill();
            fingerTimer?.Stop();
            DOTween.Kill(fingerTr);
        }

        /// <summary>
        /// 初始化剧情配置
        /// </summary>
        private CEpisode InitializeEpisodeConfig()
        {
            var episodeConfig = Cfg.C.CEpisode.I(this.UIData.EpisodeCfgID);
            K3GameEventStart(mUIGuidItemData);
            worldHand.gameObject.SetActive(false);
            return episodeConfig;
        }

        /// <summary>
        /// 设置对话系统
        /// </summary>
        private void SetupDialogSystem(CEpisode episodeConfig)
        {
            bool shouldReuseDialog = CurDialog?.mBubble.Role.Count == mUIGuidItemData.Role.Count &&
                                   mUIGuidItemData.Role.Count > 0 &&
                                   CurDialog?.mBubble.Role[0] == mUIGuidItemData.Role[0];

            if (shouldReuseDialog)
            {
                CurDialog.mBubble = mUIGuidItemData;
            }
            else
            {
                CreateNewDialog(episodeConfig);
            }
        }

        /// <summary>
        /// 创建新对话框
        /// </summary>
        private void CreateNewDialog(CEpisode episodeConfig)
        {
            var lastBubble = CurDialog?.mBubble ?? null;
            CurDialog?.HideSpineObj();

            var newDialog = UIHelper.GetChild(bottomTr.parent.gameObject, mUIGuidItemData.Id.ToString());
            if (newDialog == null)
            {
                newDialog = UnityEngine.Object.Instantiate(bottomTr.gameObject, groupDialog);
            }

            Vector2 offset = episodeConfig.DialogueFormat == 1 ? new Vector2(0.5f, 0.5f) : new Vector2(0.5f, 0f);
            var rectTransform = newDialog.GetComponent<RectTransform>();
            rectTransform.anchorMax = offset;
            rectTransform.anchorMin = offset;
            rectTransform.anchoredPosition3D = new Vector3(0, mUIGuidItemData.BubblesShift, 0);

            CurDialog = new EpisodeDialog(newDialog.gameObject, lastBubble, mUIGuidItemData, false, NextDialog, ActiveSelectAndText);
            bottomTr.anchorMax = offset;
            bottomTr.anchorMin = offset;
        }

        /// <summary>
        /// 配置显示模式
        /// </summary>
        private void ConfigureDisplayMode(CEpisode episodeConfig)
        {
            m_FullChatObj.SetActive(false);
            guideTr.gameObject.SetActive(false);
            m_FullGuide.SetActive(false);
            m_Interactive = false;

            // 设置全屏UI显示状态
            m_FullRoot.SetActive(episodeConfig.DialogueFormat == 3);
            handObj.gameObject.SetActive(episodeConfig.DialogueFormat != 3);
            btnSkip.SetActive(false);

            // 设置背景遮罩颜色
            bool shouldDarkenMask = mUIGuidItemData?.Mask == 1 && mUIGuidItemData?.GuideAction == 0;
            bgMask.GetComponent<TFWImage>().color = shouldDarkenMask ?
                new Color(0, 0, 0, 180f / 255) : new Color(0, 0, 0, 1f / 255);

            specialMask.SetActive(mUIGuidItemData?.Mask == 2);
        }

        /// <summary>
        /// 记录引导事件
        /// </summary>
        private void LogGuideEvent()
        {
            var guidEvent = new GuidEvent() { EventKey = $"guide_episode_{UIData.EpisodeCfgID}_{mUIGuidItemData.Id}" };
            K3GameEvent.I.BiLog(guidEvent);

            guidEvent.EventKey = "guide_episode";
            guidEvent.Properties.Add("episodecfgid", UIData.EpisodeCfgID.ToString());
            guidEvent.Properties.Add("episodeguidid", mUIGuidItemData.Id.ToString());
            K3GameEvent.I.TaLog(guidEvent);
        }

        /// <summary>
        /// 是否为纯对话模式
        /// </summary>
        private bool IsDialogOnlyMode()
        {
            return mUIGuidItemData?.GuideAction == 0;
        }

        /// <summary>
        /// 处理纯对话模式
        /// </summary>
        private void HandleDialogOnlyMode()
        {
            UpdateEnable = false;
            guideTr.gameObject.SetActive(false);
            CurDialog?.ShowSpine();
        }

        /// <summary>
        /// 处理目标引导模式
        /// </summary>
        private void HandleTargetGuideMode(CEpisode episodeConfig)
        {
            GameObject targetGa = GetWindowObj();

            if (targetGa == null)
            {
                guideTr.gameObject.SetActive(false);
                return;
            }

            if (!targetGa.activeInHierarchy)
            {
                ResetTargetComponents();
                guideTr.gameObject.SetActive(false);
                return;
            }

            LogGuideEvent();
            LogoIn(() => ProcessTargetComponents(targetGa));
        }

        /// <summary>
        /// 重置目标组件
        /// </summary>
        private void ResetTargetComponents()
        {
            targetTrigger = null;
            targetBtn = null;
            mergeGoodBtn = null;
            targetTab = null;
        }

        /// <summary>
        /// 处理目标组件
        /// </summary>
        private void ProcessTargetComponents(GameObject targetGa)
        {
            targetTrigger = targetGa.GetComponent<EventTriggerListener>();
            targetBtn = targetGa.GetComponent<TFWButton>();
            mergeGoodBtn = targetGa.GetComponent<K3.UIMergeGood>();
            targetTab = targetGa.GetComponent<TFWTab>();

            CurDialog?.ShowSpine();

            if (targetBtn != null)
            {
                FindTFWButton();
            }
            else if (targetTrigger != null)
            {
                FindTrigger();
            }
            else if (mergeGoodBtn != null)
            {
                FindMergeButton();
            }
            else if (targetTab != null)
            {
                FindTFWTab();
            }
            else
            {
                guideTr.gameObject.SetActive(false);
                return;
            }

            DragImage();
            UpdateEnable = false;
        }

        /// <summary>
        /// 背景遮罩点击处理
        /// </summary>
        private void OnBgMaskClick(GameObject x, UnityEngine.EventSystems.PointerEventData y)
        {
            if (CurDialog.IsTyping())
            {
                CurDialog.DisplayImmediately();
                return;
            }

            HandleGuideAction();
        }

        /// <summary>
        /// 背景遮罩拖拽处理
        /// </summary>
        private void OnBgMaskDrag(GameObject x, UnityEngine.EventSystems.PointerEventData y)
        {
            HandleGuideAction();
        }

        /// <summary>
        /// 跳过按钮点击处理
        /// </summary>
        private void OnSkipButtonClick(GameObject x, UnityEngine.EventSystems.PointerEventData y)
        {
            btn_skip = true;
            UIData.guidItems.Clear();
            CloseSelf();
        }

        /// <summary>
        /// 处理引导动作
        /// </summary>
        private void HandleGuideAction()
        {
            if (mUIGuidItemData.GuideAction == 0)
            {
                ProcessGuideCompletion();
            }
            else if (mUIGuidItemData.GuideAction == 1 || mUIGuidItemData.GuideAction == 2)
            {
                if (mUIGuidItemData.Mask == 0)
                {
                    btn_skip = true;
                    UIData.guidItems.Clear();
                    CloseSelf();
                }
            }
        }

        private GameObject GetWindowObj()
        {
            GameObject targetGa = null;

            var uiGuid = DeepUI.PopupManager.I.FindAllPopup(Type.GetType(mUIGuidItemData.ClassName));
            if (uiGuid == null)
            {
                uiGuid = DeepUI.PopupManager.I.FindAllPopup(Type.GetType("UI." + mUIGuidItemData.ClassName));
                if (uiGuid == null)
                {
                    uiGuid = DeepUI.PopupManager.I.FindAllPopup(Type.GetType("K3." + mUIGuidItemData.ClassName));
                }
            }

            if (uiGuid == null)
            {


                var guidUI = WndMgr.Get_UIAndProxy(mUIGuidItemData.ClassName);
                if (guidUI == null)
                {
                    guidUI = WndMgr.Get_UIAndProxy("UI." + mUIGuidItemData.ClassName);
                    if (guidUI == null)
                    {
                        guidUI = WndMgr.Get_UIAndProxy("K3." + mUIGuidItemData.ClassName);
                    }
                }

                if (guidUI != null && guidUI.IsLoaded)
                {
                    if (mUIGuidItemData.Targat.Contains("Auto:"))
                    {
                        targetGa = AutoFind();
                    }
                    else if (mUIGuidItemData.Targat.Contains("AutoComplete:"))
                    {
                        targetGa = AutoFindComplete();
                    }
                    else
                    {
                        if (mUIGuidItemData.Targat.Contains(";"))
                        {
                            var items = mUIGuidItemData.Targat.Split(';');
                            foreach (var item in items)
                            {
                                targetGa = guidUI.GetChild(mUIGuidItemData.Targat);

                                if (targetGa != null)
                                    break;
                            }
                        }
                        else
                        {
                            targetGa = guidUI.GetChild(mUIGuidItemData.Targat);
                        }
                    }
                }
                else
                {
                    guideTr.gameObject.SetActive(false);

                }
            }
            else
            {
                if (mUIGuidItemData.Targat.Contains("Auto:"))
                {
                    targetGa = AutoFind();
                }
                else if (mUIGuidItemData.Targat.Contains("AutoComplete:"))
                {
                    targetGa = AutoFindComplete();
                }
                else
                {
                    if (mUIGuidItemData.Targat.Contains(";"))
                    {
                        var items = mUIGuidItemData.Targat.Split(';');
                        foreach (var item in items)
                        {
                            targetGa = UIHelper.GetChild(uiGuid.Transform.gameObject, item);

                            if (targetGa != null)
                                break;
                        }
                    }
                    else
                    {
                        targetGa = UIHelper.GetChild(uiGuid.Transform.gameObject, mUIGuidItemData.Targat);
                    }
                }
            }

            return targetGa;
        }

        private void K3GameEventStart(Cfg.G.CBubble bubble)
        {
            // 游戏事件开始记录 - 当前已禁用
        }

        private void K3GameEventEnd(Cfg.G.CBubble bubble)
        {
            // 游戏事件结束记录 - 当前已禁用
        }

        private int CheckTargetTimer;

        private void DragImage()
        {
            if (mUIGuidItemData?.DragCityBG > 0)
            {
                var ui = WndMgr.Get<UIMainCity>();
                if (ui != null)
                {
                    int cityCfgID = LPlayer.I.GetCityBuildingIDByType(mUIGuidItemData.DragCityBG);
                    ui.dragImage.MoveTOCfg(cityCfgID, () =>
                    {
                    });
                }
            }

            if (CheckTargetTimer > 0)
                NTimer.Destroy(CheckTargetTimer);

            CheckTargetTimer = NTimer.Tick(5, 0.5f, () =>
            {
                if (targetBtn != null)
                {
                    if (!targetBtn.gameObject.activeInHierarchy)
                    {
                        return;
                    }
                }
                else if (targetTrigger != null)
                {
                    if (!targetTrigger.gameObject.activeInHierarchy)
                    {
                        return;
                    }
                }
                else if (mergeGoodBtn != null)
                {
                    if (!mergeGoodBtn.gameObject.activeInHierarchy)
                    {
                        return;
                    }
                }
                else if (targetTab != null)
                {
                    if (!targetTab.gameObject.activeInHierarchy)
                    {
                        return;
                    }
                }
            });
        }

        private GameObject AutoFind()
        {
            var heroIDStr = mUIGuidItemData.Targat.Replace("Auto:", "");

            int.TryParse(heroIDStr, out var heroID);

            if (mUIGuidItemData.ClassName == "UIHeroList")
            {
                var heroList = PopupManager.I.FindPopup<UIHeroList>();
                if (heroList != null)
                {
                    var heroItemList = heroList.loopListView.GetComponentsInChildren<UIHeroListItem>();
                    foreach (var item in heroItemList)
                    {
                        if (item.mHeroData.HeroCfg.Id == heroID)
                        {
                            return item.gameObject;
                        }
                    }
                }
            }
            else if (mUIGuidItemData.ClassName == "UIMerge")
            {
                var uiMerge = PopupManager.I.FindPopup<UIMerge>();
                if (uiMerge != null)
                {
                    foreach (var item in uiMerge.mGoodDic)
                    {
                        if (item != null && item.Info.Type == -1 && item.Info.id == heroID)
                        {
                            return item.gameObject;
                        }
                    }
                }
            }

            else if (mUIGuidItemData.ClassName == "UIStargazingPlatform")
            {
                var uiStargazingPlatform = PopupManager.I.FindPopup<UIStargazingPlatform>();
                if (uiStargazingPlatform != null)
                {
                    return uiStargazingPlatform.GetStarType(heroID);
                }
            }

            return null;
        }

        private GameObject AutoFindComplete()
        {
            var heroIDStr = mUIGuidItemData.Targat.Replace("AutoComplete:", "");

            int.TryParse(heroIDStr, out var heroID);

            if (mUIGuidItemData.ClassName == "UIStargazingPlatform")
            {
                var uiStargazingPlatform = PopupManager.I.FindPopup<UIStargazingPlatform>();
                if (uiStargazingPlatform != null)
                {
                    return uiStargazingPlatform.GetStarCompleteType(heroID);
                }
            }

            return null;
        }

        private void FindTrigger()
        {
            guideTr.gameObject.SetActive(true);
            guideTr.sizeDelta = targetTrigger.GetComponent<RectTransform>().sizeDelta + new Vector2(400, 400);

            UIHelper.GetAddComponent<FollowTarget>(guideTr.gameObject).InitTarget(targetTrigger.transform);
            fingerTr.gameObject.SetActive(false);

            float delay = mUIGuidItemData.Delay > 0 ? mUIGuidItemData.Delay * 0.001f : 0.2f;
            handObj.Stop();

            NTimer.CountDownNoPool(ref fingerTimer, delay, () =>
            {
                Vector3 gesture = FingerOffset(mUIGuidItemData.GestureOffset);
                Vector3 rotation = FingerOffset(mUIGuidItemData.GestureRotate);
                Vector3 scale = FingerOffset(mUIGuidItemData.GestureScale);

                if (mUIGuidItemData.GuideAction == 2 && fingerTr != null && fingerTr.gameObject != null)
                {
                    fingerTr.gameObject.SetActive(true);
                    var startPoint = Game.Utils.UIUtils.CenterLocalPoint(GameInstance.UICamera, guideTr);
                    fingerTr.localPosition = startPoint;
                    fingerMask.transform.localPosition = new Vector3(gesture.x, gesture.y);
                    fingerTr.localRotation = Quaternion.Euler(rotation.x, rotation.y, rotation.z);
                    fingerTr.localScale = new Vector3(scale.x, scale.y, scale.z);
                    fingerMask.transform.localPosition = new Vector3(gesture.x, gesture.y);
                    handObj.Stop();
                    m_HandEffect.SetActive(false);
                    fingerTween = fingerTr.DOLocalMove(new Vector3(gesture.x, gesture.y), 1f).OnComplete(() =>
                    {
                        m_HandEffect.SetActive(true);
                        handObj.Play("hand");
                    });
                }
                else
                {
                    fingerTr.gameObject.SetActive(true);
                    fingerTr.localPosition = new Vector3(gesture.x, gesture.y);
                    fingerMask.transform.localPosition = new Vector3(gesture.x, gesture.y);
                    fingerTr.localRotation = Quaternion.Euler(rotation.x, rotation.y, rotation.z);
                    fingerTr.localScale = new Vector3(scale.x, scale.y, scale.z);
                    fingerMask.transform.localPosition = new Vector3(gesture.x, gesture.y);
                    if (mUIGuidItemData.GuideAction == 5)
                    {
                        LongTouch(new Vector3(gesture.x, gesture.y));
                    }
                }

                SetCircleMask();

                if (mUIGuidItemData.Dubbing > 0)
                {
                    m_AudioId = GameAudio.PlayAudio(mUIGuidItemData.Dubbing);
                }

                EventTriggerType toGoEvent = EventTriggerType.Click;
                if (mUIGuidItemData.GuideAction == 5)
                    toGoEvent = EventTriggerType.LongPressBegin;

                AddRemoveListener(EventTriggerType.Click, guideTr.gameObject, (ga, point) =>
                {
                });

                AddRemoveListener(toGoEvent, guideTr.gameObject, (ga, point) =>
                {

                    CurClickEvent = point;

                    var tmpGuidData = mUIGuidItemData;

                    UIData.guidItems.Remove(tmpGuidData.Id);
                    K3GameEventEnd(mUIGuidItemData);

                    if (tmpGuidData.Targat == "content/LeftBtns/MainBtns/btns/BG/HeroSkillBtnRoot0(Clone)/HeroSkillBtn")
                    {
                        EventMgr.FireEvent(TEventType.UIMain_Update_LeftBtns, true);
                    }

                    if (!nextClickIDs.Contains(tmpGuidData.Id))
                    {
                        nextClickIDs.Add(tmpGuidData.Id);
                        NextGuid();
                    }
                });
            });
        }


        private void FindTFWTab()
        {
            guideTr.gameObject.SetActive(true);
            guideTr.sizeDelta = targetTab.GetComponent<RectTransform>().sizeDelta + new Vector2(400, 400);

            UIHelper.GetAddComponent<FollowTarget>(guideTr.gameObject).InitTarget(targetTab.transform);
            fingerTr.gameObject.SetActive(false);

            float delay = mUIGuidItemData.Delay > 0 ? mUIGuidItemData.Delay * 0.001f : 0.2f;
            handObj.Stop();

            NTimer.CountDownNoPool(ref fingerTimer, delay, () =>
            {
                Vector3 gesture = FingerOffset(mUIGuidItemData.GestureOffset);
                Vector3 rotation = FingerOffset(mUIGuidItemData.GestureRotate);
                Vector3 scale = FingerOffset(mUIGuidItemData.GestureScale);

                if (mUIGuidItemData.GuideAction == 2 && fingerTr != null && fingerTr.gameObject != null)
                {
                    fingerTr.gameObject.SetActive(true);
                    var startPoint = Game.Utils.UIUtils.CenterLocalPoint(GameInstance.UICamera, guideTr);
                    fingerTr.localPosition = startPoint;
                    fingerTr.localRotation = Quaternion.Euler(rotation.x, rotation.y, rotation.z);
                    fingerTr.localScale = new Vector3(scale.x, scale.y, scale.z);
                    fingerMask.transform.localPosition = new Vector3(gesture.x, gesture.y);
                    handObj.Stop();
                    m_HandEffect.SetActive(false);
                    fingerTween = fingerTr.DOLocalMove(new Vector3(gesture.x, gesture.y), 1f).OnComplete(() =>
                    {
                        m_HandEffect.SetActive(true);
                        handObj.Play("hand");
                    });
                }
                else
                {
                    fingerTr.gameObject.SetActive(true);
                    fingerTr.localPosition = new Vector3(gesture.x, gesture.y);
                    fingerTr.localRotation = Quaternion.Euler(rotation.x, rotation.y, rotation.z);
                    fingerTr.localScale = new Vector3(scale.x, scale.y, scale.z);
                    fingerMask.transform.localPosition = new Vector3(gesture.x, gesture.y);
                    if (mUIGuidItemData.GuideAction == 5)
                    {
                        LongTouch(new Vector3(gesture.x, gesture.y));
                    }
                }

                SetCircleMask();

                if (mUIGuidItemData.Dubbing > 0)
                {
                    m_AudioId = GameAudio.PlayAudio(mUIGuidItemData.Dubbing);
                }

                EventTriggerType toGoEvent = EventTriggerType.Click;
                if (mUIGuidItemData.GuideAction == 5)
                    toGoEvent = EventTriggerType.LongPressBegin;

                AddRemoveListener(EventTriggerType.Click, guideTr.gameObject, (ga, point) =>
                {
                });

                AddRemoveListener(toGoEvent, guideTr.gameObject, (ga, point) =>
                {
                    CurClickEvent = point;



                    var tmpGuidData = mUIGuidItemData;

                    this.UIData.guidItems.Remove(tmpGuidData.Id);
                    K3GameEventEnd(mUIGuidItemData);

                    if (tmpGuidData.Targat == "content/LeftBtns/MainBtns/btns/BG/HeroSkillBtnRoot0(Clone)/HeroSkillBtn")
                    {
                        EventMgr.FireEvent(TEventType.UIMain_Update_LeftBtns, true);
                    }

                    if (!nextClickIDs.Contains(tmpGuidData.Id))
                    {
                        nextClickIDs.Add(tmpGuidData.Id);
                        NextGuid();
                    }
                });
            });
        }

        void LongTouch(Vector3 pos0)
        {
            pos0 = new Vector3(52.8f, 13.8f, 0);
            Vector3 pos = pos0 + new Vector3(52.8f, 150, 0);
            fingerTr.localPosition = pos;
            fingerTr.localScale = Vector3.one * 1.25f;
            handObj.Stop();
            fingerTr.DOLocalMove(pos0, 0.25f).OnComplete<Tween>(() =>
            {
                fingerTr.DOLocalMove(pos, 0.25f).SetDelay(1.5f).OnComplete<Tween>(() =>
                {
                    LongTouch(pos0);
                });
            });

            fingerTr.DOScale(Vector3.one, 0.25f).OnComplete<Tween>(() =>
            {
                fingerTr.DOScale(Vector3.one * 1.25f, 0).SetDelay(1.5f).OnComplete<Tween>(() =>
                {
                });
            });
        }

        private Vector3 FingerOffset(List<String> offsets)
        {
            if (offsets.Count > 2)
            {
                return new Vector3(float.Parse(offsets[0]), float.Parse(offsets[1]), float.Parse(offsets[2]));
            }
            else if (offsets.Count > 1)
            {
                return new Vector3(float.Parse(offsets[0]), float.Parse(offsets[1]), 0);
            }
            return Vector3.zero;
        }

        private void FindTFWButton()
        {
            guideTr.gameObject.SetActive(true);
            guideTr.sizeDelta = targetBtn.GetComponent<RectTransform>().sizeDelta + new Vector2(400, 400);

            UIHelper.GetAddComponent<FollowTarget>(guideTr.gameObject).InitTarget(targetBtn.transform);
            fingerTr.gameObject.SetActive(false);

            handObj.Stop();

            float delay = mUIGuidItemData.Delay > 0 ? mUIGuidItemData.Delay * 0.001f : 0.2f;

            NTimer.CountDownNoPool(ref fingerTimer, delay, () =>
            {
                Vector3 gesture = FingerOffset(mUIGuidItemData.GestureOffset);
                Vector3 rotation = FingerOffset(mUIGuidItemData.GestureRotate);
                Vector3 scale = FingerOffset(mUIGuidItemData.GestureScale);

                if (mUIGuidItemData.GuideAction == 2 && fingerTr != null && fingerTr.gameObject != null)
                {
                    fingerTr.gameObject.SetActive(true);
                    var startPoint = Game.Utils.UIUtils.CenterLocalPoint(GameInstance.UICamera, guideTr);
                    fingerTr.localPosition = startPoint;
                    fingerTr.localRotation = Quaternion.Euler(rotation.x, rotation.y, rotation.z);
                    fingerTr.localScale = new Vector3(scale.x, scale.y, scale.z);
                    fingerMask.transform.localPosition = new Vector3(gesture.x, gesture.y);
                    handObj.Stop();
                    m_HandEffect.SetActive(false);
                    fingerTween = fingerTr.DOLocalMove(new Vector3(gesture.x, gesture.y), 1f).OnComplete(() =>
                    {
                        m_HandEffect.SetActive(true);
                        handObj.Play("hand");

                    });
                }
                else
                {
                    fingerTr.gameObject.SetActive(true);
                    fingerTr.localPosition = new Vector3(gesture.x, gesture.y);
                    fingerTr.localRotation = Quaternion.Euler(rotation.x, rotation.y, rotation.z);
                    fingerTr.localScale = new Vector3(scale.x, scale.y, scale.z);
                    fingerMask.transform.localPosition = new Vector3(gesture.x, gesture.y);
                }
                SetCircleMask();

                if (mUIGuidItemData.Dubbing > 0)
                {
                    m_AudioId = GameAudio.PlayAudio(mUIGuidItemData.Dubbing);
                }

                AddRemoveListener(EventTriggerType.Click, guideTr.gameObject, (ga, point) =>
                {
                    CurClickEvent = point;



                    var tmpGuidData = mUIGuidItemData;

                    this.UIData.guidItems.Remove(tmpGuidData.Id);

                    K3GameEventEnd(mUIGuidItemData);

                    if (tmpGuidData.Targat == "content/LeftBtns/MainBtns/btns/BG/HeroSkillBtnRoot0(Clone)/HeroSkillBtn")
                    {
                        EventMgr.FireEvent(TEventType.UIMain_Update_LeftBtns, true);
                    }

                    if (!nextClickIDs.Contains(tmpGuidData.Id))
                    {
                        nextClickIDs.Add(tmpGuidData.Id);
                        NextGuid();
                    }
                });
            });
        }

        private void SetCircleMask()
        {
            //if (mUIGuidItemData?.Mask == 1)
            //    bgMaskComp.curMaterial.DOFloat(300, "_Radius", 0.36f);
        }

        private void NextGuid()
        {

            CurDialog?.SelectRoot?.SetActive(false);
            CurDialog?.btnNext?.gameObject.SetActive(false);
            // 拦截等待期的重复点击触发
            if (guideTr.gameObject.activeInHierarchy)
            {
                guideTr.gameObject.SetActive(false);
            }

            if (m_FullChatObj.activeInHierarchy)
            {
                m_FullChatObj.SetActive(false);
            }

            if (m_FullGuide.activeInHierarchy)
            {
                m_FullGuide.SetActive(false);
            }

            if (m_AudioId > 0)
            {

                AudioManager.Instance.Stop(m_AudioId);
            }

            LogoOut(() =>
            {
                float delayTime = mUIGuidItemData.EpisodeDelay * 0.001f;
                Debug.Log($"LogDebug - {mUIGuidItemData.Id} 出场等待时间 ： {delayTime}");
                // 增加对下一步延迟效果
                NTimer.CountDown(delayTime, () =>
                {
                    Debug.Log($"LogDebug - {mUIGuidItemData.Id} 等待出场时间结束！ 等待时间 = {UIData.guidItems.Count}, {delayTime}, {UIData.guidItems.Count}");
                    if (UIData.guidItems.Count > 0)
                    {
                        UIHelper.GetAddComponent<FollowTarget>(guideTr.gameObject).InitTarget(null);
                        targetTrigger = null;
                        targetBtn = null;
                        mergeGoodBtn = null;
                        targetTab = null;
                        GameObject.Destroy(m_InteractionObject);
                        m_InteractionObject = null;
                        GameObject.Destroy(m_InteractionEffect);
                        m_InteractionEffect = null;
                        // m_FullSpineObj?.SetActive(false);
                        // WndMgr.Show<UIEpisodeGuid>(this.UIData);
                        // 临时处理 1010401 引导逻辑。
                        var data = Cfg.C.CBubble.I(this.UIData.guidItems[0]);
                        var next = data.Id != 1010401 && data.Id != 1010601;

                        if (next)
                        {
                            WndMgr.Show<UIEpisodeGuid>(this.UIData);
                        }
                    }
                    else
                    {
                        UIHelper.GetAddComponent<FollowTarget>(guideTr.gameObject).InitTarget(null);
                        int nextGroupID = Cfg.C.CEpisode.I(K3PlayerMgr.I.PlayerData.CurGuidGroupID)?.NextEpisode ?? 0;
                        if (nextGroupID > 0)
                        {

                            UIData.EndCallBack?.Invoke();
                        }
                        else
                        {
                            GameObject.Destroy(m_InteractionObject);
                            m_InteractionObject = null;
                            GameObject.Destroy(m_InteractionEffect);
                            m_InteractionEffect = null;
                            GameObject.Destroy(m_FullSpineObj);
                            m_FullSpineObj = null;
                            CloseSelf();
                        }
                    }
                });
            });
        }

        private void FindMergeButton()
        {
            guideTr.gameObject.SetActive(true);
            guideTr.sizeDelta = mergeGoodBtn.GetComponent<RectTransform>().sizeDelta + new Vector2(400, 400);

            UIHelper.GetAddComponent<FollowTarget>(guideTr.gameObject).InitTarget(mergeGoodBtn.transform);
            fingerTr.gameObject.SetActive(false);

            handObj.Stop();
            float delay = mUIGuidItemData.Delay > 0 ? mUIGuidItemData.Delay * 0.001f : 0.2f;

            NTimer.CountDownNoPool(ref fingerTimer, delay, () =>
            {
                Vector3 gesture = FingerOffset(mUIGuidItemData.GestureOffset);
                Vector3 rotation = FingerOffset(mUIGuidItemData.GestureRotate);
                Vector3 scale = FingerOffset(mUIGuidItemData.GestureScale);

                if (mUIGuidItemData.GuideAction == 2 && fingerTr != null && fingerTr.gameObject != null)
                {
                    fingerTr.gameObject.SetActive(true);
                    var startPoint = Game.Utils.UIUtils.CenterLocalPoint(GameInstance.UICamera, guideTr);
                    fingerTr.localPosition = startPoint;
                    fingerTr.localRotation = Quaternion.Euler(rotation.x, rotation.y, rotation.z);
                    fingerTr.localScale = new Vector3(scale.x, scale.y, scale.z);
                    handObj.Stop();
                    m_HandEffect.SetActive(false);
                    fingerTween = fingerTr.DOLocalMove(new Vector3(gesture.x, gesture.y), 1f).OnComplete(() =>
                    {
                        m_HandEffect.SetActive(true);
                        handObj.Play("hand");
                    });
                }
                else
                {
                    fingerTr.gameObject.SetActive(true);
                    fingerTr.localPosition = new Vector3(gesture.x, gesture.y);
                    fingerTr.localRotation = Quaternion.Euler(rotation.x, rotation.y, rotation.z);
                    fingerTr.localScale = new Vector3(scale.x, scale.y, scale.z);

                }
                SetCircleMask();

                if (mUIGuidItemData.Dubbing > 0)
                {
                    m_AudioId = GameAudio.PlayAudio(mUIGuidItemData.Dubbing);
                }

                EventTriggerType toGoEvent = EventTriggerType.Click;


                AddRemoveListener(EventTriggerType.Click, guideTr.gameObject, (ga, point) =>
                {
                });

                AddRemoveListener(toGoEvent, guideTr.gameObject, (ga, point) =>
                {
                    CurClickEvent = point;


                    var tmpGuidData = mUIGuidItemData;

                    this.UIData.guidItems.Remove(tmpGuidData.Id);

                    K3GameEventEnd(mUIGuidItemData);


                    if (!nextClickIDs.Contains(tmpGuidData.Id))
                    {
                        nextClickIDs.Add(tmpGuidData.Id);
                        NextGuid();
                    }
                });
            });


        }

        private NTimer.Timer closeUITimer, fingerTimer;

        private UnityEngine.EventSystems.PointerEventData CurClickEvent;

        public static void StartGuid(UIEpisodeGuidItemData uIGuidData)
        {


            var uiGuid = UI.WndMgr.Get<UIGuid>(); //对话引导 大于 Guid
            if (uiGuid != null)
            {
                uiGuid.Hide();
            }

            var uimerge = DeepUI.PopupManager.I.FindPopup<UIMerge>();
            uimerge?.StopCreatGoodGuide();


            WndMgr.Show<UIEpisodeGuid>(uIGuidData);
        }

        #region 过场动画

        private TFWRawImage m_RawImage;

        private TFWImage m_AlphaImage;

        private GameObject m_CutRoot;

        /// <summary>
        /// 默认全屏半径
        /// </summary>
        private float m_DefaultRadius = 0.4f;
        private float m_FinalRadius = 0;
        private Color m_ColorAlpha = new Color(0, 0, 0, 0);
        private Color m_ColorDefault = new Color(0, 0, 0, 1);

        /// <summary>
        /// 初始化过场动画
        /// </summary>
        private void InitCutScenes()
        {
            m_CutRoot = GetChild("GroupLoad");
            m_AlphaImage = GetComponent<TFWImage>("GroupLoad");
            m_RawImage = GetComponent<TFWRawImage>("GroupLoad/BackGround");
            m_RawImage.material.SetFloat("_Radius", m_DefaultRadius);
            m_AlphaImage.color = new Color(0, 0, 0, 0);
        }

        /// <summary>
        /// 初始化过场设置
        /// </summary>
        private void InitCutSetting()
        {
            // 0=无
            // 1=淡入
            // 2=聚焦展开
            if (mUIGuidItemData.CutIn == 1 || mUIGuidItemData.CutIn == 2)
            {
                Color color = mUIGuidItemData.CutIn == 1 ? m_ColorDefault : m_ColorAlpha;
                float radius = mUIGuidItemData.CutIn == 2 ? m_FinalRadius : m_DefaultRadius;
                m_RawImage.material.SetFloat("_Radius", radius);
                m_AlphaImage.color = color;
            }
            else
            {
                m_RawImage.material.SetFloat("_Radius", m_DefaultRadius);
                m_AlphaImage.color = m_ColorAlpha;
            }
            UpdateEnable = mUIGuidItemData.CutIn == 0;
        }

        /// <summary>
        /// 载入入场阶段
        /// </summary>
        /// <param name="logoInFinish"></param>
        private void LogoIn(Action logoInFinish)
        {

            // 入场初始化设置
            InitCutSetting();
            // 
            if (mUIGuidItemData.CutIn == 0)
            {
                logoInFinish?.Invoke();

            }
            // 入场逻辑
            else if (mUIGuidItemData.CutIn == 1)
            {
                float delay = Convert.ToSingle(mUIGuidItemData.CutInParam[0]);
                m_AlphaImage.color = m_ColorDefault;
                m_AlphaImage.DOFade(0, delay * 0.001f).OnComplete(() => logoInFinish?.Invoke());
            }
            //else
            //{
            //    // 位置
            //    Vector3 position = HeroUtils.Parse(mUIGuidItemData.CutInParam[0]);
            //    m_CutRoot.transform.localPosition = position;
            //    // 聚焦入场
            //    RecursionPlayZoom(mUIGuidItemData.CutInParam, m_DefaultRadius, 1, logoInFinish);
            //}
        }

        private void TriggerClickEvent()
        {
            if (targetTrigger != null)
            {
                targetTrigger.onClick.Invoke(targetTrigger.gameObject, CurClickEvent);
            }
            if (targetBtn != null)
            {
                targetBtn.onClick.Invoke();
            }

            if (targetTab != null)
            {
                targetTab.OnPointerClick(CurClickEvent);
            }
        }

        /// <summary>
        /// 出场
        /// </summary>
        private void LogoOut(Action logoOutFinish)
        {
            bool isBeforeTrigger = mUIGuidItemData.ActTriggerType == 1;

            if (isBeforeTrigger)
                TriggerClickEvent();

            NTimer.CountDown(mUIGuidItemData.CutOutDelay * 0.001f, () =>
            {
                D.Info?.Log($"LogDebug - {mUIGuidItemData.Id} 剧情引导出场方式 = {mUIGuidItemData.CutOut}, 间隔 {mUIGuidItemData.EpisodeDelay} 毫秒");
                if (mUIGuidItemData.CutOut == 0)
                {
                    logoOutFinish?.Invoke();
                    if (!isBeforeTrigger)
                        TriggerClickEvent();

                }
                // 出场逻辑
                else if (mUIGuidItemData.CutOut == 1)
                {
                    float delay = Convert.ToSingle(mUIGuidItemData.CutOutParam[0]);
                    D.Info?.Log($"LogDebug - {mUIGuidItemData.Id} 出场等待时间 = {delay}");
                    // 在重新启动前，确保保持黑屏状态
                    m_AlphaImage.color = m_ColorDefault; // 保持黑色
                    m_AlphaImage.DOFade(1, delay * 0.001f).OnComplete(() =>
                    {
                        logoOutFinish?.Invoke();
                        if (!isBeforeTrigger)
                            TriggerClickEvent();
                    });
                }

                // CurDialog?.ActiveDialog(false);
                // m_FullSpineObj?.SetActive(false);
            });
        }




        /// <summary>
        ///  
        /// </summary>
        private void ChangedSkin(GameObject target, string skinName)
        {
            D.Info?.Log($"LogDebug - {mUIGuidItemData.Id} 引导Spine的皮肤切换 = {skinName}");
            var anim = target.GetComponent<SkeletonGraphic>();
            // 获取 Skeleton 对象
            var skeleton = anim.Skeleton;
            if (skeleton != null)
            {
                // 查找新的皮肤
                var newSkin = skeleton.Data.FindSkin(skinName);
                if (newSkin != null)
                {
                    // 设置新的皮肤
                    skeleton.SetSkin(newSkin);
                    skeleton.SetSlotsToSetupPose();      // 更新插槽到设置姿势
                    anim.AnimationState.Apply(skeleton); // 应用动画状态
                }
                else
                {
                    Debug.LogWarning($"Skin '{skinName}' not found.");
                }
            }
        }
        #endregion
    }
}

