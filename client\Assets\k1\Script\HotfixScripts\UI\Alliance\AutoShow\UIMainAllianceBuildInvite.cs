﻿using Common;
using cspb;
using DeepUI;
using Game.Config;
using Game.Data;
using Logic;
using Public;
using Render;
using TFW;
using TFW.UI;
using UnityEngine;

namespace UI.Alliance 
{
    /// <summary>
    /// 建造方尖碑提示 
    /// </summary>
    public class UIMainAllianceBuildInvite : SpecialSortingUI
    {
        static bool isCheck = false;

        static int timerID = -1;
        static bool countDonwDone = false;
        static cspb.FlagInfo jumpFlagInfo = null;
        static cspb.Coord dragonCoord = null;
        static cspb.Coord mainCityCoord = null;
        static bool hasBuildFlag = false;

        TFWRawImage icon;

        protected override string assetPath => "MainCity/UIMainAllianceBuildInvite";

        protected override void OnLoad()
        {
   
            AddListener(EventTriggerType.Click, GetChild("Root/Msg/Bg/CloseBtn"), (a, b) =>
            {
                CloseSelf();
            });

            AddListener(EventTriggerType.Click, GetChild("Root/Msg/Bg/CheckBtn"), (a, b) =>
            {
                int x = 0, z = 0;

                if (mainCityCoord != null)
                {
                    x = mainCityCoord.X / 1000;
                    z = mainCityCoord.Z / 1000;
                }
                else if (jumpFlagInfo != null)
                {
                    x = jumpFlagInfo.coord.X / 1000;
                    z = jumpFlagInfo.coord.Z / 1000;
                }
                else if (dragonCoord != null)
                {
                    x = dragonCoord.X / 1000;
                    z = dragonCoord.Z / 1000;
                }
                else 
                {
                    return;
                }

                CloseSelf();
                //if(!DeadBeat.I.IsDeadBeat)
                //    WndMgr.Hide<UI.Alliance.UIAllianceMain>();
                //显示提示信息
                var pos = new Vector2Int(x, z);
                //WndMgr.HideAll();
                EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.WORLD);
                //UIMain.I?.TrySwitch(MainMenuConst.WORLD);//切到大世界
                RMap.JumpIntoTargetPosition(pos.x, pos.y - 15, null, true);
            });

            GetComponent<TFWText>("Root/Msg/Bg/CheckBtn/Image").text = "Map_Scout_Info_Cap".ToLocal();
            icon = GetComponent<TFWRawImage>("Root/Msg/Bg/Image");
        }

        public static void ShowCountDown() 
        {
            countDonwDone = false;
            timerID = NTimer.CountDown(MetaConfig.alliance_ai_building_time, () =>
            {
                countDonwDone = true;
                CheckCanShow();
            });
        } 

        public static void DeInit() 
        {
            if (timerID != -1) 
            {
                NTimer.Destroy(timerID);
                timerID = -1;
            }
            isCheck = false;
            countDonwDone = false;
            jumpFlagInfo = null;
            hasBuildFlag = false;
        }

        public static void CheckCanShow()
        {
            if (LSetting.I.GetSettingValue(LSetting.NotifiSettingType.PushOnBuildObelisk) > 0.1f)
            {

                if (isCheck || !countDonwDone)
                    return;

                var flags = LAllianceTerritoryBuilding.I.Flags(out var invalidCount);
                bool isJoinBuild = false;

                foreach (var item in flags)
                {
                    if (item == null)
                        continue;
                    if ((item.state == cspb.FortState.FortStateNormal && item.endTs > 0) || (item.state == cspb.FortState.FortStateBuilding && item.buildTs == 0))
                    {
                        hasBuildFlag = true;
                        if (jumpFlagInfo == null)
                            jumpFlagInfo = item;
                        if (item.GetIsSelfTroopStation())
                        {
                            isJoinBuild = true;
                            break;
                        }
                    }
                }
                ///当前已经有部队参加建设了 不弹出了 
                if (!isJoinBuild)
                {
                    CheckRally();
                    if (!hasBuildFlag)
                    {
                        isCheck = true;
                        return;
                    }
                }
                else
                {
                    isCheck = true;
                    return;
                }

                if (GameData.I.MainData.CurrMenuType == MainMenuType.WORLD || GameData.I.MainData.CurrMenuType == MainMenuType.CITY)//|| GameData.I.MainData.CurrMenuType == MainMenuType.PRISON
                {
                    ///展示界面
                    isCheck = true;
                    WndMgr.Show<UIMainAllianceBuildInvite>();
                }
            }
           
        }

        static void CheckRally() 
        {
            if (dragonCoord != null || mainCityCoord != null)
            {
                isCheck = true;
                return;
            }
            var rallys = AllianceGameData.I.MilitaryAlertData.GetAllyData();
            long PlayerID = LPlayer.I.PlayerID;
            for (int i = 0; i < rallys.Count; i++)
            {
                
                var item = rallys[i];
                if (item.aSide == null || item.bSide == null)
                    continue;
                if (dragonCoord == null)
                {
                    util_Dragon(item, ref PlayerID);
                }
                else 
                {
                    return;
                }
                if (mainCityCoord == null)
                {
                    util_MainCity(item, ref PlayerID);
                }
                else
                {
                    return;
                }
            }
            isCheck = true;
        }

        static void util_Dragon(Information item,ref long PlayerID) 
        {
            if (item.aSide.type == cspb.MapUnitType.MapUnitCentralCity)
            {
                if (item.aSide.pId != PlayerID && item.bSide.pId != PlayerID)
                {
                    dragonCoord = item.aSide.coord;
                    isCheck = true;
                    //WndMgr.Show<UIMainAllianceBuildInvite>();
                    hasBuildFlag = true;
                }
            }
            else if (item.bSide.type == cspb.MapUnitType.MapUnitCentralCity)
            {
                if (item.aSide.pId != PlayerID && item.bSide.pId != PlayerID)
                {
                    dragonCoord = item.bSide.coord;
                    isCheck = true;
                    //WndMgr.Show<UIMainAllianceBuildInvite>();
                    hasBuildFlag = true;
                }
            }
        }

        static void util_MainCity(Information item, ref long PlayerID)
        {
            if (item.aSide.type == cspb.MapUnitType.MapUnitFort)
            {
                if (item.aSide.pId != PlayerID && item.bSide.pId != PlayerID)
                {
                    mainCityCoord = item.aSide.coord;
                    isCheck = true;
                    hasBuildFlag = true;
                    //WndMgr.Show<UIMainAllianceBuildInvite>();
                }
            }
            else if (item.bSide.type == cspb.MapUnitType.MapUnitFort)
            {
                if (item.aSide.pId != PlayerID && item.bSide.pId != PlayerID)
                {
                    mainCityCoord = item.bSide.coord;
                    isCheck = true;
                    //   WndMgr.Show<UIMainAllianceBuildInvite>();
                    hasBuildFlag = true;
                }
            }
        }

        protected override void OnInit()
        {
       
        }

        protected override void OnShown()
        {
            base.OnShown();
            if (mainCityCoord != null)
            {
                GetComponent<TFWText>("Root/Msg/Bg/Text").text = "Alliance_Building_info".ToLocal("Union_territory_name_1".ToLocal());
                GetComponent<TFWText>("Root/Msg/Bg/Text2").text = "Alliance_Building_info1".ToLocal("Union_territory_name_1".ToLocal());
                UIHelper.SetRawImage(icon, "Assets/k1/Res/UI/Texture/AllianceBanner/UI_Alliance_Img_Fortress.png");
            }
            if (jumpFlagInfo != null)
            {
                GetComponent<TFWText>("Root/Msg/Bg/Text").text = "Alliance_Building_info".ToLocal("Union_territory_label_2".ToLocal());
                GetComponent<TFWText>("Root/Msg/Bg/Text2").text = "Alliance_Building_info1".ToLocal("Union_territory_label_2".ToLocal());
                UIHelper.SetRawImage(icon, "Assets/k1/Res/UI/Texture/AllianceBanner/UI_Alliance_Img_Flag.png");
            }
            else if (dragonCoord != null)
            {
                GetComponent<TFWText>("Root/Msg/Bg/Text").text = "Alliance_Attack_info".ToLocal();
                GetComponent<TFWText>("Root/Msg/Bg/Text2").text = "Alliance_Attack_info1".ToLocal();
                UIHelper.SetRawImage(icon, "Assets/k1/Res/UI/Texture/AllianceBanner/UI_Alliance_Img_DragonNest.png");
            }

        }
    }
}