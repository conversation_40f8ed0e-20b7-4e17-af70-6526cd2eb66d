﻿using System;
using System.IO;
using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.U2D;
using UnityEngine.UI;
using Object = UnityEngine.Object;

namespace TFW
{
    interface IResourceMgrImpl
    {
        /// <summary>
        /// 加载内部资源数据
        /// </summary>
        /// <param name="tag"></param>
        /// <returns></returns>
        [Obsolete]
        SpriteAtlas LoadBuiltRes(string tag);

        /// <summary>
        /// 检查Resource文件是否存在
        /// </summary>
        /// <param name="assetPath">资源路径</param>
        bool Exists(string assetPath);

        T LoadAsset<T>(string assetKey) where T : Object;

        UniTask<T> LoadAssetAsync<T>(string assetKey)
            where T : Object;

        /// <summary>
        /// 以流形式加载文本资源
        /// 注：用后记得要配对调用UnloadText卸载资源。
        /// </summary>
        /// <param name="assetPath">资源数据体.</param>
        Stream LoadTextStream(string assetPath);
        
        UniTask<Stream> LoadTextStreamAsync(string assetPath);

        UniTask<byte[]> LoadTextBytesAsync(string assetPath);

        GameObject LoadInstance(string assetKey, Transform parent = null);
        
        UniTask<GameObject> LoadInstanceAsync(string assetKey, Transform parent = null);

        [Obsolete("使用 LoadAssetAsync 代替")]
        UniTask<Texture> LoadTextureAssetAsync(string assetPath);

        void Release(string assetKey);

        void ReleaseInstance(string assetKey);

        void ReleaseInstance(GameObject instance);

        UniTask<Sprite> LoadSpriteInstanceAsync(string assetPath);

        UniTask SetImageSpriteAsync(Image image, string spriteAssetPath, bool useNaticeSize = false);

        UniTaskVoid SetRawImageTextureAsync(RawImage rawImage, string textureAssetPath,
            bool useNaticeSize = false);
        
        /// <summary>
        /// 同步加载资源
        /// </summary>
        /// <typeparam name="T">资源类型</typeparam>
        /// <param name="path">存放的编辑器路径</param>
        /// <param name="defaultAssetPath">当前资源不存在时的默认资源路径</param>
        /// <param name="fixExt">是否进行资源修复</param>
        /// <param name="isClone">是否Clone对象，只对不是从缓存池中取的prefab生效</param>
        /// <returns>序列化后的资源对象</returns>
        [Obsolete("使用 LoadAsset/LoadInstance 代替")]
        (bool isUseDefaultAsset, T asset) LoadResource<T>(string path, string defaultAssetPath = null,
            bool fixExt = false, bool isClone = true) where T : Object;

        /// <summary>
        /// 以字符串形式加载文本资源，并自动卸载资源。
        /// </summary>
        /// <param name="assetPath">资源数据体.</param>
        [Obsolete("使用 LoadTextStream/LoadTextBytes/LoadTextLinesAsync 代替")]
        string LoadTextAutoUnload(string assetPath);

        /// <summary>
        /// 以字符串形式加载文本资源。
        /// 注：用后记得要配对调用UnloadText卸载资源。
        /// </summary>
        /// <param name="assetPath">资源数据体.</param>
        [Obsolete("使用 LoadTextStream/LoadTextBytes/LoadTextLinesAsync 代替")]
        string LoadText(string assetPath);

        /// <summary>
        /// 文本资源加载为字符数组
        /// 注：用后记得要配对调用UnloadText卸载资源。
        /// </summary>
        /// <param name="assetPath"></param>
        /// <returns></returns>
        byte[] LoadTextBytes(string assetPath);

        /// <summary>
        /// 销毁一个GameObject
        /// 资源不会放入通用缓存池中
        /// </summary>
        [Obsolete("使用 Release 代替")]
        void Unload(GameObject obj);

        /// <summary>
        /// 延迟销毁
        /// 资源不会放入通用缓存池中
        /// </summary>
        [Obsolete("使用 Release 代替")]
        void Unload(GameObject obj, float delayTime);

        /// <summary>
        /// 卸载一个资源数据，资源会放入通用缓存池中
        /// </summary>
        /// <param name="path">路径</param>
        /// <param name="obj">对象</param>
        /// <param name="isInstance">是否是一个实例(当图集预设时,就不是一个克隆的实例,只是prefab就不需要销毁只需要卸载)</param>
        /// <param name="action">真正要删除资源的回调事件</param>
        [Obsolete("使用 Release 代替")]
        void Unload(string path, Object obj, bool isInstance = true, Action action = null);

        /// <summary>
        /// 卸载Assets资源
        /// </summary>
        /// <param name="path"></param>
        /// <param name="fixExt"></param>
        [Obsolete("使用 Release 代替")]
        void Unload(string path, bool fixExt = false);

        /// <summary>
        /// 卸载文本Assets资源
        /// </summary>
        /// <param name="path"></param>
        [Obsolete("使用 Release 代替")]
        void UnloadText(string path);

        [Obsolete("使用 LoadInstanceAsync 代替")]
        void LoadGameObjectAsync(string assetPath, string defaultAssetPath,
            UnityAction<string, GameObject> onOver);

        [Obsolete]
        void UnloadAssetBundle(string assetPath, bool unloadAll = false);

        [Obsolete("使用 Object.Destroy 代替")]
        void DestroyGameObj(UnityEngine.Object obj);


        void LateUpdate();

        void ReleaseMemory();
    }
}