﻿ 


using Cfg.C;
using Common;
using cspb;
using Game.Config;
using Game.Utils;
using Logic;
using Public;
using System;
using System.Collections.Generic;
using KS;
using TFW.Localization;
using TFW.UI;
using UnityEngine;
using UnityEngine.EventSystems;
using DeepUI;
using Game.Data;
using TFW;

namespace UI
{
    /// <summary>
    /// 采集者类型
    /// </summary>
    public enum GathererType
    {
        /// <summary>
        /// 无
        /// </summary>
        None,

        /// <summary>
        /// 自己
        /// </summary>
        Self,

        /// <summary>
        /// 盟友
        /// </summary>
        Ally,

        /// <summary>
        /// 敌人
        /// </summary>
        Enemy,
    }

    [Popup("Gather/UIGather")]
    public class UIGather : HistoricPopup
    {
        //protected string assetPath => "Gather/UIGather";
        //public ESortingOrder SortingOrder => ESortingOrder.Auto;
        //public EPopPanelType PopPanelType => EPopPanelType.PopUp;

        protected internal override bool PopBackEnabled => true;

        /// <summary>
        /// 主菜单互斥
        /// </summary>
        //public bool BottomMiddle { get; set; } = true;

        protected long m_EntityId;

        //--- 初始化
        protected override void OnInit()
        {
            base.OnInit();

        }

        /// <summary>
        /// 类型
        /// </summary>
        private GathererType _gathererType = GathererType.None;

        /// <summary>
        /// 采集的等级
        /// </summary>
        private TFWText _level;
        /// <summary>
        /// 采集的名称
        /// </summary>
        private TFWText _name;
        /// <summary>
        /// 采集生命剩余时间
        /// </summary>
        private TFWText _remainTime;

        /// <summary>
        /// 总可采集的资源数
        /// </summary>
        private TFWText _resTotalNum;
        /// <summary>
        /// 每小时可采集的资源数
        /// </summary>
        private TFWText _resEvHourNum;
        /// <summary>
        /// 采集的资源图
        /// </summary>
        private TFWImage _resImg;

        /// <summary>
        /// 采集者信息
        /// </summary>
        private GameObject _gather;
        /// <summary>
        /// 采集者标题
        /// </summary>
        private TFWText _gathererTitle;
        /// <summary>
        /// 采集者名称
        /// </summary>
        private TFWText _gathererName;
        /// <summary>
        /// 采集者头像
        /// </summary>
        private GameObject _gathererHead;

        /// <summary>
        /// 资源
        /// </summary>
        private GameObject _resObj;
        /// <summary>
        /// 资源数
        /// </summary>
        private TFWText _resTitle;
        /// <summary>
        /// 资源名
        /// </summary>
        private TFWText _resName;
        /// <summary>
        /// 资源图标
        /// </summary>
        private TFWImage _resIcon;

        /// <summary>
        /// 采集进度
        /// </summary>
        private GameObject _resGatherProcess;
        /// <summary>
        /// 采集数
        /// </summary>
        private TFWText _resGatherNum;
        /// <summary>
        /// 采集时间
        /// </summary>
        private TFWText _resGatherTime;
        /// <summary>
        /// 采集进度描述
        /// </summary>
        private TFWText _resGatherTitle;
        /// <summary>
        /// 采集进度
        /// </summary>
        private TFWSlider _resGatherValue;
        /// <summary>
        /// 按钮根部
        /// </summary>
        private GameObject _buttonRoot;

        /// <summary>
        /// 按钮根部
        /// </summary>
        private GameObject _recallBtn;

        /// <summary>
        /// 按钮根部
        /// </summary>
        private GameObject _attackBtn;

        /// <summary>
        /// 按钮根部
        /// </summary>
        private GameObject _gatherBtn;

        /// <summary>
        /// 采集
        /// </summary>
        private NpcGatherInfo npcGatherInfo;

        /// <summary>
        /// 分享按钮
        /// </summary>
        private GameObject _shareBtn = null;

        /// <summary>
        /// 收藏按钮
        /// </summary>
        private GameObject _favoriteBtn;

        /// <summary>
        /// OnLoad
        /// </summary>
        protected override void OnLoad()
        {
            //base.OnLoad();

            _level = GetComponent<TFWText>("Root/BG/Top/Name/Level");
            _name = GetComponent<TFWText>("Root/BG/Top/Name/Level/NameText");
            _remainTime = GetComponent<TFWText>("Root/BG/Top/Time/Level");

            //资源数
            _resTotalNum = GetComponent<TFWText>("Root/BG/Content/Desc/Reserves/Text");
            _resEvHourNum = GetComponent<TFWText>("Root/BG/Content/Desc/EveryHour/Text");
            //资源图标
            _resImg = GetComponent<TFWImage>("Root/BG/Content/Icon/Img");

            //采集者
            _gather = GetChild("Root/BG/Content/Gatherer");
            _gathererTitle = GetComponent<TFWText>("Root/BG/Content/Gatherer/Title");
            _gathererName = GetComponent<TFWText>("Root/BG/Content/Gatherer/Name");
            _gathererHead = GetChild("Root/BG/Content/Gatherer/Head_k1");

            //资源
            _resObj = GetChild("Root/BG/Content/Loot");
            _resTitle = GetComponent<TFWText>("Root/BG/Content/Loot/Title");
            _resName = GetComponent<TFWText>("Root/BG/Content/Loot/Text");
            _resIcon = GetComponent<TFWImage>("Root/BG/Content/Loot/Img");

            //进度
            _resGatherProcess = GetChild("Root/BG/Slider");
            _resGatherTitle = GetComponent<TFWText>("Root/BG/Slider/Title");
            _resGatherValue = GetComponent<TFWSlider>("Root/BG/Slider/ExpSlider");
            _resGatherNum = GetComponent<TFWText>("Root/BG/Slider/Num");
            _resGatherTime = GetComponent<TFWText>("Root/BG/Slider/Time/Level");

            _buttonRoot = GetChild("Root/BG/Button");
            _recallBtn = GetChild("Root/BG/Button/GatherBtn");
            _attackBtn = GetChild("Root/BG/Button/EnemyOccupiedBtn");
            _gatherBtn = GetChild("Root/BG/Button/NoPeopleGatherBtn");



            AddListener(TFW.EventTriggerType.Click, "Root/BG/Button/GatherBtn", OnRecallClick);//召回
            AddListener(TFW.EventTriggerType.Click, "Root/BG/Button/EnemyOccupiedBtn", OnAttackClick);//攻击
            AddListener(TFW.EventTriggerType.Click, "Root/BG/Button/NoPeopleGatherBtn", OnGatherClick);//采集
            _shareBtn = GetChild("Root/BG/Top/ShareBtn");
            _favoriteBtn = GetChild("Root/BG/Top/StarBtn");
            if (_shareBtn == null)
                Debug.Log("UIGather.ShareBtn is null!");

            AddListener(TFW.EventTriggerType.Click, _favoriteBtn, OnFavoriteClick);
            AddListener(TFW.EventTriggerType.Click, _shareBtn, OnShareClick);
        }

        /// <summary>
        /// 注册事件
        /// </summary>
        protected override void RegisterEvent()
        {
            base.RegisterEvent();
            RegisterEvent(TEventType.EntityResourceUpdate, OnEntityResourceUpdate);
            RegisterEvent(TEventType.EntityResourceDelete, OnEntityResourceDelete);
            FrameUpdateMgr.RegisterPerSecondFunListUpdate(Name, UpdatePerSecond);
            //EventMgr.RegisterEvent(TEventType.PlayerHeadInfoAck, OnPlayerHeadAck, this);
            //--部队掠夺车站的时候 资源点没有消息过来, 通过部队状态消息来判断
            RegisterEvent(TEventType.EntityTroopExitGather, OnTroopExitGather);
            RegisterEvent(TEventType.EntityResourceTroopChange, OnTroopChange);

        }

        /// <summary>
        /// 部队id
        /// </summary>
        private long troopId { get; set; }

        //private void OnPlayerHeadAck(object[] obj)
        //{
        //    if (!IsLoaded) return;
        //    if (npcGatherInfo != null)
        //    {
        //        var entity = LMapEntityManager.I.GetEntityInfo(troopId);
        //        if (entity == null || entity.owner == null) return;
        //        var ack = obj[0] as Dictionary<long, PlayerHeadInfo>;
        //        foreach (var kv in ack)
        //        {
        //            if (kv.Key == entity.owner.ID)
        //            {
        //                if (kv.Value.head != null)
        //                {
        //                    var playerHeadIcon = this._gathererHead;
        //                    UITools.SetHeadFrame(playerHeadIcon, kv.Value.head.avatarCfgID, kv.Value.head.frame);
        //                    UIHelper.ShowPlayerInfoMenuWhenClickHead(playerHeadIcon, kv.Key);
        //                }
        //            }
        //        }
        //    }
        //}

        private void OnTroopExitGather(object[] ids)
        {
            if (clientGatherInfo != null)
            {
                foreach (long id in ids)
                {
                    if (clientGatherInfo.detail.troopID == id)
                    {
                        Close();
                    }
                }
            }
        }

        private void OnTroopChange(object[] ids)
        {
            //-- 部队出了采集点

            if (clientGatherInfo != null)
            {
                Dictionary<string, object> data = ids[0] as Dictionary<string, object>;
                if ((long)data["outTroopID"] == clientGatherInfo.detail.troopID)
                {
                    Close();
                }
            }
        }

        /// <summary>
        /// 显示
        /// </summary>
        protected override async void OnShown()
        {
            base.OnShown();
            var uidata = (IHistoricPopupData)Data;
            m_EntityId = uidata.EntityId;

            CheckState();
            _shareBtn.SetActive(!LPlayer.I.IsCrossServer);
            _favoriteBtn.SetActive(!LPlayer.I.IsCrossServer);
            var data = LGatherInfo.I.GetGatherableNpc(m_EntityId);
            if (data != null)
            {
                this._level.text = string.Format("Lv.{0}", LGatherInfo.I.GetGatherLevel(m_EntityId));
                this._name.text = LGatherInfo.I.GetGatherName(m_EntityId);
                this._resTotalNum.text = ResourceUtils.GetResourceShowStr(LGatherInfo.I.GetGatherRssRemain(m_EntityId));
                var speed = LGatherInfo.I.GetCollectorSpeed(m_EntityId);
                var entity = LMapEntityManager.I.GetEntityInfo(troopId);
                
                Debug.LogFormat("CollectorSpeed={0},GoldSpeed={1}", speed, data.GoldSpeed());
                if (speed > 0)
                {
                    //采集之后以采集的GatherDetail 结构里的速度为准
                    _resEvHourNum.text = ResourceUtils.GetResourceShowStr((long)(speed * 3600000));
                }
                else
                {
                    List<long> heroIDs = new List<long>();
                    if (entity?.PlayerTroop?.heros?.Count > 0)
                    {
                        foreach (var item in entity.PlayerTroop.heros)
                        {
                            var hero = HeroGameData.I.GetHeroByCfgId(item.cfgID);
                            if (hero != null)
                                heroIDs.Add(hero.HeroId);
                        }
                    }

                    //采集之前以buff里的速度为准
                    _resEvHourNum.text = ResourceUtils.GetResourceShowStr((long)(await data.GoldSpeed(heroIDs.ToArray()) * 3600000));
                }

                //UITools.SetImage(_resIcon, data.DisplayKey);

                //UITools.SetImage(_resImg, data.DisplayKey);

                //ResourceMgr.LoadImage(rssIcon,
                //    ResourceDict.GetProxyAssetPath(ProxyAssetType.Icon, CRss.I(LGatherInfo.I.GetGatherRssId(entityId)).DisplayKey));
                //UIGatherHelper.SetRssIcon(_resIcon, m_EntityId);
                //UIGatherHelper.SeNpcGatherIcon(_resImg, m_EntityId);
            }

            //RefreshTime();
            UpdatePerSecond(0f);
        }

        /// <summary>
        /// 最大采集数量
        /// </summary>
        private int _gatherMaxCount;

        /// <summary>
        /// 刷新自己的采集数据
        /// </summary>
        public async void RefreshMyGather()
        {
            if (gameObject == null) { return; }
            if (npcGatherInfo != null)
            {
                var troopID = npcGatherInfo.troopID;

                var useCount = LGatherInfo.I.GetCollectorUseLoad(troopID);
                
                this._resTotalNum.text = ResourceUtils.GetResourceShowStr(LGatherInfo.I.GetGatherRssRemain(m_EntityId));
                var data = LGatherInfo.I.GetGatherableNpc(m_EntityId);
                var entity = LMapEntityManager.I.GetEntityInfo(troopId);
                if (data != null)
                {
                    List<long> heroIDs = new List<long>();
                    if (entity?.PlayerTroop?.heros?.Count > 0)
                    {
                        foreach (var item in entity.PlayerTroop.heros)
                        {
                            var hero = HeroGameData.I.GetHeroByCfgId(item.cfgID);
                            if (hero != null)
                                heroIDs.Add(hero.HeroId);
                        }
                    }

                    var speed = LGatherInfo.I.GetCollectorSpeed(troopID);
                   Debug.LogFormat("CollectorSpeed={0},GoldSpeed={1}", speed, data.GoldSpeed(heroIDs.ToArray()));
                    if (speed > 0)
                    {
                        //采集之后以采集的GatherDetail 结构里的速度为准
                        _resEvHourNum.text = ResourceUtils.GetResourceShowStr((long)(speed * 3600000));
                    }
                    else
                    {
                        //采集之前以buff里的速度为准
                        _resEvHourNum.text = ResourceUtils.GetResourceShowStr((long)(await data.GoldSpeed(heroIDs.ToArray()) * 3600000));
                    }
                }

                this._resGatherNum.text = $"{useCount}/{_gatherMaxCount}";
                this._resGatherTime.text = $"{UIHelper.GetFormatTime((LGatherInfo.I.GetCollectorRemainTime(troopID)))}";
                this._resGatherValue.value = useCount / (float)_gatherMaxCount;
                ////-- 采集量
                //if (m_CurLabType == 0)
                //{
                //    m_LabProgress.text = $"{useCount}/{_gatherMaxCount}";
                //    //-- 采集时间
                //    m_LabProgressTips.text = $"{LocalizationMgr.Get("LC_MAP_gather_amount")}";
                //}
                //else
                //{
                //    m_LabProgress.text = $"{UIHelper.GetFormatTime((LGatherInfo.I.GetCollectorRemainTime(troopID)))}";
                //    m_LabProgressTips.text = $"{LocalizationMgr.Get("LC_MAP_gather_time")}";
                //}

                //m_ImgProgress.fillAmount = useCount / (float)_gatherMaxCount;

                if (this._remainTime)
                {
                    if (npcGatherInfo != null && npcGatherInfo.deadline != 0)
                    {
                        var duration = Math.Max(0, (int)((npcGatherInfo.deadline - GameTime.Time)));
                        if (clientGatherInfo != null)
                        {
                            var time = GameTime.Time - clientGatherInfo.detail.startTs;
                            //Gather_Hero_Card_Get 已获得英雄:
                            var count = (int)((time / 1000) / MetaConfig.GatherHeroCDTimes);
                            //this._resName.text = string.Format("{0} {1}", LocalizationMgr.Get("Gather_Hero_Card_Get"), count);
                        }
                        _remainTime.text = UIHelper.GetFormatTime(duration);
                    }
                }
            }
        }

        /// <summary>
        /// 采集信息
        /// </summary>
        private GatherInfoAck clientGatherInfo;

        /// <summary>
        /// 自己采集
        /// </summary>
        /// <param name="ack"></param>
        private void ShowMyGatherData(GatherInfoAck ack)
        {
            //D.Error?.Log("ShowMyGatherData");
            clientGatherInfo = ack;
            if (this != null && this.IsShow && _recallBtn != null)
            {
                //-- 采集最大量计算
                var initReserveCount = LGatherInfo.I.GetGatherRssRemain(m_EntityId);
                var initUseCount = LGatherInfo.I.GetCollectorUseLoad(npcGatherInfo.troopID);
                var initTotalCount = LGatherInfo.I.GetCollectorTotalLoad(npcGatherInfo.troopID);
                _gatherMaxCount = (int)Math.Min(initReserveCount + initUseCount, initTotalCount);

                UIHelper.GetAddComponent<SelfHeadIconCompent>(this._gathererHead);
                //this._gathererName.text = LPlayer.I.GetPlayerName();

                var target = LMapEntityManager.I.GetEntityInfo(troopId);
                if (target != null)
                {
                    var unionName = string.IsNullOrEmpty(target.owner?.unionNickName) ? "" : $"[<color=#9ECAFF>{target.owner?.unionNickName}</color>]";
                    _gathererName.text = $"{unionName} {target?.owner.name}";
                }

                _recallBtn.SetActive(true);
                _attackBtn.SetActive(false);
                _gatherBtn.SetActive(false);

                _resObj.SetActive(false);
                _gather.SetActive(true);

                //fix by 20210707 zjw  老铁后面改为 UpdatePerSecond里刷新了，在这不要调用了，导致每次打开界面时间重置显示不对
                //RefreshMyGather();
            }
        }

        /// <summary>
        /// 敌人采集
        /// </summary>
        /// <param name="info"></param>
        private void ShowEnemyGatherData(NpcGatherInfo info)
        {
            if (_recallBtn == null
                || _attackBtn == null
                || _gatherBtn == null)
                return;

            //D.Error?.Log("ShowEnemyGatherData");
            _recallBtn.SetActive(false);
            _attackBtn.SetActive(true);
            _gatherBtn.SetActive(false);
            _resObj.SetActive(false);
            _gather.SetActive(true);

            //蓝色   9ECAFF
            //红色   FF5151
            var target = LMapEntityManager.I.GetEntityInfo(troopId);
            if (target != null && _gathererName != null)
            {
                var unionName = string.IsNullOrEmpty(target.owner?.unionNickName) ? "" : $"[<color=#FF5151>{target.owner?.unionNickName}</color>]";
                _gathererName.text = $"{unionName} {target?.owner.name}";
            }
        }

        /// <summary>
        /// 盟友采集
        /// </summary>
        /// <param name="info"></param>
        private void ShowAllyGatherData(NpcGatherInfo info)
        {
            //D.Error?.Log("ShowAllyGatherData");
            _recallBtn.SetActive(false);
            _attackBtn.SetActive(false);
            _gatherBtn.SetActive(false);
            _resObj.SetActive(false);
            _gather.SetActive(true);
            var target = LMapEntityManager.I.GetEntityInfo(troopId);
            if (target != null)
            {
                var unionName = string.IsNullOrEmpty(target.owner?.unionNickName) ? "" : $"[<color=#9ECAFF>{target.owner?.unionNickName}</color>]";
                _gathererName.text = $"{unionName} {target?.owner.name}";
            }
        }

        /// <summary>
        /// 无采集
        /// </summary>
        /// <param name="info"></param>
        private void ShowGatherData(NpcGatherInfo info)
        {
            //D.Error?.Log("ShowGatherData");
            _recallBtn.SetActive(false);
            _attackBtn.SetActive(false);
            _gatherBtn.SetActive(true);

            this._gathererName.text = LocalizationMgr.Get("Gather_Status_No_One");
            this._gathererHead.SetActive(false);
        }

        /// <summary>
        /// 获取头像信息
        /// </summary>
        private void GetHeadInfo()
        {
            var entityInfo = LMapEntityManager.I.GetEntityInfo(troopId);
            if (entityInfo?.owner != null)
            {
                LPlayer.I.TargetHeadInfoReq(entityInfo.owner.ID);
            }
        }

        /// <summary>
        /// 检查状态
        /// </summary>
        private void CheckState()
        {
            var npcGatherModule = LMapEntityManager.I.GetEntityModule<LMapEntityNpcGather>();
            if (npcGatherModule != null)
            {
                var gatherId = m_EntityId;
                npcGatherInfo = npcGatherModule.GetNpcGatherInfo(m_EntityId);
                if (npcGatherInfo != null)
                {
                    troopId = npcGatherInfo.troopID;
                    //D.Error?.Log("CheckState troopId=========={0}", troopId);
                    if (this._resName != null)
                    {
                        this._resName.text = LocalizationMgr.Get("Gather_Random_Hero");
                    }

                    if (troopId > 0)
                    {
                        this._gathererHead.SetActive(true);
                        var troopEntity = LMapEntityManager.I.GetEntityInfo(troopId);
                        if (troopEntity != null)
                        {
                            var ownerInfo = troopEntity.owner;
                            if (ownerInfo?.ID == LPlayer.I.GetPlayerId())
                            {
                                _resGatherProcess.SetActive(true);
                                _gathererType = GathererType.Self;
                                LGatherInfo.I.RequestGatherInfo(troopId, ack =>
                                {
                                    ShowMyGatherData(ack);
                                });
                            }
                            else if (ownerInfo != null && LPlayer.I.IsPlayerSelfUnion(ownerInfo.unionID))
                            {
                                _gathererType = GathererType.Ally;
                                _resGatherProcess.SetActive(false);
                                ShowAllyGatherData(npcGatherInfo);
                                GetHeadInfo();
                            }
                            else
                            {
                                _gathererType = GathererType.Enemy;
                                _resGatherProcess.SetActive(false);
                                ShowEnemyGatherData(npcGatherInfo);
                                GetHeadInfo();
                            }
                        }
                        else
                        {
                            _gathererType = GathererType.Enemy;
                            _resGatherProcess.SetActive(false);
                            ShowEnemyGatherData(npcGatherInfo);
                            GetHeadInfo();
                        }
                    }
                    else
                    {
                        _resObj.SetActive(true);
                        _gather.SetActive(false);

                        _resGatherProcess.SetActive(false);
                        _gathererType = GathererType.None;
                        ShowGatherData(npcGatherInfo);

                        //if (LGatherInfo.I.IsGatherCanRally(m_EntityId))
                        //{
                        //    //可以集结操作
                        //    WndMgr.Show<UINpcGatherRally>(new UIPopMovingBaseData()
                        //    {
                        //        EntityId = m_EntityId,
                        //    });
                        //}
                        //else
                        //{
                        //    //只能单人
                        //    WndMgr.Show<UINpcGather>(new UIPopMovingBaseData()
                        //    {
                        //        EntityId = m_EntityId,
                        //    });
                        //}
                    }
                }
            }
        }


        /// <summary>
        /// 关闭
        /// </summary>
        protected override void OnHidden()
        {
            base.OnHidden();
            UnregisterEvent(TEventType.EntityResourceUpdate);
            UnregisterEvent(TEventType.EntityResourceDelete);
            UnregisterEvent(TEventType.EntityTroopExitGather);
            UnregisterEvent(TEventType.EntityResourceTroopChange);
            //EventMgr.UnregisterEvent(TEventType.PlayerHeadInfoAck, this);
            FrameUpdateMgr.UnregisterPerSecondFunListUpdate(Name);
        }

        /// <summary>
        /// 召回
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        private void OnRecallClick(GameObject arg0, PointerEventData arg1)
        {
            if (npcGatherInfo == null)
                return;

            var troopID = npcGatherInfo.troopID;
            var entityInfo = LMapEntityManager.I.GetEntityInfo(troopID);
            if (entityInfo == null)
            {
                //D.Error?.Log("entityInfo is null troopID=" + troopID);
                return;
            }

            //if (entityInfo.type == MapUnitType.MapUnitUraniumConvoy)
            //{
            //    troopID = entityInfo.property.convoy.TroopDefenderID;
            //}

            if (troopID != -1)
            {
                MessageMgr.Send(new RecallMarchReq()
                {
                    entityID = troopID
                });
                Close();
            }
        }

        /// <summary>
        /// 攻击
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        private void OnAttackClick(GameObject arg0, PointerEventData arg1)
        {
            var btn2data = new MsgBoxBtnParam()
            {
                str = LocalizationMgr.Get("LC_MENU_cancel_cap"),
            };
            var btn1data = new MsgBoxBtnParam()
            {
                str = LocalizationMgr.Get("LC_MENU_confirm_cap"),
                func = (o) => { GoAttack(); },
            };
            if (LPlayer.I.ShieldOpen || LPlayer.I.GodShieldOpen)
            {
                if (LPlayer.I.ShieldOpen && !LPlayer.I.GodShieldOpen)
                {
                    UIMsgBox.Push(EMsgBoxType.two, LocalizationMgr.Get("LC_MENU_warning_cap"),
                        LocalizationMgr.Format("LC_MAP_broken_shield_desc", SCommon.FormatTimeString(LEnergyGauge.I.ShieldRemainTime / 1000), SCommon.FormatTimeString((int)CConstConfig.I(10134008).Val)),
                        btn1data,
                        btn2data, ButtonColorGroup.RedBlue);
                }
                else if (LPlayer.I.ShieldOpen && LPlayer.I.GodShieldOpen)
                {
                    UIMsgBox.Push(EMsgBoxType.two, LocalizationMgr.Get("LC_MENU_warning_cap"),
                        LocalizationMgr.Format("LC_Castle_Protection_Attack2", SCommon.FormatTimeString(Mathf.Max((float)LEnergyGauge.I.ShieldRemainTime / 1000, MetaConfig.ShieldGodCD)), SCommon.FormatTimeString((int)CConstConfig.I(10134008).Val)),
                        btn1data,
                        btn2data, ButtonColorGroup.RedBlue);
                }
                else if (LPlayer.I.GodShieldOpen)
                {
                    UIMsgBox.Push(EMsgBoxType.two, LocalizationMgr.Get("LC_MENU_warning_cap"),
                        LocalizationMgr.Format("LC_Castle_Protection_Attack1", SCommon.FormatTimeString(MetaConfig.ShieldGodCD), SCommon.FormatTimeString((int)CConstConfig.I(10134008).Val)),
                        btn1data,
                        btn2data, ButtonColorGroup.RedBlue);
                }
            }
            else
            {
                GoAttack();
            }
        }

        /// <summary>
        /// 攻击
        /// </summary>
        private void GoAttack()
        {
            Close();
            var attackId = m_EntityId;// GetAttackId();
            EventMgr.FireEvent(TEventType.UIMain_Update_Start_DispatchTroop, new MarchArgs()
            {
                targetId = attackId,
                act = MarchAct.MarchActAttack,
            });
            LMapEntityManager.I.SetLock(attackId, true);

        }

        private long GetAttackId()
        {
            var gatherType = LMapEntityManager.I.GetEntityInfo(m_EntityId)?.type ?? MapUnitType.MapUnitStub;
            if (gatherType == MapUnitType.MapUnitNpcGatherable)
            {
                return troopId;
            }
            else if (gatherType == MapUnitType.MapUnitRelay)
            {
                return troopId;
            }
            else
            {
                //D.Error?.Log($"The entity type {gatherType}not sup!");
                return 0;
            }
        }

        /// <summary>
        /// 销毁
        /// </summary>
        protected override void OnDestroyed()
        {
            base.OnDestroyed();
        }

        /// <summary>
        /// 每秒更新
        /// </summary>
        /// <param name="deltaTime"></param>
        private void UpdatePerSecond(float deltaTime)
        {
            RefreshTime();
            if (this._gathererType == GathererType.Self)
            {
                RefreshMyGather();
            }
            //switch (this._gathererType)
            //{
            //    case GathererType.None:
            //        break;
            //    case GathererType.Self:
            //        RefreshMyGather();
            //        break;
            //    case GathererType.Ally:
            //        break;
            //    case GathererType.Enemy:
            //        break;
            //    default:
            //        break;
            //}
        }

        /// <summary>
        /// 刷新时间
        /// </summary>
        private void RefreshTime()
        {
            if (_remainTime != null)
            {
                var module = LMapEntityManager.I.GetEntityModule(MapUnitType.MapUnitNpcGatherable) as LMapEntityNpcGather;
                if (module != null)
                {
                    var npcGatherInfo = module.GetNpcGatherInfo(m_EntityId);
                    if (npcGatherInfo != null && npcGatherInfo.deadline > 0)
                    {
                        var duration = Math.Max(0, (npcGatherInfo.deadline - GameTime.Time));
                        this._remainTime.text = UIHelper.GetFormatTime(duration);
                    }
                }

            }
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids"></param>
        private void OnEntityResourceDelete(object[] ids)
        {
            bool find = false;
            foreach (long id in ids)
            {
                if (m_EntityId == id)
                {
                    find = true;
                    break;
                }
            }

            if (!find)
            {
                return;
            }

            Close();
        }

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="ids"></param>
        private void OnEntityResourceUpdate(object[] ids)
        {
            bool find = false;
            foreach (long id in ids)
            {
                if (m_EntityId == id)
                {
                    find = true;
                    break;
                }
            }

            if (!find)
            {
                return;
            }

            var npcGatherInfo =
                (LMapEntityManager.I.GetEntityModule(MapUnitType.MapUnitNpcGatherable) as LMapEntityNpcGather)
                .GetNpcGatherInfo(m_EntityId);

            if (npcGatherInfo != null)
            {
                if (npcGatherInfo.troopID > 0)
                {
                    Close();
                }
                else
                {
                    RefreshTime();
                }
            }
        }

        /// <summary>
        /// 收藏
        /// </summary>
        /// <param name="go"></param>
        /// <param name="data"></param>
        private void OnFavoriteClick(GameObject go, PointerEventData data)
        {
            var entityInfo = LMapEntityManager.I.GetEntityInfo(m_EntityId);
            if (entityInfo != null)
            {
                var pos = Formula.WorldToSharePosition(entityInfo.CurrentPosition);
                var name = LGatherInfo.I.GetGatherName(m_EntityId);
                UIFavoritesTipsPanel.Show(pos.x, pos.z, name);
            }
        }

        private void OnShareClick(GameObject go, PointerEventData data)
        {
            //先检查有没有联盟，没有联盟跳到联盟界面
            if (!LPlayer.I.IsPlayerInUnion())//没有加入联盟
            {
                Close();
                //UIMain.I.TrySwitch(MainMenuConst.ALLIANCE, new UIChatData() { CurrentTabKey = UIMain.I.GetMainUICurrState(), CurrChatTab = ChatTabs.Alliance });
                //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.ALLIANCE,
                //    new UIChatData() { CurrentTabKey = GameData.I.MainData.CurrMenuType, CurrChatTab = ChatTabs.Alliance });

                //WndMgr.Show<UIChat>("UIChat", new UIChatArgs() { openTab = ChatTabs.Alliance });
                //UIMain.I?.TrySwitch(MainMenuConst.ALLIANCE);
            }
            else
            {
                SendShareCoordinateToUnionChannel();
            }
        }

        /// <summary>
        /// 发送坐标分享消息到联盟频道
        /// </summary>
        private void SendShareCoordinateToUnionChannel()
        {
            var level = LGatherInfo.I.GetGatherLevel(m_EntityId);
            var name = LGatherInfo.I.GetGatherName(m_EntityId, true);
            var entityInfo = LMapEntityManager.I.GetEntityInfo(m_EntityId);
            if (entityInfo != null)
            {
                var pos = Formula.WorldToSharePosition(entityInfo.CurrentPosition);
                var x = pos.x;
                var z = pos.z;

                ////分享{0}级{1}
                var unitName = string.Format(LocalizationMgr.GetUIString("Share_chatdes_1"), level, name);
                PopupManager.I.ShowPanel<UIShare>(new UIShareData()
                {
                    chatContent = UIChatHelper.I.GetShareCoordinateToAllianceChannel
                    (
                        name, level,
                        ShareDataTypeEnum.Gather,
                        unitName,
                        x,
                        z
                    )
                });

            }
        }

        //--- 返回
        public void OnGatherClick(GameObject arg0, PointerEventData arg)
        {
            //D.Error?.Log("OnGatherClick");
            Close();

            EventMgr.FireEvent(TEventType.UIMain_Update_Start_DispatchTroop, new MarchArgs()
            {
                targetId = this.m_EntityId,
                act = MarchAct.MarchActGather,
            });
            //LMapEntityManager.I.SetLock(m_EntityId, true);


            //var npcGatherCfg =
            //    (LMapEntityManager.I.GetEntityModule(MapUnitType.MapUnitNpcGatherable) as LMapEntityNpcGather)
            //    .GetNpcGatherCfg(m_EntityId);
            //if (!ConfigOperateCompare.GetConditionResult(npcGatherCfg.Requirement, out var outInfo))
            //{
            //    foreach (var info in outInfo)
            //    {
            //        if (!info.result)
            //        {
            //            FloatTips.I.FloatMsg(LocalizationMgr.Format(npcGatherCfg.LcLocked.Txt, info.condition.Val));
            //            return;
            //        }
            //    }
            //}

            //Close();
            //UIMain.I.StartDispatchTroop(new MarchArgs
            //{
            //    targetId = m_EntityId,
            //    act = MarchAct.MarchActGather,
            //});

            //LMapEntityManager.I.SetLock(m_EntityId, true);
        }
    }
}
//MODIFIED BY UIBASE2POPUP
//MODIFIED BY UIBASE2POPUP
//MODIFIED BY UIBASE2POPUP
//MODIFIED BY UIBASE2POPUP
