﻿ 
using Common;
using DeepUI;
using Game.Sprite.Fight;
using System;
using System.Collections.Generic;
using UI;

namespace Game.Data
{
    public partial class GameData
    {
        /// <summary>
        /// 初始化解锁相关数据
        /// </summary>
        private UnLockGameData _unlockData = new UnLockGameData();

        /// <summary>
        /// 初始化解锁相关数据
        /// </summary>
        public UnLockGameData UnlockData
        {
            get
            {
                if (I._unlockData == null)
                    _unlockData = new UnLockGameData();
                
                return I._unlockData;
            }
        }
    }

    /// <summary>
    /// 游戏解锁相关数据
    /// </summary>
    public class UnLockGameData : IDisposable
    {


        /// <summary>
        /// 最大英雄等级
        /// </summary>
        public int MaxHeroLevel
        {
            get
            {
                if (GameData.I.SkillData.MTechs.TryGetValue(1, out var data))
                {
                    return data.HeroLvLimit;
                }
                else
                    return 1;

            }
        }

        public int MaxTechHeroMaxLevel
        {
            get
            {
                if (GameData.I.SkillData.MTechs.TryGetValue(1, out var data))
                {
                    return data.MaxConfig.HeroLvLimit;
                }
                else
                    return MaxHeroLevel;

            }
        }



        /// <summary>
        /// 玩家通过的最大关卡数
        /// </summary>
        private int _maxLevel = -1;

        /// <summary>
        /// 存储的最大关卡
        /// </summary>
        private int _maxData;

        /// <summary>
        /// 需要显示关卡提示
        /// </summary>
        private bool _needShowLevel;

        /// <summary>
        /// 弓箭兵是否开启
        /// </summary>
        private bool _bowSoldier;

        /// <summary>
        /// 火法是否开启
        /// </summary>
        private bool _fireSoldier;

        /// <summary>
        /// 冰法是否开启
        /// </summary>
        private bool _iceSoldier;

        /// <summary>
        /// 毒塔是否开启
        /// </summary>
        private bool _poisonSoldier;

        /// <summary>
        /// 塔位开启信息
        /// </summary>
        private List<int> _towerData;

        /// <summary>
        /// 训练等级
        /// </summary>
        private int _trainLevel;

        /// <summary>
        /// 训练价格
        /// </summary>
        private int _trainMoney;

        /// <summary>
        /// 合成区解锁数量
        /// </summary>
        private int _synthesisPosNum;

        /// <summary>
        /// 解锁显示列表
        /// </summary>
        //public List<UnLockType> _unLocks = new List<UnLockType>();

        /// <summary>
        /// 是否重刷数据
        /// </summary>
        private bool _refreshData = false;

        /// <summary>
        /// 新解锁的塔位地块信息
        /// </summary>
        //private MapLandInfo _towerUnLock;

        /// <summary>
        /// 开启自动合成
        /// </summary>
        private bool _openMerge;

        public bool RefreshData { get => _refreshData; set => _refreshData = value; }

        /// <summary>
        /// 玩家通过的最大关卡数
        /// </summary>
        public int MaxLevel
        {
            get => _maxLevel;
            set
            {
                if (RefreshData)
                {
                    _refreshData = false;
                    _maxLevel = value;
                    //SetUnLockData(false);
                }
                else
                {
                    if (_maxLevel != -1)
                    {
                        if (_maxLevel < value)
                        {
                            _maxLevel = value;
                            //SetUnLockData(true);
                        }
                        else
                        {
                            //_unLocks.Clear();
                            Debug.LogFormat("解锁面板Clear");
                            //ShowUnLockPanel();
                        }
                    }
                    else if (_maxLevel == -1)
                    {
                        _maxLevel = value;
                        //SetUnLockData(false);
                    }
                }
                EventMgr.FireEvent(TEventType.PlayerUnlockMaxHeroLevelChange);
            }
        }



        List<string> showUIs = new List<string>();
        string[] lockToUIs = new string[]
        {
            typeof(UIMain2).Name,
            //typeof(UIMainChatMsg).Name,
            typeof(FloatTips).Name,
            typeof(UIMapWidgets).Name,
            typeof(HurtFlyText).Name,
            typeof(GoldFly).Name,
            //typeof(TreasureChestFly).Name,
            //typeof(UnLockPanel).Name,
            //typeof(FlyTowerPanel).Name,
  #if UNITY_EDITOR || INTERNAL_ENTRANCE || DEFAULT_ENV_BETA
            typeof(UIGmWindow).Name,
#endif
            typeof(UIFloatTips).Name,
            //typeof(UnLockTipsPanel).Name,
            //typeof(UILevelTips).Name,
            typeof(LevelUpPanel).Name,
            typeof(UICommonFly).Name,
            typeof(UIGuid).Name,
            //typeof(UIEpisodeGuid).Name,
        };


//        public void ShowUnlcok()
//        {
//            if (_unLocks != null && _unLocks.Count > 0)
//            {
//#if UNITY_EDITOR
//                Debug.LogWarningFormat($"_unLocks count >0 {_unLocks[0]}");
//#endif

//                var lockPanel = PopupManager.I.FindPopupFromPool<UnLockPanel>();
//                if (lockPanel != null && lockPanel.IsShow)
//                {
//                    return;
//                }

//                ShowUnLockPanel();
//            }
//        }

        /// <summary>
        /// 显示解锁弹窗
        /// </summary>
        //public void ShowUnLockPanel(bool isClosePanel = true)
        //{

        //    if (CheckUnloakDatas())
        //    {

        //        WndMgr.GetShowUINames(ref showUIs, lockToUIs);
        //        if (showUIs.Count > 0)//  && _unLocks[0] != UnLockType.TrainLevel && _unLocks[0] != UnLockType.Compose
        //        {
        //            Debug.LogWarningFormat($"解锁界面被拦截了:{showUIs[0]} ==>{_unLocks[0]}");

        //            return;
        //        }

        //        //取消大地图操作
        //        MapManager.I.CanDragOnMap(false);


        //        if (isClosePanel)
        //        {
        //            EventMgr.FireEvent(TEventType.ShowUnLockPanel, (int)_unLocks[0]);

        //            _unLocks.Remove(_unLocks[0]);
        //        }
        //        else
        //        {
        //            //开启大地图操作
        //            MapManager.I.CanDragOnMap(true);

        //            FightManager.I.StartLevelFight();

        //            if (_needShowLevel)
        //            {
        //                EventMgr.FireEvent(TEventType.ShowLevelTips);

        //                _needShowLevel = false;
        //            }
        //        }

        //    }
        //    else
        //    {
        //        if (isClosePanel)
        //            EventMgr.FireEvent(TEventType.ShowUnLockPanel, 0);

        //        //开启大地图操作
        //        MapManager.I.CanDragOnMap(true);

        //        FightManager.I.StartLevelFight();

        //        if (_needShowLevel)
        //        {
        //            EventMgr.FireEvent(TEventType.ShowLevelTips);

        //            _needShowLevel = false;
        //        }
        //    }
        //}

        //private bool CheckUnloakDatas()
        //{
        //    if (_unLocks != null && _unLocks.Count > 0)
        //    {
        //        for (int i = _unLocks.Count - 1; i >= 0; i--)
        //        {
        //            var curUnlockType = _unLocks[i];
        //            //switch (curUnlockType)
        //            //{
        //            //    case UnLockType.city:
        //            //        if (GameData.I.LevelData.CurrGameLevel > MetaConfig.OpenCity)
        //            //        {
        //            //            _unLocks.Remove(UnLockType.city);
        //            //        }
        //            //        break;
        //            //    case UnLockType.hero:
        //            //        if (GameData.I.LevelData.CurrGameLevel > MetaConfig.Hero_System_OpenLevel)
        //            //        {
        //            //            _unLocks.Remove(UnLockType.hero);
        //            //        }
        //            //        break;
        //            //    case UnLockType.alliance:
        //            //        if (GameData.I.LevelData.CurrGameLevel > MetaConfig.UnlockUnionBtn)
        //            //        {
        //            //            _unLocks.Remove(UnLockType.alliance);
        //            //        }
        //            //        break;
        //            //    case UnLockType.world:
        //            //        if (GameData.I.LevelData.CurrGameLevel > MetaConfig.UnlockWorldBtn)
        //            //        {
        //            //            _unLocks.Remove(UnLockType.world);
        //            //        }
        //            //        break;
        //            //    case UnLockType.commpose:
        //            //        if (GameData.I.LevelData.CurrGameLevel > MetaConfig.OpenComPoseLevel)
        //            //        {
        //            //            _unLocks.Remove(UnLockType.commpose);
        //            //        }
        //            //        break;
        //            //    case UnLockType.chat:
        //            //        if (GameData.I.LevelData.CurrGameLevel > MetaConfig.UnlockChatBtn)
        //            //        {
        //            //            _unLocks.Remove(UnLockType.chat);
        //            //        }
        //            //        break;
        //            //    case UnLockType.chapterQuest:
        //            //        if (GameData.I.LevelData.CurrGameLevel > MetaConfig.ChapterQuestOpenLV)
        //            //        {
        //            //            _unLocks.Remove(UnLockType.chapterQuest);
        //            //        }
        //            //        break;
        //            //    case UnLockType.skinLevel:
        //            //        if (GameData.I.LevelData.CurrGameLevel > MetaConfig.OpenFreeScene)
        //            //        {
        //            //            _unLocks.Remove(UnLockType.skinLevel);
        //            //        }
        //            //        break;
        //            //    default:
        //            //        break;
        //            //}
        //        }

        //        return _unLocks.Count > 0;
        //    }
        //    else
        //    {
        //        return false;
        //    }
        //}

        /// <summary>
        /// 飞塔
        /// </summary>
        public void FlyTower()
        {
            //EventMgr.FireEvent(TEventType.FlyTower,_towerUnLock);
        }

        public void UpdateTower()
        {
            //if(_towerData != null)
            //{
            //    GameData.I.MapData.MapTowerInfo.SetTowerUnlockData(_towerData,true);
            //}
        }

        public void FlyCompose()
        {
            //EventMgr.FireEvent(TEventType.FlyCompose, _synthesisPosNum, _towerUnLock);
        }

        public void UpdateCompose()
        {
            //if (_synthesisPosNum>0)
            //{
            //    GameData.I.MapData.MapComposeInfo.SetComposeUnlockData(_synthesisPosNum);
            //}
        }

        public void Dispose()
        {
            _maxLevel = 0;
            _bowSoldier = false;
            _fireSoldier = false;
            _iceSoldier = false;
            _poisonSoldier = false;
            _towerData = null;
            _trainLevel = 0;
            _synthesisPosNum = 0;
            //_unLocks.Clear();
        }
    }
}
