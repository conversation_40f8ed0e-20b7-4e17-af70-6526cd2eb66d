﻿using DeepUI;
using Logic;
using Public;
using Sirenix.OdinInspector;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Game.Data;
using UnityEngine;
using UnityEngine.UI;
using KS;
using Game.Config;
using Config;
using cspb;
using K3;
using Common;
using TFW;
using TFW.Localization;
using TFW.UI;

namespace UI
{
    /// <summary>
    /// 新首充三日购
    /// </summary>
    public class FirstChargeNewUI : MonoBehaviour
    {
        const string Tabs = "Tabs";
        const string DynamicElements = "Dynamic Elements";
        const string DynamicColor = "Dynamic Color";

        //[SerializeField]
        //RewardListUI _rewardListUI;

        [SerializeField]
        EventTriggerListener _changeButton;

        [SerializeField]
        EventTriggerListener _purchaseButton;

        //[SerializeField]
        //EventTriggerListener _testUseHeroButton;

        [SerializeField]
        EventTriggerListener _closeButton;

        //[BoxGroup(Tabs)]
        //[SerializeField]
        //FirstChargeNewTabUI[] _tabUIs;

        [BoxGroup(DynamicElements)]
        [SerializeField]
        RawImage _bg;

        [BoxGroup(DynamicElements)]
        [SerializeField]
        Text _exhibitName;

        [BoxGroup(DynamicElements)]
        [SerializeField]
        GameObject _exhibitNameRoot;

        [BoxGroup(DynamicElements)]
        [SerializeField]
        GameObject _exhibitHeroQualityRoot;

        [BoxGroup(DynamicElements)]
        [SerializeField]
        TFWImage ssrImage;

        [BoxGroup(DynamicElements)]
        [SerializeField]
        RewardItemUI[] RewardItems;//3+2+2


        [BoxGroup(DynamicElements)]
        [SerializeField]
        GameObject day1finesh;

        [BoxGroup(DynamicElements)]
        [SerializeField]
        GameObject day2finesh; 

        [BoxGroup(DynamicElements)]
        [SerializeField]
        GameObject day3finesh;         //[SerializeField]

        //UIHeroSkillUpItem _day1Buff;

        //[SerializeField]
        //Text _day2Buff;

        //[SerializeField]
        //Text _day3Buff;

        [Tooltip("领奖提示")]
        [SerializeField]
        Text _claimTip;

        [BoxGroup(DynamicElements)]
        [SerializeField]
        Transform _exhibitSpineRoot;

        //[BoxGroup(DynamicElements)]
        //[SerializeField]
        //RawImage _exhibitImg;

        [BoxGroup(DynamicElements)]
        [SerializeField]
        string _exhibitImgFolder = "FirstCharge3day";

        [BoxGroup(DynamicElements)]
        [SerializeField]
        Text _exhibitTitle;
        [SerializeField]
        Text _exhibitTDesc;
        [BoxGroup(DynamicColor)]
        [SerializeField]
        Text _title;

        [BoxGroup(DynamicColor)]
        [SerializeField]
        Text _description;

        [BoxGroup(DynamicColor)]
        [SerializeField]
        Image _bottomBg;

        [BoxGroup(DynamicColor)]
        [SerializeField]
        Text _rewardTitle;

        [SerializeField]
        Text _discordTxt;
        [SerializeField]
        GameObject effectObj;
        readonly Context _context = new Context();
        
        [SerializeField]
        public GameObject btnPlay;

        private BtnDescWidget _btnCostWidget;

        private bool _purchasedUnlockGift => FirstChargeNewMgr.I.buyOk;

        float _updateIntervalSeconds = 0.5f;
        float _time = 0;
       
        void OnDataChanged()
        {
            this._time = 0;

            if (this._context.isValid)
            {
                this._context.Init(this._context.currentData.day);
                this.Refresh();
            }
        }

        void Refresh()
        {
            if (this.isActiveAndEnabled && this._context.isValid)
            {
                this.SetDetail();

                //if (this._tabUIs.Length == this._context.days)
                //{
                //    var dayCfgs = FirstChargeNewMgr.I.GetDayConfigs();
                //    for (int i = 0; i < this._context.days; i++)
                //    {
                //        var tabUI = this._tabUIs[i];
                //        var dayCfg = dayCfgs[i + 1];

                //        tabUI.SetData(dayCfg);
                //    }

                //    this.SetDetail();
                //}
                //else
                //{
                //    D.Warning?.Log("天数不合法");
                //}
            }
        }

        void SetDetail()
        {
            if (this.isActiveAndEnabled && this._context.isValid && _context.currentConfig != null)
            {
                var currentConfig = this._context.currentConfig;
                if (_discordTxt != null)
                    _discordTxt.transform.parent.gameObject.SetActive(true);
                _discordTxt.text =$"{Cfg.C.CD2Gift.I(  this._context.Day1Config.GiftId).Percent}%";// LocalizationMgr.Get("Gift_FirstPurchase_Soldier_desc6");
                this.SetUIColor(this._context.Day1Config);
                this.SetSpineOrImage(this._context.Day1Config);
                //if(_rewardListUI)
                //{
                //    this._rewardListUI.SetDisplayEffect(true);
                //    this._rewardListUI.SetData(currentConfig.overrodeReward);
                //}

                for (int i = 0; i < 3; i++)
                {
                    //day1
                    if (this._context.Day1Config.overrodeReward.Count > i)
                    {
                        RewardItems[i].gameObject.SetActive(true);
                        RewardItems[i].SetData(this._context.Day1Config.overrodeReward[i]);
                    }
                    else
                    {
                        RewardItems[i].gameObject.SetActive(false);
                    }
                    
                }

                for (int i = 3; i < 5; i++)
                {
                    int j = i - 3;
                    if (this._context.Day2Config.overrodeReward.Count > j)
                    {
                        RewardItems[i].gameObject.SetActive(true);
                        RewardItems[i].SetData(this._context.Day2Config.overrodeReward[j]);
                    }
                    else
                    {
                        RewardItems[i].gameObject.SetActive(false);
                    }
                }

                for (int i = 5; i < 7; i++)
                {
                    int j = i - 5;
                    if (this._context.Day3Config.overrodeReward.Count > j)
                    {
                        RewardItems[i].gameObject.SetActive(true);
                        RewardItems[i].SetData(this._context.Day3Config.overrodeReward[j]);
                    }
                    else
                    {
                        RewardItems[i].gameObject.SetActive(false);
                    }
                }

                    
                //购买解锁礼包后不能换英雄
                this._changeButton.gameObject.SetActive(!this._purchasedUnlockGift);
                if (this._changeButton)
                    this._changeButton.gameObject.SetActive(false);
                // UITools.SetRawImage(_bg, currentConfig.Bannerid, this._exhibitImgFolder);
                if(effectObj)
                    effectObj.SetActive(true);
                var itemId = this._context.Day1Config.ItemID;
                var itemCfg = Cfg.C.CItem.I(itemId);
                if (itemCfg != null)
                {
                    this._exhibitName.text = itemCfg.GetDisplayName();
                    this._exhibitTitle.text = itemCfg.GetDisplayName();

                    var heroCfg = this.GetHeroConfig(itemId);
                    if (heroCfg != null)
                    {
                        this._exhibitHeroQualityRoot.SetActive(true); //英雄
                        //this._exhibitNameRoot.SetActive(true);
                        this._exhibitTitle.gameObject.SetActive(false);


                        UITools.SetImageBySpriteName(ssrImage, UITools.GetSSRQuality(heroCfg.HeroType));

                        //var heroData = Game.Data.HeroGameData.I.GetHeroByCfgId(heroCfg.Id);
                        //var skillData = heroData?.HeroSkillDatas?.FirstOrDefault().Value;
                        //if (heroData != null && skillData != null)
                        //{
                        //    if(_day1Buff)
                        //        this._day1Buff.InitData(skillData, heroData);
                        //}
                        //else
                        //{
                        //    D.Warning?.Log($"未找到英雄数据，heroId {heroCfg.Id}");
                        //}
                    }
                    else
                    {
                        this._exhibitHeroQualityRoot.SetActive(false); //非英雄
                        //this._exhibitNameRoot.SetActive(false);
                        this._exhibitTitle.gameObject.SetActive(true);

                        //if (currentConfig.StageID == 2)
                        //{
                        //    this._exhibitTDesc.gameObject.SetActive(true);
                        //    this._day2Buff.text = LocalizationMgr.Get(currentConfig.Labereffect);
                        //}
                        //else if (currentConfig.StageID == 3)
                        //{
                        //    this._exhibitTDesc.gameObject.SetActive(false);
                        //    this._day3Buff.text = LocalizationMgr.Get(currentConfig.Labereffect);
                        //}
                    }
                }

                //已购买
                if (this._purchasedUnlockGift)
                {
                    var curDayInfo = this._context.currentData;
                    //this._btnCostWidget?.SetBtnImgNoInteractable(curDayInfo.claimOk);

                    //按钮显示可领奖
                    if (this._context.canClaimReward)
                    {
                        this._btnCostWidget?.SetData(curDayInfo.claimOk ? LocalizationMgr.Get("Gift_monthcard_btn_02") : LocalizationMgr.Get("chronicle_txt_10"));
                        this._btnCostWidget?.SetButtonDescColor(curDayInfo.claimOk, Color.black);
                    }
                    //按钮显示充值
                    else
                    {
                        this._btnCostWidget?.SetData(LocalizationMgr.Get("Gift_FirstPurchase_Recharge"));
                        this._btnCostWidget?.SetButtonDescColor(false, Color.black);
                    }


                    day1finesh.SetActive(false);
                    day2finesh.SetActive(false);
                    day3finesh.SetActive(false);

                    if (curDayInfo.day > 1)
                    {
                        day1finesh.SetActive(true);

                        if (curDayInfo.day > 2)
                        {
                            day2finesh.SetActive(true);

                            day3finesh.SetActive(curDayInfo.claimOk);
                        }
                        else
                        {
                            day2finesh.SetActive(curDayInfo.claimOk);
                        }
                    }
                    else
                    {
                        day1finesh.SetActive(curDayInfo.claimOk);
                    }

                }
                //未购买
                else
                {
                    var giftItemData = GameData.I.GiftData.GetPeriodGiftData(this._context.unlockGiftId);
                    if(giftItemData == null)
                    {
                        giftItemData = new GiftItemData(this._context.unlockGiftId, 0, 0, null);
                    }
                    
                    this._btnCostWidget.SetData(giftItemData.GetPrice(true));

                    day1finesh.SetActive(false);
                    day2finesh.SetActive(false);
                    day3finesh.SetActive(false);
                }

                //bool canUseHero= GamePlayerPrefs.GetBoolByRole("firsttesthero");
                //var mHero= HeroGameData.I.GetMyHeroByCfgId(447);
                //_testUseHeroButton.gameObject.SetActive(false);//!canUseHero && mHero ==null

                var shouldShowClaimTip = this._context.currentData.day != 1 && !this._context.canClaimReward;
                this._claimTip.gameObject.SetActive(shouldShowClaimTip);
            }
        }

        //void SetTabIsOn(int day)
        //{
        //    var curDay = 1;
        //    foreach (var tabUI in this._tabUIs)
        //    {
        //        var isOn = day == curDay;
        //        tabUI.SetIsOn(isOn);

        //        curDay++;
        //    }
        //}

        void SetUIColor(Cfg.G.CFirstRechargeNew cfg)
        {
            //this._title.color = Game.Utils.ColorUtils.ColorFromRGBAString(cfg.Titlecolor2);
            //this._description.color = Game.Utils.ColorUtils.ColorFromRGBAString(cfg.Titledescolor);

            //var originColor = this._bottomBg.color;
            //var newColor = Game.Utils.ColorUtils.ColorFromRGBAString(cfg.Bottomframebg);
            //this._bottomBg.color = new Color(newColor.r, newColor.g, newColor.b, originColor.a);

            //this._rewardTitle.color = Game.Utils.ColorUtils.ColorFromRGBAString(cfg.RewardTxtColor);
        }

        async void SetSpineOrImage(Cfg.G.CFirstRechargeNew cfg)
        {
            if (cfg == null)
                return;
            var isSpine = cfg.AssetType == 2;
            //this._exhibitSpineRoot.gameObject.SetActive(isSpine);
            //this._exhibitImg.gameObject.SetActive(!isSpine);

            
            if (isSpine)
            {
                var spinePath = cfg.SpineiD;
                if (ResourceMgr.Exists(spinePath))
                {
                    var spineObj = await ResourceMgr.LoadInstanceAsync(spinePath);
                    if (spineObj != null)
                    {
                        UITools.DestroyChildren(this._exhibitSpineRoot);
                        spineObj.transform.SetParent(this._exhibitSpineRoot);

                        spineObj.transform.localPosition = new Vector3(
                                            float.Parse(cfg.Location[0]),
                                            float.Parse(cfg.Location[1]),
                                            float.Parse(cfg.Location[2]));
                        spineObj.transform.localScale = new Vector3(
                                        float.Parse(cfg.Scale[0]),
                                        float.Parse(cfg.Scale[1]),
                                        float.Parse(cfg.Scale[2]));
                    }
                } 
            }
            //else
            //{
            //    UITools.SetRawImage(this._exhibitImg, cfg.SpineiD, this._exhibitImgFolder);
            //}
        }

    

        private Cfg.G.CD2Hero GetHeroConfig(int itemId)
        {
            try
            {
                var heroId = Cfg.C.CItem.I(itemId).CategoryParam.Effect[0].Id;

                var heroCfg = Cfg.C.CD2Hero.I(heroId);
                return heroCfg;
            }
            catch (System.Exception ex)
            {
                D.Warning?.Log($"itemId {itemId} 未能关联 Hero配置 :{ex}");
                return null;
            }
        }

        #region Button Callback
        private void OnCloseClick(GameObject arg1, UnityEngine.EventSystems.PointerEventData arg2)
        {
            PopupManager.I.ClosePopup<Layer>();
            //AutoOpenMgr.I.AutoOpenPanel();
        }

        private void OnChangeClick(GameObject arg1, UnityEngine.EventSystems.PointerEventData arg2)
        {
            PopupManager.I.ShowPanel<FirstChargeNewSelectHeroUI.Layer>();
        }

        private void OnPurchaseClick(GameObject arg1, UnityEngine.EventSystems.PointerEventData arg2)
        {
            if (this.isActiveAndEnabled && this._context.isValid && FirstChargeNewMgr.I.isValid)
            {
                //已购买
                if (this._purchasedUnlockGift)
                {
                    var curDayInfo = this._context.currentData;

                    //已领奖
                    if (curDayInfo.claimOk)
                    {
                        FloatTips.I.FloatMsg(LocalizationMgr.Get("Gift_monthcard_btn_02"));
                    }
                    else
                    {
                        //按钮显示可领奖
                        if (this._context.canClaimReward)
                        {
                            FirstChargeNewMgr.I.RequestClaimReward(this._context.currentConfig.StageID);
                        }
                        //按钮显示充值
                        else
                        {
                            PopupManager.I.ClosePopup<Layer>();
                            // ShopMgr.I.OpenShopPanel();
                            
                            GameData.I.ActivityData.ReqActvList(true, false);
                            // 特殊处理，过滤超出时效的kvk card礼包
                            var list = ShopTabConfig.I.specialDealEntranceTabs.Where(tab =>
                            {
                                //if (tab.pageType == ShopPageType.KVKCard)
                                //{
                                //    if (!LPlayer.I.UseNewKVK)
                                //    {
                                //        return false;
                                //    }
                                //    var info = GameData.I.ActivityData.GetEndStageNewKvkInfo3();
                                //    var endTime = info.MatchET;
                                //    if (!KVKCrystalCardMgr.I.GetHaveCard())
                                //    {
                                //        endTime -= 1000 * 60 * 60 * 24;
                                //    }
                                //    if (info == null || info.MatchET == 0 || GameTime.Time >= endTime)
                                //    {
                                //        return false;
                                //    }
                                //}
                                //else if (tab.pageType == ShopPageType.KVKPrivilegeCard)
                                //{
                                //    if (!LPlayer.I.UseNewKVK)
                                //    {
                                //        return false;
                                //    }
                                //    var info = GameData.I.ActivityData.GetEndStageNewKvkInfo3();
                                //    var endTime = info.MatchET;
                                //    if (!KVKPrivilegeCardMgr.I.GetHaveCard())
                                //    {
                                //        endTime -= 1000 * 60 * 60 * 24;
                                //    }
                                //    if (info == null || info.MatchET == 0 || GameTime.Time >= endTime)
                                //    {
                                //        return false;
                                //    }
                                //}
                                return true;
                            }).ToArray();
                            ShopMgr.I.OpenShopPanel(list.FirstOrDefault(), list);
                        }
                    }
                }
                //未购买
                else
                {
                    var giftItemData = GameData.I.GiftData.GetPeriodGiftData(this._context.unlockGiftId);
                   
                    if (giftItemData != null)
                    {
                        var id = giftItemData.CfgId;
                        var price = giftItemData.GetProductId().ToString();
                        LPay.I.BuyItem(id.ToString(), price, (b) => {
                            ShopMgr.I.OnBuyItemAction(b);
                            //AutoOpenMgr.I.AddAutoOpenPanel(b);
                        });
                    }
                }
            }
        }

        void OnTabSelect(Cfg.G.CFirstRechargeNew cfg)
        {
            if (this.isActiveAndEnabled && this._context.isValid)
            {
                this._context.Init(cfg.StageID);
                this.SetDetail();
            }
        }
        #endregion

        private void Awake()
        {
            this._btnCostWidget = new BtnDescWidget(this._purchaseButton.gameObject);
        }

        private void OnEnable()
        {
            var day = 1;
            for (int i = 1; i <= 3; i++)
            {
                if (FirstChargeNewMgr.I.ShouldShowRedDot(i) 
                    || !FirstChargeNewMgr.I.GetDayInfo(i).claimOk)
                {
                    day = i;
                    break;
                }
            }

            this._context.Init(day);
            //this.SetTabIsOn(day);
            this.Refresh();
        }

        private void Start()
        {
            this._purchaseButton.AddListener(EventTriggerType.Click, this.OnPurchaseClick);
            this._closeButton.AddListener(EventTriggerType.Click, this.OnCloseClick);
            this._changeButton.AddListener(EventTriggerType.Click, this.OnChangeClick);
            
            //UIHelper.RemoveListener(EventTriggerType.Click, btnPlay);
            //UIHelper.AddListener(EventTriggerType.Click, btnPlay, (arg0, data) =>
            //{
            //    CommonVideoCtrl.I.Play(() =>
            //    {
            //        UnityEngine.Debug.Log($"LogDebug - 视频播放完成。");
            //    });
            //});

            //UIBase.AddRemoveListener(EventTriggerType.Click, _testUseHeroButton.gameObject, (x, y) =>
            //{
            //    GamePlayerPrefs.SetBoolByRole("firsttesthero",true);

            //    //试用英雄
            //    PopupManager.I.ClosePopup<FirstChargeNewUI.Layer>();
            //    PopupManager.I.ShowLayer<K3.UIMerge>();

            //    foreach (var item in CSPlayer.I.SpecialBoxs)
            //    {
            //        if (item.CityType == MainCityItem.CityType.Police)
            //        {
            //            item.TestHero(true);
                      
            //            NTimer.CountDown(60f, () =>
            //            {
            //                item.TestHero(false); 
            //            });
            //        }
            //    }

            //});
            
            FirstChargeNewMgr.I.onDataChanged += this.OnDataChanged;

            //foreach (var tabUI in this._tabUIs)
            //{
            //    if (tabUI != null)
            //    {
            //        tabUI.onSelect.AddListener(this.OnTabSelect);
            //    }
            //}

            this.Refresh();
        }

        private void Update()
        {
            this._time += Time.deltaTime;
            if (this._time > this._updateIntervalSeconds)
            {
                if (this._context.isValid && this._claimTip.isActiveAndEnabled)
                {
                    this._claimTip.text = this._context.GetClaimTip();
                    //this.text 

                    if (this._context.currentData.claimTs - GameTime.Time < 0)
                    {
                        this.OnDataChanged();
                    }
                }
            }
        }

      

        private void OnDestroy()
        {
            this._purchaseButton.RemoveListener(EventTriggerType.Click, this.OnPurchaseClick);
            this._closeButton.RemoveListener(EventTriggerType.Click, this.OnCloseClick);
            this._changeButton.RemoveListener(EventTriggerType.Click, this.OnChangeClick);
            FirstChargeNewMgr.I.onDataChanged -= this.OnDataChanged;

            //foreach (var tabUI in this._tabUIs)
            //{
            //    if (tabUI != null)
            //    {
            //        tabUI.onSelect.RemoveListener(this.OnTabSelect);
            //    }
            //}
        }

        class Context
        {
            public bool isValid => 
                this.currentConfig != null 
                && currentData != null
                && this._dayConfigs != null;

            public bool canClaimReward
            {
                get
                {
                    var time = this.currentData.claimTs - GameTime.Time;
                    var ret = time > 0 && FirstChargeNewMgr.I.buyOtherCount < currentConfig?.ConditionPay;
                    return !ret;
                }
            }

            public Cfg.G.CFirstRechargeNew currentConfig { get; private set; }

            public Cfg.G.CFirstRechargeNew Day1Config { get; private set; }
            public Cfg.G.CFirstRechargeNew Day2Config { get; private set; }
            public Cfg.G.CFirstRechargeNew Day3Config { get; private set; }

            public cspb.FirstRechargeDayInfo currentData { get; private set; }

            public int unlockGiftId { get; private set; }

            public int days { get; private set; }

            IReadOnlyDictionary<int, Cfg.G.CFirstRechargeNew> _dayConfigs;

            public void Init(int day)
            {
                if (day > 0 && day <= FirstChargeNewMgr.I.baseData.dayInfo.Count)
                {
                    this._dayConfigs = FirstChargeNewMgr.I.GetDayConfigs();

                    var firstDayCfg = this.GetDayConfig(1);
                    if (firstDayCfg != null)
                    {
                        this.unlockGiftId = firstDayCfg.GiftId;
                    }

                    this.days = FirstChargeNewMgr.I.baseData.dayInfo.Count;
                    this.currentConfig = this.GetDayConfig(day);
                    this.currentData = FirstChargeNewMgr.I.GetDayInfo(day);

                    this.Day1Config = this.GetDayConfig(1);
                    this.Day2Config = this.GetDayConfig(2);
                    this.Day3Config = this.GetDayConfig(3);
                }
                else
                {
                    this.currentConfig = null;
                    this.currentData = null;
                    this._dayConfigs = null;

                    D.Warning?.Log($"天数参数 {day} 错误");
                }
            }

            public string GetClaimTip()
            {
                if (this.isValid)
                {
                    var time = currentData.claimTs - GameTime.Time;
                    var ret = string.Format(LocalizationMgr.Get("Gift_FirstPurchase_Ads_1"), UIHelper.GetFormatTime(time), this.currentConfig.ConditionPay - FirstChargeNewMgr.I.buyOtherCount);
                    return ret;
                }
                else
                {
                    return string.Empty;
                }
            }

            private Cfg.G.CFirstRechargeNew GetDayConfig(int day)
            {
                if (this._dayConfigs.TryGetValue(day, out var ret))
                {
                    return ret;
                }
                else
                {
                    D.Warning?.Log("未找到第{day}天的数据配置");
                    return null;
                }
            }
        }

        [Popup("MainCity/UIFirstCharge3day_4", false, false)]
        public class Layer : BasePopup
        {

            protected internal override bool PopBackEnabled => true;
            /// <summary>
            /// 根据新旧逻辑打开不同的 UI
            /// </summary>
            public static void OpenAdaptively()
            {
                AutoOpenMgr.firstOpenRecharge = false;
                D.Warning?.Log($"OpenAdaptively");
                // 确认当前没有首充弹窗再弹
                DeepUI.PopupManager.I.ShowPanel<FirstChargeNewUI.Layer>();
            }

            protected override void OnInit()
            {
            }
        }
    }
}