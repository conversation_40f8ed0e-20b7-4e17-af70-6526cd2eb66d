﻿using Common;
using cspb;
using Cysharp.Threading.Tasks;
using DeepUI;
using Game.Config;
using GameState;
using K1;
using K3;
using Logic;
using Logic.Treasure;
using MainScripts;
using P2;
using Render;
using TFW;
using TFW.Map;
using THelper;
using UI;
using UnityEngine;


namespace Game.Data
{

    /// <summary>
    /// 游戏初始化流程管理
    /// </summary>
    public class GameDataFlowMgr : Ins<GameDataFlowMgr>
    {
        /// <summary>
        /// 是否登录成功后
        /// </summary>
        public bool IsLoginSucceed { get; private set; }

        /// <summary>
        /// 是否拿到角色信息后
        /// </summary>
        public bool IsPlayerInfoAck { get; private set; }

        /// <summary>
        /// 清理
        /// </summary>
        public void Clear()
        {
            IsLoginSucceed = false;
            IsPlayerInfoAck = false;
        }

        /// <summary>
        /// 游戏登录成功后
        /// </summary>
        public async UniTask OnLoginSucceed(LoginAck loginAck)
        {
          
            if (loginAck.errCode != ErrCode.ErrCodeSuccess)
            {
                //ClientLogMgr.AddClientLogInfo($"LoginAckError:{loginAck.errCode}");
                return;
            }

            var loginEvent = new K3.LoginEvent() { EventKey = $"dataflow_start" };
            K3GameEvent.I.TaLog(loginEvent);
             
            //ClientLogMgr.AddClientLogInfo(ClientLogType.GameLogic1);
            //custom配置重定向
            var serverId = LoginMgr.I.loginData.serverid;

            //using (new LaunchTimeLogger("CfgServerMultiConfigHelper.InitCfgInfo"))
            //{
            //    await CfgServerMultiConfigHelper.InitCfgInfo(serverId);
            //}

            //ClientLogMgr.AddClientLogInfo(ClientLogType.GameLogic2);

            //登录红点功能
            LGameRedPoint.I.Init();

            //初始化一些单独的数据信息，不牵扯到网络相关的信息
            GameData.I.Init();

             
            //ClientLogMgr.AddClientLogInfo(ClientLogType.GameLogic3);

            //大地图战场标记，
            //玩家信息新增战场信息, 需要处理一下大地图标记位，解决loading时大地图重复加载问题
            LCrossServer.I.CurrSceneType = loginAck.sceneType;
            if (loginAck.sceneType == SceneType.SceneTypeKvk)
                GameData.I.ServerWarData.UpdateServerWarFlag(true);
            //else if (loginAck.sceneType == SceneType.SceneTypeDragonWar)
            //    GameData.I.DragonWarData.SignData.UpdateDragonWaringSign(true);
             
            using (new LaunchTimeLogger("CfgConfig.Init"))
            {
                //初始化配置相关信息
                await CfgConfigExtension.Init();
            }

            //ClientLogMgr.AddClientLogInfo(ClientLogType.GameLogic2);
            //五星好评更新登录次数
            FivePraiseMgr.I.UpdateDayLoginTime();

            WndMgr.ResetOrder();

            // 初始化玩家信息模块
            LPlayer.I.Init(loginAck);

            

            //BrightBattleFieldMgr.I.Init();
            //LBuildingManager1.I.Init();

            //初始化跨服模块
            LCrossServer.I.Init();
            //TransServerNtfMgr.I.Init();

            //登录成功后打点一次
            // FPSSummary.RecordFPS();
            // FPSSummary.RecordFPSDetial();

            // #if USE_TGSBASIC && !UNITY_WEBGL
            //             // SDK重新设置PlayerID
            //             SDK.I.AddSessionCallbackParameter(SDKConfig.Udid, LPlayer.I.PlayerID.ToString());
            //             SDK.I.TraceLogin();
            // #endif

            //AIHelp更新设置
            // #if USE_TGSBASIC && !UNITY_WEBGL
            //             AICSService.Instance.UpdateRoleInfo(string.IsNullOrEmpty(LoginMgr.I.CharacterJwt) ? "" : LoginMgr.I.loginData.playerid.ToString(),
            //                                             string.IsNullOrEmpty(LoginMgr.I.CharacterJwt) ? "" : LoginMgr.I.CharacterJwt);
            // #endif

            //ClientLogMgr.AddClientLogInfo(ClientLogType.GameLogic3);
            //BILogInfo.UpdateFTEStageParameter("OnLoginSuccess end");
            //LoadingLog.AddLoadingBILog("OnLoginSuccess end");
         

            //MessageMgr.ResumeDispatch();

            GamePlayerPrefs.Set(GameConfig.ALLIANCE_WAR_TERRITORY_LIST_TIPS_RED, true);

            //ClientLogMgr.AddClientLogInfo(ClientLogType.GameLogic4);


            // #if USE_HUAWEI
            //             if(K1D2Config.Channel == MainScripts.Channel.HW)
            //             {
            //                 HuaweiPayManager.I.Init();
            //             }
            // #endif
           
       
            //LAllianceMgr.I.Init();
            //初始化KVK活动数据(主要用于监听每日免费迁城次数)
            GameData.I.ServerWarData.Init();
            // LHalloween.I.Init();
              

            //初始化并启动设置步骤
            LSetting.I.Init().SetByStep(LSetting.SetStep.SetStep1);

            //玩家关卡信息数据
            // LAdventure.I.OnEnterGame();

            //修复LAdventure注册协议处理时机太晚的问题
         
            //迁服
            LMigrationServer.I.Init();
            //熔岩地洞
            //LLavaCave.I.Init();

            //新招募系统
            //HeroRecruitMgr.I.InitData();
            //LHeroStageMgr.I.Init();

            //次元宝藏
            LTreasureNet.I.Init();

            LCombinedService.I.Init();

            //军团信息
            LLegionMgr.I.Init();
  
            //ClientLogMgr.AddClientLogInfo(ClientLogType.GameLogic5);

            //初始化游戏相关网络数据
            // 不要随便改变消息请求的顺序，下列请求的Ack相互之间会有隐藏的依赖，必须保证他们的Req顺序

            // 拉取玩家的资产信息
            MessageMgr.Send(new AssetReq());
            // 玩家基础信息
            MessageMgr.Send(new PlayerInfoReq()); 
            //获取玩家的被动技能
            MessageMgr.Send(new PropReq());
               
            //ClientLogMgr.AddClientLogInfo(ClientLogType.GameLogic6);

            // 强制同步一次服务器时间  放最后
            GameTime.SyncServerTime();
             
            IsLoginSucceed = true;

            loginEvent = new K3.LoginEvent() { EventKey = $"dataflow_exit" };
            K3GameEvent.I.TaLog(loginEvent);

        }


        /// <summary>
        /// 玩家角色信息收到之后
        /// </summary>
        /// <returns></returns>
        public async UniTaskVoid OnPlayerInfoReceived()
        {
            using var tmp = new LaunchTimeLogger("OnPlayerInfoReceived");

            //ClientLogMgr.AddClientLogInfo(ClientLogType.GameLogicPlayerInfoAck1);
            EntityCity.s_FirstEnter = true;

            //大地图相关协议监听
            GameData.I.MapEventData.Init();
            //初始化侦查数据
            //GameData.I.ReconData.Init();
            //初始化KVK活动数据(主要用于监听每日免费迁城次数)
            GameData.I.ServerWarData.Init();
            //初始化集结数据
            GameData.I.RallyData.Init();

            
                //初始化活动数据
                //await BadgeMgr.I.Init();
                //BadgeGuideMgr.I.Init();
            


            GameData.I.ReinforceData.Init();

            //赛季
            GameData.I.SeasonData.Init();

            GameData.I.WorldMapData.Init();

            SceneManager.I.RegistrySceneCameraHandler((int)MainMenuType.CITY, GameInstance.CityMainCameraEnable);
            SceneManager.I.RegistrySceneCameraHandler((int)MainMenuType.WORLD, MapMgr.SetCameraEnable);
            //SceneManager.I.RegistrySceneUI<UIMainCity>((int)MainMenuType.PRISON);
            //SceneManager.I.RegistrySceneUI<UIHeroList>((int)MainMenuType.HERO);
            //SceneManager.I.RegistrySceneUI<UI.Alliance.UIAllianceMain>((int)MainMenuType.ALLIANCE);

            //初始化
#if UNITY_EDITOR
            ViewRectDebug.Create();
#endif

            //战力限制
            LMassPowerLimit.I.Init();

            //设置loading状态
            GameConfig.IsLoading = false;

            NTimer.CountDown(0.1f, () =>
            {
                //默认显示内城信息
                WorldSwitchMgr.I.ShowWorldType = WorldTypeEnum.CITY;

                //if (!K1D2Config.I.Map_Delay_Init)
                {
                    //大地图的延迟初始化
                    //MapDelayInitMgr.I.InitMap().Forget();
                }

                //英雄招募
                //HeroRecruitMgr.I.InitData();
                //玩家界面
                PlayerMainMgr.I.InitData();
                //英雄战力恢复
                HeroPowerRecoverMgr.I.InitData();
                //商城数据最后获取
                ShopMgr.I.InitData();
                //英雄队列信息
                HeroTeamInfoMgr.I.InitData();
                //女神的试炼功能
                LGoddessChallenge.I.Init();
            });

            GameTime.durationTime = Time.realtimeSinceStartup - GameTime.startTime;


            IsPlayerInfoAck = true;
        }





        /// <summary>
        /// 主界面加载成功后逻辑
        /// </summary>
        /// <returns></returns>
        public void OnUIMainDone()
        {
            //await PreloadHelper.isPreloadConfigComplete;

            //ClientLogMgr.AddClientLogInfo(ClientLogType.GamePlayLoadUIMainDone);
            //停止loading声音
            AudioManager.Instance.StopAudioChannel(AudioChannelType.LOADING, true);
            //设置停止声音系统的监听
            //AudioManager.EnabelAudioListener(false);

            //保险起见，走一遍新手引导
            //if (GameData.I.UnlockData.MaxLevel == 0 && LAdventure.I.IsAdventureAckBack)
            //{
            //    Game.Map.MapManager.I.MapOperationIsFirstInGame(true);
            //}

            //BILogInfo.UpdateFTEStageParameter("GSGamePlay: UIMain Loaded");
            //LoadingLog.AddLoadingBILog("GSGamePlay: UIMain Loaded");

            //初始化英雄战斗输入
            //FightInputMgr.Init();
            MessageMgr.Send(new LoadingSuccessReq());
            //K1D2Config.ShowLoginStopwatch("加载并出现云");
            //ClientLogMgr.AddClientLogInfo(ClientLogType.GamePlayLoadCloud);
            //LogoLoading.ins.UpdateLoadingStep(LogoLoadingStepEnum.GamePlayLoadCloud);
             
            //PopupManager.I.ClearAllPopup();
             
            ////隐藏显示loadingtips信息
            //LogoLoading.LoadingTipsActive(false).Forget();

            //发送进入战场场景的请求（主要为了第一次进入的时候落城处理）F
            //LCrossServer.I.FirstEnterSceneReq();

            ////初始化资源包下载初始化
            //LResPackDownloadMgr.I.Init();

            //刷新锁定的帧率
            //GameQuality.ChangeLockMaxFPS();

            //进入游戏时，清理当前缓存的BI FTE信息数据
            //BILogInfo.ClearBIFTEInfo();

            //恢复停服维护标记
            GameConfig.IsAppMaintain = false;

            //恢复冷库服标记
            GameConfig.IsColdServer = false;

        }

        /// <summary>
        /// 播放白云完成回调
        /// </summary>
        //private void PlayCloudDoneAction()
        //{
        //    //LoadingLog.TraceBILog("loading_13_done");
        //    //LoadingLog.Clear();
        //    //LogoLoading.ins.UpdateLoadingStep(LogoLoadingStepEnum.PlayCloudDoneAction);
        //    //BILog.UserFte(
        //             //$"loading_9",
        //             //$"2"
        //             //);
        //}
    }
}
