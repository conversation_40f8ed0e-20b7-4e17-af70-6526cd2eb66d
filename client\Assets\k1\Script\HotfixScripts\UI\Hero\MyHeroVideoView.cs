﻿using Common;
using cspb;
using Cysharp.Threading.Tasks;
using DeepUI;
using DG.Tweening;
using Game.Data;
using K3;
using Logic;
using Public;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TFW;
using TFW.Localization;
using TFW.UI;
using UnityEngine;
using UnityEngine.UI;

namespace UI
{
    public class MyHeroVideoViewData : UIData
    {
        public Game.Data.HeroData mainHeroData;
    }



    [Popup("Hero/MyHeroVideoView",true,true)]
    public class MyHeroVideoView:BasePopup
    {

        [PopupField("Root/BtnClose")]
        private GameObject btnClose;

        [PopupField("Root/btnHeart")]
        private GameObject btnHeart;

        [PopupField("Root/btnVideo")] private GameObject btnVideo;

        [PopupField("Root/btnRecharge")] private GameObject btnRecharge;

        [PopupField("Root/btnGift")] private GameObject btnGift;

        [<PERSON>up<PERSON>ield("Root/lv/Text")] private TFWText lvText;
        [<PERSON>up<PERSON>ield("Root/lv/Image/slider")] private TFWImage lvProcess;
        [PopupField("Root/lv/Image/Text")] private TFWText lvProcessText;

        [PopupField("Root/videoGrid/Scroll View/Viewport/Content")] private UIGrid videoGrid;

        [PopupField("Root/giftGrid/Scroll View/Viewport/Content")] private UIGrid itemGrid;

        [PopupField("VideoCanvas/HeroVideo")]
        private PlayAddressableVideo VideoBG;

        [PopupField("Root")]
        private Transform effectTransform;

        [PopupField("Root/videoGrid/mask")]
        private GameObject videoMask;
        [PopupField("Root/giftGrid/mask")]
        private GameObject giftMask;
        [PopupField("Root/videoGrid")]
        private RectTransform videoObj;
        [PopupField("Root/giftGrid")]
        private RectTransform giftObj;


        private Game.Data.HeroData mHero;

        List<Cfg.G.CItem> heroGoodItemList;

        private Cfg.G.CItem selectItem;

        private const int heartItemID = 3000100;


        protected override void OnInit()
        {
            base.OnInit();

            BindClickListener(btnClose, (x,y) =>
            {
                Close();
            });

            BindClickListener(btnHeart, (x, y) =>
            {
                UniTask.Void(async () =>
                {
                    //送心心
                    bool maxLv = await mHero.MaxIntimacyLevel();
                    if (maxLv)
                    {
                        FloatTips.I.FloatMsg("ErrCodeHeroIntimacyFull".ToLocal());
                        return;
                    }
                    if (PlayerAssetsMgr.I.GetItemCountByID(heartItemID) > 0)
                    {
                        PopupManager.I.FindPopup<MyHeroShowView>()?.InitCreateItem();

                        //赠送礼物
                        var item = new UseItem()
                        {
                            CfgID = heartItemID,
                            count = 1,
                            customCtx = { { "hero", mHero.ModelId.ToString() } }
                        };
                        PlayerAssetsMgr.I.UseItem(item);

                        var gameEvent = new K3.GameEvent();
                        gameEvent.EventKey = "hero_intimacy";
                        gameEvent.Properties.Add("cfgid", heartItemID.ToString());
                        K3.K3GameEvent.I.TaLog(gameEvent);
                    }
                    else
                    {
                        ItemStoreMgr.I.OpenGetPropPop(heartItemID, 1);
                    }
                });
               
            });

            BindClickListener(btnVideo, (x, y) =>
            { 
                videoObj.DOAnchorPos(new Vector2(0, 0), 0.3f).SetEase(Ease.OutCubic).OnComplete(() => { 
                    videoMask.SetActive(true);
                });
            });
            BindClickListener(videoMask, (x, y) =>
            { 
                videoObj.DOAnchorPos(new Vector2(0, -600), 0.3f).SetEase(Ease.OutCubic).OnComplete(() => {
                    videoMask.SetActive(false);
                });
            });

            BindClickListener(btnGift, (x, y) =>
            {
                giftObj.DOAnchorPos(new Vector2(0, 0), 0.3f).SetEase(Ease.OutCubic).OnComplete(() => {
                    giftMask.SetActive(true);
                });
            });
            BindClickListener(giftMask, (x, y) =>
            {
                giftObj.DOAnchorPos(new Vector2(0, -600), 0.3f).SetEase(Ease.OutCubic).OnComplete(() => {
                    giftMask.SetActive(false);
                });
            });

        }

        private MyHeroVideoViewData mData;

        protected internal override void OnOpenComplete()
        {
            base.OnOpenComplete(); OnShow();
        }

        protected internal override void OnShowComplete()
        {
            base.OnShowComplete(); OnShow();
        }

        private async UniTaskVoid OnShow()
        {
            mData= Data as MyHeroVideoViewData;


            EventMgr.RegisterEvent(TEventType.RefreshHeroData, RefreshHeroByEvent, this);

            if (heroGoodItemList == null)
            {
                heroGoodItemList = new List<Cfg.G.CItem>();

                var list = await Cfg.C.CIntimacyItem.GetEnumerableAsync();
                foreach (var item in list)
                {
                    var itemCfg = await Cfg.C.CItem.GetConfigAsync(item.Id);
                    if (selectItem == null)
                        selectItem = itemCfg;
                    heroGoodItemList.Add(itemCfg);


                    UseItemEffect.Add(item.Id, new ItemEffectAndDialog()
                    {
                        EffectStr = $"Assets/K3/Res/Effect/UI/{item.Effect}.prefab",
                        RandDialog = item.RandDialog,
                    });
                }
            }


            if (mData != null)
            {
                mHero = mData.mainHeroData;


                await RefreshHeroGood();
            }
        }


        private async UniTaskVoid RefreshHeroByEvent(object[] objs)
        { 
            mHero = HeroGameData.I.GetHeroById(mHero.HeroId);
             
            RefreshHeroGood().Forget();
        }



        protected internal override void OnCloseComplete()
        {
            base.OnCloseComplete();

            curHeroVideoSkin = string.Empty;
            PlayingEffect?.Clear();
        }


        struct ItemEffectAndDialog
        {
            public string EffectStr;
            public List<string> RandDialog;
        }
        private Dictionary<int, ItemEffectAndDialog> UseItemEffect = new Dictionary<int, ItemEffectAndDialog>();

        [PopupEvent(TEventType.K3UIHero_GoodItem_Select)]
        private void SelGoodItem(object[] objs)
        {
            if (objs.Length > 0)
            {
                selectItem = objs[0] as Cfg.G.CItem;

                RefreshHeroGood().Forget();
            }
        }

        [PopupEvent(TEventType.HeroVedioSkinSelect)]
        private void VedioSkinSelect(object[] objs)
        {
            if (objs.Length > 0)
            {
                curHeroVideoSkin = objs[0].ToString();

                RefreshHeroGood().Forget();
            }
        }

        private string curHeroVideoSkin = string.Empty;

        private async UniTask RefreshHeroGood()
        {

            var processData = await mHero.IntimacyProcess();
            var intimacyData = await mHero.IntimacyLevel();
            lvText.text = intimacyData.Item1.ToString();

            lvProcess.fillAmount = intimacyData.Item2;
            lvProcessText.text = $"{processData.Item1}/{processData.Item2}";

            if (string.IsNullOrEmpty(curHeroVideoSkin))
            {
                curHeroVideoSkin = ChapterTaskMgr.I.GetHeroVedioSkin(mHero.HeroCfg.Id);
            }

            if (mHero.HeroCfg.UnlockVideo.Count > 0)
            { 
                var videoLenth = mHero.HeroCfg.UnlockVideo.Count / 2;

                videoGrid.Clear();
                for (int i = 0; i < videoLenth; i++)
                {
                    var videoName= mHero.HeroCfg.UnlockVideo[i * 2 + 1];
                    videoGrid.AddItem<HeroVideoSkinBtn>().SetData(mHero, videoName,int.Parse(mHero.HeroCfg.UnlockVideo[i*2]), intimacyData.Item1, curHeroVideoSkin);
                } 
            }

            itemGrid.Clear();
            foreach (var item in heroGoodItemList)
            {
                itemGrid.AddItem<HeroViewGoodItem>().SetItem(mHero, item, selectItem);
            }

            curHeroVideoSkin = ChapterTaskMgr.I.GetHeroVedioSkin(mHero.HeroCfg.Id);

            await VideoBG.PlayVideo(curHeroVideoSkin);

            if (string.IsNullOrEmpty(curHeroVideoSkin))
            {
                VideoBG.gameObject.SetActive(false);
                
                //TODO 播放第一个视频
            }
            else
            {
                VideoBG.gameObject.SetActive(true);
            }
        }


        [PopupEvent(TEventType.UseItemAck)]
        private async void UseItemAck(object[] objs)
        {
            if (objs.Length > 0)
            {
                var ack = objs[0] as UseItemAck;
                 
                foreach (var item in ack.useItems)
                {
                    if (UseItemEffect.TryGetValue(item.ID, out var effect))
                    {

                        if (PlayingEffect == null)
                        {
                            PlayingEffect = new Queue<ItemEffectAndDialog>();
                        }

                        PlayingEffect.Enqueue(effect);


                        if (!effectPlaying)
                        {
                            await PlayEffect();
                        }
                    }

                }
            }
        }

        private async UniTask PlayEffect()
        {
            if (PlayingEffect.Count > 0)
            {
                effectPlaying = true;

                ItemEffectAndDialog data = PlayingEffect.Dequeue();
                var effectTr = await ResourceMgr.LoadInstanceAsync(data.EffectStr, effectTransform);
                var dialogPrePath = "Assets/K3/Res/Effect/UI/Eff_ui_zbj_Dialog.prefab";
                var dialogTr = await ResourceMgr.LoadInstanceAsync(dialogPrePath, effectTransform);

                float timeDelay = 2.5f;
                var animation = effectTr.GetComponent<Animation>();
                if (animation != null)
                {
                    timeDelay = animation.clip.length;
                }

                var dialogText = dialogTr.transform.GetChild(0).GetComponentInChildren<Text>();
                var dialogStr = data.RandDialog[UnityEngine.Random.Range(0, data.RandDialog.Count)];
                dialogText.text = LocalizationMgr.Format(dialogStr, LPlayer.I.PlayerName);

                await UniTask.WaitForSeconds(timeDelay);

                UnityEngine.Object.Destroy(effectTr);
                UnityEngine.Object.Destroy(dialogTr);
                ResourceMgr.ReleaseInstance(data.EffectStr);
                ResourceMgr.ReleaseInstance(dialogPrePath);

                await PlayEffect();
            }
            else
            {
                effectPlaying = false;
            }
        }

        private bool effectPlaying = false;
        private Queue<ItemEffectAndDialog> PlayingEffect;
    }
}
