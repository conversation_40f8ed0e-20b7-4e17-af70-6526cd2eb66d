﻿using System;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using System.Text;



#if UNITY_EDITOR
using Sirenix.OdinInspector;
#endif


public enum LogoLoadingProcessEnum
{ 
    GameQualityMgr = 1,
    AddressablesInit,
    LocalizationMgr,
    GetEntrance,
    CD2ConfigInit,
    GSSDK,
    GSGateWay,
    GSAuth,
    GSAutoLogin,
    GSDataFlow,
    GSGamePlay
}

/// <summary>
/// loading固定逻辑，无法热更
/// </summary>
public class Loading_Res : MonoBehaviour
{
    #region Loading 一些表现元素


    [SerializeField]
    private Slider m_Slider = null;


    [SerializeField]
    private Text m_Label = null;


    [SerializeField]
    private Text m_ProcessLabel = null;

    /// <summary>
    /// 当前版本号显示内容
    /// </summary>
    [SerializeField]
    private Text m_VersionText = null;


    #endregion


    #region Editor相关测试元素

#if UNITY_EDITOR
     
    /// <summary>
    /// 是否使用快速登录，调试外网真实数据使用
    /// </summary>
    [Header("是否快速登录，调试外网数据使用")]
    public bool IsFastLoginGold = false;

    /// <summary>
    /// 是否使用快速登录，调试内网真实数据使用
    /// </summary>
    [Header("是否快速登录，调试内网数据使用")]
    public bool IsFastLoginDev = false;

    /// <summary>
    /// 是否使用快速登录，调试内网真实数据使用
    /// </summary>
    [Header("是否快速登录，调试内网数据使用")]
    public bool IsFastLoginBeta = false;

    /// <summary>
    /// 外网调试时的PlayerId
    /// </summary>
    [Header("外网调试时的PlayerId")]
    public int FastLoginPlayerId = 0;

    /// <summary>
    /// 外网调试时的serverId
    /// </summary>
    [Header("外网调试时的ServerId")]
    public int FastLoginServerId = 0;

     
    /// <summary>
    /// Gate地址
    /// </summary>
    [Header("线上，指定连接的Gate地址，如果Entrance地址为空则使用该默认地址")]
    public string GateAddr = "";

#endif




    #endregion


    /// <summary>
    /// 单例
    /// </summary>
    public static Loading_Res Ins;

    public static LogoLoadingProcessEnum CurLoadingProcess { get; set; }


    private void Awake()
    {
        Loading_Language.Init();
        Ins = this;

        AddListenerEvent();

        ShowLoading();

        ShowVerInfo();

        DontDestroyOnLoad(gameObject);

        m_Slider.minValue =0;
        m_Slider.maxValue =(int)LogoLoadingProcessEnum.GSGamePlay*100;
    }

    private StringBuilder stateText = new StringBuilder();
    private void Update()
    { 
        stateText.Clear();
        var process = (int)CurLoadingProcess;

        for (int i = 0; i < process; i++)
        {
            stateText.Append(". ");
        }
        m_ProcessLabel.text = stateText.ToString();


        if (process >= 3)
        {
            var toValue= Mathf.Lerp(m_Slider.value, process * 100, Time.deltaTime);
            m_Label.text = $"{ (int)(toValue*100 / m_Slider.maxValue)}%";
            m_Slider.value= toValue;
        }
    }


    /// <summary>
    /// 进度条显示
    /// </summary>
    /// <param name="progress"></param>
    /// <param name="resetFakeProgress">是否重置虚假进度条显示</param>
    /// <param name="label"></param>
    /// <param name="issinglelogo"></param>
    public static void ShowLoading()
    {
        if (Ins == null)
            return;

        Ins.gameObject.SetActive(true);
    }


    /// <summary>
    /// 注册事件
    /// </summary>
    private void AddListenerEvent()
    {
        //AddListener(EventTriggerType.Click, clickTpUpdate, (x, y) =>
        //{
        //    //跳转到相应上带你
        //    VerForceUpdateUtils.OpenStore(false);
        //});

        //AddListener(EventTriggerType.Click, _confirmBtn, OnClickConfirmBtn);
        //AddListener(EventTriggerType.Click, _closeBtn, OnClickCancelBtn);
    }

    #region loading处理


    /// <summary>
    /// 清理显示进度条信息
    /// </summary>
    public static  void ClearProgress()
    {
        if(Ins!=null) 
       Ins.m_Slider.value = 0;
    }


    private void LoadingHide()
    {
        gameObject.SetActive(false);
    }


    /// <summary>
    /// 显示资源打包信息、Code信息、上次玩家ID
    /// </summary>
    /// <param name="resBuildInfo"></param>
    /// <param name="codeBuildInfo"></param>
    /// <param name="playerID"></param>
    public static void ShowVerInfo()
    {
        if (Ins == null)
            return;
        string versionCode = PlatformUtils.GetVersionCode();
        Ins.m_VersionText.text = string.Format("Ver:{0}.{1}", Application.version, versionCode);
    }

    public static void HideLoading()
    {
        Ins?.LoadingHide();
    }



    #endregion

    #region 弹窗处理

    /// <summary>
    /// 显示消息
    /// </summary>
    /// <param name="size"></param>
    /// <param name="onCancel"></param>
    /// <param name="onConfirm"></param>
    public void ShowMessage(string title, string message, string confirmText,
        Action onCancel, Action onConfirm)
    {

        ////记录Action
        //_onCancel = onCancel;
        //_onConfirm = onConfirm;

        ////显示提示信息和内容
        //_messageTitle.text = title;
        //_messageText.text = message;
        //_confirmText.text = confirmText;

        ////显示提示信息
        //_showMessageObj.SetActive(true);
    }


    #endregion




}

 