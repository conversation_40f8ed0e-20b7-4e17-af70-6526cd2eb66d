﻿







using Common;
using cspb;
using Cysharp.Threading.Tasks;
using DeepUI;
using ExLog;
using Game.Config;
using Game.Data;
using Game.Utils;
using Google.Protobuf;
using k1;
using K3;
using KS;
using Logic;
using MainScripts;
using System;
using TFW.Localization;
using UI;
using UnityEngine;

using static UI.FloatTips;

/// <summary>
/// 自动登录阶段
/// </summary>
namespace GameState
{

    /// <summary>
    /// 角色登录时数据结构
    /// </summary>
    public class AutoLoginData
    {
        public CharacterData characterData;
        public AuthAck authAck;
    }

    /// <summary>
    /// 角色登录状态
    /// </summary>
    public class GSAutoLogin : GSBase<HLAutoLogin>
    {
        /// <summary>
        /// 登录信息
        /// </summary>
        private AutoLoginData m_LoginData;

        /// <summary>
        /// 登录协议信息
        /// </summary>
        private LoginAck m_LoginAck;

        /// <summary>
        /// 是否第一次登陆（新用户或者新角色）
        /// </summary>
        private bool isFirstLogin = false;

        private const int TIMEOUT = 15 * 1000;
        private long _beginTime;
        private bool checkTimeOut = false;

        /// <summary>
        /// 记录本次登录服务器ID 
        /// </summary>
        private int m_LastServerID;

        /// <summary>
        /// 记录本次登录的PlayerId
        /// </summary>
        private long m_LastPlayerID;

       

        public override string processName => "AutoLogining";

        /// <summary>
        /// 当前是否登录成功
        /// </summary>
        private bool isLoginSuccess = false;

        public static bool CheckCanLogin(int serverId)
        {
            //老客户端限制进新服
            if (LoginMgr.I.CheckIsLimit(serverId))
            {
                return false;
            }

            if (!LoginMgr.I.ContainServer(serverId))
            {
                Debug.LogWarning($"{serverId}服务器不存在或者维护中");
                return false;
            }

            return true;
        }

        public GSAutoLogin(AutoLoginData loginData) : base(false)
        {

            m_LoginData = loginData;
        }

        public override async UniTask Enter()
        {
            Loading_Res.CurLoadingProcess = LogoLoadingProcessEnum.GSAutoLogin;
            m_IsDone = true;
            await base.Enter();
        }

        public override async UniTask Exit()
        {
            m_LoginAck = null;
            checkTimeOut = false;
            isLoginSuccess = false;
            MessageMgr.UnregisterMsg<LoginAck>("GSAutoLogin");

            EventMgr.UnregisterEvent(TEventType.PlayerInfoReceived, this);

            await base.Exit();
        }

        public override async UniTask Prepare()
        {
            if (m_LoginData.characterData != null && !CheckCanLogin(m_LoginData.characterData.serverid))
            {
                GSSwitchMgr.AddStage(new GSSelectRole());
                return;
            }

            //BILogInfo.UpdateFTEStageParameter("GSAutoLogin: Prepare");
            //LoadingLog.AddLoadingBILog("GSAutoLogin: Prepare");

            //记录当前开始时间
            GameTime.startTime = Time.realtimeSinceStartup;
            checkTimeOut = true;

            MessageMgr.RegisterMsg<LoginAck>("GSAutoLogin", OnLoginAck);
              
            //角色登录
            DoLogin();
            LoadingModuleMgr.I.UpdatePreloadFlag(true);
            await base.Prepare();
        }

        public override void Update()
        { 
            if (checkTimeOut && _beginTime > 0 && (GameTime.LocalTime - _beginTime) > TIMEOUT)
            {
                _beginTime = GameTime.LocalTime;

                ReconnectManager.I.ShowNetReconnectMark(false);

                var dialog = PopupManager.I.FindPopup<UILoginTipsDialog>();
                if (dialog != null && dialog.IsShow)
                {
                    return;
                }

                LoginMgr.ShowLogfailWindow(LoginFailEnum.GSAutoLoginFail);

                K3GameEvent.I.TaLog(new LoginFailEvent() { EventKey = "login_autologintimeout" });

              
            } 
        }

        #region 角色创建或登录

        /// <summary>
        /// 启动登录
        /// </summary>
        private void DoLogin()
        {
            //BILogInfo.UpdateFTEStageParameter("GSAutoLogin: DoLogin");
            //LoadingLog.AddLoadingBILog("GSAutoLogin: DoLogin");
            ////BILog.UserFte(
            //    $"open",
            //    $"2"
            //    );

           
            //设置游戏启动标识，此时有可能是新开游戏进入，也有可能是进入游戏之后切换账号再次进入
            LoginMgr.IS_START_GAME_LOGIN = false;
            isFirstLogin = false;

            IMessage message = null;
            D.Debug?.Log(" recommand serverId={0} server clientVersion={1}", m_LoginData.authAck.recommandServer, m_LoginData.authAck.clientVersion);

            //强制更新移植检查更新那边去，不再放在登录 zjw modify by 7.27
            //强制更新还原,2步检查
            var needUpdate = VerForceUpdateUtils.CheckVersionUpdate(AppInfo.Version, m_LoginData.authAck.clientVersion);
            if (needUpdate)
            {
                //ClientLogMgr.AddClientLogInfo(ClientLogType.DoLoginFailForceUpdate);
                return;
            }

            int msgLoginServerID = 0;

            //判断是否为第一次登录或创建角色
            if (m_LoginData.characterData == null)
            {
                //ClientLogMgr.AddClientLogInfo(ClientLogType.DoLoginCreatRole);
                //服务器停服状态
                if (!CheckServerState(m_LoginData.authAck.recommandServer))
                {
                    //ClientLogMgr.AddClientLogInfo(ClientLogType.DoLoginCreatRoleFail);
                    return;
                }

                //创建新的角色
                message = CreateNewRole(m_LoginData.authAck.recommandServer);
                isFirstLogin = true;
                msgLoginServerID = GetMessageServerID(message);

                //BILogInfo.UpdateFTEStageParameter($"GSAutoLogin: CreateNewRole {msgLoginServerID}");
                //LoadingLog.AddLoadingBILog($"GSAutoLogin: CreateNewRole {msgLoginServerID}");
            }
            else
            {

                try
                {
                    //BILog.UserFteSwitch($"loading_5.3");
                    //角色登录
                    message = Login();
                    msgLoginServerID = GetMessageServerID(message);
                    //BILogInfo.UpdateFTEStageParameter($"GSAutoLogin: Login {msgLoginServerID}");
                    //LoadingLog.AddLoadingBILog($"GSAutoLogin: Login {msgLoginServerID}");
                }
                catch (Exception e)
                {
                    //BILog.UserFte(
                                  //$"loading_bug5",
                                  //$"2", new JsonBIObj("args", e.Message)
                                  //);
                }

            }

            //BILog.UserFte(
    //$"Loading_4.1",
    //$"{msgLoginServerID}"
    //);

            //检测是否为冷库服
            if (CheckIsColdServer(msgLoginServerID))
            {
                //显示提示信息
                LoginMgr.ShowLoginFailWindow((_) =>
                {
                    //先发送消息数据
                    SendLoginServer(message, msgLoginServerID);

                    //自动重连
                    Main.DelayReStart(1f);
                },
                    LocalizationMgr.Get("PLAYER_settings_general_title_3"),
                    LocalizationMgr.Get("ForceTransferNotice01"),
                    LocalizationMgr.Get("MENU_ok_cap")
                );

                //ClientLogMgr.AddClientLogInfo(ClientLogType.DoLoginCodeServer);

                return;
            }
            else
            {
                //服务器停服状态
                if (!CheckServerState(msgLoginServerID))
                    return;

                //发送登录服务器
                SendLoginServer(message, msgLoginServerID);
            }

           
            //同步改异步 移动到LAllianceMgr.OnLogin()
            //by tanshengqi 2024/4/23
            //LAllianceAchievement.I.OnUnionChapterCfgsNtf_FromLocalTable();
        }

        /// <summary>
        /// 获取服务器相关数据
        /// </summary>
        /// <param name="message"></param>
        /// <returns></returns>
        private int GetMessageServerID(IMessage message)
        {
            if (message != null)
            {
                if (message is CharacterLoginReq)
                {
                    return (message as CharacterLoginReq).serverID;
                }
                else if (message is CharacterCreateReq)
                {
                    return (message as CharacterCreateReq).serverID;
                }
                else if (message is FastLoginReq)
                {
                    return (message as FastLoginReq).serverID;
                }
            }

            return 0;
        }

        /// <summary>
        /// 发送登录服务器信息
        /// </summary>
        private void SendLoginServer(IMessage message, int serverId)
        {
#if UNITY_EDITOR
            if (Loading_Res.Ins.IsFastLoginGold
                    || Loading_Res.Ins.IsFastLoginDev
                    || Loading_Res.Ins.IsFastLoginBeta)
            {
                if (Loading_Res.Ins.FastLoginPlayerId <= 0)
                {
                    LoginMgr.ShowLoginFailWindow((obj) =>
                    {
                    },
                    LocalizationMgr.Get("System_Banned_Info_Title"),
                    LocalizationMgr.Get($"PlayerId Is {Loading_Res.Ins.FastLoginPlayerId} PlayerId Can not <= 0"),
                    LocalizationMgr.Get("System_Banned_Info_Cap"));
                    return;
                }

                message = MakeFastLoginReq();
            }
#endif

            ////判断是服务器是否开启
            //if (LoginMgr.I.FindServerById(serverId) == null)
            //{
            //    ////临时处理，如果找不到服务器，那么就进入推荐服（如果推荐服有角色，那么就是登陆，没有角色就新建）
            //    message = HandleCanNotFindServerId();
            //    serverId = GetMessageServerID(message);

            //    //BILog.UserFte(
            //        $"Loading_4.2",
            //        $"{serverId}");

            //    //ClientLogMgr.AddClientLogInfo(ClientLogType.DoLoginNotFindServer);

            //    //服务器停服状态
            //    if (!CheckServerState(serverId))
            //        return;

            //    SendLoginToServer(message);
            //    return;
            //}
            //else
            //{
            if (!K1D2Config.I.IsIgnoreMaintain)
            {
                foreach (var item in m_LoginData.authAck.servers)
                {
                    if (item.serverID == serverId)
                    {
                        if (item.status == ServerStatus.Test)
                        {
                            //维护
                            //ClientLogMgr.AddClientLogInfo(ClientLogType.DoLoginServerMaintain);
                            DeepUI.PopupManager.I.ShowDialog<UI.UIPauseServerUpdate>(new UI.UIPauseServerUpdateData() { endTime = (long)(GameTime.Time + 1000 * 60 * MetaConfig.Maintenance_Time), ServerID = serverId });
                            Prepared = true;
                            //BILogInfo.UpdateFTEStageParameter($"GSAutoLogin: Server Test {serverId}");
                            //LoadingLog.AddLoadingBILog($"GSAutoLogin: Server Test {serverId}");
                            return;
                        }

                        var codeServer = CheckIsColdServer(serverId);
                        if (!codeServer && (item.playerNums == PlayerNumsState.PlayerNumsErr || item.isOnline == false))
                        {
                            //ClientLogMgr.AddClientLogInfo(ClientLogType.DoLoginServerNotOnline);
                            FloatTips.I.FloatMsg(LocalizationMgr.Get("Game_Server_Update_Notice"));
                            Prepared = true;
                            return;
                        }
                    }
                }
            }
             
            //BILog.UserFte(
            //$"Loading_4.3",
            //$"{serverId}");

            //BILogInfo.UpdateFTEStageParameter($"GSAutoLogin: SendLoginMsg {serverId}");
            //LoadingLog.AddLoadingBILog($"GSAutoLogin: SendLoginMsg {serverId}");
            //发送登录信息到服务器
            SendLoginToServer(message);
            //}
        }

        #region 冷库服判断

        /// <summary>
        /// 检测是否为冷库服(已经和其他服务器进行合并了)
        /// </summary>
        /// <param name="serverid"></param>
        /// <returns></returns>
        private bool CheckIsColdServer(int serverid)
        {
            if (LoginMgr.I.m_Servers.TryGetValue(serverid, out var serverValue))
            {
                //D.Error?.Log($" CheckServerState serverid {serverid} state {serverValue.playerNums}  online {serverValue.isOnline}");
                if (serverValue.coldServer > 0)
                {
                    //设置冷库服标记
                    GameConfig.IsColdServer = true;
                    return true;
                }
            }
            else
            {
                D.Warning?.Log($" CheckServerState state can not find serverId {serverid}");
            }

            return false;
        }

        #endregion

        #region 服务器状态判断

        /// <summary>
        /// 检测服务器状态，是否为停服维护状态
        /// </summary>
        /// <param name="serverid"></param>
        /// <returns></returns>
        private bool CheckServerState(int serverid)
        {
            if (K1D2Config.I.IsIgnoreMaintain)
                return true;

            if (LoginMgr.I.m_Servers.TryGetValue(serverid, out var serverValue))
            {
                D.Warning?.Log($" CheckServerState serverid {serverid} state {serverValue.playerNums}  online {serverValue.isOnline}");
                if (serverValue.playerNums == PlayerNumsState.PlayerNumsErr || serverValue.isOnline == false)
                {
                    GSStateUtil.ShowServerCloseInfo(true);
                    Prepared = true;
                    return false;
                }
            }
            else
            {
                D.Warning?.Log($" CheckServerState state can not find serverId {serverid}");
            }

            return true;
        }

        #endregion


        #region 创建角色
        /// <summary>
        /// 创建新的角色
        /// </summary>
        private IMessage CreateNewRole(int serverId)
        {

            int curCharacterNum = 0;
            foreach (var item in m_LoginData.authAck.characters)
            {
                if (item.serverID == serverId)
                {
                    curCharacterNum++;
                }
            }


            if (curCharacterNum >= 2)
            {
                if (m_LoginData.authAck.characters.Count > 0)
                {
                    //BILogInfo.UpdateFTEStageParameter("GSAutoLogin: CreateNewRole Max =>> Login !");
                    //LoadingLog.AddLoadingBILog("GSAutoLogin: CreateNewRole Max =>> Login !");
                    return LoginRole(m_LoginData.authAck.characters[0]);
                }
                else
                {
                    //BILogInfo.UpdateFTEStageParameter("GSAutoLogin: CreateNewRole Error!");
                    //LoadingLog.AddLoadingBILog("GSAutoLogin: CreateNewRole Error!");
                    return null;
                }
            }

            //BILogInfo.UpdateFTEStageParameter($"GSAutoLogin: CreateNewRole serverId:{serverId}");
            //LoadingLog.AddLoadingBILog($"GSAutoLogin: CreateNewRole serverId:{serverId}");
            var authAck = m_LoginData.authAck;
            var message = CreateCharacterCreateReq(authAck.accessToken, LoginMgr.GetFirstName(), authAck.accountID, serverId);

            var server = LoginMgr.I.FindServerById(authAck.recommandServer);
            var sever_name = server == null ? string.Empty : server.name;

            D.Warning?.Log(string.Format("DoLogin 游戏内创建角色:accountID:{1} serverID:{2} serverName:{3} accessToken:{0} ",
                authAck.accessToken, authAck.accountID, serverId, sever_name));

            return message;
        }

        /// <summary>
        /// 创建角色请求
        /// </summary>
        /// <param name="token"></param>
        /// <param name="roleName"></param>
        /// <param name="accID"></param>
        /// <param name="serverID"></param>
        /// <returns></returns>
        private CharacterCreateReq CreateCharacterCreateReq(string token, string roleName, long accID, int serverID)
        {
            var req = new CharacterCreateReq
            {
                accessToken = token,
                name = roleName,
                accountID = accID,
                serverID = serverID,
                cInfo = LoginMgr.I.GenerateClientInfo()
            };

            LoginMgr.I.loginData.username = roleName;
            m_LastServerID = serverID;
            //新号默认PlayerId为0
            m_LastPlayerID = 0;
            return req;
        }
        #endregion

        #region 角色登录处理

        /// <summary>
        /// 角色登录处理
        /// </summary>
        private IMessage Login()
        {
            IMessage message;

            // // 这里是角色登录超时处理过程,从GSGateway移过来的。
            // LogoLoadFail.Ins.StartLoadingTimeOutTime();

            //判断是否创建角色
            if (m_LoginData.characterData.action == CharacterAction.CreatRole
                && m_LoginData.characterData.serverid > 0)
            {
                //var authAck = m_LoginData.authAck;
                //message = CreateCharacterCreateReq(authAck.accessToken, LoginMgr.GetFirstName(), authAck.accountID, m_LoginData.characterData.serverid);

                //D.Warning?.Log(string.Format("DoLogin 游戏内创建角色:accountID:{1} serverID:{2} serverName:{3} accessToken:{0} ",
                //    authAck.accessToken, authAck.accountID, m_LoginData.characterData.serverid, m_LoginData.characterData.servername));

                //BILogInfo.UpdateFTEStageParameter("GSAutoLogin: CreateNewRole action==CreatRole !");
                //LoadingLog.AddLoadingBILog("GSAutoLogin: CreateNewRole action==CreatRole !");
                //创建一个新的角色
                //ClientLogMgr.AddClientLogInfo(ClientLogType.DoLoginCreatRole1);
                message = CreateNewRole(m_LoginData.characterData.serverid);
                isFirstLogin = true;



            }
            //判断是否更换设备号登录制定服务器
            else if (m_LoginData.characterData.action == CharacterAction.LoginServer
                && m_LoginData.characterData.serverid > 0)
            {
                //判断是否登录以前设备ID ps:用最近角色登录
                if (m_LoginData.authAck.characters.Count > 0)
                {
                    //ClientLogMgr.AddClientLogInfo(ClientLogType.DoLoginRole1);
                    //角色登录
                    message = LoginRole(m_LoginData.authAck.characters[0]);


                }
                //创建角色
                else
                {
                    //var authAck = m_LoginData.authAck;
                    //message = CreateCharacterCreateReq(authAck.accessToken, LoginMgr.GetFirstName(), authAck.accountID, m_LoginData.characterData.serverid);
                    //isFirstLogin = true;

                    //var server = LoginMgr.I.FindServerById(m_LoginData.characterData.serverid);
                    //var sever_name = server == null ? string.Empty : server.name;
                    //D.Warning?.Log(string.Format("DoLogin 第一次登陆创建角色:accountID:{1} serverID:{2} serverName:{3} accessToken:{0} ",
                    //    authAck.accessToken, authAck.accountID, m_LoginData.characterData.serverid, sever_name));

                    //BILogInfo.UpdateFTEStageParameter("GSAutoLogin: CreateNewRole characters.count==0 !");
                    //LoadingLog.AddLoadingBILog("GSAutoLogin: CreateNewRole characters.count==0 !");
                    //创建新的角色
                    message = CreateNewRole(m_LoginData.characterData.serverid);
                    isFirstLogin = true;
                    //ClientLogMgr.AddClientLogInfo(ClientLogType.DoLoginCreatRole2);

#if UNITY_EDITOR
                    if (Loading_Res.Ins.IsFastLoginGold
                        || Loading_Res.Ins.IsFastLoginDev
                        || Loading_Res.Ins.IsFastLoginBeta)
                    {
                        if (Loading_Res.Ins.FastLoginPlayerId <= 0)
                        {
                            LoginMgr.ShowLoginFailWindow((obj) =>
                            {
                            },
                            LocalizationMgr.Get("System_Banned_Info_Title"),
                            LocalizationMgr.Get($"PlayerId Is {Loading_Res.Ins.FastLoginPlayerId} PlayerId Can not <= 0"),
                            LocalizationMgr.Get("System_Banned_Info_Cap"));
                            return null;
                        }

                        message = MakeFastLoginReq();
                    }
#endif
                }
            }
            else
            {
                //直接登录 缓存的账号登录
                message = new CharacterLoginReq
                {
                    accessToken = m_LoginData.authAck.accessToken,
                    name = m_LoginData.characterData.username,
                    serverID = m_LoginData.characterData.serverid,
                    playerID = m_LoginData.characterData.playerid,
                    accountID = m_LoginData.authAck.accountID,
                    cInfo = LoginMgr.I.GenerateClientInfo(),
                };

                //ClientLogMgr.AddClientLogInfo(ClientLogType.DoLoginCacheRole);


                LoginMgr.I.loginData.username = m_LoginData.characterData.username;
                m_LastServerID = m_LoginData.characterData.serverid;
                m_LastPlayerID = m_LoginData.characterData.playerid;
                D.Warning?.Log(string.Format(
                    "DoLogin 用已有角色登录:  playerID:{1} serverID:{2} serverName:{3} accessToken:{0}",
                    m_LoginData.authAck.accessToken, m_LoginData.characterData.playerid,
                    m_LoginData.characterData.serverid, m_LoginData.characterData.servername));
            }

            return message;
        }

        /// <summary>
        /// 登录角色
        /// </summary>
        /// <returns></returns>
        private IMessage LoginRole(Character character)
        {
            if (character == null)
            {
                D.Warning?.Log(" Login Failure Character Is Null ");
                return null;
            }

            //BILogInfo.UpdateFTEStageParameter("GSAutoLogin: LoginRole");
            //LoadingLog.AddLoadingBILog("GSAutoLogin: LoginRole");
            var server = LoginMgr.I.FindServerById(character.serverID);
            var sever_name = server == null ? string.Empty : server.name;
            var message = new CharacterLoginReq
            {
                accessToken = m_LoginData.authAck.accessToken,
                name = character.meta.name,
                serverID = character.serverID,
                playerID = character.playerID,
                accountID = m_LoginData.authAck.accountID,
                cInfo = LoginMgr.I.GenerateClientInfo(),
            };

            LoginMgr.I.loginData.username = character.meta.name;
            m_LastServerID = character.serverID;
            m_LastPlayerID = character.playerID;
            D.Warning?.Log(string.Format(
                "DoLogin 用已有角色登录:  playerID:{1} serverID:{2} serverName:{3} accessToken:{0}",
                m_LoginData.authAck.accessToken, character.playerID, character.serverID, sever_name));

            return message;
        }

        /// <summary>
        /// 发送登录信息到服务器
        /// </summary>
        /// <param name="message"></param>
        private void SendLoginToServer(IMessage message)
        {
            //BILog.UserFteSwitch($"loading_5.4");
            if (message == null)
            {
                D.Warning?.Log(" Login To Server Is Null " + message);
                //BILogInfo.UpdateFTEStageParameter("GSAutoLogin: SendLoginMsg Msg is Null");
                //LoadingLog.AddLoadingBILog("GSAutoLogin: SendLoginMsg Msg is Null");
                //ClientLogMgr.AddClientLogInfo(ClientLogType.DoLoginBug1);
                return;
            }
            //TODO 临时处理，暂时关闭掉用户协议界面
            isFirstLogin = false;
            //判断是否为第一次登陆 弹出用户协议
            if (isFirstLogin)
            {
                //BILogInfo.UpdateFTEStageParameter("GSAutoLogin: SendLoginMsg UILoginProtocol");
                //LoadingLog.AddLoadingBILog("GSAutoLogin: SendLoginMsg UILoginProtocol");
                WndMgr.Show<UILoginProtocol>(new UILoginProtocolData
                {
                    callback = (isok) =>
                    {
                        if (isok)
                        {
                            LoginMgr.I.loginData.serverid = m_LastServerID;

                            _beginTime = GameTime.LocalTime;

                            MessageMgr.Send(message);
                            // #if USE_TGSBASIC && !UNITY_WEBGL
                            //                             SDK.I.TraceDNU();
                            // #endif
                        }
                    }
                });
            }
            else
            {
                //BILogInfo.UpdateFTEStageParameter($"GSAutoLogin: isNetConnect {ReconnectManager.I.kickOutReason.HasValue}, GameServerConnected {GameServerConnection.GameServerConnection.IsConnected}");
                //LoadingLog.AddLoadingBILog($"GSAutoLogin: isNetConnect {ReconnectManager.I.kickOutReason.HasValue}, GameServerConnected {GameServerConnection.GameServerConnection.IsConnected}");
                //BILog.UserFte(
                //$"Loading_4.4",
                //$"{m_LastServerID}");

                _beginTime = GameTime.LocalTime;


                D.Debug?.Log(" login serverId " + m_LastServerID);
                LoginMgr.I.loginData.serverid = m_LastServerID;
                MessageMgr.Send(message);

                //ClientLogMgr.AddClientLogInfo(ClientLogType.DoLoginSendMsg);

                //if (!GameServerConnection.GameServerConnection.IsConnected)
                //{
                //    //ClientLogMgr.AddClientLogInfo(ClientLogType.DoLoginNoNet);

                //    var ping = new System.Net.NetworkInformation.Ping();
                //    var gate_addr = LoginMgr.I.GetCurGateAddress();/*Entrance.GetEntranceInfo("gate_addr");*/
                //    var gas = gate_addr.Split(':');
                //    if (gas != null && gas.Length > 0)
                //    {
                //        var reply = ping.Send(gas[0], 1);
                //        if (reply.Status != System.Net.NetworkInformation.IPStatus.Success)
                //        {
                //            //BILog.UserFte(
                //                  //$"loading_bug6",
                //                  //$"2", new JsonBIObj("args", reply.Status.ToString())
                //                  //);
                //        }
                //        //BILogInfo.UpdateFTEStageParameter($"GSAutoLogin: net ping {reply.Status}, address {reply.Address}");
                //        //LoadingLog.AddLoadingBILog($"GSAutoLogin: net ping {reply.Status}, address {reply.Address}");
                //        D.Warning?.Log($"GSAutoLogin: net ping {reply.Status}, address {reply.Address}");
                //    }
                //    else
                //    {
                //        //BILog.UserFte(
                //                  //$"loading_bug7",
                //                  //$"2", new JsonBIObj("args", gas.ToString())
                //                  //);
                //        //BILogInfo.UpdateFTEStageParameter($"GSAutoLogin: net ping is null {gate_addr}");
                //        //LoadingLog.AddLoadingBILog($"GSAutoLogin: net ping is null {gate_addr}");
                //    }
                //}
            }
        }

        #endregion

        #region FastLogin快速登录
#if UNITY_EDITOR
        /// <summary>
        /// 快速登录
        /// </summary>
        /// <returns></returns>
        FastLoginReq MakeFastLoginReq()
        {
            var req = new FastLoginReq();
            req.secret = "open the door";
            if (Loading_Res.Ins.FastLoginServerId > 0)
            {
                req.serverID = Loading_Res.Ins.FastLoginServerId;
            }
            else
            {
                req.serverID = m_LoginData.characterData.serverid;
            }
            req.playerID = Loading_Res.Ins.FastLoginPlayerId;
            req.name = "";
            req.cInfo = LoginMgr.I.GenerateClientInfo();
            return req;
        }
#endif
#endregion

        #region 处理找不到服务器情况

        /// <summary>
        /// 处理找不到服务器的情况
        /// 如果找不到服务器，那么就进入推荐服（如果推荐服有角色，那么就是登陆，没有角色就新建）
        /// </summary>
        private IMessage HandleCanNotFindServerId()
        {
            //FPS.Instance.CheckGameCaton("GSAutoLogin=====672");
            //查找当前推荐服是否有数据

            //获取所有的角色列表
            var list = m_LoginData.authAck.characters;
            //获取推荐服Id
            var recommandServerId = m_LoginData.authAck.recommandServer;
            for (int i = 0; i < list.Count; i++)
            {
                if (list[i].serverID == recommandServerId)
                {
                    //推荐服上有数据，那么登录推荐服
                    return LoginRole(list[i]);
                }
            }

            //BILogInfo.UpdateFTEStageParameter("GSAutoLogin: CreateNewRole HandleCanNotFindServer!");
            //LoadingLog.AddLoadingBILog("GSAutoLogin: CreateNewRole HandleCanNotFindServer!");
            //推荐服上没有数据，那么就在推荐服上新建角色
            var message = CreateNewRole(recommandServerId);
            isFirstLogin = true;

            //FPS.Instance.CheckGameCaton("GSAutoLogin=====692");
            return message;
        }


        #endregion

        #endregion

        #region 登录返回

        /// <summary>
        /// 登录返回
        /// </summary>
        /// <param name="ack"></param>
        private async UniTaskVoid OnLoginAck(LoginAck ack)
        {
           
            D.Debug?.Log($"Load Table: Login Ack");

            m_LoginAck = ack;

            isLoginSuccess = ack.errCode == ErrCode.ErrCodeSuccess;
            checkTimeOut = ack.errCode != ErrCode.ErrCodeSuccess;

            //ClientLogMgr.AddClientLogInfo($"LoginResult:{ack.errCode}");
            if (isLoginSuccess)
            {
                m_LastServerID = ack.serverId;
                LoginMgr.SaveLastLoginServerId(m_LastServerID);
                LoginMgr.I.loginData.playerid = ack.playerID;
                LoginMgr.I.loginData.serverid = ack.serverId;

                ThinkingAnalytics.ThinkingAnalyticsAPI.Login(ack.playerID.ToString());//用角色ID 作为账号ID
            }
            else
            {
                D.Warning?.Log(string.Format("Failed to login errorCode:{0} serverID:{1}  serverName:{2}", ack.errCode, LoginMgr.I.loginData.serverid, LoginMgr.I.loginData.servername));
            }

            //已经处理完了，那么就返回，不在处理
            if (Prepared)
            {
                //ClientLogMgr.AddClientLogInfo("LoginSucceedReturn");
                return;
            }


            //如果当前已经显示了提示信息框，那么关闭掉
            PopupManager.I.ClosePopup<UILoginTipsDialog>();

            Handle(TEventType.CharacterLogin, isLoginSuccess, m_LoginAck.errCode, m_LoginAck);

            //登录成功保存角色信息
            if (m_LoginData.characterData == null)
            {
                m_LoginData.characterData = LoginMgr.I.CreatCharacterByLoginData();
            }
            //如果是角色行为动作则完善角色信息
            else if (m_LoginData.characterData.action > CharacterAction.None)
            {
                LoginMgr.I.PerfectCharacterInfo(m_LoginData.characterData);
            }

            //初始化账号相关信息
            LoginMgr.I.InitAccountInfo(m_LoginData);
            LoginMgr.I.UpdateSessionID(m_LoginAck.sessionId);

            Loading_Res.CurLoadingProcess = LogoLoadingProcessEnum.GSDataFlow;
            await GameDataFlowMgr.I.OnLoginSucceed(m_LoginAck);
            
            while (!GameDataFlowMgr.I.IsPlayerInfoAck || !K3PlayerMgr.I.PlayerInfoReceived || MergeTaskMgr.I.AllMergeTask.Count==0)
            {
                //D.Info?.Log($"Waiting  Data~");
                await UniTask.Yield();
            }

            //需要等待 玩家必须的数据后 进行 更新
            //ClientLogMgr.AddClientLogInfo(ClientLogType.EnterGSGamePlay);
            Prepared = true;

            GSSwitchMgr.AddStage(new GSGamePlay());

            using (new LaunchTimeLogger("InvokeCallbacksOnLogin"))
            {
                await CallbackInvoker.InvokeCallbacksOnLogin();
            }
             
            using (new LaunchTimeLogger("InvokeCallbacksAfterSceneLoad"))
            {
                await CallbackInvoker.InvokeCallbacksAfterSceneLoad();
            }
        }

        


        #endregion

    }
}
