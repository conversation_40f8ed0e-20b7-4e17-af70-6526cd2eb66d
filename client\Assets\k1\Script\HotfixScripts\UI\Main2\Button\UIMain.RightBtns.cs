﻿using Common;
using cspb;
using DeepUI;
using DG.Tweening;
using Game.Config;
using Game.Data;
using K3;
using Logic;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

namespace UI
{
    /// <summary>
    /// 主界面右下一堆按钮
    /// </summary>
    public partial class UIMain2
    {

        #region 属性字段信息

        //右边一堆按钮列表
        [PopupField("content/RightBtns")]
        private GameObject rightBtnsRoot;

        //需要刷新的列表
        //[PopupField("content/RightBtns/Top")]
        //private RectTransform topRect;
        //[PopupField("content/RightBtns/Bottom/Btns")]
        //private RectTransform bottomBtnsRect;
        //[PopupField("content/RightBtns/Bottom")]
        //private RectTransform bottomRect;
        //[PopupField("content/RightBtns/BottomArrow")]
        //private GameObject bottomArrowObj;
        
        [PopupField("content/RightBtns/BuffBtn/Button")]
        private GameObject buffBtn;

        [PopupField("content/RightBtns/BuffBtn/Button/bg")]
        private GameObject buffRedObj;

        [PopupField("content/RightBtns/StargazingPlatform/Button")]
        private GameObject StargazingPlatform;

        //[PopupField("content/RightBtns/Space")]
        //private GameObject SpaceObj;//占位

        /// <summary>
        /// 在按钮列表中是否有按钮开放
        /// </summary>
        private bool _isHaveRightBtnOpen;
        /// <summary>
        /// 按钮列表中是否有按钮开放
        /// </summary>
        private bool IsHaveRightBtnOpen
        {
            get { return _isHaveRightBtnOpen; }
            set { _isHaveRightBtnOpen = value; }
        }

        #endregion


        #region 初始化

        /// <summary>
        /// 初始化按钮
        /// </summary>
        private void InitRightBtnsButton()
        {
            _isHaveRightBtnOpen = false;

            OnUpdateRightBtnsAllRedNum(null);
            
            UIBase.AddRemoveListener(TFW.EventTriggerType.Click, buffBtn, (x, y) =>
            {
                PopupManager.I.ShowPanel<UIPlayerNewBuffs>();
            });

            // buffBtn.transform.parent.gameObject.SetActive(true);
            buffRedObj.SetActive(false);

            NewBuffRefersh(null);
            
            // 
            BindClickListener(StargazingPlatform, OnClickStargazingPlatformBtnBtn);
            UpdateStargazingPlatformBtn(null);
            CheckStargazingPlatformUnlock();
            UnlockMgr.I.OnUnlockConditionUpdate += CheckStargazingPlatformUnlock;
        }


        /// <summary>
        /// 反向初始化按钮
        /// </summary>
        private void UnInitRightBtnsButton()
        {
            _isHaveRightBtnOpen = false;
            UnRechargeStargazingPlatformUnlock();
        }

        #endregion


        #region 数据刷新

        /// <summary>
        /// 第一次进入游戏
        /// </summary>
        public void OnOpenStartRightBtnsList()
        {
            UpdateRightBtnsList();

            //红点事件
            RegisterAllRightBtnRed();
        }

        /// <summary>
        /// 列表和总红点刷新
        /// </summary>
        private void UpdateRightBtnsList()
        {

            //LayoutRebuilder.ForceRebuildLayoutImmediate(bottomBtnsRect);
            //LayoutRebuilder.ForceRebuildLayoutImmediate(topRect);
            //LayoutRebuilder.ForceRebuildLayoutImmediate(bottomRect);

            //刷新右侧按钮红点个数
            OnUpdateRightBtnsAllRedNum(null);

            //EventMgr.FireEvent(TEventType.AllRightBtnsRed);
        }

        #endregion


        #region 事件监听

        /// <summary>
        /// 刷新主界面代替显示按钮开放状态
        /// 由于常驻的聊天按钮在44关才开放，44关前暂由最先解锁的任务按钮代替显示
        /// </summary>
        private void UpdateShowBtnOpenState()
        {
            var isOpenTaskBtn = true;// GameData.I.LevelData.CanToDoByGameLevel(MetaConfig.ChapterQuestOpenLV, false);

            //列表是否显示根据第一个解锁的关卡走
            if (GameData.I.MainData.CurrMenuType == MainMenuType.CITY
                || GameData.I.MainData.CurrMenuType == MainMenuType.WORLD
               )// || GameData.I.MainData.CurrMenuType == MainMenuType.PRISON
                rightBtnsRoot?.SetActive(isOpenTaskBtn);

            //if (bottomArrowObj != null && bottomArrowObj.activeSelf != IsHaveRightBtnOpen)
            //    bottomArrowObj.SetActive(IsHaveRightBtnOpen);
            //if (GameLevelManager.I.curGameType == (int)GameLevelManager.GameType.TimeTrial)
            //{
            //    rightBtnsRoot?.SetActive(false);
            //}
        }
        
        
        private void OnClickStargazingPlatformBtnBtn(GameObject arg0, PointerEventData arg1)
        {
            DeepUI.PopupManager.I.ShowLayer<UIStargazingPlatform>();
        }
        
        private void CheckStargazingPlatformUnlock()
        {
            var result = UnlockMgr.I.CheckUnlock(UnlockFuncEnum.Building_5);
            D.Info?.Log($"观星台解锁条件： = unlock = {result}");
            StargazingPlatform.SetActive(result);
        }
        
        private Sequence _Sequence = null;
        [PopupEvent(TEventType.TaskToComplete)]
        public void UpdateStargazingPlatformBtn(object[] objs)
        {
            CheckStargazingPlatformUnlock();
            
            if (LIntelligence.I.DicMission.Count <= 0)
            {
                _Sequence.Kill();
                StargazingPlatform.transform.localScale = Vector3.one;
                _Sequence = null;

                return;
            }

            if (_Sequence == null)
            {
                _Sequence = DOTween.Sequence();
                _Sequence.Append(StargazingPlatform.transform.Find("Button/Icon")
                                                   .DOScale(new Vector3(1.1f, 1.1f, 1), 0.4f).SetLoops(4, LoopType.Yoyo));
                _Sequence.AppendInterval(1f);
                _Sequence.SetLoops(-1);
            }
        }
        
        private void UnRechargeStargazingPlatformUnlock()
        {
            UnlockMgr.I.OnUnlockConditionUpdate -= CheckStargazingPlatformUnlock;
        }


        /// <summary>
        /// 刷新总红点数量
        /// </summary>
        /// <param name="objs"></param>
        
        [PopupEvent(TEventType.AllRightBtnsRed)]
        
        private void OnUpdateRightBtnsAllRedNum(object[] objs)
        {
            var count = 0;

            if(LSkill.I.GetMainCityLevel()>=MetaConfig.NewUnlockMailBtn)
            {
                if (LMailMgr.I.IsUseNewMail)
                    count += LMailNetWork.I.AllUnReadCount;
                else
                    count += GameData.I.MainData.MailRedCount;
            }
        }
  
        /// <summary>
        /// 注册红点刷新事件
        /// </summary>
        public void RegisterAllRightBtnRed()
        {
            EventMgr.RegisterEvent(TEventType.AllRightBtnsRed, OnUpdateRightBtnsAllRedNum, this);
        }

        private List<NewBuff> BuffList,DeBuffList;

        [PopupEvent(TEventType.UpdateNewBuffs)]
        private async void NewBuffRefersh(object[] objs)
        {
            if (BuffList == null)
                BuffList = new List<NewBuff>();
            if (DeBuffList == null)
                DeBuffList = new List<NewBuff>();

            await LPlayerProp.I.GetNewDebuffs(BuffList);
            await LPlayerProp.I.GetNewBuffs(DeBuffList);

            // buffBtn.transform.parent.gameObject.SetActive(BuffList.Count > 0 || DeBuffList.Count > 0);
        }

        #endregion

    }
}
