﻿using System.Collections.Generic;
using TFW;
using TFW.Localization;
using TFW.UI;
using UnityEngine;
using UnityEngine.UI;

namespace UI
{
    public partial class UITechViewSub1Item : TFWLoopListViewItem
    {
        private int mStage;
        private UITechStageSub1Data mData;
        private List<UITechViewSub2Item> mSub2Items = new List<UITechViewSub2Item>();

        private Dictionary<int, string> mStageNumColors = new Dictionary<int, string>()
        {
            {1, "#B4D6E9"},
            {2, "#B1FE73"},
            {3, "#60F8FE"},
            {4, "#EC9EFA"},
            {5, "#FECE63"},
            {6, "#FECE63"},
            {7, "#FECE63"},
            {8, "#FECE63"},
            {9, "#FECE63"},
            {10, "#FECE63"},
            {11, "#FECE63"},
            {12, "#FECE63"},
        };

        private Dictionary<int, Color> mStageProcessColors = new Dictionary<int, Color>()
        {
            {1, new Color(139f/255,178f/255,200f/255,1)},
            {2, new Color(41f/255,207f/255,132f/255,1)},
            {3,new Color(67f/255,186f/255,255f/255,1)},
            {4, new Color(175f/255,89f/255,216f/255,1)},
            {5, new Color(238f/255,169f/255,48f/255,1)},
            {6, new Color(238f/255,169f/255,48f/255,1)},
            {7, new Color(238f/255,169f/255,48f/255,1)},
            {8, new Color(238f/255,169f/255,48f/255,1)},
            {9,new Color(238f/255,169f/255,48f/255,1)},
            {10, new Color(238f/255,169f/255,48f/255,1)},
            {11,new Color(238f/255,169f/255,48f/255,1)},
            {12, new Color(238f/255,169f/255,48f/255,1)},
        };

        private Dictionary<int, string> mStageBgs = new Dictionary<int, string>()
        {
            {1, "UI_Tech_StageBg_05"},
            {2, "UI_Tech_StageBg_04"},
            {3, "UI_Tech_StageBg_03"},
            {4, "UI_Tech_StageBg_02"},
            {5, "UI_Tech_StageBg_01"},
            {6, "UI_Tech_StageBg_01"},
            {7, "UI_Tech_StageBg_01"},
            {8, "UI_Tech_StageBg_01"},
            {9, "UI_Tech_StageBg_01"},
            {10, "UI_Tech_StageBg_01"},
            {11, "UI_Tech_StageBg_01"},
            {12, "UI_Tech_StageBg_01"},
        };
        
        //private Dictionary<int, string> mStageNumBgs = new Dictionary<int, string>()
        //{
        //    {1, "UI_Tech_StageBg_01"},
        //    {2, "UI_Tech_StageBg_01"},
        //    {3, "UI_Tech_StageBg_01"},
        //    {4, "UI_Tech_StageBg_01"},
        //    {5, "UI_Tech_StageBg_01"},
        //    {6, "UI_Tech_StageBg_01"},
        //    {7, "UI_Tech_StageBg_01"},
        //    {8, "UI_Tech_StageBg_01"},
        //    {9, "UI_Tech_StageBg_01"},
        //    {10, "UI_Tech_StageBg_01"},
        //    {11, "UI_Tech_StageBg_01"},
        //    {12, "UI_Tech_StageBg_01"},
        //};
        
        public void Init()
        {
            AutoBindComponent();

            UI.UIBase.AddRemoveListener(TFW.EventTriggerType.Click, this.gameObject, (x, y) =>
            {
                OnBtn();
            });
        }

        private void OnBtn()
        {
            UITechViewData.I.ClickSub1ItemCallback?.Invoke(this.mData);
            
            RefreshDisplay();
        }

        public void UnSelect()
        {
            foreach (var sub2Item in mSub2Items)
            {
                sub2Item.UnSelect();
            }
        }

        public void SetData(UITechStageSub1Data sub1Data)
        {
            SetData(sub1Data.TechTab, sub1Data);
        }

        public void SetData(int stage, UITechStageSub1Data sub1Data)
        {
            mStage = stage;
            mData = sub1Data;

            mLock.SetActive(mData.IsLock());

            //var stageNumBgStr = GetStageNumBgStr();
            //UITools.SetDynamicRawImage(mStageImg_TFWRawImage, $"Assets/K3/Res/Art/Textures/Tech/{stageNumBgStr}.png");

            var stageBgStr = GetStageBgStr();
            UITools.SetDynamicRawImage(mStageImg_TFWRawImage, $"Assets/K3/Res/Art/Textures/Tech/{stageBgStr}.png");

            var colorStr = GetColorStr();
            mStageNum_TFWText.text = LocalizationMgr.Format(LocalizationMgr.Get("talent_tab1"), $"<color={colorStr}>{mStage}</color>");

            mStageName_TFWText.text = LocalizationMgr.Get("Tech_Tab_Name_" + mStage);

            var progress = mData.GetProgress();
            mProgressImg_TFWImage.fillAmount = progress;
            mProgressTxt_TFWText.text = progress.ToString("P1"); 
            mProgressImg_TFWImage.color= GetColorProcess();



            RefreshArr();
        }

        private void RefreshArr()
        {
            if (UITechViewData.I.IsTechTabOpen(mStage))
            {
                UITools.SetImageBySpriteName(mStageArr_TFWImage, "UI_Common_Btn_Arrow_03");
            }
            else
            {
                UITools.SetImageBySpriteName(mStageArr_TFWImage, "UI_Common_Btn_Arrow_04");
            }
        }
        
        //private string GetStageNumBgStr()
        //{
        //    if (mStageNumBgs.ContainsKey(mStage))
        //    {
        //        return mStageNumBgs[mStage];
        //    }

        //    return mStageNumBgs[1];
        //}

        private string GetStageBgStr()
        {
            if (mStageBgs.ContainsKey(mStage))
            {
                return mStageBgs[mStage];
            }

            return mStageBgs[1];
        }
        
        private string GetColorStr()
        {
            if (mStageNumColors.ContainsKey(mStage))
            {
                return mStageNumColors[mStage];
            }

            return mStageNumColors[1];
        }

        private Color GetColorProcess()
        {
            if (mStageProcessColors.ContainsKey(mStage))
            {
                return mStageProcessColors[mStage];
            }

            return mStageProcessColors[0];
        }

        private void RefreshDisplay()
        {
            RefreshArr();
        }

        public void Close()
        {
            RefreshDisplay();
        }

        public void Open()
        {
            RefreshDisplay();
        }
    }
    
    
    public partial class UITechViewSub1Item
    {
        protected UnityEngine.GameObject GameObject => this.gameObject;
        
        
        
        
        
        
        
        
        
        protected UnityEngine.GameObject mStageImg;

        protected TFW.UI.TFWRawImage mStageImg_TFWRawImage;

        protected UnityEngine.GameObject mLock;

        //protected TFW.UI.TFWRawImage mLock_TFWRawImage;

        //protected UnityEngine.GameObject mStageNumBg;

        //protected TFW.UI.TFWRawImage mStageNumBg_TFWRawImage;

        protected UnityEngine.GameObject mStageNum;

        protected TFW.UI.TFWText mStageNum_TFWText;

        protected UnityEngine.GameObject mStageName;

        protected TFW.UI.TFWText mStageName_TFWText;

        protected UnityEngine.GameObject mStageArr;

        protected TFW.UI.TFWImage mStageArr_TFWImage;

        protected UnityEngine.GameObject mProgressImg;

        protected TFW.UI.TFWImage mProgressImg_TFWImage;

        protected UnityEngine.GameObject mProgressTxt;

        protected TFW.UI.TFWText mProgressTxt_TFWText;

        protected UnityEngine.GameObject mBtn;

        protected UnityEngine.GameObject mContent;

        protected UIGrid mContent_UIGrid;

        protected void AutoBindComponent()
        {
            mStageImg = this.GameObject.transform.Find("Root/BG").gameObject;
            mStageImg_TFWRawImage = mStageImg.GetComponent<TFW.UI.TFWRawImage>();

            mLock = this.GameObject.transform.Find("Root/BG/mStageImg/mLock").gameObject;
            //mLock_TFWRawImage = mLock.GetComponent<TFW.UI.TFWRawImage>();

            //mStageNumBg = this.GameObject.transform.Find("Root/BG/mStageNumBg").gameObject;
            //mStageNumBg_TFWRawImage = mStageNumBg.GetComponent<TFW.UI.TFWRawImage>();

            mStageNum = this.GameObject.transform.Find("Root/BG/mStageNumBg/mStageNum").gameObject;
            mStageNum_TFWText = mStageNum.GetComponent<TFW.UI.TFWText>();

            mStageName = this.GameObject.transform.Find("Root/BG/mStageName").gameObject;
            mStageName_TFWText = mStageName.GetComponent<TFW.UI.TFWText>();

            mStageArr = this.GameObject.transform.Find("Root/BG/mStageArr").gameObject;
            mStageArr_TFWImage = mStageArr.GetComponent<TFW.UI.TFWImage>();

            mProgressImg = this.GameObject.transform.Find("Root/BG/progress/mProgressImg").gameObject;
            mProgressImg_TFWImage = mProgressImg.GetComponent<TFW.UI.TFWImage>();

            mProgressTxt = this.GameObject.transform.Find("Root/BG/progress/mProgressTxt").gameObject;
            mProgressTxt_TFWText = mProgressTxt.GetComponent<TFW.UI.TFWText>();

            mBtn = this.GameObject.transform.Find("Root/BG/mBtn").gameObject;
            mContent = this.GameObject.transform.Find("Root/mContent").gameObject;
            mContent_UIGrid = mContent.GetComponent<UIGrid>();
        }
    }
}