﻿








using Common;
using GameState;
using K1;
using Logic;
using Public;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using DeepUI;
using TFW;
using UnityEngine;
using Game.Config;
using Game.Data;
using Cysharp.Threading.Tasks;

namespace UI
{
    public class WndMgr
    {
        //private static readonly string s_CommonBluredBackgroundPath = "Assets/K1/K1D1/Res/UI/Panel/Misc/CommonBluredBackground.prefab";
        //private static UICommonBluredBackground s_CommonBluredBackground;

        static Dictionary<string, UIBase> UIs = new Dictionary<string, UIBase>();
        static List<string> UIsn = new List<string>();
        static Dictionary<string, UIBase> ProxyUIs = new Dictionary<string, UIBase>();

        static int s_Order;
        //public static int addOrderOffset = 300;
        //public static int Order
        //{
        //    get
        //    {
        //        s_Order = s_Order + addOrderOffset;
        //        return s_Order;
        //    }
        //}

        /// <summary>
        /// 重置 Order
        /// </summary> 
        public static void ResetOrder()
        {
            s_Order = 1000;
        }

        /// <summary>
        /// 获取Order
        /// </summary>
        /// <param name="offset"></param>
        /// <returns></returns>
        public static int GetOrder(int offset)
        {
            s_Order = s_Order + offset;
            //D.Error?.Log("==========GetOrder============" + s_Order);
            return s_Order;
        }

        /// <summary>
        /// 设置Order
        /// </summary>
        /// <param name="order"></param>
        public static void SetOrder(int order)
        {
            if (order < 0)
                return;
            s_Order = order;
            //D.Error?.Log("==========SetOrder============" + s_Order);
        }

        private static List<System.Type> igonreList = new List<Type>()
        {
            typeof(UIMsgBox),
            typeof(UIShop),
            // typeof(UIPhysicalPower),
            // typeof(UIMerage),
            //typeof(UIHeroRecruit),
            // typeof(UIHero_Cure),
            //typeof(UIGetAssetPop),
            //typeof(UIGetPropPop),
            typeof(UIComplementPop),
            typeof(Mail.WriteMailNew),
            //typeof(UIHero_GetHero),
            // typeof(UIAdEventPop),
        };

        private static List<System.Type> clearList = new List<Type>()
        {
            typeof(UIBubbleTipNew),
        };

        //private static void OnApplicationResume(params object[] _)
        //{
        //    D.Warning?.Log("切换后台");
        //    //if (MiniGame.MiniGameUtils.IsPlay())
        //    //    MiniGame.MiniGameUtils.EndMiniGame();

        //    //if (FTESettingMgr.m_IsInNewbieFTE)
        //    //    return;
        //    //System.Reflection.met

        //    //if (AdTimingMgr.I.adState)
        //    //{
        //    //    D.Warning?.Log("看广告后回到游戏中");
        //    //    //切换广告不属于切后台
        //    //    AdTimingMgr.I.adState = false;
        //    //    return;
        //    //}

        //    if (AdMgr.I.IsPlayingAd)
        //    {
        //        D.Warning?.Log("看广告后回到游戏中");
        //        //切换广告不属于切后台
        //        AdMgr.I.IsPlayingAd = false;
        //        return;
        //    }

        //    foreach (var type in igonreList)
        //    {
        //        if (PopupManager.I.FindPopup(type) != null)
        //            return;

        //        var ui = Get<UIBase>(type.Name);
        //        if (ui != null && ui.IsShow)
        //            return;
        //    }

        //    // 从后台切回来后，关闭所有栈中页面。
        //    if (GSSwitchMgr.IsCurrentStageGamePlay())
        //    {

        //        HideAll(true);
        //        foreach (var type in clearList)
        //        {
        //            if (PopupManager.I.FindPopup(type) == null)
        //                continue;

        //            PopupManager.I.ClosePopup(type);
        //        }

        //        if (WorldSwitchMgr.I.ShowWorldType == WorldTypeEnum.CITY)
        //        {
        //            //需要切回菜单
        //            //UIMain.I?.TrySwitch(MainMenuConst.TRAIN);
        //            EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.CITY);
        //        }
        //        else
        //        {
        //            //需要切回菜单
        //            //UIMain.I?.TrySwitch(MainMenuConst.WORLD);
        //            EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.WORLD);
        //        }

        //        //if (UIMain.I != null)
        //        //{
        //        //    // EventMgr.FireEvent(TEventType.ShowUI, typeof(UIMain).Name, UIMain.I);
        //        //}
        //    }
        //}

        //public static bool IsAnyFullSceenPopup()
        //{
        //    foreach (var s in UIsn)
        //    {
        //        UIs.TryGetValue(s, out var popup);
        //        if (popup != null && popup.LFullScreen)
        //        {
        //            return true;
        //        }
        //    }

        //    return false;
        //}

        private static void DoHide(UIBase menu, string name, bool isPop = false)
        {
            if (menu == null)
                return;

            UIsn.Remove(name);

            if (menu.UICahceType == EUIResCacheTypeEnum.IMMEDIATELY)
                UIs.Remove(name);

            if (menu.LFullScreen)
            {
                //临时修复双指操作UI后主界面底部菜单问题
                //for (var i = UIsn.Count - 1; i >= 0; i--)
                //{
                //    var str = UIsn[i];
                //    var ui = Get(str);
                //    if (ui == null)
                //        continue;
                //    if (ui.LFullScreen)
                //    {
                //        if (ui.gameObject == null)
                //            continue;

                //        var cg = ui.gameObject.GetComponent<CanvasGroup>();
                //        if (cg == null)
                //        {
                //            D.Error?.Log(string.Format("{0} should contains canvas group", ui.Name));
                //            continue;
                //        }
                //        cg.alpha = 1;
                //        cg.blocksRaycasts = true;
                //        break;
                //    }
                //}
            }

            //D.Debug?.Log($"hideUI {name}");

            menu.Hide();

            if (isPop)
                menu.Pop();

            menu.Destroy();

            //if (UIsn.Count == 0)
            //    s_CommonBluredBackground.Hide();
            //else
            //{
            //    bool needHide = true;
            //    for (int i = 0; i < UIsn.Count; ++i)
            //    {
            //        if (IsMenuNeedBluredBackground(UIs[UIsn[i]].PopPanelType))
            //        {
            //            needHide = false;
            //            break;
            //        }
            //    }
            //    if (needHide)
            //    {
            //        s_CommonBluredBackground.Hide();
            //    }
            //}

            //ShowPrevious(menu);
            EventMgr.FireEvent(TEventType.HideUI, name, menu);
        }

        //private static bool IsMenuNeedBluredBackground(EPopPanelType panelType)
        //{
        //    return panelType != EPopPanelType.PopUp &&
        //        panelType != EPopPanelType.Bubble &&
        //        panelType != EPopPanelType.PopupNoMask &&
        //        panelType != EPopPanelType.CanDragBlockClick;
        //}

        /// <summary>
        /// 老版本UI控制初始化
        /// </summary>
        public static void Initialize()
        {
            //重置 Order
            ResetOrder();
            //MessageMgr.OnBeforeNotify += UIHelper.LocalizeAckErrorCodeAndShowItByFloatTips;
            //添加消息错误码信息检测
            MessageMgr.OnBeforeNotify += LErrorCodeMgr.I.AckErrorCodeShowFloatTips;


            //// 创建所有UI的通用3D世界模糊背景
            //var go = ResourceMgr.LoadGameObject(s_CommonBluredBackgroundPath);

            //var canvas = go.GetComponent<Canvas>();

            //canvas.sortingOrder = (int)ESortingOrder.BlurdBackground;
            //go.transform.SetParent(UIMgr.UIRoot.transform);
            //s_CommonBluredBackground = go.GetComponent<UICommonBluredBackground>();

            //canvas.renderMode = RenderMode.ScreenSpaceCamera;
            //canvas.worldCamera = Game.Config.GameInstance.UICamera;

            //EventMgr.RegisterEvent(TEventType.ApplicationResume, OnApplicationResume, "WndMgr");

            //TFW.GameQuality.QualityChanged += UIBlur.ResizeRT;
        }

        //public static T Show<T>(string name = "", UIData data = null) where T : UIBase, new()
        //{
        //    name = Name<T>(name);
        //    Hide<T>(name);
        //    var u = new T();
        //    /*if (!u.IsShowPrevious)
        //    {
        //        for (int counter = UIsn.Count - 1; counter >= 0; counter--)
        //        {
        //            var value = UIs[UIsn[counter]];
        //            value.Hide();
        //            if (!value.IsShowPrevious)
        //            {
        //                break;
        //            }
        //        }
        //    }*/

        //    if (UIsn.Count > 0)
        //    {
        //        var topMost = UIs[UIsn[UIsn.Count - 1]];
        //        if (topMost.PopPanelType == EPopPanelType.PopUpExclusive)
        //        {
        //            Hide(topMost.Name);
        //        }
        //    }


        //    //if (IsMenuNeedBluredBackground(u.PopPanelType))
        //    //{
        //    //    s_CommonBluredBackground.Show(true, () =>
        //    //    {
        //    //        u.Initialize(name, data);
        //    //        u.Show();
        //    //    });
        //    //}
        //    //else
        //    //{
        //        u.Initialize(name, data);
        //        u.Show();
        //    //}

        //    if (u.LFullScreen)
        //    {
        //        for (var i = UIsn.Count - 1; i >= 0; i--)
        //        {
        //            var str = UIsn[i];
        //            var ui = Get(str);
        //            if (ui == null || ui.gameObject == null)
        //                continue;

        //            if (ui.Name == "UIMain")
        //                break;

        //            ////临时修复双指操作UI后主界面底部菜单问题
        //            if (ui.LFullScreen)
        //            {
        //                //zjw 如果不调用DoHide 没有执行事件  EventMgr.FireEvent(TEventType.HideUI, name, menu);
        //                DoHide(ui, ui.Name);

        //                //UIsn.Remove(ui.Name);
        //                //UIs.Remove(ui.Name);
        //                //ui.Hide();
        //                //ui.Destroy();
        //                break;
        //            }
        //            //if (ui.LFullScreen)
        //            //{
        //            //    var cg = ui.gameObject.GetComponent<CanvasGroup>();
        //            //    if (cg == null)
        //            //    {
        //            //        D.Error?.Log(string.Format("{0} should contains canvas group", ui.Name));
        //            //        continue;
        //            //    }
        //            //    cg.alpha = 0;
        //            //    cg.blocksRaycasts = false;
        //            //    break;
        //            //}
        //        }
        //    }

        //    UIs.Add(name, u);
        //    UIsn.Add(name);

        //    EventMgr.FireEvent(TEventType.ShowUI, name, u);
        //    return u;
        //}

        public static UniTask<T> ShowAsync<T>(UIData data) where T : UIBase, new()
        {
            return ShowAsync<T>(string.Empty, data);
        }

        public static UniTask<T> ShowAsync<T>(string name = "", UIData data = null) where T : UIBase, new()
        {
            var utcs = new UniTaskCompletionSource<T>();

            var originalCallback = data?.onShown;

            //ReconnectManager.I.ShowNetReconnectMark(true);
            void Hook(UIBase ui)
            {
                originalCallback?.Invoke(ui);
                utcs.TrySetResult(ui as T);
                //ReconnectManager.I.ShowNetReconnectMark(false);
            }

            var d = data ?? new UIData();
            d.onShown = Hook;

            Show<T>(name, d);

            return utcs.Task;
        }

        public static T Show<T>(string name = "", UIData data = null) where T : UIBase, new()
        {

            name = Name<T>(name);
            var u = Get<T>(name);

            //D.Debug?.Log($"showUI {name}");

            //已经有了，那就直接打开
            if (u != null)
            {
                if (u.IsShow)
                {
                    u.ResetInitData(data);
                    u.Show();
                    EventMgr.FireEvent(TEventType.ShowUI, name, u);
                    return u;
                }

                u.ResetInitData(data);
                u.Show();

                UIsn.Add(name);
                EventMgr.FireEvent(TEventType.ShowUI, name, u);
                return u;
            }
            else
            {
                u = new T();
            }

            if (UIsn.Count > 0)
            {
                var panelName = UIsn[UIsn.Count - 1];
                if (!string.IsNullOrEmpty(panelName))
                {
                    if (UIs.TryGetValue(panelName, out var topMost))
                    {
                        //var topMost = UIs[UIsn[UIsn.Count - 1]];
                        if (topMost.PopPanelType == EPopPanelType.PopUpExclusive)
                        {
                            Hide(topMost.Name);
                        }
                    }
                }
            }

            u.Initialize(name, data);
            if (!u.IsShow)
            {
                u.Show();
            }

            UIs.Add(name, u);
            UIsn.Add(name);

            EventMgr.FireEvent(TEventType.ShowUI, name, u);
            return u;
        }

        private static List<Type> filterUI = new List<Type>()
        {
            //typeof(FlyTowerPanel),
            typeof(GoldFly),
            //typeof(TreasureChestFly),
            typeof(NewTreasureChestFly),
            //typeof(BattleWinGetTechFly),
            typeof(HurtFlyText),
            typeof(UIFloatTips),
            typeof(UIFloatingDamages),
            typeof(BossRedPanel),
            typeof(UICreateTroop),
            typeof(UIUnionMoveCityTips),
            typeof(UIUnionInviteTips),
            //typeof(UILevelTips),
            //typeof(UnLockTipsPanel)
        };

        //public static int FindPopupCountExceptType<T>() where T : UIBase
        //{
        //    if (UIsn == null)
        //        return 0;

        //    var count = 0;
        //    var type = typeof(T);
        //    foreach (var ui in UIs)
        //    {
        //        if (filterUI.Contains(type))
        //            continue;

        //        if (ui.Value == null)
        //            continue;

        //        if (ui.Value.Name == Name<T>(""))  //T类型除外
        //            continue;

        //        bool filter = false;
        //        foreach (var fi in filterUI)
        //        {
        //            if (fi.Name == ui.Value.Name)
        //            {
        //                filter = true;
        //                break;
        //            }
        //        }

        //        if (filter)
        //            continue;

        //        if (ui.Value.IsShow)
        //        {
        //            //D.Error?.Log($"{ui.Key}");
        //            count++;
        //        }
        //    }
        //    return count;
        //}

        public static int FindPopupCountExceptType()
        {
            if (UIsn == null)
                return 0;

            var count = 0;
            foreach (var ui in UIs)
            {
                if (ui.Value == null)
                    continue;

                bool filter = false;
                foreach (var fi in filterUI)
                {
                    if (fi == ui.Value.GetType())
                    {
                        filter = true;
                        break;
                    }
                }

                if (filter)
                    continue;

                if (ui.Value.IsShow)
                {
                    //D.Warning?.Log($"{ui.Key}");
                    count++;
                }
            }
            return count;
        }


        public static T Show<T>(UIData data) where T : UIBase, new()
        {
            return Show<T>(string.Empty, data);
        }

        public static string Name<T>(string name)
        {
            if (string.IsNullOrEmpty(name))
            {
                name = typeof(T).Name;//FullName
            }

            return name;
        }

        //public static void Show(Type type, UIData args)
        //{

        //}

        public static T Hide<T>(string name = "") where T : UIBase
        {
            name = Name<T>(name);
            var u = Get<T>(name);
            if (u == null) return null;

            DoHide(u, name);

            return u;
        }

        public static bool Hide(string name = "")
        {

            if (UIsn.Count == 0)
            {
                D.Debug?.Log($"HideUI:{name} Error Count==0");
                return false;
            }

            if (string.IsNullOrEmpty(name))
                name = UIsn[UIsn.Count - 1];
            var u = Get(name);
            if (u == null)
            {
                D.Debug?.Log($"HideUI:{name} Error NotFind");
                return false;
            }

            DoHide(u, name);

            return true;
        }

        /// <summary>
        /// 获取显示ui的数量
        /// </summary>
        /// <returns></returns>
        public static int GetShowUICount()
        {
            if (UIsn != null)
            {
                return UIsn.Count;
            }
            return 0;
        }


        //static List<UIBase> showUIs = new List<UIBase>();
        //public static List<UIBase> GetShowUIs()
        //{
        //    showUIs.Clear();
        //    foreach (var item in UIs)
        //    {
        //        if (item.Value != null && item.Value.Showed)
        //        {
        //            showUIs.Add(item.Value);
        //        }
        //    }

        //    foreach (var item in ProxyUIs)
        //    {
        //        if (item.Value != null && item.Value.IsShow)
        //        {
        //            showUIs.Add(item.Value);
        //        }
        //    }

        //    return showUIs;
        //}


        //public static List<string> GetShowUIName()
        //{
        //    if (UIsn != null)
        //    {
        //        return UIsn;
        //    }
        //    return null;
        //}

        /// <summary>
        /// 获取所有显示的ui
        /// </summary>
        /// <returns></returns>
        public static Dictionary<string, UIBase> GetShowUI()
        {
            return UIs;
        }

        /// <summary>
        /// 显示除参数外的显示UI
        /// </summary>
        /// <param name="parms"></param>
        /// <returns></returns>
        public static void GetShowUINames(ref List<string> showUIs, params Type[] parms)
        {
            showUIs.Clear();

            foreach (var item in UIs)
            {
                if (parms.Contains(item.Value.GetType()) || !item.Value.IsShow)
                {
                    continue;
                }

                showUIs.Add(item.Key);
            }

            foreach (var item in ProxyUIs)
            {
                if (parms.Contains(item.Value.GetType()) || !item.Value.IsShow)
                {
                    continue;
                }

                showUIs.Add(item.Key);
            }
        }


        /// <summary>
        /// 关闭所有在栈里面的页面
        /// </summary>
        /// <param name="isResume">是否是后台切回</param>
        public static void HideAll(bool isResume = false, Type excludeHideType = null)
        {
            if (UIsn.Count == 0)
                return;

            //UIsn.Clear();

            //TODO 清理的时候可能会打开新的界面（内城战斗时的界面），所以使用list缓存
            var list = new List<UIBase>(UIs.Values);
            //if (!isResume)
            //{
            //    //UIs.Clear();
            //    //UIsn.Clear();
            //}

            UIBase ui = null;
            for (int i = 0; i < list.Count; i++)
            {
                ui = list[i];
                if (ui != null)
                {
                    var type = ui.GetType();
                    //界面过滤处理,因为有的界面不需要关闭
                    if (PanelFilterConfig.FilterResumeHideUI.Contains(type) || ReferenceEquals(excludeHideType, type))
                    {
                        continue;
                    }

                    ui.Hide();
                    UIsn.Remove(ui.Name);

                    EventMgr.FireEvent(TEventType.HideUI, ui.Name, ui);
                    if (ui.UICahceType == EUIResCacheTypeEnum.IMMEDIATELY)// typeof(UIGuid)
                    {
                        ui.Destroy(!isResume);
                        UIs.Remove(ui.Name);
                    }
                }
            }
            ui = null;

            //foreach (var item in UIs)
            //{
            //    D.Error?.Log(" item key 000000  " + item.Key);
            //    item.Value.Hide();
            //    D.Error?.Log(" item key 11111111111  " + item.Key);
            //    item.Value.Destroy();
            //    D.Error?.Log(" item key 22222222222  " + item.Key);
            //}

            //UIs.Clear();
            //s_CommonBluredBackground.Hide();

            //   UIMain.I?.SetGVActivity(
            //(uint)UIMain.GVEnum.LeftTop |
            //(uint)UIMain.GVEnum.MiddleTop |
            //(uint)UIMain.GVEnum.RightTop |
            //(uint)UIMain.GVEnum.LeftMiddle |
            //(uint)UIMain.GVEnum.LeftDownMiddle |
            //(uint)UIMain.GVEnum.RightMiddle |
            //(uint)UIMain.GVEnum.RightUpMiddle |
            //(uint)UIMain.GVEnum.RightDownMiddle |
            //(uint)UIMain.GVEnum.BottomMiddle, true);

            //UIMain.I?.SetGVActivity((uint)UIMain.GVEnum.BottomMiddle, true);
        }

        /// <summary>
        ///销毁所有页面
        /// </summary>
        public static void Clear()
        {
            if (UIs.Count == 0)
                return;

            //TODO 清理的时候可能会打开新的界面（内城战斗时的界面），所以使用list缓存
            var list = new List<UIBase>(UIs.Values);
            UIs.Clear();
            UIsn.Clear();

            UIBase ui = null;
            for (int i = 0; i < list.Count; i++)
            {
                ui = list[i];
                if (ui != null)
                {
                    ui.Destroy(true);
                }
            }
        }

        /// <summary>
        /// Pop弹出忽略列表
        /// </summary>
        private static Type[] popIgnoreArr = new Type[]
        {
            typeof(UIFloatingDamages),
            typeof(HurtFlyText),
            typeof(GoldFly),
            //typeof(UIMainGirl),
            typeof(NewTreasureChestFly),
        };

        /// <summary>
        /// 监测界面是否被包含在Pop忽略数组中
        /// </summary>
        /// <param name="ui"></param>
        /// <returns></returns>
        private static bool IsPopIgnoreArrContain(UIBase ui)
        {
            if (ui == null)
                return false;

            for (int i = 0; i < popIgnoreArr.Length; i++)
            {
                if (popIgnoreArr[i] == ui.GetType())
                    return true;
            }

            return false;
        }

        public static bool Pop()
        {

            if (UIsn.Count == 0)
                return false;

            UIBase ui = null;
            string name = null;
            int indexId = 0;
            bool isNeedRemove = false;
            for (int i = UIsn.Count - 1; i >= 0; i--)
            {
                indexId = i;
                name = UIsn[i];
                ui = Get(name);
                if (ui == null || IsPopIgnoreArrContain(ui))
                    continue;

                isNeedRemove = true;
                break;
            }

            if (!isNeedRemove)
                return false;

            //var name = UIsn[UIsn.Count - 1];
            //UIsn.RemoveAt(UIsn.Count - 1);

            //var u = Get(name);
            //if (u == null) return false;
            //DoHide(u, name, true);

            UIsn.RemoveAt(indexId);

            DoHide(ui, name, true);

            return true;
        }

        public static T Get<T>(string name = "") where T : UIBase
        {
            name = Name<T>(name);
            UIBase u;
            if (UIs.TryGetValue(name, out u))
            {
                return u as T;
            }

            return null;
        }

        public static T AddProxy<T>(string name = "", UIData data = null) where T : UIBase, new()
        {
            name = Name<T>(name);
            //var flag = RemoveProxy(name);
            //if (!flag)
            //{
            var proxy = GetProxy<T>(name);
            if (proxy != null)
                return proxy;
            //}

            var u = new T();
            u.Initialize(name, data);
            ProxyUIs[name] = u;
            u.Show();
            ////Modify by JH 2020/4/28 slg和k1d2融合，添加到一打开界面的缓存信息中
            //UIsn.Add(name);
            //UIs.Add(name, u);

            return u;
        }

        public static bool RemoveProxy(string name)
        {
            if (ProxyUIs.TryGetValue(name, out var ui))
            {
                ui.Hide();
                ui.Destroy();

                if (ui.UICahceType == EUIResCacheTypeEnum.IMMEDIATELY)
                {
                    ProxyUIs.Remove(name);
                    return true;
                }

                return false;
            }

            return false;
        }

        public static T GetProxy<T>(string name = "") where T : UIBase, new()
        {
            if (ProxyUIs.TryGetValue(name, out var ui))
            {
                return (T)ui;
            }

            return null;
        }

        /// <summary>
        /// 移除所有的Proxy界面
        /// </summary>
        public static void RemoveAllProxy()
        {
            if (ProxyUIs.Count == 0)
                return;

            UIBase ui = null;
            foreach (var item in ProxyUIs)
            {
                ui = item.Value;
                if (ui != null)
                {
                    ui.Hide();
                    ui.Destroy(true);
                }
            }

            ProxyUIs.Clear();
        }


        public static UIBase Get(string name = "")
        {
            return Get<UIBase>(name);
        }

        /// <summary>
        /// 获取UI 从UI和Proxy 都去获取，2套字典存储的
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public static UIBase Get_UIAndProxy(string name)
        {
            var ui = Get<UIBase>(name);
            if (UIs.TryGetValue(name, out var u))
            {
                return u;
            }

            if (ProxyUIs.TryGetValue(name, out var pu))
            {
                return pu;
            }

            return null;
        }

        /// <summary>
        /// 监测某一个界面是否正在显示中
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public static bool IsUIShow<T>(string name = null) where T : UIBase
        {
            name = Name<T>(name);
            var ui = Get_UIAndProxy(name);
            if (ui != null && ui.IsShow)
                return true;

            return false;
        }


        //public static void RegisterEvent()
        //{
        //    EventMgr.RegisterEvent(TEventType.WndShowGlobalMask, ShowGLobalMask, "WndMgr");
        //    EventMgr.RegisterEvent(TEventType.WndHideGlobalMask, HideGLobalMask, "WndMgr");
        //}

        //public static void UnRegisterEvent()
        //{
        //    EventMgr.UnregisterEvent(TEventType.WndShowGlobalMask, "WndMgr");
        //    EventMgr.UnregisterEvent(TEventType.WndHideGlobalMask, "WndMgr");
        //}

        //public static void ShowGLobalMask(object[] args)
        //{
        //    Show<UIGlobalScreenMask>();
        //}

        //public static void HideGLobalMask(object[] args)
        //{
        //    Hide<UIGlobalScreenMask>();
        //}

        //public static void ToggleCommonBluredBackground(bool show, bool reblur = false, Action onToggle = null)
        //{
        //    if (show)
        //        s_CommonBluredBackground.Show(reblur, onToggle);
        //    else
        //    {
        //        s_CommonBluredBackground.Hide();
        //        onToggle?.Invoke();
        //    }
        //}

        //public static bool IsShowCommonBluredBackground()
        //{
        //    return s_CommonBluredBackground.m_IsShow;
        //}

        public static bool HasUIShowed(bool uiMainCity = false)
        {
            var ret = false;
            foreach (var ui in UIs)
            {
                //if (uiMainCity && ui.Value is UIMainCity)
                //{
                //    continue;
                //}

                //if (uiMainCity && ui.Value is UIMainGirl)
                //{
                //    continue;
                //}

                if (ui.Value.IsShow)
                {
                    ret = true;
                    break;
                }
            }
            return ret;
        }

        /// <summary>
        /// 获取栈顶UI
        /// </summary>
        public static UIBase GetTopStackUI()
        {
            if (UIsn == null)
                return null;

            if (UIsn.Count == 0)
                return null;

            var name = UIsn[UIsn.Count - 1];
            var ui = Get(name);
            if (ui == null)
                return null;

            return ui;
        }

        ///// <summary>
        ///// 获取栈中第i个UI元素
        ///// </summary>
        ///// <param name="index"></param>
        ///// <returns></returns>
        //public static UIBase GetStackUIBuyIndex(int index = 0)
        //{
        //    if (index > (UIsn.Count - 1))
        //        return null;

        //    if (UIsn == null)
        //        return null;

        //    if (UIsn.Count == 0)
        //        return null;

        //    var name = UIsn[UIsn.Count - index - 1];
        //    var ui = Get(name);
        //    if (ui == null)
        //        return null;

        //    return ui;
        //}

        ///// <summary>
        ///// 设置界面显隐
        ///// </summary>
        ///// <param name="ui"></param>
        ///// <param name="visible"></param>
        //public static void SetPanelVisible(UIBase ui, bool visible)
        //{
        //    var cg = ui.gameObject.GetComponent<CanvasGroup>();
        //    if (cg == null)
        //    {
        //        D.Debug?.Log(string.Format("{0} should contains canvas group", ui.Name));
        //        return;
        //    }
        //    if (visible)
        //    {
        //        cg.alpha = 1;
        //        cg.blocksRaycasts = true;
        //    }
        //    else
        //    {
        //        cg.alpha = 0;
        //        cg.blocksRaycasts = false;
        //    }
        //}
    }
}
