﻿using Game.Data;
using Logic;
using Public;
using System.Collections.Generic;
using KS;
using TFW.UI;
using UI;
using UnityEngine;
using UnityEngine.UI;
using cspb;
using Common;
using THelper;
using TFW.Localization;
using DeepUI;
using K3;
using GameState;
using TFW;

namespace Logic
{
   
    public class LNetShowMgr :Ins<LNetShowMgr>
    {
        private int maxReconnectCd = 5;
        private float curReconnectCd = 0;
        private int retryTimes;
        public bool needCheckNetStatus;
        private bool m_isInit;
        private　float m_Interval;

        private UILoginTipsDialog dialog;

        public bool IsReconnecting
        {
            get
            {
                return retryTimes > 0;
            }
        }
        public void Init()
        {
            if (m_isInit)
                return;
            m_isInit = true;
            retryTimes = 0;
            curReconnectCd = 0;
            needCheckNetStatus = true;
            FrameUpdateMgr.RegisterFixedUpdate("LNetShowMgr", OnUpdate);
            
        }

        private void OnUpdate(float obj)
        {
            if (GSSwitchMgr.CurrentState is GSGamePlay)//未进入游戏阶段 不进行判断
            {

                if (!needCheckNetStatus)
                {
                    UITools.Loading(false);
                    return;
                }

                if ((m_Interval += obj) < 1f)
                {
                    return;
                }

                m_Interval = 0;

                if (string.IsNullOrEmpty(PlatformUtility.GetNetworkType())) //无网络
                {
                    UITools.Loading(false);

                    K3.K3GameEvent.I.TaLog(new ReconnectFailEvent());

                    if (dialog == null || (dialog != null && !dialog.IsShow))
                        PopupManager.I.ShowDialog<UILoginTipsDialog>(new UILoginTipsDialogData() { desc = LocalizationMgr.Get("MENU_server_disconnected_desc") });

                    return;
                }


                if (!GameServerConnection.GameServerConnection.IsConnected)
                {
                    curReconnectCd += 1;
                    // 转菊花
                    UITools.Loading(true);
                }
                else
                {
                    curReconnectCd = 0;
                    retryTimes = 0;
                    // 转菊花
                    UITools.Loading(false);
                    return;
                }
                D.Warning?.Log($"LNetShowMgr.OnUpdate = 与服务器的链接断开，重连进度{curReconnectCd}/{maxReconnectCd}");

                if (!GameServerConnection.GameServerConnection.IsConnected)
                {
                    if (curReconnectCd >= maxReconnectCd)
                    {
                        D.Warning?.Log($"LNetShowMgr.OnUpdate = 与服务器的链接断开尝试重连次数[{retryTimes}]");
                        curReconnectCd = 0;
                        retryTimes += 1;

                        dialog = PopupManager.I.FindPopup<UILoginTipsDialog>();

                        if (retryTimes >= 3)
                        {
                            needCheckNetStatus = false;
                            retryTimes = 0;
                            UITools.Loading(false);

                            K3.K3GameEvent.I.TaLog(new ReconnectFailEvent());

                            if (dialog == null || (dialog != null && !dialog.IsShow))
                                PopupManager.I.ShowDialog<UILoginTipsDialog>(new UILoginTipsDialogData() { desc = LocalizationMgr.Get("MENU_server_disconnected_desc") });
                        }
                        else
                        {
                            ReconnectManager.I.TryGameReconnect();
                        }
                    }
                }
                else
                {
                    needCheckNetStatus = true;
                    retryTimes = 0;
                    UITools.Loading(false);
                    if (dialog != null && dialog.IsShow)
                        PopupManager.I.ClosePopup<UILoginTipsDialog>();
                }
            }
        }
    }
}

