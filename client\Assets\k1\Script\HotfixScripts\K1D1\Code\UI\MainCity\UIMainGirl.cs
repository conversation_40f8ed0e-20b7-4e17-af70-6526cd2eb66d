﻿using Common;
using Cysharp.Threading.Tasks;
using DeepUI;
using Game.Utils;
using K3;
using Logic;
using Public;
using Spine.Unity;
using System.Collections.Generic;
using TFW;
using UnityEngine;
using UnityEngine.EventSystems;
using EventTriggerType = TFW.EventTriggerType;

namespace UI
{
     
    public class UIMainGirl :MonoBehaviour
    {
         
        private Dictionary<int, GameObject> m_CacheSpine = new Dictionary<int, GameObject>();


        public GameObject m_SpineRoot;

        public GameObject m_SpineTouch;

        #region 初始化

        protected  void Start()
        { 
            UIHelper.RemoveListener(EventTriggerType.Click, m_SpineRoot);
            UIHelper.AddListener(EventTriggerType.Click, m_SpineTouch, OnSpineClick);

            var ins = FloatTips.GetUIInstance();
        }

        private void OnSpineClick(GameObject arg0, PointerEventData arg1)
        {
            if (m_CacheSpine.Count == 0)
            {
                return;
            }
            PopupManager.I.ShowLayer<UIHeroSkinPreview>();
        }

        #endregion

        #region 数据刷新显示
         
        /// <summary>
        /// 界面每次显示时调用
        /// </summary>
        protected void OnEnable()
        { 
            FirstCheckMerge();

            CreateSpine().Forget();

            EventMgr.RegisterEvent(TEventType.K3TaskRefresh, RefershTask, this);
            EventMgr.RegisterEvent(TEventType.OnHeroSpineUpdate, OnChapterReward, this);
            EventMgr.RegisterEvent(TEventType.OnMainTaskUpdate, OnChapterReward, this);
        }

        private void OnDisable()
        {
            EventMgr.UnregisterEvent(this);
        }

        private void FirstCheckMerge()
        {
            if (MergeTaskMgr.I.ShowMergeOnStartGame && MergeTaskMgr.I.AllMergeTask.Count > 0)
            {
                MergeTaskMgr.I.ShowMergeOnStartGame = false;


                if (MergeTaskMgr.I.TotalCompleteTaskNum==0)
                {
                    PopupManager.I.ShowLayer<UIMerge>();
                }
            }
        }

      
        private void RefershTask(object[] objs)
        {
            FirstCheckMerge();
        }
         

        #endregion
         

        private void OnChapterReward(object[] obj)
        {
            CreateSpine().Forget();
        }

        private bool m_SpineRefresh;

        private async UniTaskVoid CreateSpine()
        {

            if (!ChapterTaskGameData.I.mChapterQuestInfo.isUnlock)
                return;

            if (m_SpineRefresh)
            {
                return;
            }
            NTimer.CountDown(0.2f, () =>
            {
                m_SpineRefresh = false;
            });
            m_SpineRefresh = true;

            foreach (var pair in m_CacheSpine)
            {
                pair.Value.SetActive(false);
            }

            var reward = ChapterTaskMgr.I.GetCurrentChapterConfig();

            if (reward != null)
            {
                if (m_CacheSpine.ContainsKey(ChapterTaskMgr.I.GetNewChapterId()))
                {
                    m_CacheSpine[ChapterTaskMgr.I.GetNewChapterId()].SetActive(true);

                    ChangeSkin(ChapterTaskMgr.I.GetHeroSkin(reward.Hero), m_CacheSpine[ChapterTaskMgr.I.GetNewChapterId()]);
                    return;
                }


                var hero = Cfg.C.CD2Hero.I(reward.Hero);
                // 创建spine
                GameObject obj = await ResourceMgr.LoadInstanceAsync(hero.HeroSpine[0]);
                if (obj != null)
                {
                    obj.transform.SetParent(m_SpineRoot.transform);
                    obj.transform.localPosition = HeroUtils.Parse(hero.HeroSpine[1]);
                    obj.transform.localScale = HeroUtils.Parse(hero.HeroSpine[2]);
                    obj.transform.localRotation = Quaternion.identity;
                    // m_CurrentSpineObj = obj;
                    m_CacheSpine[ChapterTaskMgr.I.GetNewChapterId()] = obj;

                    ChangeSkin(ChapterTaskMgr.I.GetHeroSkin(reward.Hero), obj);

                }
            }
        }

        public static void ChangeSkin(string skinName,GameObject m_SpineObj)
        {
            if (m_SpineObj != null)
            {
                //UpdateSKinBtnStatus(skinName);
                var anim = m_SpineObj.GetComponent<SkeletonGraphic>();
                // 获取 Skeleton 对象
                var skeleton = anim.Skeleton;
                if (skeleton != null)
                {
                    // 查找新的皮肤
                    var newSkin = skeleton.Data.FindSkin(skinName);
                    if (newSkin != null)
                    {
                        // 设置新的皮肤
                        skeleton.SetSkin(newSkin);
                        skeleton.SetSlotsToSetupPose();      // 更新插槽到设置姿势
                        anim.AnimationState.Apply(skeleton); // 应用动画状态
                    }
                    else
                    {
                        D.Warning?.Log($"LogDebug - Skin '{skinName}' not found.");
                    }
                }
            }
        }
    }
}
