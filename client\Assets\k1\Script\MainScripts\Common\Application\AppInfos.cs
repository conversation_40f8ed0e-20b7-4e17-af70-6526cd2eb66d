﻿// // 版权所有[北京快三科技有限公司]








// AppInfos.cs
// 项目基础信息配置

using KS;
using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;

#endif

public class AppInfos : KS.AppInfo
{
    [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.BeforeSceneLoad)]
    static void CheckInstance()
    {
        if (appInfo == null)
        {
            appInfo = new AppInfos();
        }
    }

    protected override string gameID => "cs2";

    protected override string version
    {
        get
        {
            return Application.version;
        }
    }

    protected override string bundleID => Application.identifier;


    protected override string eveUrl => "";




    protected override string entranceUrl
    {
        get
        {
            return "https://code-cs2-cdn.hwrescdn.com/";
        }
    }

    /// <summary>
    /// 平台环境
    /// </summary>
    protected override string env
    {
        get
        { 
#if DEFAULT_ENV_BETA
            return "beta";
#elif INTERNAL_ENTRANCE || UNITY_EDITOR
            return "debug";
#else
            return "gold";
#endif
        }
    }

   

    protected override string payEveUrl => "";

}
