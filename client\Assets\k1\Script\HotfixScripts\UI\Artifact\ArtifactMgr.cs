﻿using Common;
using cspb;
using GameState;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UI;
using Cfg.Cm;
using UnityEngine;

namespace Logic
{
    public struct ArtfactLevelInfo
    {
        public List<int> unlockIndexs;
        public int level;
        public long exp;
        public int season;
    }
    
    public class ArtifactMgr : THelper.Ins<ArtifactMgr>, ILoginCallback, ILogoutCallback
    {
        /// <summary>
        /// 不可被侦察的神器
        /// </summary>
        const int ScoutConfigId = 1;

        /// <summary>
        /// 免费迁城的神器
        /// </summary>
        const int FreeMoveCityConfigId = 5;

        public event Action onDataChanged;
        
        // 神器升级相关数据刷新
        public event Action onArtifactDataChanged;

        public bool isUnlock { get; private set; }

        public IReadOnlyCollection<int> worldArtifactCfgIds => _worldArtifactInfoMap.Keys;

        public IReadOnlyList<WorldArtifactInfo> worldArtifacts => _worldArtifacts;

        private readonly List<WorldArtifactInfo> _worldArtifacts = new List<WorldArtifactInfo>();

        /// <summary>
        /// key是cfg id
        /// </summary>
        private readonly Dictionary<int, WorldArtifactInfo> _worldArtifactInfoMap = new Dictionary<int, WorldArtifactInfo>();

        /// <summary>
        /// 神器信息等级，初始化
        /// </summary>
        public ArtfactLevelInfo ArtfactLevelInfo = new ArtfactLevelInfo()
        {
            season = -1
        };
        private List<long> m_canUseItemIds = new List<long>();
        public void UpdateCanUsedItemIds(long id, bool add = true)
        {
            if (m_canUseItemIds != null)
            {
                if (add)
                {
                    if (!m_canUseItemIds.Contains(id))
                    {

                        m_canUseItemIds.Add(id);
                    }
                }
                else
                {
                    m_canUseItemIds.Remove(id);
                }
            }
        }
        public void ClearCanUseItemIds()
        {
            m_canUseItemIds?.Clear();
        }
        public bool IsInCanUsedItemIds(long id)
        {
            return m_canUseItemIds.Contains(id);
        }
        public bool IsSelectedOneUsedItem()
        {
            return m_canUseItemIds.Count <= 1;
        }
        public void WorldArtifactsBatchCostReq(Dictionary<int, long> cost)
        {
            // 验证。
            //var req = new WorldArtifactsBatchCostReq();
            //foreach (var item in cost)
            //{
            //    if (item.Value != 0)
            //        req.cost.Add(item.Key, (int)item.Value);
            //}
            //MessageMgr.Send(req);
        }

        /// <summary>
        /// 能否侦察，不可侦察装备三叉戟的城堡
        /// </summary>
        public bool CanScout(EntityInfo entityInfo)
        {
            var artInfo = entityInfo?.PlayerCity?.artInfo;
            if (artInfo != null)
            {
                return !artInfo.Any(x => x != null && x.cfgId == ScoutConfigId && x.isWear);
            }

            return true;
        }

        public bool IsFreeMoveCityArtifact(int cfgId)
        {
            return cfgId == FreeMoveCityConfigId;
        }

        /// <summary>
        /// 能否免费迁城
        /// </summary>
        public bool CanFreeMoveCity()
        {
            var info = this.GetArtifact(FreeMoveCityConfigId);
            if (info != null && LPlayer.I.PlayerID == info.ownerId && info.isWear)
            {
                return info.endTs <= GameTime.Time;
            }
            return false;
        }

        public WorldArtifactInfo GetArtifact(int cfgId)
        {
            if (this._worldArtifactInfoMap.TryGetValue(cfgId, out var ret))
            {
                return ret;
            }
            return null;
        }

        /// <summary>
        /// 服务端的实现需要每次点开神器界面的时候，请求一下
        /// </summary>
        public void RequestArtifacts()
        {
            MessageMgr.Send(new WorldArtifactsReq());
        }

        public void RequestEquipArtifact(int cfgId, bool isEquip)
        {
            MessageMgr.Send(new WorldArtifactsWearReq() { cfgId = cfgId, isWear = isEquip });
        }

        void CheckUnlock()
        {
            if (!this.isUnlock && UI.ArtifactUI.Layer.canOpenUI)
            {
                this.RequestArtifacts();
            }
        }

        void UpdateWorldArtifactInfos(List<WorldArtifactInfo> worldArtifactInfos)
        {
            foreach (var item in worldArtifactInfos)
            {
                this._worldArtifactInfoMap[item.cfgId] = item;
            }
            _worldArtifacts.Clear();
            _worldArtifacts.AddRange(this._worldArtifactInfoMap.Values);
        }

        void OnWorldArtifactsAck(WorldArtifactsAck obj)
        {
            this.isUnlock = obj.isUnLock;
            this.UpdateWorldArtifactInfos(obj.worldArtifacts);

            ArtfactLevelInfo.level = obj.level;
            ArtfactLevelInfo.exp = obj.exp;
            ArtfactLevelInfo.season = obj.season;
            if (null == ArtfactLevelInfo.unlockIndexs)
            {
                ArtfactLevelInfo.unlockIndexs = new List<int>();
            }
            // 先清理。重置内部数据
            ArtfactLevelInfo.unlockIndexs.Clear();
            foreach (var pair in obj.unlockedConditions)
            {
                if (pair.Value)
                {
                    ArtfactLevelInfo.unlockIndexs.Add(pair.Key);
                }
            }

            this.onDataChanged?.Invoke();
            onArtifactDataChanged?.Invoke();
        }

        void OnWorldArtifactsNtf(WorldArtifactsNtf obj)
        {
            if (obj != null && Logic.Common.CheckErrorCode(obj.errCode, true))
            {
                this.UpdateWorldArtifactInfos(obj.worldArtifacts);
                this.onDataChanged?.Invoke();
            }
        }

        void OnWorldArtifactsWearAck(WorldArtifactsWearAck obj)
        {
            if (obj != null && Logic.Common.CheckErrorCode(obj.errCode, true))
            {
            }
        }
        //private void OnWorldArtifactsBatchCostAck(WorldArtifactsBatchCostAck obj)
        //{
        //    if (obj.errCode != ErrCode.ErrCodeSuccess)
        //    {
        //        return;
        //    }
        //    if (ArtfactLevelInfo.level != obj.level)
        //    {
        //        EventMgr.FireEvent(TEventType.ArtifactLevelupCallback);
        //    }
        //    ArtfactLevelInfo.level = obj.level;
        //    ArtfactLevelInfo.exp = obj.exp;
        //    onArtifactDataChanged?.Invoke();
        //}

        // 


        /// <summary>
        /// 神器无限成长系统解锁
        /// </summary>
        public void WorldArtifactsLevelUnlockReq()
        {
            MessageMgr.Send(new WorldArtifactsLevelUnlockReq());
        }

        /// <summary>
        ///  晋升
        /// </summary>
        /// <param name="itemId"></param>
        /// <param name="count"></param>
        public void WorldArtifactsCostReq(int itemId, int count)
        {
            // 验证。
            
            MessageMgr.Send(new WorldArtifactsCostReq()
            {
                itemId = itemId,
                count = count
            });
        }

        /// <summary>
        ///  神器无限成长系统赛季提升
        /// </summary>
        public void WorldArtifactsUpSeasonReq()
        {
            // 验证。
            
            MessageMgr.Send(new WorldArtifactsUpSeasonReq());
        }

        Cysharp.Threading.Tasks.UniTask ILoginCallback.OnLogin()
        {
            this._worldArtifacts.Clear();
            this._worldArtifactInfoMap.Clear();
            
            

            EventMgr.RegisterEvent(TEventType.MainCitySkillLevelUp, (a) => this.CheckUnlock(), this);
            EventMgr.RegisterEvent(TEventType.OnSwitchMainUI, (a) =>
            {
                if (a?.Length > 0)
                {
                    //var type = (MainMenuType)a[0];
                    //if (type == MainMenuType.PRISON)
                    {
                        this.CheckUnlock();
                    }
                }

            }, this);

            MessageMgr.RegisterMsg<WorldArtifactsAck>(this, this.OnWorldArtifactsAck);
            MessageMgr.RegisterMsg<WorldArtifactsNtf>(this, this.OnWorldArtifactsNtf);
            MessageMgr.RegisterMsg<WorldArtifactsWearAck>(this, this.OnWorldArtifactsWearAck);
            MessageMgr.Send(new WorldArtifactsReq());
            
            // 神器晋升相关
            // 神器无限成长系统解锁
            MessageMgr.RegisterMsg<WorldArtifactsLevelUnlockAck>(this, OnWorldArtifactsLevelUnlockAck);
            // 神器无限成长系统消耗道具
            MessageMgr.RegisterMsg<WorldArtifactsCostAck>(this, OnWorldArtifactsCostAck);
            // 神器无限成长系统赛季提升
            MessageMgr.RegisterMsg<WorldArtifactsUpSeasonAck>(this, OnWorldArtifactsUpSeasonAck);
            //MessageMgr.RegisterMsg<WorldArtifactsBatchCostAck>(this, OnWorldArtifactsBatchCostAck);
            return Cysharp.Threading.Tasks.UniTask.CompletedTask;
        }
        
        Cysharp.Threading.Tasks.UniTask ILogoutCallback.OnLogout()
        {

            UpgradeItemId = 0;
            //MessageMgr.UnregisterMsg<WorldArtifactsBatchCostAck>(this);
            MessageMgr.UnregisterMsg<WorldArtifactsAck>(this);
            MessageMgr.UnregisterMsg<WorldArtifactsNtf>(this);
            MessageMgr.UnregisterMsg<WorldArtifactsWearAck>(this);
            
            // 神器晋升相关
            MessageMgr.UnregisterMsg<WorldArtifactsLevelUnlockAck>(this);
            MessageMgr.UnregisterMsg<WorldArtifactsCostAck>(this);
            MessageMgr.UnregisterMsg<WorldArtifactsUpSeasonAck>(this);

            EventMgr.UnregisterEvent(this);
            return Cysharp.Threading.Tasks.UniTask.CompletedTask;
        }

        #region 神器升级

        public int UpgradeItemId = 0;
        /// <summary>
        /// 首次解锁申请，如果进入打开神器界面前，还为解锁神器条件，将尝试调用一次。
        /// </summary>
        /// <param name="obj"></param>
        void OnWorldArtifactsLevelUnlockAck(WorldArtifactsLevelUnlockAck obj)
        {
            if (obj.errCode != ErrCode.ErrCodeSuccess)
            {
                return;
            }
            if (null == ArtfactLevelInfo.unlockIndexs)
            {
                ArtfactLevelInfo.unlockIndexs = new List<int>();
            }
            // 先清理。重置内部数据
            ArtfactLevelInfo.unlockIndexs.Clear();
            foreach (var pair in obj.unlockedConditions)
            {
                if (pair.Value)
                {
                    ArtfactLevelInfo.unlockIndexs.Add(pair.Key);
                }
            }
            ArtfactLevelInfo.level = obj.level;
            ArtfactLevelInfo.exp = obj.exp;
            EventMgr.FireEvent(TEventType.ArtifactLevelupRefreshStatus);
            onArtifactDataChanged?.Invoke();
        }
        
        /// <summary>
        /// 消耗道具回调
        /// </summary>
        /// <param name="obj"></param>
        void OnWorldArtifactsCostAck(WorldArtifactsCostAck obj)
        {
            if (obj.errCode != ErrCode.ErrCodeSuccess)
            {
                return;
            }
            if (ArtfactLevelInfo.level != obj.level)
            {
                EventMgr.FireEvent(TEventType.ArtifactLevelupCallback);
            }
            ArtfactLevelInfo.level = obj.level;
            ArtfactLevelInfo.exp = obj.exp;
            onArtifactDataChanged?.Invoke();
        }
        
        /// <summary>
        /// 赛季晋升回调
        /// </summary>
        /// <param name="obj"></param>
        void OnWorldArtifactsUpSeasonAck(WorldArtifactsUpSeasonAck obj)
        {
            if (obj.errCode != ErrCode.ErrCodeSuccess)
            {
                return;
            }
            ArtfactLevelInfo.season = obj.season;
            EventMgr.FireEvent(TEventType.ArtifactPromotionCallback);
            onArtifactDataChanged?.Invoke();
        }

        /// <summary>
        /// 获取已经解锁的物品ID
        /// </summary>
        /// <returns></returns>
        public List<ItemData> GetAllUnlockItem()
        {
            List<ItemData> tmpItem = new List<ItemData>();
            foreach (var unlockId in ArtfactLevelInfo.unlockIndexs)
            {
                var config = Cfg.C.CArtifactsUnlock.I(unlockId);

                foreach (var itemId in config.UnlockItem)
                {
                    int id = Convert.ToInt32(itemId);
                    tmpItem.Add(new ItemData(id, PlayerAssetsMgr.I.GetItemCountByID(id)));
                }
            }
            return tmpItem;
        }

        private int GetSelectItemId()
        {
            int id = UpgradeItemId;

            if (id == 0)
            {
                var unlockId = ArtfactLevelInfo.unlockIndexs[0];
                var config = Cfg.C.CArtifactsUnlock.I(unlockId);
                id = Convert.ToInt32(config.UnlockItem[0]);
            }
            return id;
        }

        public void InitArtifactLevelUp()
        {
            UpgradeItemId = GetSelectItemId();
        }

        #endregion
    }
}