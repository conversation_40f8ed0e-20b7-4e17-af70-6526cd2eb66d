﻿using Cysharp.Threading.Tasks;
using DeepUI;
using K3;
using System.Collections.Generic;
using System.Linq;
using TFW;
using UI;
using UI.Alliance;
using UnityEngine;


namespace Logic
{
    public static class PreloadHelper
    {
        private static UniTaskCompletionSource<bool> preloadTaskCompletionSource = new UniTaskCompletionSource<bool>();

        // Public property to check if the preload is complete
        public static UniTask<bool> isPreloadConfigComplete => preloadTaskCompletionSource.Task;
        
        delegate UniTask Load();

        static IEnumerable<Load> GetPreloadDelegates()
        {
            yield return Cfg.C.CEpisode.RawDictAsync().AsUniTask;
            yield return Cfg.C.CBubble.RawDictAsync().AsUniTask;
            yield return Cfg.C.CBuffProperty.RawDictAsync().AsUniTask;
            yield return Cfg.C.CItem.RawDictAsync().AsUniTask;
            yield return Cfg.C.CVm.RawDictAsync().AsUniTask;
            //yield return Cfg.C.CD2Stage.RawDictAsync().AsUniTask;
            yield return Cfg.C.CD2Sence.RawDictAsync().AsUniTask;
            //yield return Cfg.C.CD2Monster.RawDictAsync().AsUniTask;
            yield return Cfg.C.CD2Skill.RawDictAsync().AsUniTask;
            yield return Cfg.C.CD2Hero.RawDictAsync().AsUniTask;
            //yield return Cfg.C.CD2Dragon.RawDictAsync().AsUniTask;
            yield return Cfg.C.CD2Model.RawDictAsync().AsUniTask;
            //yield return Cfg.C.CDragonEgg.RawDictAsync().AsUniTask;
            yield return Cfg.C.CD2Effects.RawDictAsync().AsUniTask;
            yield return Cfg.C.CD2Tech.RawDictAsync().AsUniTask;
            yield return Cfg.C.CD2Switch.RawDictAsync().AsUniTask;
            //yield return Cfg.C.CD2Soldier.RawDictAsync().AsUniTask;
        }

        /// <summary>
        /// 预加载资源数据信息
        /// </summary>
        static async UniTask PreloadConfigAsync()
        {
            preloadTaskCompletionSource = new UniTaskCompletionSource<bool>();
            var preloadDelegates = GetPreloadDelegates();
            D.Info?.Log($"预加载总配置数量:{preloadDelegates.Count()} ,{Time.realtimeSinceStartup}");

            await UniTask.WhenAll(preloadDelegates.Select(x => x.Invoke()));

            D.Info?.Log($"预加载配置完成 ,{Time.realtimeSinceStartup}");
            preloadTaskCompletionSource.TrySetResult(true);
        }

        public static bool PreloadEnd = false;

        static async UniTask PreloadUI()
        {

            K3.K3GameEvent.I.TaLog(new K3.LoginEvent() { EventKey = "preloadui_start" });
            //WndMgr.Show<UIMainCity>();
            //WndMgr.Hide<UIMainCity>();

            //WndMgr.Show<UIAllianceMain>();
            //WndMgr.Hide<UIAllianceMain>();

            //TODO 后续走预加载列表处理
            //PopupManager.I.ShowDialog<UIAllianceWelcome>(onComplete: (y) =>
            //{
            //    PopupManager.I.ClosePopup<UIAllianceWelcome>();

            //            PopupManager.I.ShowLayer<UIHeroList>(onComplete: (s) =>
            //            {

            //                NTimer.CountDown(0.1f, () =>
            //                {
            //                    PopupManager.I.ClosePopup<UIHeroList>();
            //                    PopupManager.I.ShowLayer<UIMerge>(onComplete: (t) =>
            //                    {
            //                        PopupManager.I.ClosePopup<UIMerge>();
            //                        //PopupManager.I.ShowLayer<UIShop>(onComplete: (s) =>
            //                        //{
            //                        //    PopupManager.I.ClosePopup<UIShop>();
            //                        PopupManager.I.ShowLayer<MyHeroShowView>(onComplete: (s) =>
            //                        {
            //                            PopupManager.I.ClosePopup<MyHeroShowView>();
            //                            PopupManager.I.ShowLayer<UINewTroopSelect>(new UINewTroopSelectData() { curLineUpIndex = 0, marchArgs = null }, onComplete: (s) =>
            //                            {
            //                                PopupManager.I.ClosePopup<UINewTroopSelect>();
            //                                K3.K3GameEvent.I.TaLog(new K3.LoginEvent() { EventKey = "preloadui_end" });

            //                                PreloadEnd = true;
            //                            });
            //                        });

            //                    });
            //                });

            //            });

            //});

            PopupManager.I.ShowLayer<UIAllianceMain>();
            await new WaitUntil(() => PopupManager.I.FindPopup<UIAllianceMain>() != null);
            await UniTask.Yield();
            PopupManager.I.ClosePopup<UIAllianceMain>();

            PopupManager.I.ShowLayer<UIAllianceWelcome>();
            await new WaitUntil(() => PopupManager.I.FindPopup<UIAllianceWelcome>() != null);
            await UniTask.Yield();
            PopupManager.I.ClosePopup<UIAllianceWelcome>();


            PopupManager.I.ShowLayer<UIHeroList>();
            await new WaitUntil(() => PopupManager.I.FindPopup<UIHeroList>() != null);
            await UniTask.Yield();
            PopupManager.I.ClosePopup<UIHeroList>();

            PopupManager.I.ShowLayer<UIMerge>();
            await new WaitUntil(() => PopupManager.I.FindPopup<UIMerge>() != null);
            await UniTask.Yield();
            PopupManager.I.ClosePopup<UIMerge>();
             
             
            K3.K3GameEvent.I.TaLog(new K3.LoginEvent() { EventKey = "preloadui_end" }); 
            PreloadEnd = true;
        }

        public static async UniTask PreloadData()
        { 
            using (new LaunchTimeLogger("PreloadConfig 预加载"))
            {
                K3.K3GameEvent.I.TaLog(new K3.LoginEvent() { EventKey = "preloadcfg_start" });

                await LSwitchMgr.I.LoadTable();

                await PreloadHelper.PreloadConfigAsync();

                K3.K3GameEvent.I.TaLog(new K3.LoginEvent() { EventKey = "preloadcfg_end" });
            }
             
        }

        public static async UniTask PreLoadUI()
        {
            if (PreloadEnd)
                return;

            using (new LaunchTimeLogger("PreloadUI 预加载"))
                await PreloadHelper.PreloadUI();
        }
    }
}
