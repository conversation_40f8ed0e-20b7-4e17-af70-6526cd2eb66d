﻿using System.Collections.Generic;
using System.Linq;
using Cfg.C;
using Cfg.Cm;
using Common;
using cspb;
using Cysharp.Threading.Tasks;
using DeepUI;
using Game.Data;
using Game.Sprite.Fight;
using Game.Utils;
using Logic;
using Public;
using TFW.Localization;
using TFW.UI;
using UnityEngine;
using UnityEngine.EventSystems;
using EventTriggerType = TFW.EventTriggerType;

namespace UI
{
    public class UIFracturedLandsLineInfoData : PopupData
    {
        public long EntityId;
    }
    
    [Popup("FracturedLands/UIFracturedLandsLineInfo", true, true)]
    public class UIFracturedLandsLineInfo : HistoricPopup
    {
        [PopupField("Root/BtnClose")]
        private GameObject m_BtnClose;
        [PopupField("Root/ImageIcon")]
        private TFWImage m_ImageIcon;
        [PopupField("Root/GroupCommon/InfoBtn")]
        private GameObject m_BtnInfo;
        
        [PopupField("Root/GroupCommon/ShareBtn")]
        private GameObject m_BtnShare;
        
        [PopupField("Root/GroupCommon/Btn")]
        private GameObject m_BtnClick;

        [PopupField("Root/GroupBattleLine")]
        private GameObject m_BattleLine;

        [PopupField("Root/GroupTransportLine")]
        private GameObject m_TransportLine;
        
        [PopupField("Root/GroupCommon/Title")]
        private TFWText m_Title;
        
        [PopupField("Root/GroupCommon/Btn/Text")]
        private TFWText m_BtnText;
        
        [PopupField("Root/GroupCommon/PowerUI/SelfPower/Text")]
        private TFWText m_Power;
        
        [PopupField("Root/GroupCommon/Text")]
        private TFWText m_TextLevel;
        
        [PopupField("Root/GroupTransportLine/Text")]
        private TFWText m_TextQuality;
        
        //[PopupField("Root/GroupBattleLine/ScrollView")]
        //private TFWLoopListView m_BattleView;
        
        //[PopupField("Root/GroupTransportLine/ScrollView")]
        //private TFWLoopListView m_TransportView;

        [PopupField("Root/GroupTransportLine/RewardListUI")]
        private RewardListUI m_groupTransportView;
        [PopupField("Root/GroupBattleLine/RewardListUI")]
        private RewardListUI m_groupAttackView;
        private EntityInfo m_Info;

        private Cfg.G.CKvkLine m_Config;
        
        //
        private List<ITypeIdValue> m_BattleRewardList;
        private List<ITypeIdValue> m_TransportRewardList;
        
        /// <summary>
        /// 滚动列表缓存
        /// </summary>
        private Dictionary<int, SelectItemWidget> m_BattleDic = new Dictionary<int, SelectItemWidget>();
        private Dictionary<int, SelectItemWidget> m_TranportDic = new Dictionary<int, SelectItemWidget>();
        
        protected override void OnInit()
        {
            base.OnInit();
            
            UIHelper.AddListener(EventTriggerType.Click, m_BtnClose, (arg0, data) => Close());
            UIHelper.AddListener(EventTriggerType.Click, m_BtnInfo, OnBtnInfoClick);
            UIHelper.AddListener(EventTriggerType.Click, m_BtnShare, OnBtnShareClick);
            UIHelper.AddListener(EventTriggerType.Click, m_BtnClick, OnBtnClick);
        }

        private void OnBtnClick(GameObject arg0, PointerEventData arg1)
        {
            Close();
            //GameAudio.PlayAudio(AudioID.UIGeneric);
            var entityId = m_Info.ID;
            var entityInfo = LMapEntityManager.I.GetEntityInfo(entityId);
            if (entityInfo != null)
            {
                if (entityInfo.kvkAttacker != null || entityInfo.kvkTransporter!= null)
                {
                    EventMgr.FireEvent(TEventType.UIMain_Update_Start_DispatchTroop, new MarchArgs
                    {
                        targetId = entityId,
                        act = MarchAct.MarchActAttack,
                        isCampAfterHunt = LMapEntityPlayerTroop.IsCampAfterHunt
                    });
                }
                else
                {
                    
                }
            }
        }
        
        /// <summary>
        /// 关闭
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        private void OnClose(GameObject arg0, PointerEventData arg1)
        {
            Close();
        }
        private void OnBtnShareClick(GameObject arg0, PointerEventData arg1)
        {
            if (!LPlayer.I.IsPlayerInUnion())//没有加入联盟
            {
                OnClose(null, null);
                //WndMgr.Show<UIChat>("UIChat", new UIChatArgs() { openTab = ChatTabs.Alliance });
                //UIMain.I?.TrySwitch(MainMenuConst.ALLIANCE);
                //UIMain.I.TrySwitch(MainMenuConst.ALLIANCE, new UIChatData() { CurrentTabKey = UIMain.I.GetMainUICurrState(), CurrChatTab = ChatTabs.Alliance });

                //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.ALLIANCE,
                //    new UIChatData() { CurrentTabKey = GameData.I.MainData.CurrMenuType, CurrChatTab = ChatTabs.Alliance });
            }
            else
            {
                SendShareCoordinateToUnionChannel();
            }
        }
        /// <summary>
        /// 发送坐标分享消息到联盟频道
        /// </summary>
        private void SendShareCoordinateToUnionChannel()
        {
            var entityId = m_Info.ID;
            var entityInfo = LMapEntityManager.I.GetEntityInfo(entityId);
            var pos = Formula.WorldToSharePosition(entityInfo.GetPosition());
            var x = pos.x;
            var z = pos.z;

            int _level = entityInfo.property.kvkAttacker.level;
            
            var config = m_Config;
            if (config == null)
            {
                Debug.Log("config is null!");
                return;
            }

            //分享{0}级{1}
            var unitName = string.Format(LocalizationMgr.GetUIString("Share_chatdes_1"), _level, config.LineName);
            PopupManager.I.ShowPanel<UIShare>(new UIShareData()
            {
                chatContent = UIChatHelper.I.GetShareCoordinateToAllianceChannel
               (
                  config.LineName, _level,
                  ShareDataTypeEnum.Pos,
                  unitName,
                  x,
                  z
               )
            });

        }
        private void OnBtnInfoClick(GameObject arg0, PointerEventData arg1)
        {
            if (m_Config == null || m_Info == null)
                return;

            PopupManager.I.ShowDialog<UIActivityRules>(
                    new UIActivityRuleData()
                    {
                        title =  m_Config.LineType == 1 ? "Kvk_BattleLine" : "Kvk_TransportLine",
                        rules = m_Config.LineType == 1 ? "Kvk_BattleLine_Rule" : "Kvk_TransportLine_Rule",
                    });
        }

        protected internal override void OnDataReady()
        {
            base.OnDataReady();
            var data = Data as UIFracturedLandsLineInfoData;
            m_Info = LMapEntityManager.I.GetEntityInfo(data.EntityId);
        }

        protected override async void OnShown()
        {
            base.OnShown();
            if (m_Info.type == MapUnitType.MapUnitKvkAttacker)
            {
                m_Config = await Cfg.C.CKvkLine.GetConfigAsync(m_Info.kvkAttacker.cfgId);
            }else
            {
                m_Config = await Cfg.C.CKvkLine.GetConfigAsync(m_Info.kvkTransporter.cfgId);
            }
            SetNpcHead().Forget();
            InitTextInfo();
            InitRewardView().Forget();
        }
        private async UniTaskVoid SetNpcHead()
        {
            if(m_Info.type == MapUnitType.MapUnitKvkAttacker)
            {
               
                var npcTroopClassCfg = await Cfg.C.CD2NpcTroopClass.GetConfigAsync(m_Info.kvkAttacker.npcId);
                var heroId = npcTroopClassCfg.LeaderComposition[0].Id;
                UITools.SetImage(m_ImageIcon, HeroUtils.GetHeroDrawIconId(await CD2Hero.GetConfigAsync(heroId)), "Battle");
            }else if(m_Info.type == MapUnitType.MapUnitKvkTransporter)
            {
                var npcTroopClassCfg = await Cfg.C.CD2NpcTroopClass.GetConfigAsync(m_Info.kvkTransporter.npcId);
                var heroId = npcTroopClassCfg.LeaderComposition[0].Id;
                UITools.SetImage(m_ImageIcon, HeroUtils.GetHeroDrawIconId(await CD2Hero.GetConfigAsync(heroId)), "Battle");
            }

            //m_LevelObj.SetActive(m_Config.Type != NpcType.Boss);
            //m_LevelText.text = m_Config.Lvl.ToString();
            //UIHelper.SetImage(m_NpcImage, m_Config.DisplayKey);

        }
        protected internal override void OnCloseStart()
        {
            base.OnCloseStart();
        }

        private void InitTextInfo()
        {
            m_Title.text = LocalizationMgr.Get(m_Config.LineName);
            m_BtnText.text = m_Config.LineType == 1 ? LocalizationMgr.Get("Kvk_BattleLine_Battle") : LocalizationMgr.Get("Kvk_TransportLine_Battle");
            if(m_Info.type == MapUnitType.MapUnitKvkAttacker)
                m_Power.text = UIStringUtils.FormatIntegerByLanguage(m_Info.kvkAttacker.combat);
            else if(m_Info.type == MapUnitType.MapUnitKvkTransporter)
                m_Power.text = UIStringUtils.FormatIntegerByLanguage(m_Info.kvkTransporter.combat);
            m_TextLevel.text = m_Config.LineType == 1 ? LocalizationMgr.Format("Kvk_BattleLine_Level", m_Info.kvkAttacker.level + 1) : LocalizationMgr.Format("Kvk_TransportLine_Level", m_Info.kvkTransporter.level + 1);
        }

        private async UniTaskVoid InitRewardView()
        {
            m_BattleLine.SetActive(m_Config.LineType == 1);
            m_TransportLine.SetActive(m_Config.LineType == 2);
            if (m_Config.LineType == 1)
            {
                var config = (await Cfg.C.CKvkLineLevelUp.RawListAsync()).Where(x => x.EnhanceLevel == m_Info.kvkAttacker.level && x.LineId == m_Info.kvkAttacker.cfgId).First();
                m_BattleRewardList = await VersionRewardMgr.I.GetDisplayRewards(config.Reward);
                m_groupAttackView.SetData(m_BattleRewardList);
                //InitBattleLineView();
            }
            else
            {
                var config = (await Cfg.C.CKvkLineLevelUp.RawListAsync()).Where(x => x.EnhanceLevel == m_Info.kvkTransporter.level && x.LineId == m_Info.kvkTransporter.cfgId).First();
                m_TextQuality.text = FracturedLandsChronicleMgr.I.GetRewardRarity(m_Info.kvkTransporter.level +1, config.Rarity);
                m_TransportRewardList = await VersionRewardMgr.I.GetDisplayRewards(config.AllianceRewardId);
                m_groupTransportView.SetData(m_TransportRewardList);
                //InitTransportLineView();
            }
        }

      
    }
}
