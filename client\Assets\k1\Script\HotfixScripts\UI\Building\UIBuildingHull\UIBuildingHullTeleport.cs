﻿






using Common;
using Config;
using cspb;
using DeepUI;
using Game.Config;
using Game.Data;
using Logic;
using Public;
using Render;
using TFW;
using TFW.Localization;
using TFW.Map;
using TFW.UI;
using UnityEngine;
using UnityEngine.EventSystems;
using EventTriggerType = TFW.EventTriggerType;

namespace UI
{
    /// <summary>
    /// 传送数据
    /// </summary>
    public class UIBuildingHullTeleportData : UIData
    {
        /// <summary>
        /// 迁城类型
        /// </summary>
        public TeleportTypeEnum cityTeleportType;
    }

    /// <summary>
    /// 迁城提示
    /// </summary>
    public class UIBuildingHullTeleport : UIPopMovingBase, IMapLodChangeHandler
    {
        #region Field

        //private TFWImage m_ImageDetSlider;
        //private TFWImage m_ImageOrgSlider;

        private GameObject m_GoBtnTeleportNormal;
        //private GameObject m_GoBtnTeleportGray;
        private GameObject m_CancleBtn;

        //private Color Color_Cost = TFW.Common.ConverHex2RGBA(0x00FF01FF);
        //private Color Color_Dest = TFW.Common.ConverHex2RGBA(0x079D0AFF);
        //private Color Color_NotEnough = TFW.Common.ConverHex2RGBA(0xF36422FF);
        //private Animator m_AniTwinkle;
        private int m_CurrentLod;
        private TFWText m_LabStatus;

        /// <summary>
        /// 使用道具传送
        /// </summary>
        private GameObject m_UseBtn;

        /// <summary>
        /// 发送迁城邀请
        /// </summary>
        private GameObject m_InviteBtn;

        /// <summary>
        /// 发送迁城邀请
        /// </summary>
        private GameObject m_InviteIcon;

        /// <summary>
        /// 标题
        /// </summary>
        private TFWText m_TitleTxt;

        /// <summary>
        /// 购买钻石
        /// </summary>
        private GameObject m_BuyBtn;

        /// <summary>
        /// “确定按钮”只用于提醒，无功能直接关闭窗口
        /// </summary>
        private GameObject m_DefineBtn;
        /// <summary>
        /// 需要的钻石数量
        /// </summary>
        private TFWText needDiamondTxt;

        /// <summary>
        /// 名字
        /// </summary>
        private TFWText itemNameTxt;

        /// <summary>
        /// 有多少道具
        /// </summary>
        private TFWText haveItem;

        /// <summary>
        /// 道具
        /// </summary>
        private GameObject _itemRoot;

        /// <summary>
        /// 迁城类型
        /// </summary>
        public TeleportTypeEnum cityTeleportType;

        /// <summary>
        /// 需要的钻石数量
        /// </summary>
        private int needDiamond;

        /// <summary>
        /// 道具图标
        /// </summary>
        private TFWImage itemIconImg;

        /// <summary>
        /// 品质
        /// </summary>
        private TFWImage itemIconBg;

        /// <summary>
        /// 描述文本
        /// </summary>
        private TFWText descTxt;
        /// <summary>
        /// 描述文本1
        /// </summary>
        private TFWText descTxt1;

        /// <summary>
        /// 免费按钮
        /// </summary>
        private GameObject m_FreeBtn;
        /// <summary>
        /// 免费按钮文本
        /// </summary>
        private TFWText FreeBtnText;

        private bool IsFree = false;
        /// <summary>
        /// 迁城道具id
        /// </summary>
        private int TeleportItemID
        {
            get
            {
                //if (GameData.I.DragonWarNewData.IsInNewDragonWaring)
                //{
                //    return DragonWarUtils.GetTeleportItem(ref cityTeleportType);
                //}
                // if (GameData.I.BrightBattleFieldGameData.IsInBrightBattleFieldWaring)
                //{
                //    if (GameData.I.BrightBattleFieldGameData.LBPlayerBattleUINtfData.totalFreeTeleport - GameData.I.BrightBattleFieldGameData.LBPlayerBattleUINtfData.usedFreeTeleport > 0)
                //    {
                //        cityTeleportType = TeleportTypeEnum.TeleportTypeBrightBattle;
                //        return (int)MetaConfig.NormalTeleportItemID;
                //    }
                //    else
                //    {
                //        cityTeleportType = TeleportTypeEnum.TeleportTypeFixed;
                //        return (int)MetaConfig.NormalTeleportItemID;
                //    }
                //} else
                {
                    //if (cityTeleportType == TeleportType.TeleportTypeFixed)
                    //{
                    //    return (int)MetaConfig.NormalTeleportItemID;
                    //}

                    if (cityTeleportType == TeleportTypeEnum.TeleportTypeFixed
                        || cityTeleportType == TeleportTypeEnum.TeleportDragonWar || cityTeleportType == TeleportTypeEnum.TeleportTypeKingWarCross)
                    {
                        return (int)MetaConfig.NormalTeleportItemID;
                    }
                    //联盟迁城道具
                    var unionTeleportItem = PlayerAssetsMgr.I.GetItemByID((int)MetaConfig.UnionTeleportItemID);

                    //高级迁城道具
                    var normalTeleportItem = PlayerAssetsMgr.I.GetItemByID((int)MetaConfig.NormalTeleportItemID);
                    #region KvK迁城道具使用判断
                    if (cityTeleportType == TeleportTypeEnum.TeleportServerWar)
                    {
                        if (LPlayer.I.FactionId == 0)
                        {
                            this.cityTeleportType = TeleportTypeEnum.TeleportServerWar;
                            return (int)MetaConfig.NormalTeleportItemID;
                        }
                        if (GameData.I.ServerWarData.GetLandOwenServer(CityTeleportManager.I.TeleportPos.x, CityTeleportManager.I.TeleportPos.z) != LPlayer.I.FactionId)
                        {
                            this.cityTeleportType = TeleportTypeEnum.TeleportServerWar;
                            return (int)MetaConfig.NormalTeleportItemID;
                        }
                        else if (unionTeleportItem != null && unionTeleportItem.Count > 0)
                        {
                            //1.有联盟迁城道具,优先使用联盟迁城道具
                            this.cityTeleportType = TeleportTypeEnum.TeleportTypeAlly;
                            return (int)MetaConfig.UnionTeleportItemID;
                        }
                        else if (normalTeleportItem != null && normalTeleportItem.Count > 0)
                        {
                            //2.没有联盟迁城道具,有高级迁城道具，优先使用高级迁城道具
                            return (int)MetaConfig.NormalTeleportItemID;
                        }
                        else
                        {
                            this.cityTeleportType = TeleportTypeEnum.TeleportTypeAlly;
                            //3.没有联盟迁城道和高级迁城道具 ，优先购买联盟迁城
                            return (int)MetaConfig.UnionTeleportItemID;
                        }

                    }
                    #endregion
                    //判断是否处在黑土地范围，如果在则使用高级迁移道具
                    if (LAllianceTerritoryBuilding.I.CheckInBlackLandRange(CityTeleportManager.I.TeleportPos))
                    {
                        return (int)MetaConfig.NormalTeleportItemID;
                    }
                    else if (unionTeleportItem != null && unionTeleportItem.Count > 0)
                    {
                        //1.有联盟迁城道具,优先使用联盟迁城道具
                        return (int)MetaConfig.UnionTeleportItemID;
                    }
                    else if (normalTeleportItem != null && normalTeleportItem.Count > 0)
                    {
                        //2.没有联盟迁城道具,有高级迁城道具，优先使用高级迁城道具
                        return (int)MetaConfig.NormalTeleportItemID;
                    }
                    else
                    {
                        //3.没有联盟迁城道和高级迁城道具 ，优先购买联盟迁城
                        return (int)MetaConfig.UnionTeleportItemID;
                    }

                    //return (int)MetaConfig.UnionTeleportItemID;

                    //没有联盟直接高级迁城
                    //if (LPlayer.I.UnionID == 0)
                    //{
                    //    return (int)MetaConfig.NormalTeleportItemID;
                    //}

                    ////联盟迁城道具
                    //var unionTeleportItem = PlayerAssetsMgr.I.GetItemByID((int)MetaConfig.UnionTeleportItemID);

                    ////高级迁城道具
                    //var normalTeleportItem = PlayerAssetsMgr.I.GetItemByID((int)MetaConfig.NormalTeleportItemID);

                    ////是否在领地内
                    //var isInTerritory = TerritoryUtils.TerritoryLayer.IsInTerritory(LPlayer.I.UnionID, this.RefPosition, out var isEffect);

                    //if (isInTerritory)
                    //{
                    //    if (unionTeleportItem != null && unionTeleportItem.Count > 0)
                    //    {
                    //        //1.如果在领地内 并且有联盟迁城道具,优先使用联盟迁城道具
                    //        return (int)MetaConfig.UnionTeleportItemID;
                    //    }
                    //    else if (normalTeleportItem != null && normalTeleportItem.Count > 0)
                    //    {
                    //        //2.如果在领地内 没有联盟迁城道具,有高级迁城道具，优先使用高级迁城道具
                    //        return (int)MetaConfig.NormalTeleportItemID;
                    //    }
                    //    else
                    //    {
                    //        //3.如果在领地内 没有联盟迁城道和高级迁城道具 ，优先购买联盟迁城
                    //        return (int)MetaConfig.UnionTeleportItemID;
                    //    }
                    //}
                    //else
                    //{
                    //    //4.其他情况 一律为高级迁城
                    //    return (int)MetaConfig.NormalTeleportItemID;
                    //}
                }

            }
        }

        #endregion

        protected override bool HideOnZoom => false;

        #region Method
        protected override string assetPath => "Map/ui4124PopupCityTeleportation";
        protected override bool UpdateWhenViewCenterStop => true;
        public override bool BottomMiddle { get; set; } = true;

        public override EPopPanelType PopPanelType => EPopPanelType.PopupNoMask;

        private GameObject m_panel;
        protected override void OnLoad()
        {
            base.OnLoad();
            m_UseBtn = GetChild("centre_other/animation/Button/UseBtn");
            m_BuyBtn = GetChild("centre_other/animation/Button/BuyBtn");
            m_InviteBtn = GetChild("centre_other/animation/Button/SendInviteBtn");
            m_DefineBtn = GetChild("centre_other/animation/Button/DefineBtn");
            m_FreeBtn = GetChild("centre_other/animation/Button/FreeBtn");
            FreeBtnText = GetComponent<TFWText>("centre_other/animation/Button/FreeBtn/Text");
            m_InviteIcon = GetChild("centre_other/animation/InviteIcon");
            m_panel = GetChild("centre_other/animation");
            needDiamondTxt = GetComponent<TFWText>("centre_other/animation/Button/BuyBtn/CostRoot/Cost");
            haveItem = GetComponent<TFWText>("centre_other/animation/Button/UseBtn/Have");



            itemIconImg = GetComponent<TFWImage>("centre_other/animation/Item/Icon");
            itemNameTxt = GetComponent<TFWText>("centre_other/animation/Item/Text");
            itemIconBg = GetComponent<TFWImage>("centre_other/animation/Item/BG");
            GetChild("centre_other/animation/Button").SetActive(true);

            //道具图标
            _itemRoot = GetChild("centre_other/animation/Item");
            //迁城道具
            AddListener(EventTriggerType.Click, _itemRoot, OnItemClick);


            //取消按钮监听事件和多语言
            AddListener(EventTriggerType.Click, "centre_other/animation/Button/BtnCancel", OnCancelClick);
            m_CancleBtn = GetChild("centre_other/animation/Button/BtnCancel");
            UIHelper.GetComponent<TFWText>(m_CancleBtn, "Text").text = LocalizationMgr.Get("MENU_cancel_cap");
            //取消按钮和多语言
            AddListener(EventTriggerType.Click, m_DefineBtn, OnCancelClick);
            UIHelper.GetComponent<TFWText>(m_DefineBtn, "Text").text = LocalizationMgr.Get("Recruit_own_title_001");
            //X号关闭按钮
            AddListener(EventTriggerType.Click, "centre_other/animation/Title/CloseBtn", OnCancelClick);
            //AddListener(EventTriggerType.Click, "centre_other/animation/Button/BtnCancel", OnTeleportClick);
            //迁城按钮
            AddListener(EventTriggerType.Click, "centre_other/animation/Button/BtnTeleport", OnTeleportClick);
            //AddListener(EventTriggerType.Click, "centre_other/animation/ProgressBar/BtnSpeed", OnSpeedUp);
            //AddListener(EventTriggerType.Click,"centre_other/animation/Button/BtnTeleport2",OnTeleportGrayClick);

            //添加传送点击确认
            AddListener(EventTriggerType.Click, m_UseBtn, OnTeleportClick);
            //添加钻石购买
            AddListener(EventTriggerType.Click, m_BuyBtn, OnUseDiamondBuy);
            //添加邀请
            AddListener(EventTriggerType.Click, m_InviteBtn, OnShareInviteBtnClick);
            //免费迁城
            AddListener(EventTriggerType.Click, m_FreeBtn, OnTeleportClick);

            m_TitleTxt = GetComponent<TFWText>("centre_other/animation/Title/TitleText");
            m_TitleTxt.text = LocalizationMgr.Get("Shop_Text_9");

            m_GoBtnTeleportNormal = GetChild("centre_other/animation/Button/BtnTeleport");
            //m_GoBtnTeleportGray = GetChild("centre_other/animation/Button/BtnTeleport2");

            m_GoBtnTeleportNormal.SetActive(false);
            UIHelper.GetComponent<TFWText>(m_GoBtnTeleportNormal, "Text").text = LocalizationMgr.Get("Map_Teleport_Btn");
            AddListener(EventTriggerType.Click, m_GoBtnTeleportNormal, OnTeleportClick);

            
            //m_GoBtnTeleportGray.SetActive(false);

            //m_LabStatus = GetComponent<TFWText>("centre_other/animation/Main/Txt");
            //m_LabStatus.text = LocalizationMgr.Get("MENU_city_teleportation_tips");

            //描述文本显示组件
            descTxt = GetComponent<TFWText>("centre_other/animation/Main/Txt");
            descTxt.text = LocalizationMgr.Get("MENU_city_teleportation_tips");

            descTxt1 = GetComponent<TFWText>("centre_other/animation/Main/Txt1");


            //RegisterEvent(TEventType.EnergyGaugeUpdate,(arg)=>Refresh());

            RefPosition = CityTeleportManager.I.TeleportPos;

            DirtyFlag = true;

            //MapMgr.MapLODChangeEvent += OnLodChanged;
            //m_CurrentLod = MapMgr.CurrentLod;

            //UIMain.I.ShowContent(false);
        }

        /// <summary>
        /// 分享迁城邀请(盟主才有)
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        private void OnShareInviteBtnClick(GameObject arg0, PointerEventData arg1)
        {
            RefPosition = CityTeleportManager.I.TeleportPos;
            //var pos = this.RefPosition;//  LPlayer.I.GetMainCityPos();
            var x = (int)this.RefPosition.x;
            var z = (int)this.RefPosition.z;

            //Alliance_invite_move_msg  您的盟主邀请您搬迁至此坐标周围  Your leader invites you to move around this coordinate
            var isLeader = LAllianceMgr.I.IsUnionLeader();
            if (isLeader)
            {
                var unitName = "Alliance_invite_move_msg";// LocalizationMgr.Get("Alliance_invite_move_msg");// string.Format(LocalizationMgr.GetUIString("Share_chatdes_1"), _buildingInfo._levelText.text, _buildingInfo._nameText.text);
                var shareContent = UIChatHelper.I.GetShareCoordinateToAllianceChannel
                                 (
                                   unitName, 0,// LPlayer.I.GetMainCityLevel()
                                    ShareDataTypeEnum.MoveCityInvite,
                                    unitName,
                                    x,
                                    z
                                 );
                if (!LPlayer.I.IsPlayerInUnion())//没有加入联盟
                {
                    //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.ALLIANCE,
                    //    new UIChatData() { CurrentTabKey = GameData.I.MainData.CurrMenuType, CurrChatTab = ChatTabs.Alliance });
                }
                else
                {
                    UIChatHelper.I.ShareToAlliance(shareContent);
                }
                this.ClickCloseBtn();
            }
            else
            {
                //UNION_no_jurisdiction	您没有足够的权限
                FloatTips.I.FloatMsg(LocalizationMgr.Get("UNION_no_jurisdiction"));
            }
        }

        //private void OnSpeedUp(GameObject arg0, PointerEventData arg1)
        //{
        //    //if (LEnergyGauge.I.TeleportNeedGauge == int.MaxValue)
        //    //{
        //    //    //超过飞城的最远距离
        //    //    FloatTips.I.FloatMsg(LocalizationMgr.Get("LC_MENU_energy_not_enough"));
        //    //    return;
        //    //}
        //    WndMgr.Show<UIUseItemCommon>(new UIUseItemCommonData()
        //    {
        //        itemLabel = ConfigItemUseLabel.AssetEnergy
        //    });
        //}

        /// <summary>
        /// Lod显示层级转换
        /// </summary>
        /// <param name="lod"></param>
        /// <param name="arg2"></param>
        public void OnLodChange(int lod, float arg2)
        {
            if (m_CurrentLod == lod)
            {
                return;
            }

            if (lod >= LodLevel.LevelMax || lod <= LodLevel.ZERO)
            {
                WndMgr.Hide<UIBuildingHullTeleport>();
                CityTeleportManager.I.FinishTeleport();
                return;
            }

            //if (lod >= LodLevel.ICON)
            //{
            //    //UIHelper.GetComponent<UniformText>(m_GoBtnTeleportNormal, "UniformText").Text = LocalizationMgr.Get("LC_MENU_city_teleportation_zoom");
            //    //UIHelper.GetComponent<TFWText>(m_GoBtnTeleportGray, "BtnTxt").text = LocalizationMgr.Get("LC_MENU_city_teleportation_zoom");
            //    //RemoveListener(EventTriggerType.Click,m_GoBtnTeleportNormal);
            //    //AddListener(EventTriggerType.Click, m_GoBtnTeleportNormal, OnZoomClick);
            //}
            //else
            //{
            //    //UIHelper.GetComponent<UniformText>(m_GoBtnTeleportNormal, "UniformText").Text = LocalizationMgr.Get("LC_MENU_city_teleportation_teleport");
            //    //UIHelper.GetComponent<TFWText>(m_GoBtnTeleportGray, "BtnTxt").text = LocalizationMgr.Get("LC_MENU_city_teleportation_teleport");
            //    //UIHelper.GetComponent<TFWText>(m_GoBtnTeleportNormal, "Text").text = LocalizationMgr.Get("Map_Teleport_Btn");
            //    //RemoveListener(EventTriggerType.Click, m_GoBtnTeleportNormal);
            //    //AddListener(EventTriggerType.Click, m_GoBtnTeleportNormal, OnTeleportClick);

            //    //RemoveListener(EventTriggerType.Click, this.m_UseBtn);
            //    //AddListener(EventTriggerType.Click, m_UseBtn, OnTeleportClick);

            //    //RemoveListener(EventTriggerType.Click, m_BuyBtn);
            //    //AddListener(EventTriggerType.Click, m_BuyBtn, OnUseDiamondBuy);
            //}

            m_CurrentLod = lod;
        }

        /// <summary>
        /// 拖拽开始
        /// </summary>
        private void OnDragStart()
        {
            this.gameObject?.SetActive(false);
        }

        /// <summary>
        /// 拖拽结束
        /// </summary>
        private void OnDragEnd()
        {
            this.gameObject?.SetActive(true);
        }

        protected override void OnShown()
        {
            base.OnShown();
            //RegisterEvent(TEventType.EnergyGaugeUpdate, (arg) => Refresh());
            var data = this.InitData as UIBuildingHullTeleportData;
            if (data != null)
                cityTeleportType = data.cityTeleportType;

            CityTeleportManager.I.AddDragEndListener(OnDragStart, OnDragEnd);

            MapMgr.MapLODChangeEvent += OnLodChanged;
            //MapLodChangeRegistry.Register(this);
            m_CurrentLod = MapMgr.CurrentLod;

            MapMgr.ShowObstacles(true); //主动显示 白色圆圈 UI打开即显示

            Refresh();
        }

        private static void OnLodChanged(int lod, float cameraHeight)
        {
            if(lod >= LodLevel.ICON)
            {
                CityTeleportManager.I.CancelTeleport();
                WndMgr.Hide<UIBuildingHullTeleport>();
            }
        }
       

        protected override void OnHidden()
        {

            base.OnHidden();
            CityTeleportManager.I.RemoveDragEndListener(OnDragStart, OnDragEnd);
            //UnregisterEvent(TEventType.EnergyGaugeUpdate);
            //if (CityTeleportManager.I.IsProcessing)
            //{
            //    CityTeleportManager.I.FinishTeleport();
            //    WndMgr.Hide(Name);
            //}
        }


        private void OnZoomClick(GameObject arg0, PointerEventData arg1)
        {
            if (!DeadBeat.I.IsDeadBeat)
            {
                MapCameraMgr.MoveCameraToTarget(CityTeleportManager.I.TeleportPos.x, CityTeleportManager.I.TeleportPos.z, "city_out", 0.3f, 0.3f, null);
            }
        }


        /// <summary>
        /// 取消迁城
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        private void OnCancelClick(GameObject arg0, PointerEventData arg1)
        {
            CityTeleportManager.I.CancelTeleport();
            WndMgr.Hide<UIBuildingHullTeleport>();

        }

        /// <summary>
        /// 钻石购买
        /// </summary>
        private void OnUseDiamondBuy(GameObject arg0, PointerEventData arg1)
        {
            var diamond = PlayerAssetsMgr.I.GetVMCount(ConfigID.VM_Diamond);
            if (diamond >= needDiamond)
            {
                ClickTeleportCity();
            }
            else
            {
                ItemStoreMgr.I.OpenGetAssetPop(GameCurrencyEnum.CURRENCY_DIAMOND);
                ClickCloseBtn();
            }
        }

        /// <summary>
        /// 确认传送
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        private async void OnTeleportClick(GameObject arg0, PointerEventData arg1)
        {
            if (!IsFree)
            {
                foreach (var lineUp in GameData.I.LineUpData.LineUpDataDic)
                {
                    var state = lineUp.Value.state;

                    if (state != (int)LineupState.LineupStateDefender && state != (int)LineupState.LineupStateErr)
                    {
                        UITools.PopTips(LocalizationMgr.Get("ERRCODE_hasmarchqueue"));
                        return;
                    }
                }

                //if (cityTeleportType == TeleportTypeEnum.TeleportDragonWar)
                //{
                //    //巨龙战场 迁城
                //    if (!DragonWarMgr.I.IsCanDragonWarMoveCity())
                //        return;
                //}
                //else
                {
                    var itemCfg = await Cfg.C.CItem.GetConfigAsync(TeleportItemID);
                    var item = PlayerAssetsMgr.I.GetItemByID(TeleportItemID);
                    if (itemCfg == null)
                    {
                        D.Debug?.Log("拿不到itemCfg TeleportItemID={0}", TeleportItemID);
                        return;
                    }
                    if (item == null || item.Count == 0)
                    {
                        DiamondBuy(itemCfg);
                        return;
                    }
                }

            }
            //var inRange = LAllianceTerritoryBuilding.I.CheckInBlackLandRange(CityTeleportManager.I.TeleportPos);
            //if (inRange &&(LPlayer.I.ShieldOpen || LPlayer.I.GodShieldOpen))
            //{
            //    var btn2data = new MsgBoxBtnParam()
            //    {
            //        str = LocalizationMgr.Get("LC_MENU_cancel_cap"),
            //    };
            //    var btn1data = new MsgBoxBtnParam()
            //    {
            //        str = LocalizationMgr.Get("LC_MENU_confirm_cap"),
            //        func = (o) => { ClickTeleportCity(); },
            //    };
            //    UIMsgBox.Push(EMsgBoxType.two, LocalizationMgr.Get("LC_MENU_warning_cap"),
            //        LocalizationMgr.Get("BlackLandNoShield"),
            //        btn1data,
            //        btn2data, ButtonColorGroup.RedBlue);
            //}
            ClickTeleportCity();
        }

        /// <summary>
        /// 显示钻石
        /// </summary>
        private async void DiamondBuy(Cfg.G.CItem itemCfg)
        {
            if (itemCfg == null)
                return;
            if(gameObject)
                this.gameObject.SetActive(false);

            var data = new UIMsgPopData()
            {
                Title = LocalizationMgr.Get("Map_Teleport_Noitem"),
                Content = LocalizationMgr.Format("Map_Teleport_Noitem_Tips", itemCfg.Value),
                BtnData = await VMData.CreateAsync(ConfigID.VM_Diamond, (long)itemCfg.Value),
                BtnClickCallBack = () =>
                {
                    var title = LocalizationMgr.Get("Buying_Confirm_Tips_Title");
                    var content = LocalizationMgr.Format("Buying_Confirm_Tips_Content", itemCfg.Value);
                    ClickTeleportCity();//UITools.OpenConfirmPop(ClickTeleportCity, ClickCloseBtn, title, content);
                },
                BtnCloseCallBack = () =>
                {

                    ClickCloseBtn();

                }
            };
            PopupManager.I.ShowPanel<UIMsgPop>("UIMsgPop", data);
        }
 

        /// <summary>
        /// 点击传送
        /// </summary>
        private void ClickTeleportCity()
        {
            if(cityTeleportType == TeleportTypeEnum.TeleportDragonWar)
            {
                //巨龙地图迁城
                DragonWarTeleportCity();
            }
            else if(LCrossServer.I.CurrSceneType == SceneType.SceneTypeKvk)
            {
                var inWarLand = LElementalContinentTerritoryRange.I.CheckInWarLandRange(CityTeleportManager.I.TeleportPos);
                if(inWarLand)
                {
                    var btn2data = new MsgBoxBtnParam()
                    {
                        str = LocalizationMgr.Get("LC_MENU_cancel_cap"),
                        func = (o) => {
                            m_panel?.SetActive(true);
                            WndMgr.Hide<UIBuildingHullTeleport>();
                        },
                    };
                    var btn1data = new MsgBoxBtnParam()
                    {
                        str = LocalizationMgr.Get("LC_MENU_confirm_cap"),
                        func = (o) => {
                            m_panel?.SetActive(true);
                            WorldTeleportCity();
                        },
                    };
                    UIMsgBox.Push(EMsgBoxType.two_nc, LocalizationMgr.Get("LC_MENU_warning_cap"),
                        LocalizationMgr.Get("KVKLandNoShield"),
                        btn1data,
                        btn2data, ButtonColorGroup.RedBlue);
                    m_panel?.SetActive(false);
                }
                else
                {
                    WorldTeleportCity();
                }
            }
            else if (LCrossServer.I.CurrSceneType == SceneType.SceneTypeGVGDragon)
            {
                //巨龙地图迁城
                NewDragonWarTeleportCity(); 
            }
            else if (LCrossServer.I.CurrSceneType == SceneType.SceneTypeLB)
            {
                BrightBattleWarTeleportCity();
            }
            else
            {
                //大世界迁城
                var inRange = LAllianceTerritoryBuilding.I.CheckInThroneRange(CityTeleportManager.I.TeleportPos);
                if (inRange)
                {
                    //王座8级区域直接不能迁,20210906 zjw&liubo
                    FloatTips.I.FloatErrcodeMsg(ErrCode.ErrCodeCannotTeleportThrone);
                    return;
                }

                if (LPlayer.I.ShieldOpen || LPlayer.I.GodShieldOpen)
                {
                    var inBlackLand = LAllianceTerritoryBuilding.I.CheckInBlackLandRange(CityTeleportManager.I.TeleportPos);
                    if (inBlackLand)
                    {
                        var btn2data = new MsgBoxBtnParam()
                        {
                            str = LocalizationMgr.Get("LC_MENU_cancel_cap"),
                        };
                        var btn1data = new MsgBoxBtnParam()
                        {
                            str = LocalizationMgr.Get("LC_MENU_confirm_cap"),
                            func = (o) => { WorldTeleportCity(); },
                        };
                        UIMsgBox.Push(EMsgBoxType.two, LocalizationMgr.Get("LC_MENU_warning_cap"),
                            LocalizationMgr.Get("BlackLandNoShield"),
                            btn1data,
                            btn2data, ButtonColorGroup.RedBlue);
                    }
                    else
                    {
                        WorldTeleportCity();
                    }
                }
                else
                {
                    WorldTeleportCity();
                }
            }
            
        }

        /// <summary>
        /// 大世界迁城
        /// </summary>
        private void WorldTeleportCity()
        {
            //guanxing（打包之前一定要恢复，临时处理脏数据注释掉）
            //if (!CityTeleportManager.I.CanTeleport && !LCrossServer.I.IsNeedDownCity)
            //{
            //    FloatTips.I.FloatErrcodeMsg(ErrCode.ErrCodeCityTeleporting);
            //    ClickCloseBtn();
            //    return;
            //}
            if (!CityTeleportManager.I.CanTeleport)
            {
                FloatTips.I.FloatErrcodeMsg(ErrCode.ErrCodeCityTeleporting);
                ClickCloseBtn();
                return;
            }
            var type = this.cityTeleportType;
            var id = this.TeleportItemID;
            if (type != TeleportTypeEnum.TeleportTypeKingWarCross)
            {
                if (id == MetaConfig.NormalTeleportItemID)
                    type = TeleportTypeEnum.TeleportTypeFixed;
            }
            LEnergyGauge.I.TeleportCity(type);
            WndMgr.Hide<UIBuildingHullTeleport>();
        }

        /// <summary>
        /// 巨龙地图迁城
        /// </summary>
        private void DragonWarTeleportCity()
        {
            if (!CityTeleportManager.I.CanTeleport)
            {
                FloatTips.I.FloatErrcodeMsg(ErrCode.ErrCodeCityTeleporting);
                ClickCloseBtn();
                return;
            }

            LEnergyGauge.I.TeleportCity(cityTeleportType);
            WndMgr.Hide<UIBuildingHullTeleport>();
        }
        /// <summary>
        /// 新巨龙地图迁城
        /// </summary>
        private void NewDragonWarTeleportCity()
        {
            if (!CityTeleportManager.I.CanTeleport)
            {
                FloatTips.I.FloatErrcodeMsg(ErrCode.ErrCodeCityTeleporting);
                ClickCloseBtn();
                return;
            }
            if(DragonWarUtils.CheckCanUseFreeTeleportCity())
                LEnergyGauge.I.TeleportCity(TeleportTypeEnum.TeleportTypeNewDragonWar);
            else
                LEnergyGauge.I.TeleportCity(cityTeleportType);
            WndMgr.Hide<UIBuildingHullTeleport>();
        }
        /// <summary>
        /// 新巨龙地图迁城
        /// </summary>
        private void BrightBattleWarTeleportCity()
        {
            if (!CityTeleportManager.I.CanTeleport)
            {
                FloatTips.I.FloatErrcodeMsg(ErrCode.ErrCodeCityTeleporting);
                ClickCloseBtn();
                return;
            }
            //if (GameData.I.BrightBattleFieldGameData.LBPlayerBattleUINtfData.totalFreeTeleport - GameData.I.BrightBattleFieldGameData.LBPlayerBattleUINtfData.usedFreeTeleport > 0)
            //    LEnergyGauge.I.TeleportCity(TeleportTypeEnum.TeleportTypeBrightBattle);
            //else
                LEnergyGauge.I.TeleportCity(cityTeleportType);
            WndMgr.Hide<UIBuildingHullTeleport>();
        }
        /// <summary>
        /// 点击不迁城（关闭）按钮
        /// </summary>
        private void ClickCloseBtn()
        {
            CityTeleportManager.I.CancelTeleport();
            WndMgr.Hide<UIBuildingHullTeleport>();
        }

        //private void OnTeleportGrayClick(GameObject arg0, PointerEventData arg1)
        //{
        //    UITips.ShowTips(arg0.transform.position, GetCostTips());
        //}

        private string GetCostTips()
        {
            //迁城不再需要体力 改成消耗道具
            //var hullGauge = LEnergyGauge.I;
            //var detValue = hullGauge.CurrGaugeValue - hullGauge.TeleportNeedGauge;
            //if (detValue < 0)
            //{
            //    return LocalizationMgr.Get("LC_MENU_energy_not_enough");
            //}

            if (this.cityTeleportType == TeleportTypeEnum.TeleportTypeInvite)
            {
                //Alliance_invite_move_window  请为您的盟友选择一个坐标  Please choose a coordinate for your ally
                return LocalizationMgr.Get("Alliance_invite_move_window");
            }
            else if (this.cityTeleportType == TeleportTypeEnum.TeleportTypeAlly)
            {
                return LocalizationMgr.Get("Union_Tele_Item_Desc");
            }
            return LocalizationMgr.Get("LC_MENU_city_teleportation_tips");

            //if (!CityTeleportManager.I.CanTeleport)
            //{
            //    return LocalizationMgr.Get("LC_MENU_city_teleportation_position_not");
            //}
            //else
            //{
            //    if (this.cityTeleportType == TeleportType.TeleportTypeAlly)
            //    {
            //        return LocalizationMgr.Get("Union_Tele_Item_Desc"); 
            //    }
            //    return LocalizationMgr.Get("LC_MENU_city_teleportation_tips");
            //}
        }

        protected override void OnDestroyed()
        {
            //UnregisterEvent(TEventType.EnergyGaugeUpdate);
            CityTeleportManager.I.RemoveDragEndListener(OnDragStart, OnDragEnd);
            //MapLodChangeRegistry.Unregister(this);
            //MapMgr.MapLODChangeEvent -= OnLodChanged;
            if (CityTeleportManager.I.IsProcessing)
            {
                CityTeleportManager.I.FinishTeleport();
            }

            base.OnDestroyed();
            //UIMain.I.ShowContent(true);
        }


        //protected override void Update(float deltaTime)
        //{
        //    RefPosition = CityTeleportManager.I.TeleportPos;
        //    base.Update(deltaTime);
        //    //Refresh();
        //    if (Input.GetMouseButtonUp(0))
        //    {
        //        //抬起检查一次
        //        //Refresh();
        //        CoordIsUnionAreaReq req = new CoordIsUnionAreaReq();
        //        req.coord = new Coord()
        //        {
        //            X = (int)RefPosition.x * 1000,
        //            Z = (int)RefPosition.z * 1000
        //        };
        //        MessageMgr.Send(req);
        //    }
        //}


        private void EnableTeleportButton(bool isEnable)
        {
            UIHelper.GetComponent<ButtonScale>(m_GoBtnTeleportNormal).interactable = isEnable;
        }


        #endregion


        #region Interface

        /// <summary>
        /// 领地迁城
        /// </summary>
        /// <param name="cityTeleportType"></param>
        public void RefreshType(TeleportTypeEnum cityTeleportType)
        {
            this.cityTeleportType = cityTeleportType;
            Refresh();
        }

        /// <summary>
        /// 点击迁城道具显示
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        private async void OnItemClick(GameObject arg0, PointerEventData arg1)
        {
            var item = await Cfg.C.CItem.GetConfigAsync(TeleportItemID);
            if (item != null)
            {
                var cItem = new ItemData(item.Id);
                var iconName = LocalizationMgr.Get(cItem.GetShowName());
                var iconDesc = LocalizationMgr.Get(cItem.GetShowDesc());
                UIBubbleTipsMgr.UIBubbleTipsArgs args = new UIBubbleTipsMgr.UIBubbleTipsArgs()
                {
                    title = iconName,
                    desc = iconDesc,
                    itemsData = null,
                    pos = arg0.transform.position,
                    arrowDir = ArrowDir.Top,
                    buffsData = null,
                    IsScreenSpaceCamera = true,
                    Quality = item.Quality,
                    HotRect = arg0.RectTransform(),
                };
                UIBubbleTipsMgr.I.ShowNewTip(args.pos,args.title,args.desc,args.arrowDir,args.Quality);
            }

        }

        //protected override void Update(float deltaTime)
        //{
        //    base.Update(deltaTime);
        //}

        /// <summary>
        /// 界面数据刷新
        /// </summary>
        public override async void Refresh()
        {
            IsFree = false;

            RefPosition = CityTeleportManager.I.TeleportPos;
            descTxt1.enabled = false;
            descTxt.enabled = true;
            descTxt.text = GetCostTips();

            MapMgr.ShowObstacles(true);

            m_FreeBtn?.SetActive(false);
            if (this.cityTeleportType == TeleportTypeEnum.TeleportTypeNewDragonWar)
            {
                if (DragonWarUtils.CheckCanUseFreeTeleportCity())
                {
                    m_TitleTxt.text = LocalizationMgr.Get("Shop_Text_9");
                    m_UseBtn?.SetActive(false);
                    m_BuyBtn?.SetActive(false);
                    _itemRoot.SetActive(false);
                    m_InviteBtn?.SetActive(false);
                    m_DefineBtn?.SetActive(false);
                    m_InviteIcon?.SetActive(false);
                    m_FreeBtn?.SetActive(true);
                    FreeBtnText.text = $"{LocalizationMgr.Get("Player_infos_change_name_05")} {LCrossServer.I.NewDragonWarFreeMoveCount}/4";
                    IsFree = true;
                    return;
                }
            }
            //if (this.cityTeleportType == TeleportTypeEnum.TeleportTypeBrightBattle)
            //{
               
            //        m_TitleTxt.text = LocalizationMgr.Get("Shop_Text_9");
            //        m_UseBtn?.SetActive(false);
            //        m_BuyBtn?.SetActive(false);
            //        _itemRoot.SetActive(false);
            //        m_InviteBtn?.SetActive(false);
            //        m_DefineBtn?.SetActive(false);
            //        m_InviteIcon?.SetActive(false);
            //        m_FreeBtn?.SetActive(true);
            //        FreeBtnText.text = $"{LocalizationMgr.Get("Player_infos_change_name_05")} {GameData.I.BrightBattleFieldGameData.LBPlayerBattleUINtfData.totalFreeTeleport - GameData.I.BrightBattleFieldGameData.LBPlayerBattleUINtfData.usedFreeTeleport}/{GameData.I.BrightBattleFieldGameData.LBPlayerBattleUINtfData.totalFreeTeleport}";
            //        IsFree = true;
            //        return;
                
            //}
            if (this.cityTeleportType == TeleportTypeEnum.TeleportServerWar
                || LPlayer.I.GetMainCityScene() == SceneType.SceneTypeKvk
                 || ArtifactMgr.I.CanFreeMoveCity())
            {
                if ((LCrossServer.I.FreeMoveCount > 0 && LCrossServer.I.IsNeedDownCity))
                {
                    m_TitleTxt.text = LocalizationMgr.Get("Shop_Text_9");
                    m_UseBtn?.SetActive(false);
                    m_BuyBtn?.SetActive(false);
                    _itemRoot.SetActive(false);
                    m_InviteBtn?.SetActive(false);
                    m_DefineBtn?.SetActive(false);
                    m_InviteIcon?.SetActive(false);
                    m_FreeBtn?.SetActive(true);
                    FreeBtnText.text = $"{LocalizationMgr.Get("Player_infos_change_name_05")} {LCrossServer.I.FreeMoveCount}/4";
                    IsFree = true;

                    return;
                }
                else if (ArtifactMgr.I.CanFreeMoveCity())
                {
                    m_TitleTxt.text = LocalizationMgr.Get("Shop_Text_9");
                    m_UseBtn?.SetActive(false);
                    m_BuyBtn?.SetActive(false);
                    _itemRoot.SetActive(false);
                    m_InviteBtn?.SetActive(false);
                    m_DefineBtn?.SetActive(false);
                    m_InviteIcon?.SetActive(false);
                    m_FreeBtn?.SetActive(true);
                    FreeBtnText.text = $"{LocalizationMgr.Get("Player_infos_change_name_05")}";
                    IsFree = true;

                    return;
                }
            }
           
            if (this.cityTeleportType == TeleportTypeEnum.TeleportTypeInvite)
            {
                //Alliance_invite_move    邀请迁城  INVITE TELEPORT  邀請遷城
                m_TitleTxt.text = LocalizationMgr.Get("Alliance_invite_move");
                _itemRoot.SetActive(false);
                m_DefineBtn.SetActive(false);
                //迁城邀请
                m_InviteIcon.SetActive(true);
                m_InviteBtn.SetActive(true);
            }
            else
            {
                m_TitleTxt.text = LocalizationMgr.Get("Shop_Text_9");
                _itemRoot.SetActive(true);
                m_InviteIcon.SetActive(false);
                m_DefineBtn.SetActive(false);
                m_InviteBtn.SetActive(false);
                m_UseBtn.SetActive(true);
            }


            var item = await Cfg.C.CItem.GetConfigAsync(TeleportItemID);
            if (item != null)
            {
                UITools.SetImageBySpriteName(this.itemIconImg, item.StandardIcon);
                UITools.SetQualityIcon(this.itemIconBg, item.Quality);


                needDiamond = (int)item.Value;//需要的钻石数量
                var itemData = PlayerAssetsMgr.I.GetItemByID(TeleportItemID);
                //var diamond = PlayerAssetsMgr.I.GetVMCount(ConfigID.VM_Diamond);
                itemNameTxt.text = new ItemData(TeleportItemID).GetShowName();
                if (itemData != null && itemData.Count > 0)
                {
                    //优先使用道具
                    m_BuyBtn.SetActive(false);
                    m_UseBtn.SetActive(true);
                    this.haveItem.text = LocalizationMgr.Format("Player_Attacked_shield_quantity", itemData.Count);//Have:100
                }
                else
                {
                    m_BuyBtn.SetActive(true);
                    m_UseBtn.SetActive(false);
                    this.needDiamondTxt.text = needDiamond.ToString();
                }

                //if (diamond < needDiamond)
                //{
                //    //显示红色?
                //}
            }
        }
        #endregion

    }
}
