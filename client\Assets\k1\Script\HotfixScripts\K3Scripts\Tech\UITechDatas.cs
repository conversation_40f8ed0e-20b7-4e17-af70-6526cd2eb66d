﻿using System;
using System.Collections.Generic;
using System.Linq;
using Cfg.C;
using Cysharp.Threading.Tasks;
using DeepUI;
using Game.Data;
using GameState;
using K3;
using Logic;
using THelper;

namespace UI
{
    public enum Enum_UITechViewItemPos
    {
        Left = 0,
        Center = 1,
        Right = 2,
    }
    
    public class UITechViewData : Ins<UITechViewData>,ILoginCallback
    {
        public List<KeyValuePair<int, UITechStageSub1Data>> ViewDatas = new List<KeyValuePair<int, UITechStageSub1Data>>(); // { tab, UITechStageSub1Data }

        public Action<UITechStageSub1Data> ClickSub1ItemCallback = null;
        public Action<TechData> ClickSub2ItemCallback = null;
        public Action CloseTechViewBottom = null;

        public int SelectedTechType = -1;

        public List<UITechStageLoopListData> LoopListDatas = new List<UITechStageLoopListData>();

        private HashSet<int> mOpendTechTab = new HashSet<int>();

        public List<UITechStageLoopListData> GetLoopListDatas()
        {
            var loopListDatas = new List<UITechStageLoopListData>();


            foreach (var data in ViewDatas)
            {
                var loopData1 = new UITechStageLoopListData();
                loopData1.Sub1Data = data.Value;
                
                loopListDatas.Add(loopData1);

                if (mOpendTechTab.Contains(data.Key))
                {
                    foreach (var sub2Data in data.Value.GetDatas())
                    {
                        var loopData2 = new UITechStageLoopListData();
                        loopData2.Sub2Data = sub2Data;
                
                        loopListDatas.Add(loopData2);
                    }
                }
            }

            return loopListDatas;
        }

        public bool RedPoint()
        {
            bool isUnLock = UnlockMgr.I.CheckUnlock(UnlockFuncEnum.Building_6);
            if (!isUnLock)
                return false;
            foreach (var kv in ViewDatas)
            {
                foreach (var sub1Data in kv.Value.GetDatas())
                {
                    foreach (var tech in sub1Data.TechCfgs.Values)
                    {
                        var techData = GameData.I.SkillData.MTechs[tech];
                        
                        if (CanUpgrade(techData))
                        {
                            return true;
                        }
                    }
                }
            }

            return false;
        }

        public bool CanUpgrade(TechData techData)
        {
            return !IsTechLock(techData) && ResEnough(techData) && !techData.MaxLevel;
        }

        public bool IsTechLock(TechData techData)
        {
            techData.HaveTop(out var preTechUnlock);

            if (techData.IsLocked)
            {
                return !preTechUnlock;
            }

            return false;
        }

        public bool ResEnough(TechData techData)
        {
            var isEnough = PlayerAssetsMgr.I.HaveAssets(techData.UpgradeCost);

            return string.IsNullOrEmpty(isEnough);
        }
        
        public void OpenTechStage(int techTab)
        {
            if (mOpendTechTab.Contains(techTab))
            {
                mOpendTechTab.Remove(techTab);
            }
            else
            {
                mOpendTechTab.Clear();
                
                mOpendTechTab.Add(techTab);
            }
        }

        public bool IsTechTabOpen(int tab)
        {
            return mOpendTechTab.Contains(tab);
        }

        public void CloseAllTab()
        {
            mOpendTechTab.Clear();
        }
        
        public void InitDatas()
        {
            if (ViewDatas.Count > 0)
            {
                return;
            }

            var tmpDatas = new Dictionary<int, UITechStageSub1Data>();

            foreach (var kv in GameData.I.SkillData.MTechs)
            {
                var techType = kv.Key;
                var techData = kv.Value;

                if (techData.MConfig == null)
                {
                    continue;
                }
                
                if (techData.MConfig.Position.Count != 2)
                {
                    continue;
                }
                
                var techTab = techData.MConfig.Tab;

                if (!tmpDatas.ContainsKey(techTab))
                {
                    var sub1Data = new UITechStageSub1Data();
                    sub1Data.TechTab = techTab;
                    
                    tmpDatas.Add(techTab, sub1Data);
                }
                
                tmpDatas[techTab].AddData(techType);
            }

            foreach (var kv in tmpDatas)
            {
                kv.Value.ProcessTechDatas();
            }
            
            ProcessTechDatas(tmpDatas);
        }

        private void ProcessTechDatas(Dictionary<int, UITechStageSub1Data> datas)
        {
            ViewDatas.Clear();
            
            var keys = datas.Keys.ToList();
            keys.Sort();

            foreach (var k in keys)
            {
                ViewDatas.Add(new KeyValuePair<int, UITechStageSub1Data>(k, datas[k]));
            }
        }

        public UniTask OnLogin()
        {
             InitDatas();
            return UniTask.CompletedTask;
        }
    }

    public class UITechStageLoopListData
    {
        public UITechStageSub1Data Sub1Data = null;
        public UITechStageSub2Data Sub2Data = null;
    }

    public class UITechStageSub1Data
    {
        public int TechTab;
        private List<UITechStageSub2Data> Sub2Data = new List<UITechStageSub2Data>();
        private List<int> mTechTypes = new List<int>();

        public bool IsLock()
        {
            foreach (var techType in mTechTypes)
            {
                var techData = GameData.I.SkillData.MTechs[techType];
                
                var isLock = UITechViewData.I.IsTechLock(techData);

                if (!isLock)
                {
                    return false;
                }
            }

            return true;
        }

        public float GetProgress()
        {
            var totalLv = 0;
            var curLv = 0f;

            foreach (var techType in mTechTypes)
            {
                var techData = GameData.I.SkillData.MTechs[techType];
                curLv += techData.Level;
                totalLv += techData.MaxConfig.Level;
            }
            
            if (totalLv == 0 || curLv == 0)
            {
                return 0;
            }

            if (curLv > totalLv)
            {
                curLv = totalLv;
            }

            if (curLv < 0)
            {
                curLv = 0;
            }

            return curLv / totalLv;
        }

        public List<UITechStageSub2Data> GetDatas()
        {
            return Sub2Data;
        }
        
        public void AddData(int techType)
        {
            mTechTypes.Add(techType);
        }

        public void ProcessTechDatas()
        {
            Sub2Data.Clear();

            var tmpDict = new Dictionary<int, UITechStageSub2Data>(); // { 行, 三个 TechData } 行越小的越靠前
            
            foreach (var techType in mTechTypes)
            {
                var techData = GameData.I.SkillData.MTechs[techType];
                
                if (!int.TryParse(techData.MConfig.Position[0], out var line))
                {
                    continue;
                }
                
                if (!int.TryParse(techData.MConfig.Position[1], out var pos))
                {
                    continue;
                }
                
                if (!tmpDict.ContainsKey(line))
                {
                    tmpDict.Add(line, new UITechStageSub2Data());
                }
                
                tmpDict[line].AddTechData((Enum_UITechViewItemPos) pos, techType);
            }

            var keys = tmpDict.Keys.ToList();
            keys.Sort();

            foreach (var k in keys)
            {
                Sub2Data.Add(tmpDict[k]);
            }
        }
    }
    
    public class UITechStageSub2Data
    {
        public Dictionary<Enum_UITechViewItemPos, int> TechCfgs = new Dictionary<Enum_UITechViewItemPos, int>();

        public void AddTechData(Enum_UITechViewItemPos pos, int techType)
        {
            TechCfgs.Add(pos, techType);
        }

        public HashSet<Enum_UITechViewItemPos> GetPosSet()
        {
            var posSet = new HashSet<Enum_UITechViewItemPos>();

            foreach (var kv in TechCfgs)
            {
                if (kv.Value != null)
                {
                    posSet.Add(kv.Key);
                }
            }

            return posSet;
        }
    }

    public class UITechViewJumpData
    {
        public int TechType = -1;

        public void Jump()
        {
            GameData.I.SkillData.MTechs.TryGetValue(TechType, out var techData);
            
            if (techData.MConfig.Tab == 0)
            {
                JumpToBuilding();
                
                return;
            }
            
            var techView = PopupManager.I.FindPopup<UITechView>();
            
            if (null == techView)
            {

                PopupManager.I.ShowLayer<UITechView>(this);
                
                return;
            }
            
            techView.ShowTech(this);
        }

        private void JumpToBuilding()
        {
            var ui = PopupManager.I.FindPopup<UIMainCity>();
            if (ui != null)
            {
                PopupManager.I.ClearAllPopup();
            
                int cityCfgID = LPlayer.I.GetCityBuildingIDByType(TechType);
                ui.dragImage.MoveTOCfg(cityCfgID, () =>
                {
                    var guidData = new UIGuidData();
                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMainCity", UIItem = $"FullScreenBgCanvas/Root/BG1/Content/Building/build_{TechType}" });
                    UIGuid.StartGuid(guidData);
                });
            }
        }
    }
}