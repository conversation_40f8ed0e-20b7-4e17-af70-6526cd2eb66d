﻿using Common;
using cspb;
using Cysharp.Threading.Tasks;
using DeepUI;
using Logic;
using Public;
using System.Collections;
using System.Collections.Generic;
using TFW.UI;
using UI;
using UnityEngine;

public class HeroViewGoodItem : UIGridItem
{

    public TFWImage mIcon;
    public TFWText numText;
    public TFWText addValueText;
    //public TFWImage itemQuality;
    public GameObject selectObj;
    public GameObject btnSendItem;
    // Start is called before the first frame update
    void Start()
    {
        
    }

    private Game.Data.HeroData mHero;
    private Cfg.G.CItem mItem;
    public void SetItem(Game.Data.HeroData _mHero ,Cfg.G.CItem _mItem, Cfg.G.CItem selectItem)
    {
        mHero = _mHero;
        mItem = _mItem;

        addValueText.text = mItem.CategoryParam.Effect[0].Val.ToString();
        numText.text = PlayerAssetsMgr.I.GetItemCountByID(mItem.Id).ToString();
        UITools.SetItemIcon(mIcon, mItem.Id);

        //UITools.SetImageBySpriteName(itemQuality, $"UI_Hero_Gift_Bg_0{mItem.Quality}");

        selectObj.SetActive(mItem.Id==selectItem?.Id);

        UIBase.AddRemoveListener(TFW.EventTriggerType.Click, gameObject, (x, y) =>
        {
            EventMgr.FireEvent(TEventType.K3UIHero_GoodItem_Select, mItem);
        });

        UIBase.AddRemoveListener(TFW.EventTriggerType.Click, btnSendItem, (x, y) =>
        {
            UniTask.Void(async () =>
            {
                bool maxLv= await mHero.MaxIntimacyLevel();
                if(maxLv)
                {
                    FloatTips.I.FloatMsg("ErrCodeHeroIntimacyFull".ToLocal());
                    return;
                }
                if (PlayerAssetsMgr.I.GetItemCountByID(mItem.Id) > 0)
                {
                    PopupManager.I.FindPopup<MyHeroShowView>()?.InitCreateItem();

                    //赠送礼物
                    var item = new UseItem()
                    {
                        CfgID = mItem.Id,
                        count = 1,
                        customCtx = { { "hero", mHero.ModelId.ToString() } }
                    };
                    PlayerAssetsMgr.I.UseItem(item);

                    var gameEvent = new K3.GameEvent();
                    gameEvent.EventKey = "hero_intimacy";
                    gameEvent.Properties.Add("cfgid", mItem.Id.ToString());
                    K3.K3GameEvent.I.TaLog(gameEvent);
                }
                else
                {
                    ItemStoreMgr.I.OpenGetPropPop(mItem.Id, 1);
                }
            });
        });
    }

    private void OnEnable()
    {
        EventMgr.RegisterEvent(TEventType.RefreshAssetAck, (objs) => 
        {
            if (numText != null && mItem != null)
            {
                numText.text = PlayerAssetsMgr.I.GetItemCountByID(mItem.Id).ToString();
            }
        }, this);
    }

    private void OnDisable()
    {
        EventMgr.UnregisterEvent(this);
    }
}
