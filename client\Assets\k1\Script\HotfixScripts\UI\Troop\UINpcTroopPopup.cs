﻿ 

using Cfg.C;
using Common;
using cspb;
using Cysharp.Threading.Tasks;
using DeepUI;
using Game.Config;
using Game.Data;
using Game.Sprite.Fight;
using Game.Utils;
using Logic;
using Public;
using System.Collections.Generic;
using TFW;
using TFW.Localization;
using TFW.UI;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using EventTriggerType = TFW.EventTriggerType;

namespace UI
{
    public class NPCTroopItemData
    {
        /// <summary>
        /// 根节点
        /// </summary>
        public GameObject rootObj;

        /// <summary>
        /// Icon图
        /// </summary>
        public TFWImage _iconImg;

        /// <summary>
        /// Icon数量
        /// </summary>
        public TFWText _iconText;

        /// <summary>
        /// Icon描述
        /// </summary>
        public TFWText _iconDescribeText;

        public NPCTroopItemData(GameObject root)
        {
            rootObj = root;

            _iconImg = UIHelper.GetComponent<TFWImage>(root, "sp_icon_item/sp_icon");

            _iconText = UIHelper.GetComponent<TFWText>(root, "sp_icon_item/OwnFront");
            _iconDescribeText = UIHelper.GetComponent<TFWText>(root, "sp_icon_item/DescribeTxt");
            //var clone = GameObject.Instantiate(root, parent);
            //clone.SetActive(true);
        }

        public void SetData(Transform parent, int dispalyKey, string imgPath, int numStr, string DescribeStr)
        {
            UITools.SetImage(_iconImg, dispalyKey, imgPath, true);
            //_iconText.text = "X" + numStr.ToString();
            _iconText.text = string.Format("X {0}", numStr);
            _iconDescribeText.text = LocalizationMgr.Get(DescribeStr);
        }
    }

    /// <summary>
    /// npc部队信息弹窗（Todo 该界面逻辑有点乱,待优化）
    /// </summary>
    public partial class UINpcTroopPopup : UIPopMovingBase
    {
        #region Field
        /// <summary>
        /// 奖励列表Item
        /// </summary>
        private NPCTroopItemData npcTroopItemData;

        /// <summary>
        /// 实体ID
        /// </summary>
        private long m_EntityID;

        private bool showSpecialIcon = false;
        
        /// <summary>
        /// 实体信息
        /// </summary>
        private EntityInfo m_EntityInfo;


        /// <summary>
        /// 过期时间
        /// </summary>
        private long m_DeadLine;

        /// <summary>
        /// NPC部队本地配置
        /// </summary>
        //private Cfg.G.CNpcTroop m_Config;
        private Cfg.G.CD2NpcTroopClass m_Config;

        /// <summary>
        /// 战力信息配置
        /// </summary>
        private Cfg.G.CNpcTroopPower m_PowerConfig;

        /// <summary>
        /// 是否为Boss-暂时没有显示boss信息需求
        /// </summary>
        private bool m_IsBoos;

        /// <summary>
        /// 倒计时文本
        /// </summary>
        private Text m_TimeText;


        /// <summary>
        /// 计时器实例
        /// </summary>
        private NTimer.Timer m_Timer;

        /// <summary>
        /// 详情面板
        /// </summary>
        private GameObject m_DetailPanel;

        /// <summary>
        /// 描述文本
        /// </summary>
        private Text m_DescriptionText;

        /// <summary>
        /// 奖励道具的root
        /// </summary>
        private UIGrid m_ItemGridRoot;

        /// <summary>
        /// 等级节点
        /// </summary>
        private GameObject m_LevelObj;

        /// <summary>
        /// 等级文本
        /// </summary>
        private Text m_LevelText;

        /// <summary>
        /// NPC图标
        /// </summary>
        private Image m_NpcImage;

        /// <summary>
        /// 是否满足攻击的前置条件
        /// </summary>
        private bool m_AttackRequirement;

        /// <summary>
        /// 前置Npc等级
        /// </summary>
        private int m_PreNpcLevel;

        private GameObject m_ButtonObj;
        private GameObject m_ButtonTextObj;
        /// <summary>
        /// 上半部分半身像
        /// </summary>
        private TFWImage _topMonsterImg;

        /// <summary>
        /// 按钮文本
        /// </summary>
        private TFWText _btnText;

        private TFWText mCostTxt;

        /// <summary>
        /// 是否驻扎文本
        /// </summary>
        private TFWText _keepText;

        /// <summary>
        /// 怪物属性图标
        /// </summary>
        private RectTransform _rectTr;


        private GameObject _defIcon;
        
        private GameObject _specialIcon;
        
        /// <summary>
        /// 怪物属性图标
        /// </summary>
        private TFWImage _AttrIcon;

        /// <summary>
        /// 卡片tip名字
        /// </summary>
        private string[] _iconName = new string[3];

        /// <summary>
        /// 卡片Tip描述
        /// </summary>
        private string[] _iconDesc = new string[3];

        /// <summary>
        /// 卡片列表
        /// </summary>
        private GameObject[] _iconList = new GameObject[3];

        /// <summary>
        /// 最大挑战等级
        /// </summary>
        private TFWText _maxChallengeLevel;

        /// <summary>
        /// 资源路径
        /// </summary>
        protected override string assetPath => "Map/ui4101NpcMain";

        public override EPopPanelType PopPanelType => EPopPanelType.PopUp;

        protected override bool UpdateWhenViewCenterStop => true;

        private const string CONTENT_PATH = "centre_other/animation/ContentDetail";

        public override bool BottomMiddle { get; set; } = true;

        private GameObject m_ToggleObj;

        private Toggle m_Toggle;

        private Text npcPowerText;
        private long npcPower;


        #region WorldBoss

        /// <summary>
        /// 世界boss节点
        /// </summary>
        private GameObject worldBossRoot;

        /// <summary>
        /// 世界boss血条
        /// </summary>
        private TFWSlider worldBossHP;

        /// <summary>
        /// 世界boss血条百分比
        /// </summary>
        private TFWText worldBossHPPercentageTxt;

        /// <summary>
        /// 世界boss血条值
        /// </summary>
        private TFWText worldBossHPTxt;

        #endregion

        #endregion

        #region Method

        /// <summary>
        /// 攻击
        /// </summary>
        private void OnAttackClick(GameObject gameObject, PointerEventData args)
        {
            WndMgr.Hide<UINpcTroopPopup>();
            //GameAudio.PlayAudio(AudioID.UIGeneric);

            if (this.m_EntityInfo != null)
            {

                if (this.m_EntityInfo?.owner?.ID > 0 && this.m_EntityInfo?.owner?.ID != LPlayer.I.PlayerID)
                {
                    FloatTips.I.FloatMsg("Attack_Notice_Error".ToLocal());
                    return;
                }


                if (this.m_EntityInfo.WorldBoss != null)
                {
                    EventMgr.FireEvent(TEventType.UIMain_Update_Start_DispatchTroop, new MarchArgs
                    {
                        targetId = m_EntityID,
                        act = MarchAct.MarchActAttack,
                        isCampAfterHunt = LMapEntityPlayerTroop.IsCampAfterHunt
                    });
                }
                else
                {
                    CheckPower();
                }
            }
        }

        /// <summary>
        /// 检查战力是否足够
        /// </summary>
        /// <returns></returns>
        private bool CheckPower()
        {
            List<Lineup> _lineupList = GameData.I.LineUpData.GetLineUpData();
            long lineUpid = 0;

            //找到部队中含有最大战力的
            long lineupMaxPower = 0;
            List<int> _lineupLst = GameData.I.LineUpData.lockedList;
            for (int i = 0; i < _lineupLst.Count; i++)
            {
                var index = _lineupLst[i];
                var lineup = _lineupList[index];
                if (lineup.heroes.Count > 0)
                {
                    if (_lineupList[index].state != 0)
                    {
                        if (lineupMaxPower < lineup.combatPower)
                        {
                            lineupMaxPower = lineup.combatPower;
                            lineUpid = lineup.id;
                        }
                    }
                }
            }

            //怪物战力
            long powerNpc = npcPower;
            //如果玩家最强队伍战力低于选中怪物战力，就弹二次确认
            //if (lineupMaxPower < powerNpc)
            //{
            //    var btnParam_1 = new MsgBoxBtnParam()
            //    {
            //        str = LocalizationMgr.GetUIString("Player_Name_Guide_Continue"),
            //        func = (_) =>
            //        {
            //            EventMgr.FireEvent(TEventType.UIMain_Update_Start_DispatchTroop, new MarchArgs
            //            {
            //                targetId = m_EntityID,
            //                act = MarchAct.MarchActAttack,
            //                isCampAfterHunt = LMapEntityPlayerTroop.IsCampAfterHunt
            //            });
            //        }
            //    };

            //    //Monster_Power_Enough_Toast  您的战力过低，是否继续攻击？
            //    var btnParam_2 = new MsgBoxBtnParam()
            //    {
            //        str = LocalizationMgr.GetUIString("Actv_KingRoad_SubStage_Title07"),
            //        func = (_) =>
            //        {
            //            //if (_lineupList.Count != 0 && lineUpid != 0)
            //            //    PopupManager.I.ShowDialog<UITroopPowerInfo>(new UITroopPowerInfoData() { lineUpId = lineUpid });
            //            //else
            //            //{
            //            //    EventMgr.FireEvent(TEventType.UIMain_Update_Start_DispatchTroop, new MarchArgs
            //            //    {
            //            //        targetId = m_EntityID,
            //            //        act = MarchAct.MarchActAttack,
            //            //        isCampAfterHunt = LMapEntityPlayerTroop.IsCampAfterHunt
            //            //    });
            //            //}
            //        }
            //    };
            //    var title = LocalizationMgr.GetUIString("MAIL_mail_pop_up_title");
            //    var content = string.Format(LocalizationMgr.GetUIString("Monster_Power_Enough_Toast"));

            //    UITools.ShowMsgBox(EMsgBoxType.two_nc, title, content, btnParam_1, btnParam_2, ButtonColorGroup.GrayGreen);
            //    return false;
            //}
            //else
            //{
            EventMgr.FireEvent(TEventType.UIMain_Update_Start_DispatchTroop, new MarchArgs
            {
                targetId = m_EntityID,
                act = MarchAct.MarchActAttack,
                isCampAfterHunt = LMapEntityPlayerTroop.IsCampAfterHunt
            });
            //}

            return true;
        }

        /// <summary>
        /// 搜索前置怪
        /// </summary>
        private void OnSearchClick(GameObject gameObject, PointerEventData args)
        {
            WndMgr.Hide<UINpcTroopPopup>();
            LSearch.I.SearchLevel = m_PreNpcLevel;
            LSearch.I.Choose = 0;
            LSearch.I.NeedResearch = true;
            PopupManager.I.ShowPanel<UISearch>();
        }

        /// <summary>
        /// 显示第一个Icon的Tip
        /// </summary>
        private void OnClickIconOne(GameObject arg0, PointerEventData arg1)
        {
            UIBubbleTipsMgr.UIBubbleTipsArgs datas = new UIBubbleTipsMgr.UIBubbleTipsArgs()
            {
                title = _iconName[0],
                desc = _iconDesc[0],
                itemsData = null,
                pos = new Vector3(_iconList[0].transform.position.x, _iconList[0].transform.position.y + 0.5f, _iconList[0].transform.position.z),
                arrowDir = ArrowDir.Right,
                buffsData = null
            };
            UIBubbleTipsMgr.I.ShowNewTip(datas.pos,datas.title,datas.desc,datas.arrowDir,datas.Quality);

        }

        /// <summary>
        /// 显示第二个Icon的Tip
        /// </summary>
        private void OnClickIconTwo(GameObject arg0, PointerEventData arg1)
        {
            UIBubbleTipsMgr.UIBubbleTipsArgs datas = new UIBubbleTipsMgr.UIBubbleTipsArgs()
            {
                title = _iconName[1],
                desc = _iconDesc[1],
                itemsData = null,
                pos = new Vector3(_iconList[1].transform.position.x, _iconList[1].transform.position.y + 0.5f, _iconList[1].transform.position.z),
                arrowDir = ArrowDir.Right,
                buffsData = null
            };
            UIBubbleTipsMgr.I.ShowNewTip(datas.pos, datas.title, datas.desc, datas.arrowDir, datas.Quality);

        }

        /// <summary>
        /// 显示第三个Icon的Tip
        /// </summary>
        private void OnClickIconThree(GameObject arg0, PointerEventData arg1)
        {
            UIBubbleTipsMgr.UIBubbleTipsArgs datas = new UIBubbleTipsMgr.UIBubbleTipsArgs()
            {
                title = _iconName[2],
                desc = _iconDesc[2],
                itemsData = null,
                pos = new Vector3(_iconList[2].transform.position.x, _iconList[2].transform.position.y + 0.5f, _iconList[2].transform.position.z),
                arrowDir = ArrowDir.Right,
                buffsData = null
            };
            UIBubbleTipsMgr.I.ShowNewTip(datas.pos,datas.title,datas.desc,datas.arrowDir,datas.Quality);
        }

        /// <summary>
        /// 分享
        /// </summary>
        private void OnShareClick(GameObject arg0, PointerEventData arg1)
        {
            #region 老代码
            //先检查有没有联盟，没有联盟跳到联盟界面
            if (!LPlayer.I.IsPlayerInUnion())//没有加入联盟
            {
                WndMgr.Hide<UINpcTroopPopup>();
                //WndMgr.Show<UIChat>("UIChat", new UIChatArgs() { openTab = ChatTabs.Alliance });
                //UIMain.I?.TrySwitch(MainMenuConst.ALLIANCE);

                //UIMain.I.TrySwitch(MainMenuConst.ALLIANCE, new UIChatData() { CurrentTabKey = UIMain.I.GetMainUICurrState(), CurrChatTab = ChatTabs.Alliance });

                //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.ALLIANCE, new UIChatData()
                //{
                //    CurrentTabKey = GameData.I.MainData.CurrMenuType,
                //    CurrChatTab = ChatTabs.Alliance
                //});
            }
            else
            {
                SendShareCoordinateToUnionChannel();
            }
            #endregion
        }

        /// <summary>
        /// 发送坐标分享消息到联盟频道
        /// </summary>
        private void SendShareCoordinateToUnionChannel()
        {
            var pos = Formula.WorldToSharePosition(RefPosition);
            int _entityLevel = m_EntityInfo.NpcTroop.level;
            int _realLevel = (FightManager.I.TrainLevel/*GameLevelManager.I.Soldier.Opensheet.TrainLevel*/ + _entityLevel) > 0 ? (FightManager.I.TrainLevel/*GameLevelManager.I.Soldier.Opensheet.TrainLevel*/ + _entityLevel) : 1;
            int _level = m_EntityInfo.NpcTroop.dynamic == true ? _realLevel : _entityLevel;

            //分享{0}级{1}
            var unitName = string.Format(LocalizationMgr.GetUIString("Share_chatdes_1"), _level, m_Config.GetName());
            PopupManager.I.ShowPanel<UIShare>(new UIShareData()
            {
                chatContent = UIChatHelper.I.GetShareCoordinateToAllianceChannel
               (
                   m_Config.LcName.Txt, _level,
                  ShareDataTypeEnum.Rally,
                  unitName,
                  pos.x,
                  pos.z
               )
            });

        }

        /// <summary>
        /// 计时器Tick
        /// </summary>
        private void OnTimerTick()
        {
            m_DeadLine -= 1000;

            if (m_TimeText != null)
                m_TimeText.text = UIHelper.GetFormatTime(m_DeadLine);

            if (m_DeadLine < 0)
            {
                Debug.LogFormat("OnTimerTick===============m_DeadLine={0}", m_DeadLine);
                WndMgr.Hide<UINpcTroopPopup>();
            }
        }

        protected override void OnInit()
        {
            if (InitData != null)
            {
                var data = InitData as UIPopMovingBaseData;
                m_EntityID = data.EntityId;

            }
            else
            {
                m_EntityID = 0;
            }
            m_EntityInfo = LMapEntityManager.I.GetEntityInfo(m_EntityID);
            if (m_EntityInfo != null)
            {
                //reset position
                RefPosition = m_EntityInfo.CurrentPosition;
                if (m_EntityInfo.WorldBoss != null)
                {
                    m_Config = CD2NpcTroopClass.I(m_EntityInfo.WorldBoss.CfgId);
                    m_DeadLine = m_EntityInfo.WorldBoss.deadline - GameTime.Time;
                }
                else
                {
                    if (m_EntityInfo.NpcTroop != null)
                    {
                        m_Config = CD2NpcTroopClass.I(m_EntityInfo.NpcTroop.npcTroopID);
                        m_PowerConfig = CNpcTroopPowerExtension.GetNpcTroopPower(m_EntityInfo.NpcTroop.npcTroopID, m_EntityInfo.NpcTroop.GetLevel());
                        //m_PowerConfig = GetNpcTroopPowerCfgByLevel(m_EntityInfo.NpcTroop.npcTroopID, m_EntityInfo.NpcTroop.Level);
                        if (m_PowerConfig == null) Debug.LogWarning($"CNpcTroopPower configid is null");
                        m_DeadLine = m_EntityInfo.NpcTroop.deadline - GameTime.Time;
                    }
                }


                //暂时没有显示boss信息需求
                m_IsBoos = false;// m_EntityInfo.NpcTroop.Type == NpcType.Boss;


                int _killLevel = LPlayer.I.GetKillOrderData(m_Config.Id) > 0 ? LPlayer.I.GetKillOrderData(m_Config.Id) + 1 : 1;

                if (m_EntityInfo.WorldBoss != null)
                {
                    m_AttackRequirement = true;
                }
                else
                {
                    m_AttackRequirement = _killLevel >= m_EntityInfo.NpcTroop.GetLevel();
                }

                if (!m_AttackRequirement)
                {
                    //m_PreNpcLevel = m_EntityInfo.NpcTroop.Level;
                    m_PreNpcLevel = _killLevel;
                }
            }
        }

        //private Cfg.G.CNpcTroopPower GetNpcTroopPowerCfgByLevel(int NpcID, int level)
        //{
        //    var lst = CNpcTroopPower.Is();
        //    int cnt = lst.Count;
        //    int low = 0;
        //    int high = cnt - 1;
        //    //for (; low <= high;)
        //    while (low <= high)
        //    {
        //        var mid = low + (high - low) / 2;
        //        var r = lst[mid];
        //        if (r.Type > NpcID)
        //            high = mid - 1;
        //        else if (r.Type < NpcID)
        //            low = mid + 1;
        //        else
        //        {
        //            if (r.Level > level)
        //                high = mid - 1;
        //            else if (r.Level < level)
        //                low = mid + 1;
        //            else
        //                return r;
        //        }
        //    }

        //    return null;
        //}

        protected override void OnLoad()
        {

            base.OnLoad();
            if (m_EntityInfo != null)
                RefPosition = m_EntityInfo.CurrentPosition;
            //RefTransform = RMapEntityManager.I.GetEntity(m_EntityID)?.transform;

            var btnClose = GetChild("centre_other/animation/Titlebar/CloseBtn");
            UIBase.AddRemoveListener(EventTriggerType.Click, btnClose, (x, y) =>
            {
                CloseSelf();
            });
            
            AddListener(EventTriggerType.Click, $"{CONTENT_PATH}/Image/BtnGather", OnAttackClick, out m_ButtonObj);
            AddListener(EventTriggerType.Click, "centre_other/animation/Titlebar/btnShare", OnShareClick);
            AddListener(EventTriggerType.Click, "centre_other/animation/Titlebar/BtnCollection", OnBtnMarking);

            //世界boss
            worldBossRoot = GetChild($"{CONTENT_PATH}/Image/WorldBoss");
            worldBossHP = GetComponent<TFWSlider>($"{CONTENT_PATH}/Image/WorldBoss/Slider");
            worldBossHPPercentageTxt = GetComponent<TFWText>($"{CONTENT_PATH}/Image/WorldBoss/Slider/Text");
            worldBossHPTxt = GetComponent<TFWText>($"{CONTENT_PATH}/Image/WorldBoss/Text");

            m_ToggleObj = GetChild($"{CONTENT_PATH}/Image/Toggle");
            m_Toggle = m_ToggleObj.GetComponent<TFWToggle>();
            //m_LevelObj = GetChild($"{CONTENT_PATH}/LeftIcon/LvBg");
            //m_LevelText = GetComponent<Text>($"{CONTENT_PATH}/LeftIcon/LvBg/Text");
            _btnText = GetComponent<TFWText>($"{CONTENT_PATH}/Image/BtnGather/UniformText/txt1");
            _keepText = GetComponent<TFWText>($"{CONTENT_PATH}/Image/Toggle/Label");
            _btnText.text = LocalizationMgr.Get("MENU_attack_cap");
            _keepText.text = LocalizationMgr.Get("MAP_keep_position_checkbox");
            _maxChallengeLevel = GetComponent<TFWText>("centre_other/animation/ContentDetail/Image/MaxText");
            //GetChild($"{CONTENT_PATH}/Image/Toggle").SetActive(false);

            mCostTxt = GetComponent<TFWText>("centre_other/animation/ContentDetail/Image/BtnGather/GameObject/Text (1)");

            m_ToggleObj.SetActive(false);
            GetChild($"{CONTENT_PATH}/Tip").SetActive(false);
            GetChild($"{CONTENT_PATH}/Image/BtnGather").SetActive(true);
            var key = m_IsBoos ? "LC_MENU_rally_cap" : "LC_MENU_attack_cap";
            m_ButtonTextObj = GetChild($"{CONTENT_PATH}/Image/BtnGather/UniformText");

            _defIcon = GetChild("centre_other/animation/ContentDetail/BG/BG");
            _specialIcon = GetChild("centre_other/animation/ContentDetail/BG/special");
            UIHelper.SetButtonText(m_ButtonTextObj, LocalizationMgr.Get(key));

            OnLoadInfoPanel();
            //SetToggle();
            //SetNpcRemainTime();
            //SetNpcRewards();
            //SetTitleBar();
            //SetNpcHead();
            //RefreshPreConditionDescription();
            //SetAttributeIcon();

            LayoutRebuilder.ForceRebuildLayoutImmediate(rectTransform);

        }

        protected override void AddListener()
        {
            base.AddListener();

            AddListener(EventTriggerType.Click, _iconList[0], OnClickIconOne);
            AddListener(EventTriggerType.Click, _iconList[1], OnClickIconTwo);
            AddListener(EventTriggerType.Click, _iconList[2], OnClickIconThree);
        }

        protected override void RemoveListener()
        {
            base.RemoveListener();

            RemoveListener(EventTriggerType.Click, _iconList[0]);
            RemoveListener(EventTriggerType.Click, _iconList[1]);
            RemoveListener(EventTriggerType.Click, _iconList[2]);
        }

        protected override void Update(float deltaTime)
        {
            base.Update(deltaTime);

            UpdateInfoPanelPosition(deltaTime);
        }

        /// <summary>
        /// 标记
        /// </summary>
        private void OnBtnMarking(GameObject obj, PointerEventData args)
        {
            UITools.PopTips(LocalizationMgr.Get("DEMO_30"));
            //WndMgr.Hide<UINpcTroopPopup>();
            //var coord = new Coord();
            //coord.X = (int) m_EntityInfo.CurrentPosition.x;
            //coord.Z = (int) m_EntityInfo.CurrentPosition.z;
            //WndMgr.Show<UIMapMarkingMain>(new UIMapMarkingMain.MarkingUnitData()
            //{
            //    Position = coord,
            //    UnitType = MapUnitType.MapUnitNpcTroop,
            //    Level = m_Config.Lvl,
            //    DefaultName = m_Config.Name,
            //});
        }

        private void SetToggle()
        {
            m_Toggle.isOn = LMapEntityPlayerTroop.IsCampAfterHunt;
            m_Toggle.onValueChanged.RemoveAllListeners();
            m_Toggle.onValueChanged.AddListener(OnToggleChanged);
        }

        private void OnToggleChanged(bool isChecked)
        {
            if (LMapEntityPlayerTroop.IsCampAfterHunt != isChecked)
            {
                LMapEntityPlayerTroop.IsCampAfterHunt = isChecked;
            }
        }

        private void SetNpcRemainTime()
        {
            m_TimeText = GetComponent<Text>($"{CONTENT_PATH}/LeftIcon/sp_time_di/lab_time");
            m_TimeText.text = string.Empty;
            m_TimeText.transform.parent.gameObject.SetActive(m_Config.LifetimeSwitch == 1);

            if (m_Config.LifetimeSwitch == 1)
            {
                NTimer.TickNoPool(ref m_Timer, NTimer.INF, 1.0f, OnTimerTick);
            }
        }

        /// <summary>
        /// 设置属性图
        /// </summary>
        private void SetAttributeIcon()
        {
            _rectTr = GetComponent<RectTransform>("centre_other/animation/Titlebar/Title");
            _AttrIcon = GetComponent<TFWImage>("centre_other/animation/Titlebar/Title/titleText/Icon");
            var attr = UITools.GetAttributeDisplayKey(m_Config.NPCType);
            UITools.SetImageBySpriteName(_AttrIcon, attr);
        }

        /// <summary>
        /// 奖励信息显示
        /// </summary>
        private async UniTask SetNpcRewards()
        {
            _AttrIcon.gameObject.SetActive(true);
            if (m_EntityInfo != null)
            {
                m_ItemGridRoot = GetComponent<UIGrid>($"{CONTENT_PATH}/ScrollView/RssDetail/Grid");

                if (m_ItemGridRoot)
                {
                    m_ItemGridRoot.Clear();

                    if (m_EntityInfo.WorldBoss != null)
                    {
                        _AttrIcon.gameObject.SetActive(false);
                        //世界boss奖励
                        var rewards =await CWorldBossQuestExtension.GetWorldBossReward();
                        foreach (var item in rewards)
                        {
                            m_ItemGridRoot.AddItem<CommonItem>().InitData(new CommonItem.CommonItemData(item));
                        }
                    }
                    else
                    {
                        Debug.Log($"NPCRewards Count:{m_PowerConfig.DropDisplay.Count}");

                        foreach (var item in m_PowerConfig.DropDisplay)
                        {
                            if (item.Id == 21113001)//金币
                            {
                                CommonItem.CommonItemData itemData = new CommonItem.CommonItemData();
                                int realNum = MetaConfig.NpcTroopReward[0] + (m_EntityInfo.NpcTroop.GetLevel() - 1) * MetaConfig.NpcTroopReward[1];
                                itemData.Val = realNum > MetaConfig.NpcTroopReward[2]
                                   ? MetaConfig.NpcTroopReward[2] : realNum;

                                itemData.Id = item.Id;
                                itemData.Typ = item.Typ;
                                m_ItemGridRoot.AddItem<CommonItem>().InitData(itemData);
                            }
                            else
                            {
                                m_ItemGridRoot.AddItem<CommonItem>().InitData(new CommonItem.CommonItemData(item));
                            }
                        }
                    }
                }
            }

            LayoutRebuilder.ForceRebuildLayoutImmediate(_rectTr);
        }

        /// <summary>
        /// 世界boss信息
        /// </summary>
        private void SetWorldBossInfo()
        {
            if (m_EntityInfo != null && m_EntityInfo.WorldBoss != null)
            {
                m_ToggleObj?.SetActive(false);
                var cur = m_EntityInfo.WorldBoss.combat;
                var max = m_EntityInfo.WorldBoss.combatMax;

                worldBossRoot.SetActive(true);
                var val = (float)cur / max;
                //Todo 更新血条
                worldBossHP.value = val;
                worldBossHPPercentageTxt.text = UIStringUtils.FormatPercentByLanguage(val, 2);// string.Format("{0}%", (val * 100).ToString("f2"));
                worldBossHPTxt.text = string.Format("{0}/{1}", UIStringUtils.FormatIntegerByLanguage(m_EntityInfo.WorldBoss.combat), UIStringUtils.FormatIntegerByLanguage(m_EntityInfo.WorldBoss.combatMax));
                npcPowerText.text = UIStringUtils.FormatIntegerByLanguage(m_EntityInfo.WorldBoss.combatMax);
            }
            else
            {
                worldBossRoot.SetActive(false);
            }
        }

        private void SetTitleBar()
        {
            var npcName = GetComponent<Text>("centre_other/animation/Titlebar/Title/titleText");
            ////功能无法合并暂时隐藏 TODO
            //GetChild("centre_other/animation/Titlebar/power").SetActive(false);
            npcPowerText = GetComponent<Text>("centre_other/animation/Titlebar/power/Text");
            var hordeIcon = GetComponent<TFWImage>("centre_other/animation/Titlebar/BannerHordeWhite");
            var entity = LMapEntityManager.I.GetEntityInfo(m_EntityID);
            //var npcOwner = entity.owner?.horde ?? (int) LHordeMgr.HordeIds.Unknown;

            if (entity != null)
            {
                if (entity.NpcTroop != null)
                {
                    int _entityLevel = m_EntityInfo.NpcTroop.level;
                    int _realLevel = (FightManager.I.TrainLevel/*GameLevelManager.I.Soldier.Opensheet.TrainLevel*/ + _entityLevel) > 0 ? (FightManager.I.TrainLevel/*GameLevelManager.I.Soldier.Opensheet.TrainLevel*/ + _entityLevel) : 1;
                    int _level = m_EntityInfo.NpcTroop.dynamic == true ? _realLevel : _entityLevel;
                    //npcName.text = LocalizationMgr.Format("Npc_level_parameter", m_Config.Lvl, m_Config.Name);
                    npcName.text = LocalizationMgr.Format("Npc_level_parameter", m_Config.GetName(), _level);

                    //普通怪
                    // var isNomralNpc = m_Config != null && m_Config.Class == 1;
                    // if (isNomralNpc && CNpcTroopPower.GetNpcTroopPowerByLevel(m_EntityInfo.property.npcTroop.npcTroopID, m_EntityInfo.property.npcTroop.Level, out var power))
                    // {
                    //     npcPowerText.text = UIStringUtils.FormatIntegerByLanguage(power);
                    //     npcPower = power;
                    // }
                    // else
                    // {
                    //     npcPowerText.text = UIStringUtils.FormatIntegerByLanguage(m_PowerConfig?.Power ?? 0);
                    //     npcPower = m_PowerConfig?.Power ?? 0;
                    // }
                    // long power =() m_PowerConfig.Power * m_PowerConfig.SoldierNum;
                    long power = (long)(m_PowerConfig.Power / MetaConfig.MapTroopHeroPowerParam * m_PowerConfig.SoldierNum);
                    npcPowerText.text = UIStringUtils.FormatIntUnitByLanguage(power);
                    npcPower = power;
                }
                else if (entity.WorldBoss != null)
                {
                    int _entityLevel = m_EntityInfo.WorldBoss.level;
                    //int _realLevel = (FightManager.I.TrainLevel/*GameLevelManager.I.Soldier.Opensheet.TrainLevel*/ + _entityLevel) > 0 ? (FightManager.I.TrainLevel/*GameLevelManager.I.Soldier.Opensheet.TrainLevel*/ + _entityLevel) : 1;
                    int _level = _entityLevel;
                    //npcName.text = LocalizationMgr.Format("Npc_level_parameter", m_Config.Lvl, m_Config.Name);
                    npcName.text = LocalizationMgr.Format("Npc_level_parameter", m_Config.GetName(), _level);
                }
            }

            //npcPower.text = ResourceUtils.GetResourceShowStr(m_EntityInfo.property.npcTroop.power);
            //hordeIcon.color = LHordeMgr.I.GetHordeColor(npcOwner);
        }

        private void SetNpcHead()
        {
            var tempData = InitData as UIPopMovingBaseData;
            
            /*if (tempData.ShowSpecialIcon != null )
                showSpecialIcon = tempData.ShowSpecialIcon;
            else
                showSpecialIcon = false;*/
            showSpecialIcon = m_PowerConfig?.Type == 3001;
            _defIcon.SetActive(!showSpecialIcon);
            _specialIcon.SetActive(showSpecialIcon);
            var npcImg = GetComponent<TFWImage>("centre_other/animation/Icon");

            var heroId = m_Config.LeaderComposition[0].Id;
            //Cfg.G.CD2Hero heroCfg = Cfg.C.CD2Hero.I(heroId);
            UITools.SetImage(npcImg, HeroUtils.GetHeroDrawIconId(CD2Hero.I(heroId)), "Battle");

            //m_LevelObj.SetActive(m_Config.Type != NpcType.Boss);
            //m_LevelText.text = m_Config.Lvl.ToString();
            //UIHelper.SetImage(m_NpcImage, m_Config.DisplayKey);

        }

        private void RefreshPreConditionDescription()
        {
            var labDesc = GetComponent<TFWText>($"{CONTENT_PATH}/Tip/Label");
            var labNode = GetChild($"{CONTENT_PATH}/Tip");

            var txt = string.Empty;
            if (!m_AttackRequirement)
            {
                labNode.SetActive(true);
                m_ToggleObj.SetActive(false);

                UIHelper.SetButtonText(m_ButtonTextObj, LocalizationMgr.Get("LC_MENU_search_cap"));
                RemoveListener(EventTriggerType.Click, $"{CONTENT_PATH}/Image/BtnGather");
                AddListener(EventTriggerType.Click, $"{CONTENT_PATH}/Image/BtnGather", OnSearchClick);

                txt = LocalizationMgr.Format("LC_MAP_please_defeat_low_level_monster", m_PreNpcLevel);
                labDesc.text = txt;
            }
            else
            {
                var key = m_IsBoos ? "LC_MENU_rally_cap" : "LC_MENU_attack_cap";
                UIHelper.SetButtonText(m_ButtonTextObj, LocalizationMgr.Get(key));

                labNode.SetActive(false);
                int _entityLevel = m_EntityInfo.WorldBoss != null ? m_EntityInfo.WorldBoss.level : m_EntityInfo.NpcTroop.level;


                int _realLevel = (FightManager.I.TrainLevel/*GameLevelManager.I.Soldier.Opensheet.TrainLevel*/ + _entityLevel) > 0 ? (FightManager.I.TrainLevel/*GameLevelManager.I.Soldier.Opensheet.TrainLevel*/ + _entityLevel) : 1;
                int _level = m_EntityInfo.WorldBoss != null ? m_EntityInfo.WorldBoss.level : m_EntityInfo.NpcTroop.dynamic == true ? _realLevel : _entityLevel;


                //if (m_Config.Lvl > CD2Config.I(149).Val)
                //	m_ToggleObj.SetActive(!m_IsBoos);
                // if (_level > MetaConfig.PveKeepPosition)
                //     m_ToggleObj.SetActive(!m_IsBoos);
                RemoveListener(EventTriggerType.Click, $"{CONTENT_PATH}/Image/BtnGather");
                AddListener(EventTriggerType.Click, $"{CONTENT_PATH}/Image/BtnGather", OnAttackClick);
            }


        }


        protected override void OnShown()
        {
            OnInit();

            base.OnShown();

            if (m_EntityInfo != null)
            {
                var key = m_IsBoos ? "LC_MENU_rally_cap" : "LC_MENU_attack_cap";
                m_ButtonTextObj = GetChild($"{CONTENT_PATH}/Image/BtnGather/UniformText");

                UIHelper.SetButtonText(m_ButtonTextObj, LocalizationMgr.Get(key));

                //OnLoadInfoPanel();
                SetToggle();
                SetNpcRemainTime();
                SetAttributeIcon();
                SetNpcRewards();
                SetTitleBar();
                SetNpcHead();
                RefreshPreConditionDescription();

                SetMaxChallengeLevelText();
                SetWorldBossInfo();
                AddListener();
                
                mCostTxt.text = MetaConfig.PveCostEnergy[0].Val.ToString();
            }
            else
            {
                Debug.LogWarning("m_EntityInfo is null!");
                WndMgr.Hide<UINpcTroopPopup>();
            }


        }

        /// <summary>
        /// 设置最大等级挑战显示
        /// </summary>
        private void SetMaxChallengeLevelText()
        {
            //var npcId = LSearch.I.CurrItem.SearchId; 为啥从这取。。。
            //var npcConfig = CD2NpcTroopClass.I(npcId);
            //var maxText = LocalizationMgr.Get(LSearch.I.CurrItem.Desc);
            if (m_EntityInfo != null && m_Config != null)
            {

                if (m_EntityInfo.type == MapUnitType.MapUnitWorldBoss)
                {
                    _maxChallengeLevel.gameObject.SetActive(false);
                }
                else
                {
                    var maxText = 0;
                    if (m_Config != null)
                    {
                        var preAttackId = LPlayer.I.GetKillOrderData(m_Config.Id);
                        maxText = preAttackId == -1 ? 1 : (preAttackId + 1);
                    }
                    else
                    {
                        maxText = 0;
                    }
                    int _entityLevel = m_EntityInfo.NpcTroop.level;
                    int _realLevel = (FightManager.I.TrainLevel + _entityLevel) > 0 ? (FightManager.I.TrainLevel + _entityLevel) : 1;
                    int _level = m_EntityInfo.NpcTroop.dynamic == true ? _realLevel : _entityLevel;

                    if (_level > maxText)
                    {
                        _maxChallengeLevel.gameObject.SetActive(true);
                        //_maxChallengeLevel.text = $"{LocalizationMgr.Get("LC_NPC_npc_barbarian_locked")}{maxText}";
                        //_maxChallengeLevel.text = string.Format(LocalizationMgr.Get("Skill_Info_Level"), maxText);
                        _maxChallengeLevel.text = $"{LocalizationMgr.Get("NPC_npc_barbarian_locked")}{string.Format(LocalizationMgr.Get("Skill_Info_Level"), maxText)}";
                    }
                    else
                    {
                        _maxChallengeLevel.gameObject.SetActive(false);
                    }

                }

                var root = GetChild("centre_other");
                RectTransform rect = UIHelper.GetComponent<RectTransform>(root, "animation/ContentDetail/Image");
                LayoutRebuilder.ForceRebuildLayoutImmediate(rect);
            }
        }

        protected override void RegisterEvent()
        {
            base.RegisterEvent();
            EventMgr.RegisterEvent(TEventType.EntityUpdate, OnEntityUpdate, this);
            //EventMgr.RegisterEvent(TEventType.ChatGetAllUnionCustomRooms, (args) => { SendShareCoordinateToUnionChannel(); },this);
        }

        /// <summary>
        /// 刷新世界boss
        /// </summary>
        /// <param name="args"></param>
        private void OnEntityUpdate(object[] args)
        {
            var entityInfo = (EntityInfo)args[0];
            var id = entityInfo.ID;
            if (id == m_EntityID)
            {
                this.m_EntityInfo = entityInfo;
                if (this.m_EntityInfo != null && this.m_EntityInfo.type == MapUnitType.MapUnitWorldBoss)
                {
                    this.SetWorldBossInfo();
                }
            }
        }

        protected override void OnHidden()
        {
            base.OnHidden();
            EventMgr.UnregisterEvent(TEventType.EntityUpdate, this);
            RemoveListener();

            NTimer.SafeDestroy(ref m_Timer);

            //var ui = WndMgr.Get<UIBubbleTip>();
            //if (ui != null)
            //{
            //    WndMgr.Hide<UIBubbleTip>();
            //}
        }

        protected override void OnDestroyed()
        {
            NTimer.SafeDestroy(ref m_Timer);
        }

        #endregion
    }
}
