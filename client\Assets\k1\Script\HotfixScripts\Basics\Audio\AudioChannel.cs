﻿using Game.Config;
using System.Collections.Generic;
using UnityEngine;

namespace TFW
{

    /// <summary>
    /// 声音通道
    /// </summary>
    public class AudioChannel
    {

        #region 属性字段

        /// <summary>
        /// 声音通道
        /// </summary>
        private AudioChannelType _channel;

        /// <summary>
        /// 通道声音数量
        /// </summary>
        private int _capacity = 1;

        /// <summary>
        /// 声音缩放倍数（正常播放的音效）
        /// </summary>
        private float _soundVolumeScaleFactor = 1f;

        /// <summary>
        /// 背景音缩放倍数（正常播放的音效）
        /// </summary>
        private float _bgVolumeScaleFactor = 1f;

        /// <summary>
        /// 播放速度倍数
        /// </summary>
        private float _speedScaleFactor = 1f;

        /// <summary>
        /// 正在播放声音列表
        /// </summary>
        private List<AudioObject> _playingAudioObjectsList;

        /// <summary>
        /// 单个声音停止过渡（不是整个通道都渐弱）
        /// </summary>
        private List<AudioObject> _stopLerpAudioObjectsList;

        ///// <summary>
        ///// 正在播放的声音数量
        ///// </summary>
        //public int PlayingAudiosCount
        //{
        //    get { return _playingAudioObjectsList?.Count ?? 0; }
        //}

        /// <summary>
        /// 音频对象停止Pending队列
        /// </summary>
        private Dictionary<int, AudioObject> _pendingStopAudioObjectsDic;

        /// <summary>
        /// 当前所有的音频对象
        /// </summary>
        private Dictionary<int, AudioObject> _audioObjectsDic;

        /// <summary>
        /// 当前空闲的声音队列
        /// </summary>
        private Queue<AudioObject> _idleAudioObjectsQueue;

        /// <summary>
        /// 当前所有的AudioClip
        /// </summary>
        private Dictionary<string, AudioClip> _audioClipsDic;

        /// <summary>
        /// 正在播放的排序字典
        /// </summary>
        private SortedDictionary<int, List<AudioObject>> _playingSortedDic;

        /// <summary>
        /// 父对象 tran
        /// </summary>
        private Transform _tran;

        /// <summary>
        /// 启用音频管理器标识
        /// </summary>
        private bool _enable = true;

        /// <summary>
        /// 声音过渡时间
        /// </summary>
        private float _lerpTime = -1;

        /// <summary>
        /// 声音通道缩放倍数
        /// </summary>
        private float audioChannelVolumeTime = 1;

        public const bool EnableLog=false;

        #endregion


        #region 初始化

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="channelType"></param>
        /// <param name="capacity"></param>
        /// <param name="tran"></param>
        public AudioChannel(AudioChannelType channelType, int capacity,
            Transform tran)
        {
            this._channel = channelType;
            this._capacity = capacity;
            this._tran = tran;

            //信息数据
            Init();
        }

        /// <summary>
        /// 初始化
        /// </summary>
        private void Init()
        {
            _audioClipsDic = new Dictionary<string, AudioClip>();
            _audioObjectsDic = new Dictionary<int, AudioObject>();
            _idleAudioObjectsQueue = new Queue<AudioObject>();
            _playingAudioObjectsList = new List<AudioObject>();
            _stopLerpAudioObjectsList = new List<AudioObject>();
            _pendingStopAudioObjectsDic = new Dictionary<int, AudioObject>();
            _playingSortedDic = new SortedDictionary<int, List<AudioObject>>();

            var maxLimit = Mathf.Max(MetaConfig.SoundSceneLimit) + 100;
            maxLimit = (int)_channel * maxLimit;

            for (var i = maxLimit; i < maxLimit + _capacity; i++)
            {
                var obj = new AudioObject(_channel, i + 1);
                obj.gameObject.transform.SetParent(_tran);
                _audioObjectsDic[i + 1] = obj;
                _idleAudioObjectsQueue.Enqueue(obj);
            }
        }

        #endregion


        #region 播放

        /// <summary>
        /// 播放指定音频
        /// </summary>
        /// <param name="assetPath">资源名称</param>
        /// <param name="time">播放时长</param>
        /// <param name="volume">音量</param>
        /// <param name="speed">播放速度</param>
        /// <param name="priority">播放优先级</param>
        /// <param name="playMode">音效类型</param>
        /// <param name="is3DAudio">是否为3D音效</param>
        /// <returns>音频对象ID</returns>
        public int Play(string assetPath, float time, float volume, float speed, int priority = 0,
            AudioPlayMode playMode = AudioPlayMode.Normal,
            bool is3DAudio = false,
            Transform parent = null)
        {
            //Debug.Log($"[AudioManager] Play audio: {assetPath}");
            if (!_enable
                || string.IsNullOrEmpty(assetPath)
                || volume <= 0
                || audioChannelVolumeTime <= 0)
            {
                return -1;
            }

            parent = parent == null ? _tran : parent;
            switch (playMode)
            {
                case AudioPlayMode.Normal:
                    return PlayNormal(assetPath, time, volume, speed, priority, is3DAudio, parent, playMode);
                case AudioPlayMode.Unique:
                    return PlayUnique(assetPath, time, volume, speed, priority, is3DAudio, parent);
                case AudioPlayMode.BGM:
                    return PlayNoLoopBGM(assetPath, time, volume, speed, priority, is3DAudio, parent);
                case AudioPlayMode.Preemptive:
                    return PlayPreemptive(assetPath, time, volume, speed, priority, is3DAudio, parent);
                case AudioPlayMode.UnPreemptive:
                    return PlayUnPreemptive(assetPath, time, volume, speed, priority, is3DAudio, parent);
                default:
                    return -1;
            }
        }


        /// <summary>
        /// 常规播放模式
        /// </summary>
        private int PlayNormal(string assetPath, float time, float volume, float speed, int priority,
            bool is3DAudio, Transform parent, AudioPlayMode audioPlayMode)
        {
            //如果当前为静音状态，那么就不播放
            if (audioPlayMode == AudioPlayMode.Unique
                || audioPlayMode == AudioPlayMode.BGM)
            {
                if (_bgVolumeScaleFactor <= 0)
                    return -1;
            }
            else
            {
                if (_soundVolumeScaleFactor <= 0)
                    return -1;
            }

            if (TryGetAudioObject(priority, out var obj))
            {
                // TODO AudioClip管理/预加载/异步加载...
                if (!_audioClipsDic.TryGetValue(assetPath, out var clip))
                {
                    
                    clip = ResourceMgr.LoadAsset<AudioClip>(assetPath);
                    //Debug.Assert(clip != null, $"[AudioManager] Failed to load AudioClip {assetPath}.");
                    _audioClipsDic[assetPath] = clip;
                }

                //音效播放的声音大小
                var playVolume = volume * (audioPlayMode == AudioPlayMode.Unique || audioPlayMode == AudioPlayMode.BGM ? _bgVolumeScaleFactor : _soundVolumeScaleFactor)
                    * audioChannelVolumeTime;

                obj.AudioAssetPath = assetPath;
                obj.Play(clip, time, volume, playVolume, speed * _speedScaleFactor, priority, is3DAudio, audioPlayMode);
                if (obj.transform)
                {
                    obj.transform.SetParent(parent);
                    obj.transform.localPosition = Vector3.zero;
                }
                return obj.ID;
            }

            return -1;
        }

        /// <summary>
        /// 全局唯一播放模式
        /// </summary>
        private int PlayUnique(string assetPath, float time, float volume, float speed, int priority,
            bool is3DAudio, Transform parent)
        {
            var obj = FindPlayingAudioObject(assetPath);
            if (obj != null)
            {
                return obj.ID;
            }
            return PlayNormal(assetPath, time, volume, speed, priority, is3DAudio, parent, AudioPlayMode.Unique);
        }

        /// <summary>
        /// 播放非循环的背景音乐
        /// </summary>
        private int PlayNoLoopBGM(string assetPath, float time, float volume, float speed, int priority,
            bool is3DAudio, Transform parent)
        {
            var obj = FindPlayingAudioObject(assetPath);
            if (obj != null)
            {
                return obj.ID;
            }
            return PlayNormal(assetPath, time, volume, speed, priority, is3DAudio, parent, AudioPlayMode.BGM);
        }

        /// <summary>
        /// 抢占式播放: 若有相同音频播放直接重新开始
        /// </summary>
        private int PlayPreemptive(string assetPath, float time, float volume, float speed, int priority,
            bool is3DAudio, Transform parent)
        {
            var obj = FindPlayingAudioObject(assetPath);
            if (obj != null)
            {
                obj.Restart();
                return obj.ID;
            }

            return PlayNormal(assetPath, time, volume, speed, priority, is3DAudio, parent, AudioPlayMode.Preemptive);
        }
        /// <summary>
        /// 非抢占式播放: 若有相同音频播放不再处理
        /// </summary>
        private int PlayUnPreemptive(string assetPath, float time, float volume, float speed, int priority,
            bool is3DAudio, Transform parent)
        {
            var obj = FindPlayingAudioObject(assetPath);
            if (obj != null)
            {
                return obj.ID;
            }
            return PlayNormal(assetPath, time, volume, speed, priority, is3DAudio, parent, AudioPlayMode.UnPreemptive);
        }

        /// <summary>
        /// 抢占式播放，如有相同的音频播放进度超过一定进度就直接重新开始
        /// </summary>
        /// <param name="assetPath"></param>
        /// <param name="time"></param>
        /// <param name="progress"></param>
        /// <returns></returns>
        public int PlayPreemptive(string assetPath, float time, float progress = 0.5f)
        {
            if (!_enable || string.IsNullOrEmpty(assetPath))
            {
                return -1;
            }

            var obj = FindPlayingAudioObject(assetPath);
            if (obj != null)
            {
                if (obj.Progress >= progress)
                {
                    obj.Restart();
                }

                return obj.ID;
            }

            return -1;
        }

        #endregion


        #region 数据刷新


        /// <summary>
        /// 帧更新: 完成音频播放/停止的Pending操作
        /// </summary>
        public void Update(float deltaTime)
        {

            //刷新单个声音过渡处理
            StopSingleAudioLerp(deltaTime);

            //刷新声音过渡处理
            StopAllLerpTime(deltaTime);

            if (_pendingStopAudioObjectsDic == null)
                return;

            foreach (var obj in _pendingStopAudioObjectsDic)
            {
                obj.Value.Stop();
                ReleaseAudioObject(obj.Value);
            }

            _pendingStopAudioObjectsDic.Clear();
            foreach (var obj in _playingAudioObjectsList)
            {
                if (!obj.LoopTime && Time.realtimeSinceStartup > obj.EndTime)
                {
                    _pendingStopAudioObjectsDic[obj.ID] = obj;
                }
            }
        }

        /// <summary>
        /// 销毁
        /// </summary>
        public void OnDestroy()
        {
            foreach (var obj in _audioObjectsDic.Values)
            {
                obj.Destroy();
            }

            if (_audioClipsDic != null)
            {
                _audioClipsDic.Clear();
                _audioClipsDic = null;
            }

            if (_playingSortedDic != null)
            {
                foreach (var item in _playingSortedDic)
                {
                    item.Value.Clear();
                }

                _playingSortedDic.Clear();
            }

            _audioObjectsDic.Clear();
            _idleAudioObjectsQueue.Clear();
            _playingAudioObjectsList.Clear();
            _pendingStopAudioObjectsDic.Clear();
        }

        /// <summary>
        /// 动态修改当前正在播放的声音大小
        /// </summary>
        /// <param name="isSound">true 音效, false 背景音乐</param>
        public void OnValidate(bool isSound, float scaleFactor)
        {
            if (_playingAudioObjectsList != null && _channel != AudioChannelType.HeroAudio)
            {
                var b = 0;
                //var audioPlayMode = AudioPlayMode.Normal;
                if (isSound)
                {
                    //audioPlayMode = AudioPlayMode.Normal;
                    _soundVolumeScaleFactor = scaleFactor;
                    b = 1 << (int)AudioPlayMode.Normal;
                }
                else
                {
                    //audioPlayMode = AudioPlayMode.Unique;
                    _bgVolumeScaleFactor = scaleFactor;
                    b = 1 << (int)AudioPlayMode.Unique | 1 << (int)AudioPlayMode.BGM;
                }

                foreach (var obj in _playingAudioObjectsList)
                {

                    if (obj != null
                        && IsTargetAudioPlayMode(obj.AudioPlayMode, b))
                    {
                        //音效播放的声音大小
                        var playVolume = obj.SrcVolume * scaleFactor;
                        obj.Volume = playVolume;
                    }
                }
            }
        }


        public void OnValidateHeroSound(float scaleFactor)
        {
            if (_playingAudioObjectsList != null && _channel == AudioChannelType.HeroAudio)
            {
                _soundVolumeScaleFactor = scaleFactor;

                foreach (var obj in _playingAudioObjectsList)
                {
                    if (obj != null)
                    {
                        //音效播放的声音大小
                        var playVolume = obj.SrcVolume * scaleFactor;
                        obj.Volume = playVolume;
                    }
                }
            }
        }

        /// <summary>
        /// 是否为目标的声音播放模式
        /// </summary>
        /// <param name="mode"></param>
        /// <param name="b"></param>
        /// <returns></returns>
        private bool IsTargetAudioPlayMode(AudioPlayMode mode, int b)
        {
            return (1 << (int)mode & b) >= 1;
        }


        /// <summary>
        /// 通过资源路径查找正在播放的音频对象
        /// </summary>
        private AudioObject FindPlayingAudioObject(string assetPath)
        {
            AudioObject obj = null;
            for (int i = 0; i < _stopLerpAudioObjectsList.Count; i++)
            {
                obj = _stopLerpAudioObjectsList[i];
                if (obj != null
                    && string.Equals(obj.AudioAssetPath, assetPath))
                {
                    //恢复声音播放数据
                    var volScale = 1f;
                    if (obj.AudioPlayMode == AudioPlayMode.Unique
                        || obj.AudioPlayMode == AudioPlayMode.BGM)
                        volScale = _bgVolumeScaleFactor;
                    else
                        volScale = _soundVolumeScaleFactor;

                    obj.Volume = obj.SrcVolume * volScale * audioChannelVolumeTime;
                    obj.UpdateStopLerpTime(-1f);

                    //移除数据信息
                    _stopLerpAudioObjectsList.RemoveAt(i);
                    //如果存在，那么基本上可以认为在play列表中
                    return obj;
                }
            }

            return _playingAudioObjectsList.Find(audio => audio.AudioAssetPath == assetPath);
        }

        /// <summary>
        /// 尝试从空闲队列中取音频对象
        /// </summary>
        private bool TryGetAudioObject(int priority, out AudioObject obj)
        {

            if (_channel == AudioChannelType.BGM || _channel== AudioChannelType.GuidHeroAudio)
            {
                if (_playingAudioObjectsList.Count > 0)
                {
                    obj = _playingAudioObjectsList[0];
                    return true;
                }
                //增加BGM 特殊处理
            }


            if (_idleAudioObjectsQueue.Count == 0)
            {
                //监测当前正在播放的声音优先级
                List<AudioObject> temp = null;
                foreach (var item in _playingSortedDic)
                {
                    temp = item.Value;
                    if (temp.Count > 0 && priority > item.Key)
                    {
                        //var indexId = temp.Count - 1;
                        var indexId = 0;
                        obj = temp[indexId];
                        temp.RemoveAt(indexId);
                        return true;
                    }
                }

                //Debug.LogError("[AudioManager] Not any available audio object.");
                obj = null;
                return false;
            }

            obj = _idleAudioObjectsQueue.Dequeue();
            _playingAudioObjectsList.Add(obj);

            //添加到排序字典当中
            if (!_playingSortedDic.TryGetValue(priority, out var list))
            {
                list = new List<AudioObject>();
                _playingSortedDic[priority] = list;
            }
            list.Add(obj);


            return true;
        }

        /// <summary>
        /// 修改声音通道音量倍数
        /// </summary>
        /// <param name="times"></param>
        public void ModifyOtherAudioChannelVolume(float volumeTimes)
        {
            audioChannelVolumeTime = volumeTimes;
            foreach (var obj in _playingAudioObjectsList)
            {
                if (obj.AudioPlayMode == AudioPlayMode.Unique
                    || obj.AudioPlayMode == AudioPlayMode.BGM)
                    obj.Volume = obj.SrcVolume * volumeTimes * _bgVolumeScaleFactor;
                else
                    obj.Volume = obj.SrcVolume * volumeTimes * _soundVolumeScaleFactor;
            }
        }


        #endregion





        #region 暂停播放

        /// <summary>
        /// 暂停
        /// </summary>
        public bool Pause(int id)
        {

            if (_playingAudioObjectsList == null)
                return false;

            for (int i = 0; i < _playingAudioObjectsList.Count; i++)
            {
                if (_playingAudioObjectsList[i].ID == id)
                {
                    _playingAudioObjectsList[i].Pause();
                    return true;
                }
            }

            return false;
        }
        public void PauseAll()
        {

            if (_playingAudioObjectsList == null)
                return;

            for (int i = 0; i < _playingAudioObjectsList.Count; i++)
            {

                _playingAudioObjectsList[i].Pause();

            }

        }
        public void ResumeAll()
        {

            if (_playingAudioObjectsList == null)
                return;

            for (int i = 0; i < _playingAudioObjectsList.Count; i++)
            {

                _playingAudioObjectsList[i].UnPause();

            }

        }
        /// <summary>
        /// 取消暂停
        /// </summary>
        public bool UnPause(int id)
        {

            if (_playingAudioObjectsList == null)
                return false;

            for (int i = 0; i < _playingAudioObjectsList.Count; i++)
            {
                if (_playingAudioObjectsList[i].ID == id)
                {
                    _playingAudioObjectsList[i].UnPause();
                    return true;
                }
            }

            return false;
        }


        #endregion


        #region 停止播放

        /// <summary>
        /// 停止某个频道的音效
        /// </summary>
        /// <param name="id">某一个声音ID</param>
        /// <param name="lerpTime">声音过渡时间</param>
        /// <returns></returns>
        public bool Stop(int id, float lerpTime = -1)
        {

            if (_audioObjectsDic.TryGetValue(id, out var obj))
            {
                if (lerpTime <= 0)
                {
                    //立即释放
                    if (obj.IsPlaying)
                    {
                        obj.Stop();
                        ReleaseAudioObject(obj);
                    }
                }
                else
                {
                    obj.UpdateStopLerpTime(lerpTime);

                    //添加到停止过渡效果
                    _stopLerpAudioObjectsList.Add(obj);
                }

                return true;
            }

            return false;
        }

        /// <summary>
        /// 单个声音停止过渡渐变
        /// </summary>
        /// <param name="deltaTime"></param>
        private void StopSingleAudioLerp(float deltaTime)
        {
            if (_stopLerpAudioObjectsList.Count == 0)
            {
                return;
            }

            AudioObject obj = null;
            var volScale = 1f;
            for (int i = _stopLerpAudioObjectsList.Count - 1; i >= 0; i--)
            {
                obj = _stopLerpAudioObjectsList[i];
                obj.stopLerpSurplusTime -= deltaTime;
                if (obj.stopLerpSurplusTime <= 0)
                {
                    obj.Stop();
                    ReleaseAudioObject(obj);
                    _stopLerpAudioObjectsList.RemoveAt(i);
                }

                if (obj.AudioPlayMode == AudioPlayMode.Unique
                        || obj.AudioPlayMode == AudioPlayMode.BGM)
                    volScale = _bgVolumeScaleFactor;
                else
                    volScale = _soundVolumeScaleFactor;

                obj.Volume = obj.SrcVolume * Mathf.Lerp(0, volScale, obj.stopLerpSurplusTime / obj.StopLerpTime) * audioChannelVolumeTime;
            }
        }


        /// <summary>
        /// 停止对应资源的音频
        /// </summary>
        /// <param name="assetPath"></param>
        /// <returns></returns>
        public bool Stop(string assetPath)
        {
            var obj = FindPlayingAudioObject(assetPath);
            if (obj != null)
            {
                obj.Stop();
                return true;
            }
            return false;
        }

        /// <summary>
        /// 停止所有的音频播放
        /// <param name="lerpTime">声音过渡时间</param>
        /// </summary>
        public void StopAll(float lerpTime = -1, bool isUnload = false, bool isStopImmediately = false)
        {

            this._lerpTime = lerpTime;
            if (lerpTime <= 0)
            {
                if (isStopImmediately)
                {
                    foreach (var obj in _playingAudioObjectsList)
                    {
                        if (obj != null)
                        {
                            obj.Stop();
                            ReleaseAudioObject(obj);
                        }
                    }
                }
                else
                {
                    foreach (var obj in _playingAudioObjectsList)
                    {
                        obj.IsUnloadAsset = isUnload;
                        _pendingStopAudioObjectsDic[obj.ID] = obj;
                    }
                }

                //清理正在播放的声音资源
                _playingAudioObjectsList.Clear();
            }
        }

        /// <summary>
        /// 在一段时间内过渡到目标
        /// </summary>
        /// <param name="deltaTime"></param>
        private void StopAllLerpTime(float deltaTime)
        {
            if (_lerpTime <= 0)
            {
                return;
            }

            var time = _lerpTime -= deltaTime;
            if (time <= 0)
            {
                //停止回收所有声音
                StopAll();
            }
            else
            {
                var volScale = 1f;
                foreach (var obj in _playingAudioObjectsList)
                {
                    if (obj == null)
                        continue;

                    if (obj.AudioPlayMode == AudioPlayMode.Unique
                        || obj.AudioPlayMode == AudioPlayMode.BGM)
                        volScale = _bgVolumeScaleFactor;
                    else
                        volScale = _soundVolumeScaleFactor;

                    obj.Volume = obj.SrcVolume * Mathf.Lerp(0, volScale, time) * audioChannelVolumeTime;
                }
            }
        }

        #endregion




        #region 资源释放


        /// <summary>
        /// 释放音频对象
        /// </summary>
        private void ReleaseAudioObject(AudioObject obj)
        {
            if (obj == null)
            {
                return;
            }

            if (obj.Is3DAudio)
            {
                if (obj.transform)
                {
                    obj.transform.SetParent(_tran);
                }
            }

            //移除排序列表中的数据
            if (_playingSortedDic.TryGetValue(obj.Priority, out var list))
            {
                for (int i = 0; i < list.Count; i++)
                {
                    if (list[i].ID == obj.ID)
                    {
                        list.RemoveAt(i);
                        break;
                    }
                }
            }

            if (_playingAudioObjectsList.Contains(obj))
                _playingAudioObjectsList.Remove(obj);
            _idleAudioObjectsQueue.Enqueue(obj);
        }

        #endregion


    }
}
