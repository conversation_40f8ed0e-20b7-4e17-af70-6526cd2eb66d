﻿using DeepUI;
using Game.Config;
using Game.Data;
using Logic;
using System.Collections.Generic;
using Cfg;
using Cfg.C;
using Cysharp.Threading.Tasks;
using K3;
using Public;
using TFW;
using TFW.Localization;
using TFW.UI;
using UI;
using UnityEngine;
using CD2Hero = Cfg.G.CD2Hero;
using Common;

namespace Game.Utils
{
    /// <summary>
    /// 英雄品质
    /// </summary>
    public enum HeroType
    {
        /// <summary>
        /// 绿
        /// </summary>
        Green = 1,

        /// <summary>
        /// 蓝
        /// </summary>
        Blue = 2,

        /// <summary>
        /// 紫
        /// </summary>
        Purple = 3,

        /// <summary>
        /// 橙
        /// </summary>
        Orange = 4
    }

    /// <summary>
    /// 英雄属性
    /// </summary>
    public enum HeroAttribute
    {
        /// <summary>
        /// 木
        /// </summary>
        Wood = 1,

        /// <summary>
        /// 水
        /// </summary>
        Water = 2,

        /// <summary>
        /// 火
        /// </summary>
        Fire = 3
    }

    public class HeroUtils
    {
        /// <summary>
        /// 颜色
        /// </summary>
        //private Color _color;
        ///// <summary>
        ///// 根据类型获取高光框
        ///// </summary>
        ///// <param name="type"></param>
        ///// <returns></returns>
        //public static int GetHightLightByType(HeroType type)
        //{
        //    switch (type)
        //    {
        //        case HeroType.Green:
        //            return HeroConst.HIGHT_LIGHT_GREEN;
        //        case HeroType.Blue:
        //            return HeroConst.HIGHT_LIGHT_BLUE;
        //        case HeroType.Purple:
        //            return HeroConst.HIGHT_LIGHT_PUBPLE;
        //        case HeroType.Orange:
        //            return HeroConst.HIGHT_LIGHT_ORANGE;
        //        default:
        //            return HeroConst.HIGHT_LIGHT_GREEN;
        //    }
        //}

        /// <summary>
        /// 获取品质的颜色
        /// </summary>
        /// <param name="heroType"></param>
        public static Color GetColorByType(HeroType heroType)
        {
            switch (heroType)
            {
                case HeroType.Green:
                    return ColorConst.HERO_GREEN_TEXT;
                case HeroType.Blue:
                    return ColorConst.HERO_BLUE_TEXT;
                case HeroType.Purple:
                    return ColorConst.HERO_PURPLE_TEXT;
                case HeroType.Orange:
                    return ColorConst.HERO_ORANGE_TEXT;
                default:
                    return ColorConst.HERO_GREEN_TEXT;
            }
        }

        /// <summary>
        /// 根据数据表配置的坐标值获取Vector3  "[0,0,0]"
        /// </summary>
        /// <param name="offsets"></param>
        /// <returns></returns>
        public static Vector3 ParseList(List<string> offsets)
        {
            if (offsets.Count > 2)
            {
                return new Vector3(float.Parse(offsets[0]), float.Parse(offsets[1]), float.Parse(offsets[2]));
            }
            else if (offsets.Count > 1)
            {
                return new Vector3(float.Parse(offsets[0]), float.Parse(offsets[1]), 0);
            }
            return Vector3.zero;
        }

        ///// <summary>
        ///// 根据类型获取攻击背景
        ///// </summary>
        ///// <param name="type"></param>
        ///// <returns></returns>
        //public static int GetAttackBgByType(HeroType type)
        //{
        //    switch (type)
        //    {
        //        case HeroType.Green:
        //            return HeroConst.ATTACK_BG_GREEN;
        //        case HeroType.Blue:
        //            return HeroConst.ATTACK_BG_BLUE;
        //        case HeroType.Purple:
        //            return HeroConst.ATTACK_BG_PURPLE;
        //        case HeroType.Orange:
        //            return HeroConst.ATTACK_BG_ORANGE;
        //        default:
        //            return HeroConst.ATTACK_BG_GREEN;
        //    }
        //}

        public static int GetHeroAttributeByAttribute(int attribute)
        {
            switch (attribute)
            {
                case 1:
                    return HeroConst.HERO_ATTRIBUTE_1;
                case 2:
                    return HeroConst.HERO_ATTRIBUTE_2;
                case 3:
                    return HeroConst.HERO_ATTRIBUTE_3;
                case 4:
                    return HeroConst.HERO_ATTRIBUTE_4;
                default:
                    return HeroConst.HERO_ATTRIBUTE_1;
            }
        }

        //public static int GetTitleBgWithHeroType(HeroType heroType)
        //{
        //    switch (heroType)
        //    {
        //        case HeroType.Green:
        //            return HeroConst.HERO_SHOWVIEW_TITLEBG_GREEN;
        //        case HeroType.Blue:
        //            return HeroConst.HERO_SHOWVIEW_TITLEBG_BLUE;
        //        case HeroType.Purple:
        //            return HeroConst.HERO_SHOWVIEW_TITLEBG_PURPLE;
        //        case HeroType.Orange:
        //            return HeroConst.HERO_SHOWVIEW_TITLEBG_ORANGE;
        //        default:
        //            return HeroConst.HERO_SHOWVIEW_TITLEBG_GREEN;
        //    }
        //}
        ///// <summary>
        ///// 获取部队的多语言状态
        ///// </summary>
        ///// <param name="state"></param>
        ///// <returns></returns>
        //public static string GetLineUpState(int state)
        //{
        //    switch (state)
        //    {
        //        //空闲
        //        case 0:
        //            return "Troops_Setting_Status1";
        //        //防守
        //        case 1:
        //            return "Troops_Setting_Status1";
        //        case 2:
        //            return "Troops_Setting_Status3";
        //        case 3:
        //            return "Troop_Status_Stationing";
        //        default:
        //            return string.Empty;

        //    }
        //}

        static int[] HERO_EXP = null, DOG_HERO_EXP = null;

        /// <summary>
        /// 获取英雄吞卡可获得经验
        /// </summary>
        /// <param name="heroData"></param>
        /// <returns></returns>
        public static long GetHeroExp(HeroData heroData)
        {

            if (heroData == null || heroData.HeroCfg == null)
                return 0;

            if (HERO_EXP == null)
            {
                HERO_EXP = new int[Cfg.C.CD2Config.I(HeroConst.HERO_EXP).Array.Count];
                for (int i = 0; i < HERO_EXP.Length; i++)
                {
                    HERO_EXP[i] = ConvertUtils.GetIntFromString(Cfg.C.CD2Config.I(HeroConst.HERO_EXP).Array[i]);
                }
            }

            if (DOG_HERO_EXP == null)
            {
                DOG_HERO_EXP = new int[Cfg.C.CD2Config.I(HeroConst.DOG_HERO_EXP).Array.Count];
                for (int i = 0; i < DOG_HERO_EXP.Length; i++)
                {
                    DOG_HERO_EXP[i] = ConvertUtils.GetIntFromString(Cfg.C.CD2Config.I(HeroConst.DOG_HERO_EXP).Array[i]);
                }
            }

            long exp = 0;
            long heroExp = 0;
            if (heroData.HeroCfg.Type == 1)
            {
                exp += HERO_EXP[heroData.HeroCfg.HeroType - 1];
            }
            else if (heroData.HeroCfg.Type == 2)
            {
                exp += DOG_HERO_EXP[heroData.HeroCfg.HeroType - 1];
            }

            //if (heroData.ModelId == modelId)
            //{
            //    exp *= 2;
            //}

            // Debug.LogErrorFormat($"经验获取: {GetExpByLevel(heroData)}..{heroData.Exp} .. {exp}");

            heroExp += GetExpByLevel(heroData);

            heroExp += heroData.Exp;

            heroExp = (long)(heroExp * Cfg.C.CD2Config.I(HeroConst.SWALLOWING_LOSS).Val + 0.5f);

            //if (heroData.Level > 1) //没有升级的等级大于1 的 英雄 不需要这个判断了
            //{
            //    return heroExp; //大于1级的卡，不进行基础经验的给予
            //}

            exp += heroExp;

            return exp;
        }

        /// <summary>
        /// 获取英雄吞卡可获得经验
        /// </summary>
        /// <param name="heroData"></param>
        /// <returns></returns>
        public static long GetHeroExp(HeroData heroData, int modelId)
        {

            if (heroData == null || heroData.HeroCfg == null)
                return 0;

            if (HERO_EXP == null)
            {
                HERO_EXP = new int[Cfg.C.CD2Config.I(HeroConst.HERO_EXP).Array.Count()];
                for (int i = 0; i < HERO_EXP.Length; i++)
                {
                    HERO_EXP[i] = ConvertUtils.GetIntFromString(Cfg.C.CD2Config.I(HeroConst.HERO_EXP).Array[i]);
                }
            }

            if (DOG_HERO_EXP == null)
            {
                DOG_HERO_EXP = new int[Cfg.C.CD2Config.I(HeroConst.DOG_HERO_EXP).Array.Count()];
                for (int i = 0; i < DOG_HERO_EXP.Length; i++)
                {
                    DOG_HERO_EXP[i] = ConvertUtils.GetIntFromString(Cfg.C.CD2Config.I(HeroConst.DOG_HERO_EXP).Array[i]);
                }
            }

            long exp = 0;
            long heroExp = 0;
            if (heroData.HeroCfg.Type == 1)
            {
                exp += HERO_EXP[heroData.HeroCfg.HeroType - 1];
            }
            else if (heroData.HeroCfg.Type == 2)
            {
                exp += DOG_HERO_EXP[heroData.HeroCfg.HeroType - 1];
            }

            if (heroData.ModelId == modelId)
            {
                exp *= 2;
            }

            // D.Error?.Log($"经验获取: {GetExpByLevel(heroData)}..{heroData.Exp} .. {exp}");

            heroExp += GetExpByLevel(heroData);

            heroExp += heroData.Exp;

            heroExp = (long)(heroExp * Cfg.C.CD2Config.I(HeroConst.SWALLOWING_LOSS).Val + 0.5f);

            if (heroData.Level > 1)
            {
                return heroExp; //大于1级的卡，不进行基础经验的给予
            }

            exp += heroExp;

            return exp;
        }

        /// <summary>
        /// 根据等级获取该英雄当前的经验
        /// </summary>
        /// <param name="heroData"></param>
        /// <returns></returns>
        private static long GetExpByLevel(HeroData heroData)
        {
            long exp = 0;

            for (int i = 1; i < heroData.Level; i++)
            {
                exp += CD2HeroExpExtension.GetHeroExp(heroData.HeroCfg.HeroExpId, i).Exp;
            }

            return exp;
        }

        /// <summary>
        /// 判断当前英雄添加经验后的等级
        /// </summary>
        /// <param name="heroData"></param>
        /// <param name="exp"></param>
        /// <returns></returns>
        public static int GetHeroLevel(HeroData heroData, long exp)
        {
            if (heroData != null && heroData.HeroCfg != null)
            {
                var hero = heroData;

                int level = hero.Level;
                // 应考虑该英雄已经有的那点经验
                exp += heroData.Exp;
                var heroExp = CD2HeroExpExtension.GetHeroExp(hero.HeroCfg.HeroExpId, level);
                while (exp >= 0)
                {
                    if (heroExp.Exp <= exp)
                    {
                        exp -= heroExp.Exp;
                        level++;
                        heroExp = CD2HeroExpExtension.GetHeroExp(hero.HeroCfg.HeroExpId, level);

                    }
                    else
                    {
                        break;
                    }
                }
                return level;
            }
            else
            {
                return 0;
            }


        }

        /// <summary>
        /// 获取当前英雄达到最大等级的经验
        /// </summary>
        /// <returns>小于等于0 说明不能升级了</returns>
        //public static long GetExpMaxLevel(HeroData heroData)
        //{
        //    if (heroData != null)
        //    {
        //        var hero = heroData;

        //        long needExp = heroData.Exp * -1;

        //        int level = hero.Level;

        //        if (level >= GameData.I.UnlockData.MaxHeroLevel)
        //        {
        //            return 0;
        //        }
        //        else
        //        {
        //            for (int i = level; i < GameData.I.UnlockData.MaxHeroLevel; i++)
        //            {
        //                var heroExp = CD2HeroExpExtension.GetHeroExp(hero.HeroCfg.HeroExpId, i);
        //                needExp += heroExp.Exp;
        //            }
        //        }

        //        return needExp;
        //    }
        //    else
        //    {
        //        return 0;
        //    }


        //}

        public static long GetExpToLevel(HeroData heroData, int toLevel)
        {
            if (heroData != null)
            {
                var hero = heroData;

                // long needExp = heroData.Exp;
                long needExp = 0;

                int level = hero.Level;

                if (level >= toLevel)
                {
                    D.Info?.Log($"等级所需=>{toLevel}：{0}");
                    return 0;
                }
                else
                {
                    for (int i = level; i < toLevel; i++)
                    {
                        var heroExp = CD2HeroExpExtension.GetHeroExp(hero.HeroCfg.HeroExpId, i);
                        needExp += heroExp.Exp;
                    }
                }

                D.Info?.Log($"等级所需=>{toLevel}：{needExp}");
                return needExp;
            }
            else
            {
                D.Info?.Log($"等级所需=>{toLevel}：{0}");
                return 0;
            }
        }

        ///// <summary>
        ///// 获取spine资源
        ///// </summary>
        ///// <param name="heroCfg"></param>
        ///// <returns></returns>
        //public static SkeletonDataAsset GetHeroSkeDataAsset(CD2Hero heroCfg)
        //{
        //    return ResourceMgr.LoadResource<SkeletonDataAsset>(heroCfg.BustIcon[0]);
        //    //return null;
        //}

        ///// <summary>
        ///// 获取spine的材质
        ///// </summary>
        ///// <param name="heroCfg"></param>
        ///// <returns></returns>
        //public static Material GetHeroSkeMaterial(CD2Hero heroCfg)
        //{
        //    return ResourceMgr.LoadMaterial(heroCfg.BustIcon[1]);
        //}


        public static void ShowAddQueue()
        {
            var showList = new List<ComplementPopData>();

            if (!MonthCardMgr.I.GetHavaCard(MonthCardEnum.Common))
            {
                showList.Add(new ComplementPopData(28, UIComplementPop.UIComplementPopItemEnum.MonthCard, ShopBtnTypeEnum.Desc, () =>
                {
                    if (UnlockMgr.I.CheckUnlock(UnlockFuncEnum.Shop))
                    {
                        PopupManager.I.ClosePopup<UIComplementPop>();
                        MonthCardMgr.I.OpenMonthCardPop(MonthCardEnum.Common);
                    }
                    else
                    {
                        UITools.PopTips(UnlockMgr.I.GetUnlockDesc(UnlockFuncEnum.Shop));
                    }
                }));
            }


            showList.Add(new ComplementPopData(31, UIComplementPop.UIComplementPopItemEnum.VIPShop, ShopBtnTypeEnum.Desc, () =>
            {
                if (UnlockMgr.I.CheckUnlock(UnlockFuncEnum.VIP))
                {
                    PopupManager.I.ClosePopup<UIComplementPop>();
                    VipManager.I.OpenVip();
                }
                else
                {
                    UITools.PopTips(UnlockMgr.I.GetUnlockDesc(UnlockFuncEnum.VIP));
                }

            }));


            foreach (var item in MetaConfig.Skill_Quality_ID_Group)
            {
                GameData.I.SkillData.MTechs.TryGetValue(item, out var techData);

                if (techData != null && techData.MaxLevel)
                    break;

                showList.Add(new ComplementPopData(30, UIComplementPop.UIComplementPopItemEnum.Tech, ShopBtnTypeEnum.Desc,
                                  () =>
                                  {
                                      if (UnlockMgr.I.CheckUnlock(UnlockFuncEnum.Building_6))
                                      {
                                          PopupManager.I.ClosePopup<UIComplementPop>();

                                          PopupManager.I.ShowLayer<UITechView>(null);
                                      }
                                      else
                                      {
                                          UITools.PopTips(UnlockMgr.I.GetUnlockDesc(UnlockFuncEnum.Building_6));
                                      }
                                  }));

                break;

            }

            PopupManager.I.ShowPanel<UIComplementPop>(new UIComplementPopData()
            {
                popShowEnum = UIComplementPop.UIComplementPopEnum.

                Queue,
                datas = showList
            });
        }



        /// <summary>
        /// 获取英雄立绘显示的IconId
        /// </summary>
        /// <param name="heroCfg"></param>
        /// <returns></returns>
        public static int GetHeroDrawIconId(CD2Hero heroCfg)
        {
            return ConvertUtils.GetIntFromString(heroCfg.InfoSpine[0]);
        }

        /// <summary>
        /// 创建英雄Spine到指定transform子级上
        /// 应用配置的位置与缩放，并整理特效层级
        /// </summary>
        public static async UniTask<GameObject> CreateHeroSpine(Cfg.G.CD2Hero heroCfg, Transform heroSpineParent, bool unscaledTime = true)
        {
            if (heroCfg != null)
            {
                var vt = await HeroUtils.GetHeroSpinePrefab(heroCfg);
                var spineObj = vt.obj;
                if (spineObj != null)
                {
                    spineObj.transform.SetParent(heroSpineParent, false);

                    var spineLocalPostion = HeroUtils.Parse(heroCfg.InfoSpine[1]);
                    spineObj.transform.localPosition = spineLocalPostion;
                    var spineLocalScale = HeroUtils.Parse(heroCfg.InfoSpine[2]);
                    spineObj.transform.localScale = spineLocalScale;

                    //整理特效层级
                    //var skeletonGraphic = spineObj.GetComponent<Spine.Unity.SkeletonGraphic>();
                    //skeletonGraphic.unscaledTime = unscaledTime;
                    //var popupEffect = Public.UIHelper.GetComponent<PopupEffectConfigurator>(spineObj);
                    //if (popupEffect != null && skeletonGraphic != null)
                    //{
                    //    var canvas = skeletonGraphic.GetComponentInParent<Canvas>();
                    //    if(canvas != null)
                    //        popupEffect.InitSortingRelativeByConfig(canvas.sortingLayerID, canvas.sortingOrder);
                    //}

                    return spineObj;
                }
            }
            return null;
        }

        public static async UniTask<(bool isUseDefaultAsset, GameObject obj)> GetHeroSpinePrefab(CD2Hero heroCfg)
        {
            if (heroCfg == null)
                return (false, null);
            // 将这个方法与下面的同名不同参的方法合并
            // return ResourceMgr.LoadGameObject(heroCfg.BustIcon[0], heroCfg.BustIcon[0]);
            return await GetHeroSpinePrefab(heroCfg.InfoSpine[0], heroCfg.InfoSpine[0]);
        }

        /// <summary>
        /// 获取英雄spine预设，同上
        /// </summary>
        public static async UniTask<(bool isUseDefaultAsset, GameObject obj)> GetHeroSpinePrefab(string path, string defaultAsset = null)
        {
            //判断当前包体内是否有spine资源，如果没有则替换为默认的heroid=101的spine资源返回
            if (!ResourceMgr.Exists(path))
            {
                path = (await Cfg.C.CD2Hero.GetConfigAsync(101)).InfoSpine[0];
            }

            return (false, await ResourceMgr.LoadInstanceAsync(path));
        }
        /// <summary>
        /// 根据数据表配置的坐标值获取Vector3  "(0,0,0)"
        /// </summary>
        /// <param name="vector3"></param>
        /// <returns></returns>
        public static Vector3 Parse(string vector3)
        {
            if (string.IsNullOrEmpty(vector3))
                return Vector3.zero;
            vector3 = vector3.Replace("(", "").Replace(")", "");
            string[] vector = vector3.Split(',');
            return new Vector3(float.Parse(vector[0]), float.Parse(vector[1]), float.Parse(vector[2]));
        }


        public static void ShowViewTOGetHero(Cfg.G.CD2Hero heroCfg, bool isUpgradeLevel = true)
        {
            /*
1：招募
2：最强指挥官
3：首充礼包
4：7日登录*
5.普通礼包 
6.每日礼包
7.VIP商店
8.活动跳转
*/
            var showList = new List<ComplementPopData>();


            switch (heroCfg.Access)
            {
                case 1:
                    showList.Add(new ComplementPopData(25, UIComplementPop.UIComplementPopItemEnum.HeroRecruit, ShopBtnTypeEnum.Desc, () =>
                    {
                        EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.CITY);
                        if (UnlockMgr.I.CheckUnlock(UnlockFuncEnum.Building_4))
                        {
                            HeroRecruitMgr.I.OpenHeroRecruit();
                            K3PlayerMgr.I.SetOrderState(K3PlayerMgr.orderState.Normal);
                        }
                        else
                        {
                            "UpgradeTips_TavernUnlock".ToShowFloatTips();
                        }

                    }));
                    break;
                case 2:
                    showList.Add(new ComplementPopData(0, UIComplementPop.UIComplementPopItemEnum.TopCommander, ShopBtnTypeEnum.Desc, () =>
                    {
                        
                        if (!UIActivityMain.ShowActivityByCfgId(1000004))
                        {
                            UITools.PopTips(LocalizationMgr.Get("Skin_manage_tips_01"));
                        }

                    }));

                    break;
                case 3:
                    showList.Add(new ComplementPopData(2, UIComplementPop.UIComplementPopItemEnum.HeroGift, ShopBtnTypeEnum.Desc, () =>
                    {
                        
                        // PopupManager.I.ShowPanel<UIIceBreakPop>(); //TaskUtils.JumpShop(ShopTab.LegacyTab, false);
                        // TODO: 破冰礼包 by xieqing
                    }));

                    break;
                case 4:
                    showList.Add(new ComplementPopData(0, UIComplementPop.UIComplementPopItemEnum.SeverLogin, ShopBtnTypeEnum.Desc, () =>
                    {
                         
                        if (!UIActivityMain.ShowActivityByCfgId(1000002) && !UIActivityMain.ShowActivityByCfgId(1000101))
                        {
                            UITools.PopTips(LocalizationMgr.Get("Skin_manage_tips_01"));
                        }

                    }));

                    break;
                case 5:
                    showList.Add(new ComplementPopData(2, UIComplementPop.UIComplementPopItemEnum.HeroGift, ShopBtnTypeEnum.Desc, () =>
                    {
                        // TaskUtils.JumpShop(ShopTab.GemRecharge, false); // TODO: 礼包跳转 by xieqing
                       
                    }));

                    break;
                case 6:
                    showList.Add(new ComplementPopData(2, UIComplementPop.UIComplementPopItemEnum.HeroGift, ShopBtnTypeEnum.Desc, () =>
                    {
                        // TaskUtils.JumpShop(ShopTab.LegacyTab, false); // TODO: 礼包跳转 by xieqing
                         
                    }));
                    break;
                case 7:
                    showList.Add(new ComplementPopData(21, UIComplementPop.UIComplementPopItemEnum.VIPShop, ShopBtnTypeEnum.Desc, () =>
                    {
                        
                        VipManager.I.OpenVip();
                    }));

                    break;
                case 8:
                    showList.Add(new ComplementPopData(0, UIComplementPop.UIComplementPopItemEnum.CommonAcivity, ShopBtnTypeEnum.Desc, () =>
                       {
                            
                           bool showActivity = false;
                           var activityID = heroCfg.AccessParam1;

                           if (UIActivityMain.ShowActivityByCfgId(activityID))
                           {
                               showActivity = true;
                           }

                           if (!showActivity)
                           {
                               UITools.PopTips(LocalizationMgr.Get("Skin_manage_tips_01"));
                           }
                           else
                           {
                               PopupManager.I.ClosePopup<UIComplementPop>();
                           }
                       }));
                    break;
                default:
                    Debug.LogWarningFormat($"主要获取途径尚未实现{heroCfg.Access}");


                    break;
            }
            if (isUpgradeLevel)//英雄升级判断背包内是否有可使用道具
            {
                var items = Cfg.C.CD2Config.I(36);
                var itemCfg = Cfg.C.CItem.I(int.Parse(items.Array[0]));
                var bagDatas = BagMgr.I.GetBagShowData();
                long count = 0;
                long count1 = 0;

                foreach (var item in bagDatas)
                {
                    if (itemCfg.Id == item.ConfigID)
                    {
                        count = item.Count;
                        break;
                    }
                }
                if (count > 0)
                {
                    showList.Add(new ComplementHeroData(heroCfg.Access, UIComplementPop.UIComplementPopItemEnum.GetHero1, ShopBtnTypeEnum.UseHeroItem, itemCfg, count,
                                () =>
                                {
                                    PopupManager.I.ShowDialog<UIBuyItemPop>(new UIBuyItemPopData()
                                    {
                                        Item = itemCfg,
                                        NeedNum = (int)count,
                                    });
                                    PopupManager.I.ClosePopup<UIComplementPop>();
                                },
                               () =>
                               {
                                   PopupManager.I.ClosePopup<UIComplementPop>();
                                   PlayerAssetsMgr.I.UseItem(new ItemData() { Cfg = itemCfg, Count = count });

                               }));
                }
                var itemCfg1 = Cfg.C.CItem.I(int.Parse(items.Array[1]));

                foreach (var item in bagDatas)
                {
                    if (itemCfg1.Id == item.ConfigID)
                    {
                        count1 = item.Count;
                        break;
                    }
                }
                if (count1 > 0)
                {
                    showList.Add(new ComplementHeroData(heroCfg.Access, UIComplementPop.UIComplementPopItemEnum.GetHero2, ShopBtnTypeEnum.UseHeroItem, itemCfg1, count1,
                                () =>
                                {
                                    PopupManager.I.ShowDialog<UIBuyItemPop>(new UIBuyItemPopData()
                                    {
                                        Item = itemCfg1,
                                        NeedNum = (int)count1,
                                    });
                                    PopupManager.I.ClosePopup<UIComplementPop>();
                                },
                               () =>
                               {
                                   PopupManager.I.ClosePopup<UIComplementPop>();
                                   PlayerAssetsMgr.I.UseItem(new ItemData() { Cfg = itemCfg1, Count = count1 });

                               }));
                }
            }

            if (Cfg.C.CD2CityBuilding.I((int)MainCityItem.CityType.Tavern * 1000)?.ReBuilded(true) == true)
            {
                var needData = new CommonItem.CommonItemData()
                {
                    Id = heroCfg.HeroItem,
                    Typ = AssetType.Item,
                    Val = 1,
                };

                PopupManager.I.ShowPanel<UIComplementPop>(new UIComplementPopData()
                {
                    popShowEnum = UIComplementPop.UIComplementPopEnum.Hero,
                    needData = needData,
                    datas = showList
                });
            }

        }

        public static bool GetHero(Cfg.G.CD2Hero heroCfg)
        {
            /*
1：招募
2：最强指挥官
3：首充礼包
4：7日登录*
5.普通礼包 
6.每日礼包
7.VIP商店
8.活动跳转
9.每日特购
*/
            switch (heroCfg.Access)
            {
                //case 1:
                //    HeroRecruitMgr.I.OpenHeroRecruit();
                //    //PopupManager.I.ShowLayer<UIHero_Upgrade_RecruitHero>("UIHero_Upgrade_RecruitHero");
                //    break;
                //case 2:
                //    if (!UIActivityMain.ShowActivityByCfgId(1000004))
                //    {
                //        UITools.PopTips(LocalizationMgr.Get("Skin_manage_tips_01"));
                //        return false;
                //    }
                //    break;
                case 3:
                   
                    PopupManager.I.ClearAllPopup();
                    // PopupManager.I.ShowPanel<UIIceBreakPop>(); //TaskUtils.JumpShop(ShopTab.LegacyTab, false);
                    // TODO: 破冰礼包 by xieqing
                    break;
                //case 4:
                //    if (!UIActivityMain.ShowActivityByCfgId(1000002) && !UIActivityMain.ShowActivityByCfgId(1000101))
                //    {
                //        UITools.PopTips(LocalizationMgr.Get("Skin_manage_tips_01"));
                //        return false;
                //    }
                //    break;
                //case 5:
                //    TaskUtils.JumpShop(ShopTab.GemRecharge, false);
                //    break;
                //case 6:
                //    TaskUtils.JumpShop(ShopTab.LegacyTab, false);
                //    break;
                //case 7:
                //    VipManager.I.OpenVip();
                //    break;
                //case 8:
                //    foreach (var item in heroCfg.AccessArray1)
                //    {
                //        if (int.TryParse(item, out var activityID) && UIActivityMain.ShowActivityByCfgId(activityID))
                //        {
                //            return true;
                //        }
                //    }
                //    UITools.PopTips(LocalizationMgr.Get("Skin_manage_tips_01"));
                //    return false;
                //case 9:
                //    TaskUtils.JumpShop(ShopTab.DailyDeal, ShopTabConfig.I.specialDealEntranceTabs.ToArray());
                //    break;
                default:
                    Debug.LogWarningFormat($"主要获取途径尚未实现{heroCfg.Access}");
                    HeroRecruitMgr.I.OpenHeroRecruit();
                    //PopupManager.I.ShowLayer<UIHero_Upgrade_RecruitHero>("UIHero_Upgrade_RecruitHero");
                    return false;
            }

            return true;
        }

        /// <summary>
        /// 获取当前英雄部队战力
        /// </summary>
        /// <param name="HeroModel">主将ID</param>
        /// <returns></returns>
        public static cspb.ShowPower GetHeroPowerFrom(int HeroModel, bool netReq = true)
        {
            if (netReq)
                HeroGameData.I.GetPower(true);

            if (HeroGameData.I.HeroShowPowers.TryGetValue(HeroModel, out var showPower))
            {
                return showPower;
            }

            HeroGameData.I.GetPower(true);
            return null;
        }

        #region 新升星效果处理

        /// <summary>
        /// 星级icon处理
        /// </summary>
        /// <param name="starIcon"></param>
        /// <param name="star"></param>
        /// <param name="step"></param>
        /// <param name="i"></param>
        public static void SetNewStarIcon(TFWImage starIcon, int star, int step, int i)
        {
            if (starIcon)
            {
                //UI_Hero_Star_01 1星 1阶
                //UI_Hero_Star_02 1星 2阶
                //UI_Hero_Star_03 1星 3阶
                //UI_Hero_Star_04 1星 4阶
                //UI_Hero_Star_05 1星 5阶 （升星了）
                //D.Error?.Log($"SetNewStarIcon star={star},step={step},i");
                if (star > i)
                {
                    step = 5;
                    UIUtils.SetImage2(starIcon, "HeroAtlas", string.Format("UI_Hero_Star_0{0}", step));
                }
                else if (star == i && step > 0)
                {
                    UIUtils.SetImage2(starIcon, "HeroAtlas", string.Format("UI_Hero_Star_0{0}", step));
                }
            }
        }

        /// <summary>
        /// 星级显示
        /// </summary>
        public static void RefreshStar(List<GameObject> starList, int star, int starStep)
        {
            for (int i = 0; i < starList.Count; i++)
            {
                var iconObj = starList[i];
                var show = star > i || (star == i && starStep > 0);
                starList[i].SetActive(show);

                var iconImg = iconObj.GetComponent<TFWImage>();
                if (show)
                {
                    SetNewStarIcon(iconImg, star, starStep, i);
                }
            }
        }

        /// <summary>
        /// 星级显示
        /// </summary>
        public static void RefreshStar(UIGrid starList, int starShowCount, int star, int starStep)
        {
            starList.Clear();

            for (int i = 0; i < starShowCount; i++)
            {
                var iconObj = starList.AddItem<Transform>().Find("Icon");
                var show = star > i || (star == i && starStep > 0);
                iconObj.gameObject.SetActive(show);
                var iconImg = iconObj.GetComponent<TFWImage>();
                if (show)
                {
                    SetNewStarIcon(iconImg, star, starStep, i);
                }
            }
        }

        /// <summary>
        /// 星级升级对比显示
        /// ⭐ -> ⭐
        /// </summary>
        public static void RefreshStar(UIGrid starList, int star, int starStep, bool onlySelf = false)
        {
            starList.Clear();

            var starShowCount = star;
            if (starStep > 0)
            {
                starShowCount += 1;
            }

            if (starShowCount == 0)
            {
                //0星0阶 到 0星1阶
                starShowCount += 1;
            }

            for (int i = 0; i < starShowCount; i++)
            {
                Transform iconObj = null;
                if (onlySelf)
                {
                    iconObj = starList.AddItem<Transform>();
                }
                else
                {
                    iconObj = starList.AddItem<Transform>().Find("Icon");
                }
                //0,0 | 0,1 | 0,2
                //1,0 | 1,1 | 1,2
                if (iconObj)
                {
                    var show = star > i || (star == i && starStep > 0);
                    iconObj.gameObject.SetActive(show);
                    var iconImg = iconObj.GetComponent<TFWImage>();
                    if (show)
                    {
                        SetNewStarIcon(iconImg, star, starStep, i);
                    }
                }

            }
        }

        #endregion

        public static GameObject GetHeroSpinePrefabSync(CD2Hero heroCfg)
        {
            if (heroCfg == null)
                return null;

            return ResourceMgr.LoadInstance(heroCfg.InfoSpine[0]);
        }

        /// <summary>
        /// 获取英雄spine预设，同上
        /// </summary>
        public static GameObject GetHeroSpinePrefabSync(string path)
        {
            return ResourceMgr.LoadInstance(path);
        }
    }
}
