﻿using Common;
using DeepUI;
using Logic;
using Public;
using TFW.UI;
using UI;
using UnityEngine;
using UnityEngine.EventSystems;
using EventTriggerType = TFW.EventTriggerType;


public enum AllianceTabEnum
{
    /// <summary>
    /// 无
    /// </summary>
    None = 0,
    /// <summary>
    /// 建立
    /// </summary>
    Create = 1,
    /// <summary>
    /// 加入
    /// </summary>
    Join = 2,
    /// <summary>
    /// 邀请/申请
    /// </summary>
    Invite = 3,
}

public class UINewAllianceStartData : PopupData
{
    public AllianceTabEnum tab = AllianceTabEnum.None;
}

/// <summary>
/// 未加入联盟时的面板
/// </summary>
[Popup("Alliance/New/UIAllianceNoAlliance", true)]
public partial class UINewAllianceStart : BasePopup
{
    /// <summary>
    /// tab页签
    /// </summary>
    [PopupField("Root/Tab")]
    private TFWTabGroup _tabGroup;

    /// <summary>
    /// 关闭按钮
    /// </summary>
    [PopupField("Root/BtnClose")]
    private GameObject closeBtn;

    [PopupText("Root/AllianceAttend/CentreOther/Animation/FastJoin/Text", "Alliance_Auto_Join_Cap")]
    TFWText btnText;

    /// <summary>
    /// 当前tab页签
    /// </summary>
    private AllianceTabEnum curTab;

    protected override void OnInit()
    {
        InitCreate();
        InitJoin();
        InitInvite();
        for (int i = 1; i < 4; i++)
        {
            var id = i;
            var tab = GetComponent<TFWTab>(_tabGroup.transform, $"tab{id}");
            tab.AddTabClickEvent((a, b) =>
            {
                OnTabClick((AllianceTabEnum)id);
            });
        }
        HideCreatePanel();
        HideJoinPanel();
        HideInvitePanel();
        AddListener(TFW.EventTriggerType.Click, closeBtn, OnClose);
    }

    protected internal override void OnDataReady()
    {
        base.OnDataReady();
    }

    protected internal override void OnOpenStart()
    {
        base.OnOpenStart();

        AllianceTabEnum temp = AllianceTabEnum.Create;
        if (Data != null)
        {
            var data = Data as UINewAllianceStartData;
            if(data!=null)
            {
                temp = data.tab;
            }
        }
        OnTabClick(temp);
        _tabGroup.TurnTabOn(((int)temp).ToString());
        EventMgr.RegisterEvent(TEventType.AllianceCreate, OnAllianceCreate, this);

        EventMgr.RegisterEvent(TEventType.AllianceRecommendUnionList, OnRecommendUnionUpdate, this);
        EventMgr.RegisterEvent(TEventType.AllianceQueryList, OnSearchUnionUpdate, this);
        EventMgr.RegisterEvent(TEventType.AllianceQueryError, OnSearchUnionError, this);
        EventMgr.RegisterEvent(TEventType.AllianceJoin, OnJoinUnionUpdate, this);
        EventMgr.RegisterEvent(TEventType.AllianceCancelApply, OnCancelApplyUnionUpdate, this);
        EventMgr.RegisterEvent(TEventType.AllianceApplyRecordChange, OnApplyRecordChange, this);

        // 事件会带回来一个List<UnionInfo>
        EventMgr.RegisterEvent(TEventType.AllianceBeInvitedRecordList, OnInvitedUpdate, this);
        EventMgr.RegisterEvent(TEventType.AllianceApplyRecordList, OnApplyUpdate, this);

        // 谁邀请了我
        LAllianceMgr.I.PlayerBeUnionInvitedRecordReq();
        // 我请求了谁
        LAllianceMgr.I.UnionApplyRecordReq();
    }

    protected internal override void OnCloseStart()
    {
        base.OnCloseStart();
        EventMgr.UnregisterEvent(TEventType.AllianceCreate, this);

        EventMgr.UnregisterEvent(TEventType.AllianceRecommendUnionList, this);
        EventMgr.UnregisterEvent(TEventType.AllianceQueryList, this);
        EventMgr.UnregisterEvent(TEventType.AllianceQueryError, this);
        EventMgr.UnregisterEvent(TEventType.AllianceJoin, this);
        EventMgr.UnregisterEvent(TEventType.AllianceCancelApply, this);
        EventMgr.UnregisterEvent(TEventType.AllianceApplyRecordChange, this);

        EventMgr.UnregisterEvent(TEventType.AllianceBeInvitedRecordList, this);
        EventMgr.UnregisterEvent(TEventType.AllianceApplyRecordList, this);
    }

    protected internal override void OnCloseComplete()
    {
        base.OnCloseComplete();
        //if(LPlayer.I.UnionID==0)
        //{
        //    EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.CITY);
        //}
        //else
        //{
        //    EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.ALLIANCE);
        //}
    }

    private void OnTabClick(AllianceTabEnum clickTab)
    {
        if (curTab != clickTab)
        {
            switch (curTab)
            {
                case AllianceTabEnum.Create:
                    HideCreatePanel();
                    break;
                case AllianceTabEnum.Join:
                    HideJoinPanel();
                    break;
                case AllianceTabEnum.Invite:
                    HideInvitePanel();
                    break;
                default:
                    break;
            }
            curTab = clickTab;
        }

        //显示页签
        switch (curTab)
        {
            case AllianceTabEnum.Create:
                ShowCreatePanel();
                break;
            case AllianceTabEnum.Join:
                ShowJoinPanel();
                break;
            case AllianceTabEnum.Invite:
                ShowInvitePanel();
                break;
            default:
                break;
        }
    }

    private void OnClose(GameObject arg0, PointerEventData arg1)
    {
        Close();
    }

    /// <summary>
    /// 取消申请后根据当前页面刷新 加入联盟界面/申请界面
    /// </summary>
    /// <param name="args"></param>
    private void OnCancelApplyUnionUpdate(object[] args)
    {
        var ok = (bool)args[0];

        if (ok)
        {
            if (curTab == AllianceTabEnum.Join)
            {
                ShowJoinPanel();
            }
            RefreshInvitePage();

        }
    }

    private void OnJoinUnionUpdate(object[] args)
    {
        if(args==null || args.Length<3)
        {
            return;
        }
        var ok = (bool)args[0];
        var isApply = (bool)args[2];
        if (ok)
        {
            if(!isApply)
                Close();
            else
            {
                if (curTab == AllianceTabEnum.Join)
                {
                    ShowJoinPanel();
                }
                // 发送请求用来更新红点
                LAllianceMgr.I.PlayerBeUnionInvitedRecordReq();
                LAllianceMgr.I.UnionApplyRecordReq();
            }
        }
    }
}
