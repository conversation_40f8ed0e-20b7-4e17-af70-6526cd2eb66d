﻿using Cfg.C;
using Common;
using cspb;
using Cysharp.Threading.Tasks;
using DeepUI;
using Game.Config;
using K1;
using Logic;
using Render;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using TFW;
using TFW.Localization;
using TFW.Map;
using UnityEngine;
using UnityEngine.EventSystems;
namespace UI
{
    [Popup("ThroneTowerWar/UIThroneBuildingInfo")]
    public class UIThroneWarfBuilding : HistoricPopup
    {

        #region 属性信息数据

        protected internal override bool PopBackEnabled => true;



        /// <summary>
        /// 设置和主菜单界面互斥
        /// </summary>
        public bool BottomMiddle { get; set; } = true;

        /// <summary>
        /// 建筑物信息
        /// </summary>
        private UIThroneWarBuildingInfo _buildingInfo;

        /// <summary>
        /// 哨塔信息数据
        /// </summary>
        private ThroneCityInfo _info = null;

        /// <summary>
        /// 界面打开时的数据信息
        /// </summary>
        private IHistoricPopupData _uiData;

        private UITimeSelect uiTimeSelect;
        #endregion


        #region 初始化

        /// <summary>
        /// 打开界面时的初始化操作
        /// </summary>
        protected override void OnInit()
        {
            base.OnInit();

        }

        /// <summary>
        /// 资源加载完成之后的处理
        /// </summary>
        protected override void OnLoad()
        {
            _buildingInfo = new UIThroneWarBuildingInfo(gameObject);

            //添加事件监听
            AddListener();
        }
        protected override void RemoveListener()
        {
            if (_buildingInfo != null)
                _buildingInfo.OnDestory();
            base.RemoveListener();
        }

        /// <summary>
        /// 按钮添加相应的事件监听
        /// </summary>
        protected override void AddListener()
        {
            uiTimeSelect = new UITimeSelect();
            uiTimeSelect.Init(_buildingInfo.TimeSelect);
            uiTimeSelect.SetVisable(true);
            //占领排行面板
            AddListener(TFW.EventTriggerType.Click, _buildingInfo.occupyDisBtn, OnClickOccupyDisBtn);
            AddListener(TFW.EventTriggerType.Click, _buildingInfo.shareBtn, OnClickShareBtn);
            AddListener(TFW.EventTriggerType.Click, _buildingInfo.favoriteBtn, OnClickFavoriteBtn);
            AddListener(TFW.EventTriggerType.Click, _buildingInfo.infoBtn, OnClickInfoBtn);

            AddListener(TFW.EventTriggerType.Click, _buildingInfo.attackBtn, OnClickAttackBtn); //需要分出攻击和驻守

            AddListener(TFW.EventTriggerType.Click, _buildingInfo.massBtn, OnClickDetailsBtn); //需要区分出部队详情和集结

            AddListener(TFW.EventTriggerType.Click, _buildingInfo.e_AttackBtn, OnClickAttackBtn); //攻击
            #region 先去掉侦查按钮
            _buildingInfo.e_InvestigateBtn?.SetActive(false);
            //AddListener(TFW.EventTriggerType.Click, _buildingInfo.e_InvestigateBtn, OnClickInvestigateBtn); //侦查
            #endregion

            AddListener(TFW.EventTriggerType.Click, _buildingInfo.e_MassBtn, OnRallyClick); //集结
        }

        private void OnClickOccupyDisBtn(GameObject arg0, PointerEventData arg1)
        {
            UniTask.Void(async () =>
            {
                var mapEntity = LMapEntityManager.I.GetEntityModule<LMapEntityThroneWarBuilding>();
                var cfg =await _info.BuildingCfg();
                mapEntity.SetCurrentThrone(true, LPlayer.I.CrossServerId, cfg == null ? "" : cfg.Name);
                mapEntity.GetOccupyTimeReq(_info.entityID);

            });
       
        }

        #region 分享，收藏，看规则
        /// <summary>
        /// 分享
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        private void OnClickShareBtn(GameObject arg0, PointerEventData arg1)
        {
            UniTask.Void(async () =>
            {
                if (_info != null && _buildingInfo != null)
                {
                    //显示提示信息
                    var pos = await _info.Pos();
                    var x = (int)pos.x;
                    var z = (int)pos.z;
                    ////分享{0}级{1}
                    var unitName = string.Format(LocalizationMgr.GetUIString("Share_chatdes_1"), _buildingInfo._UnionName.text, _buildingInfo._TitleText.text);
                    PopupManager.I.ShowPanel<UIShare>(new UIShareData()
                    {
                        chatContent = UIChatHelper.I.GetShareCoordinateToAllianceChannel
                                       (
                                         (await _info.BuildingCfg()).Name, 0,
                                          ShareDataTypeEnum.ThroneWar,
                                          unitName,
                                          x,
                                          z
                                       )
                    });

                }
            });
        }

        /// <summary>
        /// 收藏
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        private void OnClickFavoriteBtn(GameObject arg0, PointerEventData arg1)
        {
            UniTask.Void(async () =>
            {
                if (_info != null)
                {
                    var pos = await _info.Pos();
                    UIFavoritesTipsPanel.Show((int)pos.x, (int)pos.z, this._buildingInfo.GetName());
                }
            });
        }

        /// <summary>
        /// 点击查看信息按钮事件监听
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        private void OnClickInfoBtn(GameObject arg0, PointerEventData arg1)
        {
            string str = "KingWar_Help_2201";
            var actv = ActivityMgr.I.ChackActivityIsOpenByModuleId((int)ActivityType.ThroneWar);
            var CrossServer = ActivityMgr.I.ChackActivityIsOpenByModuleId((int)ActivityType.CrossServerThroneWar);
            if(actv == null && CrossServer == null)
            {
                str = "KingWar_Help_1001";
            }
            else
            {
                if(actv != null)
                {
                    str = "KingWar_Help_1001";
                }
                else if(CrossServer != null)
                {
                   str = "KingWar_Help_2301";
                }
            }
            PopupManager.I.ShowDialog<UIActivityRules>(
                new UIActivityRuleData()
                {
                    rules = str
                });
        }

        #endregion

        /// <summary>
        /// 当前实体信息刷新的时候，需要刷新显示相关
        /// </summary>
        protected override void RegisterEvent()
        {
            RegisterEvent(TEventType.EntityUpdate, OnEntityUpdate);
            RegisterEvent(TEventType.EntitiesUpdate, OnEntitiesUpdate);
        }

        #endregion

        #region 事件监听
        //侦查
        private void OnClickInvestigateBtn(GameObject arg0, PointerEventData arg1)
        {
            if (_info != null && _buildingInfo != null)
            {
                //LRecon.I.LaunchScout(_info.entityID, true);
                //PopupManager.I.ShowPanel<UIThroneWarTowerTroopDetail>(new UITowerTroopData() { entityId = _info.entityID });
                //PopupManager.I.ShowDialog<UIReconEffects>();
            }
        }

        /// <summary>
        /// 点击部队详情按钮事件监听（还可能是集结按钮）
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        ///guanxing(可能需要热更)
        private void OnClickDetailsBtn(GameObject arg0, PointerEventData arg1)
        {
            //王座实体不会产生位置移动大可不币看向目标
            //if (AutoLookTarget())
            //    return;
            if (_info != null && _buildingInfo != null)
            {
                //是自己联盟的显示部队详情按钮不是显示集结
                if (this._info.GetIsSelfAllianceOccupying())
                {
                    //部队详情
                    PopupManager.I.ShowPanel<UIThroneWarTowerTroopDetail>(new UITowerTroopData() { entityId = _info.entityID, IsThrone = true });
                    PopupManager.I.ClearPanel<UIThroneWarTowerBuilding>();
                }
                else
                {
                    OnRallyClick(arg0, arg1);
                }
            }
        }

        #region 集结相关
        //集结
        private async void OnRallyClick(GameObject arg0, PointerEventData arg1)
        {
            if (_info != null && _buildingInfo != null)
            {
                //var mapEntity = LMapEntityManager.I.GetEntityModule<LMapEntityThroneWarTower>();
                //var ok = _buildingInfo.MarchTowerPopTips(mapEntity.IsCanMarchTower(_info.entityID));
                //if (ok)
                //    return;
                if (WarTip.I.IsOpen() && _info.status != ThroneStatus.ThroneStatusNone)
                {
                    WarTip.I.PupopTip(() => { GoRally(); });
                }
                else if (LPlayer.I.ShieldOpen || LPlayer.I.GodShieldOpen)
                {
                    var btn2data = new MsgBoxBtnParam()
                    {
                        str = LocalizationMgr.Get("LC_MENU_cancel_cap"),
                    };
                    var btn1data = new MsgBoxBtnParam()
                    {
                        str = LocalizationMgr.Get("LC_MENU_confirm_cap"),
                        func = (o) =>
                        {
                            GoRally();
                        },
                    };

                    if (LPlayer.I.ShieldOpen && !LPlayer.I.GodShieldOpen)
                    {
                        UIMsgBox.Push(EMsgBoxType.two, LocalizationMgr.Get("LC_MENU_warning_cap"),
                            LocalizationMgr.Format("LC_MAP_broken_shield_desc", SCommon.FormatTimeString(LEnergyGauge.I.ShieldRemainTime / 1000), SCommon.FormatTimeString((int)(await CConstConfig.GetConfigAsync(10134008)).Val)),
                            btn1data,
                            btn2data, ButtonColorGroup.RedBlue);
                    }
                    else if (LPlayer.I.ShieldOpen && LPlayer.I.GodShieldOpen)
                    {
                        UIMsgBox.Push(EMsgBoxType.two, LocalizationMgr.Get("LC_MENU_warning_cap"),
                            LocalizationMgr.Format("LC_Castle_Protection_Attack2", SCommon.FormatTimeString(Mathf.Max((float)LEnergyGauge.I.ShieldRemainTime / 1000, MetaConfig.ShieldGodCD)), SCommon.FormatTimeString((int)(await CConstConfig.GetConfigAsync(10134008)).Val)),
                            btn1data,
                            btn2data, ButtonColorGroup.RedBlue);
                    }
                    else if (LPlayer.I.GodShieldOpen)
                    {
                        UIMsgBox.Push(EMsgBoxType.two, LocalizationMgr.Get("LC_MENU_warning_cap"),
                            LocalizationMgr.Format("LC_Castle_Protection_Attack1", SCommon.FormatTimeString(MetaConfig.ShieldGodCD), SCommon.FormatTimeString((int)(await CConstConfig.GetConfigAsync(10134008)).Val)),
                            btn1data,
                            btn2data, ButtonColorGroup.RedBlue);
                    }

                }
                else
                {
                    GoRally();
                }
                #region 集结护盾判断
                /*这里是判断护盾提示的
                 *   var btn2data = new MsgBoxBtnParam()
                {
                    str = LocalizationMgr.Get("LC_MENU_cancel_cap"),
                };
                var btn1data = new MsgBoxBtnParam()
                {
                    str = LocalizationMgr.Get("LC_MENU_confirm_cap"),
                    func = (o) => { GoRally(); },
                };
                if (entity != null && (entity.PlayerCity.ShieldOpen || entity.PlayerCity.GodShieldOpen))
                {
                    //var isRallyAttack = GetComponent<TFWToggle>("centre_other/animation/ContentDetail/Button/Toggle").isOn;
                    var tipStr = LocalizationMgr.Get("LC_TIP_cant_rally_shield");
                    FloatTips.I.FloatMsg(tipStr);
                }
                else if (LPlayer.I.ShieldOpen || LPlayer.I.GodShieldOpen)
                {
                    if (LPlayer.I.ShieldOpen && !LPlayer.I.GodShieldOpen)
                    {
                        UIMsgBox.Push(EMsgBoxType.two, LocalizationMgr.Get("LC_MENU_warning_cap"),
                            LocalizationMgr.Format("LC_MAP_broken_shield_desc", SCommon.FormatTimeString(LEnergyGauge.I.ShieldRemainTime / 1000), SCommon.FormatTimeString((int)CConstConfig.I(10134008).Val)),
                            btn1data,
                            btn2data, ButtonColorGroup.RedBlue);
                    }
                    else if (LPlayer.I.ShieldOpen && LPlayer.I.GodShieldOpen)
                    {
                        UIMsgBox.Push(EMsgBoxType.two, LocalizationMgr.Get("LC_MENU_warning_cap"),
                            LocalizationMgr.Format("LC_Castle_Protection_Attack2", SCommon.FormatTimeString(Mathf.Max((float)LEnergyGauge.I.ShieldRemainTime / 1000, MetaConfig.ShieldGodCD)), SCommon.FormatTimeString((int)CConstConfig.I(10134008).Val)),
                            btn1data,
                            btn2data, ButtonColorGroup.RedBlue);
                    }
                    else if (LPlayer.I.GodShieldOpen)
                    {
                        UIMsgBox.Push(EMsgBoxType.two, LocalizationMgr.Get("LC_MENU_warning_cap"),
                            LocalizationMgr.Format("LC_Castle_Protection_Attack1", SCommon.FormatTimeString(MetaConfig.ShieldGodCD), SCommon.FormatTimeString((int)CConstConfig.I(10134008).Val)),
                            btn1data,
                            btn2data, ButtonColorGroup.RedBlue);
                    }
                }*/
                #endregion
            }
        }
        /// <summary>
        /// 前往集结
        /// </summary>
        private void GoRally()
        {
            if (_info != null && _buildingInfo != null)
            {
                var entityId = _info.entityID;
                var time = 0;
                if (uiTimeSelect != null)
                {
                    time = uiTimeSelect.CurTimeSeconds * 1000;
                }

                Rally(entityId, time);
            }
        }


        /// <summary>
        /// 发起集结
        /// </summary>
        /// <param name="entityId"></param>
        private void Rally(long entityId, int time)
        {
            var cityId = LPlayer.I.MainCityID;
            var cityEntity = LMapEntityManager.I.GetEntityInfo(cityId);
            var targetEntity = LMapEntityManager.I.GetEntityInfo(entityId);

            if (cityEntity == null || targetEntity == null)
            {
                return;
            }

            if (!LPlayer.I.IsPlayerInUnion())
            {
                //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.ALLIANCE);
            }
            else
            {

                if (WorldSwitchMgr.I.ShowWorldType == WorldTypeEnum.CITY)
                {
                    //UIMain.I.TrySwitch(MainMenuConst.WORLD);
                    EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.WORLD);
                }
                EventMgr.FireEvent(TEventType.UIMain_Update_Start_DispatchTroop, new MarchArgs()
                {
                    targetId = entityId,
                    act = MarchAct.MarchActRally,
                    isCampAfterHunt = LMapEntityPlayerTroop.IsCampAfterHunt,
                    rallyTime = time
                });
            }
            //关闭当前界面
            Close();
        }

        #endregion



        /// <summary>
        /// 点击攻击按钮事件监听
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        private void OnClickAttackBtn(GameObject arg0, PointerEventData arg1)
        {
            UniTask.Void(async () =>
            {
                if (await AutoLookTarget())
                    return;

                if (_buildingInfo != null)
                {
                    //var mapEntity = LMapEntityManager.I.GetEntityModule<LMapEntityThroneWarTower>();
                    //判断是否已有部队占领
                    if (_info.currOccupy != null && _info.currOccupy.unionID > 0)
                    {
                        //判断是否自己联盟
                        if (this._info.GetIsSelfAllianceOccupying())
                        {
                            if (WarTip.I.IsOpen() && _info.status != ThroneStatus.ThroneStatusNone)
                            {
                                WarTip.I.PupopTip(() =>
                                {
                                    EventMgr.FireEvent(TEventType.UIMain_Update_Start_DispatchTroop, new MarchArgs()
                                    {
                                        targetId = _info.entityID,
                                        act = MarchAct.MarchActOccupy,
                                    });
                                });
                            }
                            else if (LPlayer.I.ShieldOpen || LPlayer.I.GodShieldOpen)
                            {
                                var btn2data = new MsgBoxBtnParam()
                                {
                                    str = LocalizationMgr.Get("LC_MENU_cancel_cap"),
                                };
                                var btn1data = new MsgBoxBtnParam()
                                {
                                    str = LocalizationMgr.Get("LC_MENU_confirm_cap"),
                                    func = (o) =>
                                    {
                                        EventMgr.FireEvent(TEventType.UIMain_Update_Start_DispatchTroop, new MarchArgs()
                                        {
                                            targetId = _info.entityID,
                                            act = MarchAct.MarchActOccupy,
                                        });
                                    },
                                };

                                if (LPlayer.I.ShieldOpen && !LPlayer.I.GodShieldOpen)
                                {
                                    UIMsgBox.Push(EMsgBoxType.two, LocalizationMgr.Get("LC_MENU_warning_cap"),
                                        LocalizationMgr.Format("LC_MAP_broken_shield_desc", SCommon.FormatTimeString(LEnergyGauge.I.ShieldRemainTime / 1000), SCommon.FormatTimeString((int)(await CConstConfig.GetConfigAsync(10134008)).Val)),
                                        btn1data,
                                        btn2data, ButtonColorGroup.RedBlue);
                                }
                                else if (LPlayer.I.ShieldOpen && LPlayer.I.GodShieldOpen)
                                {
                                    UIMsgBox.Push(EMsgBoxType.two, LocalizationMgr.Get("LC_MENU_warning_cap"),
                                        LocalizationMgr.Format("LC_Castle_Protection_Attack2", SCommon.FormatTimeString(Mathf.Max((float)LEnergyGauge.I.ShieldRemainTime / 1000, MetaConfig.ShieldGodCD)), SCommon.FormatTimeString((int)(await CConstConfig.GetConfigAsync(10134008)).Val)),
                                        btn1data,
                                        btn2data, ButtonColorGroup.RedBlue);
                                }
                                else if (LPlayer.I.GodShieldOpen)
                                {
                                    UIMsgBox.Push(EMsgBoxType.two, LocalizationMgr.Get("LC_MENU_warning_cap"),
                                        LocalizationMgr.Format("LC_Castle_Protection_Attack1", SCommon.FormatTimeString(MetaConfig.ShieldGodCD), SCommon.FormatTimeString((int)(await CConstConfig.GetConfigAsync(10134008)).Val)),
                                        btn1data,
                                        btn2data, ButtonColorGroup.RedBlue);
                                }

                            }
                            else
                            {
                                EventMgr.FireEvent(TEventType.UIMain_Update_Start_DispatchTroop, new MarchArgs()
                                {
                                    targetId = _info.entityID,
                                    act = MarchAct.MarchActOccupy,
                                });
                            }
                        }
                        else
                        {
                            //var ok = _buildingInfo.MarchTowerPopTips(mapEntity.IsCanMarchTower(_info.entityID));
                            //if (ok)
                            //    return;
                            if (WarTip.I.IsOpen() && _info.status != ThroneStatus.ThroneStatusNone)
                            {
                                WarTip.I.PupopTip(() => { GoAttack(); });
                            }
                            else if (LPlayer.I.ShieldOpen || LPlayer.I.GodShieldOpen)
                            {
                                var btn2data = new MsgBoxBtnParam()
                                {
                                    str = LocalizationMgr.Get("LC_MENU_cancel_cap"),
                                };
                                var btn1data = new MsgBoxBtnParam()
                                {
                                    str = LocalizationMgr.Get("LC_MENU_confirm_cap"),
                                    func = (o) =>
                                    {
                                        GoAttack();
                                    },
                                };

                                if (LPlayer.I.ShieldOpen && !LPlayer.I.GodShieldOpen)
                                {
                                    UIMsgBox.Push(EMsgBoxType.two, LocalizationMgr.Get("LC_MENU_warning_cap"),
                                        LocalizationMgr.Format("LC_MAP_broken_shield_desc", SCommon.FormatTimeString(LEnergyGauge.I.ShieldRemainTime / 1000), SCommon.FormatTimeString((int)(await CConstConfig.GetConfigAsync(10134008)).Val)),
                                        btn1data,
                                        btn2data, ButtonColorGroup.RedBlue);
                                }
                                else if (LPlayer.I.ShieldOpen && LPlayer.I.GodShieldOpen)
                                {
                                    UIMsgBox.Push(EMsgBoxType.two, LocalizationMgr.Get("LC_MENU_warning_cap"),
                                        LocalizationMgr.Format("LC_Castle_Protection_Attack2", SCommon.FormatTimeString(Mathf.Max((float)LEnergyGauge.I.ShieldRemainTime / 1000, MetaConfig.ShieldGodCD)), SCommon.FormatTimeString((int) (await CConstConfig.GetConfigAsync(10134008)).Val)),
                                        btn1data,
                                        btn2data, ButtonColorGroup.RedBlue);
                                }
                                else if (LPlayer.I.GodShieldOpen)
                                {
                                    UIMsgBox.Push(EMsgBoxType.two, LocalizationMgr.Get("LC_MENU_warning_cap"),
                                        LocalizationMgr.Format("LC_Castle_Protection_Attack1", SCommon.FormatTimeString(MetaConfig.ShieldGodCD), SCommon.FormatTimeString((int)(await CConstConfig.GetConfigAsync(10134008)).Val)),
                                        btn1data,
                                        btn2data, ButtonColorGroup.RedBlue);
                                }

                            }
                            else
                            {
                                GoAttack();
                            }
                        }
                    }
                    else
                    {
                        if (WarTip.I.IsOpen() && _info.status != ThroneStatus.ThroneStatusNone)
                        {
                            WarTip.I.PupopTip(() =>
                            {
                                EventMgr.FireEvent(TEventType.UIMain_Update_Start_DispatchTroop, new MarchArgs()
                                {
                                    targetId = _info.entityID,
                                    act = MarchAct.MarchActOccupy,
                                });
                            });
                        }
                        else if (LPlayer.I.ShieldOpen || LPlayer.I.GodShieldOpen)
                        {
                            var btn2data = new MsgBoxBtnParam()
                            {
                                str = LocalizationMgr.Get("LC_MENU_cancel_cap"),
                            };
                            var btn1data = new MsgBoxBtnParam()
                            {
                                str = LocalizationMgr.Get("LC_MENU_confirm_cap"),
                                func = (o) =>
                                {
                                    EventMgr.FireEvent(TEventType.UIMain_Update_Start_DispatchTroop, new MarchArgs()
                                    {
                                        targetId = _info.entityID,
                                        act = MarchAct.MarchActOccupy,
                                    });
                                },
                            };

                            if (LPlayer.I.ShieldOpen && !LPlayer.I.GodShieldOpen)
                            {
                                UIMsgBox.Push(EMsgBoxType.two, LocalizationMgr.Get("LC_MENU_warning_cap"),
                                    LocalizationMgr.Format("LC_MAP_broken_shield_desc", SCommon.FormatTimeString(LEnergyGauge.I.ShieldRemainTime / 1000), SCommon.FormatTimeString((int)(await CConstConfig.GetConfigAsync(10134008)).Val)),
                                    btn1data,
                                    btn2data, ButtonColorGroup.RedBlue);
                            }
                            else if (LPlayer.I.ShieldOpen && LPlayer.I.GodShieldOpen)
                            {
                                UIMsgBox.Push(EMsgBoxType.two, LocalizationMgr.Get("LC_MENU_warning_cap"),
                                    LocalizationMgr.Format("LC_Castle_Protection_Attack2", SCommon.FormatTimeString(Mathf.Max((float)LEnergyGauge.I.ShieldRemainTime / 1000, MetaConfig.ShieldGodCD)), SCommon.FormatTimeString((int)(await CConstConfig.GetConfigAsync(10134008)).Val)),
                                    btn1data,
                                    btn2data, ButtonColorGroup.RedBlue);
                            }
                            else if (LPlayer.I.GodShieldOpen)
                            {
                                UIMsgBox.Push(EMsgBoxType.two, LocalizationMgr.Get("LC_MENU_warning_cap"),
                                    LocalizationMgr.Format("LC_Castle_Protection_Attack1", SCommon.FormatTimeString(MetaConfig.ShieldGodCD), SCommon.FormatTimeString((int)(await CConstConfig.GetConfigAsync(10134008)).Val)),
                                    btn1data,
                                    btn2data, ButtonColorGroup.RedBlue);
                            }

                        }
                        else
                        {
                            EventMgr.FireEvent(TEventType.UIMain_Update_Start_DispatchTroop, new MarchArgs()
                            {
                                targetId = _info.entityID,
                                act = MarchAct.MarchActOccupy,
                            });
                        }
                    }
                }

                //关闭当前界面
                Close();
            });
        }

        /// <summary>
        /// 去攻击
        /// </summary>
        private void GoAttack()
        {
            EventMgr.FireEvent(TEventType.UIMain_Update_Start_DispatchTroop, new MarchArgs()
            {
                targetId = _info.entityID,
                act = this._info.GetBuildingMarchAct(),
            });
        }




        /// <summary>
        /// 自动看向目标
        /// </summary>
        private async UniTask< bool> AutoLookTarget()
        {
            var isAreaShow = WndMgr.IsUIShow<UIAllianceWarArea>();

            if (isAreaShow)
            {
                //界面处于显示状态的话，那么需要先定位到目标位置
                var pos = await _info.Pos();
                //跳转到目标位置
                RMap.JumpIntoTargetPosition(pos.x, pos.y - 15, null);

                return true;
            }

            return false;
        }


        #endregion


        #region 数据刷新

        /// <summary>
        /// 界面显示信息数据
        /// </summary>
        protected override void OnShown()
        {
            base.OnShown();

            //根据传入数据来获取相应的数据
            _uiData = Data as IHistoricPopupData;

            var module = LMapEntityManager.I.GetEntityModule<LMapEntityThroneWarBuilding>();
            if (module == null)
            {
                D.Warning?.Log(" can not find alliance war building module ");
                return;
            }
            if (uiTimeSelect != null)
            {
                uiTimeSelect.Reset();
            }
            //根据entityId获取相应的实体信息
            if (_uiData != null)
            {
                _info = null;
                if (_uiData.EntityId > 0)
                {
                    _info = module.GetThroneWarBuildingInfo(_uiData.EntityId);
                }
                else
                {
                    _info = _uiData.Obj as ThroneCityInfo;
                }

                if (_info != null)
                {
                    if (_buildingInfo != null)
                    {
                        _buildingInfo.ShowBuildingInfo(_info, _info.currOccupy).Forget();
                    }
                }
                //自己如果是跨服的就不显示分享和收藏按钮
                _buildingInfo.shareBtn.SetActive(!LPlayer.I.IsCrossServer);
                _buildingInfo.favoriteBtn.SetActive(!LPlayer.I.IsCrossServer);
                if (Application.isEditor)
                {
                    var entityInfo = LMapEntityManager.I.GetEntityInfo(_uiData.EntityId);
                    if (entityInfo != null)
                    {
                        var x = MapMgr.I2F(entityInfo.RawPosition.X);
                        var z = MapMgr.I2F(entityInfo.RawPosition.Z);
                        var gridV2 = TerritoryUtils.GetGridPos(new Vector2Int((int)x, (int)z));
                        D.Debug?.Log("RefreshData x={0},z={1}, gridV2={2}", x, z, gridV2);
                    }
                }
            }
        }

        /// <summary>
        /// 建筑隐藏关闭
        /// </summary>
        protected override void OnHidden()
        {
            //清理界面信息数据
            if (_buildingInfo != null)
                _buildingInfo.ClearInfo();

            base.OnHidden();
        }


        /// <summary>
        /// 实体更新: 更新对应的信息数据显示
        /// </summary>
        private void OnEntityUpdate(object[] args)
        {
            if (args == null
                || _info == null
                || _uiData == null)
                return;

            var entity = (EntityInfo)args[0];

            var id = entity.ID;
            if (id > 0)
            {
                if (_uiData != null)
                {
                    ////如果是驻扎在龙巢内的npc 刷新血条
                    //var entityInfo = LMapEntityManager.I.GetEntityInfo(_uiData.EntityId);
                    ////D.Error?.Log("OnEntityUpdate id={0},entityInfo.CentralCity.npcID={1}", id, entityInfo.CentralCity.npcID);
                    //if (entityInfo != null && entityInfo.ThroneTower != null && entityInfo.ThroneTower.entityID == id)
                    //{
                    //    if (entity.NpcTroop != null)
                    //    {
                    //        if (_buildingInfo != null)
                    //            _buildingInfo.RefreshBlood(entity.NpcTroop.combatPower, entity.NpcTroop.combatMax);
                    //    }
                    //}
                }


                //那么刷新数据显示
                var module = LMapEntityManager.I.GetEntityModule<LMapEntityThroneWarBuilding>();
                if (_info != null && _info.entityID == id)
                {
                    _info = module.GetThroneWarBuildingInfo(id);
                    if (_info != null)
                    {
                        if (_buildingInfo != null)
                            _buildingInfo.ShowBuildingInfo(_info, _info.currOccupy).Forget();

                    }
                }
            }
        }

        private void OnEntitiesUpdate(object[] args)
        {
            if (args == null
                || _info == null
                || _uiData == null)
                return;

            var entity = (List<EntityInfo>)args[0];

           
                //那么刷新数据显示
                var module = LMapEntityManager.I.GetEntityModule<LMapEntityThroneWarBuilding>();
                if (_info != null && entity?.Count(a=>a.ID== _info.entityID )>0)
                {
                    _info = module.GetThroneWarBuildingInfo(_info.entityID);
                    if (_info != null)
                    {
                        if (_buildingInfo != null)
                            _buildingInfo.ShowBuildingInfo(_info, _info.currOccupy).Forget();

                    }
                }
             
        }


        #endregion

        protected override void OnDestroyed()
        {
            base.OnDestroyed();
            if (uiTimeSelect != null)
            {
                uiTimeSelect.Dispose();
                uiTimeSelect = null;
            }

        }

    }
}
