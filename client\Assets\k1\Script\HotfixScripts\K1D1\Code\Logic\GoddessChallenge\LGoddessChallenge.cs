﻿using cspb;
using THelper;
using Common;
using System.Collections.Generic;
using Public;
using DeepUI;
using UI;
using Game.Map;
using Game.Sprite.Fight;
using System.Linq;
using Game.Config;
using UnityEngine;
using Config;
using Cysharp.Threading.Tasks;
using K1;

namespace Logic 
{

    public class GoddessChallengeData 
    {
        public bool isOpen=false;
        public Dictionary<long, D2Quest> unlockQuest = new Dictionary<long, D2Quest>();
        public Dictionary<int, TrialData> pageData = new Dictionary<int, TrialData>();

        public class TrialData 
        {
            public TrialInfo trialInfo;
            public Cfg.G.CGoddessTrialList cfg;
        }
    }

    /// <summary>
    /// 女神的试炼 --- 商店数据
    /// </summary>
    public class GoddessData 
    {
        /// <summary>
        /// 通过关卡
        /// </summary>
        public int count;

        /// <summary>
        /// 一般商店
        /// </summary>
        public Dictionary<int, ItemData> normalDataList = new Dictionary<int, ItemData>();
        /// <summary>
        /// 首破商店
        /// </summary>
        public Dictionary<int, ItemData> breakDataList = new Dictionary<int, ItemData>();

        public class ItemData 
        {
            public GTrialShopInfo serverData;
            public Cfg.G.CGoddessTrialShop cfg;
        }
    }


    /// <summary>
    /// 女神的试炼
    /// </summary>
    public class LGoddessChallenge : Ins<LGoddessChallenge>
    {
        private RenderTexture renderTexture;
        public RenderTexture RenderTex
        {
            get
            {
                if (renderTexture == null)
                    renderTexture = new RenderTexture(Screen.width, Screen.height,0);
                return renderTexture;
            }
        }
        public long power;
        public Dictionary<string,List<TrialLineUp>> lineUps;
        public Cfg.G.CGoddessTrialStage goddessTrialStageCfg;

        public GoddessChallengeData challengeData;

        public GoddessData shopData;

        /// <summary>
        /// 完成任务数量
        /// </summary>
        public int DoneQuestCount = 0;
        /// <summary>
        /// 可领奖任务数量
        /// </summary>
        public int RewardQuestCount = 0;

        /// <summary>
        /// 限购商店有可以购买的道具
        /// </summary>
        public int LimitShopCanBuy = 0;

        /// <summary>
        /// 女神币数量
        /// </summary>
        public long GoddessCoinCount = 0;
        public int trialsId;
        public int TrialsIdx;
        public GoddessChallengeData.TrialData trialData;
        public string SkipGoddessBatleKey
        {
            get
            {
                return "SkipGoddessChallengeBattle" + trialData.cfg.Type;
            }
        }
        public bool battleResult = true;
        public void Init()
        {
            MessageMgr.RegisterMsg<GoddessTrialQuestRdAck>(this, OnGoddessTrialQuestRdAck);

            MessageMgr.RegisterMsg<GoddessTrialAck>(this, OnGoddessTrialAck);
            MessageMgr.RegisterMsg<GoddessTrialUnLockAck>(this, OnGoddessTrialUnLockAck);
            MessageMgr.RegisterMsg<GetTrialRankAck>(this, OnGetTrialRankAck);
            MessageMgr.RegisterMsg<GoddessTrialQuestNtf>(this, OnGoddessTrialQuestNtf);
            MessageMgr.RegisterMsg<GoddessTrialHangUpRdAck>(this, OnGoddessTrialHangUpRdAck);
           

            MessageMgr.RegisterMsg<TrialChallengeDetailsAck>(this, OnTrialChallengeDetailsAck);
            MessageMgr.RegisterMsg<TrialChallengeFinishAck>(this, OnTrialChallengeFinishAck);
            MessageMgr.RegisterMsg<TrialChallengeLineUpAck>(this, OnTrialChallengeLineUpAck);


            ///商店
            MessageMgr.RegisterMsg<GoddessTrialShopAck>(this, OnGoddessTrialShopAck);
            MessageMgr.RegisterMsg<GoddessTrialShopBuyAck>(this, OnGoddessTrialShopBuyAck);
            MessageMgr.RegisterMsg<GoddessTrialShopRefreshAck>(this, OnGoddessTrialShopRefreshAck);

            EventMgr.RegisterEvent(TEventType.RefreshAssetAck, OnRefreshAssetAck, this);

            EventMgr.RegisterEvent(TEventType.OnSwitchMainUI, (a) =>
            {
                if (a?.Length > 0)
                {
                    //var type = (MainMenuType)a[0];
                    //if (type == MainMenuType.PRISON)
                    {
                        this.ReqGoddessTrialReq();
                    }
                }

            }, this);
            //ReqGoddessTrialReq(); //登录不再请求 走切换城建判断请求 
            lineUps = new Dictionary<string, List<TrialLineUp>>();
            GoddessCoinCount = PlayerAssetsMgr.I.GetItemCountByID((int)Game.Config.MetaConfig.TrialVM);
        }


        public void DeInit()
        {
            MessageMgr.UnregisterMsg(this);
            EventMgr.UnregisterEvent(this);
            
            lineUps = null;
            goddessTrialStageCfg = null;

            challengeData = null;
            shopData = null;

        }

        public bool InBattle(long id,string LineUpType)
        {
            if(lineUps.TryGetValue(LineUpType,out var lineUp))
            {
                for (int i = 0; i < lineUp.Count; i++)
                {
                    for (int j = 0; j < lineUp[i].id.Count; j++)
                    {
                        if (lineUp[i].id[j] == id)
                            return true;
                    }
                }
            }
            
            return false;
        }
        void OnRefreshAssetAck(object[] arr) 
        {
            GoddessCoinCount = PlayerAssetsMgr.I.GetItemCountByID((int)Game.Config.MetaConfig.TrialVM);
        }

        #region

        #region  解锁任务界面

        void OnGoddessTrialQuestRdAck(GoddessTrialQuestRdAck ack)
        {
            if (ack.errCode != ErrCode.ErrCodeSuccess)
            {
                ack.errCode.ToFloatErrCode();
                return;
            }
            challengeData.unlockQuest[ack.Quests.id] = ack.Quests;
            RewardQuestCount--;
            DoneQuestCount++;
            UIHelper.FlyItems(ack.Reward);
            EventMgr.FireEvent(TEventType.GoddessChallengeQuestReward, ack.Quests);
        }

        async UniTaskVoid OnGoddessTrialAck(GoddessTrialAck ack)
        {
            RewardQuestCount = 0;
            DoneQuestCount = 0;
            challengeData = new GoddessChallengeData();
            challengeData.isOpen = ack.UnLock;
            int count = ack.Quests.Count;
            for (int i = 0; i < count; i++)
            {
                var item = ack.Quests[i];
                challengeData.unlockQuest[item.id] = item;
                if (item.state == QuestState.QuestStateFinish)
                {
                    RewardQuestCount++;
                }
                else if (item.state == QuestState.QuestStateReward)
                {
                    DoneQuestCount++;
                }
            }
            count = ack.Trials.Count;
            for (int i = 0; i < count; i++)
            {
                var cfg = await Cfg.C.CGoddessTrialList.GetConfigAsync(ack.Trials[i].TrialId);
                if (cfg == null)
                    continue;
                challengeData.pageData.Add(cfg.Type, new GoddessChallengeData.TrialData()
                {
                    trialInfo = ack.Trials[i],
                    cfg = cfg
                });

            }
            EventMgr.FireEvent(TEventType.GoddessChallengeDataRefush);

            if (ack.UnLock && this.shopData == null)
            {
                ReqGoddessTrialShopReq();
            }
        }

        async UniTaskVoid OnGoddessTrialUnLockAck(GoddessTrialUnLockAck ack)
        {
            if (ack.errCode != ErrCode.ErrCodeSuccess)
            {
                ack.errCode.ToFloatErrCode();
                return;
            }
            int count = ack.Trials.Count;
            this.challengeData.isOpen = ack.UnLock;
            for (int i = 0; i < count; i++)
            {
                var cfg = await Cfg.C.CGoddessTrialList.GetConfigAsync(ack.Trials[i].TrialId);
                if (!challengeData.pageData.TryGetValue(cfg.Type, out var item))
                {
                    item = new GoddessChallengeData.TrialData();
                    item.cfg = cfg;
                    challengeData.pageData.Add(cfg.Type, item);
                }
                item.trialInfo = ack.Trials[i];
            }
            challengeData.isOpen = true;

            if (ack.UnLock && this.shopData == null)
            {
                ReqGoddessTrialShopReq();
            }

            EventMgr.FireEvent(TEventType.GoddessChallengeDataRefush);

            EventMgr.FireEvent(TEventType.GoddessChallengeUnlock);
        }

        void OnGoddessTrialQuestNtf(GoddessTrialQuestNtf ntf) 
        {
            RewardQuestCount = 0;
            if (challengeData != null)
            {
                foreach (var item in ntf.Quest)
                {
                    challengeData.unlockQuest[item.id] = item;
                }
                foreach (var item in challengeData.unlockQuest)
                {
                    if (item.Value.state == QuestState.QuestStateFinish)
                    {
                        RewardQuestCount++;
                    }
                }
                EventMgr.FireEvent(TEventType.GoddessChallengeQuestRefush);
            }
        }


        async UniTaskVoid OnGoddessTrialHangUpRdAck(GoddessTrialHangUpRdAck ack) 
        {
            if (ack.errCode != ErrCode.ErrCodeSuccess)
            {
                ack.errCode.ToFloatErrCode();
                return;
            }
            UIHelper.FlyItems(ack.Asset);
            var cfg = await Cfg.C.CGoddessTrialList.GetConfigAsync(ack.Trial.TrialId);
            if (challengeData.pageData.TryGetValue(cfg.Type, out var item))
            {
                item.trialInfo = ack.Trial;
            }
            EventMgr.FireEvent(TEventType.GoddessChallengeLayerReward, item);
        }

        #endregion

        #region  排行榜

        void OnGetTrialRankAck(GetTrialRankAck ack)
        {
            if (ack.errCode != ErrCode.ErrCodeSuccess)
            {
                ack.errCode.ToFloatErrCode();
                return;
            }
            EventMgr.FireEvent(TEventType.RankListGet_Common, ack.players, ack.OwnerRank,ack.OwnerScore, ack.Type);
        }

        #endregion

        #region  商店

        async UniTaskVoid OnGoddessTrialShopAck(GoddessTrialShopAck ack)
        {
            if (shopData == null)
                shopData = new GoddessData();
            shopData.count = ack.Cnt;
            shopData.normalDataList.Clear();
            shopData.breakDataList.Clear();
            LimitShopCanBuy = 0;
            foreach (var item in ack.Shops)
            {
                shopData.normalDataList.Add(item.CfgId,new GoddessData.ItemData()
                {
                    serverData = item,
                    cfg = await Cfg.C.CGoddessTrialShop.GetConfigAsync(item.CfgId)
                });
            }
            foreach (var item in ack.LimitShops)
            {
                var cfg = await Cfg.C.CGoddessTrialShop.GetConfigAsync(item.CfgId);
                shopData.breakDataList.Add(item.CfgId,new GoddessData.ItemData()
                {
                    serverData = item,
                    cfg = cfg
                });
                if (item.Cnt != cfg.Limit && cfg.StageNum <= ack.Cnt && GoddessCoinCount >= cfg.Good[0].Val)
                {
                    LimitShopCanBuy ++;
                }
            }
            EventMgr.FireEvent(TEventType.GoddessChallengeShopRefush);
        }


        async UniTaskVoid OnGoddessTrialShopBuyAck(GoddessTrialShopBuyAck ack) 
        {
            if (ack.errCode != ErrCode.ErrCodeSuccess) 
            {
                ack.errCode.ToFloatErrCode();
                return;
            }
            var cfg = await Cfg.C.CGoddessTrialShop.GetConfigAsync(ack.Good.CfgId);
            if (cfg == null)
                return;
            
            GoddessData.ItemData result = null;

            ///一般商店
            if (cfg.Type == 1)
            {
                if (shopData.normalDataList.TryGetValue(cfg.Id, out var itemData)) 
                {
                    itemData.serverData = ack.Good;
                    result = itemData;
                }
            }
            ///首破商店
            else if (cfg.Type == 2) 
            {
                if (shopData.breakDataList.TryGetValue(cfg.Id, out var itemData))
                {
                    itemData.serverData = ack.Good;
                    result = itemData;
                }
                if (ack.Good.Cnt == cfg.Limit && GoddessCoinCount >= cfg.Good[0].Val)
                {
                    LimitShopCanBuy--;
                }
            }

            //女神试炼-首通兑换后，返回主城界面时
            FivePraiseMgr.I.UpdateCanOpenFivePraise();

            EventMgr.FireEvent(TEventType.GoddessChallengeShopBuy, result);
        }

        void OnGoddessTrialShopRefreshAck(GoddessTrialShopRefreshAck ack) 
        {
            foreach (var item in ack.Shops)
            {
                if (shopData.normalDataList.TryGetValue(item.CfgId, out var itemData)) 
                {
                    itemData.serverData = item;
                }
            }
            LimitShopCanBuy = 0;
            foreach (var item in ack.LimitShops)
            {
                if (shopData.breakDataList.TryGetValue(item.CfgId, out var itemData))
                {
                    itemData.serverData = item;
                    if (item.Cnt != itemData.cfg.Limit && itemData.cfg.StageNum >= shopData.count && GoddessCoinCount >= itemData.cfg.Good[0].Val)
                    {
                        LimitShopCanBuy ++;
                    }
                }
            }

            EventMgr.FireEvent(TEventType.GoddessChallengeShopRefush);
        }

        #endregion

        #endregion

        #region
        public void ReqTrialChallengeFinish()
        {
            TrialChallengeFinishReq req = new TrialChallengeFinishReq()
            {
                TrialId = trialsId,
            };
            MessageMgr.Send(req);
        }
        public async UniTaskVoid ReqTrialChallengeDetails(GoddessChallengeData.TrialData data)
        {
            trialData = data;
            TrialsIdx = data.cfg.Type;
            trialsId = data.trialInfo.TrialId;
            //0层作为挂机奖励，不走战斗
            goddessTrialStageCfg = await Cfg.C.CGoddessTrialStage.GetConfigAsync(data.cfg.Type * 1000 + data.trialInfo.stage + 1);
            TrialChallengeDetailsReq req = new TrialChallengeDetailsReq()
            {
                TrialId = data.trialInfo.TrialId,
            };
            MessageMgr.Send(req);
        }
        public void ReqTrialChallengeLineUp( int LineIdx, int idx, int id)
        {
            TrialChallengeLineUpReq req = new TrialChallengeLineUpReq()
            {
                TrialId = trialsId,
                LineIdx = LineIdx,
                Idx = idx,
                Id = id
            };
            MessageMgr.Send(req);
        }
        private void OnTrialChallengeLineUpAck(TrialChallengeLineUpAck obj)
        {
            //var uiTrialsHeroList = PopupManager.I.FindPopup<UITrialsHeroList>();
            //if (uiTrialsHeroList != null)
            //    uiTrialsHeroList.Close();
            //var uiTrialsEquipList = PopupManager.I.FindPopup<UITrialsEquipList>();
            //if (uiTrialsEquipList != null)
            //    uiTrialsEquipList.Close();
            //if (obj.errCode == ErrCode.ErrCodeSuccess)
            //{
            //    string lineUpType = (TrailsType)TrialsIdx == TrailsType.Equip ? "Equip" : "Hero";
            //    if(lineUps.TryGetValue(lineUpType,out var lineUp))
            //    {
            //        lineUps[lineUpType] = obj.lineUp;
            //    }
            //    else
            //    {
            //        lineUps.Add(lineUpType, obj.lineUp);
            //    }
                    
            //    power = obj.power;
            //    EventMgr.FireEvent(TEventType.TrialChallengeLineUpSuccess);
            //}
        }
        public bool HasFullHero(bool isEquip = false)
        {
            lineUps.TryGetValue(isEquip?"Equip":"Hero", out var lineUp);
            if (lineUp == null)
                return false;
            foreach(var item in lineUp)
            {
                if (item.id.Count == 0)
                    return false;
                for(int i = 0;i<  item.id.Count;i++)
                {
                    if (item.id[i] == 0 &&i< goddessTrialStageCfg.ArrangementMax)
                        return false;
                }
            }
            return true;

        }
        private async UniTaskVoid OnTrialChallengeFinishAck(TrialChallengeFinishAck obj)
        {
            //if (obj.errCode == ErrCode.ErrCodeSuccess)
            //{
            //    if(challengeData.pageData.TryGetValue(TrialsIdx,out var data))
            //    {
            //        data.trialInfo.stage = obj.layers;
            //        goddessTrialStageCfg = await Cfg.C.CGoddessTrialStage.GetConfigAsync(data.cfg.Type * 1000 + data.trialInfo.stage + 1);
            //    }
            //    if (shopData != null)
            //    {
            //        shopData.count = 0;
            //        foreach (var item in challengeData.pageData)
            //        {
            //            shopData.count += item.Value.trialInfo.stage;
            //        }
            //        CheckShopRed();
            //    }
            //    battleResult = true;
            //    TrailsFightManager.I.LevelEnd(true);
            //    EventMgr.FireEvent(TEventType.TrialsBattleResult, true, data.trialInfo);

            //}
            //else
            //{
            //    battleResult = false;
            //    TrailsFightManager.I.LevelEnd(false);
            //    EventMgr.FireEvent(TEventType.TrialsBattleResult, false);

            //}
        }

        private void OnTrialChallengeDetailsAck(TrialChallengeDetailsAck obj)
        {
            //if (obj.errCode == ErrCode.ErrCodeSuccess)
            //{
            //    power = obj.power;
            //    string lineUpType = (TrailsType)TrialsIdx == TrailsType.Equip ? "Equip" : "Hero";
            //    if (lineUps.TryGetValue(lineUpType, out var lineUp))
            //    {
            //        lineUps[lineUpType] = obj.lineUp;
            //    }
            //    else
            //    {
            //        lineUps.Add(lineUpType, obj.lineUp);
            //    }

            //}
            //PopupManager.I.ShowLayer<UIGoddessChallenge>(new UIGoddessChallengeData()
            //{
            //    trailsType = (TrailsType)goddessTrialStageCfg.Type
            //});
        }
        /// <summary>
        /// 基础信息
        /// </summary>
        public void ReqGoddessTrialReq() 
        {
            if (LPlayer.I.GetMainCityLevel() >= MetaConfig.TrialOpen && challengeData==null)
            {
                MessageMgr.Send(new GoddessTrialReq());
            }
        }

        /// <summary>
        /// 请求排行榜数据
        /// </summary>
        /// <param name="rankType"></param>
        /// <param name="startIndex"></param>
        /// <param name="endIndex"></param>

        public void ReqGetTrialRankReq(GoddessRankType rankType,int startIndex,int endIndex) 
        {
            MessageMgr.Send(new GetTrialRankReq()
            {
                Type = rankType,
                Start = startIndex,
                End = endIndex
            });
        }


        /// <summary>
        /// 请求解锁任务领奖
        /// </summary>
        public void ReqGoddessTrialQuestRdReq(int cfgid) 
        {
            MessageMgr.Send(new GoddessTrialQuestRdReq()
            {
                QuestId = cfgid
            });
        }


        /// <summary>
        /// 请求解锁试炼
        /// </summary>
        public void ReqGoddessTrialUnLockReq() 
        {
            MessageMgr.Send(new GoddessTrialUnLockReq());
        }

        /// <summary>
        /// 请求商店数据
        /// </summary>
        public void ReqGoddessTrialShopReq() 
        {
            MessageMgr.Send(new GoddessTrialShopReq());
        }


        /// <summary>
        /// 申请商店购买
        /// </summary>
        /// <param name="cfgid"></param>
        /// <param name="count"></param>
        public void ReqGoddessTrialShopBuyReq(int cfgid,long count) 
        {
            MessageMgr.Send(new GoddessTrialShopBuyReq()
            {
                CfgID = cfgid,
                Cnt = count
            });
        }

        /// <summary>
        /// 倒计时结束 商店刷新
        /// </summary>
        public void ReqGoddessTrialShopRefreshReq() 
        {
            MessageMgr.Send(new GoddessTrialShopRefreshReq());
        }

        /// <summary>
        /// 申请领取试炼奖励
        /// </summary>
        /// <param name="cfgID"></param>
        public void ReqGoddessTrialHangUpRdReq(int cfgID) 
        {
            MessageMgr.Send(new GoddessTrialHangUpRdReq()
            {
                TrialId = cfgID
            });
        }

        #endregion

        void CheckShopRed() 
        {
            LimitShopCanBuy = 0;
            foreach (var item in shopData.breakDataList)
            {
                if (item.Value.cfg.Limit != item.Value.serverData.Cnt && item.Value.cfg.StageNum <= shopData.count && GoddessCoinCount >= item.Value.cfg.Good[0].Val)
                {
                    LimitShopCanBuy++;
                }
            }
        }


        public int RedCount() 
        {
            if(challengeData == null)
                return 0;
            if (challengeData.isOpen)
            {
                int num = 0;
                if (LimitShopCanBuy != 0)
                    num++;
                foreach (var item in challengeData.pageData)
                {
                    if (item.Value.trialInfo.isReward) 
                    {
                        num++;
                    }
                }
                return num;
            }
            else
            {
                return RewardQuestCount;
            }
        }


        /// <summary>
        /// 尝试打开商店界面
        /// </summary>
        public void TryOpenShop() 
        {
            var flag = LPlayer.I.GetMainCityLevel() >= MetaConfig.TrialOpen;
            if (!flag)
            {
                UITools.PopTips("Actv_KingRoad_Task_CastleLevelReaches".ToLocal(MetaConfig.TrialOpen));
                return;
            }

            var isOpen = LSwitchMgr.I.IsFunctionOpen(SwitchConfig.Goddess_Trial);
            if (!isOpen)
            {
                "DEMO_30".ToShowFloatTips();
                return;
            }
            if (challengeData == null || !challengeData.isOpen) 
            {
                "GoddessTrialList_Unlock_Tips_01".ToShowFloatTips();
                return;
            }
            if (shopData == null)
            {
                return;
            }
            PopupManager.I.ShowPanel<UIGoddessExchangeMall>(new UIGoddessExchangeMallData());
        }
    }

}