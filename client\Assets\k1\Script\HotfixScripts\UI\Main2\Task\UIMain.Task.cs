﻿ 
using System.Linq;
using Common;
using Config;
using DeepUI;
using Game.Config;
using Game.Data;
using Logic;
using UnityEngine;
using Public;
using cspb;
using K3;
using TFW;
using TFW.Localization;
using TFW.UI;
using System;
using Cysharp.Threading.Tasks;
using TMPro;

namespace UI
{
    /// <summary>
    /// 任务
    /// </summary>
    public partial class UIMain2
    {

        #region 属性字段信息

        /// <summary>
        /// 任务对象
        /// </summary>
        [<PERSON>up<PERSON>ield("content/LeftNewBtns/Task/taskImg")]
        private GameObject taskObj;
        
        [PopupField("content/LeftNewBtns/Task/Chapter")]
        private GameObject chapterImgObj;
        [PopupField("content/LeftNewBtns/Task/Chapter")]
        private TFWImage chapterImage;
        
        [PopupField("content/LeftNewBtns/Task/Daily")]
        private GameObject dailyImgObj;
        [PopupField("content/LeftNewBtns/Task/Daily")]
        private TFWImage dailyImage;
        [PopupField("content/LeftNewBtns/Task/GuideImage")]
        private GameObject m_GuideImage;
        
        [PopupField("content/LeftNewBtns/Task")]
        private GameObject taskBtnRoot;
        [PopupField("content/LeftNewBtns/Task/taskImg")]
        private TFWImage taskImg;
        [PopupField("content/LeftNewBtns/Task/taskImg/text")]
        private TextMeshProUGUI taskText;
        [PopupField("content/LeftNewBtns/Task/taskImg/text/Icon")]
        private GameObject finishGa;
        
        [PopupField("content/LeftNewBtns/Task/taskImg/Eff_ui_rwwc")]
        private GameObject finishEffect;
        
        [PopupField("content/LeftNewBtns/Task/taskImg/Eff_ui_rwwc_sg")]
        private GameObject refreshEffect;
        
        /// <summary>
        /// 任务按钮widget
        /// </summary>
        private BtnDescWidget taskBtnWidget;
        /// <summary>
        /// 章节任务按钮
        /// </summary>
        private BtnDescWidget chapterTaskBtnWidget;
        /// <summary>
        /// 每日任务按钮
        /// </summary>
        private BtnDescWidget dailyTaskBtnWidget;

        /// <summary>
        /// 任务红点对象
        /// </summary>
        [PopupField("content/LeftNewBtns/Task/red")]
        private GameObject taskRedObj;
        ///// <summary>
        ///// 任务icon图片
        ///// </summary>
        //[PopupField("content/TaskBtn/Button/Icon")]
        //private TFWImage taskImg;

        /// <summary>
        /// 红点对象显示
        /// </summary>
        private RedWidget taskRedWidget;

        /// <summary>
        /// 任务飞向目标点对象
        /// </summary>
        [PopupField("content/taskflypoint")]
        private GameObject taskFlyPointObj;

        private RedWidget dailyTaskRedWidget;

        [PopupField("content/RightBtns/TaskBtn")]
        private GameObject dailyTaskObj;

        [PopupField("content/RightBtns/TaskBtn/Button")]
        private GameObject dailyTaskBtn;

        [PopupField("content/RightBtns/TaskBtn/Button/Red")]
        private GameObject dailyTaskRedObj;

        #endregion


        #region 初始化

        /// <summary>
        /// 初始化任务
        /// </summary>
        private void InitTask()
        {

            taskBtnWidget = new BtnDescWidget(taskObj);
            taskBtnWidget.SetBtnClickCallBack(OnClickTaskBtn);
            
            chapterTaskBtnWidget = new BtnDescWidget(chapterImgObj);
            chapterTaskBtnWidget.SetBtnClickCallBack(OnClickChapterTaskBtn);
            
            dailyTaskBtnWidget = new BtnDescWidget(dailyImgObj);
            dailyTaskBtnWidget.SetBtnClickCallBack(OnClickDailyTaskBtn);

            taskRedWidget = new RedWidget(taskRedObj);
            GameData.I.MainData.UpdateMainTaskBtnFlyPos(taskFlyPointObj.transform.position);
            
            dailyTaskRedWidget = new RedWidget(dailyTaskRedObj);

            //刷新显示状态
            UpdateTaskActive();
            EventMgr.RegisterEvent(TEventType.ChapterQuestStateChange, (pars) => { OnChapterReward(); }, this);
            EventMgr.RegisterEvent(TEventType.ChapterQuestReward, (pars) => { OnChapterReward(); }, this);
            EventMgr.RegisterEvent(TEventType.ChapterReward, (pars) => { OnChapterReward(); }, this);
            EventMgr.RegisterEvent(TEventType.ChapterQuestInfo, (pars) => { RefreshText(); }, this);
            EventMgr.RegisterEvent(TEventType.DragonDataRefresh, (pars) => { RefreshText(); }, this);
            EventMgr.RegisterEvent(TEventType.DragonLevelUp, (pars) => { RefreshText(); }, this);

            EventMgr.RegisterEvent(TEventType.QuestChangeNtf, (pars) => { RefreshText(); }, this);
            EventMgr.RegisterEvent(TEventType.ChestChangeNtf, (pars) => { RefreshText(); }, this);


            EventMgr.RegisterEvent(TEventType.EnterTaskPanel, (pars) => { RefreshText(); }, this);
            EventMgr.RegisterEvent(TEventType.OnMainTaskUpdate, (pars) => { RefreshText(); }, this);

            //if (taskObj.activeInHierarchy) //这样会在棋盘UI显示的情况下 执行引导了
            //{
            //    var guidData = new UIGuidData();
            //    guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMain2", UIItemGa = taskObj, slide = true });
            //    UIGuid.StartGuid(guidData);
            //}

            UIBase.AddRemoveListener(EventTriggerType.Click, dailyTaskBtn, (x, y) =>
            {
                TaskMgr.I.OpenDailyTask(DailyTaskEnum.Alliance);
            });

            RefreshText();
        }
        D2Quest curTask;
        
        private void OnChapterReward()
        {
            RefreshText();
            
            refreshEffect.SetActive(true);

            NTimer.CountDown(1.5f, () => refreshEffect.SetActive(false));
        }

        private void RefreshGuideTask()
        {
            m_GuideImage.SetActive((GameData.I.MainData.CurrMenuType == MainMenuType.CITY  || GameData.I.MainData.CurrMenuType == MainMenuType.WORLD) && !UIGuid.HaveWakeGuid());//|| GameData.I.MainData.CurrMenuType == MainMenuType.PRISON
        }

        private void RefreshText()
        {
            chapterImage.gameObject.SetActive(!ChapterTaskMgr.I.CheckChapterTaskIsFinish());
            dailyImage.gameObject.SetActive(ChapterTaskMgr.I.CheckChapterTaskIsFinish());
            
            if (ChapterTaskMgr.I.CheckChapterTaskIsFinish())
            {
                taskRedObj.transform.localPosition = new Vector3(72.8f, 50.502f, 0);
                DailyTask();
            }
            else
            {
                taskRedObj.transform.localPosition = new Vector3(105.5f, 78.4f, 0);
                ChapterTask();
            }
            
            UpdateTaskNum();
        }

        private void DailyTask()
        {
            var dailyTaskList = GameData.I.TaskData.allianceTaskData.dailyTaskList.Where(x=>x.state != QuestState.QuestStateReward).ToList();
            if (dailyTaskList.Count() > 0)
            {
                dailyTaskList = dailyTaskList.OrderByDescending(s => Cfg.C.CNewQuestCommon.I(s.cfgID).Sortord).ToList();
                dailyTaskList = dailyTaskList.OrderBy(s => s.state == QuestState.QuestStateReward).ToList();
                dailyTaskList = dailyTaskList.OrderByDescending(s => s.state == QuestState.QuestStateFinish).ToList();
                curTask = dailyTaskList[0];
                var cfg = Cfg.C.CNewQuestCommon.I(curTask.cfgID);
            
                var count = curTask.status >= cfg.Count ? cfg.Count : curTask.status;
                if (count < 0)
                    count = 0;
                var chageColor = count >= cfg.Count ? $"<color=#FFFFFF>{cfg.Count}</color>" : $"<color=#FF0000>{count}</color>";
                if (cfg.Params.Count == 0)
                {
                    taskText.text = $"{LocalizationMgr.Format(cfg.LcDesc, cfg.Count)}   {chageColor}/{cfg.Count}";
                }
                else if (cfg.Params.Count == 1)
                {
                    taskText.text = $"{LocalizationMgr.Format(cfg.LcDesc, cfg.Count, cfg.Params[0])}   {chageColor}/{cfg.Count}";
                }
                else if (cfg.Params.Count == 2)
                {
                    taskText.text = $"{LocalizationMgr.Format(cfg.LcDesc, cfg.Count, cfg.Params[0], cfg.Params[1])}   {chageColor}/{cfg.Count}";
                }
            
                if (curTask.state == cspb.QuestState.QuestStateFinish)
                {
                    finishGa.SetActive(true);
                    finishEffect.SetActive(true);
                }
                else
                {
                    finishGa.SetActive(false);
                    finishEffect.SetActive(false);
                }
            }
            else
            {
                curTask = null;
                finishGa.SetActive(false);
                finishEffect.SetActive(false);
                taskText.text = "daily_all_complete_tips".ToLocal();
            }
        }


        /// <summary>
        /// 
        /// </summary>
        private void ChapterTask()
        {
            finishEffect.SetActive(false);
            var chapterList = ChapterTaskGameData.I.mChapterQuestInfo.chapterQuests;
            chapterList.Sort((a, b) =>
            {
                if (a == null || a.ConfigData == null)
                {
                    return 1;
                }
                else if (b == null || b.ConfigData == null)
                {
                    return -1;
                }
                else
                {
                    if (b.state == cspb.QuestState.QuestStateFinish)
                    {
                        if (a.state == cspb.QuestState.QuestStateFinish)
                        {
                            return a.ConfigData.DisplayID - b.ConfigData.DisplayID;
                        }
                        else
                            return 1;
                    }
                    else if (b.state == cspb.QuestState.QuestStateGoOn)
                    {
                        if (a.state == cspb.QuestState.QuestStateFinish)
                        {
                            return -1;
                        }
                        else if (a.state == cspb.QuestState.QuestStateGoOn)
                        {
                            return a.ConfigData.DisplayID - b.ConfigData.DisplayID;
                        }
                        else
                        {
                            return 1;
                        }
                    }
                    else
                    {
                        if (a.state == cspb.QuestState.QuestStateReward)
                        {
                            return a.ConfigData.DisplayID - b.ConfigData.DisplayID;
                        }
                        else
                        {
                            return -1;
                        }
                    }
                }
            });
            if (chapterList.Count > 0)
            {
                bool isAllFinish = true;
                foreach (var item in chapterList)
                {
                    if (item.state != QuestState.QuestStateReward)
                    {
                        isAllFinish = false;
                    }
                }
                curTask = chapterList[0];
                var conf = Cfg.C.CChapterQuest.I(curTask.cfgID);
                if (isAllFinish)
                {       
                    var chapterListCfg = Cfg.C.CChapterReward.RawList();
                    var targetCfg = chapterListCfg.Find(s => conf.ChapterID == s.ChapterNum) ;
                    var heroCfgList = Cfg.C.CD2Hero.RawList();
                    var heroCfg = heroCfgList.Find(s => s.Id == targetCfg.Hero);
                    // taskText.text = LocalizationMgr.Format("Chapter_mission_complete", conf.ChapterID);
                    //taskText.text = "chapter_act_desc_02".ToLocal(); 
                    taskText.text = LocalizationMgr.Format("chapter_act_desc_04",LocalizationMgr.Get(heroCfg.Name));

                    //UITools.SetCommonItemIcon(taskImg, "common_img_task_finish");
                    finishGa.SetActive(true);
                    finishEffect.SetActive(true);
                    return;
                }
                
                // 当前组
                int group = ChapterTaskMgr.I.GetCurrentGroup();
                var currentList = chapterList.Where(x => x.ConfigData.ActGroup == group && x.state != QuestState.QuestStateReward).ToList();
                if (currentList.Count == 0)
                {
                    var chapterListCfg = Cfg.C.CChapterReward.RawList();
                    var targetCfg = chapterListCfg.Find(s => conf.ChapterID == s.ChapterNum) ;
                    var heroCfgList = Cfg.C.CD2Hero.RawList();
                    var heroCfg = heroCfgList.Find(s => s.Id == targetCfg.Hero);
                    taskText.text = LocalizationMgr.Format("chapter_act_desc_03",LocalizationMgr.Get(heroCfg.Name));
                    //taskText.text = "asd ";//"chapter_act_desc_01".ToLocal();
                    finishGa.SetActive(true);
                    finishEffect.SetActive(true);
                }
                else
                {
                    curTask = currentList[0];
                    taskText.text = string.Format("{0}({1}/{2})", curTask.GetDes(), curTask.Process, curTask.ConfigData.Count);
                    if (curTask.state == cspb.QuestState.QuestStateFinish)
                    {
                        // UITools.SetCommonItemIcon(taskImg, "common_img_task_finish");
                        finishGa.SetActive(true);
                        finishEffect.SetActive(true);
                        var a = taskText.text.Length;
                    }
                    else
                    {
                        // UITools.SetCommonItemIcon(taskImg, "common_img_task");
                        finishGa.SetActive(false);
                        finishEffect.SetActive(false);
                    }
                }
            }

            var reward = ChapterTaskMgr.I.GetCurrentChapterConfig();
            if (reward != null)
            {
                UITools.SetImageBySpriteName(chapterImage, reward.HeroImage);
            }
        }


        /// <summary>
        /// 反向初始化任务
        /// </summary>
        private void UnInitTask()
        {
            if (taskBtnWidget != null)
            {
                taskBtnWidget.Destroy();
                taskBtnWidget = null;
            }

            ////红点widget
            if (taskRedWidget != null)
            {
                taskRedWidget.Destroy();
                taskRedWidget = null;
            }
        }


        #endregion


        #region 数据刷新

        /// <summary>
        /// 刷新任务活动状态
        /// </summary>
        private void UpdateTaskActive()
        {
            bool show = UnlockMgr.I.CheckUnlock(UnlockFuncEnum.ChatperFunc) && LSwitchMgr.I.IsFunctionOpen(SwitchConfig.ChapterQuest);
            
            if (show)
            {
                if (!ChapterTaskGameData.I.mChapterQuestInfo.isUnlock || ChapterTaskGameData.I.mChapterQuestInfo.Config == null)
                {
                    ChapterTaskGameData.I.UnLockChapterQuest();
                    ChapterTaskGameData.I.GetChapterQuestInfo();
                }
            }

            taskBtnRoot.SetActive(show);
             
            var open = LSwitchMgr.I.IsFunctionOpen(SwitchConfig.DailyTask) && UnlockMgr.I.CheckUnlock(UnlockFuncEnum.DailyTask);

            //dailyTaskObj.SetActive(open);
            
            UpdateTaskNum();
        }

        /// <summary>
        /// 刷新显示任务红点数量
        /// </summary>
        private  void UpdateTaskNum()
        {
            if (dailyTaskRedWidget != null)
            {
                if (LSwitchMgr.I.IsFunctionOpen(SwitchConfig.DailyTask) && UnlockMgr.I.CheckUnlock(UnlockFuncEnum.DailyTask))
                {
                    var num = GameData.I.TaskData.GetTaskRedCountByType(DailyTaskEnum.Alliance).ContinueWith(t =>
                    {
                        //显示数量
                        dailyTaskRedWidget.SetData(t, false);
                    }); 
                }
                else
                {
                    dailyTaskRedWidget.SetData(0, false);
                }
            }

            //if (taskImg != null
            //    && DragonData.I.mDragonInfo?.ConfigData != null)
            //{
            //    UITools.SetImage(taskImg, DragonData.I.mDragonInfo.ConfigData.UpPreview, "Dragon");
            //}
        }


        /// <summary>
        /// 刷新任务开放状态
        /// </summary>
        private void UpdateTaskOpenState()
        {
            var isOpenChatBtn = true; //GameData.I.LevelData.CanToDoByGameLevel(MetaConfig.UnlockChatBtn, true);
            var isOpenChapterBtn = true;// GameData.I.LevelData.CanToDoByGameLevel(MetaConfig.ChapterQuestOpenLV, true);

            //if (isOpenChatBtn == true && isOpenChapterBtn == true
            //    && LSwitchMgr.I.IsFunctionOpen(SwitchConfig.ChapterQuest))
            //{
            //    if (!ChapterTaskGameData.I.mChapterQuestInfo.isUnlock
            //        || ChapterTaskGameData.I.mChapterQuestInfo.Config == null)
            //    {
            //        ChapterTaskGameData.I.UnLockChapterQuest();
            //        ChapterTaskGameData.I.GetChapterQuestInfo();
            //    }
            //    taskBtnRoot.SetActive(true);
            //}
            //else
            //    taskBtnRoot.SetActive(false);

            //刷新任务活动状态
            UpdateTaskActive();

        }


        #endregion



        #region 事件监听

        /// <summary>
        /// 点击任务按钮
        /// </summary>
        /// <param name="objs"></param>
        private void OnClickTaskBtn(System.Object[] objs)
        {
            if (!ChapterTaskMgr.I.CheckChapterTaskIsFinish())
            {
                if (ChapterTaskMgr.I.CheckGotoHeroPreview())
                {
                    PopupManager.I.ShowLayer<UIHeroSkinPreview>();
                    return;
                }
                if (curTask != null)
                {
                    if (curTask.state == QuestState.QuestStateFinish) // 完成直接领取
                    {
                        PopupManager.I.ShowPanel<UIChapterTask>();
                    }else if (curTask.state == QuestState.QuestStateReward)
                    {
                        OnClickChapterTaskBtn(null);
                    }
                    else // 未完成，直接跳转
                    {
                        curTask.GoTo();
                    }
                }
            }
            else
            {
                if (curTask != null)
                {
                    if (curTask.state == QuestState.QuestStateFinish) // 完成直接领取
                    {
                        TaskMgr.I.OpenDailyTask( DailyTaskEnum.Alliance);
                    }
                    else // 未完成，直接跳转
                    {
                        curTask.GoTo();
                        //var cfg = Cfg.C.CNewQuestCommon.I(curTask.cfgID);
                        //TaskUtils.GoTo(cfg.QuestType);
                    }
                }
                else
                {
                    TaskMgr.I.OpenDailyTask( DailyTaskEnum.Alliance);
                }
            }
        }

        /// <summary>
        /// 点击任务按钮
        /// </summary>
        /// <param name="objs"></param>
        private void OnClickChapterTaskBtn(System.Object[] objs)
        {
            if (ChapterTaskMgr.I.CheckGotoHeroPreview())
            {
                PopupManager.I.ShowLayer<UIHeroSkinPreview>();
                return;
            }
            PopupManager.I.ShowPanel<UIChapterTask>();
        }
        
        /// <summary>
        /// 点击任务按钮
        /// </summary>
        /// <param name="objs"></param>
        private void OnClickDailyTaskBtn(System.Object[] objs)
        {
            TaskMgr.I.OpenDailyTask( DailyTaskEnum.Alliance);
        }

        /// <summary>
        /// 任务活动状态改变
        /// </summary>
        /// <param name="objs"></param>
        [PopupEvent(TEventType.UIMain_Update_Task_Active)]
        private void OnUpdateTaskActive(object[] objs)
        {
            UpdateTaskActive();
        }

        /// <summary>
        /// 章节任务状态改变
        /// </summary>
        /// <param name="objs"></param>
        [PopupEvent(TEventType.ChapterQuestStateChange)]
        private void OnChapterQuestStateChange(object[] objs)
        {
            UpdateTaskNum();
        }

        /// <summary>
        /// 章节任务奖励
        /// </summary>
        /// <param name="objs"></param>
        [PopupEvent(TEventType.ChapterQuestReward)]
        private void OnChapterQuestReward(object[] objs)
        {
            UpdateTaskNum();
        }

        /// <summary>
        /// 章节任务奖励
        /// </summary>
        /// <param name="objs"></param>
        [PopupEvent(TEventType.ChapterReward)]
        private void OnChapterReward(object[] objs)
        {
            UpdateTaskNum();
        }

        /// <summary>
        /// 章节任务信息
        /// </summary>
        /// <param name="objs"></param>
        [PopupEvent(TEventType.ChapterQuestInfo)]
        private void OnChapterQuestInfo(object[] objs)
        {
            UpdateTaskNum();
        }
        
        [PopupEvent(TEventType.AddDailyQuest)]
        private void OnAddDailyQuest(object[] objs)
        {
            UpdateTaskNum();
        }
        
        [PopupEvent(TEventType.GetRewardDailyQuest)]
        private void OnGetRewardDailyQuest(object[] objs)
        {
            UpdateTaskNum();
        }
        
        [PopupEvent(TEventType.GetRewardDailyQuestChest)]
        private void OnGetRewardDailyQuestChest(object[] objs)
        {
            UpdateTaskNum();
        }
        
        [PopupEvent(TEventType.QuestInfoAck)]
        private void OnQuestInfoAck(object[] objs)
        {
            UpdateTaskNum();
        }

        /// <summary>
        /// 刷新龙数据信息
        /// </summary>
        /// <param name="objs"></param>
        [PopupEvent(TEventType.DragonDataRefresh)]
        private void OnUpdateDragonDataChange(object[] objs)
        {
            UpdateTaskNum();
        }

        /// <summary>
        /// 龙升级
        /// </summary>
        /// <param name="objs"></param>
        [PopupEvent(TEventType.DragonLevelUp)]
        private void OnDragonLevelUp(object[] objs)
        {
            UpdateTaskNum();
        }

        #endregion

    }
}
