﻿








using System.Collections.Generic;
using Common;
using cspb;
using THelper;
using TFW.Localization;
using UI;
using System.Linq;
using System;
using TFW;
using Game.Utils;
using Game.Data;
using Public;
using Config;
using Game.Config;
using DeepUI;
using Render;
using UnityEngine;
using Logic.Alliance.Achievement;

using Cysharp.Threading.Tasks;

using TFW.UI;
using K3;

namespace Logic
{
    /// <summary>
    /// 联盟主界面 Logic
    /// </summary>
    public class LAllianceMain : Ins<LAllianceMain>
    {
        #region Property

        /// <summary>
        /// 联盟成员界面 阶级展开状态
        /// 切换账号角色，退出联盟需要清理
        /// </summary>
        private Dictionary<int, bool> m_SelfAllianceExpandStateDic;
        private Dictionary<int,Cfg.G.CUnionClass> m_SelfUnionClassDic;
        /// <summary>
        /// 最新位置索引
        /// </summary>
        public int _lastScollIndex;

        /// <summary>
        /// 成员列表最新位置索引
        /// 由于成员列表界面每次都是新的实例，必须保存重新定位
        /// </summary>
        public int LastScollIndex
        {
            get
            {
                return _lastScollIndex;
            }
            set
            {
                _lastScollIndex = value;
                //D.Error?.Log("_lastScollIndex={0}", _lastScollIndex);
            }
        }

        #endregion

        #region Interface

        public void InitMessage()
        {
            // 联盟招募 ack
            MessageMgr.RegisterMsg<UnionRecruitAck>(this, OnUnionRecruitAck);
            //迁城
            MessageMgr.RegisterMsg<UnionRandomCoordAck>(this, OnUnionRandomCoordAck);

            // 联盟信息变更 ntf
            MessageMgr.RegisterMsg<UnionNtf>(this, OnUnionNtf);
            // 联盟阶级自定义 ntf
            MessageMgr.RegisterMsg<UnionRankNameNtf>(this, OnUnionRankNameNtf);
            // 联盟阶级自定义
            MessageMgr.RegisterMsg<SetUnionRankNameAck>(this, OnSetUnionRankNameAck);
            // 加入联盟 ntf
            MessageMgr.RegisterMsg<JoinUnionNtf>(this, OnJoinUnionNtf);
            // 退出联盟 ntf
            MessageMgr.RegisterMsg<QuitUnionNtf>(this, OnQuitUnionNtf);
            // 拉取联盟信息 ack
            MessageMgr.RegisterMsg<UnionAck>(this, OnUnionAck);
            // 解散 联盟 ack
            MessageMgr.RegisterMsg<DisbandUnionAck>(this, OnDisbandUnionAck);
            // 加入 联盟 ack
            MessageMgr.RegisterMsg<JoinUnionAck>(this, OnJoinUnionAck);
            //快速加入联盟 ack
            MessageMgr.RegisterMsg<QuickJoinUnionAck>(this, OnQuickJoinUnionAck);
            MessageMgr.RegisterMsg<QuickJoinUnionNtf>(this, OnQuickJoinUnionNtf);
            // 退出 联盟 ack
            MessageMgr.RegisterMsg<QuitUnionAck>(this, OnQuitUnionAck);
            // 同意|拒绝 玩家联盟申请 ack
            MessageMgr.RegisterMsg<HandleUnionApplyAck>(this, OnHandleUnionApplyAck);
            // 调整成员阶级 ack
            MessageMgr.RegisterMsg<UnionMemberClassAdjustAck>(this, OnUnionMemberClassAdjustAck);
            // 移除 成员 ack
            MessageMgr.RegisterMsg<RemoveUnionMemberAck>(this, OnRemoveUnionMemberAck);
            // 盟主转让 ack
            MessageMgr.RegisterMsg<UnionLeaderTransferAck>(this, OnUnionLeaderTransferAck);
            // 更新 联盟名称 ack
            MessageMgr.RegisterMsg<UpdateUnionNameAck>(this, OnUpdateUnionNameAck);
            // 更新 联盟简称 ack
            MessageMgr.RegisterMsg<UpdateUnionNickNameAck>(this, OnUpdateUnionNickNameAck);
            // 更新 联盟公告 ack
            MessageMgr.RegisterMsg<UpdateUnionDescAck>(this, OnUpdateUnionDescAck);
            MessageMgr.RegisterMsg<UpdateUnionDescNtf>(this, OnUpdateUnionDescNtf);
            //更新联盟宣言
            MessageMgr.RegisterMsg<UpdateUnionManifestoAck>(this, OnUpdateUnionManifestoAck);

            // 更新 联盟旗帜 ack
            MessageMgr.RegisterMsg<UpdateUnionFlagAck>(this, OnUpdateUnionFlagAck);
            // 更新 联盟国家 ack
            MessageMgr.RegisterMsg<UpdateUnionLangAck>(this, OnUpdateUnionLangAck);
            // 更新 联盟语言 ack
            MessageMgr.RegisterMsg<UpdateUnionLanguageAck>(this, OnUpdateUnionLanguageAck);
            // 更新 联盟语言 ack
            MessageMgr.RegisterMsg<UpdateUnionLimitLvAck>(this, OnUpdateUnionLimitLvAck);

            // 更新 联盟声请条件 ack
            MessageMgr.RegisterMsg<UpdateUnionJoinConditionAck>(this, OnUpdateUnionJoinConditionAck);

            // 晋升条件修改 ack
            MessageMgr.RegisterMsg<UnionPromotionPowerAck>(this, OnUnionPromotionPowerAck);

            // 老的晋升开关 ack
            //MessageMgr.RegisterMsg<UnionPromotionOpenAck>(this, OnUnionPromotionOpenAck);

            // 晋升开关 ack
            MessageMgr.RegisterMsg<UnionPromotionClassOpenAck>(this, OnUnionPromotionOpenAck);

            //弹劾
            MessageMgr.RegisterMsg<UnionLeaderDismissAck>(this, OnUnionLeaderDismissAck);


            //踢人设置信息
            MessageMgr.RegisterMsg<UnionMemberKickOutSettingInfoAck>(this, OnUnionMemberKickOutSettingInfoAck);

            //踢人设置信息
            MessageMgr.RegisterMsg<UnionMemberKickOutSettingAck>(this, OnUnionMemberKickOutSettingAck);

            //上线提醒
            MessageMgr.RegisterMsg<UnionMemberMessagePushAck>(this, OnUnionMemberMessagePushAck);

            //接管联盟协议
            MessageMgr.RegisterMsg<UnionTakeOverLeaderAck>(this, OnUnionTakeOverLeaderAck);

            MessageMgr.RegisterMsg<SysUnionChangeAck>(this, OnSysUnionChangeAck);

            MessageMgr.RegisterMsg<AIUnionPayForLeaderAck>(this, OnAIUnionPayForLeaderAck);

            #region  联盟日志

            ///联盟日志查询
            MessageMgr.RegisterMsg<UnionLogAck>(this, OnUnionLogAck);
            MessageMgr.RegisterMsg<UnionMemLogsNtf>(this, OnUnionMemLogsNtf);
            MessageMgr.RegisterMsg<UnionActLogsNtf>(this, OnUnionActLogsNtf);
            MessageMgr.RegisterMsg<UnionWarLogsNtf>(this, OnUnionWarLogsNtf);
            MessageMgr.RegisterMsg<UnionOfficialPositionSetAck>(this, OnAppointOfficial);

            #endregion

            ///联盟成就
            //LAllianceAchievement.I.Init();

            //联盟活动礼物
            LAllianceActivityGift.I.Init();
            //LAllianceNewAchievement.I.Init();
        }
        public async UniTask Init()
        {
            ResetAllianceInfo();
            ResetOtherAllianceInfo();

            LastScollIndex = -1;
            m_SelfAllianceExpandStateDic = new Dictionary<int, bool>();

            m_SelfUnionClassDic = new Dictionary<int, Cfg.G.CUnionClass>();
            var UnionClassDic = await Cfg.C.CUnionClass.RawDictAsync();
            foreach (var item in UnionClassDic)
            {
                m_SelfUnionClassDic.Add(item.Key, item.Value);
            }
            
            // 联盟申请者列表
            m_AllianceApplies = new List<UnifyPlayerInfo>();

            // 联盟官职申请列表
            m_AllianceOfficerApplys = new List<ClassApplyRecordInfo>();

            // 各阶级成员数量
            m_ClassMemberMax = new Dictionary<int, int>();

            var a1 = 18110001;
            var a2 = 18110002;
            var a3 = 18110003;
            var a4 = 18110004;
            var a5 = 18110005;

            var cls = new int[]
            {
                a1, // 阶级1-试用成员
                a2, // 阶级2-普通成员
                a3, // 阶级3-核心成员
                a4, // 阶级4
                a5, // 阶级5-盟主
            };


            ////todo
            //var cls = new int[]
            //{
            //    18110001, // 阶级1-试用成员
            //    18110002, // 阶级2-普通成员
            //    18110003, // 阶级3-核心成员
            //    18110004, // 阶级4-4class
            //    18110005, // 阶级5-盟主
            //};

            for (int i = 0; i < cls.Length; i++)
            {
                var clsNum = cls[i];
                var clsInfo = await Cfg.C.CUnionClass.GetConfigAsync(clsNum);

                var num = 0;
                if (!m_ClassMemberMax.TryGetValue(clsInfo.Class, out num))
                {
                    num = 0;
                }

                num = num + clsInfo.Num;

                m_ClassMemberMax[clsInfo.Class] = num;
            }

        }

        /// <summary>
        /// 是否需要立即跳转迁城
        /// </summary>
        private bool needNowJumpMoveCity;

        /// <summary>
        /// 迁城请求
        /// </summary>
        public void ReqUnionRandomCoord(bool needNowJump = true)
        {
            this.needNowJumpMoveCity = needNowJump;
            UnionRandomCoordReq req = new UnionRandomCoordReq();
            MessageMgr.Send(req);
        }

        /// <summary>
        /// AI联盟接管 花钱
        /// </summary>
        public void ReqAIUnionPayForLeaderReq() 
        {
            MessageMgr.Send(new AIUnionPayForLeaderReq());
        }


        /// <summary>
        /// 邀请迁城坐标
        /// </summary>
        public Coord moveCoord = null;

        /// <summary>
        /// 联盟迁城坐标
        /// </summary>
        /// <param name="obj"></param>
        private void OnUnionRandomCoordAck(UnionRandomCoordAck obj)
        {
            //D.Error?.Log("OnUnionRandomCoordAck");
            //UIChat.I?.OnUnionRandomCoordAck(obj);
            if (obj.errCode != ErrCode.ErrCodeSuccess)
                return;

            D.Debug?.Log("UnionRandomCoordAck needNowJumpMoveCity={0}", needNowJumpMoveCity);
            var coord = obj.coord;
            var x = coord.X / 1000;
            var z = coord.Z / 1000;
            moveCoord = coord;
            if (coord != null)
            {
                EventMgr.FireEvent(TEventType.UnionRandomCoordAck, obj);
                if (this.needNowJumpMoveCity)
                {
                    needNowJumpMoveCity = false;
                    CityTeleportManager.I.TryStartTeleport(new Vector3(x, 0, z));
                    //CityTeleportManager

                }
            }
            else
            {
                D.Warning?.Log("coord is null!");
            }
        }

        /// <summary>
        /// 设置展开状态
        /// </summary>
        /// <param name="index"></param>
        /// <param name="state"></param>
        public void SetExpandState(int index, bool state)
        {
            if (m_SelfAllianceExpandStateDic != null)
            {
                m_SelfAllianceExpandStateDic[index] = state;
            }
        }

        /// <summary>
        /// 获取展开状态
        /// </summary>
        /// <param name="index"></param>
        /// <returns></returns>
        public bool GetExpandState(int index)
        {
            if (m_SelfAllianceExpandStateDic != null)
            {
                if (m_SelfAllianceExpandStateDic.TryGetValue(index, out var state))
                {
                    return state;
                }
            }
            return true;
        }

        /// <summary>
        /// 弹劾
        /// </summary>
        /// <param name="obj"></param>
        private void OnUnionLeaderDismissAck(UnionLeaderDismissAck obj)
        {
            if (obj.errCode == ErrCode.ErrCodeSuccess)
            {
                EventMgr.FireEvent(TEventType.UnionLeaderDismissAck);
            }
        }

        /// <summary>
        /// 自动晋升开关
        /// </summary>
        /// <param name="obj"></param>
        private void OnUnionPromotionOpenAck(UnionPromotionClassOpenAck obj)
        {
            if (obj.errCode == ErrCode.ErrCodeSuccess)
            {
                if (this.m_AllianceInfo != null)
                {
                    this.m_AllianceInfo.Promotion = obj.up;
                }
            }
            EventMgr.FireEvent(TEventType.UnionPromotionOpenAck, obj.errCode == ErrCode.ErrCodeSuccess);
        }

        /// <summary>
        /// 自动晋升条件
        /// </summary>
        /// <param name="obj"></param>
        private void OnUnionPromotionPowerAck(UnionPromotionPowerAck obj)
        {
            if (obj.errCode == ErrCode.ErrCodeSuccess)
            {
                if (this.m_AllianceInfo != null)
                {
                    this.m_AllianceInfo.Promotion = obj.up;
                }
            }
            EventMgr.FireEvent(TEventType.UnionPromotionPowerAck, obj.errCode == ErrCode.ErrCodeSuccess);
        }

        /// <summary>
        /// 联盟招募
        /// </summary>
        /// <param name="obj"></param>
        private void OnUnionRecruitAck(UnionRecruitAck obj)
        {
            if (obj.errCode == ErrCode.ErrCodeSuccess)
            {
                UIChatHelper.I.ShareRecruitToWorldChannel();
            }
            else 
            {
                //FloatTips.I.FloatErrcodeMsg(obj.errCode);
            }
            //EventMgr.FireEvent(TEventType.UnionRecruitAck, obj.errCode == ErrCode.ErrCodeSuccess, obj.lastTime);
        }

        public void DeInit()
        {
            MessageMgr.UnregisterMsg(this);
            //LAllianceAchievement.I.DeInit();
            LAllianceNewAchievement.I.DeInit();
            LAllianceActivityGift.I.UnInit();

            m_ClassMemberMax = null;
            m_AllianceOfficerApplys = null;
            m_AllianceApplies = null;
            m_OtherAllianceInfo = null;
            m_AllianceInfo = null;
            m_SelfAllianceExpandStateDic = null;
        }

        /// <summary>
        /// Reset AllianceInfo
        /// </summary>
        public void ResetAllianceInfo()
        {
            // 自己的联盟信息
            m_AllianceInfo = new AllianceInfo();
        }

        /// <summary>
        /// Reset OtherAllianceInfo
        /// </summary>
        public void ResetOtherAllianceInfo()
        {
            // 其他人的联盟信息
            m_OtherAllianceInfo = new AllianceInfo();
        }

        /// <summary>
        /// Clean Data
        /// </summary>
        public void CleanData(long oldUnionId)
        {
            ResetAllianceInfo();
            //清理数据信息
            AllianceGameData.I.AllianceWarData.ClearOwnerCentralCityInfo();

            // 联盟官职申请列表
            m_AllianceOfficerApplys = new List<ClassApplyRecordInfo>();
            LAllianceTerritoryBuilding.I.ExitUnion(oldUnionId);
            LAllianceAllyCity.I.ExitUnion();
            LAllianceAltar.I.ExitUnion();
            LAllianceBuildingRecord.I.ExitUnion();
            LAllianceHelp.I.ExitUnion();
            GameData.I.RallyData.ExitUnion();
            LAllianceMillitaryAlert.I.ExitUnion();
            LMigrationServer.I.ExitUnion();
            AllianceGameData.I.AlliancMsgBoardData.ExitUnion(oldUnionId);
        }

        /// <summary>
        /// 获取联盟成员阶级信息
        /// </summary>
        /// <param name="playerId"></param>
        /// <param name="unionInfo"></param>
        /// <returns></returns>
        public Cfg.G.CUnionClass GetMemberClassInfo(long playerId, AllianceInfo unionInfo = null)
        {
            var allianceInfo = unionInfo == null ? m_AllianceInfo : unionInfo;
            var members = allianceInfo.Members;

            for (int i = 0; i < members.Count; i++)
            {
                if (members[i].PlayerId == playerId)
                {
                    //return Cfg.C.CUnionClass.I(members[i].ClassCfgId);
                    return m_SelfUnionClassDic[members[i].ClassCfgId];
                }
            }

            return null;
        }

        /// <summary>
        /// 获取联盟旗帜信息
        /// </summary>
        /// <returns></returns>
        public UnionFlagInfo GetUnionFlag()
        {
            return GetUnionFlagByFlagID(m_AllianceInfo.Flag);
        }

        public uint GetUnionFlagByInt()
        {
            return m_AllianceInfo.Flag;
        }

        /// <summary>
        /// 获取联盟旗帜信息
        /// </summary>
        /// <returns></returns>
        public UnionFlagInfo GetUnionFlagByFlagID(uint flagID)
        {
            uint shading, background, pattern, patternColor;
            LAllianceMgr.I.UnionFlag2Table(flagID,
                out shading, out background, out pattern, out patternColor);

            var unionFlag = new UnionFlagInfo()
            {
                Shading = (int)shading,
                Background = (int)background,
                Pattern = (int)pattern,
                PatternColor = (int)patternColor,
            };

            return unionFlag;
        }

        /// <summary>
        /// 获取联盟简称
        /// </summary>
        /// <returns></returns>
        public string GetUnionNickName()
        {
            return m_AllianceInfo.NickName;
        }

        public Dictionary<int, int> GetMedals() 
        {
            if (m_AllianceInfo == null)
                return new Dictionary<int, int>();
            return m_AllianceInfo.medals;
        }

        /// <summary>
        /// 获取联盟等级限制
        /// </summary>
        public int GetUnionLimitLv()
        {
            return m_AllianceInfo.LimitLv;
        }
        /// <summary>
        /// 获取联盟名称
        /// </summary>
        public string GetUnionName()
        {
            return m_AllianceInfo.Name;
        }

        /// <summary>
        /// 获取联盟公告
        /// </summary>
        public string GetUnionDesc()
        {
            return m_AllianceInfo.Desc;
        }

        public string GetUnionModifyName => m_AllianceInfo.lastModifyName;

        public long GetUnionModifyTime => m_AllianceInfo.lastModifyTime;

        /// <summary>
        /// 获取联盟宣言
        /// </summary>
        public string GetUnionManifesto()
        {
            return m_AllianceInfo.Manifesto;
        }
        /// <summary>
        /// 获取联盟PowerRank
        /// </summary>
        public long GetUnionPowerRank()
        {
            return m_AllianceInfo.PowerRank;
        }

        public async UniTask<string> GetUnionRankName(int cfgID)
        {
            if (m_AllianceInfo != null && m_AllianceInfo.UnionRankInfos != null)
            {
                foreach (var rankInfo in m_AllianceInfo.UnionRankInfos)
                {
                    if (rankInfo.RankCfgId == cfgID)
                    {
                        if (rankInfo.RankName.Trim().Equals(string.Empty))
                        {
                            var cfgUnionClass = await Cfg.C.CUnionClass.GetConfigAsync(cfgID);
                            if (cfgUnionClass != null)
                            {
                                return LocalizationMgr.Get(cfgUnionClass.ClassName.Txt);
                            }
                        }
                        else
                        {
                            return rankInfo.RankName;
                        }
                    }
                }
            }

            var cfg = await Cfg.C.CUnionClass.GetConfigAsync(cfgID);
            if (cfg != null)
            {
                return LocalizationMgr.Get(cfg.ClassName.Txt);
            }
            return string.Empty;
        }

        /// <summary>
        /// 获取联盟Power
        /// </summary>
        public long GetUnionPower()
        {
            return m_AllianceInfo.Power;
        }

        /// <summary>
        /// 获取联盟PowerRank
        /// </summary>
        public long GetUnionUraniumGauge()
        {
            return m_AllianceInfo.UraniumGauge;
        }

        /// <summary>
        /// 获取联盟LangID
        /// </summary>
        public int GetUnionLangId()
        {
            return m_AllianceInfo.Lang;
        }

        /// <summary>
        /// 入盟是否需要申请
        /// </summary>
        public bool IsUnionNeedApply()
        {
            return m_AllianceInfo.NeedApply;
        }

        /// <summary>
        /// 获取联盟id
        /// </summary>
        public long GetUnionId()
        {
            return m_AllianceInfo.Id;
        }

        /// <summary>
        /// 获取联盟盟主名称
        /// </summary>
        /// <returns></returns>
        public string GetUnionLeaderName()
        {
            return m_AllianceInfo.LeaderHead?.name;
        }

        /// <summary>
        /// 获取联盟盟主id
        /// </summary>
        public long GetUnionLeaderId()
        {
            return m_AllianceInfo.LeaderId;
        }

        /// <summary>
        /// 获取联盟盟主头像id
        /// </summary>
        public int GetUnionLeaderAvatarId()
        {
            if (m_AllianceInfo.LeaderHead == null)
                return 0;

            return m_AllianceInfo.LeaderHead.avatarCfgID;
        }

        /// <summary>
        /// 获取联盟成员数量 cur, max
        /// </summary>
        public void GetUnionMemberNum(out int cur, out int max)
        {
            cur = m_AllianceInfo.Num;
            max = m_AllianceInfo.MaxNum;
        }

        /// <summary>
        /// 获取联盟成员
        /// </summary>
        /// <param name="includeLeader">是否包含盟主</param>
        /// <returns></returns>
        public List<UnionMemberInfo> GetUnionMembers(bool includeLeader = true, AllianceInfo otherAllianceInfo = null)
        {
            var members = new List<UnionMemberInfo>();
            var tempInfo = m_AllianceInfo;
            if (otherAllianceInfo != null)
            {
                tempInfo = otherAllianceInfo;
            }

            for (int i = 0; i < tempInfo.Members.Count; i++)
            {
                var m = tempInfo.Members[i];
                if (!includeLeader)
                {
                    if (m.PlayerId == LPlayer.I.GetPlayerId())
                        continue;
                }
                var mm = new UnionMemberInfo()
                {
                    PlayerId = m.PlayerId,
                    PlayerHead = m.PlayerHead,
                    ClassCfgId = m.ClassCfgId,
                    Cls = m.Cls,
                    Power = m.Power,
                    LogoutTs = m.LogoutTs,
                    Kill = m.Kill,
                    CityLevel = m.CityLevel,
                    IsOnline = m.IsOnline,
                    UnionPoint = m.UnionPoint,
                    IsTakeOver = m.IsTakeOver,
                    officialPosition = m.officialPosition,
                    gender = m.gender,
                };

                members.Add(mm);
            }

            return members;
        }

        public bool GetPlayerIsTakeOver()
        {
            for (int i = 0; i < m_AllianceInfo.Members.Count; i++)
            {
                var m = m_AllianceInfo.Members[i];
                if (m.PlayerId == LPlayer.I.GetPlayerId())
                    return m.IsTakeOver;
            }
            return false;
        }

        /// <summary>
        /// 获取联盟成员
        /// </summary>
        /// <param name="includeLeader">是否包含盟主</param>
        /// <returns></returns>
        public List<UnionMemberInfo> GetUnionMembers(int cls)
        {
            var members = new List<UnionMemberInfo>();
            var tempInfo = m_AllianceInfo;
            for (int i = 0; i < tempInfo.Members.Count; i++)
            {
                var m = tempInfo.Members[i];
                if (m.Cls >= cls)
                {
                    var mm = new UnionMemberInfo()
                    {
                        PlayerId = m.PlayerId,
                        PlayerHead = m.PlayerHead,
                        ClassCfgId = m.ClassCfgId,
                        Cls = m.Cls,
                        Power = m.Power,
                        LogoutTs = m.LogoutTs,
                        Kill = m.Kill,
                        CityLevel = m.CityLevel,
                        IsOnline = m.IsOnline,
                        UnionPoint = m.UnionPoint,
                        IsTakeOver = m.IsTakeOver,
                        officialPosition = m.officialPosition,
                        gender = m.gender,
                    };
                    members.Add(mm);
                }
            }

            return members;
        }

        public List<UnionMemberInfo> GetUnionMembersRankByClass(int classId)
        {
            var members = GetUnionMembers(classId);
            members.Sort(MemberSort);
            for (int i = 0; i < members.Count; i++)
            {
                members[i].index = i + 1;
            }
            return members;
        }

        public List<UnionMemberInfo> GetUnionMembersRank()
        {
            var members = GetUnionMembers();
            members.Sort(MemberSort);
            for(int i=0;i<members.Count;i++)
            {
                members[i].index = i + 1;
            }
            return members;
        }

        /// <summary>
        /// 排序
        /// </summary>
        /// <param name="a"></param>
        /// <param name="b"></param>
        /// <returns></returns>
        private int MemberSort(UnionMemberInfo a, UnionMemberInfo b)
        {
            if (b.UnionPoint > a.UnionPoint)
                return 1;
            else if (b.UnionPoint < a.UnionPoint)
                return -1;
            else
                return 0;

        }
        /// <summary>
        /// 获取单个联盟成员
        /// </summary>
        /// <param name="playerId"></param>
        /// <returns></returns>
        public UnionMemberInfo GetUnionMembersWithId(long playerId, bool isCreateNewStruct = false)
        {
            for (int i = 0; i < m_AllianceInfo.Members.Count; i++)
            {
                var m = m_AllianceInfo.Members[i];
                if (m.PlayerId == playerId)
                {
                    if(isCreateNewStruct)
                    {
                        return new UnionMemberInfo()
                        {
                            PlayerId = m.PlayerId,
                            PlayerHead = m.PlayerHead,
                            ClassCfgId = m.ClassCfgId,
                            Cls = m.Cls,
                            Power = m.Power,
                            LogoutTs = m.LogoutTs,
                            Kill = m.Kill,
                            CityLevel = m.CityLevel,
                            IsOnline = m.IsOnline,
                            officialPosition = m.officialPosition,
                            gender = m.gender,
                        };
                    }
                    else
                    {
                        return m;
                    }
                }
            }

            return null;
        }

        /// <summary>
        /// 获取联盟内 在线成员数量
        /// </summary>
        /// <returns></returns>
        public int GetOnlineNumber() 
        {
            int count = 0;
            foreach (var item in m_AllianceInfo.Members)
            {
                if (item.IsOnline)
                    count++;
            }
            return count;
        }
        /// <summary>
        /// 获取剩余只为数量
        /// </summary>
        /// <param name="id">配置id</param>
        /// <returns></returns>
        public int GetAppointOfficialCountById(int id)
        {
            int count = 0;
            var allMembers = GetAllUnionMembers();
            for (int i = 0;i< allMembers.Count;i++)
            {
                if (allMembers[i].officialPosition == id)
                    count += 1;
            }
            return count;
        }
        public List<UnionMemberInfo> GetUnionMemberInfos(int cfgId)
        {
            var list = new List<UnionMemberInfo>();
            var allMembers = GetAllUnionMembers();
            for (int i = 0; i < allMembers.Count; i++)
            {
                if (allMembers[i].officialPosition == cfgId)
                    list.Add(allMembers[i]);
            }
            return list;
        }
        /// <summary>
        /// 获取联盟所有成员，并且按照权限、是否在线、战力排序
        /// </summary>
        public List<UnionMemberInfo> GetAllUnionMembers()
        {
            var allianceInfo = m_AllianceInfo;
            var members = new List<UnionMemberInfo>();
            for (int i = (int)AllianceClassEnum.ClassCnt; i > 0; i--)
            {
                var list = GetUnionMembersWithClass((AllianceClassEnum)i, allianceInfo);
                list.Sort((a, b) =>
                {
                    if (a.IsOnline && !b.IsOnline)
                    {
                        // a在线, b不在线
                        return -1;
                    }
                    if (!a.IsOnline && b.IsOnline)
                    { // a不在线, b在线
                        return 1;
                    }
                    return a.Power > b.Power ? -1 : 1;
                });
                for (int s = 0; s < list.Count; s++)
                {
                    members.Add(list[s]);
                }
            }
            return members;
        }

        /// <summary>
        /// 获取指定阶级的联盟成员
        /// </summary>
        public List<UnionMemberInfo> GetUnionMembersWithClass(AllianceClassEnum cls, AllianceInfo unionInfo = null, bool filterSelf = false)
        {
            var allianceInfo = unionInfo == null ? m_AllianceInfo : unionInfo;
            var members = new List<UnionMemberInfo>();
            for (int i = 0; i < allianceInfo.Members.Count; i++)
            {
                var m = allianceInfo.Members[i];
                if (m.Cls == (int)cls)
                {
                    if (filterSelf)
                    {
                        if (m.PlayerId == LPlayer.I.PlayerID)
                        {
                            continue;
                        }
                    }
                    var mm = new UnionMemberInfo()
                    {
                        PlayerId = m.PlayerId,
                        PlayerHead = m.PlayerHead,
                        ClassCfgId = m.ClassCfgId,
                        Cls = m.Cls,
                        Power = m.Power,
                        LogoutTs = m.LogoutTs,
                        Kill = m.Kill,
                        CityLevel = m.CityLevel,
                        IsOnline = m.IsOnline,
                        officialPosition = m.officialPosition,
                        gender = m.gender,

                    };

                    members.Add(mm);
                }
            }

            return members;
        }

        /// <summary>
        /// 获取指定阶级的联盟成员最大数量
        /// </summary>
        /// <param name="cls"></param>
        public int GetClassMembersMax(AllianceClassEnum cls)
        {
            return m_ClassMemberMax[(int)cls];
        }

        /// <summary>
        /// 获取指自动晋升的开关状态
        /// </summary>
        /// <param name="cls"></param>
        public bool GetPromotionState(AllianceClassEnum cls)
        {
            if (this.m_AllianceInfo != null && this.m_AllianceInfo.Promotion != null)
            {
                var classId = (int)AllianceClassIdEnum.ClassIdEx + (int)cls;
                foreach (var item in this.m_AllianceInfo.Promotion.pc)
                {
                    if (item.classID == classId)
                    {
                        return item.isOpen;
                    }
                }
                //return this.m_AllianceInfo.Promotion.IsOpen;
            }
            return false;
        }

        /// <summary>
        /// 获取指定阶级的自动晋升战力条件
        /// </summary>
        /// <param name="cls"></param>
        public long GetPromotionClassPower(AllianceClassEnum cls)
        {
            var classId = (int)AllianceClassIdEnum.ClassIdEx + (int)cls;
            if (this.m_AllianceInfo != null && this.m_AllianceInfo.Promotion != null)
            {
                foreach (var item in this.m_AllianceInfo.Promotion.pc)
                {
                    if (item.classID == classId)
                    {
                        return item.power;
                    }
                }
            }
            return 0;
        }

        /// <summary>
        /// 拉取自动踢人设置信息
        /// </summary>
        public void GetKickOutSettingInfo()
        {
            UnionMemberKickOutSettingInfoReq req = new UnionMemberKickOutSettingInfoReq();
            MessageMgr.Send(req);
        }

        public Dictionary<int, UnionKickSetting> AutoKickOutInfo { get; private set; }

        /// <summary>
        /// 踢人设置信息
        /// </summary>
        /// <param name="ack"></param>
        public void OnUnionMemberKickOutSettingInfoAck(UnionMemberKickOutSettingInfoAck ack)
        {
            //AutoKickOutEnable = ack.enable;
            //AutoKickOutHours = ack.hours;
            AutoKickOutInfo = ack.settings;
            EventMgr.FireEvent(TEventType.UnionMemberKickOutSettingInfoAck); 
        }

        /// <summary>
        /// 自动踢人
        /// </summary>
        /// <param name="ack"></param>
        public void OnUnionMemberKickOutSettingAck(UnionMemberKickOutSettingAck ack)
        {
            if (ack.errCode == ErrCode.ErrCodeSuccess)
            {
                AutoKickOutInfo = ack.settings;
                //AutoKickOutEnable = ack.enable;
                //AutoKickOutHours = ack.hours;
            }
            EventMgr.FireEvent(TEventType.UnionMemberKickOutSettingAck, ack.errCode == ErrCode.ErrCodeSuccess);
        }

        /// <summary>
        /// 自动踢人设置
        /// </summary>
        public void ReqUnionMemberKickOutSetting(Dictionary<int, UnionKickSetting> dic)
        {
            if (dic.Count > 0)
            {
                UnionMemberKickOutSettingReq req = new UnionMemberKickOutSettingReq();
                foreach (var item in dic)
                {
                    req.settings[item.Key] = item.Value;
                }
                MessageMgr.Send(req);
            }
        }
        //设置官职
        public void AppointOfficialReq(long pid,int position)
        {
            UnionOfficialPositionSetReq req = new UnionOfficialPositionSetReq()
            {
                pid = pid,
                position = position
            };
            MessageMgr.Send(req);
        }
        private void OnAppointOfficial(UnionOfficialPositionSetAck unionOfficialPositionSetAck)
        {
            if(unionOfficialPositionSetAck.errCode == ErrCode.ErrCodeSuccess)
            {
                if(unionOfficialPositionSetAck.position!= 0)
                {
                    FloatTips.I.FloatMsg(LocalizationMgr.Get("Alliance_Official_tips_6"));
                }else
                {
                    FloatTips.I.FloatMsg(LocalizationMgr.Get("Alliance_Official_tips_7"));
                }
                var member = GetUnionMembersWithId(unionOfficialPositionSetAck.pid);
                member.officialPosition = unionOfficialPositionSetAck.position;
                EventMgr.FireEvent(TEventType.AllianceMemberOfficialChange);
            }
        }

        /// <summary>
        /// 提醒上线推送
        /// </summary>
        /// <param name="playerId"></param>
        public void ReqUnionMemberMessagePush(long playerId)
        {
            UnionMemberMessagePushReq req = new UnionMemberMessagePushReq();
            req.targetId = playerId;
            req.typ = UnionMemberMessagePushType.PUSH_ONLINE;
            MessageMgr.Send(req);
        }

        /// <summary>
        /// 提醒上线推送
        /// </summary>
        /// <param name="obj"></param>
        private void OnUnionMemberMessagePushAck(UnionMemberMessagePushAck obj)
        {
            D.Debug?.Log("UnionMemberMessagePushAck errCode={0}", obj.errCode);
        }

        /// <summary>
        /// 接管联盟返回
        /// </summary>
        /// <param name="obj"></param>
        private void OnUnionTakeOverLeaderAck(UnionTakeOverLeaderAck obj)
        {
            if(obj.errCode == ErrCode.ErrCodeSuccess)
            {
                //接管成功刷新界面
                for (int i = 0; i < m_AllianceInfo.Members.Count; i++)
                {
                    var m = m_AllianceInfo.Members[i];
                    if (m.PlayerId == LPlayer.I.GetPlayerId())
                        m_AllianceInfo.Members[i].IsTakeOver = true;
                }
                EventMgr.FireEvent(TEventType.AllianceMemberClassChange);
            }
        }

        private void OnSysUnionChangeAck(SysUnionChangeAck obj)
        {
            if (obj.errCode == ErrCode.ErrCodeSuccess)
            {
                //转盟成功刷新界面

               PopupManager.I.ClosePopup<UI.Alliance.UIAllianceMain>();
            }
        }

        void OnAIUnionPayForLeaderAck(AIUnionPayForLeaderAck ack) 
        {
            if (ack.errCode != ErrCode.ErrCodeSuccess)
                ack.errCode.ToFloatErrCode();
        }

        /// <summary>
        /// 联盟日志
        /// </summary>
        /// <param name="ack"></param>
        void OnUnionLogAck(UnionLogAck ack) 
        {
            if(ack.errCode != ErrCode.ErrCodeSuccess)
                FloatTips.I.FloatErrcodeMsg(ack.errCode);
        }
        /// <summary>
        /// 联盟成员页签日志推送
        /// </summary>
        /// <param name="ntf"></param>
        void OnUnionMemLogsNtf(UnionMemLogsNtf ntf) 
        {
            if (ntf.errCode != ErrCode.ErrCodeSuccess)
            {
                FloatTips.I.FloatErrcodeMsg(ntf.errCode);
                return;
            }
            EventMgr.FireEvent(TEventType.AllianceLogsRefush, 1, ntf.Logs);
        }

        /// <summary>
        /// 联盟活动页签日志推送
        /// </summary>
        /// <param name="ntf"></param>
        void OnUnionActLogsNtf(UnionActLogsNtf ntf)
        {
            if (ntf.errCode != ErrCode.ErrCodeSuccess)
            {
                FloatTips.I.FloatErrcodeMsg(ntf.errCode);
                return;
            }
            EventMgr.FireEvent(TEventType.AllianceLogsRefush, 2, ntf.Logs);
        }

        /// <summary>
        /// 联盟战争页签日志推送
        /// </summary>
        /// <param name="ntf"></param>
        void OnUnionWarLogsNtf(UnionWarLogsNtf ntf)
        {
            if (ntf.errCode != ErrCode.ErrCodeSuccess)
            {
                FloatTips.I.FloatErrcodeMsg(ntf.errCode);
                return;
            }

            //EventMgr.FireEvent(TEventType.AllianceLogsRefush, 3, ntf.Logs);
        }

        /// <summary>
        /// 获取联盟申请列表
        /// </summary>
        public List<UnifyPlayerInfo> GetUnionApplies()
        {
            return m_AllianceApplies;
        }

        /// <summary>
        /// Update UnionApplies
        /// </summary>
        public void UpdateUnionApplies(long playerId, bool isAdd)
        {
            if (isAdd)
            {
            }
            else
            {
                var applies = m_AllianceApplies;
                for (int i = 0; i < applies.Count; i++)
                {
                    if (applies[i].ID == playerId)
                    {
                        applies.RemoveAt(i);
                        break;
                    }
                }
            }
        }

        /// <summary>
        /// 判断是否拥有权限
        /// </summary>
        /// <returns></returns>
        public bool HavePermission(long playerId, AlliancePermissionEnum perm)
        {
            var clsInfo = GetMemberClassInfo(playerId);
            if (clsInfo == null)
            {
                return false;
            }

            var cnt = clsInfo.Permission.Count;
            var flag = (int)perm;
            for (int i = 0; i < cnt; i++)
            {
                int p = ConvertUtils.GetIntFromString(clsInfo.Permission[i]);
                if (p == flag)
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 获取最低权限等级
        /// </summary>
        /// <param name="perm"></param>
        /// <returns></returns>
        public async UniTask<int> GetLostLevelPermission(int perm)
        {
            //注入要求int数组初始化,必须用变量接收,传入进去,否则注入会失败
            //Warning: not support il[IL_003b: ldtoken <PrivateImplementationDetails>/__StaticArrayInitTypeSize=16
            var a1 = 18110001;
            var a2 = 18110002;
            var a3 = 18110003;
            var a4 = 18110004;
            var a5 = 18110005;

            var cls = new int[]
            {
                a1, // 阶级1-试用成员
                a2, // 阶级2-普通成员
                a3, // 阶级3-核心成员
                a4, // 阶级4
                a5, // 阶级5-盟主
            };

            var perms = new List<Cfg.G.CUnionClass>();
            for (int i = 0; i < cls.Length; i++)
            {
                var clsInfo = await Cfg.C.CUnionClass.GetConfigAsync(cls[i]);
                perms.Add(clsInfo);
            }
            for (int i = 0; i < cls.Length; i++)
            {
                var clsInfo = perms[i];
                var cnt = clsInfo.Permission.Count;

                for (int j = 0; j < cnt; j++)
                {
                    int p = ConvertUtils.GetIntFromString(clsInfo.Permission[j]);
                    if (p == perm)
                    {
                        return i;
                    }
                }
            }
            return 0;
        }

        /// <summary>
        /// 获取联盟信息
        /// </summary>
        public AllianceInfo GetUnionInfo()
        {
            return m_AllianceInfo;
        }

        /// <summary>
        /// 获取其它人联盟信息
        /// </summary>
        public AllianceInfo GetOtherUnionInfo()
        {
            return m_OtherAllianceInfo;
        }

        /// <summary>
        /// 获取官职申请记录列表
        /// </summary>
        /// <param name="classCfgId"></param>
        /// <returns></returns>
        public List<UnionMemberInfo> GetOfficerApplys(int classCfgId)
        {
            var applys = m_AllianceOfficerApplys;
            for (int i = 0; i < applys.Count; i++)
            {
                if (applys[i].Record.cfgID == classCfgId)
                {
                    return applys[i].List;
                }
            }

            return new List<UnionMemberInfo>();
        }

        /// <summary>
        /// 判断是否申请了classCfgID对应的官职
        /// </summary>
        public bool IsOfficerApplyed(int classCfgId, long playerId)
        {
            var applys = m_AllianceOfficerApplys;
            for (int i = 0; i < applys.Count; i++)
            {
                if (applys[i].Record.cfgID == classCfgId)
                {
                    var applicants = applys[i].Record.applicants;
                    for (int j = 0; j < applicants.Count; j++)
                    {
                        if (applicants[j] == playerId)
                        {
                            return true;
                        }
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// 请求联盟信息
        /// </summary>
        /// <param name="unionId"></param>
        public void UnionReq(long unionId)
        {
            var req = new UnionReq()
            {
                unionID = unionId,
            };

            Debug("Send2GS UnionReq req = {0}", req);
            MessageMgr.Send(req);
        }

        /// <summary>
        /// 请求解散联盟
        /// </summary>
        public void DisbandUnionReq()
        {
            if (!CheckIsCanOperator())
                return;
            MessageMgr.Send(new DisbandUnionReq());
        }

        /// <summary>
        /// 加入联盟成功后 是否显示联盟面板-原来默认都是显示
        /// </summary>
        private bool isShowUIChat;

        /// <summary>
        /// 请求加入联盟
        /// </summary>
        /// <param name="unionID"></param>
        public void JoinUnionReq(long unionID, bool isShowUIChat = true)
        {
            this.isShowUIChat = isShowUIChat;
            var req = new JoinUnionReq()
            {
                unionID = unionID,
            };

            K3.K3GameEvent.I.TaLog(new K3.AllianceEvent() { EventKey = "alliancejoin", EventValue = unionID.ToString() });

            var gameEvent = new K3.GameEvent();
            gameEvent.EventKey = $"join_alliance";
            K3.K3GameEvent.I.BiLog(gameEvent, "custom_social");

            Debug("Send2GS JoinUnionReq req = {0}", req);
            MessageMgr.Send(req);
        }

        /// <summary>
        /// 请求快速加入联盟
        /// </summary>
        public void QuickJoinUnionReq()
        {
            var gameEvent = new K3.GameEvent();
            gameEvent.EventKey = $"join_alliance";
            K3.K3GameEvent.I.BiLog(gameEvent, "custom_social");

            K3.K3GameEvent.I.TaLog(new K3.AllianceEvent() { EventKey = "alliancequickjoin" });

            var req = new QuickJoinUnionReq()
            { };
            MessageMgr.Send(req);
        }
        /// <summary>
        /// 请求退出联盟
        /// </summary>
        public void QuitUnionReq()
        {
            MessageMgr.Send(new QuitUnionReq());
        }

        /// <summary>
        /// 同意|拒绝 玩家声请
        /// </summary>
        /// <param name="targetPlayerId"></param>
        /// <param name="isAgree"></param>
        public void HandleUnionApplyReq(long targetPlayerId, bool isAgree)
        {
            var req = new HandleUnionApplyReq()
            {
                targetPlayerID = targetPlayerId,
                isAgree = isAgree,
            };

            Debug("Send2GS HandleUnionApplyReq req = {0}", req);
            MessageMgr.Send(req);
        }

        /// <summary>
        /// 成员阶级调整
        /// </summary>
        /// <param name="targetPlayerId"></param>
        /// <param name="targetClassId"></param>
        public void UnionMemberClassAdjustReq(long targetPlayerId, AllianceClassIdEnum targetClassId)
        {
            if (!CheckIsCanOperator())
                return;
            var req = new UnionMemberClassAdjustReq()
            {
                targetPlayerID = targetPlayerId,
                targetClassID = (int)targetClassId,
            };

            Debug("Send2GS UnionMemberClassAdjustReq req = {0}", req);
            MessageMgr.Send(req);
        }

        /// <summary>
        /// 移除联盟成员
        /// </summary>
        /// <param name="targetPlayerId"></param>
        public void RemoveUnionMemberReq(long targetPlayerId)
        {
            if (!CheckIsCanOperator())
                return;
            var req = new RemoveUnionMemberReq()
            {
                targetPlayerID = targetPlayerId,
            };

            Debug("Send2GS RemoveUnionMemberReq req = {0}", req);
            MessageMgr.Send(req);
        }

        /// <summary>
        /// 盟主转让
        /// </summary>
        /// <param name="targetPlayerId"></param>
        public void UnionLeaderTransferReq(long targetPlayerId)
        {
            if (!CheckIsCanOperator())
                return;
            var req = new UnionLeaderTransferReq()
            {
                targetPlayerID = targetPlayerId,
            };

            Debug("Send2GS UnionLeaderTransferReq req = {0}", req);
            MessageMgr.Send(req);
        }

        /// <summary>
        /// 更新 联盟名称
        /// </summary>
        /// <param name="name"></param>
        public void UpdateUnionNameReq(string name)
        {
            var req = new UpdateUnionNameReq()
            {
                name = name,
            };

            Debug("Send2GS UpdateUnionNameReq req = {0}", req);
            MessageMgr.Send(req);
        }

        /// <summary>
        /// 更新 联盟简称
        /// </summary>
        /// <param name="nickName"></param>
        public void UpdateUnionNickNameReq(string nickName)
        {
            var req = new UpdateUnionNickNameReq()
            {
                name = nickName,
            };

            Debug("Send2GS UpdateUnionNickNameReq req = {0}", req);
            MessageMgr.Send(req);
        }

        /// <summary>
        /// 更新 联盟公告-对内
        /// </summary>
        /// <param name="desc"></param>
        public void UpdateUnionDescReq(string desc)
        {
            var req = new UpdateUnionDescReq()
            {
                desc = desc,
            };

            Debug("Send2GS UpdateUnionDesc req = {0}", req);
            MessageMgr.Send(req);
        }

        /// <summary>
        /// 更新 联盟宣言-对外
        /// </summary>
        /// <param name="desc"></param>
        public void UpdateUnionManifestoReq(string desc)
        {
            var req = new UpdateUnionManifestoReq()
            {
                manifesto = desc,
            };

            Debug("Send2GS UpdateUnionManifestoReq req = {0}", req);
            MessageMgr.Send(req);
        }

        /// <summary>
        /// 更新 联盟等级限制
        /// </summary>
        /// <param name="desc"></param>
        public void UpdateUnionLevelReq(int level)
        {
            var req = new UpdateUnionLimitLvReq()
            {
                level = level
            };

            Debug("Send2GS UpdateUnionDescReq req = {0}", req);
            MessageMgr.Send(req);
        }

        /// <summary>
        /// 更新 联盟标志
        /// </summary>
        /// <param name="unionFlag"></param>
        public void UpdateUnionFlagReq(uint unionFlag)
        {
            var req = new UpdateUnionFlagReq()
            {
                flag = unionFlag,
            };

            Debug("Send2GS UpdateUnionFlag req = {0}", req);
            MessageMgr.Send(req);
        }

        /// <summary>
        /// 更新联盟国旗
        /// </summary>
        /// <param name="lang"></param>
        public void UpdateUnionLangReq(int lang)
        {
            var req = new UpdateUnionLangReq()
            {
                lang = lang,
            };

            Debug("Send2GS UpdateUnionLangReq req = {0}", req);
            MessageMgr.Send(req);
        }

        /// <summary>
        /// 更新联盟语言
        /// </summary>
        /// <param name="lang"></param>
        public void UpdateUnionLanguageReq(string lang)
        {
            var req = new UpdateUnionLanguageReq()
            {
                language = lang,
            };
            MessageMgr.Send(req);
        }

        /// <summary>
        /// 更新 入盟条件
        /// </summary>
        /// <param name="needApply"></param>
        public void UpdateUnionJoinConditionReq(bool needApply)
        {
            var req = new UpdateUnionJoinConditionReq()
            {
                needApply = needApply,
            };

            Debug("Send2GS UpdateUnionJoinConditionReq req = {0}", req);
            MessageMgr.Send(req);
        }

        /// <summary>
        /// 拉取联盟所有官员申请记录
        /// </summary>
        public void UnionAllOfficerApplyReq()
        {
            MessageMgr.Send(new UnionAllOfficerApplyReq());
        }

        /// <summary>
        /// 请求联盟日志
        ///  1 成员页签日志， 2 活动页签日志，3战争页签日志
        /// </summary>
        /// <param name="index"></param>
        public void UnionLogReq(int index) 
        {
            MessageMgr.Send(new UnionLogReq() 
            {
                 logTyp = index
            });
        }

        #region  特殊期间 屏蔽联盟操作 

        bool CheckIsCanOperator() 
        {
            //屏蔽巨龙修改设置行为
            //if (GameData.I.DragonWarData.SignData.ShieldDragonWaringAction())
            //    return false;

            return true;
        }

        #endregion

        #endregion

        #region Method
        /// <summary>
        /// 联盟信息 ntf
        /// </summary>
        /// <param name="args"></param>
        async UniTaskVoid OnUnionNtf(UnionNtf args)
        {
            Debug("Send2GS OnUnionNtf args = {0}", args);
            D.Debug?.Log("disbandTimestamp={0}", args.disbandTimestamp);
            //if (args.disbandTimestamp > 0)
            //{
            //    //联盟解散的到期时间
            //    var time1 = args.disbandTimestamp - GameTime.Time;
            //    var time = UIHelper.GetFormatTime(time1);
            //    D.Error?.Log("联盟自动解散时间============= args.disbandTimestamp={0},{1}", time, args.disbandTimestamp);
            //}
            AITakeOverCountDown = args.endTs;
            if (args.ID == LPlayer.I.UnionID)
            {
               
                if (!LPlayer.I.UnionJoined)
                {
                    LPlayer.I.UnionJoined = true;
                }

                m_AllianceInfo.disbandTimestamp = args.disbandTimestamp;
                m_AllianceInfo.CreateTS = args.createTime;
                m_AllianceInfo.TechExp = args.unionExp;

                //自己的联盟
                // 联盟 id
                m_AllianceInfo.Id = args.ID;
                // 联盟名称
                m_AllianceInfo.Name = args.name;
                m_AllianceInfo.LimitLv = args.limitLv;
                m_AllianceInfo.TeachLevel = args.techGroup;
                // 联盟简称
                m_AllianceInfo.NickName = args.nickName;
                // 联盟战力
                m_AllianceInfo.Power = args.power;
                //D.Error?.Log("UnionNtf args.name={0},power={1}", args.name, args.power);
                // 最大成员数量
                m_AllianceInfo.MaxNum = args.maxNum;
                // 联盟公告
                m_AllianceInfo.Desc = args.desc;
                m_AllianceInfo.lastModifyName = args.descPName;
                m_AllianceInfo.lastModifyTime = args.descTimes;
                //联盟宣言
                m_AllianceInfo.Manifesto = args.manifesto;
                // 联盟标志
                m_AllianceInfo.Flag = args.flag;
                // 联盟语言
                m_AllianceInfo.Lang = args.lang;
                // 盟主id
                m_AllianceInfo.LeaderId = args.leaderID;
                // 入盟条件
                m_AllianceInfo.NeedApply = args.needApply;
                // 战力排名
                m_AllianceInfo.PowerRank = args.powerRank;
                // 铀贡献量
                m_AllianceInfo.UraniumGauge = args.uraniumGauge;
                //晋升
                m_AllianceInfo.Promotion = args.up;

                m_AllianceInfo.medals = args.medals;

                m_AllianceInfo.medalLogs = args.medalLogs;

                m_AllianceInfo.Language = args.language;

                m_AllianceInfo.UnionRankInfos.Clear();
                m_AllianceInfo.UnionRankInfos.AddRange(args.RankNames);

                // 联盟申请列表
                m_AllianceApplies = new List<UnifyPlayerInfo>();

                if (args.applies != null)
                {
                    var applies = args.applies;

                    // - message PlayerBaseInfo {
                    //    int64 ID = 1; // 玩家ID
                    //    string name = 2; // 名字
                    //    int32 avatarCfgID = 3; // 头像
                    //    int32 level = 4; // 等级
                    //    int32 cityLevel = 5; // 基地等级
                    //    int64 power = 6; // 战力
                    //    string unionName = 7; // 名字
                    //    string unionNickName = 9; // 简称
                    //}
                    for (int i = 0; i < applies.Count; i++)
                    {
                        m_AllianceApplies.Add(applies[i]);
                    }
                }

                // 有联盟成员
                if (args.hasDetail)
                {
                    Dictionary<long, UnionMemberInfo> oldData = new Dictionary<long, UnionMemberInfo>();
                    Dictionary<long, UnionMemberInfo> newData = new Dictionary<long, UnionMemberInfo>();
                    if (m_AllianceInfo.Members != null)
                    {
                        foreach (var item in m_AllianceInfo.Members)
                        {
                            oldData[item.PlayerId] = item;
                        }
                    }

                    var members = args.members;
                    var memberList = new List<UnionMemberInfo>();
                    for (int i = 0; i < members.Count; i++)
                    {
                        var UnionCfg = await Cfg.C.CUnionClass.GetConfigAsync(members[i].ClassCfgID);

                        var memberInfo = new UnionMemberInfo()
                        {
                            PlayerId = members[i].playerID,
                            PlayerHead = members[i].head,
                            ClassCfgId = members[i].ClassCfgID,
                            Cls = UnionCfg.Class,
                            Power = members[i].power,
                            LogoutTs = members[i].logoutTS,
                            Kill = members[i].killNum,
                            CityLevel = members[i].cityLevel,
                            IsOnline = members[i].isOnline,
                            UnionPoint = members[i].point,
                            IsTakeOver = members[i].takeOverLeader,
                            officialPosition = members[i].officialPosition,
                            gender = members[i].gender,
                        };

                        memberList.Add(memberInfo);

                        if (oldData.ContainsKey(memberInfo.PlayerId))
                        {
                            //删除已存在的,剩余的是退出的玩家
                            oldData.Remove(memberInfo.PlayerId);
                        }
                        else
                        {
                            //如果不存在则是新加入的玩家
                            newData[memberInfo.PlayerId] = memberInfo;
                        }

                        // 设置盟主信息
                        if (memberInfo.PlayerId == m_AllianceInfo.LeaderId)
                        {
                            m_AllianceInfo.LeaderHead = memberInfo.PlayerHead;
                            m_AllianceInfo.LeaderName = memberInfo.PlayerHead.name;
                        }
                    }

                    foreach (var item in oldData)
                    {
                        //退出联盟处理,这样下次查看就会直接获取新的玩家信息
                        LUnifyPlayerInfoCache.I.TryUpdateAliiain(item.Key, 0);
                    }

                    foreach (var item in newData)
                    {
                        //加入联盟处理,这样下次查看就会直接获取新的玩家信息
                        LUnifyPlayerInfoCache.I.TryUpdateAliiain(item.Key, args.ID);
                    }

                    m_AllianceInfo.Num = memberList.Count;
                    m_AllianceInfo.Members = memberList;
                }

                EventMgr.FireEvent(TEventType.AllianceInfoDetail, m_AllianceInfo, LAllianceMgr.I.reqSource);
                LAllianceMgr.I.reqSource = AllianceReqsource.None; //重置来源
                // 延迟通知 加入联盟成功
                if (m_NeedNotifyJoinSuccess)
                {
                    m_NeedNotifyJoinSuccess = false;
                    EventMgr.FireEvent(TEventType.AllianceJoinSuccess);
                }
            }
            else //其他人的联盟
            {
                var allianceInfo = new AllianceInfo()

                {
                    // 联盟 id
                    Id = args.ID,
                    // 联盟名称
                    Name = args.name,
                    LimitLv = args.limitLv,
                    // 联盟简称
                    NickName = args.nickName,
                    // 联盟战力
                    Power = args.power,
                    // 当前成员数量
                    Num = 0,
                    // 最大成员数量
                    MaxNum = args.maxNum,
                    // 联盟公告
                    Desc = args.desc,
                    lastModifyName = args.descPName,
                    lastModifyTime = args.descTimes,
                    //联盟宣言
                    Manifesto = args.manifesto,
                    // 联盟标志
                    Flag = args.flag,
                    Language = args.language,
                    // 联盟语言
                    Lang = args.lang,
                    // 盟主id
                    LeaderId = args.leaderID,
                    // 盟主头像id
                    LeaderHead = null,
                    // 盟主名字
                    LeaderName = "",
                    // 入盟条件
                    NeedApply = args.needApply,
                    // 战力排名
                    PowerRank = args.powerRank,
                    // 铀贡献量
                    UraniumGauge = args.uraniumGauge,
                    //晋升
                    Promotion = args.up,
                    disbandTimestamp = args.disbandTimestamp,
                    medals = args.medals,
                    medalLogs = args.medalLogs,
                    CreateTS = args.createTime,
                    TechExp = args.unionExp,
                    TeachLevel = args.techGroup
                };

                // 有联盟成员
                if (args.hasDetail)
                {
                    var members = args.members;
                    var memberList = new List<UnionMemberInfo>();
                    for (int i = 0; i < members.Count; i++)
                    {
                        var UnionClassCfg = await Cfg.C.CUnionClass.GetConfigAsync(members[i].ClassCfgID);
                        var info = new UnionMemberInfo()
                        {
                            PlayerId = members[i].playerID,
                            PlayerHead = members[i].head,
                            ClassCfgId = members[i].ClassCfgID,
                            Cls = UnionClassCfg.Class,
                            Power = members[i].power,
                            LogoutTs = members[i].logoutTS,
                            Kill = members[i].killNum,
                            CityLevel = members[i].cityLevel,
                            IsOnline = members[i].isOnline,
                            officialPosition = members[i].officialPosition,
                            gender = members[i].gender,
                        };

                        memberList.Add(info);

                        // 设置盟主信息
                        if (info.PlayerId == allianceInfo.LeaderId)
                        {
                            allianceInfo.LeaderHead = info.PlayerHead;
                            allianceInfo.LeaderName = info.PlayerHead.name;
                        }
                    }

                    allianceInfo.Num = memberList.Count;
                    allianceInfo.Members = memberList;
                }
                //var result = m_OtherAllianceInfo.Members.Except(allianceInfo.Members);

                m_OtherAllianceInfo = allianceInfo;
                EventMgr.FireEvent(TEventType.AllianceInfoDetail, allianceInfo, LAllianceMgr.I.reqSource);
                LAllianceMgr.I.reqSource = AllianceReqsource.None; //重置来源
            }
        }


        //盟主改位阶名字时 所有人都会收到推送
        void OnUnionRankNameNtf(UnionRankNameNtf args)
        {
            Debug("OnUnionRankNameNtf errCode = {0}", args.errCode);

            if (args.errCode == ErrCode.ErrCodeSuccess)
            {
                m_AllianceInfo.UnionRankInfos.Clear();
                m_AllianceInfo.UnionRankInfos.AddRange(args.RankNames);

                EventMgr.FireEvent(TEventType.AllianceRankNameChangeNtf, args);

            }
        }
        public void UpdatePositionIcon(TFWImage iconImg, int positionId, bool useNativeSize = false)
        {
            var cfg = Cfg.C.CAllianceOfficialPositions.I(positionId);
            if(cfg!= null && iconImg!= null)
                UITools.SetImageBySpriteName(iconImg, cfg.Icon,useNativeSize);
        }
        //盟主改位阶名字时 自己会收到改协议 但不包含新的位阶名字
        void OnSetUnionRankNameAck(SetUnionRankNameAck args)
        {
            Debug("OnSetUnionRankNameAck errCode = {0}", args.errCode);
            if (args.errCode == ErrCode.ErrCodeSuccess)
            {

                EventMgr.FireEvent(TEventType.AllianceRankNameChangeAck, args);

            }
        }

        /// <summary>
        /// 加入联盟 ntf
        /// </summary>
        /// <param name="args"></param>
        void OnJoinUnionNtf(JoinUnionNtf args)
        {
            Debug("OnJoinUnionNtf args = {0}", args);
            var oldUnionId = LPlayer.I.UnionID;
            // 更新下 player 的 unionID
            LPlayer.I.UnionID = args.ID;
            LPlayer.I.LastCrossDgResult = args.LastCrossDgResult;
            LPlayer.I.CrossDgRank = args.CrossDgRank;
            m_AllianceInfo.Id = args.ID;
            m_AllianceInfo.NickName = args.nickName;
            m_AllianceInfo.Name = args.name;

            // 请求下联盟数据
            UnionReq(args.ID);
            //LAllianceHelp.I.Init();
            LAllianceRedPoint.I.Init();
            //LAllianceTech.I.Init();

            //刷新自己联盟的建筑信息数据
            AllianceGameData.I.AllianceWarData.UpdateOwnerAllianceWarAreaInfo();

            // 强制请求联盟集结信息
            //LAllianceCommandPost.RequestRallyList(true);

            GameData.I.RallyData.RequestRallyList();
            // 请求标记信息
            //LMapMarkingMgr.I.GetAllSign();

            if (!LAllianceMgr.I.UnionCreating())
            {
                UI.FloatTips.I.FloatMsg(LocalizationMgr.Format("LC_UNION_you_have_joined", args.name));
            }

            EventMgr.FireEvent(TEventType.AllianceJoining, args.ID);

            // EVENT_ALLIANCE_JOIN_SUCCESS 延迟通知 等有联盟数据之后
            m_NeedNotifyJoinSuccess = true;
            LAllianceHelp.I.ReqUnionHelpList();
            GetKickOutSettingInfo();

            AllianceTechMgr.I.UnionTechReq();
            LFavorites.I.ReqCoordsFavorites();
            LAllianceTerritoryBuilding.I.JoinUnion(oldUnionId, LPlayer.I.UnionID);
            LAllianceHelp.I.JoinUnion();
            LMigrationServer.I.JoinUnion();
            //重新请求所有任务数据
            TaskMgr.I.QuestInfoReq();
            //LAllianceAchievement.I.ReqAllQuest();
            //if (GameData.I.MainData.CurrMenuType == MainMenuType.ALLIANCE)
            //{
                //SceneManager1.I.ClearScene<UIMainCity>();
                PopupManager.I.ShowLayer<UI.Alliance.UIAllianceMain>(new UI.Alliance.UIAllianceMainData());
            //}

        }

        /// <summary>
        /// 退出联盟 ntf
        /// </summary>
        /// <param name="args"></param>
        void OnQuitUnionNtf(QuitUnionNtf args)
        {
            var oldUnionId = LPlayer.I.UnionID;
            // 更新下 player 的 unionID
            LPlayer.I.UnionID = 0;
            //刷新标记
            //LMapMarkingMgr.I.GetAllSign();

            CleanData(oldUnionId);
            //LAllianceTech.I.DeInit();
            Debug("OnQuitUnionNtf args = {0}", args);
            EventMgr.FireEvent(TEventType.AllianceExiting);
            EventMgr.FireEvent(TEventType.AllianceExitSuccess);

            LastScollIndex = -1;
            //清理展开状态
            m_SelfAllianceExpandStateDic?.Clear();
            AllianceTechMgr.I.ExitUnionTech();
        }

        /// <summary>
        /// 请求联盟信息 ack
        /// </summary>
        /// <param name="args"></param>
        void OnUnionAck(UnionAck args)
        {
            Debug("Send2GS OnUnionAck args = {0}", args);

            var ok = args.errCode == 0;
            if (!ok)
            {
                UIChat.I?.SetUIAllianceInfoPopState(false);
            }
        }

        /// <summary>
        /// 请求解散联盟 ack
        /// </summary>
        /// <param name="args"></param>
        void OnDisbandUnionAck(DisbandUnionAck args)
        {
            Debug("Send2GS OnDisbandUnionAck args = {0}", args);

            var ok = args.errCode == 0;

            EventMgr.FireEvent(TEventType.AllianceDisband, ok);
            RankNewMgr.I.ReqAllianceRankData();
            AllianceTechMgr.I.ExitUnionTech();
        }

        public bool isCanShow = false;

        /// <summary>
        /// 请求加入联盟 ack
        /// </summary>
        /// <param name="args"></param>
        void OnJoinUnionAck(JoinUnionAck args)
        {
            Debug("Send2GS OnJoinUnionAck args = {0}", args);
            if (args.errCode != ErrCode.ErrCodeSuccess)
            {
                FloatTips.I.FloatErrcodeMsg(args.errCode);
            }

            var ok = args.errCode == 0;
            var unionID = args.unionID;
            var isApply = args.isApply;

            if (ok)
            {
                var needApply = LAllianceMgr.I.IsUnionNeedApply(unionID);

                if (needApply != isApply)
                { // 服务器状态和本地状态不一致了
                    LAllianceMgr.I.UpdateUnionNeedApply(unionID, isApply);
                }
                if (!LPlayer.I.UnionJoined)
                {
                    LPlayer.I.UnionJoined = true;
                }
                if (isApply)
                {
                    FloatTips.I.FloatMsg(LocalizationMgr.Get("Alliance_Application_Submit_Tips"));
                    // 这个地方更新下 已申请的 联盟id
                    LAllianceMgr.I.UpdateRecommendUnionListApplied(unionID, true);
                }

                else if (isShowUIChat)
                {
                    //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.ALLIANCE,
                    //    new UI.Alliance.UIAllianceMainData
                    //    {
                             
                    //    });
                }
                //UIMain.I?.TrySwitch(MainMenuConst.ALLIANCE, new UIChatData
                //{
                //    CurrChatTab = ChatTabs.Alliance,
                //    CurrentTabKey = UIMain.I.GetMainUICurrState(),
                //});

                //var chatMenu = WndMgr.Show<UIChat>("UIChat", new UIChatData
                //{
                //    CurrChatTab = ChatTabs.Alliance
                //});
            }
            else if(args.errCode == ErrCode.ErrCodeUnionNotSameRegion)
            {
                FloatTips.I.FloatMsg(LocalizationMgr.Get("Area_Block_Alliance_Tips"));
                return;
            }

            EventMgr.FireEvent(TEventType.AllianceJoin, ok, unionID, isApply);
            ///申请有限制的联盟也会走这个协议  
            if (LAllianceMgr.I.GetUnionId() != 0)
            {
                RankNewMgr.I.ReqAllianceRankData();
                LFavorites.I.ReqCoordsFavorites();
                AllianceTechMgr.I.UnionTechReq();
                LAllianceAltar.I.JoinUnion();
                LAllianceBuildingRecord.I.JoinUnion();
                LAllianceAllyCity.I.JoinUnion();
                LAllianceMillitaryAlert.I.JoinUnion();
                AllianceGameData.I.AlliancMsgBoardData.JoinUnion();
                LAllianceNewAchievement.I.ReqAllianceAchieveInfoReq();
            }
            //LAllianceMgr.I.ShowAllianceMain("");
        }
        /// <summary>
        /// 快速加入联盟的ack
        /// </summary>
        /// <param name="obj"></param>
        private void OnQuickJoinUnionAck(QuickJoinUnionAck args)
        {
            if (args.notUseAck)
                return;
            var ok = args.errCode == 0;
            var unionID = args.unionID;
            if (ok)
            {
                //由于快速加入 如果没有联盟自动创建联盟 而且免费！
                //如果不是创建联盟才走逻辑
                if (!args.isCreated)
                {
                    if (!LPlayer.I.UnionJoined)
                    {
                        LPlayer.I.UnionJoined = true;
                    }
                    EventMgr.FireEvent(TEventType.AllianceJoin, ok, unionID, false);
                    //if (GameData.I.MainData.CurrMenuType == MainMenuType.ALLIANCE)
                     PopupManager.I.ShowLayer<UI.Alliance.UIAllianceMain>(new UI.Alliance.UIAllianceMainData
                        {
                            CurrentTabKey = GameData.I.MainData.CurrMenuType,
                        });
                }

                //LAllianceMgr.I.ShowAllianceMain("");
            }
            else if (args.errCode == ErrCode.ErrCodeMatchedUnionNotFound)
            {
                var isOpen = MetaConfig.UnionFastJoinSwitch == 1;
                if (isOpen)
                {
                    var btn2data = new MsgBoxBtnParam()
                    {
                        str = LocalizationMgr.Get("LC_MENU_cancel_cap"),
                        func = (o) => {
                            PopupManager.I.ShowDialog<UIAllianceWelcome>();
                        }
                    };
                    var btn1data = new MsgBoxBtnParam()
                    {
                        str = LocalizationMgr.Get("LC_MENU_confirm_cap"),
                        func = (o) =>
                        {
                            //PopupManager.I.ShowPanel<UI.Alliance.UIAllianceCreateAndSet>(new UI.Alliance.UIAllianceCreateAndSetData()
                            //{
                            //    createAndSetEnum = UI.Alliance.AllianceCreateAndSetEnum.create
                            //});

                            PopupManager.I.ShowPanel<UINewAllianceStart>();
                            //PopupManager.I.ShowPanel<UI.Alliance.UIAllianceCreatePop>(new UI.Alliance.UIAllianceCreatePopData());
                        },
                    };

                    string content = LocalizationMgr.Get("UNION_no_union");
                    UIMsgBox.Push(EMsgBoxType.two_nc, LocalizationMgr.Get("LC_MENU_confirm_cap"), content, btn1data,
                             btn2data, ButtonColorGroup.BlueGlod);

                }
                else
                {
                    UI.FloatTips.I.FloatMsg(LocalizationMgr.Get("Alliance_Auto_Join_Cant_Find"));
                }
                //
            }
            else if (args.errCode == ErrCode.ErrCodeUnionNotSameRegion)
            {
                FloatTips.I.FloatMsg(LocalizationMgr.Get("Area_Block_Alliance_Tips"));
                return;
            }
            RankNewMgr.I.ReqAllianceRankData();
            LFavorites.I.ReqCoordsFavorites();
            AllianceTechMgr.I.UnionTechReq();
            LAllianceNewAchievement.I.ReqAllianceAchieveInfoReq();
        }

        void OnQuickJoinUnionNtf(QuickJoinUnionNtf ntf) 
        {
            var ack = new QuickJoinUnionAck()
            {
                errCode = ntf.errCode,
                isCreated = ntf.isCreated,
                unionID = ntf.unionID,
                notUseAck = false
            };
            ///服务器有可能不使用ack  但是消息结构一样 方便处理下 
            OnQuickJoinUnionAck(ack);
        }
        /// <summary>
        /// QuitUnionAck
        /// </summary>
        /// <param name="args"></param>
        void OnQuitUnionAck(QuitUnionAck args)
        {
            Debug("Send2GS OnQuitUnionAck args = {0}", args);
            var ok = args.errCode == 0;

            //UITools.PopTips(LocalizationMgr.Get("Union_Quit_Success_Tips"));

            RankNewMgr.I.ReqAllianceRankData();
            AllianceTechMgr.I.UnionTechReq();
            LFavorites.I.ReqCoordsFavorites();
        }

        /// <summary>
        /// 同意|拒绝 玩家声请 ack
        /// </summary>
        /// <param name="args"></param>
        void OnHandleUnionApplyAck(HandleUnionApplyAck args)
        {
            Debug("Send2GS OnHandleUnionApplyAck args = {0}", args);

            var targetPlayerId = args.targetPlayerID;
            var isAgree = args.isAgree;
            var ok = args.errCode == 0;

            if (ok)
            {
                // 无论同意 | 拒绝 都需要删掉
                UpdateUnionApplies(targetPlayerId, false);

                if (isAgree)
                { // 同意玩家入盟成功了
                    var member = GetUnionMembersWithId(targetPlayerId);
                    if (member != null)
                    {
                        UI.FloatTips.I.FloatMsg(LocalizationMgr.Format("LC_UNION_member_jion_union", member.PlayerHead.name));
                    }
                }
            }

            EventMgr.FireEvent(TEventType.AllianceAgree, ok, targetPlayerId, isAgree);
        }

        /// <summary>
        /// 成员阶级调整 ack
        /// </summary>
        /// <param name="args"></param>
        async UniTaskVoid OnUnionMemberClassAdjustAck(UnionMemberClassAdjustAck args)
        {
            Debug("Send2GS OnUnionMemberClassAdjustAck args = {0}", args);

            var targetPlayerId = args.targetPlayerID;
            var targetClassId = args.targetClassID;
            var ok = args.errCode == 0;

            //修一下members
            var members = m_AllianceInfo.Members;
            for (int i = 0; i < members.Count; i++)
            {
                if (members[i].PlayerId == targetPlayerId)
                {
                    members[i].ClassCfgId = targetClassId;
                    var UnionClassCfg = await Cfg.C.CUnionClass.GetConfigAsync(targetClassId);
                    members[i].Cls = UnionClassCfg.Class;
                }
            }
            EventMgr.FireEvent(TEventType.AllianceInfoChange);
            EventMgr.FireEvent(TEventType.AllianceMemberClassChange, ok, targetPlayerId, targetClassId);
        }

        /// <summary>
        /// 移除联盟成员 ack
        /// </summary>
        /// <param name="args"></param>
        void OnRemoveUnionMemberAck(RemoveUnionMemberAck args)
        {
            Debug("Send2GS OnRemoveUnionMemberAck args = {0}", args);

            var targetPlayerId = args.targetPlayerID;
            var ok = args.errCode == 0;

            //修一下members
            var members = m_AllianceInfo.Members;
            for (int i = 0; i < members.Count; i++)
            {
                if (members[i].PlayerId == targetPlayerId)
                {
                    members.RemoveAt(i);
                    break;
                }
            }

            EventMgr.FireEvent(TEventType.AllianceMemberRemove, ok, targetPlayerId);
        }

        /// <summary>
        /// 盟主转让 ack
        /// </summary>
        /// <param name="args"></param>
        void OnUnionLeaderTransferAck(UnionLeaderTransferAck args)
        {
            Debug("Send2GS OnUnionLeaderTransferAck args = {0}", args);

            var ok = args.errCode == 0;

            EventMgr.FireEvent(TEventType.AllianceLeaderTransfer, ok);
            RankNewMgr.I.ReqAllianceRankData();
        }

        /// <summary>
        /// 更新 联盟 名称 ack
        /// </summary>
        /// <param name="args"></param>
        void OnUpdateUnionNameAck(UpdateUnionNameAck args)
        {
            Debug("Send2GS OnUpdateUnionNameAck args = {0}", args);

            var ok = args.errCode == 0;

            EventMgr.FireEvent(TEventType.AllianceNameChange, ok);
            RankNewMgr.I.ReqAllianceRankData();
        }

        /// <summary>
        /// 更新 联盟 简称 ack
        /// </summary>
        /// <param name="args"></param>
        void OnUpdateUnionNickNameAck(UpdateUnionNickNameAck args)
        {
            Debug("Send2GS OnUpdateUnionNickNameAck args = {0}", args);

            var ok = args.errCode == 0;

            EventMgr.FireEvent(TEventType.AllianceNicknameChange, ok);
            RankNewMgr.I.ReqAllianceRankData();
        }

        /// <summary>
        /// 更新 联盟公告 ack
        /// </summary>
        /// <param name="args"></param>
        void OnUpdateUnionDescAck(UpdateUnionDescAck args)
        {
            Debug("Send2GS OnUpdateUnionDescAck args = {0}", args);

            //不走ack，走ntf
            if (!args.useAck)
            {
                return;
            }

            var ok = args.errCode == 0;
            
            if (ok)
            {
                m_AllianceInfo.lastModifyName = args.name;
                m_AllianceInfo.lastModifyTime = args.updTimes;
                FloatTips.I.FloatMsg(LocalizationMgr.Get("UNION_alliance_info_modify_succeed"));
            }
            EventMgr.FireEvent(TEventType.AllianceDescChange, ok);
        }

        /// <summary>
        /// 更新 联盟公告 ack，服务器接nlp需要这个ntf协议
        /// </summary>
        /// <param name="args"></param>
        void OnUpdateUnionDescNtf(UpdateUnionDescNtf args)
        {
            Debug("Send2GS OnUpdateUnionDescAck args = {0}", args);

            var ok = args.errCode == 0;
            
            if (ok)
            {
                m_AllianceInfo.lastModifyName = args.name;
                m_AllianceInfo.lastModifyTime = args.updTimes;
                FloatTips.I.FloatMsg(LocalizationMgr.Get("UNION_alliance_info_modify_succeed"));
            }
            EventMgr.FireEvent(TEventType.AllianceDescChange, ok);
        }

        /// <summary>
        /// 更新 联盟宣言 ack
        /// </summary>
        /// <param name="args"></param>
        void OnUpdateUnionManifestoAck(UpdateUnionManifestoAck args)
        {
            Debug("Send2GS UpdateUnionManifestoAck args = {0}", args);

            var ok = args.errCode == 0;

            EventMgr.FireEvent(TEventType.AllianceManifestoChange, ok);
        }

        /// <summary>
        /// 更新 联盟标志 ack
        /// </summary>
        /// <param name="args"></param>
        void OnUpdateUnionFlagAck(UpdateUnionFlagAck args)
        {
            Debug("Send2GS OnUpdateUnionFlagAck args = {0}", args);

            var ok = args.errCode == 0;

            EventMgr.FireEvent(TEventType.AllianceFlagChange, ok);
            RankNewMgr.I.ReqAllianceRankData();
        }

        /// <summary>
        /// 更新 联盟国家旗帜 ack
        /// </summary>
        /// <param name="args"></param>
        void OnUpdateUnionLangAck(UpdateUnionLangAck args)
        {
            Debug("Send2GS OnUpdateUnionLangAck args = {0}", args);

            var ok = args.errCode == 0;

            EventMgr.FireEvent(TEventType.AllianceLangChange, ok);

            RankNewMgr.I.ReqAllianceRankData();
        }

        /// <summary>
        /// 更新 联盟语言 ack
        /// </summary>
        void OnUpdateUnionLanguageAck(UpdateUnionLanguageAck args)
        {
            if(args.errCode==0)
            {
                FloatTips.I.FloatMsg(LocalizationMgr.Get("Change_language_1"));
                EventMgr.FireEvent(TEventType.AllianceLanChange, args.language);
                GetUnionInfo().Language = args.language;
            }
            else if (args.errCode == ErrCode.ErrCodelanguageNotFind)
            {
                FloatTips.I.FloatMsg(LocalizationMgr.Get("Not_find_language"));
            }
            else if (args.errCode == ErrCode.ErrCodeNotHasUnionAuthority)
            {
                FloatTips.I.FloatMsg(LocalizationMgr.Get("CNot_Change_language"));
            }
        }

        /// <summary>
        /// 更新 联盟等级限制 ack
        /// </summary>
        /// <param name="args"></param>
        void OnUpdateUnionLimitLvAck(UpdateUnionLimitLvAck args)
        {
            Debug("Send2GS UpdateUnionLimitLvAck args = {0}", args);
            var ok = args.errCode == 0;
            EventMgr.FireEvent(TEventType.AllianceLimitLevelChange, ok);
        }

        /// <summary>
        /// 更新 入盟条件 ack
        /// </summary>
        /// <param name="args"></param>
        void OnUpdateUnionJoinConditionAck(UpdateUnionJoinConditionAck args)
        {
            Debug("Send2GS OnUpdateUnionJoinConditionAck args = {0}", args);

            var ok = args.errCode == 0;

            EventMgr.FireEvent(TEventType.AllianceJoinConditionChange, ok);
        }
        #endregion

        #region Field

        private List<UnifyPlayerInfo> m_AllianceApplies;
        private List<ClassApplyRecordInfo> m_AllianceOfficerApplys;
        private Dictionary<int, int> m_ClassMemberMax;
        private AllianceInfo m_AllianceInfo;
        private AllianceInfo m_OtherAllianceInfo;
        private bool m_NeedNotifyJoinSuccess = false;
        /// <summary>
        /// AI联盟竞选盟主倒计时
        /// </summary>
        public long AITakeOverCountDown { get; private set; }
        #endregion
    }
}
