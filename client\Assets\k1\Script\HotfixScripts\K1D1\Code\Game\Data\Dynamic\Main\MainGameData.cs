﻿using Common;
using Logic;
using System;
using System.Collections.Generic;
using TFW;
using UI;
using UI.Alliance;
using UnityEngine;

namespace Game.Data
{

    public partial class GameData
    {
        /// <summary>
        /// 初始化主界面数据
        /// </summary>
        private MainGameData _mainData = new MainGameData();

        /// <summary>
        /// 初始化主界面数据
        /// </summary>
        public MainGameData MainData
        {
            get
            {
                return I._mainData;
            }
        }
    }

    /// <summary>
    /// 主界面游戏数据
    /// </summary>
    public class MainGameData : IDisposable
    {

        #region 属性字段数据

        /// <summary>
        /// 是否在内城中
        /// </summary>
        public bool IsInCity { get; private set; }

        /// <summary>
        /// 当前显示的主界面菜单类型
        /// </summary>
        public MainMenuType CurrMenuType { get; private set; } = MainMenuType.CITY;

        /// <summary>
        /// 主界面Canvas信息
        /// </summary>
        public Canvas MainCanvas { get; private set; }

        /// <summary>
        /// 菜单栏英雄按钮对象
        /// </summary>
        public GameObject MenuHeroBtnObj { get; private set; }

        /// <summary>
        /// 在界面左边按钮合集中是否显示单个按钮
        /// </summary>
        public bool IsShowSingleLeftBtns { get; private set; } = true;
        /// <summary>
        /// 在界面右边按钮合集中是否显示单个按钮
        /// </summary>
        /// <value></value>
        public bool IsShowSingleRigtBtns { get;  set; } = true;

        /// <summary>
        /// 判断当前点击是造兵操作还是切换操作
        /// </summary>
        public bool IsClickBuildSoldierBtn { get; private set; } = false;

        /// <summary>
        /// 主界面的 content 对象
        /// </summary>
        public Transform MainContentTran { get; private set; }

        /// <summary>
        /// 主界面飞数据root节点
        /// </summary>
        public Transform MainFlyRootTran { get; private set; }

        /// <summary>
        /// 城市建造按钮位置
        /// </summary>
        public Vector3 MainCityBtnPos { get; private set; }
        /// <summary>
        /// 世界按钮位置
        /// </summary>
        public Vector3 MainWorldBtnPos { get; private set; }

        /// <summary>
        /// 主界面内城解锁动画位置
        /// </summary>
        public Vector3 MainCityUnLockAnimPos { get; private set; }
        /// <summary>
        /// 主界面英雄解锁动画位置
        /// </summary>
        public Vector3 MainHeroUnLockAnimPos { get; private set; }
        /// <summary>
        /// 主界面联盟解锁动画位置
        /// </summary>
        public Vector3 MainAllianceUnLockAnimPos { get; private set; }
        /// <summary>
        /// 主界面世界解锁动画位置
        /// </summary>
        public Vector3 MainWorldUnLockAnimPos { get; private set; }

        /// <summary>
        /// 主界面聊天按钮飞向目标位置
        /// </summary>
        public Vector3 MainChatBtnFlyPos { get; private set; }
        /// <summary>
        /// 主界面合成按钮飞向目标位置
        /// </summary>
        public Vector3 MainMergeBtnFlyPos { get; private set; }
        /// <summary>
        /// 主界面任务按钮飞向目标位置
        /// </summary>
        public Vector3 MainTaskBtnFlyPos { get; private set; }
        /// <summary>
        /// 主界面皮肤按钮飞向目标位置
        /// </summary>
        public Vector3 MainSkinBtnFlyPos { get; private set; }

        /// <summary>
        /// 主界面宝箱飞道具位置
        /// </summary>
        public Vector3 TreasurePos { get; private set; }

        /// <summary>
        /// 邮件显示数量
        /// </summary>
        public int MailDisplayCount { get; set; }

        /// <summary>
        /// 地图飞道具开始位置
        /// </summary>
        public RectTransform MapFlyStartAnchor { get; set; }

        /// <summary>
        /// 训练按钮模板对象
        /// </summary>
        public GameObject TrainBtnTemplateObj { get; set; }

        /// <summary>
        /// 红点数
        /// </summary>
        public int MassRedCount { get; set; }

        public int AllianceHelpRedCount { get; set; }

        public int TreasureRedCount { get; set; }

        public int ChatRedCount { get; set; }

        /// <summary>
        /// 废弃
        /// </summary>
        public int MailRedCount { get; set; }

        public int TaskRedCount { get; set; }

        public int ChapterRedCount { get; set; }



        /// <summary>
        /// 资源飞行目的地
        /// </summary>
        public Dictionary<int, List<Transform>> ResFlyTarget { get; set; } = new Dictionary<int, List<Transform>>();

        #endregion

        #region 数据刷新

        /// <summary>
        /// 刷新城市信息数据
        /// </summary>
        /// <param name="isCity"></param>
        public void UpdateInCity(bool isCity)
        {
            //if (IsInCity != isCity && isCity)
            //    GameData.I.UnlockData.UpdateTower();
            IsInCity = isCity;
        }

        /// <summary>
        /// 刷新当前菜单栏类型
        /// </summary>
        /// <param name="type"></param>
        public void UpdateCurrMenuType(MainMenuType type)
        { 
            CurrMenuType = type;
            //更新内城显示
            UpdateInCity(type == MainMenuType.CITY);

            UIMainAllianceBuildInvite.CheckCanShow();
            
            //if(CurrMenuType== MainMenuType.PRISON)
            //{
            //    BadgeGuideMgr.I.TryOpenBadgeGuide(BadgePopStage.BadgePopStage_1);
            //}
        }

        /// <summary>
        /// 刷新主界面Canvas
        /// </summary>
        /// <param name="canvas"></param>
        public void UpdateMainCanvas(Canvas canvas)
        {
            MainCanvas = canvas;
        }

        /// <summary>
        /// 刷新菜单栏英雄按钮对象
        /// </summary>
        /// <param name="go"></param>
        public void UpdateMenuHeroBtnObj(GameObject go)
        {
            MenuHeroBtnObj = go;
        }

        /// <summary>
        /// 刷新界面左边按钮合集显示信息
        /// </summary>
        /// <param name="isSingle"></param>
        public void UpdateShowSingleLeftBtns(bool isSingle)
        {
            IsShowSingleLeftBtns = isSingle;
        }

        /// <summary>
        /// 刷新当前点击是造兵操作还是切换操作
        /// </summary>
        /// <param name="isSingle"></param>
        public void UpdateIsClickBuildSoldiersBtn(bool isSingle)
        {
            IsClickBuildSoldierBtn = isSingle;
        }

        /// <summary>
        /// 刷新主界面Content Tran对象
        /// </summary>
        /// <param name="tran"></param>
        public void UpdateMainContentTran(Transform tran)
        {
            MainContentTran = tran;
        } 

        /// <summary>
        /// 刷新主界面FlyRoot Tran对象
        /// </summary>
        /// <param name="tran"></param>
        public void UpdateMainFlyRootTran(Transform tran)
        {
            MainFlyRootTran = tran;
        }
         

        /// <summary>
        /// 主界面聊天按钮飞向目标点
        /// </summary>
        /// <param name="pos"></param>
        public void UpdateMainChatBtnFlyPos(Vector3 pos)
        {
            MainChatBtnFlyPos = pos;
        }

        /// <summary>
        /// 主界面合成按钮飞向目标点
        /// </summary>
        /// <param name="pos"></param>
        public void UpdateMainMergeBtnFlyPos(Vector3 pos)
        {
            MainMergeBtnFlyPos = pos;
        }

        /// <summary>
        /// 主界面任务按钮飞向目标点
        /// </summary>
        /// <param name="pos"></param>
        public void UpdateMainTaskBtnFlyPos(Vector3 pos)
        {
            MainTaskBtnFlyPos = pos;
            // D.Error?.Log("更新任务按钮");
        }

        /// <summary>
        /// 主界面皮肤按钮飞向目标点
        /// </summary>
        /// <param name="pos"></param>
        public void UpdateMainSkinBtnFlyPos(Vector3 pos)
        {
            MainSkinBtnFlyPos = pos;
        }

        /// <summary>
        /// 主界面皮肤按钮飞向目标点
        /// </summary>
        /// <param name="pos"></param>
        public void UpdateTreasureFlyPos(Vector3 pos)
        {
            TreasurePos = pos;
        }

        /// <summary>
        /// 刷新地图飞道具开始位置
        /// </summary>
        /// <param name="pos"></param>
        public void UpdateMapFlyStartAnchor(RectTransform rect)
        {
            MapFlyStartAnchor = rect;
        }

        /// <summary>
        /// 刷新训练按钮模板对象
        /// </summary>
        /// <param name="go"></param>
        public void UpdateTrainBtnTemplateObj(GameObject go)
        {
            //D.Error("  update TrainBtnTemplateObj " + go + "  active " + go.activeInHierarchy + " self " + go.activeSelf);
            TrainBtnTemplateObj = go;
        }

        #endregion



        #region 数据清理

        /// <summary>
        /// 数据清理
        /// </summary>
        public void Dispose()
        {
            MainCanvas = null;
            MainContentTran = null;
            MainFlyRootTran = null;
            MainCityBtnPos = Vector3.zero;
            MainWorldBtnPos = Vector3.zero;
            MainCityUnLockAnimPos = Vector3.zero;
            MainHeroUnLockAnimPos = Vector3.zero;
            MainAllianceUnLockAnimPos = Vector3.zero;
            MainWorldUnLockAnimPos = Vector3.zero;
            MainChatBtnFlyPos = Vector3.zero;
            MainMergeBtnFlyPos = Vector3.zero;
            MainTaskBtnFlyPos = Vector3.zero;
            MainSkinBtnFlyPos = Vector3.zero;
            TreasurePos = Vector3.zero;
            IsInCity = true;
            IsShowSingleLeftBtns = true;
            CurrMenuType = MainMenuType.CITY;
            MenuHeroBtnObj = null;
            MapFlyStartAnchor = null;
            TrainBtnTemplateObj = null;

            MailDisplayCount = 0;

            MassRedCount = 0;
            AllianceHelpRedCount = 0;
            TreasureRedCount = 0;
            ChatRedCount = 0;
            MailRedCount = 0;
            TaskRedCount = 0;
            ChapterRedCount = 0;
        }

        #endregion


    }
}
