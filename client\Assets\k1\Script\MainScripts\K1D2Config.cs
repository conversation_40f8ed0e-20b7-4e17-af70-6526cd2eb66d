﻿using ExLog;
using System.Diagnostics;
using UnityEditor;
using UnityEngine;
using MainScripts;
using Google.Protobuf;
using Sirenix.OdinInspector;
using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using KS;

/// <summary>
/// K1d2项目配置
/// </summary>
[UnityEngine.Scripting.Preserve]
public class K1D2Config : MonoBehaviour
{
     
    private static Channel _channel = Channel.None;

    /// <summary>
    /// 当前代码渠道类型, 统一用这个，后面打包的时候改这里一处就好了
    /// </summary>
    public static Channel Channel
    {
        get
        {
            if (_channel == Channel.None)
                _channel = GetIdentifierChanel();
            return _channel;
        }
    }
     

    /// <summary>
    /// 是否显示登录界面
    /// </summary>
    [Header("是否显示登录界面")]
    public bool IsShowLoginPanel = true;

    /// <summary>
    /// 是否使用内城战斗测试数据
    /// </summary>
    [Header("是否忽略维护标记")]
    public bool IsIgnoreMaintain = false;
      

    /// <summary>
    /// 是否启用暂停快捷键调试
    /// </summary>
    [Header("是否启用暂停快捷键调试")]
    public bool OpenDebug = false;

    [Header("开启新招募")]
    public bool NewHeroRecruitFlag = false;

 
    [Header("是否开启引导")]
    public bool OpenGuide = true;
     
    /// <summary>
    /// 是否开启log
    /// </summary>
    [Header("是否开启log")]
    [HideInInspector]
    public bool OpenLog = false;

  
    /// <summary>
    /// 本地模拟购买月卡
    /// </summary>
    [HideInInspector]
    public bool LocalBuyMonthCard = false;

    /// <summary>
    /// 是否启用暂停快捷键调试
    /// </summary>
    [Header("是否启用键盘打开GM, 如果启用，按O打开GM")]
    public bool OpenGMPanel = false;

    /// <summary>
    /// 是否绘制随机地图四叉树区域
    /// </summary>
    [Header("是否绘制随机地图四叉树区域")]
    public bool isDrawRandomMapQuadTree = false;

  

    [Header("是否弹出服务器错误码，内网和beta环境默认弹出，线上不弹")]
    public bool ShowErrorCode = true;

      

#region 单例

    /// <summary>
    /// 单例对象
    /// </summary>
    public static K1D2Config I { get; set; }

    /// <summary>
    /// 计时器
    /// </summary>
    public static Stopwatch Stopwatch = new Stopwatch();

    /// <summary>
    /// 登录时长显示
    /// </summary>
    public static bool ShowLoginTime = false;

    private void Awake()
    {
        I = this;
 

        Stopwatch.Restart();



#if INTERNAL_ENTRANCE || DEFAULT_ENV_BETA || UNITY_EDITOR
         OpenLog = true;
#else
        OpenLog = false; 
#endif

#if USE_LOG
     OpenLog=true;
#endif

#if UNITY_EDITOR || INTERNAL_ENTRANCE || DEFAULT_ENV_BETA
        ExLogMgr.SetLogLevel((int)ExLogLevelEnum.Debug);
        var logLevelPath= $"{Application.persistentDataPath}/LogLevel";
        if (File.Exists(logLevelPath))
        {
            var loglevelStr= File.ReadAllText(logLevelPath);
            if (int.TryParse(loglevelStr, out int logLevel))
            {
                ExLogMgr.SetLogLevel(logLevel);
            }
        }
#else
        ExLogMgr.SetLogLevel((int)ExLogLevelEnum.Error);
#endif
        //确认是否开启日志
        ExLogMgr.SetAllLogEnable(OpenLog);

        
    }

#endregion

    /// <summary>
    /// 根据Bundleid获取当前渠道类型
    /// </summary>
    /// <returns></returns>
    public static Channel GetIdentifierChanel()
    {
        return Channel.GlobalAnd;  
    }

    // Start is called before the first frame update
    void Start()
    {

        //TODO 正式的网络版本，目前只能自动登录，不显示这些选择账号界面
#if INTERNAL_ENTRANCE || DEFAULT_ENV_BETA || UNITY_EDITOR

#else
        IsShowLoginPanel = false;
         
        ShowErrorCode = true;
        
#endif

#if UNITY_EDITOR
        LocalBuyMonthCard = true;
#endif

        transform.Find("Dev").gameObject.SetActive(OpenLog);
       
        D.Debug?.Log($"37SDKTest:---->:K1D2Config:Start");
  
        Application.runInBackground=true;

    }

    /// <summary>
    /// 显示登录时长
    /// </summary>
    /// <param name="desc"></param>
    public static void ShowLoginStopwatch(string desc)
    {
        if(ShowLoginTime)
            D.Warning?.Log($"<color=yellow> {desc}, 总耗时: {Stopwatch.ElapsedMilliseconds * 1f / 1000} s </color>");
    }




    public string GetGateAddr()
    {

#if UNITY_EDITOR
        if (Loading_Res.Ins.IsFastLoginGold)
        {
            return "ws://ga-bp10u1f4emu46nsggacvh.aliyunga0018.com:31001";
        }
        else if (Loading_Res.Ins.IsFastLoginBeta)
        {
            return "ws://47.251.4.28:31001";
        }
        else if (Loading_Res.Ins.IsFastLoginDev)
        {
            return "ws://192.168.31.11:8001";
        }
        else if (!string.IsNullOrEmpty(Loading_Res.Ins.GateAddr))
        {
            return Loading_Res.Ins.GateAddr;
        }
#endif


        var gate_addr = Entrance.mClientInfo?.gateUrl ?? string.Empty;
        if (!string.IsNullOrEmpty(gate_addr))
        {
            return gate_addr;
        }

#if INTERNAL_ENTRANCE || UNITY_EDITOR
        return "ws://192.168.31.11:8001"; //dev
#elif DEFAULT_ENV_BETA
               return "ws://47.251.4.28:31001"; //beta
#else
                return "ws://ga-bp10u1f4emu46nsggacvh.aliyunga0018.com:31001"; //线上
#endif 
    }
}
