﻿
using Common;
using cspb;
using Cysharp.Threading.Tasks;
using DeepUI;
using Game.Config;
using Game.Data;
using K1;
using K3;
using Logic;
using Public;
using Render;
using System;
using System.Collections.Generic;
using TFW;
using TFW.UI;
using UI.Utils;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

namespace UI
{
    /// <summary>
    /// 菜单栏世界按钮
    /// </summary>
    public class UIMainMenuWorld : MonoBehaviour
    {
        #region 属性字段信息

        ///// <summary>
        ///// 体力进度条
        ///// </summary>
        //private RectTransform powerSliderFill;

        ///// <summary>
        ///// 体力进度条
        ///// </summary>
        //private Slider powerSlider;

        ///// <summary>
        ///// 体力数字显示
        ///// </summary>
        //private TFWText powerNumText;

        ///// <summary>
        ///// 进度条图片
        ///// </summary>
        //private RectTransform powerFillNew;

        ///// <summary>
        ///// size
        ///// </summary>
        //private Vector2 powerFillNewSizeDelta;

        ///// <summary>
        ///// 默认宽度
        ///// </summary>
        //private float defualtWidth;

        public GameObject worldNode,homeNode;

        public GameObject lockObj, unlockObj;

        #endregion 属性字段信息

        #region 初始化



        /// <summary>
        ///计时器实例
        /// </summary>
        //private NTimer.Timer m_restoreTimer;

        private int enterWorldNum;

        UIMain2 Main;

        private void Start()
        {
            UIBase.AddRemoveListener(TFW.EventTriggerType.Click, gameObject, ClickMenuBtn); 
        }

        private void OnEnable()
        {
            EventMgr.RegisterEvent(TEventType.OnSwitchMainUI, OnSwitchMainUI, this);
            EventMgr.RegisterEvent(TEventType.OnUnlockConditionChanged, UpdateMenuOpenState, this);

            UpdateMenuOpenState(null);
        }

        private void OnDisable()
        {
            EventMgr.UnregisterEvent(this);
            enterWorldNum = 0;
        }

        private void OnSwitchMainUI(object[] objs)
        {
            switch (GameData.I.MainData.CurrMenuType)
            {
                case MainMenuType.CITY:
                    ExitMenuPanel();
                    break;
                case MainMenuType.WORLD:
                    //前往世界地图
                    //刷新世界信息展示
                    var ok = UpdatWorldInfo();
                    if (ok)
                    {
                        EnterMenuPanel();
                        //跳转大地图相当于切换场景，关闭所有ui
                        PopupManager.I.ClearAllPopup();
                    }
                    break;
                default:
                    break;
            }
        }

        ///// <summary>
        ///// 数据初始化
        ///// </summary>
        //protected override void Init()
        //{
        //    base.Init();

        //    enterWorldNum = 0;

        //    //开始计时器数据刷新
        //    //if (m_restoreTimer == null)
        //    //    m_restoreTimer = NTimer.New();

        //    //每秒刷新一次
        //    //m_restoreTimer.StartTick(NTimer.INF, 1.0f, UpdatePowerInfo);



        //    SelectedObj = UIHelper.GetChild(Root, "Selected");
        //    //powerSlider = UIHelper.GetComponent<Slider>(Root, "Selected/Slider");
        //    //powerNumText = UIHelper.GetComponent<TFWText>(Root, "Selected/Slider/TxtNum");
        //    //powerFillNew = UIHelper.GetComponent<RectTransform>(Root, "Selected/Slider/FillNew");
        //    //defualtWidth = powerFillNew.sizeDelta.x;
        //    //powerFillNewSizeDelta = powerFillNew.sizeDelta;
        //    //powerSliderFill = UIHelper.GetComponent<RectTransform>(Root, "Selected/Slider/Fill Area/Fill");
        //}



        //public override void Clear()
        //{
        //    base.Clear();

        //    enterWorldNum = 0;
        //    m_restoreTimer?.Stop();
        //    m_restoreTimer = null;
        //}

        #endregion 初始化

        #region 数据刷新

        /// <summary>
        /// 隐藏世界场景中的Panel
        /// </summary>
        private void HideWorldPanel()
        {
            //WndMgr.Hide<UIPlayerSelfCityPopup>();
            //WndMgr.Hide<UIPlayerOtherCityPopup>();
            PopupManager.I.ClosePopup<UIPlayerCityInfo>();
            WndMgr.Hide<UINpcTroopPopup>();
            PopupManager.I.ClosePopup<UIInvestigationResults>();
            PopupManager.I.ClosePopup<UIPlayerCityBuff>();
            PopupManager.I.ClosePopup<UIRallyBattleNpc>();
            WndMgr.Hide<UIPlayerTroopPopup>();
        }

        /// <summary>
        /// 刷新外城信息显示
        /// </summary>
        private bool UpdatWorldInfo()
        {
            //if (!LPlayer.I.CanToDoByCitybuilding((int)MetaConfig.UnlockWorldBtn, true))//LSkill.I.CanToDoByCityLevel_ConfigID
            //    return false;

            if(Main==null)
            Main = PopupManager.I.FindPopup<UIMain2>();

            Action refreshWorld = new Action(() =>
            {
                //刷新城市信息
                UpdateMainCityInfo();

                //发送进入世界信息
                SendEnterWorld();

                WorldSwitchMgr.I.ShowWorldType = WorldTypeEnum.WORLD;

                //通知外部事件信息
                EventMgr.FireEvent(TEventType.CHANGE_SLG_OR_INNER);
                EventMgr.FireEvent(TEventType.PlayerProtect);

                //由于大地图切换缩放按钮 对其进行了隐藏处理，此时直接切换到内城 没有恢复显示 所以在此处显示下
                if (Main != null)
                {
                    Main.IsFirstClickMap = true;
                    Main?.UpdateMenuActive(false);

                    //GameData.I.MainData.CurrMenuType 还没更新,延迟检查
                    NTimer.CountDown(0.2f, () =>
                    {
                        if (LPlayer.I.IsPlayerInUnion())
                        {
                            //D.Error?.Log($"CheckIsShowAllianceMoveCityTip 001");
                            //var uiGuid = UI.WndMgr.Get<UIEpisodeGuid>();//
                            if (enterWorldNum == 0) //(uiGuid == null || !uiGuid.IsShow) &&
                            {
                                if (Main != null)
                                {
                                    //D.Error?.Log($"CheckIsShowAllianceMoveCityTip 002");
                                    //监测迁城提示
                                    (Main as UIMain2)?.CheckIsShowAllianceMoveCityTip();

                                    enterWorldNum += 1;
                                }
                            }

                        }

                        EventMgr.FireEvent(TEventType.GoToWorldEnd);
                    });
                }

                PhysicalPowerMgr.I.GetPowerBuyNum();

                //天下大势区域事件
                ChronicleMgr.I.SetMapAreaEvent();
            });

            if (k1.MapDelayInitMgr.I.MapInitStatus != k1.MapDelayInitMgr.MapInitStatusEnum.INIT_DONE)
            {
                UniTask.Void(async () =>
                {
                    await new WaitUntil(() => k1.MapDelayInitMgr.I.MapInitStatus == k1.MapDelayInitMgr.MapInitStatusEnum.INIT_DONE);

                    refreshWorld();
                });
            }
            else
            {
                refreshWorld();
            }

            return true;
        }

        /// <summary>
        /// 刷新主城信息
        /// </summary>
        private bool UpdateMainCityInfo()
        {
            //这个不靠谱 id不为 0，没执行？
            if (LPlayer.I.GetMainCityID() == 0)
            {
                if (Main != null)
                { 
                    Main.IsFirstClickMap = false;
                }
                var req = new NewMapCityReq();
                MessageMgr.Send(req);

                LAllianceTerritoryBuilding.I.TryFirstDraw();
                return false;
            }

            LAllianceTerritoryBuilding.I.TryFirstDraw();

            return true;
        }

        /// <summary>
        /// 发送进入世界消息
        /// </summary>
        private void SendEnterWorld()
        {
            var req_enter = new EnterWorldReq();
            MessageMgr.Send(req_enter);
            GameData.I.WorldMapData.TrySetFirstEnterWorld();
        }

        /// <summary>
        /// 发送搜索敌人信息
        /// </summary>
        private void SendSearchEnemy()
        {
            if (LSearch.I.CurrItem == null)
            {
                Debug.LogWarningFormat("LSearch.I.CurrItem == null");
                return;
            }

            var npcConfig = Cfg.C.CD2NpcTroopClass.I(LSearch.I.CurrItem.SearchId);
            if (npcConfig == null)
                return;
            var level = LPlayer.I.GetKillOrderData(npcConfig.Id);
            var maxLv = level > 0 ? level + 1 : 1;

            LSearch.I.NeedResearch = true;
            var refIdx = LSearch.I.Choose;
            LSearch.I.Choose = refIdx;
            LPlayer.I.SearchEnmyId = refIdx;
            LSearch.I.SearchLevel = maxLv;
            LSearch.I.TrySearch();
        }

        /// <summary>
        /// 退出菜单栏界面
        /// </summary>
        /// <param name="nextType"></param>
        public  void ExitMenuPanel()
        { 
            worldNode.SetActive(false);
            homeNode.SetActive(true);

            //清理当前正在播放的大地图背景音乐记录
            //DayNightSystem_WorldAudio.I.ClearPlayWorldBGM();

            //if (nextType != MainMenuType.PRISON)
            {
                WndMgr.Hide<UIUnionInviteTips>();
            }
            //if (nextType != MainMenuType.ALLIANCE)
            {
                //WndMgr.Hide<UIChat>();
                PopupManager.I.ClosePopup<UIPlayerReName>();
            }
            WndMgr.Hide<UIUnionMoveCityTips>();
            WndMgr.Hide<UIAllianceWarArea>();
            PopupManager.I.ClosePopup<UISearch>();
            PopupManager.I.ClosePopup<UIGather>();
            //WndMgr.Hide<UIPlayerSelfCityPopup>();
            //WndMgr.Hide<UIPlayerOtherCityPopup>();
            PopupManager.I.ClosePopup<UIPlayerCityInfo>();
            WndMgr.Hide<UINpcTroopPopup>();
            PopupManager.I.ClosePopup<UIInvestigationResults>();
            PopupManager.I.ClosePopup<UIPlayerCityBuff>();
            PopupManager.I.ClosePopup<UIRallyBattleNpc>();
            WndMgr.Hide<UIPlayerTroopPopup>();


            GameAudio.PlayBGM(AudioConst.Merge_BGM);
        }

        /// <summary>
        /// 刷新解锁状态
        /// </summary>
        public void UpdateMenuOpenState(object[] objs)
        { 
            var isOpen = MainFunctionOpenUtils.WorldOpenState;
            lockObj.SetActive(!isOpen);
            unlockObj.SetActive(isOpen);
        }

        //private TextLoopDisplay powerText;
        //private long lastTime;
        //private long lastAction;

        //private List<string> powerTxt = new List<string>() { "", "" };
        //private string phyPowerStr = "";
        //private string lastTimeStr = "";

        /// <summary>
        /// 刷新体力信息数据
        /// </summary>
        //public async void UpdatePowerInfo()
        //{
        //    var param=await PlayerAssetsMgr.I.GetShowAction();
        //    long currentPhyPower = (long)param[0];
        //    long maxPhyPower = (long)param[1];
        //    var energy = (float)currentPhyPower / maxPhyPower;
        //    if (powerFillNew)
        //    {
        //        if (energy < 0)
        //        {
        //            energy = 0f;
        //        }
        //        if (energy > 1f)
        //        {
        //            energy = 1f;
        //        }
        //        //powerSlider.value = energy;
        //        //由于slider设置有几率出现空槽情况,改用图片宽度
        //        powerFillNewSizeDelta.x = energy * defualtWidth;
        //        powerFillNew.sizeDelta = powerFillNewSizeDelta;

        //        //if (powerSliderFill)
        //        //{
        //        //    Debug.LogFormat($"UpdatePowerInfo currentPhyPower={currentPhyPower}, maxPhyPower={maxPhyPower}, energy={energy}, anchorMax={powerSliderFill.anchorMax}, anchorMin={powerSliderFill.anchorMin}, sizeDelta={powerSliderFill.sizeDelta}, offsetMax={powerSliderFill.offsetMax},offsetMin={powerSliderFill.offsetMin}");
        //        //}

        //        if (powerText == null)
        //            powerText = UIHelper.GetAddComponent<TextLoopDisplay>(powerNumText.gameObject);

        //        phyPowerStr = string.Format("{0}/{1}", currentPhyPower, maxPhyPower);

        //        if (currentPhyPower < maxPhyPower)
        //        {
        //            lastTime = PlayerAssetsMgr.I.RestoreActionTotalTime;
        //            lastTimeStr = UIHelper.GetFormatTime(lastTime);

        //            powerTxt[0] = phyPowerStr;
        //            powerTxt[1] = lastTimeStr;

        //            if (!powerText.enabled)
        //            {
        //                powerText.enabled = true;
        //            }

        //            //powerText.RefreshList(new System.Collections.Generic.List<string>() { $"{currentPhyPower}/{maxPhyPower}", $"{UIHelper.GetFormatTime(lastTime)}" }, false);
        //            powerText.RefreshList(powerTxt, false);
        //        }
        //        else
        //        {
        //            if (powerText && powerText.enabled)
        //            {
        //                powerText.enabled = false;
        //            }

        //            powerNumText.text = phyPowerStr;
        //            //powerText.RefreshList(new System.Collections.Generic.List<string>() { $"{currentPhyPower}/{maxPhyPower}" });
        //        }
        //    }
        //}

        #endregion 数据刷新

        #region 声音播放

        /// <summary>
        /// 声音播放
        /// </summary>
        private void PlayAudio()
        {
            AudioManager.Instance.StopOtherAudioChannel(AudioConst.AUDIO_CHANNEL_STOP,
                            AudioChannelType.UI, AudioChannelType.WORLD, AudioChannelType.BGM, AudioChannelType.GuidHeroAudio);

            //直接停止当前通道的声音（避免在声音停止过渡时间的时候，又播放当前通道的声音，导致不能正常播放）
            AudioManager.Instance.StopAudioChannel(AudioChannelType.WORLD);

            ////播放外城背景音

            GameAudio.PlayBGM(AudioConst.WORLD_BGM);

            //DayNightSystem_WorldAudio.I.UpdateWorldBGM(true);
        }

        #endregion 声音播放

        #region 事件监听

       

        /// <summary>
        /// 点击造兵
        /// </summary>
        //private void ClickWorldBtn()
        //{
        //    //隐藏世界场景中的界面
        //    HideWorldPanel();
        //    if (Main != null)
        //    {
        //        //监测显示联盟迁城提示
        //        //(Main as UIMain1)?.CheckIsShowAllianceMoveCityTip();
        //        (Main as UIMain2)?.CheckIsShowAllianceMoveCityTip();
        //    }
        //    //点击搜索按钮
        //    ClickSearchBtn();
        //}


        //public static int SearchType = -1;

        ///// <summary>
        ///// 点击搜索按钮处理
        ///// </summary>
        //private void ClickSearchBtn()
        //{
        //    var uiSearch = PopupManager.I.FindPopupFromPool<UISearch>();
        //    if (uiSearch != null && uiSearch.IsShow)
        //    {
        //        //搜索数据信息
        //        uiSearch.PublicSearchClick();
        //    }
        //    else
        //    {
        //        if (SearchType >= 0)
        //        {
        //            //打开搜索界面
        //            PopupManager.I.ShowPanel<UISearch>(new UISearchData() { SearchType = SearchType });

        //            SearchType = -1;
        //        }
        //        else
        //        {
        //            PopupManager.I.ShowPanel<UISearch>();
        //        }
        //    }
        //}

        public  void EnterMenuPanel()
        {
            worldNode.SetActive(true);
            homeNode.SetActive(false);

            PlayAudio();
        }
         
        /// <summary>
        /// 点击菜单按钮
        /// </summary>
        public void ClickMenuBtn(GameObject ga, PointerEventData po)
        { 
            if (!MainFunctionOpenUtils.WorldOpenState)
            {
                //if (lockAnim != null)
                //    lockAnim.Play();
                FloatTips.I.FloatMsg(UnlockMgr.I.GetUnlockDesc(UnlockFuncEnum.World));
                return;
            }

            switch (Game.Data.GameData.I.MainData.CurrMenuType)
            {
                case MainMenuType.CITY:
                    EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.WORLD); 
                    break;
                case MainMenuType.WORLD:
                    //离开世界地图
                    EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.CITY);
                    break;
                default:
                    break;
            }  
        }

        #endregion 事件监听
    }
}