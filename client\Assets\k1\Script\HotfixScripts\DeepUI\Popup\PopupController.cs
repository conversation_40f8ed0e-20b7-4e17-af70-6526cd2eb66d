﻿using Cysharp.Threading.Tasks;
using Game.Config;
using NewGuide;
using System;
using System.Collections.Generic;
using System.Reflection;
using TFW;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using EventTriggerType = TFW.EventTriggerType;

namespace DeepUI
{
    internal abstract class PopupController : MonoBehaviour
    {
        public delegate void EmptyStateChange(bool isEmpty);

        protected PopupManager _popupCache => PopupManager.I;
        
        private int sortingLayerID;
       
        //private GameObject mask;
        //private GameObject blur;
        //private RawImage blurImg;

        private string currentPipeline;
        private Dictionary<string, List<Context>> sceneMask;
        private protected List<Context> PopupStack;

        private List<BasePopup> activePopups;
        private List<BasePopup> deactivePopups;

        private Dictionary<string, bool> pendingLoading;
        private Dictionary<Type, List<StackContext>> pendingNodePool;

        private int keyCounter;                                                            // key生成器
        private protected float SubsidiaryPlanDistance;
        private protected float ActionPlanDistance;
        private List<Action> tickCallbackQueue;

        private PopupController decoratedController;

        internal bool AnyPopup => activePopups?.Count > 0; // this.PopupStack?.Count > 0;

        internal bool AnyPopupExitPopup<T>() where T : BasePopup
        {
            if (this.activePopups == null || this.activePopups.Count <= 0) return false;
            foreach (var popup in this.activePopups)
            {
                if (popup != null)
                {
                    if (popup is T)
                        continue;
                    else
                        return true;
                }
            }
            return false;
        }
         


        internal EmptyStateChange OnEmptyStateChange;

        private void LateUpdate()
        {
            if (this.tickCallbackQueue == null) return;

            if (this.tickCallbackQueue.Count > 0)
            {
                this.tickCallbackQueue.ForEach(action => action());
                this.tickCallbackQueue.RemoveRange(0, this.tickCallbackQueue.Count);
            }

            //刷新被托管对象
            var count = this.deactivePopups.Count;
            for (int i = 0; i < count; i++)
            {
                this.activePopups.Remove(this.deactivePopups[i]);
            }

            var count1 = this.activePopups.Count;
            for (int i = 0; i < count1; i++)
            {
                this.activePopups[i].Update(Time.deltaTime);
            }

            this.deactivePopups.Clear();
        }

        internal void Init(int sortingLayerID, float planDistanceOffset,string defaultPipeline)
        {
            this.sortingLayerID = sortingLayerID;

            this.SubsidiaryPlanDistance = planDistanceOffset - 10;
            this.ActionPlanDistance = planDistanceOffset - 10 * 2;

            //var maskTransform = this.transform.Find("Mask");
            //var blurTransform = this.transform.Find("Blur");

            //this.ui_blur_effect = GameInstance.UICamera.transform.GetComponent<ScreenBlurEffect>();
            //if (maskTransform && blurTransform)
            //{
            //    //this.mask = maskTransform.gameObject;
            //    //this.mask.SetActive(false);

            //    //var eventTriggerListener = this.mask.GetComponent<EventTriggerListener>();
            //    //eventTriggerListener.AddListener(EventTriggerType.Click, this.OnMaskClick);

            //    //var canvas = this.mask.GetComponent<Canvas>();
            //    //canvas.renderMode = RenderMode.ScreenSpaceCamera;
            //    //canvas.worldCamera = GameInstance.UICamera;
            //    //canvas.sortingLayerID = sortingLayerID;
            //    //canvas.sortingOrder = -1;
            //    //canvas.planeDistance = planDistanceOffset;                    // 非必要，仅为 Scene 模式下调试查看时层级清晰

            //    //this.blur = blurTransform.gameObject;
            //    //this.blur.SetActive(false);
            //    //this.blurImg = this.blur.GetComponent<RawImage>();
            //    //var blurEventTriggerListener = this.blur.GetComponent<EventTriggerListener>();
            //    //blurEventTriggerListener.AddListener(EventTriggerType.Click, this.OnMaskClick);

            //    //var canvasB = this.blur.GetComponent<Canvas>();
            //    //canvasB.renderMode = RenderMode.ScreenSpaceCamera;
            //    //canvasB.worldCamera = GameInstance.UICamera;
            //    //canvasB.sortingLayerID = sortingLayerID;
            //    //canvasB.sortingOrder = -1;
            //    //canvasB.planeDistance = planDistanceOffset;
            //}

            this.keyCounter = 0;
            this.tickCallbackQueue = new List<Action>(1);

            this._popupCache.PopupPool = new Dictionary<Type, BasePopup>();
            this.PopupStack = new List<Context>();
            this.sceneMask = new Dictionary<string, List<Context>>();
            this.activePopups = new List<BasePopup>();
            this.deactivePopups = new List<BasePopup>();

            this.pendingLoading = new Dictionary<string, bool>();
            this.pendingNodePool = new Dictionary<Type, List<StackContext>>();

            this.currentPipeline = defaultPipeline;
            this.sceneMask.Add(this.currentPipeline, this.PopupStack);
        }

        internal void DecorateInteractionMask(PopupController controller)
        {
            this.decoratedController = controller;
        }

        #region 集成扩展功能

        //internal void RegistryPipelines(IEnumerable<string> pipelines)
        //{
        //    foreach (var pipeline in pipelines)
        //    {
        //        if (pipeline == this.currentPipeline || this.sceneMask.ContainsKey(pipeline)) continue;
        //        this.sceneMask.Add(pipeline, new List<Context>());
        //    }
        //}

        internal void ClearAllPopup(Action<bool> onComplete = null)
        {
            var stack = this.PopupStack;
            var head = stack.Count > 0 ? stack[stack.Count - 1] : null;
            if (head != null)
            {
                if (this.OnEmptyStateChange != null)
                {
                    this.OnEmptyStateChange(true);
                }


                for (int i = 0; i < stack.Count; i++)
                {
                    var cxt = stack[i];
                    if (this._popupCache.PopupPool.TryGetValue(cxt.ClassType, out var p))
                    {
                        HidePopup(cxt, (s) => { });
                        //p.OnCloseStart();
                        //p.AfterClose();
                        //p.OnCloseComplete();
                    }
                }

                stack.Clear();

                onComplete?.Invoke(true);

                //this.HidePopup(head, success =>
                //{
                //    Context cxt = null;
                //    for (int i = 0; i < stack.Count; i++)
                //    {
                //        cxt = stack[i];
                //        if (this._popupCache.PopupPool.TryGetValue(cxt.ClassType, out var p))
                //        {
                //            p.OnCloseStart();
                //            p.AfterClose();
                //            p.OnCloseComplete();
                //        }
                //    } 

                //    //this.SetOverlayActive(PopupOverlayType.None, false);
                //    stack.Clear();

                //    onComplete?.Invoke(success);
                //});
            }
            else
            {
                //this.SetOverlayActive(PopupOverlayType.None, false);
                onComplete?.Invoke(true);
            }
        }

        //internal void PackageAllPopup(string targetPipeline, Action<bool> onComplete = null, bool clear = false)
        //{
        //    if (this.currentPipeline == targetPipeline)
        //    {
        //        onComplete?.Invoke(true);
        //        return;
        //    }

        //    var stack = this.PopupStack;
        //    var head = stack.Count > 0 ? stack[stack.Count - 1] : null;
        //    if (this.sceneMask.TryGetValue(targetPipeline, out var s))
        //    {
        //        this.currentPipeline = targetPipeline;
        //        this.PopupStack = s;

        //        if (head != null)
        //        {
        //            if (this.OnEmptyStateChange != null)
        //            {
        //                this.OnEmptyStateChange(true);
        //            }

        //            this.HidePopup(head, success =>
        //            {
        //                //this.mask.SetActive(false);                        // 直接设置为不可见，不可通过装饰器逻辑赋值
        //                //this.blur.SetActive(false);
        //                if (clear) stack.Clear();

        //                onComplete?.Invoke(success);
        //            });
        //        }
        //        else
        //        {
        //            onComplete?.Invoke(true);
        //        }
        //    }
        //    else
        //    {
        //        throw new ArgumentException($"Unregistered SceneName {targetPipeline}");
        //    }
        //}

        //internal void ReleaseAllPopup(Action<bool> onComplete = null)
        //{
        //    this.CallNextTick(() => CheckPopupStackForLoad(this.PopupStack, this.LoadPopup, WrapEmptyAction(onComplete)));
        //}

        internal bool PopBack(bool force = true)
        {
            if (this.AnyPopup)
            {
                var head = this.PopupStack[this.PopupStack.Count - 1];
                if (head.WillShow == 0 && this._popupCache.PopupPool.TryGetValue(head.ClassType, out var popup))
                {
                    if ((force || popup.PopBackEnabled) && popup.PopBackESC)
                    {
                        if (force)
                        {
                            bool popEnd=popup.Pop();
                            if (popEnd)
                            {
                                return popEnd;
                            }
                        }
                        popup.Close();
                    }
                    return true;
                }
            }

            return false;
        }

        internal BasePopup FindPopup(Type type)
        {
            //if (this.AnyPopup)
            //{
            //    //var head = this.PopupStack[this.PopupStack.Count - 1];
            //    if ( this._popupCache.PopupPool.TryGetValue(type, out var popup))//ReferenceEquals(head.ClassType, type) &&
            //    {
            //        return popup;
            //    }
            //}

            return FindPopupAllStack(type);
        }

        /// <summary>
        /// 查找堆栈中所有界面
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        internal BasePopup FindPopupAllStack(Type type)
        {
            if (this.AnyPopup)
            {
                foreach (var contex in this.PopupStack)
                {
                    if (ReferenceEquals(contex.ClassType, type) && this._popupCache.PopupPool.TryGetValue(type, out var popup))
                    {
                        return popup;
                    }
                }
            }

            return null;
        }

        internal T FindPopupFromPool<T>() where T : BasePopup
        {
            return this._popupCache.PopupPool.TryGetValue(typeof(T), out var popup) ? popup as T : null;
        }

        internal BasePopup FindPopupFromPool(Type _type)
        {
            if (this._popupCache.PopupPool != null && _type!=null)
            {
                return this._popupCache.PopupPool.TryGetValue(_type, out var popup) ? popup : null;
            }
            else
                return null;
        }


        /// <summary>
        /// 当前Controller中已经打开的界面的数量
        /// </summary>
        /// <param name="type">该类型除外</param>
        /// <returns></returns>
        internal int FindPopupCountExceptType(List<Type> types)
        {
            if (this._popupCache.PopupPool == null || this.PopupStack == null)
                return 0;

            int count = 0;
            foreach (var context in this.PopupStack)
            {
                if (this._popupCache.PopupPool.TryGetValue(context.ClassType, out var p))
                {
                    if (types != null && !types.Contains(context.ClassType))
                    {
                        count++;
                    }
                }
            }

            return count;
        }

        /// <summary>
        /// 当前Controller中已经打开的界面的数量
        /// </summary>
        /// <param name="type">该类型除外</param>
        /// <returns></returns>
        internal int FindPopupCountExceptType(Type type)
        {
            if (this._popupCache.PopupPool == null || this.PopupStack == null)
                return 0;

            int count = 0;
            foreach (var context in this.PopupStack)
            {
                if (this._popupCache.PopupPool.TryGetValue(context.ClassType, out var p))
                    count++;
            }

            if (type == null)
                return count;

            if (this.FindPopup(type) != null)
                return count - 1;

            return count;
        }


        #endregion

        #region 基础功能

        internal UniTask<bool> ShowPopupAsync<T>(object data, string pipeline, Priority priority) where T : BasePopup, new()
        {
            var utcs = new UniTaskCompletionSource<bool>();

            this.ShowPopup<T>(data, pipeline, priority, (success) =>
            {
                utcs.TrySetResult(success);
            });

            return utcs.Task;
        }

        internal UniTask<bool> ShowPopupAsync(Type uitype,object data, string pipeline, Priority priority) 
        {
            var utcs = new UniTaskCompletionSource<bool>();

            this.ShowPopup(uitype,data, pipeline, priority, (success) =>
            {
                utcs.TrySetResult(success);
            });

            return utcs.Task;
        }

        internal void ShowPopup<T>(object data, string pipeline, Priority priority, Action<bool> onComplete) where T : BasePopup, new()
        {
            this.ShowPopup(typeof(T), data, pipeline, priority, onComplete);
        }

        internal void ShowPopup(Type type, object data, string pipeline, Priority priority, Action<bool> onComplete)
        {

            //D.Error?.Log($"ShowPopup:{type.Name}");

            if (PopupManager.I.PopupBeforeShowAction != null)
            {
                PopupManager.I.PopupBeforeShowAction(type.Name);
            }

            List<Context> stack;
            if (pipeline != null)
            {
                if (!this.sceneMask.TryGetValue(pipeline, out stack))
                {
                    throw new ArgumentException($"Unregistered Pipeline {pipeline}");
                }
            }
            else
            {
                stack = this.PopupStack;
            }

            var context = this.CreateContext(type, data);
            this.ShowPopup(stack, context, WrapEmptyAction(onComplete));
        }

        internal bool ClosePopup<T>(Action onComplete = null) where T : BasePopup
        {
           
            return this.ClosePopup(typeof(T), onComplete);
        }

        internal bool ClosePopup(Type type, Action onComplete = null)
        { 
            if (PopupManager.I.PopupAfterCloseAction != null)
            {
                PopupManager.I.PopupAfterCloseAction(type.Name);
            }

            var context = FindFromStack(type, this.PopupStack);
            if (context != null)
            { 
                this.ClosePopup(this.PopupStack, context, () =>
                {
                    onComplete?.Invoke();
                }, false);
                return true;
            }
            else
            {
                onComplete?.Invoke();
                return false;
            }
        }

        internal bool ClosePopup<T>(object data, Action onComplete = null, string pipeline = null) where T : BasePopup, new()
        {
            List<Context> stack;
            if (pipeline != null)
            {
                if (!this.sceneMask.TryGetValue(pipeline, out stack))
                {
                    throw new ArgumentException($"Unregistered SceneName {pipeline}");
                }
            }
            else
            {
                stack = this.PopupStack;
            }

            var context = FindFromStack(typeof(T), stack, data);
            if (context != null)
            {
                this.ClosePopup(stack, context, () =>
                {
                    onComplete?.Invoke();
                }, false);
                return true;
            }
            else
            {
                Debug.LogError($"Can Not Find Context {typeof(T)} Binding {data}");
                onComplete?.Invoke();
                return false;
            }
        }

        internal void DisposeAllPopup()
        {
            foreach (var kv in this._popupCache.PopupPool)
            {
                var popup = kv.Value;
                popup.Dispose();
                DestroyPopup(popup);
            }

            this._popupCache.PopupPool.Clear();
        }

        /// <summary>
        /// 销毁某个界面
        /// </summary>
        /// <typeparam name="T"></typeparam>
        internal void DisposePopup<T>() where T : BasePopup, new()
        {
            if (this._popupCache.PopupPool.TryGetValue(typeof(T), out var popup))
            {
                popup.Dispose();
                DestroyPopup(popup);

                this._popupCache.PopupPool.Remove(typeof(T));
            }
        }

        #endregion

        private protected abstract void ShowPopup(List<Context> stack, Context context, Action<bool> onComplete);

        private protected async void LoadPopup(List<Context> stack, Context context, Action<bool> onComplete)
        {
            context.WillHide = 0;

            var currentTime = Time.time;
            if (context.WillShow < currentTime)
            {
                context.WillShow = GetLockExpires(currentTime);
            }
            else
            {
                onComplete(false);
                return;
            }

            var classType = context.ClassType;
            if (this._popupCache.PopupPool.TryGetValue(classType, out var popup) && popup != null)
            {
                //this.SetOverlayActive(popup.OverlayType, true);
                popup.Context = context;
                popup.SetOrderDistance(this.ActionPlanDistance);

                if (popup.IsInitReady)
                {
                    popup.Animation?.Reset();
                    popup.OnDataReady();
                    popup.SetSortRender();
                }
                else
                {
                    popup.Start();
                }

                if (popup.Transform.parent)
                {
                    this.ActivePopupNode(popup, true);
                }
                else
                {
                    this.AttachPopupNode(popup);
                }
                
                
                this.DisplayPopup(stack, popup, onComplete).Forget();
                
                return;
            }

            this.pendingLoading.Add(context.Key, true);

            var stackContext = new StackContext(stack, context, onComplete);
            if (this.pendingNodePool.TryGetValue(classType, out var pool))
            {
                pool.Add(stackContext);
            }
            else
            {
                pool = new List<StackContext>(1);                    // 通常情况下仅需要容量为 1，TODO 采用对象池获取
                pool.Add(stackContext);
                this.pendingNodePool.Add(classType, pool);

//#if UNITY_WEBGL 
                PopupManager.I.TemporaryMask.SetActive(true);
                //Logic.ReconnectManager.I.ShowNetReconnectMark(true);
                var loadPopup = await this._popupCache.LoadPopupAsync(context.ClassType, this.transform);
                //Logic.ReconnectManager.I.ShowNetReconnectMark(false);
                PopupManager.I.TemporaryMask.SetActive(false);
//#else

//                var loadPopup = this._popupCache.LoadPopup(context.ClassType, this.transform);
//#endif
                if (loadPopup != null)
                {
                    if (stack != null && stack.Count <= 0)
                    {
                        //异步加载过程中可能UI栈被清理, 生成的UI不被框架控制，需要立即删除从
                        this.ClosePopup(stack, context, EmptyCallback, false);
                        DestroyImmediate(loadPopup?.GameObject);
                        this.pendingNodePool.Remove(classType);
                        return;
                    }

                    if (!this._popupCache.PopupPool.ContainsKey(classType))
                    {
                        this._popupCache.PopupPool.Add(classType, loadPopup);

                        loadPopup.Init();
                    }

                    bool checkPopup = false;

                    for (var i = 0; i < pool.Count; i++)
                    {
                        var sc = pool[i];
                        if (sc == null) continue;

                        var c = sc.Context;
                        if (this.pendingLoading.ContainsKey(c.Key))
                        {
                            this.pendingLoading?.Remove(c.Key);
                            if (c.WillShow > 0)
                            {
                                loadPopup.Context = c;
                                loadPopup.SetOrderDistance(this.ActionPlanDistance);
                                this.ActivePopupNode(loadPopup, true);
                                loadPopup?.Start();

                                loadPopup.Canvas.enabled = false;
                                this.DisplayPopup(sc.Stack, loadPopup, sc.Callback).Forget();
                                if(loadPopup != null && loadPopup.Canvas)
                                    loadPopup.Canvas.enabled = true;
                            }
                            else
                            {
                                loadPopup?.SetActive(false);
                            }
                        }
                        else
                        {
                            context.WillShow = 0;
                            loadPopup?.SetActive(false);
                        }

                        checkPopup = true;
                    }

                    if (checkPopup)
                    {
                        //正常流程，显示遮罩
                        //this.SetOverlayActive(loadPopup.OverlayType, true);
                    }
                    else
                    {
                        //非正常流程，创建的预制体不受框架控制，应该立即销毁
                        this.ClosePopup(stack, context, EmptyCallback, false);
                        DestroyImmediate(loadPopup?.GameObject);
                        this.pendingNodePool?.Remove(classType);
                        this._popupCache.PopupPool?.Remove(classType);
                    }

                }
                else
                {
                    pool?.ForEach(sc =>
                    {
                        var c = sc?.Context;
                        if (c != null)
                        {
                            this.pendingLoading.Remove(c.Key);
                            if (c.WillShow > 0)
                            {
                                sc.Callback(false);
                            }
                        }
                    });

                    //this.SetOverlayActive(PopupOverlayType.None, false);
                }

                this.pendingNodePool?.Remove(classType);
            }
        }

        #region 打开/显示逻辑

        


        private async UniTaskVoid DisplayPopup(List<Context> stack, BasePopup popup, Action<bool> onComplete)
        {
            popup.SetCloseFunction((context, stopPropagation) => this.ClosePopup(stack, context, EmptyCallback, stopPropagation));
            var popupAnimation = popup.Animation;
            if (popup?.Context?.Exposure == true)
            {
                popup.OnShowStart();
                if (popupAnimation != null)
                {
                    popup.PauseSystemEvents();
                    popupAnimation.Show(p =>
                    {
                        OnDisplayPopupShow(p);
                        onComplete(true);
                    });
                }
                else
                {
                    OnDisplayPopupShow(popup);
                    onComplete(true);
                }
            }
            else
            {
                if (popup != null)
                {
                    popup.BeforeOpen();
                    popup.OnOpenStart();
                    if (popup is IAsyncOpenPopup)
                    {
                        var iPopup = popup as IAsyncOpenPopup;
                        if (iPopup != null)
                        {
                            await iPopup.BeforeOpenCompleteAsync();
                        }
                    }
                    if (popupAnimation != null)
                    {
                        popup.PauseSystemEvents();
                        popupAnimation.Open(p =>
                        {
                            OnDisplayPopupOpen(p, this);
                            onComplete(true);
                        });
                    }
                    else
                    {
                        OnDisplayPopupOpen(popup, this);
                        onComplete(true);
                    }
                }
                else
                {
                    onComplete(false);
                }
            }
        }

        private static void OnDisplayPopupShow(BasePopup popup)
        {
            if (popup == null)
                return;

            var context = popup.Context;
            if (context == null)
                return;

            context.WillShow = 0;
            try
            {
                popup.OnShowComplete();
            }
            catch (Exception ex)
            {
                D.Error?.Log(ex);
            }
            //保证能执行到 ResumeSystemEvents
            popup.ResumeSystemEvents();
        }

        private static void OnDisplayPopupOpen(BasePopup popup, PopupController controller)
        {
            if (popup == null || controller == null)
                return;

            //if (controller.OnEmptyStateChange != null && controller.AnyPopup)
            //{
            //    controller.OnEmptyStateChange(false);
            //}

            var context = popup.Context;
            if(context != null)
            {
                context.WillShow = 0;
                context.Exposure = true;
            }
            else
            {
                D.Error?.Log("context == null");
            }
            try
            {
                popup.OnOpenComplete();
                //DialogGuideMgr.I.CheckTriggerForFirstOpenUI(popup.GetType());
            }
            catch (Exception ex)
            {
                D.Error?.Log(ex);
            }

            //保证能执行到 ResumeSystemEvents
            popup.ResumeSystemEvents();
        }

        #endregion

        #region 隐藏逻辑

        private protected void HidePopup(Context context, Action<bool> onComplete)
        {
            context.WillShow = 0;
            var currentTime = Time.time;
            if (context.WillHide > currentTime)
            {
                onComplete(true);
            }
            else
            {
                this.pendingLoading.Remove(context.Key);

                if (this._popupCache.PopupPool.TryGetValue(context.ClassType, out var popup))
                {
                    context.WillHide = GetLockExpires(currentTime);
                    if (popup != null)
                    {
                        popup.OnHideStart();

                        this.CheckAnimBreakAndSetFlag(popup);
                        var popupAnimation = popup.Animation;
                        if (popupAnimation != null)
                        {
                            popup.PauseSystemEvents();
                            popupAnimation.Hide(p =>
                            {
                                this.ActivePopupNode(p, false);
                                OnHidePopup(p, onComplete);
                            });
                        }
                        else
                        {
                            this.ActivePopupNode(popup, false);
                            OnHidePopup(popup, onComplete);
                        }
                    }
                    else
                    {
                        this._popupCache.PopupPool.Remove(context.ClassType);
                        onComplete(false);
                    }
                }
                else
                {
                    onComplete(false);
                }
            }
        }

        public void CheckAnimBreakAndSetFlag(BasePopup popup)
        {
            var anim = popup.GameObject.GetComponentInChildren<Animator>();
            if(anim != null)
            {
                AnimatorStateInfo stateinfo = anim.GetCurrentAnimatorStateInfo(0);
                if (anim.enabled && stateinfo.normalizedTime < 1.0f && stateinfo.normalizedTime > 0)
                {
                    if (popup.NTimerId != default)
                    {
                        NTimer.Destroy(popup.NTimerId);
                        popup.NTimerId = default;
                    }
                    popup.IsAnimBreak = true;
                }
            }
            
        }

        private static void OnHidePopup(BasePopup popup, Action<bool> callback)
        {
            if (popup == null)
                return;

            if(popup.Context != null)
                popup.Context.WillHide = 0;

            if(callback != null)
                callback(true);
            popup.OnHideComplete();
            popup.PauseSystemEvents();
        }

        #endregion

        #region 关闭逻辑

        private protected virtual void ClosePopup(List<Context> stack, Context context, Action onComplete, bool stopPropagation)
        {
            //D.Error?.Log($"ClosePopup:{context.Key}");

            RemoveFromStack(stack, context.Key);
            if (this.pendingNodePool.TryGetValue(context.ClassType, out var stackContexts))
            {
                for (var i = 0; i < stackContexts.Count; i++)
                {
                    var sc = stackContexts[i];
                    if (sc != null && sc.Context.Key == context.Key)
                    {
                        stackContexts[i] = null;
                        break;
                    }
                }
            }

            if (this.OnEmptyStateChange != null && !this.AnyPopup)
            {
                this.OnEmptyStateChange(true);
            }

            if (this._popupCache.PopupPool.TryGetValue(context.ClassType, out var popup) && ReferenceEquals(popup.Context, context))
            {
                popup.SetOrderDistance(this.ActionPlanDistance);
                popup.SetCloseFunction(null);                                    // 防止关闭过程中，弹窗 Close 方法被调用
                popup.OnCloseStart();
                var popupAnimation = popup.Animation;
                if (popupAnimation != null)
                {
                    popup.PauseSystemEvents();
                    popupAnimation.Close(p =>
                    {
                        if (ReferenceEquals(p.Context, context))
                        {                // 二次校验，防止动画期间弹窗实例被重用打开
                            this.ActivePopupNode(p, false);
                            OnClosePopup(p, context.ClassType, stack, this._popupCache.PopupPool, onComplete,
                                stopPropagation, this.LoadPopup, (v, t) => { /*this.SetOverlayActive(t, v);*/ });
                        }
                        else
                        {
                            onComplete();
                            //this.SetOverlayActive(popup.OverlayType, stack.Count > 0);
                            if (!stopPropagation)
                            {
                                CheckPopupStackForLoad(stack, this.LoadPopup, EmptyCallback);
                            }
                        }
                    });
                }
                else
                {
                    this.ActivePopupNode(popup, false);
                    OnClosePopup(popup, context.ClassType, stack, this._popupCache.PopupPool, onComplete, 
                        stopPropagation, this.LoadPopup, (v, t) => {/* this.SetOverlayActive(t, v);*/ });
                }
            }
            else
            {
                this.pendingLoading.Remove(context.Key);
            }
        }

        private static void OnClosePopup(BasePopup popup, Type classType, List<Context> stack, Dictionary<Type, BasePopup> nodePool, Action onComplete,
            bool stopPropagation, Action<List<Context>, Context, Action<bool>> loadAction, Action<bool, PopupOverlayType> overlayAction)
        {
            popup.OnCloseComplete();
            popup.AfterClose();
            popup.PauseSystemEvents(); 

            if (/*popup.DisposeOnClose && */CheckPopupStackForDispose(stack, classType))
            {
                RemoveFromNodePool(nodePool, popup);
                popup.Dispose();
                DestroyPopup(popup);
                
                UniTask.NextFrame().ContinueWith(() =>
                {
                    PopupManager.I.ReleasePopup(classType);
                }).Forget();
            }

            onComplete();

            //int count = 0;
            //foreach (var context in stack)
            //{
            //    if (string.IsNullOrEmpty(context.isolatorKey))
            //        count++;
            //}

            if (stack.Count <= 0)
            {
                overlayAction(false, PopupOverlayType.None);
            }

            if (!stopPropagation)
            {
                CheckPopupStackForLoad(stack, loadAction, EmptyCallback);
            }
        }

        #endregion

        protected static void RemoveFromStack(List<Context> stack, string key)
        {
            for (var i = stack.Count - 1; i >= 0; i--)
            {                        // 倒序查询，通常命中集合末尾元素
                if (stack[i].Key == key)
                {
                    stack.RemoveAt(i);
                    break;
                }
            }
        }

        private static void RemoveFromNodePool(Dictionary<Type, BasePopup> popupPool, BasePopup popup)
        {
            var keyCollection = popupPool.Keys;
            var keys = new Type[keyCollection.Count];
            keyCollection.CopyTo(keys, 0);

            foreach (var key in keys)
            {
                if (popupPool.TryGetValue(key, out var p) && ReferenceEquals(p, popup))
                {
                    popupPool.Remove(key);
                    break;
                }
            }
        }

        private static Context FindFromStack(Type type, List<Context> stack)
        {
            for (var i = stack.Count - 1; i >= 0; i--)
            {                        // 倒序查询，通常命中集合末尾元素
                if (ReferenceEquals(stack[i].ClassType, type))
                {
                    return stack[i];
                }
            }

            return null;
        }

        private static Context FindFromStack(Type classType, List<Context> stack, object data)
        {
            for (var i = stack.Count - 1; i >= 0; i--)
            {                        // 倒序查询，通常命中集合末尾元素
                if (ReferenceEquals(stack[i].ClassType, classType) && ReferenceEquals(stack[i].Data, data))
                {
                    return stack[i];
                }
            }

            return null;
        }

        public  int GetMaxOrderNum(BasePopup popup)
        {
            int maxOrderNum = 0;
            var contents= this.activePopups;
            foreach (var c in contents)
            {
                if (c == popup)
                    continue;

                maxOrderNum = maxOrderNum > c.MaxOrderInLayerNum ? maxOrderNum : c.MaxOrderInLayerNum;
            }

            return maxOrderNum;
        }

        private static void CheckPopupStackForLoad(
            List<Context> stack, Action<List<Context>, Context, Action<bool>> loadAction, Action<bool> onComplete)
        {
            if (stack.Count > 0)
            {
                var context = stack[stack.Count - 1];
                if (!string.IsNullOrEmpty(context.isolatorKey))
                {
                    Debug.Log($"CheckPopupStackForLoad 处于被阻隔状态 {context.className}");
                    onComplete(true);
                }
                else if (context.WillShow < Time.time)
                {
                    Debug.Log($"CheckPopupStackForLoad 开始显示 {context.className}");
                    loadAction(stack, context, onComplete);
                }
                else
                {
                    Debug.Log($"CheckPopupStackForLoad 已在显示中 {context.className}");
                    onComplete(true);
                }
            }
        }

        private static bool CheckPopupStackForDispose(List<Context> stack, Type classType)
        {
#if UNITY_WEBGL
            foreach (var ctx in stack)
            {
                if (ReferenceEquals(ctx.ClassType, classType))
                {
                    return false;
                }
            }

            return true;
#else
            return false; 
#endif
        }

        public static string GetPopupAssetPath(Type classType)
        {
            var popupInfo = classType.GetCustomAttribute(typeof(Popup)) as Popup;
            var constructor = classType.GetConstructor(new Type[0]);
            var popup = (BasePopup)constructor?.Invoke(new object[0]);


            var assetPath = popupInfo?.AssetPath;
            if (String.IsNullOrEmpty(assetPath))
            {
                assetPath = popup?.AssetPath;
            }

            var fullAssetPath= ResourceDict.CheckPopupUIAssetPath(assetPath); 
            
           
            return fullAssetPath;
        }

        internal static void EmptyCallback() { }

        internal static void EmptyCallback(bool _) { }

        private static Action<bool> WrapEmptyAction(Action<bool> action)
        {
            return action ?? EmptyCallback;
        }

        private static void DestroyPopup(BasePopup node)
        {
            if (node.GameObject) 
            {
                Destroy(node.GameObject);
                
                node = null;
            }
        }

        private static float GetLockExpires(float currentTime)
        {
            return currentTime + 2f;                            // 所有动画时长都在 2 秒以内
        }

        private protected void AttachPopupNode(BasePopup popup)
        {
            if (popup == null)
                return;

            popup.Attach(this.transform);
        }

        private protected void ActivePopupNode(BasePopup popup, bool value)
        {
            if (popup == null)
                return;

            popup.SetActive(value); 
            popup.Resume();

            if (value)
            {
                if (this.activePopups != null && !this.activePopups.Contains(popup))
                {
                    this.activePopups.Add(popup);
                }
            }
            else
            {
                if (this.deactivePopups != null && !this.deactivePopups.Contains(popup))
                {
                    this.deactivePopups.Add(popup);
                }
            }
        }

        //private void SetOverlayActive(PopupOverlayType type, bool value, bool maskTopActive = false)
        //{
        //    if(type == PopupOverlayType.Mask)
        //    {
        //        this.SetMaskActive(value, maskTopActive);
        //        this.SetBlurActive(false);
        //    }
        //    else if(type == PopupOverlayType.Blur)
        //    {
        //        this.SetMaskActive(false);
        //        this.SetBlurActive(value);
        //    }
        //    else if(type == PopupOverlayType.None)
        //    {
        //        this.SetMaskActive(false);
        //        this.SetBlurActive(false);
        //    }
        //}

        //private void SetMaskActive(bool value, bool topActive = false)
        //{
        //    if(this.mask != null) this.mask.SetActive(value && !this.DisableMasking && AnyPopup);
            

        //    if (this.decoratedController && !topActive)
        //    {
        //        this.decoratedController.SetMaskActive(!value && this.decoratedController.AnyPopup , value);
        //    }
        //}

        //private void SetBlurActive(bool value)
        //{
        //    if (value && !this.DisableMasking)
        //    {
        //        this.SetBlur(this.isScreenShot);
        //    }
        //    else
        //    {
        //        this.DisabledBlurEffect();
        //    }
        //}

        #region 模糊效果

        //rt数据
        //private RenderTexture blur_bg_rt;

        ///// <summary>
        ///// 相机模糊效果
        ///// </summary>
        //private ScreenBlurEffect ui_blur_effect;

        ///// <summary>
        ///// 是否截屏式模糊
        ///// </summary>
        //private bool isScreenShot = true;

        /// <summary>
        /// 设置模糊效果
        /// </summary>
        /// <param name="isScreenShot">是否采用截屏式</param>
//        private void SetBlur(bool isScreenShot = true)
//        {
//#if !UNITY_WEBGL
//            // 构造默认的模糊数据
//            //BlurData blur_data = new BlurData();
//            //blur_data.blur_spread = 1;
//            //blur_data.blur_iteration = 4;
//            //blur_data.blur_size = 1;
//            //blur_data.blur_down_sample = 4;
//            BlurData blur_data = null;
//            // 截屏式的模糊
//            if (isScreenShot)
//            {
//                Action<RenderTexture> action = this.SetBlurImage;
//                if(this.ui_blur_effect != null) this.ui_blur_effect.EnableBlurRender(BlurType.ScreenShot, blur_data, action);
//            }
//            // 实时模糊效果
//            else 
//            {
//                if (this.ui_blur_effect != null) this.ui_blur_effect.EnableBlurRender(BlurType.Normal, blur_data);
//            }
//#endif
//        }

//        void SetBlurImage(RenderTexture rt)
//        {
//            if (this.blur != null)
//            {
//                this.blur_bg_rt = rt;
//                this.blurImg.texture = this.blur_bg_rt;
//                this.blur.SetActive(true);
//            }
//            else
//            {
//                RenderTexture.ReleaseTemporary(rt);
//            }
//        }

        /// <summary>
        /// 注销回收
        /// </summary>
        //public void DisabledBlurEffect()
        //{
        //    if (this.blur_bg_rt != null)
        //    {
        //        RenderTexture.ReleaseTemporary(this.blur_bg_rt);
        //        this.blur_bg_rt = null;
        //    } 
        //    else if (!this.isScreenShot)
        //    {
        //        this.ui_blur_effect.DisabledBlurRender();
        //    }

        //    if(this.blur && this.blur.gameObject.activeSelf) this.blur.SetActive(false);
        //}

        #endregion

        /// <summary>
        /// 引导关闭Mask 好像生效后也会被激活(加个标记）
        /// </summary>
        //bool DisableMasking = false;
        /// <summary>
        /// 引导关闭Mask 好像生效后也会被激活
        /// </summary>
        //public void DisableMask() 
        //{
        //    this.DisableMasking = true;
        //    this.mask.SetActive(false);
        //    this.blur.SetActive(false);
        //}

        //public void EnabelMask()
        //{
        //    this.DisableMasking = false;
        //}

        private void OnMaskClick(GameObject go, PointerEventData data)
        {
            this.PopBack(false);
        }

        private Context CreateContext(Type classType, object data)
        {
            var index = this.keyCounter++;
            var key = $"{classType.ToString()}-{Time.time}-{index}";
            return new Context(classType, data, key, this.sortingLayerID);
        }

        private void CallNextTick(Action callback)
        {
            this.tickCallbackQueue.Add(callback);
        }

        #region 获取当前界面堆栈信息

        /// <summary>
        /// 获取当前堆栈中的ObjectName
        /// </summary>
        /// <returns></returns>
        public string GetStackUIObjectName()
        {
            if (this.AnyPopup)
            {
                var head = this.PopupStack[this.PopupStack.Count - 1];
                if (head.WillShow == 0 && this._popupCache.PopupPool.TryGetValue(head.ClassType, out var popup))
                {
                    if (popup != null)
                        return popup.objName;
                }
            }

            return null;
        }


        #endregion

    }

    [Serializable]
    public class Context
    {
        public string className;

        public readonly Type ClassType;
        public readonly object Data;

        public readonly string Key;
        public readonly int SortingLayerID;

        internal float WillShow;
        internal float WillHide;
        internal bool Exposure;

        internal string isolatorKey;

        public Context(Type classType, object data, string key, int sortingLayerID)
        {
            this.className = classType.Name;

            this.ClassType = classType;
            this.Data = data;
            this.Key = key;
            this.SortingLayerID = sortingLayerID;
        }
    }

    internal class StackContext
    {
        public readonly List<Context> Stack;
        public readonly Context Context;
        public readonly Action<bool> Callback;

        public StackContext(List<Context> stack, Context context, Action<bool> callback)
        {
            this.Stack = stack;
            this.Context = context;
            this.Callback = callback;
        }
    }
}
