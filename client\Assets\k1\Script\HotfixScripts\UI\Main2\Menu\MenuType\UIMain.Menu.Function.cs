﻿using System.Threading.Tasks;
using Game.Config;
using Game.Data;
using UI.Utils;

namespace UI
{

    /// <summary>
    /// 主界面菜单栏功能
    /// </summary>
    public partial class UIMain2
    {

        #region 菜单栏相关数据刷新

        /// <summary>
        /// 主界面菜单栏操作功能
        /// </summary>
        //private UIMainMenuFunctionBase[] menuFunctionArr;

        /// <summary>
        /// 初始化菜单栏功能
        /// </summary>
        //private void InitMenuFunction()
        //{
        //    if (mainMenuData != null && mainMenuData.menuArr != null)
        //    {
        //        menuFunctionArr = new UIMainMenuFunctionBase[mainMenuData.menuArr.Length];
        //        for (int i = 0; i < mainMenuData.menuArr.Length; i++)
        //        {
        //            menuFunctionArr[(int)(mainMenuData.menuArr[i].type) - 1] = CreateMenuFunctionType(mainMenuData.menuArr[i]);
        //        }
        //    }
        //}

        /// <summary>
        /// 反向初始化菜单栏功能
        /// </summary>
        //private void UnInitMenuFunction()
        //{
        //    if (menuFunctionArr != null)
        //    {
        //        for (int i = 0; i < menuFunctionArr.Length; i++)
        //        {
        //            if (menuFunctionArr[i] != null)
        //            {
        //                menuFunctionArr[i].Clear();
        //                menuFunctionArr[i] = null;
        //            }
        //        }

        //        menuFunctionArr = null;
        //    }
        //}


        /// <summary>
        /// 创建主界面菜单类型数据
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        //private UIMainMenuFunctionBase CreateMenuFunctionType(UIMainMenuData.MenuData data)
        //{
        //    if (data == null
        //        || data.type == MainMenuType.NONE)
        //        return null;

        //    switch (data.type)
        //    {
        //        //case MainMenuType.PRISON:
        //        //    return new UIMainMenuCity(data.type, data.obj, this);
        //        //case MainMenuType.HERO:
        //        //    return new UIMainMenuHero(data.type, data.obj, this);
        //        case MainMenuType.CITY:
        //            return new UIMainMenuMain(data.type, data.obj, this);
        //        //case MainMenuType.ALLIANCE:
        //        //    return new UIMainMenuAlliance(data.type, data.obj, this);
        //        case MainMenuType.WORLD:
        //            return new UIMainMenuWorld(data.type, data.obj, this);
        //        default:
        //            return null;
        //    }
        //}


        /// <summary>
        /// 获取菜单功能类型
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        //private UIMainMenuFunctionBase GetMenuFunctionType(MainMenuType type)
        //{
        //    if (menuFunctionArr == null
        //        || type == MainMenuType.NONE)
        //        return null;

        //    var indexId = (int)type;
        //    if (menuFunctionArr.Length > indexId - 1)
        //    {
        //        return menuFunctionArr[indexId - 1];
        //    }

        //    return null;
        //}

        /// <summary>
        /// 获取菜单栏信息数据
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        //public UIMainMenuData.MenuData GetMenuData(MainMenuType type)
        //{
        //    if (type == MainMenuType.NONE)
        //        return null;

        //    if (mainMenuData != null && mainMenuData.menuArr != null)
        //    {
        //        for (int i = 0; i < mainMenuData.menuArr.Length; i++)
        //        {
        //            if (mainMenuData.menuArr[i].type == type)
        //                return mainMenuData.menuArr[i];
        //        }
        //    }

        //    return null;
        //}

        /// <summary>
        /// 刷新菜单开放状态
        /// </summary>
        private void UpdateMenuOpenState()
        {
            //按钮是否显示
            var isOpen = MainFunctionOpenUtils.OpenMenuBtnsState;
            var trainId = (int)MainMenuType.CITY - 1;

            //if (menuBgObj != null && menuBgObj.activeSelf != isOpen)
            //    menuBgObj.SetActive(isOpen);

            //if (mainMenuData != null && mainMenuData.menuArr != null)
            //{
            //    for (int i = 0; i < mainMenuData.menuArr.Length; i++)
            //    {
            //        if (i != trainId)
            //        {
            //            mainMenuData.menuArr[i].obj?.SetActive(isOpen);
            //            if (isOpen)
            //            {
            //                var index = (int)mainMenuData.menuArr[i].type - 1;
            //                if (menuFunctionArr != null && menuFunctionArr[index] != null)
            //                    menuFunctionArr[index].UpdateMenuOpenState();
            //            }
            //        }
            //    }
            //}
        }

        /// <summary>
        /// 解锁菜单
        /// </summary>
        /// <param name="type"></param>
        private void UnLockMenu(MainMenuType type)
        {
            //if (type == MainMenuType.NONE)
            //    return;

            //var index = (int)type - 1;
            //if (menuFunctionArr != null
            //    && menuFunctionArr.Length > index
            //    && index >= 0)
            //{
            //    menuFunctionArr[index].UnLockMenu();
            //}
        }

        #region 具体菜单页签数据刷新

        /// <summary>
        /// 刷新内城红点显示信息数据
        /// </summary>
        private void UpdateCityRedInfo()
        {
            //临时修 必须延迟执行 不然ad数据说没过来就刷新了红点数导致显示数量不正确 todo
            //TFW.NTimer.CountDown(0.3f,()=>{
            //    var index = (int)MainMenuType.PRISON - 1;
            //    if (menuFunctionArr != null
            //        && menuFunctionArr.Length > index
            //        && index >= 0)
            //    {
            //        var city = menuFunctionArr[index] as UIMainMenuCity;
            //        if (city != null)
            //        {
            //            //city.UpdateCityRedInfo();
            //        }
            //    }
            //});

        }

        /// <summary>
        /// 刷新英雄红点显示信息数据
        /// </summary>
        private void UpdateHeroRedInfo()
        {
            //var index = (int)MainMenuType.HERO - 1;
            //if (menuFunctionArr != null
            //    && menuFunctionArr.Length > index
            //    && index >= 0)
            //{
            //    var hero = menuFunctionArr[index] as UIMainMenuHero;
            //    if (hero != null)
            //    {
            //        hero.UpdateHeroRedInfo().Forget();
            //    }
            //}
        }

        /// <summary>
        /// 刷新当前拥有金币数据
        /// </summary>
        public void UpdateOwnCoinInfo()
        {
            //var index = (int)MainMenuType.CITY - 1;
            //if (menuFunctionArr != null
            //    && menuFunctionArr.Length > index
            //    && index >= 0)
            //{
            //    var city = menuFunctionArr[index] as UIMainMenuTrain;
            //    if (city != null)
            //    {
            //        city.UpdateOwnCoinInfo();
            //    }
            //}
        }

        /// <summary>
        /// 刷新训练金币数据
        /// </summary>
        public void UpdateTrainCoinInfo()
        {
            //var index = (int)MainMenuType.CITY - 1;
            //if (menuFunctionArr != null
            //    && menuFunctionArr.Length > index
            //    && index >= 0)
            //{
            //    var city = menuFunctionArr[index] as UIMainMenuTrain;
            //    if (city != null)
            //    {
            //        city.UpdateTrainCoinInfo();
            //    }
            //}
        }

        /// <summary>
        /// 刷新训练金币数据
        /// </summary>
        public void UpdateTrainCoinActive(bool active)
        {
            //var index = (int)MainMenuType.CITY - 1;
            //if (menuFunctionArr != null
            //    && menuFunctionArr.Length > index
            //    && index >= 0)
            //{
            //    var city = menuFunctionArr[index] as UIMainMenuTrain;
            //    if (city != null)
            //    {
            //        city.UpdateTrainCoinRootActive(active);
            //    }
            //}
        }


        /// <summary>
        /// 刷新联盟红点显示信息数据
        /// </summary>
        private void UpdateAlliacneRedInfo()
        {
            //var index = (int)MainMenuType.ALLIANCE - 1;
            //if (menuFunctionArr != null
            //    && menuFunctionArr.Length > index
            //    && index >= 0)
            //{
            //    var alliacne = menuFunctionArr[index] as UIMainMenuAlliance;
            //    if (alliacne != null)
            //    {
            //        alliacne.UpdateAllianceRedInfo().Forget();
            //    }
            //}
        }

        //public T GetFunction<T>() where T : UIMainMenuFunctionBase
        //{
        //    foreach (var item in menuFunctionArr)
        //    {
        //        if (item is T)
        //            return item as T;
        //    }

        //    return null;
        //}

        /// <summary>
        /// 刷新世界体力信息数据
        /// </summary>
        private void UpdateWorldPhyPowerInfo()
        {
            //var index = (int)MainMenuType.WORLD - 1;
            //if (menuFunctionArr != null
            //    && menuFunctionArr.Length > index
            //    && index >= 0)
            //{
            //    var world = menuFunctionArr[index] as UIMainMenuWorld;
            //    if (world != null)
            //    {
            //        world.UpdatePowerInfo();
            //    }
            //}
        }

        #endregion


        #endregion

    }
}
