﻿using Common;
using DeepUI;
using Game.Config;
using Game.Data;
using K3;
using Logic;
using System.Collections.Generic;
using UI.Utils;
using UnityEngine;

namespace UI
{
    /// <summary>
    /// 分享类型
    /// </summary>
    public enum ShareTypeEnum
    {
        KingDom,
        Alliance,
        Friend,
    }

    public class UIShareData : UIData
    {
        public string chatContent;

        //需要屏蔽分享的内容
        public List<ShareTypeEnum> maskShareList;
    }

    [Popup("Player/UIShare", true)]
    public class UIShare : BasePopup
    {
        [PopupField("Root/BG/Title/CloseBtn")] 
        public GameObject btn_close;

        [PopupField("Root/BG/KindomBtn")]
        public GameObject btnKingdom;

        [Popup<PERSON>ield("Root/BG/AllianceBtn")]
        public GameObject btnAlliance;

        [PopupField("Root/BG/FriendBtn")]
        public GameObject btnFriend;

        private Dictionary<int, GameObject> shareCache;

        private string shareContent;

        protected override void OnInit()
        {
            BindClickListener(btn_close, (x, y) =>
            {
                Close();
            });


            BindClickListener(btnAlliance, (x, y) =>
                        {
                            if (!LPlayer.I.IsPlayerInUnion())//没有加入联盟
                            {
                                //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.ALLIANCE,
                                //    new UIChatData() { CurrentTabKey = GameData.I.MainData.CurrMenuType, CurrChatTab = ChatTabs.Alliance });
                            }
                            else
                            {
                                Close();
                                UIChatHelper.I.ShareToAlliance(shareContent);

                            }
                        });

            BindClickListener(btnKingdom, (x, y) =>
                                    {
                                        if (!MainFunctionOpenUtils.ALllianceOpenState)
                                        {
                                            FloatTips.I.FloatMsg(UnlockMgr.I.GetUnlockDesc(UnlockFuncEnum.Alliance));
                                        } 
                                        else
                                        {
                                            Close();
                                            UIChatHelper.I.ShareToWorld(shareContent);
                                        }

                                    });

            BindClickListener(btnFriend, (x, y) =>
            {
                Close();
                DeepUI.PopupManager.I.ShowPanel<UIShareFriend>(new UIShareFriendData() { chatContent = shareContent });
            });

            shareCache = new Dictionary<int, GameObject>();
            shareCache.Add((int)ShareTypeEnum.KingDom, btnKingdom);
            shareCache.Add((int)ShareTypeEnum.Alliance, btnAlliance);
            shareCache.Add((int)ShareTypeEnum.Friend, btnFriend);
        }


        protected internal override void OnOpenStart()
        {
            var panelData = Data as UIShareData;
            shareContent = panelData.chatContent;

            if(shareCache != null)
            {
                foreach (var obj in shareCache)
                {
                    obj.Value.SetActive(true);
                }
            }


            if(panelData.maskShareList != null)
            {
                var count = panelData.maskShareList.Count;
                for (int i = 0; i < count; i++)
                {
                    if(shareCache.TryGetValue((int)panelData.maskShareList[i], out var obj))
                    {
                        obj.SetActive(false);
                    }
                }
            }
        }


    }
}
