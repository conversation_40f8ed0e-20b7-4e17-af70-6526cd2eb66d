﻿using Common;
using cspb;
using DeepUI;
using Game.Config;
using Game.Data;
using Game.Utils;
using Logic;
using Sirenix.OdinInspector;
using System;
using System.Collections;
using System.Collections.Generic;
using SharedDataStructure;
using TFW.Localization;
using UnityEngine;
using UnityEngine.UI;
using K3;

namespace UI
{
    public class PowerDetailBaseItemUI : MonoBehaviour
    {
        [SerializeField]
        Text _powerName;

        [SerializeField]
        Text _powerValue;

        [InfoBox("以下 UI 元素如不需要可置空")]
        [SerializeField]
        Image _powerIcon;

        [ShowIf(nameof(_powerIcon))]
        [SerializeField]
        string _iconFolder = "GetItem";

        [SerializeField]
        Button _improveButton;

        Cfg.G.CNewRally _data;
        double _power;

        public void SetData(Cfg.G.CNewRally data, double power)
        {
            this._data = data;
            this._power = power;
            this.Refresh();
        }

        void Refresh()
        {
            if (this.isActiveAndEnabled && this._data != null)
            {
                if (this._powerIcon != null)
                {
                    UITools.SetImage(_powerIcon, this._data.Iconid, this._iconFolder);
                }

                _powerName.text = LocalizationMgr.Get(this._data.Powername);
                _powerValue.text = UIStringUtils.FormatIntegerByLanguage((long)this._power);

                //1=拆分 0=不拆分
                //if (this._improveButton != null)
                //{
                //    this._improveButton.gameObject.SetActive(this._data.Split == 0);

                //}
                //if (this._tipButton != null)
                //{
                //    this._tipButton.gameObject.SetActive(this._data.Split == 1);
                //}
            }
        }

        async void OnImproveClick()
        {
            if (this._data == null)
            {
                return;
            }

            var popup = PopupManager.I.FindPopup<TroopPowerUI.Layer>();
            var troopPowerInfo = popup != null ? popup.GameObject.GetComponent<TroopPowerUI>() : null;
            int soldierType = troopPowerInfo?._heroData?.HeroCfg?.SoldiersType ?? 1;

            List<Type> panelList = new List<Type>()
                {
                    typeof(TroopPowerUI.Layer),
                    typeof(UIMapTroopInfoPanel),
                    typeof(UIPlayerInfo_K1),
                    typeof(UIPlayerMain),
                    typeof(UIAllianceWarBuilding),
                    typeof(UIAllianceWarCollect),
                };

            PopupManager.I.ClosePopupList(panelList);
            UIGuidData guidData;

            var powerType = this._data.powerType;
            switch (powerType)
            {
                case PowerType.SoliderBase:
                    //var cfg = await Cfg.C.CD2CityBuilding.GetConfigAsync((int)MainCityItem.CityType.Main);
                    //if (!cfg.GetIsOpen())
                    //{
                    //    return;
                    //}
                    //PopupManager.I.ShowLayer<UIMainCityLevelUp>();

                    //guidData = new UIGuidData();
                    //guidData.guidItems.Add(new UIGuidItem() { UIName = "UI.UIMainCityLevelUp", UIItem = "Root/Button", slide = true });
                    //UIGuid.StartGuid(guidData, true);

                    break;
                case PowerType.Tech:
                    //var cfg1 = await Cfg.C.CD2CityBuilding.GetConfigAsync((int)MainCityItem.CityType.College);
                    //if (!cfg1.GetIsOpen())
                    //{
                    //    return;
                    //}
                    //PopupManager.I.ShowLayer<UITech>();

                    break;
                case PowerType.SoliderEvo:
                    //var cfg2 = await Cfg.C.CD2CityBuilding.GetConfigAsync((int)MainCityItem.CityType.Barracks);
                    //if (!cfg2.GetIsOpen())
                    //{
                    //    return;
                    //}
                    //PopupManager.I.ShowLayer<UIEvo>();
                    ////if (LTowerDefenseType.I.isNTD)
                    ////    PopupManager.I.ShowLayer<UIEvoNew>();
                    ////else
                    ////    PopupManager.I.ShowLayer<UIEvo>();

                    //guidData = new UIGuidData();
                    //guidData.guidItems.Add(new UIGuidItem() { UIName = "UI.UIEvo", UIItem = $"Root/SoldierGrid/{soldierType}/UpgradeBtn", slide = true });
                    //UIGuid.StartGuid(guidData, true);

                    break;
                case PowerType.SoldierTalent:
                    //int lv = 1;
                    //if (soldierType == 1)
                    //    lv = LEvolution.I.ArcherData.EvoLevel;
                    //else if (soldierType == 2)
                    //    lv = LEvolution.I.FireData.EvoLevel;
                    //else if (soldierType == 3)
                    //    lv = LEvolution.I.IceData.EvoLevel;
                    //else if (soldierType == 4)
                    //    lv = LEvolution.I.ToxData.EvoLevel;

                    //var evoData = TDUtils.NewEvolutionData(new EvoInfo() { evoLv = lv, exp = 0, soldierType = soldierType });
                    //TalentMgr.I.OpenTalentPanel(evoData, true);
                    break;
                case PowerType.HeroBase:
                    TaskUtils.JumpToHero(TaskUtils.HeroToDO.LevelUp);
                    break;
                case PowerType.HeroStar:
                case PowerType.HeroSkill:
                    TaskUtils.JumpToHero(TaskUtils.HeroToDO.StarUp);
                    break;
                case PowerType.Fetter:
                    if (LPlayer.I.GetMainCityLevel() < MetaConfig.OpenFetters)
                    {
                        UITools.PopTips(LocalizationMgr.Format("Unlock_level", MetaConfig.OpenFetters));
                        return;
                    }
                    TaskUtils.JumpToHero(TaskUtils.HeroToDO.Fellter);
                    break;
                case PowerType.DragonTotal:
                case PowerType.DragonRune:
                    if (!UnlockMgr.I.CheckUnlock(UnlockFuncEnum.Building_8))
                    {
                        UITools.PopTips(UnlockMgr.I.GetUnlockDesc(UnlockFuncEnum.Building_8));
                        return;
                    }
                    PopupManager.I.ShowLayer<UIDragon>();

                    guidData = new UIGuidData();
                    guidData.guidItems.Add(new UIGuidItem() { UIName = "UI.UIDragon", UIItem = "Root/btn_upgrade", slide = true });
                    UIGuid.StartGuid(guidData, true);
                    break;
                case PowerType.DragonEquipment:
                    if (DragonEquipmentMgr.I.unlocked)
                    {
                        PopupManager.I.ShowLayer<UIDragonEquipment>();
                    }
                    else
                    {
                        UITools.PopTips(LocalizationMgr.Format("Open_chapter_equip", MetaConfig.Drangon_Equipment_open_chapter));
                    }
                    break;
                case PowerType.AllianceBuilding:
                    //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.ALLIANCE);
                    break;
                case PowerType.Skin:
                    PopupManager.I.ShowLayer<UIShopSkin>();
                    break;
                case PowerType.Vip:
                    VipManager.I.OpenVip();
                    break;
                case PowerType.WitchMagic:
                case PowerType.HeroEquipBuild:
                    // HeroEquipmentUI.Layer.OpenSafely();
                    break;
                case PowerType.HeroEquip:
                    // HeroEquipmentUI.Layer.OpenSafely();
                    break;
                case PowerType.WitchBase:
                    //WitchsBrewUI.Layer.OpenSafely();
                    break;
                case PowerType.WitchStone:
                    //WitchStoneUI.Layer.OpenSafely();
                    break;
                case PowerType.SummonAltar:
                    SummonAltarMgr.I.ReqPowerData();
                    break;
                case PowerType.Atlas:
                    CollectAtlasTaskUI.Layer.OpenSafely();
                    break;
                case PowerType.Buff:
                    DeepUI.PopupManager.I.ShowWidget<UIPlayerNewBuffs>();
                    break;
                default:
                    break;
            }
        }

        private void Awake()
        {
            if (this._improveButton != null)
            {
                this._improveButton.onClick.AddListener(this.OnImproveClick);
            }
        }

        private void OnEnable()
        {
            this.Refresh();
        }

        private void OnDestroy()
        {
            if (this._improveButton != null)
            {
                this._improveButton.onClick.RemoveListener(this.OnImproveClick);
            }
        }
    }
}