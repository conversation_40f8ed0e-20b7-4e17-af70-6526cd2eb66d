﻿ 
using Common;
using Config;
using cspb;
using Game.Data;
using Game.Map;
using Public;
using System;
using System.Collections.Generic;
using TFW.Localization;
using TFW.UI;
using UI;
using UnityEngine;
using UnityEngine.EventSystems;
using DeepUI;
using TFW;
using static UIHeroList;

/// <summary>
/// 英雄列表UI界面
/// <AUTHOR>
/// @data 2020/5/8
/// @ver 1.0
/// </summary>
/// 
public class UIHeroListWindowData : PopupData
{
    public bool isMain;
    public bool isHeroList;
    /// <summary>
    /// 要添加英雄的部队的索引
    /// </summary>
    public int LineUpId = -1;

    /// <summary>
    /// 副将位置上阵
    /// </summary>
    public int Pos;

    public K3.SpecialBoxData mBox;
    public int mBoxHeroIndex;

    /// <summary>
    /// 上阵英雄回调
    /// </summary>
    public Action UpHeroCallBack;
    /// <summary>
    /// 普通关闭回调
    /// </summary>
    public Action CloseUICallBack;

    /// <summary>
    /// 对 Item 进行过滤
    /// </summary>
    public Predicate<Game.Data.HeroData> ItemShouldShow;
    public int mHeroIndex;
    public bool isHelpHero;
    public int mHelpHeroIndex;
}

[Popup("Hero/UIHeroWindow")]
public partial class UIHeroListWindow : HistoricPopupLayer
{

    public override bool LFullScreen => true;


    #region UI关联数据
    /// <summary>
    /// 英雄列表Title
    /// </summary>
    private GameObject _listTitle;

    /// <summary>
    /// 关闭按钮
    /// </summary>
    private GameObject _closeBtn;

    /// <summary>
    /// 顶部资源条
    /// </summary>
    private TopResourceWidget _topResBar;

    private GameObject _heroContent;

    //无限滚动
    public TFWLoopListView loopListView;

    public Transform SelectObj;
    public Transform LineUpSelectObj;

    private GameObject heroItem_main;
    private GameObject heroItem_1;
    private GameObject heroItem_2;

    private GameObject GoToInfoBtn;
    private GameObject LineUpPutOnBtn;
    private GameObject LineUpPutOffBtn;

    // /// <summary>
    // /// 新加锁得英雄配置id
    // /// </summary>
    // private List<int> _lockConfigId;
    // /// <summary>
    // /// 新解锁的英雄的配置id
    // /// </summary>
    // private List<int> _unLockConfigId;
    // /// <summary>
    // /// 新加锁的英雄的唯一id
    // /// </summary>
    // private List<long> _lockHeroId;
    // /// <summary>
    // /// 新解锁的英雄的唯一id
    // /// </summary>
    // private List<long> _unLockHeroId;
    ///// <summary>
    ///// 是否上阵英雄
    ///// </summary>
    //private bool _isInBattleHero;
    /// <summary>
    /// 要上阵的部队的id
    /// </summary>
    public int _InBattleHeroLintUpId, _InBattleHeroLineUpPos;

    private K3.SpecialBoxData mBox; private int mBoxHeroIndex;

    private bool isMain = false;
    private bool isHeroList = false;
    private bool isHelpHero = false; private int mHelpHeroIndex;
    private Action SetHeroCallBack, CloseUICallBack;

    /// <summary>
    /// 是否选中所有
    /// </summary>
    private bool _isSelectedAll;
    /// <summary>
    /// 选择被吃的卡的英雄列表
    /// </summary>
    private List<long> _LevelUpHeroLst;
    /// <summary>
    /// 临时用于存储同样英雄的id
    /// </summary>
    private List<long> _LevelUpHeroLstTmp;


    /// <summary>
    /// UIHeroList窗口上阵英雄部分
    /// </summary>
    private GameObject _UIHero_InBattle;
    /// <summary>
    /// 存储英雄item的字典
    /// </summary>
    //private Dictionary<GameObject, UIHero_Item> _UIHeroItemsDic;
    /// <summary>
    /// 存储英雄item的集合
    /// </summary>
    private List<UIHeroListItem> UIHeroItemsLst = new List<UIHeroListItem>();
    ///// <summary>
    ///// 当前选中的HreoItem
    ///// </summary>
    //private UIHero_Item _selectedHeroItem;

    /// <summary>
    /// 英雄面板下面的金币按钮
    /// </summary>
    private GameObject _ButtomPanel;
    /// <summary>
    /// 玩家的总金币
    /// </summary>
    private TFWText _totalGoldText;
    /// <summary>
    /// 升级英雄要花费的金币
    /// </summary>
    private TFWText _costGoldText;

    /// <summary>
    /// 没有选中英雄的提示
    /// </summary>
    private TFWText _noHeroTipText;

    //private List<UIHero_Item> _LevelUpCostHero;

    /// <summary>没有英雄的跳转button</summary>
    private GameObject _noneButton;

    /// <summary>没有英雄的提示文本</summary>
    private TFWText _noneText;

    private GameObject _heroMainBtn;

    /// <summary>
    /// none根节点
    /// </summary>
    private GameObject _noneRoot;

    /// <summary>
    /// 回复战力按钮
    /// </summary>
    private GameObject _rePowerBtn;

    public bool LockState { get; private set; }
    /// <summary>
    /// 锁界面的开关
    /// </summary>
    private GameObject _lockBtn;

    private cspb.Lineup mLineup;
    //在阵容上的
    private long curLineUpSelectHeroId;
    private int curLineUpSelectHeroIndex;
    //不在阵容上的
    private long curSelectHeroId;

    #endregion

    #region override methods
    protected override void OnInit()
    {
        base.OnInit();
    }

    protected override void OnLoad()
    {
        _LevelUpHeroLst = new List<long>();
        _LevelUpHeroLstTmp = new List<long>();
        //嵌套无限滚动
        loopListView = GetComponent<TFWLoopListView>("Root/Content/Scroll");
        _heroContent = GetChild("Root/Content/Scroll/ViewPort/Content");
        _closeBtn = GetChild("Root/Title/CloseBtn");
        _ButtomPanel = GetChild("Root/Buttom");
        SelectObj = GetChild("Root/Content/Select").transform;
        LineUpSelectObj = GetChild("Root/Top/Select").transform;

        heroItem_main = GetChild("Root/Top/HeroItem_main");
        heroItem_1 = GetChild("Root/Top/HeroItem_1");
        heroItem_2 = GetChild("Root/Top/HeroItem_2");
        GoToInfoBtn = GetChild("Root/Buttom/GO");
        LineUpPutOnBtn = GetChild("Root/Buttom/PutOn");
        LineUpPutOffBtn = GetChild("Root/Buttom/PutOff");
        
        //none tips
        _noneRoot = GetChild("Root/Content/NoneTip");
        //_noneButton = UIHelper.GetChild(_noneRoot, "button");
        //_noneButton = UIMain.I?.HeroGoToShopBtn;
        //_heroMainBtn = UIMain.I?.HeroBtn;
        //if (_noneButton == null) Debug.LogFormat("_noneButton == null");
        _noneText = UIHelper.GetComponent<TFWText>(_noneRoot, "text");

        //RemoveListener(TFW.EventTriggerType.Click, _diamondUpgradeBtn);
        AddListener(TFW.EventTriggerType.Click, _closeBtn, CloseUI);
        AddListener(TFW.EventTriggerType.Click, GetChild("Root/Top/Btn_main"), (x,y) =>
        {
            SelectLineUpHero(0);
        });
        AddListener(TFW.EventTriggerType.Click, GetChild("Root/Top/Btn_1"), (x,y) =>
        {
            SelectLineUpHero(1);
        });
        AddListener(TFW.EventTriggerType.Click, GetChild("Root/Top/Btn_2"), (x,y) =>
        {
            SelectLineUpHero(2);
        });

        AddListener(TFW.EventTriggerType.Click, LineUpPutOnBtn, (x,y) =>
        {
            LineUpBuild(true);
        });

        AddListener(TFW.EventTriggerType.Click, LineUpPutOffBtn, (x,y) =>
        {
            LineUpBuild(false);
        });

        AddListener(TFW.EventTriggerType.Click, GoToInfoBtn, (x,y) =>
        {
            if (curSelectHeroId == -1)
            {
               return;
            }
            
            var popData = new UIHeroShowViewData()
            {
                mainHeroData = HeroGameData.I.GetHeroById(curSelectHeroId)
            };
            // PopupManager.I.ShowLayer<MyHeroShowView>(popData);
            //PopupManager.I.ShowDialog<MyHeroShowViewNew>(popData);
        });


        // _UIHero_InBattle = GetChild("Root/TopInfo");
        // _rePowerBtn = GetChild("Root/TopInfo/ChangeHeroPanel/RePowerBtn");
        MessageMgr.RegisterMsg<LineupBuildHeroAck>(this, OnLineupBuildHeroAck);
        MessageMgr.RegisterMsg<DeputyHeroAck>(this, OnDeputyHeroAck);
        MessageMgr.RegisterMsg<LineupBuildRemoveAck>(this, OnLineupBuildRemoveAck);
    }

    
    protected override void OnHidden()
    {

        LockState = false;
       
        base.OnHidden();

        UnInitEvent();

        //if(_noneButton!=null)
        //_noneButton.SetActive(false);

        SetMainButtonVisible(true);

        //if(_diamondUpgradeBtn!=null)
        //_diamondUpgradeBtn.SetActive(false);

        //_isInBattleHero = false;
    }

    protected override void OnDestroyed()
    {
        MessageMgr.UnregisterMsg(this);

        if (_LevelUpHeroLstTmp != null)
        {
            _LevelUpHeroLstTmp.Clear();
            _LevelUpHeroLstTmp = null;
        }

        if (_LevelUpHeroLst != null)
        {
            _LevelUpHeroLst.Clear();
            _LevelUpHeroLst = null;
        }

    }

    protected override void OnShown()
    {
        base.OnShown();

        _InBattleHeroLintUpId = -1;
        _InBattleHeroLineUpPos = 0;
        
        curLineUpSelectHeroIndex = 0;
        curLineUpSelectHeroId = -1;
        curSelectHeroId = -1;
        
        mBox = null;
        isMain = false;

        if (InitData != null)
        {
            var heroListData = InitData as UIHeroListWindowData;
            if (heroListData != null)
            {
                _InBattleHeroLintUpId = heroListData.LineUpId;
                //  Debug.LogErrorFormat($"ToLineUpID:{_InBattleHeroLintUpId}   _InBattleHeroLineUpPos:{heroListData.Pos}");


                SetHeroCallBack = heroListData.UpHeroCallBack;
                CloseUICallBack = heroListData.CloseUICallBack;
                _InBattleHeroLineUpPos = heroListData.Pos;
                curLineUpSelectHeroIndex = heroListData.Pos;
                mBox = heroListData.mBox;
                isHeroList = heroListData.isHeroList;
                isMain = heroListData.isMain;
                mBoxHeroIndex = heroListData.mBoxHeroIndex;
                isHelpHero = heroListData.isHelpHero;
            }
            else
            {
                SetHeroCallBack = null;
                CloseUICallBack = null;
            }
        }
        else
        {
            SetHeroCallBack = null;
            CloseUICallBack = null;
        }

        InitEvent();
        RefreshLineUp();
        ShowHeroList();
        RefreshBottomBtn();
    }

    protected internal override void OnShowStart()
    {
        base.OnShowStart();

        OnShown();
    }


    /// <summary>
    /// 初始化事件监听
    /// </summary>
    private void InitEvent()
    {
        EventMgr.RegisterEvent(TEventType.RefreshHeroData, OnRefreshHeroList, gameObject);
    }


    /// <summary>
    /// 移除事件监听
    /// </summary>
    private void UnInitEvent()
    {
        EventMgr.UnregisterEvent(gameObject);
    }

    #endregion
    /// <summary>
    /// 界面关闭的方法
    /// </summary>
    private void CloseUI(GameObject arg0, PointerEventData arg1)
    {
        curLineUpSelectHeroIndex = -1;
        curLineUpSelectHeroId = -1;
        curSelectHeroId = -1;
        SelectObj.gameObject.SetActive(false);
        LineUpSelectObj.gameObject.SetActive(false);
        // if (LockState)
        // {
        //     SendHeroLockInfo();
        // }
        CloseUICallBack?.Invoke();
        Close();
    }

    /// <summary>
    /// 刷新英雄数据
    /// </summary>
    private void OnRefreshHeroList(object[] obj)
    {
        //_UIHeroItemsDic.Clear();
        OnShown();
    }

    /// <summary>
    /// 设置主UI上英雄按钮的显示处理
    /// </summary>
    private void SetMainButtonVisible(bool isShow)
    {
        if (_heroMainBtn == null)
            return;

        var anim = _heroMainBtn.GetComponent<Animation>();
        if (anim != null)
            anim.enabled = isShow;

        var tran = _heroMainBtn.GetComponent<Transform>();
        //if (tran == null)
        //    return;

        _heroMainBtn.GetComponent<Animation>().enabled = isShow;
        //Transform tran = _heroMainBtn.GetComponent<Transform>();
        Vector3 off = Vector3.one;
        if (!isShow)
            off = new Vector3(.01f, .01f, .01f);

        tran.localScale = off;
        var child = tran.GetChild(0).transform;
        child.localScale = off;
        child = tran.GetChild(1).transform;
        child.localScale = off;
    }


    /// <summary>
    /// 对子物体排序
    /// </summary>
    public Transform[] GetTransforms(GameObject parentGameObject)
    {
        if (parentGameObject != null)
        {
            List<UnityEngine.Component> components = new List<UnityEngine.Component>();
            for (int i = 0; i < parentGameObject.transform.childCount; i++)
            {
                components.Add(parentGameObject.transform.GetChild(i));
            }
            List<Transform> transforms = components.ConvertAll(c => (Transform)c);

            transforms.Remove(parentGameObject.transform);
            transforms.Sort(delegate (Transform a, Transform b)
            {
                return b.name.CompareTo(a.name);
            });

            return transforms.ToArray();
        }

        return null;
    }


    private void SelectLineUpHero(int index)
    {
        if (index > 0)
        {
            var heroListData = InitData as UIHeroListWindowData;

            if (heroListData != null && heroListData.LineUpId >= 0)
            {
                var curLineUpIndex = heroListData.LineUpId;
                mLineup = null;

                if (GameData.I.LineUpData.lockedList.Count > curLineUpIndex)
                {
                    GameData.I.LineUpData.LineUpDataDic.TryGetValue(GameData.I.LineUpData.lockedList[curLineUpIndex],
                        out mLineup);
                }

                if (curLineUpSelectHeroIndex >= 0)
                {
                    var heroData = GameData.I.LineUpData.GetLineUpHero(mLineup, curLineUpSelectHeroIndex);
                    curLineUpSelectHeroId = heroData?.HeroId ?? -1;
                }

                D.Warning.Log($"LineUp  index:{curLineUpSelectHeroIndex}  HeroId:{curLineUpSelectHeroId}");

                var mainHero = GameData.I.LineUpData.GetLineUpHero(mLineup, 0);
                if (mainHero == null)
                {
                    FloatTips.I.FloatMsg(LocalizationMgr.Format("March_select_hero_tips_03"));
                    curLineUpSelectHeroIndex = 0;
                    return;
                }
            }
        
        }
        curLineUpSelectHeroIndex = index;
        RefreshLineUp();
        ShowHeroList();
        RefreshBottomBtn();
    }
    
    private void LineUpBuild(bool putOn)
    {
        
        var heroListData = InitData as UIHeroListWindowData;

        if (heroListData == null || heroListData.LineUpId < 0)
            return;
        
        var curLineUpIndex = heroListData.LineUpId;
        mLineup = null;

        if (GameData.I.LineUpData.lockedList.Count > curLineUpIndex)
        {
            GameData.I.LineUpData.LineUpDataDic.TryGetValue(GameData.I.LineUpData.lockedList[curLineUpIndex],
                out mLineup);
        }

        if (mLineup == null)
            return;
        
        if (curLineUpSelectHeroIndex >=0)
        {
            var heroData = GameData.I.LineUpData.GetLineUpHero(mLineup, curLineUpSelectHeroIndex);
            curLineUpSelectHeroId = heroData?.HeroId ?? -1;
        }
        else
        {
            curLineUpSelectHeroId = -1;
        }
        D.Warning.Log($"LineUp  index:{curLineUpSelectHeroIndex}  HeroId:{curLineUpSelectHeroId}");
        if (putOn)
        {
            if (curSelectHeroId == -1)
            {
                FloatTips.I.FloatMsg(LocalizationMgr.Format("March_select_hero_tips_03"));
                return;
            }

            
            
            if (curLineUpSelectHeroId == curSelectHeroId)
            {
                D.Warning.Log($"上阵英雄与当前阵容英雄一致  id:{curLineUpSelectHeroId}");
                return;
            }
            
            if (curLineUpSelectHeroIndex == 0)
            {
                var req = new LineupBuildHeroReq();

                req.index = curLineUpIndex;
                req.heroIDs.Add(curSelectHeroId);
                MessageMgr.Send(req);
            }
            else
            {
                MessageMgr.Send(new DeputyHeroReq()
                {
                    heroId = curSelectHeroId,
                    deputyHeroPos = curLineUpSelectHeroIndex,
                    isPutOn = 1,
                    lineupId = mLineup.id
                });
            }
           
        }
        else
        {
            if (curLineUpSelectHeroId == -1)
                return;
            
            if (curLineUpSelectHeroIndex == 0)
            {
                int curLineup = GameData.I.LineUpData.lockedList[curLineUpIndex];
                MessageMgr.Send(new LineupBuildRemoveReq()
                {
                    index = curLineup
                });
            }
            else
            {
                MessageMgr.Send(new DeputyHeroReq()
                {
                    heroId = curLineUpSelectHeroId,
                    deputyHeroPos = curLineUpSelectHeroIndex,
                    isPutOn = 0,
                    lineupId = mLineup.id
                });
            }
        }

    }
    
    

    public TFWLoopListViewItem OnGetItemByIndex(TFWLoopListView listView, int index)
    {
        if (index < 0 || AllHeroItems == null || AllHeroItems.Count <= index)
        {
            return null;
        }

        if (AllHeroItems[index].heroDatas != null && AllHeroItems[index].heroDatas.Count > 0)
        {
            var itemTr = loopListView.NewListViewItem("Item");
            var UIGrid = itemTr.GetComponent<UIGrid>();
            UIGrid.Clear();

            itemTr.gameObject.name = $"Item{index}";

            for (int i = 0; i < AllHeroItems[index].heroDatas.Count; i++)
            {
                var itemData = UIGrid.AddItem<UIHeroListWinItem>();

                itemData.gameObject.name = $"Item{i}";
                
                itemData.InitHero(AllHeroItems[index].heroDatas[i], AllHeroItems[index].troopUIIndex >= 0 && i == 0,
                    (heroId) =>
                    {
                        curSelectHeroId = heroId;
                        SelectObj.gameObject.SetActive(true);
                        SelectObj.SetParent(itemData.gameObject.transform);
                        SelectObj.localPosition = Vector3.zero;
                        SelectObj.localScale = Vector3.one;
                        RefreshBottomBtn();
                    });
                
                if (AllHeroItems[index].heroDatas[i].HeroId == curLineUpSelectHeroId)
                {
                    itemData.RefreshOnLineUp(true);
                }

                if (curSelectHeroId == AllHeroItems[index].heroDatas[i].HeroId)
                {
                    SelectObj.gameObject.SetActive(true);
                    SelectObj.SetParent(itemData.gameObject.transform);
                    SelectObj.localPosition = Vector3.zero;
                    SelectObj.localScale = Vector3.one; 
                }
                // UIHeroItemsLst.Add(itemData);


                // if (LockState)
                // {
                //     itemData.SetSelState();
                // }
            }


            return itemTr;
        }

        return null;
    }


    #region Ack回调

    private void OnLineupBuildRemoveAck(LineupBuildRemoveAck obj)
    {
        // RefreshLineUp();
        // ShowHeroList();
        // RefreshBottomBtn();
        FloatTips.I.FloatMsg(LocalizationMgr.Format("March_select_hero_tips_02"));
        SelectLineUpHero(0);
    }
    
    /// <summary>
    /// 上阵英雄回调
    /// </summary>
    /// <param name="obj"></param>
    private void OnLineupBuildHeroAck(LineupBuildHeroAck ack)
    {
        if (ack.errCode == ErrCode.ErrCodeSuccess)
        {
            FloatTips.I.FloatMsg(LocalizationMgr.Format("March_select_hero_tips_01"));
            // RefreshLineUp();
            // ShowHeroList();
            // RefreshBottomBtn();
            SelectLineUpHero(1);
            
            // Close();
            if (SetHeroCallBack != null)
            {
                SetHeroCallBack?.Invoke();
            }
            else //if(_InBattleHeroLintUpId!=-1)
            {
                if (GameData.I.MainData.CurrMenuType == MainMenuType.HERO)
                {/*
                    WndMgr.Show<UIHeroUpgrade>(new UIHeroUpgradeData
                    {
                        lineUIIndex = GameData.I.LineUpData.lockedList.IndexOf(ack.indexId)   // _InBattleHeroLintUpId
                    });*/
                }
            }
        }
    }

    private void OnDeputyHeroAck(DeputyHeroAck ack)
    {
        if (ack.err == ErrCode.ErrCodeSuccess)
        {
           
            //这个时候是上阵副将完成 当有空余副将位置时选择
            var leftHero = GameData.I.LineUpData.GetLineUpHero(mLineup, 1);
            var rightHero = GameData.I.LineUpData.GetLineUpHero(mLineup, 2);
            if (curLineUpSelectHeroIndex >0)
            {
                if (leftHero != null && curLineUpSelectHeroIndex == 1)
                {
                    FloatTips.I.FloatMsg(LocalizationMgr.Format("March_select_hero_tips_01"));
                }
                else if (leftHero == null && curLineUpSelectHeroIndex == 1)
                {
                    FloatTips.I.FloatMsg(LocalizationMgr.Format("March_select_hero_tips_02"));
                } 
                if (rightHero != null && curLineUpSelectHeroIndex == 2)
                {
                    FloatTips.I.FloatMsg(LocalizationMgr.Format("March_select_hero_tips_01"));
                }
                else if (rightHero == null && curLineUpSelectHeroIndex == 2)
                {
                    FloatTips.I.FloatMsg(LocalizationMgr.Format("March_select_hero_tips_02"));
                }
                
            }
            if (leftHero == null)
            {
                SelectLineUpHero(1);
            }
            else if (rightHero == null)
            {
                SelectLineUpHero(2);
            }
            else
            {
                RefreshLineUp();
                ShowHeroList();
                RefreshBottomBtn();
            }



            // Close();
            if (SetHeroCallBack != null)
            {
                SetHeroCallBack?.Invoke();
            }
            else //if(_InBattleHeroLintUpId!=-1)
            {
                if (GameData.I.MainData.CurrMenuType == MainMenuType.HERO)
                {
                    /*
                    WndMgr.Show<UIHeroUpgrade>(new UIHeroUpgradeData
                    {
                        lineUIIndex = GameData.I.LineUpData.GetUIIndexById(ack.lineupId)   // _InBattleHeroLintUpId
                    });*/
                }
            }
        }
    }

    #endregion

    /// <summary>
    /// 点击界面锁的按钮
    /// </summary>
    /// <param name="arg0"></param>
    /// <param name="arg1"></param>
    private void OperationLockPanel(GameObject arg0, PointerEventData arg1)
    {
        // LockState = !LockState;
        // UIHelper.GetChild(_lockBtn, "Lock")?.SetActive(!LockState);
        // UIHelper.GetChild(_lockBtn, "Unlock")?.SetActive(LockState);
        // if (LockState)
        // {
        //     ShowHeroList();
        // }
        // else
        // {
        //     SendHeroLockInfo();
        // }
    }
    /// <summary>
    ///向服务器发送加锁和解锁的信息
    /// </summary>
    private void SendHeroLockInfo()
    {
        var req = new HeroLockReq();
        foreach (var item in UIHeroItemsLst)
        {
            if (item.SelToLock() != HeroGameData.I.GetLockedByModelID(item.mHeroData.ModelId))
            {
                if (item.SelToLock())
                {
                    req.lockedCfgIDs.Add(item.mHeroData.ModelId);
                }
                else
                {
                    req.unlockedCfgIDs.Add(item.mHeroData.ModelId);
                }
            }
        }
        if (req.lockedCfgIDs.Count > 0 || req.unlockedCfgIDs.Count > 0)
        {
            MessageMgr.Send(req);
        }

        ShowHeroList();
    }

    private void RefreshBottomBtn()
    {
        if (curLineUpSelectHeroId == -1)
        {
            LineUpPutOffBtn.SetActive(false);
            LineUpPutOnBtn.SetActive(true);
        }
        else
        {
            LineUpPutOffBtn.SetActive(curLineUpSelectHeroId == curSelectHeroId);
            LineUpPutOnBtn.SetActive(curLineUpSelectHeroId != curSelectHeroId);
        }
        
    }

    private void RefreshLineUp()
    {
        curLineUpSelectHeroId = -1;
        
        var heroListData = InitData as UIHeroListWindowData;
        
        if (heroListData != null && heroListData.LineUpId >= 0)
        {
            var curLineUpIndex = heroListData.LineUpId;
            mLineup = null;

            if (GameData.I.LineUpData.lockedList.Count > curLineUpIndex)
            {
                GameData.I.LineUpData.LineUpDataDic.TryGetValue(GameData.I.LineUpData.lockedList[curLineUpIndex],
                    out mLineup);
            }

            if (curLineUpSelectHeroIndex >=0)
            {
                var heroData = GameData.I.LineUpData.GetLineUpHero(mLineup, curLineUpSelectHeroIndex);
                curLineUpSelectHeroId = heroData?.HeroId ?? -1;
            }
            
            D.Warning.Log($"LineUp  index:{curLineUpSelectHeroIndex}  HeroId:{curLineUpSelectHeroId}");
            
            var mainHero = GameData.I.LineUpData.GetLineUpHero(mLineup, 0);
            var leftHero = GameData.I.LineUpData.GetLineUpHero(mLineup, 1);
            var rightHero = GameData.I.LineUpData.GetLineUpHero(mLineup, 2);

            
            if (curLineUpSelectHeroIndex == 0)
            {
                LineUpSelectObj.transform.position = heroItem_main.transform.position;
            }
            else if (curLineUpSelectHeroIndex == 1)
            {
                if (mainHero == null)
                {
                    FloatTips.I.FloatMsg(LocalizationMgr.Format("March_select_hero_tips_03"));
                    return;
                }
                LineUpSelectObj.transform.position = heroItem_1.transform.position;
            }
            else if (curLineUpSelectHeroIndex == 2)
            {
                if (mainHero == null)
                {
                    FloatTips.I.FloatMsg(LocalizationMgr.Format("March_select_hero_tips_03"));
                    return;
                }
                LineUpSelectObj.transform.position = heroItem_2.transform.position;
            }

            LineUpSelectObj.gameObject.SetActive(true);

            if (mainHero != null)
            {
                UIHeroNewUtils.SetTroopSpineData(heroItem_main, mainHero, -1, -1);
                if (leftHero != null)
                {
                    UIHeroNewUtils.SetTroopSpineData(heroItem_1, leftHero, -1, -1);
                }
                else
                {
                    UIHeroNewUtils.SetData(heroItem_1, HeroEmptyEnum.Add);
                }

                if (rightHero != null)
                {
                    UIHeroNewUtils.SetTroopSpineData(heroItem_2, rightHero, -1, -1);
                }
                else
                {
                    UIHeroNewUtils.SetData(heroItem_2, HeroEmptyEnum.Add);
                }
            }
            else
            {
                UIHeroNewUtils.SetData(heroItem_main, HeroEmptyEnum.Add);
                UIHeroNewUtils.SetData(heroItem_1, HeroEmptyEnum.None);
                UIHeroNewUtils.SetData(heroItem_2, HeroEmptyEnum.None);
            }
        }
    }

    // private void RefreshHeroList()
    // {
    //     LockState = false;
    //     UIHelper.GetChild(_lockBtn, "Lock")?.SetActive(!LockState);
    //     UIHelper.GetChild(_lockBtn, "Unlock")?.SetActive(LockState);
    //     ShowHeroList();
    //
    // }

    List<HeroItemData> AllHeroItems;


    private void ShowHeroList()
    {
        UIHeroItemsLst.Clear();

        HeroGameData.I.GetSolidersByNum(4, HeroGameData.HeroListEnum.Have, out AllHeroItems, (InitData as UIHeroListWindowData)?.ItemShouldShow,curLineUpSelectHeroId);
        curSelectHeroId = -1;
        if (AllHeroItems != null && AllHeroItems.Count > 0)
        {
            if (AllHeroItems[0].heroDatas.Count > 0)
            {
                curSelectHeroId = AllHeroItems[0].heroDatas[0].HeroId;
            }
        }
        
        if (loopListView.ListViewInited)
        {
            loopListView.SetListItemCount(AllHeroItems.Count);
            loopListView.RefreshAllShownItemWithFirstIndex(0);
            //loopListView.MovePanelToItemIndex(0, 0);
        }
        else
        {
            loopListView.InitListView(AllHeroItems.Count, OnGetItemByIndex);
        }
        
    }


}



//MODIFIED BY UIBASE2POPUP
//MODIFIED BY UIBASE2POPUP
//MODIFIED BY UIBASE2POPUP
