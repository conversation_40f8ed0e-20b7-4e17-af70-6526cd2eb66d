﻿using System;
using System.Collections.Generic;
using Cfg;
using Common;
using cspb;
using DeepUI;
using DG.Tweening;
using Game.Config;
using Game.Data;
using TFW;
using TFW.Localization;
using TFW.UI;
using Logic;
using Render;
using UnityEngine;
using UnityEngine.UI;
using Sequence = DG.Tweening.Sequence;

namespace UI
{
    [Popup("Technology/UITechView", true)]
    public partial class UITechView
    {
        private TechData mTechData = null;

        private TFWLoopListView mLoopListView;

        private List<UITechStageLoopListData> mLoopListDatas = new List<UITechStageLoopListData>();

        private readonly int UpgradeToolId = 20110000;
        
        private Dictionary<int, ItemData> mItemMap = null;
        
        private List<Asset> mVmAsset = null;

        private UITechViewJumpData mJumpData = null;

        private Sequence mRefreshSeq = null;

        private bool mInit = false;

        protected override void OnInit()
        {
            base.OnInit();
            
            UITechViewData.I.InitDatas();

            BindClickListener(mBtnUpgrade, (_, _) => OnBtnUpgrade());
            
            BindClickListener(mBtnBack, (_, _) => this.Close());

            mLoopListView = mScrollView.GetComponent<TFWLoopListView>();

            mTitle_TFWText.text = LocalizationMgr.Get("City_Academy");
        }

        protected internal override void OnDataReady()
        {
            base.OnDataReady();

            mJumpData = Data as UITechViewJumpData;
            
            Reset();
            
            EventMgr.RegisterEvent(TEventType.OnUpgradeSkillAck, OnTechUpgrade, this);
            EventMgr.RegisterEvent(TEventType.RefreshTech, OnTechUpgrade, this);
            EventMgr.RegisterEvent(TEventType.RefreshAssetAck, OnTechUpgrade, this);

            if (null == mJumpData)
            {
                mJumpData = GetDefaultJumpData();
            }
            
            Refresh();
        }

        private UITechViewJumpData GetDefaultJumpData()
        {
            foreach (var kv in UITechViewData.I.ViewDatas)
            {
                var sub1Data = kv.Value;

                foreach (var sub2Data in sub1Data.GetDatas())
                {
                    foreach (var techDataKv in sub2Data.TechCfgs)
                    {
                        var techData = GameData.I.SkillData.MTechs[techDataKv.Value];
                        if (!UITechViewData.I.IsTechLock(techData) && !techData.MaxLevel)
                        {
                            return new UITechViewJumpData()
                            {
                                TechType = techData.Type,
                            };
                        }
                    }
                }
            }

            return null;
        }

        private void OnTechUpgrade(object[] _)
        {
            PlayerAssetsMgr.I.GetCurAssetAndItem(out mItemMap, out mVmAsset);
            
            RefreshTop();

            mLoopListDatas = UITechViewData.I.GetLoopListDatas();
            
            mLoopListView.SetListItemCount(mLoopListDatas.Count, false, true);
            
            mLoopListView.RefreshAllShownItem();
            
            HideBottom();
        }

        private void Reset()
        {
            // mBottom.SetActive(false);

            UITechViewData.I.ClickSub1ItemCallback = OnSub1ItemClick;
            UITechViewData.I.ClickSub2ItemCallback = OnSub2ItemClick;
            UITechViewData.I.CloseTechViewBottom = HideBottom;
            UITechViewData.I.SelectedTechType = -1;
        }

        public void ShowTech(UITechViewJumpData jumpData)
        {
            if (null == jumpData)
            {
                return;
            }

            mJumpData = jumpData;
            
            GameData.I.SkillData.MTechs.TryGetValue(mJumpData.TechType, out var techData);

            if (null == techData || techData.MConfig.Tab == 0)
            {
                mJumpData = null;
                
                Close();
                
                var ui = PopupManager.I.FindPopup<UIMainCity>();
                if (ui != null)
                {
                    PopupManager.I.ClearAllPopup();
                
                    int cityCfgID = LPlayer.I.GetCityBuildingIDByType(jumpData.TechType);
                    ui.dragImage.MoveTOCfg(cityCfgID, () =>
                    {
                        var guidData = new UIGuidData();
                        guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMainCity", UIItem = $"FullScreenBgCanvas/Root/BG1/Content/Building/build_{jumpData.TechType}" });
                        UIGuid.StartGuid(guidData);
                    });
                }

                return;
            }
            
            UITechViewData.I.OpenTechStage(techData.MConfig.Tab);
            UITechViewData.I.SelectedTechType = mJumpData.TechType;

            mLoopListDatas = UITechViewData.I.GetLoopListDatas();
            
            Refresh();
        }
        
        private void Refresh()
        {
            PlayerAssetsMgr.I.GetCurAssetAndItem(out mItemMap, out mVmAsset);
            
            RefreshTop();
            
            RefreshCenter();
            
            RefreshBottom();
        }

        private async void RefreshTop()
        {
            var itemCfg = await Cfg.C.CItem.GetConfigAsync(UpgradeToolId);
            UITools.SetImageBySpriteName(mResIcon_TFWImage, itemCfg.StandardIcon, false);
            mResNum_TFWText.text = UIStringUtils.FormatIntegerByLanguage(PlayerAssetsMgr.I.GetItemCountByID(UpgradeToolId));
        }

        private void RefreshCenter()
        {
            mLoopListDatas = UITechViewData.I.GetLoopListDatas();
            
            mRefreshSeq?.Kill();

            mRefreshSeq = DOTween.Sequence();

            if (!mInit)
            {
                mRefreshSeq.AppendInterval(0.03f);
                mInit = true;
            }

            mRefreshSeq.AppendCallback(() =>
            {
                LayoutRebuilder.ForceRebuildLayoutImmediate(this.mRoot.transform as RectTransform);
                
                RefreshLoopList();
                
                if (null == mJumpData)
                {
                    return;
                }

                UITechViewData.I.CloseAllTab();

                GameData.I.SkillData.MTechs.TryGetValue(mJumpData.TechType, out var tech);

                if (null == tech)
                {
                    return;
                }

                var tab = tech.MConfig.Tab;
                var type = mJumpData.TechType;

                UITechViewData.I.SelectedTechType = type;
                OnSub1ItemClick(tab);
                OnSub2ItemClick(tech);
            });
            
            mRefreshSeq.AppendInterval(0.01f);

            mRefreshSeq.AppendCallback(() =>
            {
                if (null == mJumpData)
                {
                    return;
                }
                
                var type = mJumpData.TechType;

                var cellIdx = 0;
                var findTech = false;
                
                foreach (var listData in mLoopListDatas)
                {
                    if (null == listData.Sub2Data)
                    {
                        cellIdx++;
                        
                        continue;
                    }

                    foreach (var kv in listData.Sub2Data.TechCfgs)
                    {
                        var techType = kv.Value;

                        if (techType == type)
                        {
                            findTech = true;
                            break;
                        }
                    }

                    if (findTech)
                    {
                        break;
                    }

                    cellIdx++;
                }

                mLoopListView.MovePanelToItemIndex(Math.Max(0, cellIdx - 1), 0);

                mJumpData = null;
            });
        }

        private async void RefreshBottom()
        {
            if (mTechData == null)
            {
                mBottom.SetActive(false);
                return;
            }
            
            UITools.SetImageBySpriteName(mTechIcon_TFWImage, mTechData.MConfig.Icon);
            mTechTitle_TFWText.text = LocalizationMgr.Get(mTechData.MConfig.Name);

            var nextData = mTechData.NextConfig;
            if (nextData != null)
            {
                for (int i = 0; i < mTechData.MConfig.AddBuffProperty.Count; i++)
                {
                    int buffID = int.Parse(mTechData.MConfig.AddBuffProperty[i]);
                    var buff = await Cfg.C.CBuffProperty.GetConfigAsync(buffID);
                    if (buff != null && mTechData.MConfig.AddBuffPropertyValue.Count > i && nextData.AddBuffPropertyValue.Count > i)
                    {
                        var value1 = buff.ValueType == 1 ? UIStringUtils.FormatPercentByLanguage(float.Parse(mTechData.MConfig.AddBuffPropertyValue[i])) : mTechData.MConfig.AddBuffPropertyValue[i].ToString();
                        var value2 = buff.ValueType == 1 ? UIStringUtils.FormatPercentByLanguage(float.Parse(nextData.AddBuffPropertyValue[i])) : nextData.AddBuffPropertyValue[i].ToString();
                        mTechDesc_TFWText.text = LocalizationMgr.Get(mTechData.MConfig.Des);
                        mTechValue1.SetActive(true);
                        mTechValue1_TFWText.text = value1;
                        mAdd.SetActive(true);
                        mTechValue2_TFWText.text = value2;
                        
                        LayoutRebuilder.ForceRebuildLayoutImmediate(mAdd.transform as RectTransform);
                    }
                }
            }
            else
            {
                for (int i = 0; i < mTechData.MConfig.AddBuffProperty.Count; i++)
                {
                    int buffID = int.Parse(mTechData.MConfig.AddBuffProperty[i]);
                    var buff = await Cfg.C.CBuffProperty.GetConfigAsync(buffID);
                    if (buff!=null && mTechData.MConfig.AddBuffPropertyValue.Count>i && mTechData.MConfig.AddBuffPropertyValue.Count>i)
                    {
                        mTechDesc_TFWText.text = LocalizationMgr.Get(mTechData.MConfig.Des);
                        var value2 = buff.ValueType == 1 ? UIStringUtils.FormatPercentByLanguage(float.Parse(mTechData.MConfig.AddBuffPropertyValue[i])) : mTechData.MConfig.AddBuffPropertyValue[i].ToString(); 
                        mTechDesc_TFWText.text = LocalizationMgr.Get(mTechData.MConfig.Des);
                        mTechValue1.SetActive(false);
                        mAdd.SetActive(false);
                        mTechValue2_TFWText.text = value2;
                    }
                }
            }
            
            mCost_UIGrid.Clear();

            foreach (var cost in mTechData.MConfig.UpgradeCost)
            {
                mCost_UIGrid.AddItem<CommonDiscountItem>().InitDiscountData(new CommonItem.CommonItemData(cost), false, 0);
            }

            for (int i = 0; i < mTechData.MConfig.TechCondition.Count; i++)
            {
                var preTechId = mTechData.MConfig.TechCondition[i];
                var preTechCfg = await Cfg.C.CD2Tech.GetConfigAsync(int.Parse(preTechId));

                if (null == preTechCfg)
                {
                    continue;
                }

                if (GameData.I.SkillData.MTechs.TryGetValue(preTechCfg.Type, out var preTech))
                {
                    if (preTech.Level >= preTechCfg.Level)
                    {
                        continue;
                    }
                }
                else
                {
                    continue;
                }
                
                mCost_UIGrid.AddItem<CommonDiscountItem>().InitDiscountData(new CommonItem.CommonItemData()
                {
                    Id = int.Parse(preTechId),
                    Typ = AssetType.Tech,
                    Val = preTechCfg.Level,
                }, false, 0);
            }


            mBtnUpgrade.SetActive(mTechData.NextConfig != null);

            if (UITechViewData.I.CanUpgrade(mTechData))
            {
                mBtnUpgradeTxt_TFWText.text = LocalizationMgr.Get("ActvCommanderStageNmae25");
                UITools.SetImageBySpriteName(mBtnUpgrade_TFWImage, "UI_Common_Btn_Yellow");
            }
            else
            {
                mBtnUpgradeTxt_TFWText.text = LocalizationMgr.Get("Quick_Access_Items");
                UITools.SetImageBySpriteName(mBtnUpgrade_TFWImage, "UI_Common_Btn_Blue");
            }
            
            

            mMax.SetActive(mTechData.MaxLevel);
            mBtnUpgrade.SetActive(!mTechData.MaxLevel);
            mCost.SetActive(!mTechData.MaxLevel);
        }

        private void OnSub1ItemClick(UITechStageSub1Data sub1Data)
        {
            OnSub1ItemClick(sub1Data.TechTab);
        }

        private void OnSub1ItemClick(int techTab)
        {
            UITechViewData.I.OpenTechStage(techTab);

            mTechData = null;
            
            RefreshBottom();

            mLoopListDatas = UITechViewData.I.GetLoopListDatas();
            
            RefreshLoopList();
        }
        
        private void OnSub2ItemClick(TechData techData)
        {
            if (techData == null)
            {
                mTechData = null;
                mBottom.SetActive(false);
                
                return;
            }
            
            if (mTechData != null && mTechData.Type == techData.Type)
            {
                mTechData = null;
                mBottom.SetActive(false);
                
                return;
            }
            
            mTechData = techData;
            mBottom.SetActive(true);
            
            RefreshBottom();
        }

        private void HideBottom()
        {
            UITechViewData.I.SelectedTechType = -1;
            mLoopListView.RefreshAllShownItem();
            mTechData = null;
            mBottom.SetActive(false);
        }

        private void RefreshLoopList()
        {
            if (!mLoopListView.ListViewInited)
            {
                mLoopListView.InitListView(mLoopListDatas.Count, OnGetItemByIndex);
            }
            else
            {
                mLoopListView.SetListItemCount(mLoopListDatas.Count);
                mLoopListView.RefreshAllShownItem();
                mLoopListView.UpdateContentSize();
                    
                var idx = mLoopListDatas.FindIndex(d => d.Sub1Data != null && UITechViewData.I.IsTechTabOpen(d.Sub1Data.TechTab));

                if (idx >= 0)
                {
                    mLoopListView.MovePanelToItemIndex(idx, 0);
                }
            }
        }
        
        private TFWLoopListViewItem OnGetItemByIndex(TFWLoopListView loopListView, int index)
        {
            if (index < 0)
            {
                return null;
            }
            
            var data = mLoopListDatas[index];
            var nextData = index + 1 >= mLoopListDatas.Count ? null : mLoopListDatas[index + 1];
            
            TFWLoopListViewItem item = null;
            
            if (data.Sub1Data != null)
            {
                item = loopListView.NewListViewItem("UITechViewSubItem1");
                
                var subItem = item as UITechViewSub1Item;
                subItem.Init();
                subItem.SetData(data.Sub1Data);
                subItem.GetComponent<UIGridItem>()._scroll = mLoopListView.ScrollRect;
            }
            else if (data.Sub2Data != null)
            {
                item = loopListView.NewListViewItem("UITechViewSubItem2");
                
                var subItem = item as UITechViewSub2Item;
                subItem.Init();
                subItem.SetData(data.Sub2Data);

                if (null != nextData && null != nextData.Sub2Data)
                {
                    subItem.ShowLine(nextData.Sub2Data.GetPosSet());
                }
                
                subItem.GetComponent<UIGridItem>()._scroll = mLoopListView.ScrollRect;
                
            }

            return item;
        }

        private async void OnBtnUpgrade()
        {
            if (mTechData.MaxLevel)
            {
                UITools.PopTips(LocalizationMgr.Get("ERRCODE_uniontechreachmaxlv"));
            }

            mTechData.HaveTop(out bool canUpdate);

            if (!canUpdate)
            {
                if (mTechData.MConfig.TechCondition.Count > 0)
                {
                    //说明本科技需要前置科技
                    for (int i = 0; i < mTechData.MConfig.TechCondition.Count; i++)
                    {
                        var targetTechConfig = await Cfg.C.CD2Tech.GetConfigAsync(int.Parse(mTechData.MConfig.TechCondition[i]));
                        if (targetTechConfig != null)
                        {
                            if (!(GameData.I.SkillData.MTechs.TryGetValue(targetTechConfig.Type, out var techData) &&  techData.State==2  && techData.Level >= targetTechConfig.Level))
                            {
                                if(targetTechConfig.Type==1)
                                {
                                    PopupManager.I.ClosePopup<UITechDetail>(); 
                                    PopupManager.I.ShowPanel<UICityLevelUp>(new UICityBuildLevelUpData() { cityType = (int)MainCityItem.CityType.Castle });
                                }
                                else
                                { 
                                    PopupManager.I.ClosePopup<UITechDetail>();
                                    
                                    FloatTips.I.FloatMsg(LocalizationMgr.Get("Not_Unlocked"));
                                }
                                return;
                            }
                        }
                    }
                }
                 
            }

            if (!string.IsNullOrEmpty(PlayerAssetsMgr.I.HaveAssets(mTechData?.UpgradeCost, mItemMap, mVmAsset)))
            {
                PlayerAssetsMgr.I.HaveAssetsToGO(mTechData?.UpgradeCost);
                return;
            }
  
            if (mTechData?.MaxLevel==true)//升级限制检查判断
            {
                return;
            }
     
            UpgradeSkill();
        }
        
        private void UpgradeSkill()
        {
            PlayerAssetsMgr.I.CalAssets(mTechData.UpgradeCost, mItemMap, mVmAsset, false);
            GameAudio.PlayAudio(AudioConst.SOUND_UI_SKILL_UPGRADE);
            LSkill.I.UpgradeSkill(mTechData);
        }

        protected internal override void OnCloseComplete()
        {
            base.OnCloseComplete();
            
            EventMgr.UnregisterEvent(this);
            
            mRefreshSeq?.Kill();
            mRefreshSeq = null;
        }
    }
}