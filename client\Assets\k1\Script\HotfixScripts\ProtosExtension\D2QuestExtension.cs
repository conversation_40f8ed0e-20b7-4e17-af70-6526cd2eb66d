﻿using System.Collections.Generic;
using TFW.Localization;
using System;
using System.Linq;
using Common;
using Config;
using Cysharp.Threading.Tasks;
using DeepUI;
using Game.Config;
using Game.Data;
using Logic;
using Public;
using UI;
using UI.Alliance;
using K3;
using UI.Utils;

namespace cspb
{
    public static class D2QuestExtension
    {
        public static async UniTask<string> GetDisplayName(this D2Quest obj)
        {
            string ret = string.Empty;

            //章节任务
            if (obj.questType == QuestType.QuestTypeMain)
            {
                var configData = await Cfg.C.CChapterQuest.GetConfigAsync(obj.cfgID);
                switch (configData.QuestType)
                {
                    case 201:
                    case 203:
                    case 301:
                    case 501:
                    case 503:
                        if (configData.Params.Count > 0)
                        {
                            ret = LocalizationMgr.Format(configData.QuestDes, configData.Count, configData.Params[0]);
                        }
                        else
                        {
                            ret = LocalizationMgr.Format(configData.QuestDes, configData.Count);
                        }

                        break;
                    case 403:
                        if (configData.Params.Count > 0)
                        {
                            if (int.TryParse(configData.Params[0], out int parValue))
                            {
                                switch (parValue)
                                {
                                    case 0:
                                        ret = LocalizationMgr.Format(configData.QuestDes, configData.Count,
                                            LocalizationMgr.Get("Soldier_anyone"));
                                        break;
                                    case 1:
                                        ret = LocalizationMgr.Format(configData.QuestDes, configData.Count,
                                            LocalizationMgr.Get("Skill_title_1"));
                                        break;
                                    case 2:
                                        ret = LocalizationMgr.Format(configData.QuestDes, configData.Count,
                                            LocalizationMgr.Get("Skill_title_2"));
                                        break;
                                    case 3:
                                        ret = LocalizationMgr.Format(configData.QuestDes, configData.Count,
                                            LocalizationMgr.Get("Skill_title_3"));
                                        break;
                                    case 4:
                                        ret = LocalizationMgr.Format(configData.QuestDes, configData.Count,
                                            LocalizationMgr.Get("Skill_title_4"));
                                        break;
                                    default:
                                        break;
                                }
                            }
                        }
                        else
                        {
                            ret = LocalizationMgr.Format(configData.QuestDes, configData.Count);
                        }

                        break;
                    case 505:
                    case 702:
                        //采集金币
                        ret = LocalizationMgr.Format(configData.QuestDes,
                            UIStringUtils.ExchangeValue(configData.Count));
                        break;
                    default:
                        ret = LocalizationMgr.Format(configData.QuestDes, configData.Count);
                        break;
                }
            }
            //日常任务
            else if (obj.questType == QuestType.QuestTypeDaily)
            {
                var config = await Cfg.C.CNewQuestCommon.GetConfigAsync(obj.cfgID);
                var colorTagLeft = obj.status < config.Count ? "<color=#f95f5f>" : "<color=#409e16>";
                var colorTagRight = obj.status < config.Count ? "</color>" : "</color>";

                var statusStr = UIStringUtils.FormatIntegerByLanguage(Math.Min(obj.status, config.Count));
                var countStr = UIStringUtils.FormatIntegerByLanguage(config.Count);
                ret = $"({colorTagLeft}{statusStr}{colorTagRight}/{countStr}) " +
                      LocalizationMgr.Format(config.LcDesc, countStr);
            }
            //主、支线任务
            else if (obj.questType == QuestType.QuestTypeNewMain || obj.questType == QuestType.QuestTypeBranch)
            {
                var config = await Cfg.C.CMainQuest.GetConfigAsync(obj.cfgID);
                var colorTagLeft = obj.status < config.Count ? "<color=#f95f5f>" : "<color=#409e16>";
                var colorTagRight = obj.status < config.Count ? "</color>" : "</color>";

                var statusStr = UIStringUtils.FormatIntegerByLanguage(Math.Min(obj.status, config.Count));
                var countStr = UIStringUtils.FormatIntegerByLanguage(config.Count);
                ret = $"({colorTagLeft}{statusStr}{colorTagRight}/{countStr}) " +
                      LocalizationMgr.Format(config.QuestDes, countStr);
            }
            //成就任务
            //else if (obj.questType == QuestType.QuestTypeAchievement)
            //{
            //    var config = await Cfg.C.CAchievementQuest.GetConfigAsync(obj.cfgID);
            //    var colorTagLeft = obj.status < config.Count ? "<color=#f95f5f>" : "<color=#409e16>";
            //    var colorTagRight = obj.status < config.Count ? "</color>" : "</color>";

            //    var statusStr = UIStringUtils.FormatIntegerByLanguage(Math.Min(obj.status, config.Count));
            //    var countStr = UIStringUtils.FormatIntegerByLanguage(config.Count);
            //    var param = config.Params?.FirstOrDefault();
            //    if (string.IsNullOrEmpty(param))
            //    {
            //        ret = $"({colorTagLeft}{statusStr}{colorTagRight}/{countStr}) " +
            //              LocalizationMgr.Format(config.QuestDes, countStr);
            //    }
            //    else
            //    {
            //        ret = $"({colorTagLeft}{statusStr}{colorTagRight}/{countStr}) " +
            //              LocalizationMgr.Format(config.QuestDes, countStr, param);
            //    }
            //}

            if (D2Quest.debugShowTaskCfgId)
            {
                return $"(CfgId:{obj.cfgID},Id:{obj.id}){ret}";
            }
            else
            {
                return ret;
            }
        }

        public static async UniTask<List<Cfg.Cm.CValTypId_isi>> GetDisplayReward(this D2Quest obj)
        {
            List<Cfg.Cm.CValTypId_isi> ret = null;

            if (obj.questType == QuestType.QuestTypeNewMain || obj.questType == QuestType.QuestTypeBranch)
            {
                var config = await Cfg.C.CMainQuest.GetConfigAsync(obj.cfgID);
                ret = config.Reward;
            }
            //成就任务
            //else if (obj.questType == QuestType.QuestTypeAchievement)
            //{
            //    var config = await Cfg.C.CAchievementQuest.GetConfigAsync(obj.cfgID);
            //    ret = config.Reward;
            //}
            else if (obj.questType == QuestType.QuestTypeGoddessTrial)
            {
                var config = await Cfg.C.CGoddessTrialUnlock.GetConfigAsync(obj.cfgID);
                ret = config.DisplayReward;
            }
            else if (obj.questType == QuestType.QuestTypeNewAllianceAchievePlayer)
            {
                var config2 = await Cfg.C.CNewAlliancePrivateQuest.GetConfigAsync(obj.cfgID);
                if (config2 != null)
                    ret = config2.Reward;
            }

            return ret;
        }

        /// <summary>
        /// 获取任务类型 用于跳转
        /// 没有任务总表 暂时只能这么写了
        /// </summary>
        public static async UniTask<int> GetCfgQuestType(this D2Quest obj)
        {
            switch (obj.questType)
            {
                case QuestType.QuestTypeMain:
                    var chapterQuest = await Cfg.C.CChapterQuest.GetConfigAsync(obj.cfgID);
                    if (chapterQuest != null)
                        return chapterQuest.QuestType;
                    break;
                case QuestType.QuestTypeDaily:
                    var questCommon = await Cfg.C.CNewQuestCommon.GetConfigAsync(obj.cfgID);
                    if (questCommon != null)
                        return questCommon.QuestType;
                    break;
                case QuestType.QuestTypeNewMain:
                    var config = await Cfg.C.CMainQuest.GetConfigAsync(obj.cfgID);
                    if (config != null)
                        return config.QuestType;
                    break;
                //case QuestType.QuestTypeAchievement:
                //    var cfg = await Cfg.C.CAchievementQuest.GetConfigAsync(obj.cfgID);
                //    if (cfg != null)
                //        return cfg.QuestType;
                //    break;
                case QuestType.QuestTypeGoddessTrial:
                    var godness = await Cfg.C.CGoddessTrialUnlock.GetConfigAsync(obj.cfgID);
                    if (godness != null)
                        return godness.QuestType;
                    break;
                case QuestType.QuestTypeNewAllianceAchieve:
                    var cfg1 = await Cfg.C.CNewAllianceQuest.GetConfigAsync(obj.cfgID);
                    if (cfg1 != null)
                        return cfg1.QuestType;
                    break;
                case QuestType.QuestTypeNewAllianceAchievePlayer:
                    var cfg2 = await Cfg.C.CNewAlliancePrivateQuest.GetConfigAsync(obj.cfgID);
                    if (cfg2 != null)
                        return cfg2.QuestType;
                    break;
                case QuestType.QuestTypeMobilize:
                    var cfg3 = await Cfg.C.CMobilizationCommon.GetConfigAsync(obj.cfgID);
                    if (cfg3 != null)
                        return cfg3.QuestType;
                    break;
            }

            return 0;
        }

        /// <summary>
        /// 任务前往方法 
        /// 任务无数据时 服务器不发 需要自己创建一个
        /// </summary>
        public static async void GoTo(this D2Quest obj)
        {
            var questType = await obj.GetCfgQuestType();
            if (questType == 0)
            {
                if (obj.QuestGoToType == 0)
                    return;
                questType = obj.QuestGoToType;
            }


            /// 章节任务分支
            if (obj.questType == QuestType.QuestTypeMain)
            {
                if (!GuidManage.TriggerGuid(GuidManage.GuidTriggerType.ChapterTaskGuide, obj.ConfigData.ChapterID, obj.ConfigData.Id))
                {
                    obj.Jump(questType);
                }
            }
            else
            {
                obj.Jump(questType);
            }
        }

        /// <summary>
        /// 最强指挥官任务跳转
        /// </summary>
        /// <param name="obj"></param>
        public static async void CommanderGoTo(this D2Quest obj, int jump)
        {
            if (jump == 0)
                return;

            D.Warning?.Log($"任务跳转:{jump}~");
            obj.Jump(jump, null);
        }


        static async void Jump(this D2Quest obj, int questType)
        {
            var cfg = await Cfg.C.CQuestList.GetConfigAsync(questType);
            if (cfg == null || cfg.Jump == 0)
                return;

            List<string> param = null;
            /// 章节任务分支
            if (obj.questType == QuestType.QuestTypeMain)
            {
                param = obj.ConfigData.Params;
            }
            /// 日常任务分支
            else if (obj.questType == QuestType.QuestTypeDaily)
            {
                param = obj.DailyConfigData.Params;
            }
            else if (obj.questType == QuestType.QuestTypeMobilize)
            {
                param = (await Cfg.C.CMobilizationCommon.GetConfigAsync(obj.cfgID)).Params;
            }

            D.Warning?.Log($"任务跳转:{cfg.Jump}~");

            obj.Jump(cfg.Jump, param);

        }

        static async void Jump(this D2Quest obj, int jump, List<string> param = null)
        {
            switch (jump)
            {
                case 1: ///内城界面
                    UI.TaskUtils.JumpToCity();
                    break;
                case 2: ///内城合成界面  带引导
                    var configData = await obj.GetConfigData();
                    if (configData != null)
                    {
                        UI.TaskUtils.ShowCityGuid(configData.Params.Count > 0 ? int.Parse(configData.Params[0]) : 0);
                    }
                    else
                    {
                        UI.TaskUtils.ShowCityGuid();
                    }

                    break;
                case 3: ///英雄升级界面
                    UI.TaskUtils.JumpToHero(UI.TaskUtils.HeroToDO.LevelUp);
                    break;
                case 4: ///英雄升星界面
                    UI.TaskUtils.JumpToHero(UI.TaskUtils.HeroToDO.StarUp);
                    break;
                case 5: ///普通招募界面
                    UI.TaskUtils.JumapToHeroRecruit();
                    break;
                case 6: ///龙界面
                    UI.TaskUtils.JumpToDragonSkill();
                    break;
                case 7: ///士兵界面
                    UI.TaskUtils.JumpToEvo();
                    break;
                case 8: ///小怪界面
                    UI.TaskUtils.JumpToWorld();
                    break;
                case 9: ///泰坦界面
                    UI.TaskUtils.JumpToWorld(2);
                    break;
                case 10: ///金币采集界面
                    UI.TaskUtils.JumpToWorld(1);
                    break;
                case 11: ///熔岩试炼活动界面
                    UI.TaskUtils.OpenActvPage();
                    break;
                case 12: ///大地图界面
                    UI.TaskUtils.JumpToWorld(4);
                    break;
                case 13: ///背包界面
                    UI.TaskUtils.JumpToWorld(2);
                    break;
                case 14: ///道具商店界面
                    UI.TaskUtils.JumpShop(ShopJumpMark.itemStore);
                    break;
                case 15: ///联盟界面
                    UI.TaskUtils.JumpToAlliance();
                    break;
                case 16: ///角色信息界面
                    UI.TaskUtils.OpenChangeName();
                    break;
                case 17: ///城堡升级界面
                    UI.TaskUtils.OpenCity();
                    break;
                case 18: ///钻石购买界面
                    UI.TaskUtils.JumpShop(ShopJumpMark.gem);
                    break;
                case 19: ///平价包界面
                         ///暂时废弃
                    break;
                case 20: ///王座活动界面
                    UI.TaskUtils.JumpToKingThrone();
                    break;
                case 21: ///竞技场界面
                    break;
                case 22: ///次元宝藏界面
                    UI.TaskUtils.OpenDimensionalTreasure();
                    break;
                case 23: ///联盟帮助界面
                    UI.TaskUtils.OpenAllianceHelp();
                    break;
                case 24: ///联盟科技界面
                    UI.TaskUtils.OpenAllianceTech();
                    break;
                case 25: ///铁匠铺界面
                    // UI.TaskUtils.OpenHeroEquipment();
                    break;
                case 26: ///召唤阵界面
                    UI.TaskUtils.OpenSummonAltar();
                    break;
                case 27: ///女巫锅界面
                    UI.TaskUtils.OpenWitchsBrew();
                    break;
                case 28: ///学院科技界面
                    UI.TaskUtils.JumpToDragonSkill();
                    break;
                case 29: ///联盟挑战界面
                    UI.TaskUtils.OpenAllianceChallenge();
                    break;
                case 30: ///英雄治疗界面
                    UI.TaskUtils.JumpToHero();
                    break;
                case 31: ///联盟领地堡垒
                    UI.TaskUtils.OpenUIAllianceWarTerritoryList(1);
                    break;
                case 32: ///联盟领地方尖碑
                    UI.TaskUtils.OpenUIAllianceWarTerritoryList(2);
                    break;
                case 33: ///高级招募界面
                    UI.TaskUtils.JumpToAdvancedHeroRecruit();
                    break;
                case 34: ///情报站
                    UI.CollectAtlasTaskUI.Layer.OpenSafely();
                    break;
                case 35: ///英雄主界面
                    PopupManager.I.ShowLayer<UIHeroList>();
                    break;
                case 36: ///赏金工会主界面
                    BountyGuild.BountyGuildUI.Show();
                    break;
                case 37: ///日常任务界面
                    // TaskMgr.I.OpenDailyTask(new UI.TaskUI.TaskUIData() { isOpenDailyPage = true });
                    break;
                case 40: ///自动合成
                    EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)UI.MainMenuType.CITY);
                    // PopupManager.I.ShowPanel<UI.UIMerage>("UIMerage");
                    break;
                case 41: ///加速界面
                    EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)UI.MainMenuType.CITY);
                    //PopupManager.I.ShowPanel<UI.UIUnionHelpSpeedUpPanel>();
                    break;
                case 42: ///联盟商店
                    if (LPlayer.I.UnionID > 0)
                    {
                        //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, MainMenuType.ALLIANCE);
                        PopupManager.I.ShowLayer<UIAllianceShop>();
                        AllianceShopMgr.I.OpenAllianceShop();
                    }
                    else
                    {
                        //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, MainMenuType.ALLIANCE);
                        PopupManager.I.ShowDialog<UIAllianceWelcome>();
                    }

                    break;
                case 43: ///图鉴
                    DeepUI.PopupManager.I.ShowLayer<UI.CollectAtlasUI.Layer>();
                    break;
                case 51: ///士兵界面  带手的
                    //UI.TaskUtils.JumpToMainCity(MainCityItem.CityType.Barracks);
                    break;
                case 52: ///铁匠铺界面  带手的

                    UI.TaskUtils.JumpToMainCity(MainCityItem.CityType.BlacksmithShop);
                    break;
                case 53: ///科技界面  带手的
                    //UI.TaskUtils.JumpToMainCity(MainCityItem.CityType.College);
                    break;



                /// CS新增jump - id
                case 101:
                    if (param == null || param.Count == 0)
                    {
                        Debug.LogError($"跳转类型的参数设置不正确，请检查， Id = {obj.cfgID}");
                        return;
                    }
                    UI.TaskUtils.JumpToReBuild(int.Parse(param[0]));
                    break;
                case 102:
                    UI.TaskUtils.JumpToCityToMerge();
                    break;
                case 103:
                    UI.TaskUtils.JumpToHero(TaskUtils.HeroToDO.LevelUp_MinTypeLevel);
                    break;
                case 104:
                    UI.TaskUtils.JumpToAlliance();
                    break;
                case 105:
                    UI.TaskUtils.JumpCityTechLevelUp();
                    break;
                case 106:
                    UI.TaskUtils.JumpToStar();
                    break;
                case 107: //主城升级
                    UI.TaskUtils.JumpToBuildLevelUp();
                    break;
                case 108:
                    if (!UIActivityMain.ShowActivityByCfgId(1000002))
                        UITools.PopTips(LocalizationMgr.Get("Skin_manage_tips_01"));
                    break;
                case 109:
                    UI.TaskUtils.JumpToHero(TaskUtils.HeroToDO.LevelUp);
                    break;
                case 110:
                    UI.TaskUtils.JumpToAllianceScience();
                    break;
                case 111:
                    UI.TaskUtils.JumpToWorld();
                    break;
                case 112:
                    UI.TaskUtils.JumpToWorld(1);
                    break;
                case 113:
                    UI.TaskUtils.JumpToHeroRecruit();
                    break;
                case 114:
                    //赠送亲密度 引导主界面英雄按钮 add by yujiawei
                    UI.TaskUtils.JumpToHero(UI.TaskUtils.HeroToDO.Intimacy);
                    break;
                case 115:
                    UI.TaskUtils.JumpToGoUIMain2();
                    break;
                case 116:
                    UI.TaskUtils.JumpToWorld(2);
                    break;
                case 117:
                    UI.TaskUtils.JumpToStar();
                    break;
                case 118:
                    UI.TaskUtils.JumpToCityToArmy();
                    break;
                case 119:
                    UI.TaskUtils.JumpToCityClickScience();
                    break;
                case 120:
                    UI.TaskUtils.JumpToDragonSkill();
                    break;
                case 121:
                    UI.TaskUtils.JumpToHero(TaskUtils.HeroToDO.StarUp);
                    break;
                case 122:
                    UI.TaskUtils.JumpToCityLevelUp(param);
                    break;
                case 123:
                    UI.TaskUtils.JumpToChangeName();
                    break;
            }
        }

        public static async UniTask<Cfg.G.CChapterQuest> GetConfigData(this D2Quest obj)
        {
            if (obj.questType == QuestType.QuestTypeMain)
            {
                return await Cfg.C.CChapterQuest.GetConfigAsync(obj.cfgID);
            }

            return null;
        }

        private static string GetClientKey(this D2Quest obj)
        {
            return string.Format("{0}_{1}", LPlayer.I.PlayerID, obj.cfgID);
        }

        public static async UniTask<long> Process(this D2Quest obj)
        {
            var configData = await obj.GetConfigData();
            if (configData != null)
            {
                if (ChapterTaskGameData.I.QuestTypeClientProcess.Contains(configData.QuestType))
                {
                    long clientProcess = long.Parse(PlayerTriggerPrefMgr.I.GetStr(obj.GetClientKey()));
                    obj.status = obj.status > clientProcess ? obj.status : clientProcess;
                    obj.UpdateProccess(obj.status);
                }
            }

            return obj.status;
        }

        public static async UniTask UpdateProccess(this D2Quest obj, long process)
        {
            //if (GameData.I.LevelData.CanToDoByGameLevel(MetaConfig.ChapterQuestOpenLV) &&
            //    LSwitchMgr.I.IsFunctionOpen(SwitchConfig.ChapterQuest))
            //{
            //    if (obj.state == QuestState.QuestStateGoOn)
            //    {
            //        obj.status = process;

            //        //D.Warning?.Log($"Client更新任务进度{ConfigData.Id}:{ConfigData.QuestType} {process}");

            //        PlayerTriggerPrefMgr.I.Save(obj.GetClientKey(), obj.status.ToString());
            //        Common.EventMgr.FireEvent(Common.TEventType.ChapterQuestProcessChange, obj);

            //        var configData = await obj.GetConfigData();
            //        if (configData?.Count <= obj.status && obj.state == QuestState.QuestStateGoOn)
            //        {
            //            obj.state = QuestState.QuestStateFinish;
            //            Common.EventMgr.FireEvent(Common.TEventType.ChapterQuestStateChange, obj);

            //            Common.MessageMgr.Send(new AdventureQuestSyncReq() { questId = obj.cfgID });
            //        }
            //    }
            //}
        }

        /// <summary>
        /// 获取任务描述
        /// </summary>
        /// <param name="questProgess">任务的进度信息</param>
        /// <returns></returns>
        public static async UniTask<string> GetDes(this D2Quest obj, bool questProgess = false)
        {
            if (questProgess)
            {
                return await GetDesc(obj.questType, obj.cfgID, obj.status);
            }

            return await GetDesc(obj.questType, obj.cfgID);
        }

        static string QuestDesc(int questType, string key, long count, List<string> Params, long curCount = -1)
        {
            string result = string.Empty;
            switch (questType)
            {
                case 201:
                case 203:
                case 301:
                case 501:
                case 503:
                case 903:
                case 513:
                case 1036:
                case 1004:
                case 208:
                    if (Params.Count > 0)
                    {
                        result = LocalizationMgr.Format(key, count, Params[0]);
                    }
                    else
                    {
                        result = LocalizationMgr.Format(key, count);
                    }

                    break;
                case 410:
                    ///兵种
                    if (Params.Count > 0)
                    {
                        if (int.TryParse(Params[0], out var t))
                        {
                            if (t == 0)
                            {
                                result = LocalizationMgr.Format(key, UIStringUtils.ExchangeValue(count),
                                    "Soldier_Type_0".ToLocal());
                            }
                            else if (t == 1)
                            {
                                result = LocalizationMgr.Format(key, UIStringUtils.ExchangeValue(count),
                                    "Skill_title_1".ToLocal());
                            }
                            else if (t == 2)
                            {
                                result = LocalizationMgr.Format(key, UIStringUtils.ExchangeValue(count),
                                    "ASSET_avatar_desc_5".ToLocal());
                            }
                            else if (t == 3)
                            {
                                result = LocalizationMgr.Format(key, UIStringUtils.ExchangeValue(count),
                                    "ASSET_avatar_name_8".ToLocal());
                            }
                            else if (t == 4)
                            {
                                result = LocalizationMgr.Format(key, UIStringUtils.ExchangeValue(count),
                                    "ASSET_avatar_name_1".ToLocal());
                            }
                        }
                    }

                    break;
                case 745:
                case 710:
                case 101:
                case 1037:
                case 1038:
                case 1039:
                case 505:
                case 1001:
                case 1002:
                case 1013:
                case 1006:
                case 727:
                case 302:
                case 801:
                case 511:
                case 904:
                case 905:
                case 907:
                case 909:
                case 910:
                case 912:
                case 606:
                case 914:
                case 608:
                case 916:
                case 607:
                case 1005:
                case 1007:
                case 1008:
                case 1015:
                case 1018:
                case 1012:
                case 1014:
                case 1035:
                default:
                    result = LocalizationMgr.Format(key, count);
                    break;
            }

            if (curCount != -1)
            {
                if (curCount >= count)
                {
                    curCount = count;
                    result += $"(<color=#409e16>{curCount}</color>/{count})";
                }
                else
                {
                    result += $"(<color=#f95f5f>{curCount}</color>/{count})";
                }
            }

            return result;
        }

        public static async UniTask<string> GetDesc(QuestType questType, int cfgID, long curCount = -1)
        {
            string str = "";

            //章节任务
            if (questType == QuestType.QuestTypeMain)
            {
                var configData = await Cfg.C.CChapterQuest.GetConfigAsync(cfgID);
                switch (configData.QuestType)
                {
                    case 201:
                    case 203:
                    case 301:
                    case 501:
                    case 503:
                        if (configData.Params.Count > 0)
                        {
                            str = LocalizationMgr.Format(configData.QuestDes, configData.Count, configData.Params[0]);
                        }
                        else
                        {
                            str = LocalizationMgr.Format(configData.QuestDes, configData.Count);
                        }

                        break;
                    case 403:
                        if (configData.Params.Count > 0)
                        {
                            if (int.TryParse(configData.Params[0], out int parValue))
                            {
                                switch (parValue)
                                {
                                    case 0:
                                        str = LocalizationMgr.Format(configData.QuestDes, configData.Count,
                                            LocalizationMgr.Get("Soldier_anyone"));
                                        break;
                                    case 1:
                                        str = LocalizationMgr.Format(configData.QuestDes, configData.Count,
                                            LocalizationMgr.Get("Skill_title_1"));
                                        break;
                                    case 2:
                                        str = LocalizationMgr.Format(configData.QuestDes, configData.Count,
                                            LocalizationMgr.Get("Skill_title_2"));
                                        break;
                                    case 3:
                                        str = LocalizationMgr.Format(configData.QuestDes, configData.Count,
                                            LocalizationMgr.Get("Skill_title_3"));
                                        break;
                                    case 4:
                                        str = LocalizationMgr.Format(configData.QuestDes, configData.Count,
                                            LocalizationMgr.Get("Skill_title_4"));
                                        break;
                                    default:
                                        break;
                                }
                            }
                        }
                        else
                        {
                            str = LocalizationMgr.Format(configData.QuestDes, configData.Count);
                        }

                        break;
                    case 505:
                    case 702:
                        //采集金币
                        str = LocalizationMgr.Format(configData.QuestDes,
                            UIStringUtils.ExchangeValue(configData.Count));
                        break;
                    case 308:
                        ///科技
                        if (int.TryParse(configData.Params[0], out var id0))
                        {
                            var techCfg = await Cfg.C.CD2Tech.GetConfigAsync(id0);
                            str = LocalizationMgr.Format(configData.QuestDes, techCfg.Name.ToLocal(), configData.Count);
                        }

                        break;
                    case 309:
                        ///科技
                        if (int.TryParse(configData.Params[0], out var id))
                        {
                            var techCfg = await Cfg.C.CD2Tech.GetConfigAsync(id);
                            str = LocalizationMgr.Format(configData.QuestDes, techCfg.Name.ToLocal());
                        }

                        break;
                    case 218:
                        ///英雄
                        if (configData.Params.Count > 0)
                        {
                            if (int.TryParse(configData.Params[0], out var type))
                            {
                                if (type == 0)
                                {
                                    str = LocalizationMgr.Format(configData.QuestDes, "Quality_Type_0".ToLocal(),
                                        UIStringUtils.ExchangeValue(configData.Count));
                                }
                                else if (type == 1)
                                {
                                    str = LocalizationMgr.Format(configData.QuestDes, "Hero_Text_4".ToLocal(),
                                        UIStringUtils.ExchangeValue(configData.Count));
                                }
                                else if (type == 2)
                                {
                                    str = LocalizationMgr.Format(configData.QuestDes, "Hero_Text_5".ToLocal(),
                                        UIStringUtils.ExchangeValue(configData.Count));
                                }
                                else if (type == 3)
                                {
                                    str = LocalizationMgr.Format(configData.QuestDes, "Hero_Text_6".ToLocal(),
                                        UIStringUtils.ExchangeValue(configData.Count));
                                }
                                else if (type == 4)
                                {
                                    str = LocalizationMgr.Format(configData.QuestDes, "Hero_Text_7".ToLocal(),
                                        UIStringUtils.ExchangeValue(configData.Count));
                                }
                            }
                        }
                        else
                        {
                            str = LocalizationMgr.Format(configData.QuestDes,
                                UIStringUtils.ExchangeValue(configData.Count));
                        }

                        break;
                    case 410:
                    case 103:
                        ///兵种
                        if (configData.Params.Count > 0)
                        {
                            if (int.TryParse(configData.Params[0], out var t))
                            {
                                if (t == 0)
                                {
                                    str = LocalizationMgr.Format(configData.QuestDes,
                                        UIStringUtils.ExchangeValue(configData.Count), "Soldier_Type_0".ToLocal());
                                }
                                else if (t == 1)
                                {
                                    str = LocalizationMgr.Format(configData.QuestDes,
                                        UIStringUtils.ExchangeValue(configData.Count), "Skill_title_1".ToLocal());
                                }
                                else if (t == 2)
                                {
                                    str = LocalizationMgr.Format(configData.QuestDes,
                                        UIStringUtils.ExchangeValue(configData.Count), "ASSET_avatar_desc_5".ToLocal());
                                }
                                else if (t == 3)
                                {
                                    str = LocalizationMgr.Format(configData.QuestDes,
                                        UIStringUtils.ExchangeValue(configData.Count), "ASSET_avatar_name_8".ToLocal());
                                }
                                else if (t == 4)
                                {
                                    str = LocalizationMgr.Format(configData.QuestDes,
                                        UIStringUtils.ExchangeValue(configData.Count), "ASSET_avatar_name_1".ToLocal());
                                }
                            }
                        }

                        break;
                    default:
                        str = LocalizationMgr.Format(configData.QuestDes, configData.Count);
                        break;
                }
            }
            //日常任务
            else if (questType == QuestType.QuestTypeDaily)
            {
                var config = await Cfg.C.CNewQuestCommon.GetConfigAsync(cfgID);
                str = LocalizationMgr.Format(config.LcDesc, config.Count);
            }
            //主、支线任务
            else if (questType == QuestType.QuestTypeNewMain)
            {
                var config = await Cfg.C.CMainQuest.GetConfigAsync(cfgID);
                str = LocalizationMgr.Format(config.QuestDes, config.Count);
            }
            //成就任务
            //else if (questType == QuestType.QuestTypeAchievement)
            //{
            //    var config = await Cfg.C.CAchievementQuest.GetConfigAsync(cfgID);
            //    str = LocalizationMgr.Format(config.QuestDes, config.Count);
            //}
            else if (questType == QuestType.QuestTypeGoddessTrial)
            {
                ///女神的试炼 --- 解锁任务
                var cfg = await Cfg.C.CGoddessTrialUnlock.GetConfigAsync(cfgID);
                if (cfg == null)
                    return string.Empty;
                str = QuestDesc(cfg.QuestType, cfg.QuestDes, cfg.Count, cfg.Params);
            }
            else if (questType == QuestType.QuestTypeNewAllianceAchieve)
            {
                var cfg = await Cfg.C.CNewAllianceQuest.GetConfigAsync(cfgID);
                if (cfg != null)
                {
                    str = QuestDesc(cfg.QuestType, cfg.QuestDes, cfg.Count, cfg.Params, curCount);
                }
            }
            else if (questType == QuestType.QuestTypeNewAllianceAchievePlayer)
            {
                var cfg = await Cfg.C.CNewAlliancePrivateQuest.GetConfigAsync(cfgID);
                if (cfg != null)
                    str = QuestDesc(cfg.QuestType, cfg.QuestDes, cfg.Count, cfg.Params, curCount);
            }

            return str;
        }


        public static string QuestDesc(int questType, string key, long count, List<string> Params = null)
        {
            string result = string.Empty;
            switch (questType)
            {
                case 201:
                case 203:
                case 301:
                case 501:
                case 503:
                    if (Params != null && Params.Count > 0)
                    {
                        result = LocalizationMgr.Format(key, count, Params[0]);
                    }
                    else
                    {
                        result = LocalizationMgr.Format(key, count);
                    }

                    break;
                case 410:
                    ///兵种
                    if (Params != null && Params.Count > 0)
                    {
                        if (int.TryParse(Params[0], out var t))
                        {
                            if (t == 0)
                            {
                                result = LocalizationMgr.Format(key, UIStringUtils.ExchangeValue(count),
                                    "Soldier_Type_0".ToLocal());
                            }
                            else if (t == 1)
                            {
                                result = LocalizationMgr.Format(key, UIStringUtils.ExchangeValue(count),
                                    "Skill_title_1".ToLocal());
                            }
                            else if (t == 2)
                            {
                                result = LocalizationMgr.Format(key, UIStringUtils.ExchangeValue(count),
                                    "ASSET_avatar_desc_5".ToLocal());
                            }
                            else if (t == 3)
                            {
                                result = LocalizationMgr.Format(key, UIStringUtils.ExchangeValue(count),
                                    "ASSET_avatar_name_8".ToLocal());
                            }
                            else if (t == 4)
                            {
                                result = LocalizationMgr.Format(key, UIStringUtils.ExchangeValue(count),
                                    "ASSET_avatar_name_1".ToLocal());
                            }
                        }
                    }

                    break;
                case 745:
                case 710:
                case 101:
                case 1037:
                case 1038:
                case 1039:
                case 706:
                case 746:
                    result = LocalizationMgr.Format(key, count);
                    break;
            }

            return result;
        }
    }

    public struct D2QuestComparer : IComparer<D2Quest>
    {
        public int Compare(D2Quest x, D2Quest y)
        {
            if (y != null)
            {
                //分支以抽离后的D2Quest.CompareXToY代码实现
                //章节任务
                if (x.questType == QuestType.QuestTypeMain)
                {
                    var config = Cfg.C.CChapterQuest.I(x.cfgID);
                    var otherConfig = Cfg.C.CChapterQuest.I(y.cfgID);
                    if (config == null)
                    {
                        return 1;
                    }
                    else if (otherConfig == null)
                    {
                        return -1;
                    }
                    else
                    {
                        if (y.state == cspb.QuestState.QuestStateFinish)
                        {
                            if (x.state == cspb.QuestState.QuestStateFinish)
                            {
                                return config.DisplayID - otherConfig.DisplayID;
                            }
                            else
                                return 1;
                        }
                        else if (y.state == cspb.QuestState.QuestStateGoOn)
                        {
                            if (x.state == cspb.QuestState.QuestStateFinish)
                            {
                                return -1;
                            }
                            else if (x.state == cspb.QuestState.QuestStateGoOn)
                            {
                                return config.DisplayID - otherConfig.DisplayID;
                            }
                            else
                            {
                                return 1;
                            }
                        }
                        else
                        {
                            if (x.state == cspb.QuestState.QuestStateReward)
                            {
                                return config.DisplayID - otherConfig.DisplayID;
                            }
                            else
                            {
                                return -1;
                            }
                        }
                    }
                }
                //日常任务
                else if (x.questType == QuestType.QuestTypeDaily)
                {
                    var config = Cfg.C.CNewQuestCommon.I(x.cfgID);
                    var otherConfig = Cfg.C.CNewQuestCommon.I(y.cfgID);
                    if (config == null)
                    {
                        return 1;
                    }
                    else if (otherConfig == null)
                    {
                        return -1;
                    }
                    else
                    {
                        if (y.state == cspb.QuestState.QuestStateFinish)
                        {
                            if (x.state == cspb.QuestState.QuestStateFinish)
                            {
                                return config.Sortord - otherConfig.Sortord;
                            }
                            else
                                return 1;
                        }
                        else if (y.state == cspb.QuestState.QuestStateGoOn)
                        {
                            if (x.state == cspb.QuestState.QuestStateFinish)
                            {
                                return -1;
                            }
                            else if (x.state == cspb.QuestState.QuestStateGoOn)
                            {
                                var ret = config.Sortord - otherConfig.Sortord;
                                if (ret == 0)
                                {
                                    ret = x.cfgID - y.cfgID;
                                }

                                return ret;
                            }
                            else
                            {
                                return 1;
                            }
                        }
                        else
                        {
                            if (x.state == cspb.QuestState.QuestStateReward)
                            {
                                return config.Sortord - otherConfig.Sortord;
                            }
                            else
                            {
                                return -1;
                            }
                        }
                    }
                }
                //主、支线任务
                else if (x.questType == QuestType.QuestTypeNewMain || x.questType == QuestType.QuestTypeBranch)
                {
                    var config = Cfg.C.CMainQuest.I(x.cfgID);
                    var otherConfig = Cfg.C.CMainQuest.I(y.cfgID);
                    if (config == null)
                    {
                        return 1;
                    }
                    else if (otherConfig == null)
                    {
                        return -1;
                    }
                    else
                    {
                        if (y.state == cspb.QuestState.QuestStateFinish)
                        {
                            if (x.state == cspb.QuestState.QuestStateFinish)
                            {
                                return config.QuestGroup - otherConfig.QuestGroup;
                            }
                            else
                                return 1;
                        }
                        else if (y.state == cspb.QuestState.QuestStateGoOn)
                        {
                            if (x.state == cspb.QuestState.QuestStateFinish)
                            {
                                return -1;
                            }
                            else if (x.state == cspb.QuestState.QuestStateGoOn)
                            {
                                return config.QuestGroup - otherConfig.QuestGroup;
                            }
                            else
                            {
                                return 1;
                            }
                        }
                        else
                        {
                            if (x.state == cspb.QuestState.QuestStateReward)
                            {
                                return config.QuestGroup - otherConfig.QuestGroup;
                            }
                            else
                            {
                                return -1;
                            }
                        }
                    }
                }
                //成就任务
                //else if (x.questType == QuestType.QuestTypeAchievement)
                //{
                //    var config = Cfg.C.CAchievementQuest.I(x.cfgID);
                //    var otherConfig = Cfg.C.CAchievementQuest.I(y.cfgID);
                //    if (config == null)
                //    {
                //        return 1;
                //    }
                //    else if (otherConfig == null)
                //    {
                //        return -1;
                //    }
                //    else
                //    {
                //        if (y.state == cspb.QuestState.QuestStateFinish)
                //        {
                //            if (x.state == cspb.QuestState.QuestStateFinish)
                //            {
                //                return config.QuestGroup - otherConfig.QuestGroup;
                //            }
                //            else
                //                return 1;
                //        }
                //        else if (y.state == cspb.QuestState.QuestStateGoOn)
                //        {
                //            if (x.state == cspb.QuestState.QuestStateFinish)
                //            {
                //                return -1;
                //            }
                //            else if (x.state == cspb.QuestState.QuestStateGoOn)
                //            {
                //                return config.QuestGroup - otherConfig.QuestGroup;
                //            }
                //            else
                //            {
                //                return 1;
                //            }
                //        }
                //        else
                //        {
                //            if (x.state == cspb.QuestState.QuestStateReward)
                //            {
                //                return config.QuestGroup - otherConfig.QuestGroup;
                //            }
                //            else
                //            {
                //                return -1;
                //            }
                //        }
                //    }
                //}
                else
                {
                    return 0;
                }
            }
            else
            {
                return 1;
            }
        }
    }
}