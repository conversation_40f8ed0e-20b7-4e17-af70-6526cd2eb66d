using System;
using System.Collections.Generic;
using System.Diagnostics;
using Common;
using cspb;
using Game.Config;
using Game.Data;
using Logic;
using P2;
using Public;
using TFW;
using TFW.Localization;
using TFW.UI;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

using EventTriggerType = TFW.EventTriggerType;
using Object = UnityEngine.Object;
using DG.Tweening;
using DeepUI;
using Game.Utils;
using Cysharp.Threading.Tasks;

namespace UI
{
    /// <summary>
    /// Banner模块
    /// </summary>
    public class UIActivityBanner : UIActivityModule
    {
        /// <summary>
        /// 标题
        /// </summary>
        protected TFWText title;
        protected BetterOutline titleOutline;
        /// <summary>
        /// 描述
        /// </summary>
        protected TFWText desc;
        /// <summary>
        /// 进度
        /// </summary>
        protected TFWSlider progress;
        /// <summary>
        /// 进度文本
        /// </summary>
        private TFWText progressTxt;

        /// <summary>
        /// 新的倒计时展示
        /// </summary>
        protected TimeBarNew timeBarNew;

        /// <summary>
        /// 模块数据
        /// </summary>
        protected ActivityBannerModule data;

        /// <summary>
        /// 奖励列表固定3个
        /// </summary>
        private List<RewardItemUI> rewardItems;

        /// <summary>
        /// banner图
        /// </summary>
        protected GameObject bannerImgParent;

        /// <summary>
        /// banner图
        /// </summary>
        protected GameObject bannerImg;

        /// <summary>
        /// banner图
        /// </summary>
        protected string bannerImgPath;

        /// <summary>
        /// 计时动画
        /// </summary>
        protected Animator timerImgAnim;

        /// <summary>
        /// 计时动画指针-需要归位
        /// </summary>
        private GameObject timerImgAnimPointer;

        /// <summary>
        /// Tip新需求，空内容不显示Tips
        /// </summary>
        protected GameObject tipObj;

        /// <summary>
        /// 奖励背景对象
        /// </summary>
        protected GameObject rewardBgObj;

        /// <summary>
        /// 奖励根节点
        /// </summary>
        protected GameObject rewardRoot;
        // /// <summary>
        // /// banner排行榜按钮
        // /// </summary>
        // protected GameObject rankObj;
        // /// <summary>
        // /// banner补给按钮
        // /// </summary>
        // protected GameObject recoverObj;
        /// <summary>
        /// 构造
        /// </summary>
        /// <param name="moduleId"></param>
        public UIActivityBanner(int moduleId) : base(moduleId)
        {

        }

        public void SetTipObjActive(bool isShowTip)
        {
            tipObj?.SetActive(isShowTip);
        }

        /// <summary>
        /// 获取ui元素
        /// </summary>
        protected override void GetUI()
        {
            #region GetUI
            var go = LinkModule;
            if (go)
            {
                //通用部分
                title = UIHelper.GetComponent<TFWText>(go, "Title");
                titleOutline = UIHelper.GetComponent<BetterOutline>(go, "Title");
                desc = UIHelper.GetComponent<TFWText>(go, "Desc");
                bannerImgParent = UIHelper.GetChild(go, "BG");
                tipObj = UIHelper.GetChild(go, "Info");
                UIBase.RemoveListener(EventTriggerType.Click, tipObj);
                UIBase.AddListener(EventTriggerType.Click, tipObj, OnInfoClick);


                //非通用部分
                timerImgAnim = UIHelper.GetComponent<Animator>(go, "Clock");
                timerImgAnimPointer = UIHelper.GetChild(go, "Clock/Point1");
                progress = UIHelper.GetComponent<TFWSlider>(go, "Slider");
                progressTxt = UIHelper.GetComponent<TFWText>(go, "Slider/Text");

                //进度条根据描述长度缩进处理
                if (!timerImgAnim)
                    timerImgAnim = UIHelper.GetComponent<Animator>(go, "Desc/Clock");
                if (!timerImgAnimPointer)
                    timerImgAnimPointer = UIHelper.GetChild(go, "Desc/Clock/Point1");
                if (!progress)
                    progress = UIHelper.GetComponent<TFWSlider>(go, "Desc/Slider");
                if (!progressTxt)
                    progressTxt = UIHelper.GetComponent<TFWText>(go, "Desc/Slider/Text");

                //奖励背景对象
                rewardRoot = UIHelper.GetChild(go, "Reward");
                rewardBgObj = UIHelper.GetChild(go, "Reward/BG");

                var bgBtn = UIHelper.GetChild(go, "Reward/BgBtn");
                if (!bgBtn)
                    bgBtn = UIHelper.GetChild(go, "Reward");
                if (bgBtn)
                {
                    UIBase.RemoveListener(EventTriggerType.Click, bgBtn);
                    UIBase.AddListener(EventTriggerType.Click, bgBtn, OnRewardItemClick);
                }

                rewardItems = new List<RewardItemUI>();
                var reawrdRoot = UIHelper.GetChild(go, "Reward/List");
                if (reawrdRoot != null)
                {
                    RewardItem rewardItem;
                    for (int i = 0; i < 3; i++)
                    {
                        var tr = reawrdRoot.transform.GetChild(i);
                        if (tr)
                        {
                            RewardItemUI rewardItemGo = tr.gameObject.GetComponent<RewardItemUI>();

                            rewardItems.Add(rewardItemGo);
                        }
                        //var listener = parent.GetComponent<EventTriggerListener>();
                        //if (listener == null)
                        //    listener = parent.AddComponent<EventTriggerListener>();

                        //listener.RemoveListener(EventTriggerType.Click);
                        //listener.AddListener(EventTriggerType.Click, (a, b) =>

                    }
                }

                //if (progress != null && progress.fillRect != null)
                //{
                //    progress.fillRect.gameObject.SetActive(false);
                //}

                if (progress != null && progress.fillRect != null)
                {
                    //不要进度条了
                    progress.fillRect.gameObject.SetActive(false);
                }

                timeBarNew = GetComponent<TimeBarNew>("Time");
            }


            #endregion
        }


        /// <summary>
        /// 加载完成
        /// </summary>
        /// <param name="go"></param>
        protected override void OnLoaded(GameObject go)
        {
            base.OnLoaded(go);

            if (this.m_UIActivity != null && this.m_UIActivity.ActivityContext != null)
            {
                Attach(this.m_UIActivity.ActivityContext.ActivityBannerRoot);
            }
        }

        /// <summary>
        /// 活动规则
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        protected virtual void OnInfoClick(GameObject arg0, PointerEventData arg1)
        {
            if (data != null)
            {
                //if (ActivityMgr.EanbleDebug)
                //{
                //    //显示活动规则界面
                //    WndMgr.Show<UIActivityPassLevelRankRule>("", new UIActivityPassLevelRankRuleData() { activityId = 1, rules = "zxczxczxcxzc" });
                //}
                //else
                {
                    PopupManager.I.ShowDialog<UIActivityRules>(
                        new UIActivityRuleData()
                        {
                            activityId = data.ActId,
                            rules = data.Tips
                        });
                }
            }
            else
            {
                D.Warning?.Log("UIActivityPassLevelBanner data is null!");
            }
        }

        /// <summary>
        /// 奖励界面查看
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        public virtual void OnRewardItemClick(GameObject arg0, PointerEventData arg1)
        {
            if (ActivityData != null)
            {
                var rank = GameData.I.ActivityData.GetActvRankById(ActivityData.Id);
                if (rank != null)
                {
                    PopupManager.I.ShowPanel<UIActivityPassLevelRankReward>("", new UIActivityPassLevelRankRewardData() { activityId = ActivityData.Id, actvRankRewards = rank.rankRewards });
                }
                else
                {
                    D.Warning?.Log("not find GetActvRankById ={0}", ActivityData.Id);
                }
            }
        }

        /// <summary>
        /// 父节点
        /// </summary>
        /// <param name="parent"></param>
        //public override void Attach(GameObject parent)
        //{
        //    //base.Attach(this.m_UIActivity.ActivityContext.ActivityBannerRoot);
        //    if (LinkModule)
        //    {
        //        var rect = LinkModule.GetComponent<RectTransform>();
        //        var pos = rect.anchoredPosition3D;
        //        var size = rect.sizeDelta;
        //        var offsetMin = rect.offsetMin;
        //        var offsetMax = rect.offsetMax;

        //        //需要在隐藏状态下 设置父对象,设置完成后显示,这样锚点才不会变化,否则直接乱了???
        //        LinkModule.transform.SetParent(parent.transform);
        //        rect.anchoredPosition3D = pos;
        //        rect.offsetMin = offsetMin;
        //        rect.offsetMax = offsetMax;
        //        rect.sizeDelta = size;
        //        LinkModule.transform.localScale = Vector3.one;
        //    }
        //}

        /// <summary>
        /// 交替时间
        /// </summary>
        private int replaceTime = 5;

        /// <summary>
        /// 交替
        /// </summary>
        private bool isReplace = true;

        /// <summary>
        /// 交替时间
        /// </summary>
        private int replaceTimer1 = 0;

        /// <summary>
        /// 交替时间
        /// </summary>
        private int replaceTimer2 = 0;

        /// <summary>
        /// 开始
        /// </summary>
        private DateTime startTime;

        /// <summary>
        /// 结束
        /// </summary>
        private DateTime endTime;

        /// <summary>
        /// 当前状态
        /// </summary>
        protected ActivityState curActivityState;

        /// <summary>
        /// 是否需要更新
        /// </summary>
        private bool canUpdate;

        /// <summary>
        /// 加载中
        /// </summary>
        private bool isLoadingBanner = false;

        public override void Show(Activity curActivity, ActivityModule moduleData)
        {
            base.Show(curActivity, moduleData);
        }

        /// <summary>
        /// 刷新
        /// </summary>
        public override async UniTask Refresh(Activity curActivity, ActivityModule moduleData)
        {
            await base.Refresh(curActivity, moduleData);
            this.data = moduleData as ActivityBannerModule;
            if (tipObj)
                tipObj.SetActive(data != null && data.Tips != string.Empty);

            if (Loaded)
            {
                if (this.data != null)
                {


                    curActivityState = curActivity.Brief.CurrentState;
                    canUpdate = curActivityState == ActivityState.Going;//未来可能还有预告状态
                    title.text = LocalizationMgr.Get(this.data.Title == null ? "" : this.data.Title);
                    title.color = ColorUtils.ColorFromRGBAString(this.data.TitleColor == null ? "FFFFFF" : this.data.TitleColor);
                    desc.color = ColorUtils.ColorFromRGBAString(this.data.DescColor == null ? "FFFF00" : this.data.DescColor);

                    if (titleOutline != null)
                    {
                        if (this.data.TitleOutlineColor == "0")
                            titleOutline.enabled = false;
                        else
                            titleOutline.effectColor = ColorUtils.ColorFromRGBAString(this.data.TitleOutlineColor == null ? "FFFFFF" : this.data.TitleOutlineColor);
                    }
                    desc.text = LocalizationMgr.Get(this.data.Desc == null ? "" : this.data.Desc);
                    isReplace = true;

                    startTime = TFW.Common.ConvertUnixTimeStampToDateTime(this.data.Startime.ToString());
                    endTime = TFW.Common.ConvertUnixTimeStampToDateTime(this.data.EndTime.ToString());

                    //var maxTime = this.data.EndTime - this.data.Startime;//总时长
                    //var rematime = this.data.EndTime - GameTime.Time;//剩余时间
                    //var result = UIHelper.GetFormatTime(maxTime);
                    ////ActivityLogUtils.Error("总时间=======" + result);
                    ////ActivityLogUtils.Error("start={0} end={1} time={2} maxCdTime={3} rematime={4}", startTime, endTime, GameTime.Time, maxTime, rematime);

                    //D.Error?.Log("this.data.BannerImg={0}", this.data.BannerImg);

                    //TryUnLoadBanner();
                    if (bannerImg == null && this.bannerImgParent)
                    {
                        bannerImgPath = UIActivityModuleHelper.LoadImg(this.data.BannerImg, this.bannerImgParent.transform, BannerImgLoadCallBack);

                        //bannerImgPath = UIActivityModuleHelper.LoadImg(this.data.BannerImg, this.bannerImgParent.transform, (o) =>
                        //{
                        //    //this.bannerImg = o;
                        //    ////临时处理。年后需要统一尺寸
                        //    //if (this.data.BannerImg == 250007)
                        //    //{
                        //    //    if (this.bannerImg)
                        //    //    {
                        //    //        var pos = this.bannerImg.transform.localPosition;
                        //    //        pos.y -= 40;
                        //    //        this.bannerImg.transform.localPosition = pos;
                        //    //    }
                        //    //}
                        //    //else if(this.data.BannerImg == 220014)
                        //    //{
                        //    //    EventMgr.FireEvent(TEventType.ActvTurnTableHeroSpine); 

                        //    //    //Baner上的spine动态加载
                        //    //    //PopupEffectConfigurator turntableBannerEffect = this.bannerImg.transform.GetChild(0).transform.GetChild(0).GetComponent<PopupEffectConfigurator>();
                        //    //    //D.Error?.Log(turntableBannerEffect.transform.localPosition);
                        //    //    //D.Error?.Log(turntableBannerEffect.transform.localScale);
                        //    //    //var ui = PopupManager.I.FindPopup<UIActivityMain>();
                        //    //    //if (ui != null)
                        //    //    //    turntableBannerEffect.InitSorting(ui.canvas.sortingLayerID, ui.canvas.sortingOrder + 600);
                        //    //}
                        //    ////isLoadingBanner = false;  //如果设为false会出现两个baner的情况，在销毁时重置即可
                        //});
                    }


                    if (curActivityState == ActivityState.End)
                    {
                        this.End();
                    }
                    else
                    {
                        if (timerImgAnim != null) timerImgAnim.enabled = true;
                        Going();
                    }

                    //奖励预览
                    if (rewardItems != null && rewardItems.Count > 0)
                    {
                        for (int i = 0; i < rewardItems.Count; i++)
                        {
                            if (data.rewards == null)
                            {
                                rewardItems[i].gameObject.SetActive(false);
                                continue;
                            }

                            if (i < this.data.rewards.Count)
                            {
                                //UIItem.SetSingleReward(rewardItems[i], this.data.rewards[i], false);
                                ////ActivityLogUtils.Log("SetSingleReward isNewItem={0} reward.ID={1} typ={2} val={3}", true, this.data.rewards[i].ID, this.data.rewards[i].typ, this.data.rewards[i].val);
                                rewardItems[i].gameObject.SetActive(true);
                                rewardItems[i].SetData(data.rewards[i]);
                                rewardItems[i].SetCount(String.Empty);//不显示奖励数量
                            }
                            else
                            {
                                rewardItems[i].gameObject.SetActive(false);
                            }
                        }
                    }

                    if (timeBarNew)
                    {
                        timeBarNew.SetData(this.data.Startime, this.data.EndTime);
                    }
                }
            }
        }

        /// <summary>
        /// banner加载完成回调
        /// </summary>
        protected virtual void BannerImgLoadCallBack(GameObject o)
        {
            if (!o || o == null)
            {
                isLoadingBanner = false;
                return;
            }

            this.bannerImg = o;
            //临时处理。年后需要统一尺寸
            if (this.data.BannerImg == 250007)
            {
                if (this.bannerImg)
                {
                    var pos = this.bannerImg.transform.localPosition;
                    pos.y -= 40;
                    this.bannerImg.transform.localPosition = pos;
                }
            }
            if (this.data.BannerImg == 230025)
            {
                Debug.LogError("活动图标加载");
            }
            //子类实现去做逻辑
            //else if (this.data.BannerImg == 220014)
            //{
            //    EventMgr.FireEvent(TEventType.ActvTurnTableHeroSpine);

            //    //Baner上的spine动态加载
            //    //PopupEffectConfigurator turntableBannerEffect = this.bannerImg.transform.GetChild(0).transform.GetChild(0).GetComponent<PopupEffectConfigurator>();
            //    //D.Error?.Log(turntableBannerEffect.transform.localPosition);
            //    //D.Error?.Log(turntableBannerEffect.transform.localScale);
            //    //var ui = PopupManager.I.FindPopup<UIActivityMain>();
            //    //if (ui != null)
            //    //    turntableBannerEffect.InitSorting(ui.canvas.sortingLayerID, ui.canvas.sortingOrder + 600);
            //}
            ////isLoadingBanner = false;  //如果设为false会出现两个baner的情况，在销毁时重置即可
        }

        /// <summary>
        /// 已结束
        /// </summary>
        public virtual void End()
        {
            canUpdate = false;
            EndRematime = 0;
            if (progress)
            {
                progress.value = 1f;
                progressTxt.text = LocalizationMgr.Get("LevelRank_Text06");
            }

            if (timerImgAnim)
            {
                timerImgAnimPointer.transform.rotation = Quaternion.identity;
                timerImgAnim.enabled = false;
            }
        }

        /// <summary>
        /// 刷新进度
        /// </summary>
        /// <param name="rematime"></param>
        /// <param name="text"></param>
        private void RefreshProgress(string text)
        {
            if (progressTxt)
            {
                var color = progressTxt.color;
                color.a = 0;
                progressTxt.color = color;
                progressTxt.DOFade(1f, 2f);
                progressTxt.text = text;
            }
        }

        /// <summary>
        /// 结束剩余时间
        /// </summary>
        public long EndRematime { get; private set; }


        /// <summary>
        /// 加入原因：有的banner倒计时用的结束时间不是this.data.EndTime，要自己重新写。
        /// </summary>
        /// <returns></returns>
        protected virtual long GetEndTime()
        {
            return this.data.EndTime;
        }

        /// <summary>
        /// 进行中
        /// </summary>
        private void Going()
        {
            if (progressTxt)
            {
                EndRematime = GetEndTime() - GameTime.Time;//剩余时间
                var maxTime = GetEndTime() - this.data.Startime;//总时长

                if (EndRematime > 0)
                {
                    //日期和剩余时间交替显示
                    if (!isReplace)
                    {
                        replaceTimer1 += 1;
                        if (replaceTimer1 > replaceTime)
                        {
                            isReplace = true;
                            replaceTimer1 = 0;
                            progressTxt.isFlipDelayText = false;
                            progressTxt.isUseSrcText = false;
                            RefreshProgress(string.Format("{0} {1}", LocalizationMgr.Get("Gift_daily_gift_reset_time"), UIHelper.GetFormatTime(EndRematime, true, true)));
                        }
                    }
                    else
                    {
                        replaceTimer2 += 1;

                        progressTxt.isUseSrcText = false;
                        progressTxt.isFlipDelayText = false;
                        progressTxt.text = UIHelper.GetFormatTime(EndRematime, true, true);
                        //if (replaceTimer2 > replaceTime)
                        //{
                        //    isReplace = false;
                        //    replaceTimer2 = 0;
                        //    if (LocalizationMgr.IsRightToLeftLanguage)
                        //    {
                        //        progressTxt.isUseSrcText = true;
                        //        RefreshProgress($"{string.Format("{0}–{1}", startTime.ToString("yyyy/MM/dd"), endTime.ToString("yyyy/MM/dd"))}");
                        //    }
                        //    else
                        //    {
                        //        RefreshProgress(string.Format("{0}–{1}", startTime.ToString("yyyy/MM/dd"), endTime.ToString("yyyy/MM/dd")));
                        //    }
                        //}
                        //else
                        //{
                        //    progressTxt.isUseSrcText = false;
                        //    progressTxt.text = string.Format("{0} {1}", LocalizationMgr.Get("Gift_daily_gift_reset_time"), UIHelper.GetFormatTime(EndRematime, true, true));
                        //}
                    }
                    progress.value = EndRematime * 1f / maxTime;
                }
                else
                {
                    //progress.value = rematime * 1f / maxTime;
                    this.End();
                }
            }
        }

        /// <summary>
        /// 每秒更新
        /// </summary>
        public override void UpdateSecond()
        {
            if (Loaded && data != null && ActivityData != null && canUpdate)
            {
                //var startTime = TFW.Common.ConvertUnixTimeStampToDateTime(ActivityData.Brief.StartTime.ToString());
                //ActivityLogUtils.Warning("开始时间=" + startTime.ToString("yyyy/MM/dd HH:mm:ss"));
                //var foreCastTime = TFW.Common.ConvertUnixTimeStampToDateTime(ActivityData.Brief.ForeCastTime.ToString());
                //ActivityLogUtils.Warning("预告时间=" + foreCastTime.ToString("yyyy/MM/dd HH:mm:ss"));
                //var endTime = TFW.Common.ConvertUnixTimeStampToDateTime(ActivityData.Brief.EndTime.ToString());
                //ActivityLogUtils.Warning("结束时间=" + endTime.ToString("yyyy/MM/dd HH:mm:ss"));
                //var closeTime = TFW.Common.ConvertUnixTimeStampToDateTime(ActivityData.Brief.CloseTime.ToString());
                //ActivityLogUtils.Warning("关闭时间=" + closeTime.ToString("yyyy/MM/dd HH:mm:ss"));

                //var rematime = ActivityData.Brief.ForeCastTime - GameTime.Time;
                //if (rematime > 0)
                //{
                //    ActivityLogUtils.Warning("预告剩余时间(开始) rematime=" + UIHelper.GetFormatTime(rematime, true, true));
                //}
                //else
                //{
                //    ActivityLogUtils.Warning("预告剩余时间0,活动已经开始 rematime=" + UIHelper.GetFormatTime(rematime, true, true));
                //}


                this.curActivityState = this.ActivityData.Brief.CurrentState;
                if (curActivityState == ActivityState.End)
                {
                    End();
                }
                else if (curActivityState == ActivityState.Going)
                {
                    Going();
                }
            }
        }

        /// <summary>
        /// 卸载banner
        /// </summary>
        private void TryUnLoadBanner()
        {
            if (!string.IsNullOrEmpty(bannerImgPath))
            {
                if (bannerImg != null) Object.Destroy(bannerImg);
                //ResourceMgr.Unload(bannerImgPath, this.bannerImg, true);
                ////ActivityLogUtils.Log("bannerImgPath Unload ={0},{1}", bannerImgPath, this.bannerImg);
                this.bannerImg = null;
                bannerImgPath = "";
            }
        }

        /// <summary>
        /// 销毁
        /// </summary>
        protected override void OnDestroyed()
        {
            isLoadingBanner = false;
            TryUnLoadBanner();
        }
    }
}
