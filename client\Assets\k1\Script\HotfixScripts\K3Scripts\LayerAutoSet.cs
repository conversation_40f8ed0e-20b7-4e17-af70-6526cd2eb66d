﻿using Public;
using System.Collections.Generic;
using TFW.Map;
using UnityEngine;


[RequireComponent(typeof(Canvas))]
    public class LayerAutoSet : MonoBehaviour
{
    [SerializeField]
    private List<Canvas> childCanvas;
    [SerializeField]
    private List<Renderer> childRenders;
    [SerializeField]
    private List<int> childRenderSortNums;
    [SerializeField]
    private Canvas mCanvas;

    public int curPopupMaxSortNum;

    [ContextMenu("SetLayer")]
    public int InitLayer()
    {
        int maxSortNum = 0;
        if (mCanvas == null)
        {
            mCanvas = GetComponent<Canvas>();

            if (mCanvas == null)
                mCanvas = UIHelper.GetParentComponent<Canvas>(gameObject);
        }

        if (mCanvas == null)
            return 0;

        if (childCanvas==null)
        {
            childCanvas=new List<Canvas>();
        }

        childCanvas.Clear();

        var canvases= GetComponentsInChildren<Canvas>(true);
        foreach (var item in canvases)
        {
            if (item.tag != MapCoreDef.IGNORED_OBJECT_TAG)
            {
                childCanvas.Add(item);
            }
        }
       
         
        if (childRenders == null)
        {
            childRenders = new List<Renderer>();
            childRenders.AddRange(GetComponentsInChildren<Renderer>(true));
            childRenderSortNums = new List<int>();
            for (int i = 0; i < childRenders.Count; i++)
            {
                if (childRenders[i] != null)
                { 
                    childRenderSortNums.Add(childRenders[i].sortingOrder- mCanvas.sortingOrder);
                }
                   
            }
        }

        int orderNum = 0;
        foreach (var item in childCanvas)
        {
            if (item != null && item != mCanvas )
            {
                orderNum += 5;
                item.sortingLayerID = mCanvas.sortingLayerID;
                var toOrderNum= mCanvas.sortingOrder + orderNum;
                item.sortingOrder = toOrderNum;

                maxSortNum = maxSortNum < toOrderNum ? toOrderNum : maxSortNum;
            }
        }

        for (int i = 0; i < childRenders.Count; i++)
        {
            if (childRenders[i] != null)
            {
                childRenders[i].sortingLayerID = mCanvas.sortingLayerID;

                var toOrderNum = mCanvas.sortingOrder + childRenderSortNums[i];

                childRenders[i].sortingOrder = toOrderNum;

                //maxSortNum = maxSortNum < toOrderNum ? toOrderNum : maxSortNum;
            }
        }

        if(maxSortNum==0)
            maxSortNum=mCanvas.sortingOrder;

        curPopupMaxSortNum = maxSortNum;

        return maxSortNum;
    }
}




 