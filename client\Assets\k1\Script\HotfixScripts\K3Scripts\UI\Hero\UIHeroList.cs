﻿ 
using Common;
using cspb;
using DeepUI;
using Game.Data;
using K1;
using Logic;
using Public;
using System;
using System.Collections.Generic;
using System.Linq;
using TFW;
using TFW.Localization;
using TFW.UI;
using UI;
using UnityEngine;
using UnityEngine.EventSystems;
using EventTriggerType = TFW.EventTriggerType;
using HeroData = Game.Data.HeroData;

/// <summary>
/// 英雄列表UI界面
/// <AUTHOR>
/// @data 2020/5/8
/// @ver 1.0
/// </summary>
///
public class UIHeroListData : UIData
{
    public bool isMain;
    public bool isHeroList;

    /// <summary>
    /// 要添加英雄的部队的索引
    /// </summary>
    public int LineUpId = -1;

    /// <summary>
    /// 副将位置上阵
    /// </summary>
    public int Pos;

    public K3.SpecialBoxData mBox;
    public int mBoxHeroIndex;

    /// <summary>
    /// 上阵英雄回调
    /// </summary>
    public Action UpHeroCallBack;

    /// <summary>
    /// 普通关闭回调
    /// </summary>
    public Action CloseUICallBack;

    /// <summary>
    /// 对 Item 进行过滤
    /// </summary>
    public Predicate<Game.Data.HeroData> ItemShouldShow;

    public int mHeroIndex;
    public bool isHelpHero;
    public int mHelpHeroIndex;
}

[Popup("Hero/UIHero")]
public partial class UIHeroList : BasePopupLayer
{
    /*
    protected override string assetPath => "Hero/UIHero";
    protected override int SortingLayer => CustomSortingLayer.SceneUI;
    public override bool LFullScreen => true;
    */

    /// <summary>
    /// 排序
    /// </summary>
    //public override ESortingOrder SortingOrder => ESortingOrder.MainMenuPanel;

    ///// <summary>
    ///// 当前界面的PanelId
    ///// </summary>
    //public int PanelId { get; protected set; } = PanelConfig.PANEL_HERO_LIST;
    ///// <summary>
    ///// 资源路径 HeroList_k1 UIHero
    ///// </summary>
    //protected string assetPath => "Hero/UIHero";

    ///// <summary>
    ///// 设置黑色遮罩
    ///// </summary>
    //public EPopPanelType PopPanelType { get; set; } = EPopPanelType.FullScreen;

    //public bool BottomMiddle { get; set; } = true;

    #region UI关联数据

    /// <summary>
    /// 英雄列表Title
    /// </summary>
    private GameObject _listTitle;

    /// <summary>
    /// 关闭按钮
    /// </summary>
    private GameObject _closeBtn;

    /// <summary>
    /// 顶部资源条
    /// </summary>
    private TopResourceWidget _topResBar;

    private GameObject _heroContent;

    
    //无限滚动
    public TFWLoopListView loopListView;

    /// <summary>
    /// 新加锁得英雄配置id
    /// </summary>
    private List<int> _lockConfigId;

    /// <summary>
    /// 新解锁的英雄的配置id
    /// </summary>
    private List<int> _unLockConfigId;

    /// <summary>
    /// 新加锁的英雄的唯一id
    /// </summary>
    private List<long> _lockHeroId;

    /// <summary>
    /// 新解锁的英雄的唯一id
    /// </summary>
    private List<long> _unLockHeroId;
    
    ///// <summary>
    ///// 是否上阵英雄
    ///// </summary>
    //private bool _isInBattleHero;
    /// <summary>
    /// 要上阵的部队的id
    /// </summary>
    public int _InBattleHeroLintUpId, _InBattleHeroLineUpPos;

    private K3.SpecialBoxData mBox; private int mBoxHeroIndex;

    private bool isMain = false;
    private bool isHeroList = false;
    private bool isHelpHero = false; public int mHelpHeroIndex;
    private Action SetHeroCallBack, CloseUICallBack;

    /// <summary>
    /// 是否选中所有
    /// </summary>
    private bool _isSelectedAll;

    /// <summary>
    /// 选择被吃的卡的英雄列表
    /// </summary>
    private List<long> _LevelUpHeroLst;

    /// <summary>
    /// 临时用于存储同样英雄的id
    /// </summary>
    private List<long> _LevelUpHeroLstTmp;

    /// <summary>
    /// UIHeroList窗口上阵英雄部分
    /// </summary>
    private GameObject _UIHero_InBattle;

    /// <summary>
    /// 存储英雄item的字典
    /// </summary>
    //private Dictionary<GameObject, UIHero_Item> _UIHeroItemsDic;
    /// <summary>
    /// 存储英雄item的集合
    /// </summary>
    private List<UIHeroListItem> UIHeroItemsLst = new List<UIHeroListItem>();

    ///// <summary>
    ///// 当前选中的HreoItem
    ///// </summary>
    //private UIHero_Item _selectedHeroItem;

    /// <summary>
    /// 英雄面板下面的金币按钮
    /// </summary>
    private GameObject _ButtomPanel;

    /// <summary>
    /// 玩家的总金币
    /// </summary>
    private TFWText _totalGoldText;

    /// <summary>
    /// 升级英雄要花费的金币
    /// </summary>
    private TFWText _costGoldText;

    /// <summary>
    /// 没有选中英雄的提示
    /// </summary>
    private TFWText _noHeroTipText;

    //private List<UIHero_Item> _LevelUpCostHero;

    /// <summary>没有英雄的跳转button</summary>
    private GameObject _noneButton;

    /// <summary>没有英雄的提示文本</summary>
    private TFWText _noneText;

    private GameObject _heroMainBtn;

    /// <summary>
    /// none根节点
    /// </summary>
    private GameObject _noneRoot;

    ///// <summary>
    ///// 钻石升级按钮
    ///// </summary>
    //private GameObject _diamondUpgradeBtn;

    ///// <summary>
    ///// 钻石升级文本
    ///// </summary>
    //private TFWText _diamondUpgradeTxt;

    ///// <summary>
    ///// 钻石消耗
    ///// </summary>
    //private int _diamondCost = -1;

    /// <summary>
    /// 回复战力按钮
    /// </summary>
    private GameObject _rePowerBtn;

    public bool LockState { get; private set; }

    /// <summary>
    /// 锁界面的开关
    /// </summary>
    private GameObject _lockBtn;

    private GameObject BtnAll;
    private GameObject BtnInTroop;
    private GameObject BtnRecruit;
    private GameObject BtnTroop;
    
    
    //private Color Color_Open, Color_Close;

    #endregion UI关联数据

    #region override methods

    protected override void OnInit()
    {
    }

    protected internal override void OnOpenStart()
    {
        base.OnOpenStart();
        _LevelUpHeroLst = new List<long>();
        _LevelUpHeroLstTmp = new List<long>();
        //_LevelUpCostHero = new List<UIHero_Item>();

        //资源条
        _topResBar = new TopResourceWidget(GetChild("Root/TopBar/TopResource"));
        _topResBar.SetData();
        //嵌套无限滚动
        loopListView = GetComponent<TFWLoopListView>("Root/Content/Scroll");
        _heroContent = GetChild("Root/Content");
        _closeBtn = GetChild("Root/TopBar/Close");
        _ButtomPanel = GetChild("Root/Buttom");
        _totalGoldText = GetComponent<TFWText>("Root/Buttom/Coin/TotalGoldNum");
        _costGoldText = GetComponent<TFWText>("Root/Buttom/Coin/Button/CostCoinNum");

        _noHeroTipText = GetComponent<TFWText>("Root/Buttom/Coin/Button/CostTip");
        _noHeroTipText.text = LocalizationMgr.Get("Hero_Consume_Noselect_Hero");
        //_diamondUpgradeBtn = UIMain.I?.HeroDiamondUpgradeButton;
        //if (_diamondUpgradeBtn == null) Debug.LogFormat("_diamondUpgradeBtn == null");
        //_diamondUpgradeTxt = UIHelper.GetComponent<TFWText>(_diamondUpgradeBtn, "Num");
        //_lockBtn = GetChild("Root/LockButton");

        //none tips
        _noneRoot = GetChild("Root/Content/NoneTip");
        //_noneButton = UIHelper.GetChild(_noneRoot, "button");
        //_noneButton = UIMain.I?.HeroGoToShopBtn;
        //_heroMainBtn = UIMain.I?.HeroBtn;
        //if (_noneButton == null) Debug.LogFormat("_noneButton == null");
        _noneText = UIHelper.GetComponent<TFWText>(_noneRoot, "text");

        //RemoveListener(KS.EventTriggerType.Click, _diamondUpgradeBtn);
        AddListener(EventTriggerType.Click, _closeBtn, CloseUI);

        _UIHero_InBattle = GetChild("Root/TopInfo");
        _rePowerBtn = GetChild("Root/TopInfo/ChangeHeroPanel/RePowerBtn");
        MessageMgr.RegisterMsg<LineupBuildHeroAck>(this, OnLineupBuildHeroAck);
        MessageMgr.RegisterMsg<DeputyHeroAck>(this, OnDeputyHeroAck);
        
        BtnAll = GetChild("Root/BtnAll");
        BtnInTroop = GetChild("Root/BtnInTroop");
        BtnRecruit = GetChild("Root/bottomGrid/BtnRecruit");
        BtnTroop = GetChild("Root/bottomGrid/BtnTroop");
 
        
        IsShowAll = true;

        //AddListener(KS.EventTriggerType.Click, BtnAll, BtnClick_BtnAll);
        //AddListener(KS.EventTriggerType.Click, BtnInTroop, BtnClick_BtnInTroop);

        AddListener(EventTriggerType.Click, BtnRecruit, BtnClick_BtnRecruit);

        //图鉴功能
        CollectFuncInit();
        //AddListener(KS.EventTriggerType.Click, BtnTroop, BtnClick_BtnTroop);
        OnShown();
      
    }

    private bool IsShowAll = false;

    private void BtnClick_BtnAll(GameObject arg0, PointerEventData arg1)
    {
        if (!IsShowAll)
        {
            IsShowAll = true;
            OnShown();
        }
    }

    private void BtnClick_BtnInTroop(GameObject arg0, PointerEventData arg1)
    {
        if (IsShowAll)
        {
            IsShowAll = false;
            OnShown();
        }
    }

    private void BtnClick_BtnRecruit(GameObject arg0, PointerEventData arg1)
    {
        HeroRecruitMgr.I.OpenHeroRecruit();
    }

    private void BtnClick_BtnTroop(GameObject arg0, PointerEventData arg1)
    {
        PopupManager.I.ShowLayer<UINewTroopSelect>(new UINewTroopSelectData() { curLineUpIndex = 0, marchArgs = null });
    }

    protected override void OnDispose()
    {
        LockState = false;
        // MapManager.I?.CanDragOnMap(true);
        //base.OnHidden();

        UnInitEvent();

        //if(_noneButton!=null)
        //_noneButton.SetActive(false);

        SetMainButtonVisible(true);

        //if(_diamondUpgradeBtn!=null)
        //_diamondUpgradeBtn.SetActive(false);

        //_isInBattleHero = false;
        OnDestroyed();
    }

    protected void OnDestroyed()
    {
        MessageMgr.UnregisterMsg(this);

        collectRewardsWidget?.Destroy();
        collectRewardsWidget = null;
        if (_LevelUpHeroLstTmp != null)
        {
            _LevelUpHeroLstTmp.Clear();
            _LevelUpHeroLstTmp = null;
        }

        if (_LevelUpHeroLst != null)
        {
            _LevelUpHeroLst.Clear();
            _LevelUpHeroLst = null;
        }
    }

    protected void OnShown()
    {
        //base.OnShown();

        _InBattleHeroLintUpId = -1;
        _InBattleHeroLineUpPos = 0;
        mBox = null;
        isMain = false;

        if (Data != null)
        {
            var heroListData = Data as UIHeroListData;
            if (heroListData != null)
            {
                _InBattleHeroLintUpId = heroListData.LineUpId;
                //  Debug.LogErrorFormat($"ToLineUpID:{_InBattleHeroLintUpId}   _InBattleHeroLineUpPos:{heroListData.Pos}");

                SetHeroCallBack = heroListData.UpHeroCallBack;
                CloseUICallBack = heroListData.CloseUICallBack;
                _InBattleHeroLineUpPos = heroListData.Pos;
                mBox = heroListData.mBox;
                isHeroList = heroListData.isHeroList;
                isMain = heroListData.isMain;
                mBoxHeroIndex = heroListData.mBoxHeroIndex;
                isHelpHero = heroListData.isHelpHero;
                mHelpHeroIndex = heroListData.mHelpHeroIndex;
            }
            else
            {
                SetHeroCallBack = null;
                CloseUICallBack = null;
            }
        }
        else
        {
            SetHeroCallBack = null;
            CloseUICallBack = null;
        }

        InitEvent();
        RefreshHeroList();
        SetCollectRewardWidget();
    }

    /// <summary>
    /// 初始化事件监听
    /// </summary>
    private void InitEvent()
    {
        EventMgr.RegisterEvent(TEventType.RefreshHeroData, OnRefreshHeroList, GameObject);
    }

    /// <summary>
    /// 移除事件监听
    /// </summary>
    private void UnInitEvent()
    {
        EventMgr.UnregisterEvent(GameObject);
    }

    #endregion override methods

    /// <summary>
    /// 界面关闭的方法
    /// </summary>
    private void CloseUI(GameObject arg0, PointerEventData arg1)
    {
        if (LockState)
        {
            SendHeroLockInfo();
        }
        CloseUICallBack?.Invoke();

        Pop();
    }

    /// <summary>
    /// 刷新英雄数据
    /// </summary>
    private void OnRefreshHeroList(object[] obj)
    {
        //_UIHeroItemsDic.Clear();
        OnShown();
    }

    /// <summary>
    /// 设置主UI上英雄按钮的显示处理
    /// </summary>
    private void SetMainButtonVisible(bool isShow)
    {
        if (_heroMainBtn == null)
            return;

        var anim = _heroMainBtn.GetComponent<Animation>();
        if (anim != null)
            anim.enabled = isShow;

        var tran = _heroMainBtn.GetComponent<Transform>();
        //if (tran == null)
        //    return;

        _heroMainBtn.GetComponent<Animation>().enabled = isShow;
        //Transform tran = _heroMainBtn.GetComponent<Transform>();
        Vector3 off = Vector3.one;
        if (!isShow)
            off = new Vector3(.01f, .01f, .01f);

        tran.localScale = off;
        var child = tran.GetChild(0).transform;
        child.localScale = off;
        child = tran.GetChild(1).transform;
        child.localScale = off;
    }

    /// <summary>
    /// 对子物体排序
    /// </summary>
    public Transform[] GetTransforms(GameObject parentGameObject)
    {
        if (parentGameObject != null)
        {
            List<UnityEngine.Component> components = new List<UnityEngine.Component>();
            for (int i = 0; i < parentGameObject.transform.childCount; i++)
            {
                components.Add(parentGameObject.transform.GetChild(i));
            }
            List<Transform> transforms = components.ConvertAll(c => (Transform)c);

            transforms.Remove(parentGameObject.transform);
            transforms.Sort(delegate (Transform a, Transform b)
            {
                return b.name.CompareTo(a.name);
            });

            return transforms.ToArray();
        }

        return null;
    }

    public TFWLoopListViewItem OnGetItemByIndex(TFWLoopListView listView, int index)
    {
        if (index < 0 || AllHeroItems == null || AllHeroItems.Count <= index)
        {
            return null;
        }

        if (AllHeroItems[index].heroDatas != null && AllHeroItems[index].heroDatas.Count > 0)
        {
            var itemTr = loopListView.NewListViewItem("Item");
            var UIGrid = itemTr.GetComponent<UIGrid>();
            UIGrid.Clear();

            for (int i = 0; i < AllHeroItems[index].heroDatas.Count; i++)
            {
                var itemData = UIGrid.AddItem<UIHeroListItem>();
                itemData.AddHeroLineUpIndex = _InBattleHeroLintUpId;
                itemData.AddHeroPos = _InBattleHeroLineUpPos;
                itemData.mBox = mBox;
                itemData.isMain = isMain;
                itemData.isHeroList = isHeroList;
                itemData.mBoxHeroIndex = mBoxHeroIndex;
                itemData.mHelpHeroIndex = mHelpHeroIndex;
                itemData.isHelpHero = isHelpHero;
                itemData.InitHero(AllHeroItems[index].heroDatas[i], AllHeroItems[index].troopUIIndex >= 0 && i == 0);
                UIHeroItemsLst.Add(itemData);

                if (LockState)
                {
                    itemData.SetSelState();
                }
            }

            return itemTr;
        }
        else
        {
            var item = loopListView.NewListViewItem("Title");
            if (LockState)
            {
                item.transform.Find("Text").GetComponent<TFWText>().text = LocalizationMgr.Get("HeroCard_LockOperation");
            }
            else
            {
                item.transform.Find("Text").GetComponent<TFWText>().text = AllHeroItems[index].tittle;
            }
            if (index == 0)
            {
                _lockBtn = UIMgr.GetChildByPath(item.gameObject, "LockButton");
                _lockBtn.SetActive(true);
                AddListener(EventTriggerType.Click, _lockBtn, OperationLockPanel);
                UIHelper.GetChild(_lockBtn, "Lock")?.SetActive(!LockState);
                UIHelper.GetChild(_lockBtn, "Unlock")?.SetActive(LockState);
            }
            else
            {
                UIMgr.GetChildByPath(item.gameObject, "LockButton").SetActive(false);
            }
            return item;
        }
    }

    public GameObject GetHeroItem(int modelID)
    {
        foreach (var item in UIHeroItemsLst)
        {
            if (item.mHeroData.ModelId == modelID)
                return item.gameObject;
        }

        return null;
    }

    /// <summary>
    /// 上阵英雄回调
    /// </summary>
    /// <param name="obj"></param>
    private void OnLineupBuildHeroAck(LineupBuildHeroAck ack)
    {
        if (ack.errCode == ErrCode.ErrCodeSuccess)
        {
            if (SetHeroCallBack != null)
            {
                SetHeroCallBack?.Invoke();
            }
            else //if(_InBattleHeroLintUpId!=-1)
            {
                //if (GameData.I.MainData.CurrMenuType == MainMenuType.HERO)
                //{
                //    /*
                //    WndMgr.Show<UIHeroUpgrade>(new UIHeroUpgradeData
                //    {
                //        lineUIIndex = GameData.I.LineUpData.lockedList.IndexOf(ack.indexId)   // _InBattleHeroLintUpId
                //    });*/
                //}
            }
        }
    }

    private void OnDeputyHeroAck(DeputyHeroAck ack)
    {
        if (ack.err == ErrCode.ErrCodeSuccess)
        {
            if (SetHeroCallBack != null)
            {
                SetHeroCallBack?.Invoke();
            }
            else //if(_InBattleHeroLintUpId!=-1)
            {
                //if (GameData.I.MainData.CurrMenuType == MainMenuType.HERO)
                //{
                //    /*
                //    WndMgr.Show<UIHeroUpgrade>(new UIHeroUpgradeData
                //    {
                //        lineUIIndex = GameData.I.LineUpData.GetUIIndexById(ack.lineupId)   // _InBattleHeroLintUpId
                //    });*/
                //}
            }
        }
    }

    /// <summary>
    /// 点击界面锁的按钮
    /// </summary>
    /// <param name="arg0"></param>
    /// <param name="arg1"></param>
    private void OperationLockPanel(GameObject arg0, PointerEventData arg1)
    {
        LockState = !LockState;
        UIHelper.GetChild(_lockBtn, "Lock")?.SetActive(!LockState);
        UIHelper.GetChild(_lockBtn, "Unlock")?.SetActive(LockState);
        if (LockState)
        {
            ShowHeroList();
        }
        else
        {
            SendHeroLockInfo();
        }
    }

    /// <summary>
    ///向服务器发送加锁和解锁的信息
    /// </summary>
    private void SendHeroLockInfo()
    {
        var req = new HeroLockReq();
        foreach (var item in UIHeroItemsLst)
        {
            if (item.SelToLock() != HeroGameData.I.GetLockedByModelID(item.mHeroData.ModelId))
            {
                if (item.SelToLock())
                {
                    req.lockedCfgIDs.Add(item.mHeroData.ModelId);
                }
                else
                {
                    req.unlockedCfgIDs.Add(item.mHeroData.ModelId);
                }
            }
        }
        if (req.lockedCfgIDs.Count > 0 || req.unlockedCfgIDs.Count > 0)
        {
            MessageMgr.Send(req);
        }

        RefreshHeroList();
    }

    private void RefreshHeroList()
    {
        //if (mBox != null)
        //{
        //    //上阵相册
        //    HeroGameData.I.GetMergeHeros(4, out AllHeroItems);
        //}
        //else if (isHelpHero)
        //{
        //    HeroGameData.I.GetMergeHeros(4, out AllHeroItems);
        //}
        //else if (isMain)
        //{
        //    HeroGameData.I.GetMainHeros(4, out AllHeroItems);
        //}
        //else if (SetHeroCallBack != null || _InBattleHeroLintUpId >= 0)
        //{
        //    HeroGameData.I.GetSolidersByNum(4, HeroGameData.HeroListEnum.Have, out AllHeroItems, (Data as UIHeroListData)?.ItemShouldShow);
        //}
        //else
        //{
        //    HeroGameData.I.GetSolidersByNum(4, HeroGameData.HeroListEnum.ALL, out AllHeroItems, (Data as UIHeroListData)?.ItemShouldShow);
        //}

        LockState = false;
        UIHelper.GetChild(_lockBtn, "Lock")?.SetActive(!LockState);
        UIHelper.GetChild(_lockBtn, "Unlock")?.SetActive(LockState);

        //BtnTroop.SetActive(UI.Utils.MainFunctionOpenUtils.WorldOpenState);

        ShowHeroList();
    }

    private List<HeroItemData> AllHeroItems;

    private void ShowHeroList()
    {
        UIHeroItemsLst.Clear();

        if (mBox != null)
        {
            //上阵相册
            var data = Data as UIHeroListData;
            if (data.mBox.heroCfgIDs[data.mBoxHeroIndex] != 0)
            {
                HeroGameData.I.GetMergeHeros(4, out AllHeroItems, true);
            }
            else
            {
                HeroGameData.I.GetMergeHeros(4, out AllHeroItems);
            }
        }
        else if (isHelpHero)
        {
            HeroGameData.I.GetHelpHeros(4, out AllHeroItems);
        }
        else if (isMain)
        {
            HeroGameData.I.GetMainHeros(4, out AllHeroItems);
        }
        else if (SetHeroCallBack != null || _InBattleHeroLintUpId >= 0)
        {
            HeroGameData.I.GetSolidersByNum(4, HeroGameData.HeroListEnum.Have, out AllHeroItems, (Data as UIHeroListData)?.ItemShouldShow);
        }
        else if (LockState)
        {
            HeroGameData.I.GetSolidersByNum(4, HeroGameData.HeroListEnum.Have, out AllHeroItems, (Data as UIHeroListData)?.ItemShouldShow);
        }
        else
        {
            HeroGameData.I.GetSolidersByNum(4, HeroGameData.HeroListEnum.ALL, out AllHeroItems, (Data as UIHeroListData)?.ItemShouldShow);
        }

        //执行筛选 delegate
        //var heroListData = Data as UIHeroListData;
        //if (heroListData != null && heroListData.ItemShouldShow != null)
        //{
        //    var filteredList = new List<HeroItemData>();
        //    foreach (var item in AllHeroItems)
        //    {
        //        if (heroListData.ItemShouldShow(item))
        //        {
        //            filteredList.Add(item);
        //        }
        //    }
        //    AllHeroItems = filteredList;
        //}

        if (loopListView.ListViewInited)
        {
            AllHeroItems = HeroListOrder(AllHeroItems);

            //loopListView.InitListView(AllHeroItems.Count, OnGetItemByIndex);
            loopListView.SetListItemCount(AllHeroItems.Count);
            loopListView.RefreshAllShownItemWithFirstIndex(0);
            loopListView.MovePanelToItemIndex(0, 0);
        }
        else
        {
            AllHeroItems = HeroListOrder(AllHeroItems);

            loopListView.InitListView(AllHeroItems.Count, OnGetItemByIndex);
        }
        
        BtnAll.transform.Find("Choose").transform.localScale = Vector3.one * (IsShowAll ? 1 : 0);
        BtnInTroop.transform.Find("Choose").transform.localScale = Vector3.one * (IsShowAll ? 0 : 1);
        //BtnAll.GetComponentInChildren<TFWText>().color = IsShowAll ? Color_Open : Color_Close;
        //BtnInTroop.GetComponentInChildren<TFWText>().color = IsShowAll ? Color_Close : Color_Open;
        //BtnAll.GetComponentInChildren<BetterOutline>().effectColor = new Color(1, 1, 1, IsShowAll ? 1 : 0);
        //BtnInTroop.GetComponentInChildren<BetterOutline>().effectColor = new Color(1, 1, 1, IsShowAll ? 0 : 1);
    }
    

    /// <summary>
    /// 英雄列表按照已上阵>高品质>高等级>ID排序
    /// </summary>
    private List<HeroItemData> HeroListOrder(List<HeroItemData> list_old)
    {
        List<HeroItemData> list_new = new List<HeroItemData>();

        List<HeroData> list_unlock = new List<HeroData>();
        List<HeroData> list_locked = new List<HeroData>();

        //解除结构
        if (list_old.Count > 0)
        {
            for (int i = 0; i < list_old.Count; i++)
            {
                var herolist = list_old[i];

                if (herolist?.heroDatas?.Count>0)
                {
                    foreach (var item in herolist.heroDatas)
                    {
                        if(item==null)
                            continue;

                        long count = HeroGameData.I.GetBumByModelID(item.ModelId);
                        if (count > 0)
                        {
                            if (IsShowAll)
                            {
                                list_unlock.Add(item);
                            }
                            else if (GameData.I.LineUpData.HeroIsInBattle(item) >= 0)
                            {
                                list_unlock.Add(item);
                            }
                        }
                        else
                        {
                            list_locked.Add(item);
                        }
                    }
                }
            }
        }
        //重新排序

        //已解锁列表按照【已上阵>高品质>高等级>ID】排序
        if (list_unlock.Count > 0)
        {
            list_unlock = list_unlock.OrderByDescending(t => t.HeroCfg.HeroType).ThenByDescending(t => t.Level).ToList();//OrderByDescending(t => GameData.I.LineUpData.HeroIsInBattle(t))
        }

        //未解锁列表按照【高品质>高等级>ID】排序
        if (list_locked.Count > 0)
        {
            list_locked = list_locked.OrderByDescending(t => t.HeroCfg.HeroType).ThenByDescending(t => t.Level).ToList();
        }
        /*
        foreach (var item in list_unlock)
        {
            Debug.LogError(LocalizationMgr.Get(item.HeroCfg.Name) + " 品质：" + item.HeroCfg.HeroType);
        }
        */

        //重组
        //KS.Localization.LocalizationMgr.Get("Hero_Text_17")
        List<HeroData> list = new List<HeroData>();
        /*list_new.Add(new HeroItemData
        {
            tittle = LocalizationMgr.Get("Hero_Text_17"),
            troopUIIndex = -1
        });*/

        //合并到一起，解锁和未解锁不再单独显示。提需求的策划Lyon 梁尧 修改的程序 yujiawei
        if (list_locked.Count > 0)
        {
            list_unlock.AddRange(list_locked);
        }

        if (list_unlock.Count > 0)
        {
            for (int i = 0; i < list_unlock.Count; i++)
            {
                list.Add(list_unlock[i]);
                if (list.Count >= 4 || i == list_unlock.Count - 1)
                {
                    HeroItemData data = new HeroItemData();
                    data.troopUIIndex = -1;
                    data.heroDatas = new List<HeroData>();
                    foreach (var item in list)
                    {
                        data.heroDatas.Add(item);
                    }

                    list_new.Add(data);
                    list.Clear();
                }
            }
        }
        /*if (IsShowAll)
        {
            /*list_new.Add(new HeroItemData
            {
                tittle = LocalizationMgr.Get("Hero_Text_18"),
                troopUIIndex = -1
            });#1#
            list.Clear();

            for (int i = 0; i < list_locked.Count; i++)
            {
                list.Add(list_locked[i]);
                if (list.Count >= 4 || i == list_locked.Count - 1)
                {
                    HeroItemData data = new HeroItemData();
                    data.troopUIIndex = -1;
                    data.heroDatas = new List<HeroData>();
                    foreach (var item in list)
                    {
                        data.heroDatas.Add(item);
                    }

                    list_new.Add(data);
                    list.Clear();
                }
            }
        }*/

        return list_new;
    }

    protected internal override bool Pop()
    {
        ////打开其他界面
        //if (WorldSwitchMgr.I.ShowWorldType == WorldTypeEnum.CITY)
        //{
        //    //需要切回菜单
        //    //UIMain.I?.TrySwitch(MainMenuConst.TRAIN);
        //    EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.CITY);
        //}
        //else
        //{
        //    //需要切回菜单
        //    //UIMain.I?.TrySwitch(MainMenuConst.WORLD);
        //    EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.WORLD);
        //}
        Close();

        return true;
    }

    public class HeroItemData
    {
        public string tittle;

        public int troopUIIndex = -1;

        public List<Game.Data.HeroData> heroDatas;
    }
}

//MODIFIED BY UIBASE2POPUP
//MODIFIED BY UIBASE2POPUP
//MODIFIED BY UIBASE2POPUP