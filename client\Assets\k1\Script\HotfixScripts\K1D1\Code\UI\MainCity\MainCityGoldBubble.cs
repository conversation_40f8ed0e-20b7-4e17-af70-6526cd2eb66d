﻿using Cfg;
using Common;
using cspb;
using Game.Config;
using K3;
using TFW;
using TFW.UI;
using Logic;
using System.Collections;
using System.Collections.Generic;
using UI;
using UnityEngine;
using Cysharp.Threading.Tasks;

public class MainCityGoldBubble : MonoBehaviour
{
    public GameObject bubbleObj;
    public GameObject bg;
    public TFWImage fillTFWImage;
    public Animator animator;
    public TFWText goldValue;

    public int techID;

    private void OnEnable()
    {
        EventMgr.RegisterEvent(TEventType.K3OfflineGoldPanelRefresh, (objs) => { Refresh(); }, this);
        EventMgr.RegisterEvent(TEventType.OnUnlockConditionChanged, (objs) => { Refresh(); }, this);
      
        FrameUpdateMgr.RegisterLateUpdate(this, FrameUpdate);

        Refresh();

        UIBase.AddRemoveListener(TFW.EventTriggerType.Click, bubbleObj, (x, y) =>
        {
            List<TypIDVal> list = new List<TypIDVal>();
            if ((GameTime.Time - LOfflineRevenue.I.ReceiveTime(techID)) >= LOfflineRevenue.I.DisplayBubbleCDTime)
            {
                // 领取
                LOfflineRevenue.I.SoldierCollect(techID);
                // 假表现
                list.Clear();
                list.Add(new TypIDVal()
                {
                    ID = Config.ConfigID.VM_Gold,
                    typ = AssetType.Vm,
                    val = 999999
                });
                Public.UIHelper.FlyItems(list, bubbleObj.transform.position, true);
            }
        });
    }

    private void OnDisable()
    {
        FrameUpdateMgr.UnregisterLateUpdate(this);

        EventMgr.UnregisterEvent(this);
    }


    private void Refresh()
    { 
        timeRefreshGold = 0;
        FrameUpdate(0); 
    }

    float timeRefreshGold = 0;
    // Update is called once per frame
    async UniTaskVoid FrameUpdate(float t)
    {
        if ((GameTime.Time - LOfflineRevenue.I.ReceiveTime(techID)) >= LOfflineRevenue.I.DisplayBubbleCDTime)
        {
            bubbleObj.SetActive(true);
             
            if (await LOfflineRevenue.I.MaxValue(techID))
            {
                //值已满
                bg.SetActive(true);

                fillTFWImage.fillAmount = 0;

                animator.enabled = true;

                goldValue.text = UIStringUtils.FormatIntUnitByLanguage((long) await LOfflineRevenue.I.GoldMaxValue(techID), 2);
            }
            else
            {
                bg.SetActive(false);
                fillTFWImage.fillAmount = (timeRefreshGold % 5) / 5;
                animator.enabled = false;
                bubbleObj.transform.localEulerAngles = Vector3.zero;

                if (timeRefreshGold >= 5 || timeRefreshGold == 0 || t==0)
                {
                    timeRefreshGold = 0;

                    goldValue.text = UIStringUtils.FormatIntUnitByLanguage((long) await LOfflineRevenue.I.CurValue(techID), 2);
                }
            }

            timeRefreshGold += t;
        }
        else
        {
            bubbleObj.SetActive(false);
        }
    }
}
