﻿//using Common;
//using Cysharp.Threading.Tasks;
//using DG.Tweening;
//using Game.Data;
//using Game.Sprite.Fight;
//using Public;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;
//using UnityEngine;

//namespace UI
//{

//    /// <summary>
//    /// 主界面菜单栏基础数据
//    /// </summary>
//    public class UIMainMenuFunctionBase
//    {

//        #region 属性字段信息数据

//        /// <summary>
//        /// 正向播放动画名字
//        /// </summary>
//        //private const string MENU_PLAY_NAME = "MainMenuBtnAnim";

//        /// <summary>
//        /// 反向播放动画名字
//        /// </summary>
//        //private const string MENU_REVERSE_NAME = "MainMenuBtnAnimReverse";

//        /// <summary>
//        /// 菜单栏类型
//        /// </summary>
//        public MainMenuType MenuType { get; private set; }

//        /// <summary>
//        /// root跟节点对象
//        /// </summary>
//        public GameObject Root { get; private set; }

//        /// <summary>
//        /// 菜单栏数据信息
//        /// </summary>
//        public DeepUI.BasePopupLayer Main { get; private set; }

//        /// <summary>
//        /// 锁定动画
//        /// </summary>
//        protected Animation lockAnim;
//        /// <summary>
//        /// 解锁正式显示图标对象
//        /// </summary>
//        protected GameObject UnlockObj;
//        protected GameObject UnlockObj2;
//        #endregion


//        #region 初始化

//        /// <summary>
//        /// 初始化信息
//        /// </summary>
//        /// <param name="type"></param>
//        /// <param name="root"></param>
//        /// <param name="data"></param>
//        public UIMainMenuFunctionBase(MainMenuType type, GameObject root, DeepUI.BasePopupLayer main)
//        {
//            MenuType = type;
//            Root = root;
//            Main = main;

//            //初始化
//            Init();

//            //添加事件监听
//            RegisterEvents();
//        }

//        /// <summary>
//        /// 初始化
//        /// </summary>
//        protected virtual void Init()
//        {
//            lockAnim = UIHelper.GetComponent<Animation>(Root, "Lock");
//            UnlockObj = UIHelper.GetChild(Root, "Icon");
//            UnlockObj2 = UIHelper.GetChild(Root, "bluebg");
//        }

//        /// <summary>
//        /// 注册事件信息
//        /// </summary>
//        protected virtual void RegisterEvents() { }

//        /// <summary>
//        /// 取消事件监听
//        /// </summary>
//        protected virtual void UnRegisterEvents()
//        {
//            EventMgr.UnregisterEvent(this);
//        }

//        #endregion


//        #region 数据刷新处理

//        public void PlayLockAni()
//        {
//            if (lockAnim != null)
//                lockAnim.Play();
//        }



//        /// <summary>
//        /// 点击当前菜单栏按钮
//        /// </summary>
//        public virtual void ClickCurrMenuBtn(UIData data = null)
//        {

//        }

//        /// <summary>
//        /// 点击菜单栏按钮
//        /// </summary>
//        /// <param name="data"></param>
//        /// <returns>是否成功点击菜单按钮</returns>
//        public virtual UniTask<bool> ClickMenuBtn(UIData data = null)
//        {
//            return UniTask.FromResult(true);
//        }

//        /// <summary>
//        /// 进入菜单对应界面
//        /// </summary>
//        public virtual void EnterMenuPanel()
//        {
//            if (UnlockObj2)
//                UnlockObj2.SetActive(false);

//            //播放进入动画
//            PlayEnterAnim();

//            if (MenuType != MainMenuType.WORLD)
//            {
//                WndMgr.Hide<UISearchArrow>();
//                WndMgr.Hide<UIMapPopup>();
//                WndMgr.Hide<UIAllianceBuilding>();
//                WndMgr.Hide<UIBuildingHullTeleport>();
//                WndMgr.Hide<UIAllianceBossPopup>();
//                WndMgr.Hide<UIJumpPopup>();
//            }
//        }

//        /// <summary>
//        /// 离开界面对应界面
//        /// </summary>
//        /// <param name="nextType"></param>
//        public virtual void ExitMenuPanel(MainMenuType nextType)
//        {
//            if (UnlockObj2)
//                UnlockObj2.SetActive(true);

//            //播放退出动画
//            PlayExitAnim();
//        }

//        /// <summary>
//        /// 播放菜单退出动画
//        /// </summary>
//        protected virtual void PlayExitAnim()
//        {
//            //if (Main is UIMain1)
//            //{
//            //    var data = (Main as UIMain1).GetMenuData(MenuType);
//            //    if (data != null && data.anim != null)
//            //    {
//            //        var clip = data.anim.GetClip(MENU_REVERSE_NAME);
//            //        if (clip != null)
//            //            data.anim.Play(MENU_REVERSE_NAME);
//            //    }
//            //}
//            //else 
//            //if (Main is UIMain2)
//            //{
//            //    var data = (Main as UIMain2).GetMenuData(MenuType);
//            //    if (data != null && data.anim != null)
//            //    {
//            //        var clip = data.anim.GetClip(MENU_REVERSE_NAME);
//            //        if (clip != null)
//            //            data.anim.Play(MENU_REVERSE_NAME);
//            //    }
//            //}
//        }

//        /// <summary>
//        /// 播放菜单进入动画
//        /// </summary>
//        protected virtual void PlayEnterAnim()
//        {
//            //if (Main is UIMain1)
//            //{
//            //    var data = (Main as UIMain1).GetMenuData(MenuType);
//            //    if (data != null && data.anim != null)
//            //    {
//            //        var clip = data.anim.GetClip(MENU_PLAY_NAME);
//            //        if (clip != null)
//            //            data.anim.Play(MENU_PLAY_NAME);
//            //    }
//            //}
//            //else 
//            //if (Main is UIMain2)
//            //{
//            //    var data = (Main as UIMain2).GetMenuData(MenuType);
//            //    if (data != null && data.anim != null)
//            //    {
//            //        var clip = data.anim.GetClip(MENU_PLAY_NAME);
//            //        if (clip != null)
//            //            data.anim.Play(MENU_PLAY_NAME);
//            //    }
//            //}
//        }

       

//        /// <summary>
//        /// 刷新菜单开放状态
//        /// </summary>
//        public virtual void UpdateMenuOpenState() { }

//        /// <summary>
//        /// 菜单栏解锁
//        /// </summary>
//        public void UnLockMenu()
//        {
//            if (lockAnim != null)
//            {
//                lockAnim.gameObject.SetActive(false);
//            }

//            if (UnlockObj != null)
//            {
//                UnlockObj.SetActive(true);
//                var tran = UnlockObj.transform;
//                tran.DOScale(1.2f, 0.36f);
//                tran.DOScale(1f, 0.16f).SetDelay(0.6f);
//            }
//        }

//        #endregion


//        #region 数据清理

//        /// <summary>
//        /// 数据清理
//        /// </summary>
//        public virtual void Clear()
//        {
//            //取消事件监听
//            UnRegisterEvents();
//        }


//        #endregion

//    }
//}
