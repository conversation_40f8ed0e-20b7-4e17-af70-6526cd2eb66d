﻿ 
using Common;
using cspb;
using DeepUI;
using DG.Tweening;
using Game.Config;
using Game.Data;
using KS;
using Logic;
using System;
using System.Collections;
using System.Collections.Generic;
using TFW;
using UI;
using UnityEngine;
using UnityEngine.EventSystems;

/// <summary>
/// 图片拖拽组件
/// 记录：UI相机模式下生效，Anchors(0.5, 0.5)(0.5, 0.5) Pivot(0.5, 0.5)
/// @date 2021/8/18 17:06:58
/// @ver 1.0
/// </summary>
public class UIDragImgNew : MonoBehaviour //, IBeginDragHandler, IDragHandler, IEndDragHandler
{

    [Header("是否启用缩放")]
    public bool enableZoom = true;

    public float scaleMinValue, scaleMaxValue;

    public float scaleValue = 1; //缩放系数

    private float normalScaleValue;
    private Vector3 normalPops;

    public bool scaleChile = true;//针对子物体缩放, 子物体缩放则不会自适应缩放后的拖动区域
    public Transform scaleTrans;
    public float scaleValueCache = 1; //非子物体缩放时该值等于scaleValue

    public bool scaleChange = false;

    public RectTransform rt;

    // 位置偏移量
    Vector3 offset = Vector3.zero;
    // 最小、最大X、Y坐标
    float minX, maxX, minY, maxY;

    private bool lockLimit = false;

    //这个变量用来记录单指双指的变换
    private bool m_IsSingleFinger;
    //记录上一次手机触摸位置判断用户是在左放大还是缩小手势
    private Vector2 oldPosition1;
    private Vector2 oldPosition2;

    private Vector2 lastSingleTouchPosition;

    public bool responseDamp = false; //是否响应阻尼效果
    public Vector3 dampSpeed;//阻尼速率

    public bool lookAt = false;//是否移动到目标
    public bool isScaleIng = false;  //是否正在缩放中

    Vector2 prevMousePos;//上次拖拽位置

    public bool ClickIgnoreArea = false; //是否点击到不可操作区域
    public RectTransform mask;
  

    /// <summary>
    /// 拖动调用
    /// </summary>
    public Action<bool, Vector3> MoveAction;

    void Awake()
    {

        scaleTrans = transform;
        rt = GetComponent<RectTransform>();
        normalScaleValue = scaleMinValue;// scaleValue;
        normalPops = Vector3.zero;// rt.transform.localPosition;
    }

    void OnEnable()
    {
        //EventMgr.RegisterEvent(TEventType.CityBuildingRefresh, (objs) =>
        //{
        //    ResetData();
        //}, this);

        EventMgr.RegisterEvent(TEventType.ClickToShowCityBuilding, (objs) =>
        {
            ResetData();
        }, this);

        ResetData();

        NTimer.CountDown(0.1f, () =>
        {
            rt.position = DragRangeLimit(rt.position);//不能出框判断
        });
    }

    private bool canDrag;


    public void ResetData()
    {

        D.Warning?.Log($"ResetData[rt]");

        var cfgID = LPlayer.I.GetNowCityBuildingID();

        scaleTrans.localScale = new Vector3(normalScaleValue, normalScaleValue, 1);
        rt.transform.localPosition = normalPops;


        canDrag = cfgID == 0;
        if (cfgID > 0)
        {
            scaleTrans.localScale = Math.Clamp(Cfg.C.CD2CityBuilding.I(cfgID).Scaling, scaleMinValue, scaleMaxValue)  * Vector3.one;
            rt.transform.localPosition = JsonUtility.FromJson<Vector3>(Cfg.C.CD2CityBuilding.I(cfgID).Focus);


            rt.position = DragRangeLimit(rt.position);//不能出框判断
        }

    }

    bool moveing = false;
    public void MoveTOCfg(int cfgID, System.Action callBack)
    {
        D.Warning?.Log($"MoveToCfg[rt]:{cfgID}");

        moveing = true;
     

        var toScale = Math.Clamp(Cfg.C.CD2CityBuilding.I(cfgID).Scaling ,scaleMinValue,scaleMaxValue)* Vector3.one;
       
        scaleTrans.DOScale(toScale, UIGuid.moveTime).SetEase(Ease.Linear);

        var toPos = JsonUtility.FromJson<Vector3>(Cfg.C.CD2CityBuilding.I(cfgID).Focus);
        rt.DOLocalMove(toPos, UIGuid.moveTime).SetEase(Ease.Linear).OnUpdate<Tween>(() =>
        { 
            rt.position = DragRangeLimit(rt.position);//不能出框判断
        }).OnComplete<Tween>(() =>
        {
            moveing = false;
            rt.position = DragRangeLimit(rt.position);//不能出框判断
            callBack.Invoke();
        });
    }

    /// <summary>
    /// 指定移动到某个位置
    /// </summary>
    /// <param name="toScale"></param>
    /// <param name="position"></param>
    //public void MoveToPos(Vector3 toScale, Vector3 toPos, System.Action callBack)
    //{
    //    moveing = true;
    //    scaleTrans.DOScale(toScale, UIGuid.moveTime).SetEase(Ease.Linear);
    //    rt.DOLocalMove(toPos, UIGuid.moveTime).SetEase(Ease.Linear).OnUpdate<Tween>(() =>
    //    {
    //        rt.position = DragRangeLimit(rt.position); //不能出框判断
    //    }).OnComplete<Tween>(() =>
    //    {
    //        moveing = false;
    //        rt.position = DragRangeLimit(rt.position); //不能出框判断
    //        callBack.Invoke();
    //    });
    //}


    void OnDisable()
    {
        EventMgr.UnregisterEvent(this);
    }

    void Start()
    {

    }

    private bool CanDrag()
    { 
        if (PopupManager.I.AnyPopupExitPopup<UIMainCity>())
            return false;
        // 新增强制引导时拖拽权限
        // if (GuidManage.ForcedGuidanceDragCheck())
        //     return false;

        return true;
    }

    void Update()
    {
        if (moveing)
        {
            return;
        }

        if (!CanDrag())
            return;
 
          
        #region 拖动阻尼效果

#if UNITY_EDITOR
        if (Input.GetMouseButtonDown(0))
        {
            prevMousePos = Input.mousePosition;

            Vector3[] worldcorners = new Vector3[4];
            mask.GetWorldCorners(worldcorners);
            if (!ScreenToWorldPoint(Input.mousePosition, out var inputPos))
            {
                D.Info?.Log($"LogDebug - ScreenToWorldPoint1");
                return;
            }

            if (inputPos.x > worldcorners[2].x || inputPos.x < worldcorners[0].x
                || inputPos.y > worldcorners[1].y)
                ClickIgnoreArea = true;
        }

        if (Input.GetMouseButton(0))
        {
            if (!ScreenToWorldPoint(Input.mousePosition, out var inputPos))
            {
                D.Info?.Log($"LogDebug - ScreenToWorldPoint2");
                return;
            }

            if (!ScreenToWorldPoint(prevMousePos, out var prevPos))
            {
                D.Info?.Log($"LogDebug - ScreenToWorldPoint3");
                return;
            }
            dampSpeed = (inputPos - prevPos) / Time.deltaTime;
            prevMousePos = Input.mousePosition;
            MoveAction?.Invoke(true, prevMousePos);
        }
        if (Input.GetMouseButtonUp(0))
        {
            if (ClickIgnoreArea)
            {
                ClickIgnoreArea = false;
                return;
            }

            prevMousePos = Input.mousePosition;  //给移动目标使用
            responseDamp = true;
            MoveAction?.Invoke(false, Vector3.zero);
        }
#else
        int touchCount = Input.touchCount;
        if (touchCount == 1)
        {
            var touch = Input.GetTouch(0);

            if (touch.phase== TouchPhase.Began)
            {
                if (RectTransformUtility.ScreenPointToWorldPointInRectangle(rt, touch.position, GameInstance.UICamera, out Vector3 globalMousePos))
                {
                    // 计算偏移量
                    offset = rt.position - globalMousePos;
                    responseDamp = false;
                }
                prevMousePos = touch.position;
                
            }
            else if (touch.phase == TouchPhase.Moved)
            {
                // 将屏幕空间上的点转换为位于给定RectTransform平面上的世界空间中的位置
                if (RectTransformUtility.ScreenPointToWorldPointInRectangle(rt, touch.position, GameInstance.UICamera, out Vector3 globalMousePos))
                {
                    rt.position = DragRangeLimit(globalMousePos + offset);
                }

                dampSpeed = (GameInstance.UICamera.ScreenToWorldPoint(touch.position) -
                    GameInstance.UICamera.ScreenToWorldPoint(prevMousePos)) / Time.deltaTime;

                prevMousePos = touch.position;
                MoveAction?.Invoke(true, prevMousePos);
            }
            else if (touch.phase ==  TouchPhase.Ended || touch.phase== TouchPhase.Canceled)
            {
                prevMousePos = touch.position;
                responseDamp = true;
                MoveAction?.Invoke(false, Vector3.zero);
            }
        }
        else  if (touchCount > 1)
        {
            responseDamp = false;
        }
#endif
        #endregion

        #region 缩放逻辑
        if (enableZoom)
        {
#if UNITY_EDITOR
            var axis = Input.GetAxis("Mouse ScrollWheel") * 0.2f;
            if (axis != 0)
            {
                scaleValue += axis;
                if (scaleValue >= scaleMaxValue)
                    scaleValue = scaleMaxValue;
                if (scaleValue <= scaleMinValue)
                    scaleValue = scaleMinValue;
                lockLimit = false;
                isScaleIng = true;
                responseDamp = false;
            }
#else
        //判断触摸数量为单点触摸
        //if (Input.touchCount == 1)
        //{
        //    if (Input.GetTouch(0).phase == TouchPhase.Began || !m_IsSingleFinger)
        //    {
        //        //在开始触摸或者从两字手指放开回来的时候记录一下触摸的位置
        //        lastSingleTouchPosition = Input.GetTouch(0).position;
        //        prevMousePos = Input.mousePosition;

        //        Vector3[] worldcorners = new Vector3[4];
        //        mask.GetWorldCorners(worldcorners);
        //        var inputPos = GameInstance.UICamera.ScreenToWorldPoint(Input.mousePosition);
        //        if (inputPos.x > worldcorners[2].x || inputPos.x < worldcorners[0].x
        //            || inputPos.y > worldcorners[1].y)
        //            ClickIgnoreArea = true;
        //    }
        //    if (Input.GetTouch(0).phase == TouchPhase.Moved)
        //    {
        //        //单指操作无视，改为拖拽图片逻辑了
        //        dampSpeed = (GameInstance.UICamera.ScreenToWorldPoint(Input.mousePosition) -
        //            GameInstance.UICamera.ScreenToWorldPoint(prevMousePos)) / Time.deltaTime;
        //        prevMousePos = Input.mousePosition;
        //    }
        //    if(Input.GetTouch(0).phase == TouchPhase.Ended)
        //    {
        //        if (ClickIgnoreArea)
        //        {
        //            ClickIgnoreArea = false;
        //            return;
        //        }
        //        responseDamp = true;
        //    }
        //    m_IsSingleFinger = true;
        //}
        //else 
        if (Input.touchCount > 1)
        {
            responseDamp = false;
            isScaleIng = true;
            //当从单指触摸进入多指触摸的时候,记录一下触摸的位置
            //保证计算缩放都是从两指手指触碰开始的
            if (Input.GetTouch(0).phase == TouchPhase.Began || Input.GetTouch(1).phase == TouchPhase.Began)
            {
                oldPosition1 = Input.GetTouch(0).position;
                oldPosition2 = Input.GetTouch(1).position;
            }

            if (Input.GetTouch(0).phase == TouchPhase.Moved || Input.GetTouch(1).phase == TouchPhase.Moved)
            {
                //计算出当前两点触摸点的位置
                var tempPosition1 = Input.GetTouch(0).position;
                var tempPosition2 = Input.GetTouch(1).position;

                float currentTouchDistance = Vector3.Distance(tempPosition1, tempPosition2);
                float lastTouchDistance = Vector3.Distance(oldPosition1, oldPosition2);

                //计算上次和这次双指触摸之间的距离差距
                scaleValue += (currentTouchDistance - lastTouchDistance) * Time.deltaTime * 0.1f;
                if (scaleValue >= scaleMaxValue)
                    scaleValue = scaleMaxValue;
                if (scaleValue <= scaleMinValue)
                    scaleValue = scaleMinValue;

                D.Info?.Log($"LogDebug - 初始点： = {oldPosition1}, {oldPosition2}, {lastTouchDistance} 缩放点： = {tempPosition1}, {tempPosition1}, {currentTouchDistance}, 缩放比例 = {scaleValue}, 缩放值 = {(currentTouchDistance - lastTouchDistance) * Time.deltaTime * 0.1f}");

                //备份上一次触摸点的位置，用于对比
                oldPosition1 = tempPosition1;
                oldPosition2 = tempPosition2;
            }
            m_IsSingleFinger = false;
        }
#endif

            if (isScaleIng)
            {
                scaleTrans.localScale = new Vector3(scaleValue, scaleValue, 1);
                if (scaleChile)
                    scaleValueCache = 1;
                else
                    scaleValueCache = scaleValue;

                isScaleIng = false;

                //TODO 超出参数 进行滑动

                rt.position = DragRangeLimit(rt.position);
            }

        }
        #endregion

        if (responseDamp && Vector3.Magnitude(dampSpeed) != 0)
        {
            dampSpeed *= Mathf.Pow(0.01f, Time.deltaTime);

            if (Mathf.Abs(Vector3.Magnitude(dampSpeed)) < 1)
            {
                responseDamp = false;
                dampSpeed = Vector3.zero;
            }
            Vector3 pos = DragRangeLimit(rt.position + dampSpeed * Time.deltaTime);
            rt.position = pos;
        }

    }

    private bool ScreenToWorldPoint(Vector3 pos, out Vector3 resultPos)
    {
        resultPos = Vector3.zero;
        if (GameInstance.UICamera == null)
            return false;

        if (!GameInstance.UICamera.orthographic)
            GameInstance.UICamera.Render();

        try
        {
            resultPos = GameInstance.UICamera.ScreenToWorldPoint(pos);
        }
        catch (Exception e)
        {
            resultPos = Vector3.zero;
            Debug.LogErrorFormat($"{e.Message}");
            return false;
        }

        return true;
    }

    //public void OnBeginDrag(PointerEventData eventData)
    //{
    //    if (!CanDrag())
    //        return;

    //    if (isScaleIng)
    //        return;

    //    if (ClickIgnoreArea)
    //        return;

    //    if (!isOperation)
    //        return;

    //    if (Input.touchCount > 1)
    //        return;

    //    if (RectTransformUtility.ScreenPointToWorldPointInRectangle(rt, eventData.position, eventData.enterEventCamera, out Vector3 globalMousePos))
    //    {
    //        // 计算偏移量
    //        offset = rt.position - globalMousePos;
    //        responseDamp = false;
    //    }
    //}

    // public void OnDrag(PointerEventData eventData)
    //{
    //    if (!CanDrag())
    //        return;

    //    if (isScaleIng)
    //        return;

    //    if (ClickIgnoreArea)
    //        return;

    //    if (!isOperation)
    //        return;

    //    if (Input.touchCount > 1)
    //        return;

    //    // 将屏幕空间上的点转换为位于给定RectTransform平面上的世界空间中的位置
    //    if (RectTransformUtility.ScreenPointToWorldPointInRectangle(rt, eventData.position, eventData.pressEventCamera, out Vector3 globalMousePos))
    //    {
    //        // 设置拖拽范围
    //        //if (!lockLimit)

    //        rt.position = DragRangeLimit(globalMousePos + offset);
    //    }


    //}

    //public void OnEndDrag(PointerEventData eventData)
    //{
    //    if (!CanDrag())
    //        return;

    //    if (isScaleIng)
    //        return;

    //    if (ClickIgnoreArea)
    //        return;

    //    if (!isOperation)
    //        return;

    //    if (Input.touchCount > 1)
    //        return;
    //}

    // 设置最大、最小坐标
    void SetDragRange()
    {
        //if (limit)
        //{
        //限制区域坐标

        var pos1 = GameInstance.UICamera.ScreenToWorldPoint(new Vector3(Screen.width, Screen.height, 0));
        var pos2 = GameInstance.UICamera.ScreenToWorldPoint(new Vector3(0, 0, 0));

        //屏幕左下、右上、中心坐标

        //if (scaleValueCache >= 1)
        //{
        //    Vector3[] worldcorners = new Vector3[4];
        //    limit.GetWorldCorners(worldcorners);//左下、左上、右上、右下

        //    minX = worldcorners[0].x * scaleValueCache - pos2.x + scaleValueCache * (pos3.x - limit.transform.position.x);//左下 - 屏幕左下 + 中心坐标偏移
        //    maxX = worldcorners[2].x * scaleValueCache - pos1.x + scaleValueCache * (pos3.x - limit.transform.position.x);//右上 - 屏幕右上 + 中心坐标偏移
        //    minY = worldcorners[0].y * scaleValueCache - pos2.y + scaleValueCache * (pos3.y - limit.transform.position.y);//左下 - 屏幕左下 + 中心坐标偏移
        //    maxY = worldcorners[2].y * scaleValueCache - pos1.y + scaleValueCache * (pos3.y - limit.transform.position.y);//右上 - 屏幕右上 + 中心坐标偏移

        //    minY = minY - 1f * scaleValueCache;
        //}
        //else
        //{

        var pos4 = GameInstance.UICamera.ScreenToWorldPoint(RectTransformUtility.WorldToScreenPoint(GameInstance.UICamera, rt.position));

        Vector3[] corners = new Vector3[4];
        rt.GetWorldCorners(corners);//左下、左上、右上、右下

        float width = pos4.x - corners[0].x;
        maxX = pos2.x + width;
        minX = pos1.x - width;

        float height = pos4.y - corners[0].y;

        maxY = pos2.y + (height * 1.1f);
        // maxY = pos2.y + height;
        minY = pos1.y - (height * 1.1f);
        // minY = pos1.y - height;

        //if (corners[0].x > pos2.x)
        //{
        //    minX = corners[0].x + pos2.x;
        //    maxX = pos1.x - pos2.x + Mathf.Abs(corners[0].x);
        //}
        //else if (corners[2].x < pos1.x)
        //{
        //    minX = pos2.x + corners[2].x;
        //    maxX = corners[2].x - pos2.x;
        //}
        //else
        //{
        //    minX = pos2.x;
        //    maxX = pos1.x;
        //}

        //if (corners[0].y > pos2.y)
        //{
        //    minY = corners[0].y;
        //    maxY = pos1.y;
        //}
        //else if (corners[2].y < pos1.y)
        //{
        //    minY = pos2.y;
        //    maxY = corners[2].y;
        //}
        //else
        //{
        //    minY = pos2.y;
        //    maxY = pos1.y;
        //}

        //    minY = minY - 1f * scaleValueCache;
        //}


        //    lockLimit = true;
        //}
        //else
        //{
        //    //没有限制区域，则使用屏幕区域限制
        //    minX = rt.rect.width / 2;
        //    maxX = Screen.width - minX;
        //    minY = rt.rect.height / 2;
        //    maxY = Screen.height - minY;
        //}

    }

    // 限制坐标范围
    Vector3 DragRangeLimit(Vector3 pos)
    {
        SetDragRange();
        pos.x = Mathf.Clamp(pos.x, minX, maxX);
        pos.y = Mathf.Clamp(pos.y, minY, maxY);
        return pos;
    }

    //private Action lookAtCallBack;
    //public void LookAtTalent(Action callBack = null)
    //{
    //    if (scaleValue >= 0.9f)
    //    {
    //        callBack?.Invoke();
    //        return;
    //    }

    //    //立即缩放值变为1，后续可变成渐变
    //    scaleValue = 1;
    //    scaleTrans.localScale = new Vector3(scaleValue, scaleValue, 1);

    //    lookAtCallBack = callBack;
    //    lookAt = true;
    //}
}
