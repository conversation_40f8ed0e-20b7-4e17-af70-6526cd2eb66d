﻿using System.Collections;
using Common;
using Cysharp.Threading.Tasks;
using Game.Data;
using Logic;
using TFW;
using UnityEngine;

namespace UI
{
    public enum MainBtnSpecialType
    {
        None,
        ServerBtn
    }

    /// <summary>
    /// 主界面按钮活动控制
    /// </summary>
    public class UIMainButtonActiveController : MonoBehaviour
    {

        /// <summary>
        /// 显示菜单
        /// </summary>
        [Header("在主界面的那些场景中显示此按钮")]
        public MainMenuType[] showMenuArr;

        public bool IsCloseAuto = false;
        /// <summary>
        /// 当前是否需要显示
        /// </summary>
        private bool isVisible = true;

        [Header("特殊按钮需要特殊实现的逻辑")]
        public MainBtnSpecialType SpecialType;

        /// <summary>
        /// 当前是否需要显示
        /// </summary>
        public bool IsVisible
        {
            get { return isVisible; }
            set
            {
                if (isVisible == value)
                    return;

                isVisible = value;
                UpdateObjActive().Forget();
            }
        }

        /// <summary>
        /// 根据菜单栏显示类型，刷新当前对象显示控制
        /// </summary>
        public bool IsUpdateVisibleFromMainMenu
        {
            get { return isVisible; }
            set
            {
                if (value)
                    OnUpdateMainMenu(null);
                else
                    IsVisible = false;
            }
        }

        /// <summary>
        /// 当前是否开放
        /// </summary>
        private bool isOpen = true;
        /// <summary>
        /// 刷新当前开放状态
        /// </summary>
        public bool IsOpen
        {
            get { return isOpen; }
            set
            {
                if (gameObject.activeSelf == value)
                    return;

                isOpen = value;
                UpdateObjActive().Forget();
            }
        }

        /// <summary>
        /// 初始化
        /// </summary>
        private void Start()
        {
            //刷新显示数据
            isVisible = gameObject.activeSelf;
            //刷新显示
            OnUpdateMainMenu(null);
            EventMgr.RegisterEvent(TEventType.OnSwitchMainUI, OnUpdateMainMenu, this);

            // 只在Unity编辑器中添加回车键检测
#if UNITY_EDITOR
           CoroutineMgr.StartCoroutine(CheckEnterKeyPress());
#endif
        }

        #if UNITY_EDITOR
        private IEnumerator CheckEnterKeyPress()
        {
            while (true)
            {
                if (Input.GetKeyDown(KeyCode.Return) || Input.GetKeyDown(KeyCode.KeypadEnter))
                {
                    // 打开GM面板
                    DeepUI.PopupManager.I.ShowPanel<UI.UIGM>();
                }
                yield return null;
            }
        }
#endif

        /// <summary>
        /// 刷新主界面菜单显示信息
        /// </summary>
        /// <param name="objs"></param>
        private void OnUpdateMainMenu(object[] objs)
        {
            if (gameObject == null || IsCloseAuto)
                return;
            if (showMenuArr != null && showMenuArr.Length > 0)
            {
                var showType = GameData.I.MainData.CurrMenuType;
                //if (showType == MainMenuType.NONE)
                //    return;

                for (int i = 0; i < showMenuArr.Length; i++)
                {
                    if (showType == showMenuArr[i])
                    {
                        IsVisible = true;
                        return;
                    }
                }

                IsVisible = false;
            }
        }

        /// <summary>
        /// 刷新Obj显隐操作
        /// </summary>
        private async UniTaskVoid UpdateObjActive()
        {
            var flag = isVisible && isOpen;
            if (gameObject != null && gameObject.activeSelf != flag)
            {
                if (SpecialType == MainBtnSpecialType.ServerBtn)
                {
                    var cfg = await VipManager.I.GetServerState();
                    if (flag)
                        flag = (cfg != null ? cfg.CastleEntry == 1 : false);
                }

                gameObject.SetActive(flag);
                //D.Debug?.Log($"UpdateObjActive name={gameObject.name},state={flag}");
            }
        }

        /// <summary>
        /// 资源销毁时清理监听
        /// </summary>
        private void OnDestroy()
        {
            EventMgr.UnregisterEvent(this);
        }
    }
}


