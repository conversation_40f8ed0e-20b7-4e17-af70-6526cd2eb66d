﻿using Common;
using cspb;
using Cysharp.Threading.Tasks;
using DeepUI;
using Game.Data;
using Logic;
using Public;
using System.Collections.Generic;
using TFW.Localization;
using TFW.UI;
using UnityEngine;
using UnityEngine.EventSystems;

namespace UI
{

    /// <summary>
    /// 邪灵城堡信息数据
    /// </summary>
    public class UIEvilInvasionInfoData
    {
        /// <summary>
        /// 实体Id
        /// </summary>
        public long entityId;
    }


    /// <summary>
    /// 邪灵城堡信息
    /// <AUTHOR>
    /// @date 2022/02/08 15:44:38
    /// @ver 1.0
    /// </summary>
    [Popup("Map/UIEvilInvasion", true, true)]
    public class UIEvilInvasion : BasePopup
    {

        #region 属性信息数据

        /// <summary>
        /// 设置点击空白区域可以关闭界面
        /// </summary>
        protected internal override bool PopBackEnabled => true;

        private GameObject _starBtn;
        private GameObject _shareBtn;
        private GameObject _infoBtn;
        private TFWText _titleText;
        private TFWText _tipsText;
        private BtnDescWidget _openBtnWidget;
        private GameObject _rewardObj;
        private List<RewardItemUI> rewardItemUI = new List<RewardItemUI>();
        #endregion
        //玩家信息
        private UnifyPlayerInfo playerInfo;
        private  UIEvilInvasionInfoData data;
        private EntityInfo _entityInfo;
        #region 数据初始化

        /// <summary>
        /// 初始化脚本
        /// </summary>
        protected override void OnInit()
        {
            _starBtn = UIHelper.GetChild(GameObject, "Root/TitleBar/StarBtn");
            _shareBtn = UIHelper.GetChild(GameObject, "Root/TitleBar/ShareBtn");
            _infoBtn = UIHelper.GetChild(GameObject, "Root/TitleBar/InfoBtn");
            _rewardObj = UIHelper.GetChild(GameObject, "Root/RewardList");
            _titleText = UIHelper.GetComponent<TFWText>(GameObject, "Root/TitleBar/TitleText");
            _titleText.text = LocalizationMgr.Get("Invasion_EvilCastle");
            _tipsText = UIHelper.GetComponent<TFWText>(GameObject, "Root/Desc");
            _tipsText.text = LocalizationMgr.Get("Invasion_EvilCastle_desc");
            var openBtnObj = UIHelper.GetChild(GameObject, "Root/Button");
            if (openBtnObj)
            {
                _openBtnWidget = new BtnDescWidget(openBtnObj);
                _openBtnWidget.SetData(LocalizationMgr.Get("Invasion_Open"));
                _openBtnWidget.SetBtnClickCallBack(OnClickOpen);
            }
            for (int i = 1; i <= 4; i++)
            {
                var temp = UIHelper.GetComponent<RewardItemUI>(_rewardObj, "Item" + i);
                rewardItemUI.Add(temp);
            }
            
        }

        private void OnClickOpen(object[] obj)
        {
            if (!LPlayer.I.IsPlayerInUnion())
            {
                FloatTips.I.FloatMsg(LocalizationMgr.Get("ActiveUnionBoss_Tips_102"));
                return;
            }
            if (GameData.I.ActivityData.ActvEvilInvasionData.EvilInvasionData.beginSta == (int)BEGINSTA.Opening || GameData.I.ActivityData.ActvEvilInvasionData.EvilInvasionData.beginSta == (int)BEGINSTA.End)
            {
                FloatTips.I.FloatMsg(LocalizationMgr.Get("Invasion_opened"));
                return;
            }
            else
            {
                //只有R4以上权限的成员可以召唤联盟boss
                Cfg.G.CUnionClass unionClass = LAllianceMgr.I.GetMemberClassInfo(LPlayer.I.PlayerID);
                if (unionClass?.Class < 4)
                {
                    FloatTips.I.FloatMsg(LocalizationMgr.Get("Invasion_tips_01"));
                    return;
                }
            }
            PopupManager.I.ClosePopup<UIEvilInvasion>();
            PopupManager.I.ShowPanel<UIActivityEvilInvasionDifficulty>(new UIActivityEvilInvasionDifficultyData
            { 
                entityId = data.entityId
            });
        }

        /// <summary>
        /// 当界面第一次打开时调用
        /// </summary>
        protected internal override void OnOpenStart()
        {
            //添加事件监听
            AddListener();

            //刷新数据显示
            OnShowStart();
        }

        /// <summary>
        /// 界面销毁时调用
        /// </summary>
        protected internal override void OnCloseStart()
        {
            //反注册LOD事件监听
            //MapZoomChangeRegistry.Unregister(this);

            //移除事件监听
            RemoveListener();

        }

        /// <summary>
        /// 界面每次显示时调用
        /// </summary>
        protected internal override void OnShowStart()
        {
            data = Data as UIEvilInvasionInfoData;
            if (data == null)
                return;
            _entityInfo = LMapEntityManager.I.GetEntityInfo(data.entityId);
            //注册LOD事件监听
            //MapZoomChangeRegistry.Register(this);
            var actvData = ActivityMgr.I.GetActvById(GameData.I.ActivityData.ActvEvilInvasionData.actvId);
            var rewardsData = actvData.clientInfo.DisplayReward;
            for (int i = 0; i < 4; i++)
            {
                if (i < rewardsData.Count)
                {
                    rewardItemUI[i].SetData(rewardsData[i]);
                    rewardItemUI[i].gameObject.SetActive(true);
                }
                else
                {
                    rewardItemUI[i].gameObject.SetActive(false);
                }
            }
        }

        /// <summary>
        /// 数据清理销毁
        /// </summary>
        protected override void OnDispose()
        {
            base.OnDispose();
        }

        /// <summary>
        /// 添加按钮事件监听
        /// </summary>
        private void AddListener()
        {
            //绑定收藏按钮事件
            BindClickListener(_starBtn, OnClickStarBtn);
            //绑定分享按钮事件
            BindClickListener(_shareBtn, OnClickShareBtn);
            //绑定帮助按钮事件
            BindClickListener(_infoBtn, OnClickInfoBtn);
        }

        /// <summary>
        /// 移除事件监听
        /// </summary>
        private void RemoveListener()
        {
            //移除绑定收藏按钮事件
            RemoveClickListener(_starBtn);
            //移除绑定分享按钮事件
            RemoveClickListener(_shareBtn);
        }


        #endregion


        #region 数据显示刷新

       


        #endregion

        #region 地图Zoom更改时数据刷新

        /// <summary>
        /// 地图Zoom更改时自动调用
        /// </summary>
        /// <param name="lod"></param>
        /// <param name="height"></param>
        public void OnZoomChanged(float zoom)
        {
            //关闭当前界面
            Close();
        }

        #endregion


        #region 事件处理
        /// <summary>
        /// 详情
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        private void OnClickInfoBtn(GameObject arg0, PointerEventData arg1)
        {

            PopupManager.I.ShowDialog<UIActivityRules>(
               new UIActivityRuleData()
               {
                   title = LocalizationMgr.Get("LevelRank_Text02"),
                   rules = LocalizationMgr.Get("ActiveInvasion_Help"),
               });
        }
        /// <summary>
        /// 点击收藏按钮
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        private void OnClickStarBtn(GameObject arg0, PointerEventData arg1)
        {
            UniTask.Void(async () =>
            {
                if (_entityInfo != null)
                {
                    var pos = Formula.WorldToSharePosition(this._entityInfo.GetCurrentPosition());
                    UIFavoritesTipsPanel.Show(pos.x, pos.z, LocalizationMgr.Get((await _entityInfo.doorInfo.BuildingCfg()).Name));
                }
            });
           
        }

        /// <summary>
        /// 点击分享按钮
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        private void OnClickShareBtn(GameObject arg0, PointerEventData arg1)
        {
            if (!LPlayer.I.IsPlayerInUnion())//没有加入联盟
            {
                PopupManager.I.ClosePopup<UIEvilInvasion>();
                //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.ALLIANCE,
                //    new UIChatData() { CurrentTabKey = GameData.I.MainData.CurrMenuType, CurrChatTab = ChatTabs.Alliance });
                return;
            }

            SendShareCoordinateToUnionChannel();
        }

        /// <summary>
        /// 发送坐标分享消息到联盟频道
        /// </summary>
        private void SendShareCoordinateToUnionChannel()
        {
            UniTask.Void(async () =>
            {
                if (_entityInfo == null)
                {
                    D.Debug?.Log("_entityInfo is null!");
                    return;
                }

                var pos = Formula.WorldToSharePosition(this._entityInfo.GetCurrentPosition());
                var x = pos.x;
                var z = pos.z;

                var name = LocalizationMgr.Get((await _entityInfo.doorInfo.BuildingCfg()).Name);

                //分享{0}级{1}
                var unitName = string.Format(LocalizationMgr.GetUIString("Share_chatdes_1"), 1, name);
                PopupManager.I.ShowPanel<UIShare>(new UIShareData()
                {
                    chatContent = UIChatHelper.I.GetShareCoordinateToAllianceChannel
                    (
                        name, 1,
                        ShareDataTypeEnum.PlayerCastle,
                        unitName,
                        x,
                        z
                    )
                });
            });
            
        }
        #endregion

    }
}
