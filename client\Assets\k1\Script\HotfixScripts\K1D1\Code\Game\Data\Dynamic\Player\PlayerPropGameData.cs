﻿using Cfg.G;
using Config;
using Cysharp.Threading.Tasks;
using Game.Config;
using Logic;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TFW;
using UnityEngine;

namespace Game.Data
{

    /// <summary>
    /// 属性类型枚举
    /// </summary>
    public enum PropTypeEnum
    {
        /// <summary>
        /// 空
        /// </summary>
        NONE = 0,

        /// <summary>
        /// 怪物移动速度
        /// </summary>
        MONSTER_MOVE_SPEED = 1,

        /// <summary>
        /// 怪物受到伤害增加
        /// </summary>
        MONSTER_HURT_ADD = 2,

        /// <summary>
        /// 怪物血量减少
        /// </summary>
        MONSTER_HP_REDUCE = 3,

        /// <summary>
        /// 怪物攻击力减少
        /// </summary>
        MONSTER_ATTACK_REDUCE = 4,

        /// <summary>
        /// 士兵攻击增加
        /// </summary>
        SOLDIER_ATTACK_ADD = 5,

        /// <summary>
        /// 士兵攻击速度增加
        /// </summary>
        SOLDIER_ATTACK_SPEED_ADD = 6,

        /// <summary>
        /// 士兵等级增加
        /// </summary>
        SOLDIER_LEVEL_ADD = 7,

        /// <summary>
        /// 英雄受到伤害减少
        /// </summary>
        HERO_HURT_REDUCE = 8,

        /// <summary>
        /// 英雄对怪物攻击增加
        /// </summary>
        HERO_ATTACK_ADD = 9,

        /// <summary>
        /// 怪物攻击加速
        /// </summary>
        MONSTER_ATTACK_SPEED = 1000,

        /// <summary>
        /// (塔防)所有部队某士兵暴击率增加
        /// </summary>
        SOLIDER_ATTACK_CRITICAL_STRIKES = 2001,

        /// <summary>
        /// (塔防)所有部队某士兵暴击伤害增加
        /// </summary>
        SOLIDER_ATTACK_VIOLENCE_INJURY = 2002,

        /// <summary>
        /// (塔防)所有部队某士兵对飞行单位伤害增加
        /// </summary>
        SOLIDER_ATTACK_FLY_EXTRA_HURT = 2003,

        /// <summary>
        /// (塔防)所有部队某士兵对BOSS伤害增加
        /// </summary>
        SOLIDER_ATTACK_BOSS_EXTRA_HURT = 2004,

        /// <summary>
        /// (塔防)所有部队某士兵攻击距离增加
        /// </summary>
        SIKUDER_ATTACK_ATTACK_RANGE = 2005,


        /// <summary>
        /// 看守所 科技增加的坑位
        /// </summary>
        Soldier_HeroAddNum = 12053,
        /// <summary>
        /// 金库 科技增加坑位
        /// </summary>
        Coin_HeroAddNum = 12055,
        /// <summary>
        /// 
        /// </summary>
        TaskStarAddValue = 12056,
         
        /// <summary>
        /// 金库产生速率
        /// </summary>
        GoldSpeed = 12057,
        /// <summary>
        /// 金库上限值
        /// </summary>
        GoldMax=12058,

        /// <summary>
        /// 金库上限值(联盟属性）
        /// </summary>
        GoldMax_Alliance = 30004,
        /// <summary>
        /// 属性 合成道具等级+
        /// </summary>
        MergeItemLvAdd= 11909,
    }

    /// <summary>
    /// 玩家属性信息数据
    /// </summary>
    public class PlayerPropGameData : IDisposable
    {

        #region 属性数据信息

        /// <summary>
        /// 玩家属性字典信息数据
        /// </summary>
        private Dictionary<int, double> _propDic;

        /// <summary>
        /// buff属性字典信息数据
        /// </summary>
        private Dictionary<int, List<CBuffProperty>> _buffPropertyDic;

        #endregion

        #region 数据初始化

        /// <summary>
        /// 数据初始化
        /// </summary>
        public async void Init()
        {
            //初始化属性信息数据
            await InitBuffPropertyInfo();
        }

        /// <summary>
        /// 初始化buff属性信息数据
        /// </summary>
        private async UniTask InitBuffPropertyInfo()
        {
            var dic = await Cfg.C.CBuffProperty.RawDictAsync();
            var itor = dic.GetEnumerator();

            while (itor.MoveNext())
            {
                InitBuffPropertyInfo(itor.Current.Value);
            }
        }

        /// <summary>
        /// 初始化buff属性信息数据
        /// </summary>
        /// <param name="prop"></param>
        private void InitBuffPropertyInfo(CBuffProperty prop)
        {
            if (prop == null
                || prop.PropertyType == (int)PropTypeEnum.NONE)
                return;

            if (_buffPropertyDic == null)
                _buffPropertyDic = new Dictionary<int, List<CBuffProperty>>();

            List<CBuffProperty> list = null;
            if (!_buffPropertyDic.TryGetValue(prop.PropertyType, out list))
            {
                list = new List<CBuffProperty>();
                _buffPropertyDic[prop.PropertyType] = list;
            }

            list.Add(prop);

        }

        #endregion


        #region 数据刷新

        /// <summary>
        /// 更新属性信息数据
        /// </summary>
        /// <param name="dic"></param>
        public void UpdatePropInfo(Dictionary<int, double> dic)
        {
            _propDic = dic;

            //输出属性  日志太多，有需要再打开
            //if (Application.isEditor)
            //{
            //    if (dic != null)
            //    {
            //        foreach (var item in dic)
            //        {
            //            D.Debug?.Log("UpdatePropInfo={0}====={1}", item.Key, item.Value);
            //        }
            //    }
            //}
        }

        #endregion

        #region 获取属性数据信息


        /// <summary>
        /// 获取具体某一属性加速信息-只有百分比！！！
        /// </summary>
        /// <param name="propId"></param>
        /// <returns></returns>
        public double GetPropSpeedUp(int propId)
        {
            if (_propDic == null || propId <= 0)
                return 0;

            double val = 0;
            if (_propDic.TryGetValue(propId, out var speedUp))
            {
                var prop = Cfg.C.CBuffProperty.I(propId);
                if (prop != null)
                {
                    if (prop.ValueType == 0)
                    {
                        //取值
                        val += speedUp;
                    }
                    else
                    {
                        //百分比
                        val += speedUp;
                    }
                }
            }

            //D.Warning?.Log($"玩家属性：{propId}：val：{val}  字典有：{GameUtils.SerializeToStr(_propDic)}");
            return val;
        }

        /// <summary>
        /// 获取属性信息数据
        /// </summary>
        /// <param name="propType"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        //public double GetPropInfo(PropTypeEnum propType, int type)
        //{
        //    if (propType == PropTypeEnum.NONE
        //        || _buffPropertyDic == null
        //        || !LSwitchMgr.I.IsFunctionOpen(SwitchConfig.BuffProperty_Tower))
        //        return 0;

        //    List<CBuffProperty> list = null;
        //    if (_buffPropertyDic.TryGetValue((int)propType, out list))
        //    {
        //        return GetPropInfo(list, type);
        //    }

        //    return 0;
        //}

        /// <summary>
        /// 获取属性信息数据
        /// </summary>
        /// <param name="propType"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public double GetPropInfo(PropTypeEnum propType, int type)
        {
            if (propType == PropTypeEnum.NONE
                || _buffPropertyDic == null)
            {
                Debug.LogWarning($"propNone&开关");
                return 0;
            }

            List<CBuffProperty> list = null;
            if (_buffPropertyDic.TryGetValue((int)propType, out list))
            {

                return GetPropInfo(list, type);
            }

            Debug.LogWarning($"_buffPropertyDic 不包含{(int)propType}");
            return 0;
        }


        /// <summary>
        /// 获取属性信息数据
        /// </summary>
        /// <param name="list"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public double GetPropInfo(List<CBuffProperty> list, int type)
        {
            if (list == null)
                return 0;

            CBuffProperty prop = null;
            double val = 0;
            for (int i = 0; i < list.Count; i++)
            {
                prop = list[i];
                if (prop != null
                    && (prop.PropertyParam == 0 || prop.PropertyParam == type))
                {
                    val += GetPropSpeedUp(prop.Id);
                }
            }

            return val;
        }

        #endregion

        #region 数据清理

        /// <summary>
        /// 数据清理
        /// </summary>
        public void Dispose()
        {
            if (_propDic != null)
            {
                _propDic.Clear();
                _propDic = null;
            }

            if (_buffPropertyDic != null)
            {
                _buffPropertyDic.Clear();
                _buffPropertyDic = null;
            }
        }


        #endregion

    }
}
